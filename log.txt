{"level":30,"time":1715174843069,"pid":78087,"hostname":"Shikhas-MacBook-Air.local","context":"NestFactory","msg":"Starting Nest application..."}
{"level":30,"time":1715174843069,"pid":78087,"hostname":"Shikhas-MacBook-Air.local","context":"InstanceLoader","msg":"MongooseModule dependencies initialized"}
{"level":30,"time":1715174843069,"pid":78087,"hostname":"Shikhas-MacBook-Air.local","context":"InstanceLoader","msg":"ThingModule dependencies initialized"}
{"level":30,"time":1715174843069,"pid":78087,"hostname":"Shikhas-MacBook-Air.local","context":"InstanceLoader","msg":"ConfigHostModule dependencies initialized"}
{"level":30,"time":1715174843069,"pid":78087,"hostname":"Shikhas-MacBook-Air.local","context":"InstanceLoader","msg":"AppModule dependencies initialized"}
{"level":30,"time":1715174843069,"pid":78087,"hostname":"Shikhas-MacBook-Air.local","context":"InstanceLoader","msg":"LoggerModule dependencies initialized"}
{"level":30,"time":1715174843069,"pid":78087,"hostname":"Shikhas-MacBook-Air.local","context":"InstanceLoader","msg":"ConfigModule dependencies initialized"}
{"level":30,"time":1715174843069,"pid":78087,"hostname":"Shikhas-MacBook-Air.local","context":"InstanceLoader","msg":"ConfigModule dependencies initialized"}
{"level":30,"time":1715174843069,"pid":78087,"hostname":"Shikhas-MacBook-Air.local","context":"InstanceLoader","msg":"MongooseCoreModule dependencies initialized"}
{"level":30,"time":1715174843069,"pid":78087,"hostname":"Shikhas-MacBook-Air.local","context":"InstanceLoader","msg":"MongooseModule dependencies initialized"}
{"level":30,"time":1715174843069,"pid":78087,"hostname":"Shikhas-MacBook-Air.local","context":"InstanceLoader","msg":"FeatureModule dependencies initialized"}
{"level":30,"time":1715174843069,"pid":78087,"hostname":"Shikhas-MacBook-Air.local","context":"RoutesResolver","msg":"AppController {/api}:"}
{"level":30,"time":1715174843069,"pid":78087,"hostname":"Shikhas-MacBook-Air.local","context":"RouterExplorer","msg":"Mapped {/api, GET} route"}
{"level":30,"time":1715174843069,"pid":78087,"hostname":"Shikhas-MacBook-Air.local","context":"RoutesResolver","msg":"FeatureController {/api/features}:"}
{"level":30,"time":1715174843069,"pid":78087,"hostname":"Shikhas-MacBook-Air.local","context":"RouterExplorer","msg":"Mapped {/api/features, POST} route"}
{"level":30,"time":1715174843069,"pid":78087,"hostname":"Shikhas-MacBook-Air.local","context":"RouterExplorer","msg":"Mapped {/api/features, GET} route"}
{"level":30,"time":1715174843069,"pid":78087,"hostname":"Shikhas-MacBook-Air.local","context":"RouterExplorer","msg":"Mapped {/api/features/:id, GET} route"}
{"level":30,"time":1715174843069,"pid":78087,"hostname":"Shikhas-MacBook-Air.local","context":"RouterExplorer","msg":"Mapped {/api/features/:id, PATCH} route"}
{"level":30,"time":1715174843069,"pid":78087,"hostname":"Shikhas-MacBook-Air.local","context":"RouterExplorer","msg":"Mapped {/api/features/:id, DELETE} route"}
{"level":30,"time":1715174843069,"pid":78087,"hostname":"Shikhas-MacBook-Air.local","context":"NestApplication","msg":"Nest application successfully started"}
{"level":30,"time":1715174859703,"pid":78087,"hostname":"Shikhas-MacBook-Air.local","req":{"id":"req-1","method":"GET","url":"/api","query":{},"headers":{"host":"localhost:3000","connection":"keep-alive","cache-control":"max-age=0","sec-ch-ua":"\"Chromium\";v=\"124\", \"Google Chrome\";v=\"124\", \"Not-A.Brand\";v=\"99\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"macOS\"","upgrade-insecure-requests":"1","user-agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","accept":"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7","sec-fetch-site":"none","sec-fetch-mode":"navigate","sec-fetch-user":"?1","sec-fetch-dest":"document","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-GB,en-US;q=0.9,en;q=0.8"},"remoteAddress":"127.0.0.1","remotePort":51301},"context":"AppController","msg":"Hello world"}
{"level":30,"time":1715174990787,"pid":78119,"hostname":"Shikhas-MacBook-Air.local","context":"NestFactory","msg":"Starting Nest application..."}
{"level":30,"time":1715174990787,"pid":78119,"hostname":"Shikhas-MacBook-Air.local","context":"InstanceLoader","msg":"MongooseModule dependencies initialized"}
{"level":30,"time":1715174990787,"pid":78119,"hostname":"Shikhas-MacBook-Air.local","context":"InstanceLoader","msg":"ThingModule dependencies initialized"}
{"level":30,"time":1715174990787,"pid":78119,"hostname":"Shikhas-MacBook-Air.local","context":"InstanceLoader","msg":"ConfigHostModule dependencies initialized"}
{"level":30,"time":1715174990787,"pid":78119,"hostname":"Shikhas-MacBook-Air.local","context":"InstanceLoader","msg":"LoggerModule dependencies initialized"}
{"level":30,"time":1715174990787,"pid":78119,"hostname":"Shikhas-MacBook-Air.local","context":"InstanceLoader","msg":"ConfigModule dependencies initialized"}
{"level":30,"time":1715174990787,"pid":78119,"hostname":"Shikhas-MacBook-Air.local","context":"InstanceLoader","msg":"ConfigModule dependencies initialized"}
{"level":30,"time":1715174990787,"pid":78119,"hostname":"Shikhas-MacBook-Air.local","context":"InstanceLoader","msg":"AppModule dependencies initialized"}
{"level":30,"time":1715174990787,"pid":78119,"hostname":"Shikhas-MacBook-Air.local","context":"InstanceLoader","msg":"MongooseCoreModule dependencies initialized"}
{"level":30,"time":1715174990787,"pid":78119,"hostname":"Shikhas-MacBook-Air.local","context":"InstanceLoader","msg":"MongooseModule dependencies initialized"}
{"level":30,"time":1715174990787,"pid":78119,"hostname":"Shikhas-MacBook-Air.local","context":"InstanceLoader","msg":"FeatureModule dependencies initialized"}
{"level":30,"time":1715174990787,"pid":78119,"hostname":"Shikhas-MacBook-Air.local","context":"RoutesResolver","msg":"AppController {/api}:"}
{"level":30,"time":1715174990787,"pid":78119,"hostname":"Shikhas-MacBook-Air.local","context":"RouterExplorer","msg":"Mapped {/api, GET} route"}
{"level":30,"time":1715174990787,"pid":78119,"hostname":"Shikhas-MacBook-Air.local","context":"RoutesResolver","msg":"FeatureController {/api/features}:"}
{"level":30,"time":1715174990788,"pid":78119,"hostname":"Shikhas-MacBook-Air.local","context":"RouterExplorer","msg":"Mapped {/api/features, POST} route"}
{"level":30,"time":1715174990788,"pid":78119,"hostname":"Shikhas-MacBook-Air.local","context":"RouterExplorer","msg":"Mapped {/api/features, GET} route"}
{"level":30,"time":1715174990788,"pid":78119,"hostname":"Shikhas-MacBook-Air.local","context":"RouterExplorer","msg":"Mapped {/api/features/:id, GET} route"}
{"level":30,"time":1715174990788,"pid":78119,"hostname":"Shikhas-MacBook-Air.local","context":"RouterExplorer","msg":"Mapped {/api/features/:id, PATCH} route"}
{"level":30,"time":1715174990788,"pid":78119,"hostname":"Shikhas-MacBook-Air.local","context":"RouterExplorer","msg":"Mapped {/api/features/:id, DELETE} route"}
{"level":30,"time":1715174990788,"pid":78119,"hostname":"Shikhas-MacBook-Air.local","context":"NestApplication","msg":"Nest application successfully started"}
{"level":30,"time":1715174999487,"pid":78119,"hostname":"Shikhas-MacBook-Air.local","req":{"id":"req-1","method":"GET","url":"/api","query":{},"headers":{"host":"localhost:3000","connection":"keep-alive","cache-control":"max-age=0","sec-ch-ua":"\"Chromium\";v=\"124\", \"Google Chrome\";v=\"124\", \"Not-A.Brand\";v=\"99\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"macOS\"","upgrade-insecure-requests":"1","user-agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","accept":"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7","sec-fetch-site":"none","sec-fetch-mode":"navigate","sec-fetch-user":"?1","sec-fetch-dest":"document","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-GB,en-US;q=0.9,en;q=0.8"},"remoteAddress":"127.0.0.1","remotePort":51865},"context":"AppController","msg":"Hello world"}
{"level":30,"time":1715174999516,"pid":78119,"hostname":"Shikhas-MacBook-Air.local","req":{"id":"req-1","method":"GET","url":"/api","query":{},"headers":{"host":"localhost:3000","connection":"keep-alive","cache-control":"max-age=0","sec-ch-ua":"\"Chromium\";v=\"124\", \"Google Chrome\";v=\"124\", \"Not-A.Brand\";v=\"99\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"macOS\"","upgrade-insecure-requests":"1","user-agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","accept":"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7","sec-fetch-site":"none","sec-fetch-mode":"navigate","sec-fetch-user":"?1","sec-fetch-dest":"document","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-GB,en-US;q=0.9,en;q=0.8"},"remoteAddress":"127.0.0.1","remotePort":51865},"res":{"statusCode":200,"headers":{}},"responseTime":36,"msg":"request completed"}
{"level":30,"time":1715175042858,"pid":78119,"hostname":"Shikhas-MacBook-Air.local","req":{"id":"req-2","method":"GET","url":"/api","query":{},"headers":{"host":"localhost:3000","connection":"keep-alive","cache-control":"max-age=0","sec-ch-ua":"\"Chromium\";v=\"124\", \"Google Chrome\";v=\"124\", \"Not-A.Brand\";v=\"99\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"macOS\"","upgrade-insecure-requests":"1","user-agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","accept":"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7","sec-fetch-site":"none","sec-fetch-mode":"navigate","sec-fetch-user":"?1","sec-fetch-dest":"document","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-GB,en-US;q=0.9,en;q=0.8"},"remoteAddress":"127.0.0.1","remotePort":51865},"context":"AppController","msg":"Hello world"}
{"level":30,"time":1715175042863,"pid":78119,"hostname":"Shikhas-MacBook-Air.local","req":{"id":"req-2","method":"GET","url":"/api","query":{},"headers":{"host":"localhost:3000","connection":"keep-alive","cache-control":"max-age=0","sec-ch-ua":"\"Chromium\";v=\"124\", \"Google Chrome\";v=\"124\", \"Not-A.Brand\";v=\"99\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"macOS\"","upgrade-insecure-requests":"1","user-agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","accept":"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7","sec-fetch-site":"none","sec-fetch-mode":"navigate","sec-fetch-user":"?1","sec-fetch-dest":"document","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-GB,en-US;q=0.9,en;q=0.8"},"remoteAddress":"127.0.0.1","remotePort":51865},"res":{"statusCode":200,"headers":{}},"responseTime":5,"msg":"request completed"}
{"level":30,"time":1715175067828,"pid":78119,"hostname":"Shikhas-MacBook-Air.local","req":{"id":"req-8","method":"GET","url":"/api","query":{},"headers":{"host":"localhost:3000","connection":"keep-alive","sec-ch-ua":"\"Chromium\";v=\"124\", \"Google Chrome\";v=\"124\", \"Not-A.Brand\";v=\"99\"","accept":"*/*","sec-ch-ua-mobile":"?0","user-agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","sec-ch-ua-platform":"\"macOS\"","sec-fetch-site":"same-origin","sec-fetch-mode":"cors","sec-fetch-dest":"empty","referer":"http://localhost:3000/explorer","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-GB,en-US;q=0.9,en;q=0.8"},"remoteAddress":"127.0.0.1","remotePort":51865},"context":"AppController","msg":"Hello world"}
{"level":30,"time":1715175067840,"pid":78119,"hostname":"Shikhas-MacBook-Air.local","req":{"id":"req-8","method":"GET","url":"/api","query":{},"headers":{"host":"localhost:3000","connection":"keep-alive","sec-ch-ua":"\"Chromium\";v=\"124\", \"Google Chrome\";v=\"124\", \"Not-A.Brand\";v=\"99\"","accept":"*/*","sec-ch-ua-mobile":"?0","user-agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","sec-ch-ua-platform":"\"macOS\"","sec-fetch-site":"same-origin","sec-fetch-mode":"cors","sec-fetch-dest":"empty","referer":"http://localhost:3000/explorer","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-GB,en-US;q=0.9,en;q=0.8"},"remoteAddress":"127.0.0.1","remotePort":51865},"res":{"statusCode":200,"headers":{}},"responseTime":12,"msg":"request completed"}
