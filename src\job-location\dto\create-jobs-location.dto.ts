import { IsNotEmpty, IsOptional, IsString, IsMongoId, IsN<PERSON>ber } from 'class-validator';
import { Types } from 'mongoose';

export class CreateJobsLocationDto {
  
  @IsMongoId()
  @IsNotEmpty()
  city: Types.ObjectId;;

  @IsMongoId()
  @IsNotEmpty()
  state: Types.ObjectId;;

  @IsMongoId()
  @IsNotEmpty()
  country: Types.ObjectId;;

  @IsOptional()
  @IsString()
  postalCode?: string;

  @IsOptional()
  @IsNumber()
  latitude?: number;

  @IsOptional()
  @IsNumber()
  longitude?: number;
}
