import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsOptional, IsString, IsInt, Min, IsEnum, IsMongoId } from 'class-validator';
import { BusinessUnitType } from 'src/shared/constants';

export class FilterBusinessUnitsDto {
  
  @IsOptional()
  @IsString()
  @Transform(({ value }) => (value === undefined || value === '0' ? '' : value))
  name?: string;

  @IsOptional()
  @IsMongoId()
  @Transform(({ value }) => (value === undefined || value === '0' ? '' : value))
  org?: string;
 
  @ApiProperty({
    type: String,
    required: false,
    default: BusinessUnitType.UNSPECIFIED,
    enum: BusinessUnitType,
    description: "Business Unit type",
  })
  @IsOptional()
  @IsString()
  @IsEnum(BusinessUnitType)
  type?: string;

  @IsOptional()
  @IsString()
  @Transform(({ value }) => (value === undefined || value === '0' ? '' : value))
  parentBusinessUnit?: string;

  @IsOptional()
  @Transform(({ value }) => {
    return value > 0 ? value : 1;
  })
  page: number = 1;


  @IsOptional()
  @Transform(({ value }) => {
    return value > 0 ? value : 10;
  })
  limit: number = 10;

}
