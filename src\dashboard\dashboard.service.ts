import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import * as jwt from 'jsonwebtoken';
import { Role } from 'src/auth/enums/role.enum';
import { DashboardMetadata } from './schemas/dashboard-metadata.schema';
import { Model } from 'mongoose';
import { CreateDashboardDto } from './dto/createDashboard.dto';

@Injectable()
export class DashboardService {

  constructor(
    @InjectModel(DashboardMetadata.name) private dashboardMetadataModel: Model<DashboardMetadata>,
  ) { }

  private METABASE_SITE_URL = 'http://localhost:3030'; // Your Metabase server
  private METABASE_SECRET_KEY = '67c34f14fc4f0705dd7cb652baa675e05458295aa2b7196688605d007bc3c90a'; // From Metabase settings

  async generateDashboardUrl(user: any): Promise<string> {
    const userRoles = user.roles || []; // Assuming user.roles is an array of roles
    const userId = user._id.toString();
    const orgId = user.org._id.toString();
    console.log('Generating dashboard URL for user:', userId);
    let metadata: DashboardMetadata | null = null;
    if (userRoles.includes(Role.Recruiter)) {
      console.log('User is a recruiter, generating dashboard URL...');
      metadata = await this.dashboardMetadataModel.findOne({ role: Role.Recruiter });
      if (!metadata) {
        throw new NotFoundException(`No Dashboard is Available for the role ${Role.Recruiter}`);
      }
    }
    else if (userRoles.includes(Role.BUHead)) {
      console.log('User is a BU Head, generating dashboard URL...');
      metadata = await this.dashboardMetadataModel.findOne({ role: Role.BUHead });
      if (!metadata) {
        throw new NotFoundException(`No Dashboard is Available for the role ${Role.BUHead}`);
      }
    }
    else if (userRoles.includes(Role.ResourceManager)) {
      console.log('User is a Resource Manager, generating dashboard URL...');
      metadata = await this.dashboardMetadataModel.findOne({ role: Role.ResourceManager });
      if (!metadata) {
        throw new NotFoundException(`No Dashboard is Available for the role ${Role.ResourceManager}`);
      }
    }
    else if (userRoles.includes(Role.DeliveryManager)) {
      console.log('User is a Delivery Manager, generating dashboard URL...');
      metadata = await this.dashboardMetadataModel.findOne({ role: Role.DeliveryManager });
      if (!metadata) {
        throw new NotFoundException(`No Dashboard is Available for the role ${Role.DeliveryManager}`);
      }
    }
    else if (userRoles.includes(Role.TeamLead)) {
      console.log('User is a Team Lead, generating dashboard URL...');
      metadata = await this.dashboardMetadataModel.findOne({ role: Role.TeamLead });
      if (!metadata) {
        throw new NotFoundException(`No Dashboard is Available for the role ${Role.TeamLead}`);
      }
    }
    else if (userRoles.includes(Role.TeamMember)) {
      console.log('User is a Team Member, generating dashboard URL...');
      metadata = await this.dashboardMetadataModel.findOne({ role: Role.TeamMember });
      if (!metadata) {
        throw new NotFoundException(`No Dashboard is Available for the role ${Role.TeamMember}`);
      }
    }
    else if (userRoles.includes(Role.Vendor)) {
      console.log('User is a Vendor, generating dashboard URL...');
      metadata = await this.dashboardMetadataModel.findOne({ role: Role.Vendor });
      if (!metadata) {
        throw new NotFoundException(`No Dashboard is Available for the role ${Role.Vendor}`);
      }
    }
    else if (userRoles.includes(Role.JobSeeker)) {
      console.log('User is a Job Seeker, generating dashboard URL...');
      metadata = await this.dashboardMetadataModel.findOne({ role: Role.JobSeeker });
      if (!metadata) {
        throw new NotFoundException(`No Dashboard is Available for the role ${Role.JobSeeker}`);
      }
    }
    else if (userRoles.includes(Role.Freelancer)) {
      console.log('User is a Freelancer, generating dashboard URL...');
      metadata = await this.dashboardMetadataModel.findOne({ role: Role.Freelancer });
      if (!metadata) {
        throw new NotFoundException(`No Dashboard is Available for the role ${Role.Freelancer}`);
      }
    }
    let params: any = {};
    if (metadata?.params.includes('user')) {
      params.user = userId.toString(); // This will be available in Metabase as {{user}}
    }
    if (metadata?.params.includes('org')) {
      params.org = orgId.toString(); // This will be available in Metabase as {{org}}
    }
    console.log('Params:', params);
    // const payload = {
    //   resource: { dashboard: 3 }, // replace with your dashboard ID
    //   params: {
    //     user: userId.toString(), // This will be available in Metabase as {{user}}
    //   },
    //   exp: Math.round(Date.now() / 1000) + (60 * 10), // 10 minutes expiration
    // };

    const payload = {
      resource: { dashboard: metadata?.dashboardId },
      params,
      exp: Math.round(Date.now() / 1000) + (60 * 10), // 10 minutes expiration
    };

    const token = jwt.sign(payload, this.METABASE_SECRET_KEY);
    return `${this.METABASE_SITE_URL}/embed/dashboard/${token}#bordered=true&titled=true`;
  }

  async saveDashboard(dto: CreateDashboardDto): Promise<DashboardMetadata> {
    const newDashboard = new this.dashboardMetadataModel({
      role: dto.role,
      dashboardId: dto.dashboardId,
      title: dto.title,
      params: dto.params,
      createdAt: new Date(),
    });
    const dashboardExists = await this.dashboardMetadataModel.findOne({ role: dto.role });
    if (dashboardExists) {
      // Update existing dashboard metadata
      dashboardExists.dashboardId = dto.dashboardId;
      dashboardExists.title = dto.title;
      dashboardExists.params = dto.params || [];
      return await dashboardExists.save();
    }
    return await newDashboard.save();
  }

}
