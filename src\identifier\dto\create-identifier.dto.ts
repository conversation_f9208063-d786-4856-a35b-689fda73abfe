import { ApiHideProperty, ApiProperty } from "@nestjs/swagger";
import { Transform, TransformFnParams } from "class-transformer";
import { IsBoolean, IsEnum, IsMongoId, IsNotEmpty, IsOptional, IsString, MaxLength, Min<PERSON>ength } from "class-validator";
import { sanitizeWithStyle } from "src/utils/sanitize-with-style";
import { IdentifierType } from "src/shared/constants";

export class CreateIdentifierDto {

    @ApiProperty({
        type: String,
        required: true,
        description: 'Identifier Name.',
    })
    @IsNotEmpty()
    @IsString()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    name: string;

    @ApiProperty({
        type: Boolean,
        required: false,
        default: true
    })
    @IsBoolean()
    @IsOptional()
    isUpload?: boolean;

    @ApiProperty({
        type: String,
        required: false,
        enum: IdentifierType,
        description: 'Identifier Type',
    })
    @IsString()
    @IsOptional()
    @IsEnum(IdentifierType)
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    isType?: string;

    @ApiProperty({
        type: Boolean,
        required: false,
        default: true
    })
    @IsBoolean()
    @IsOptional()
    isTaxIdentifier?: boolean;

    @ApiProperty({
        type: Boolean,
        required: false,
        default: false
    })
    @IsBoolean()
    @IsOptional()
    isEntityIdentifier?: boolean;

    @ApiProperty({
        type: String,
        required: true,
    })
    @IsNotEmpty()
    @IsMongoId()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    region: string;

    @ApiProperty({
        type: Boolean,
        required: false,
        default: false
    })
    @IsBoolean()
    @IsOptional()
    isNewIdentifier?: boolean;
    
}
