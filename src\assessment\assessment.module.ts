import { Modu<PERSON> } from '@nestjs/common';
import { AssessmentService } from './assessment.service';
import { AssessmentController } from './assessment.controller';
import { MongooseModule } from '@nestjs/mongoose';
import { Assessment, AssessmentSchema } from './schemas/assessment.schema';
import { JwtModule } from '@nestjs/jwt';
import { EmailTemplate, EmailTemplateSchema } from 'src/email-template-builder/schemas/email-template-builder.schema';
import { Placeholder, PlaceholderSchema } from 'src/org/schemas/org.schema';
import { EndpointsRolesModule } from 'src/endpoints-roles/endpoints-roles.module';

@Module({
  controllers: [AssessmentController],
  providers: [AssessmentService],
  imports: [
    JwtModule, EndpointsRolesModule,
    MongooseModule.forFeature([{ name: Assessment.name, schema: AssessmentSchema },
    { name: EmailTemplate.name, schema: EmailTemplateSchema },
    { name: Placeholder.name, schema: PlaceholderSchema },
    ])
  ],
  exports: [MongooseModule]
})
export class AssessmentModule { }
