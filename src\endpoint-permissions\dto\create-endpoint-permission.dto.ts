import { IsNotEmpty, IsO<PERSON>, IsA<PERSON>y, IsString, Matches } from 'class-validator';

export class CreateEndpointPermissionDto {
    @IsObject()
    @IsNotEmpty()
    rolesMapping: Record<string, string[]>; // Role -> ["controller:METHOD"]
}

export class UpdateUserPermissionsDto {
  @IsString()
  userId: string;

  @IsArray()
  @IsString({ each: true }) // Ensures each element in the array is a string
  permissions: string[];
}
