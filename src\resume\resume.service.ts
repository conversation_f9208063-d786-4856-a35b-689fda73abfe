import { Injectable, InternalServerErrorException, Logger, NotFoundException } from '@nestjs/common';
import { UpdateResumeDto } from './dto/update-resume.dto';
import { ConfigService } from '@nestjs/config';
import { InjectModel } from '@nestjs/mongoose';
import { Resumes } from './schemas/resume.schema';
import { Model, Types } from 'mongoose';
import { CreateResumeDto } from './dto/create-resume.dto';

@Injectable()
export class ResumeService {

  private readonly logger = new Logger(ResumeService.name);

  constructor(private configService: ConfigService, @InjectModel(Resumes.name) private ResumeModel: Model<Resumes>) { }

  
  async create(createResumeDto: CreateResumeDto, userId: string) {
    try {
      const createdResume = new this.ResumeModel({
        ...createResumeDto,
        createdBy: userId 
      });
      return await createdResume.save();
    } catch (error) {
      this.logger.error(`Failed to create a candidate resume. ${error}`);
      throw new InternalServerErrorException(`Error while creating a candidate resume. ${error?.message}`);
    }
  }
  

  async findAll(userId: string) {
    try {
      return this.ResumeModel.find({ createdBy: userId }).exec();
    } catch (error) {
      this.logger.error(`Failed to retrieve resumes for user ${userId}. ${error}`);
      throw new InternalServerErrorException('Error retrieving resumes.');
    }
  }  

  async findOne(resumeId: Types.ObjectId) {
    try {
      const resume = await this.ResumeModel.findById(resumeId)
      if (!resume) {
        throw new NotFoundException(`Resume not found with ID ${resumeId}`);
      }
      return resume;
    } catch (error) {
      this.logger.error(error);
      this.logger.error(`An error occurred in fetching candidate resume by Id ${resumeId}. ${error?.message}`);
      throw error;
    }
  }

  async update(resumeId: Types.ObjectId, updateResumeDto: UpdateResumeDto) {
    try {
      const resume = await this.ResumeModel.findById(resumeId)
      if (!resume) {
        throw new NotFoundException(`Candidate resume not found with ID ${resumeId}`);
      }
      return await this.ResumeModel.findByIdAndUpdate(resumeId, updateResumeDto, { new: true });
    } catch (error) {
      this.logger.error(error);
      this.logger.error(`An error occurred while updating candidate resume by ID ${resumeId}. ${error?.message}`);
      throw error;
    }
  }

  async remove(resumeId: Types.ObjectId) {
    try {
      const resume = await this.ResumeModel.findById(resumeId)
      if (!resume) {
        throw new NotFoundException(`Candidate resume not found with ID ${resumeId}`);
      }
      await this.ResumeModel.deleteOne({ _id: resumeId });
      return { message: 'Candidate resume deleted' };
    } catch (error) {
      this.logger.error(`An error occurred while deleting candidate resume by ID ${resumeId}. ${error?.message}`);
      throw new InternalServerErrorException('An error occurred while deleting candidate resume');
    }
  }
}
