import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Types } from 'mongoose';
import { Country } from 'src/country/schemas/country.schema';
import { State } from 'src/state/schemas/state.schema';
import { BasicUser } from 'src/user/schemas/basic-user.schema';

export type RegionDocument = HydratedDocument<Region>;

@Schema({
  timestamps: true
})
export class Region {

  @Prop({
    type: Types.ObjectId,
    required: true,
    ref: 'Country'
  })
  country: Country;

  @Prop({
    type: Types.ObjectId,
    required: true,
    ref: 'State'
  })
  state: State;

  @Prop({
    required: true,
    type: Types.ObjectId,
    ref: 'Status'
  })
  status: Types.ObjectId;

  @Prop({
    required: true,
    type: Types.ObjectId, ref: 'BasicUser'
  })
  createdBy: BasicUser;

}

export const RegionSchema = SchemaFactory.createForClass(Region);

RegionSchema.index({ country: 1, state: 1 }, { unique: true });



