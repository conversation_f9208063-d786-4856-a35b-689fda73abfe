import { Lo<PERSON>, <PERSON><PERSON><PERSON>, forwardRef } from '@nestjs/common';
import { TaskService } from './task.service';
import { TaskController } from './task.controller';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { MongooseModule, getConnectionToken } from '@nestjs/mongoose';
import { Task, TaskSchema } from './schemas/task.schema';
import { UserModule } from 'src/user/user.module';
import { v4 as generateUuid } from 'uuid';
import { CommonModule } from 'src/common/common.module';
import { EndpointsRolesModule } from 'src/endpoints-roles/endpoints-roles.module';

@Module({
  imports: [
    ConfigModule,
    JwtModule, EndpointsRolesModule,
    UserModule,
    CommonModule,
    //   MongooseModule.forFeatureAsync([
    //   {
    //     name: Task.name,
    //     imports: [ConfigModule],
    //     useFactory: (configService: ConfigService) => {
    //       const logger =  new Logger('TaskSchemaPreHook')
    //       const schema = TaskSchema;
    //       schema.pre('save', async function (next:any) {
    //         logger.log('pre hook on task schema');

    //         try {
    //           if (!this.code) {
    //             this.code = generateUuid(); // Generate a UUID as the code
    //           }
    //           next();
    //           logger.log(`Unique code generated for task.`);
    //         } catch (err) {
    //           logger.error('Error generating unique code for task', err);
    //           return next(err);
    //         }
    //     });
    //       return schema;
    //     },
    //     inject: [ConfigService],
    //   },
    // ]),
    
    MongooseModule.forFeatureAsync([
      {
        name: Task.name,
        imports: [ConfigModule],
        useFactory: (configService: ConfigService) => {
          const AutoIncrement = AutoIncrementFactory(configService);

          const schema = TaskSchema;
          schema.plugin(AutoIncrement, {
            inc_field: 'taskCode',
            id: 'task_sequence',
            start_seq: 1,
            reference_fields: [ ]
          });

          return schema;
        },
        inject: [getConnectionToken(),ConfigService],
  
      },
    ]),
   
  ],
  controllers: [TaskController],
  providers: [TaskService],
})
export class TaskModule {}

const AutoIncrementFactory = require('mongoose-sequence');

