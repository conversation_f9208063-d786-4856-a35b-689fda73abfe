import { ApiHideProperty, ApiProperty } from "@nestjs/swagger";
import { Transform, TransformFnParams } from "class-transformer";
import { IsBoolean, IsDate, IsEnum, IsISO8601, IsMongoId, IsNotEmpty, IsOptional, IsString, Length } from "class-validator";
import { Priority } from "src/shared/constants";
import { sanitizeWithStyle } from "src/utils/sanitize-with-style";

export class CreateJobAllocationBaseDto {
    @ApiProperty({ type: String, required: true, description: 'Job ID' })
    @IsMongoId()
    @IsNotEmpty()
    job: string;

    @ApiProperty({
        type: Date,
        required: true,
        description: 'Job allocation start date',
        // default: new Date().toLocaleDateString('en-GB').split('/').reverse().join('-')
        // default : new Date().toISOString() -it is in ISO format but not in UTC time zone
        // new Date(new Date().toISOString().split('T')[0] + 'T00:00:00.000Z'); - to convert ISO to UTC time zone
        default: new Date(Date.UTC(new Date().getUTCFullYear(), new Date().getUTCMonth(), new Date().getUTCDate(), new Date().getUTCHours(), new Date().getUTCMinutes())),
    })
    // YYYY-MM-DDThh:mm:ssZ - example : 2024-05-27T00:00:00.000Z
    @IsISO8601({ strict: true })
    @Length(10, 24)
    startDate: Date;

    @ApiProperty({
        type: Date,
        required: true,
        description: 'Job allocation due date',
        // default: new Date().toLocaleDateString('en-GB').split('/').reverse().join('-')
        // default : new Date().toISOString() -it is in ISO format but not in UTC time zone
        // new Date(new Date().toISOString().split('T')[0] + 'T00:00:00.000Z'); - to convert ISO to UTC time zone
        // default: new Date(Date.UTC(new Date().getUTCFullYear(), new Date().getUTCMonth(), new Date().getUTCDate(), new Date().getUTCHours(), new Date().getUTCMinutes())),
    })
    // YYYY-MM-DDThh:mm:ssZ - example : 2024-05-27T00:00:00.000Z
    @IsISO8601({ strict: true })
    @Length(10, 24)
    @IsOptional()
    dueDate?: Date;

    @ApiProperty({ type: Number, required: true, default: 1, description: 'Target profiles' })
    @IsNotEmpty()
    targetProfiles: number;

    @ApiProperty({
        type: String,
        required: false,
        default: Priority.HIGH,
        enum: Priority,
        description: 'Priority level of the job allocation',
    })
    @IsEnum(Priority)
    @IsOptional()
    priority?: Priority;

    @ApiHideProperty()
    @IsOptional()
    isDeleted?: boolean;

    @ApiProperty({
        type: Boolean,
        required: true,
        default: false,
        description: 'Indicates if the position is closed',
    })
    @IsBoolean()
    @IsNotEmpty()
    untilPositionClosed: boolean;  

    @ApiHideProperty()
    @IsOptional()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    createdBy?: string;

    @ApiProperty({ type: String, required: false, description: 'lead user ID' })
    @IsMongoId()
    @IsOptional()  // Make leadId optional
    leadId?: string;

    @ApiProperty({ type: String, required: false, description: 'Department ID' })
    @IsMongoId()
    @IsOptional()  // Make departmentId optional
    departmentId?: string;
}
