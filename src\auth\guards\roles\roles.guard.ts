// import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
// import { Observable } from 'rxjs';

// @Injectable()
// export class RolesGuard implements CanActivate {
//   canActivate(
//     context: ExecutionContext,
//   ): boolean | Promise<boolean> | Observable<boolean> {
//     return true;
//   }
// }
import { Injectable, CanActivate, ExecutionContext, Logger, ForbiddenException, UnauthorizedException } from '@nestjs/common';
import { Role } from 'src/auth/enums/role.enum';
import { EndpointsRolesService } from 'src/endpoints-roles/endpoints-roles.service';

@Injectable()
export class RolesGuard implements CanActivate {
  private readonly logger = new Logger(RolesGuard.name);

  constructor(private readonly endpointsRolesService: EndpointsRolesService) { }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const { user } = request;

    if (!user) {
      throw new UnauthorizedException(`User is not logged in.`);
    }

    // return true;
    const { org } = user;
    const userId = user._id.toString();
    const userData = await this.endpointsRolesService.getUserData(userId);
    this.logger.log(JSON.stringify(userData))
    const userRoles = userData?.roles || [];
    const isCustom = userData?.isCustom || false;
    // console.log(isCustom)
    if (userRoles && userRoles.includes(Role.SuperAdmin)) {
      return true
    }

    const { method, url } = request;
    let orgId = user.org?._id;
    if (userRoles?.includes(Role.Vendor)) {
      orgId = user.companyId?._id;
    }
    if (isCustom) {
      this.logger.log(`Checking roles for ${method} ${url} for user ${userId}`);
      const requiredRoles = await this.endpointsRolesService.getEndpointRolesUser(method, url, userId, orgId);
      this.logger.log(JSON.stringify(requiredRoles))
      if (requiredRoles) {
        return true;
      }
    }
    else {
      this.logger.log(`Checking roles for ${method} ${url} in org ${orgId}`);
      let requiredRoles : any;
      if(orgId) {
         requiredRoles = await this.endpointsRolesService.getEndpointRoles(method, url, orgId);
      }
      else{
        requiredRoles = await this.endpointsRolesService.getEndpointRoles(method, url);
      }
      this.logger.log(JSON.stringify(requiredRoles))
      if (!requiredRoles.length) {
        this.logger.log(`No roles found for endpoint ${method} ${url} in org ${orgId}`);
        return false; // Or allow access if no roles are set
      }
      const normalizedUserRoles = userRoles.map((role: string) => role.toLowerCase());
      const normalizedRequiredRoles = requiredRoles.map((role: string) => role.toLowerCase());

      // Check if user has any of the required roles
      const hasRequiredRole = normalizedUserRoles.some((role: string) => normalizedRequiredRoles.includes(role));

      if (!hasRequiredRole) {
        throw new ForbiddenException(`Forbidden: User doesnt have roles: ${requiredRoles.join(', ')}`);
      }

      return true;

    }
    return false;
  }
}
// // import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
// import { Observable } from 'rxjs';

// @Injectable()
// export class RolesGuard implements CanActivate {
//   canActivate(
//     context: ExecutionContext,
//   ): boolean | Promise<boolean> | Observable<boolean> {
//     return true;
//   }
// }

// The following code extends AuthJwtGuard

// import { Injectable, CanActivate, ExecutionContext, Logger, UnauthorizedException, ForbiddenException } from '@nestjs/common';
// import { Reflector } from '@nestjs/core';
// import { ROLES_KEY } from 'src/auth/decorators/roles.decorator';
// import { Role } from 'src/auth/enums/role.enum';
// import { AuthJwtGuard } from '../auth-jwt/auth-jwt.guard';
// import { JwtService } from '@nestjs/jwt';
// import { ConfigService } from '@nestjs/config';

// @Injectable()
// export class RolesGuard extends AuthJwtGuard implements CanActivate {
//    readonly logger = new Logger(RolesGuard.name);

//   constructor(private reflector: Reflector,  jwtService: JwtService,  configService: ConfigService) {
//     super(jwtService, configService);
//   }

//   canActivate(context: ExecutionContext): Promise<boolean> {
//     const requiredRoles = this.reflector.getAllAndOverride<Role[]>(ROLES_KEY, [
//       context.getHandler(),
//       context.getClass(),
//     ]);
//     if (!requiredRoles) {
//       return Promise.resolve(true);
//     }
//     const { user } = context.switchToHttp().getRequest();
//     this.logger.log(`Roles guard`);
//     this.logger.debug(user, requiredRoles);

//     const hasRole = () => user.roles.some((role:Role) => requiredRoles.includes(role));
//     if (!user) {
//       throw new UnauthorizedException();
//     }
//     if (!(user.roles && hasRole())) {
//       throw new ForbiddenException('Forbidden');
//     }
//     // console.log(user && user.roles && hasRole());

//     return user && user.roles && hasRole();

//     // return requiredRoles.some((role) => user.roles?.includes(role));
//   }
// }

