import { <PERSON><PERSON>, <PERSON>hem<PERSON>, SchemaFactory } from "@nestjs/mongoose";
import { HydratedDocument, Types } from "mongoose";
import { Task } from "src/task/schemas/task.schema";
import { BasicUser } from "src/user/schemas/basic-user.schema";
import { Contact } from 'src/contact/schemas/contact.schema';
import { Job } from "src/job/schemas/job.schema";
import { Org } from "src/org/schemas/org.schema";
import { FileMetadata } from 'src/file-upload/schemas/file-metadata.schema';
import { Invitee } from "./invitee.schema";
import { MeetingStatus, MeetingType } from "src/shared/constants";
import { BusinessUnit } from "src/business-unit/schemas/business-unit.schema";

export type MeetingDocument = HydratedDocument<Meeting>;

@Schema({ timestamps: true })
export class Meeting {

    @Prop({
        type: String,
        required: true,
        trim: true,
    })
    subject: string;


    @Prop({
        type: String,
        required: false,
        trim: true,
    })
    summary?: string;

    @Prop({
        type: String,
        required: false,
        trim: true,
    })
    description?: string;

    @Prop({
        required: true,
        type: Types.ObjectId, ref: 'BasicUser'
    })
    organizer: BasicUser;

    // @Prop({ required: true, type: [{ type: Types.ObjectId, ref: 'Invitee' }], index: true, default: [] })
    // invitees: Types.ObjectId[];

    @Prop({
        required: false,
        type: [String],
        default: []
    })
    invitees?: string[];

    @Prop({
        required: false,
        type: [String],
        default: []
    })
    guests?: string[];

    @Prop({ type: String, required: true, trim: true})
    meetingLink: string;

    @Prop({
        type: String,
        required: false,
    })
    meetingCode?: string;

    @Prop({
        required: true
    })
    scheduledAt: Date;

    @Prop({
        required: false,
        type: [String],
        default: []
    })
    accept?: string[];

    @Prop({
        required: false,
        type: [String],
        default: []
    })
    reject?: string[];

    @Prop({
        required: false
    })
    endTime?: Date;

    @Prop({ required: false, type: Number })
    duration?: number;

    @Prop({ required: false, default: 'UTC' })
    timezone?: string;

    @Prop({
        required: false,
        default: false,
    })
    isDeleted?: boolean;

    @Prop({
        required: false,
        default: false,
    })
    allowRecording?: boolean;

    @Prop({
        required: false,
        default: false,
    })
    allowTranscripts?: boolean;

    @Prop({
        required: false,
        default: MeetingType.REGULAR,
        enum: Object.values(MeetingType),
    })
    type?: MeetingType;

    @Prop({
        required: false,
        default: MeetingStatus.SCHEDULED,
        enum: Object.values(MeetingStatus),
    })
    status?: string;

    @Prop({
        type: Types.ObjectId,
        required: true,
        ref: 'BasicUser'
    })
    createdBy: BasicUser;

    @Prop({ type: Types.ObjectId, ref: 'BasicUser', required: false })
    updatedBy?: BasicUser;

    @Prop({ required: false, default: false })
    sendReminder?: boolean;

    @Prop({ required: false, type: Number, default: 10 }) // Reminder before 10 minutes
    reminderMinutesBefore?: number;


    @Prop({
        required: false,
        type: Types.ObjectId,
        ref: 'BasicUser'
    })
    deletedBy?: BasicUser;



    @Prop({
        required: false,
        type: Types.ObjectId, ref: 'Task'
    })
    task?: Task;

    @Prop({
        required: false,
        type: Types.ObjectId, ref: 'Org'
    })
    org?: Org;

    @Prop({
        required: false,
        type: Types.ObjectId, ref: 'BusinessUnit'
    })
    businessUnit?: BusinessUnit;

    @Prop({
        required: false,
        type: Types.ObjectId, ref: 'Contact'
    })
    contact?: Contact;

    @Prop({
        required: false,
        type: Types.ObjectId, ref: 'Job'
    })
    job?: Job;

    @Prop({ type: [{ type: Types.ObjectId, ref: 'FileMetadata' }], required: false })
    files?: FileMetadata[];


    @Prop({
        required: false,
        type: [String],
        default: []
    })
    tags?: string[];


}

export const MeetingSchema = SchemaFactory.createForClass(Meeting);

MeetingSchema.index({ org: 1 });
MeetingSchema.index({ organizer: 1 });