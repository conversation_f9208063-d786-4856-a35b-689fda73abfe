// dto/google-oauth-callback.dto.ts
import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from 'class-validator';

export class ZoomOAuthCallbackDto  {
    @ApiProperty({ description: 'OAuth code returned from Zoom', required: true })
    @IsString()
    @IsNotEmpty()
    code: string;

    @ApiProperty({ description: 'State string returned (used as Integration ID)', required: true })
    @IsString()
    @IsNotEmpty()
    state: string;
}
