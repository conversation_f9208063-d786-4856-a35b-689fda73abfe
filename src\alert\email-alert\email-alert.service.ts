import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { OnEvent } from '@nestjs/event-emitter';
import { MailerService } from '@nestjs-modules/mailer';
import { Model, Types } from 'mongoose';
import { join } from 'path';
import { PUBLIC_FOLDER_PATH, EMAIL_TEMPLATES_FOLDER_PATH, EmailSubject, EmailMessage, EmailInfo, EmailButtonText, StatusFunctionality } from 'src/shared/constants';
import ical from 'ical-generator';
import { CalendarService } from 'src/calendar/calendar.service';
import { CreateCalendarDto } from 'src/calendar/dto/create-calender.dto';
import { FileMetadata } from 'src/file-upload/schemas/file-metadata.schema';
import { InjectModel } from '@nestjs/mongoose';
import { Org, OrgDocument } from 'src/org/schemas/org.schema';


interface EmailDetails {
  firstName: string;
  toEmail: string;
  subject: string;
  plainTextContents?: string;
  verification?: string;
  otpCode?: string;
  message?: string;
  info?: string;
  buttonText?: string;
  // module?: string,
  id?: Types.ObjectId;
  unsubscribeUrl?: string;
  isAccountRelated?: boolean;
  sendHtml?: boolean;
  callToActionLink?: string;
  calendarEvent?: string;
  password?: string
}
interface ContactDetail {
  contactEmail: string;
  isPrimary: boolean;
  createdAt: string;
  updatedAt: string;
}

interface Organization {
  _id: string;
  title: string;
  contactDetails?: ContactDetail[];
  isApproved: boolean;
  orgType: string;
}


@Injectable()
export class EmailAlertService {
  private readonly logger = new Logger(EmailAlertService.name);

  private readonly defaultFromEmail: string;
  Url: string;

  constructor(private configService: ConfigService,
    @InjectModel(Org.name) private orgModel: Model<OrgDocument>,

    private readonly mailerService: MailerService,
    private calendarService: CalendarService) {
    this.defaultFromEmail = this.configService.get<string>('DEFAULT_EMAIL_SENDER') || '<EMAIL>';
    this.Url = this.configService.get<string>('API_URL') || `https://apis.gana.talency.in/api`;
  }

  public async sendEmail(emailDetails: EmailDetails) {
    try {
      const {
        firstName,
        toEmail,
        subject,
        plainTextContents,
        verification,
        otpCode,
        message,
        info,
        buttonText,
        callToActionLink,
        id,
        sendHtml = true,
        calendarEvent,
        password
      } = emailDetails;

      this.logger.log(`TEMPLATES PATH - ${join(EMAIL_TEMPLATES_FOLDER_PATH, 'action', 'template.hbs')}`);

      this.logger.log(`PUBLIC_FOLDER_PATH - ${PUBLIC_FOLDER_PATH}`);

      const productName = this.configService.get<string>('PRODUCT_NAME');
      const productTagline = this.configService.get<string>('PRODUCT_TAGLINE');
      const productWebsiteUrl = this.configService.get<string>('PRODUCT_WEBSITE_URL');
      const companyName = this.configService.get<string>('COMPANY_NAME');
      const companyWebsiteUrl = this.configService.get<string>('COMPANY_WEBSITE_URL');
      const companyContactEmail = this.configService.get<string>('COMPANY_CONTACT_EMAIL');
      const companyAddress1 = this.configService.get<string>('COMPANY_ADDRESS_L1');
      const companyAddress2 = this.configService.get<string>('COMPANY_ADDRESS_L2');
      const year = this.configService.get<string>('YEAR');

      const emailTemplatePath = join(EMAIL_TEMPLATES_FOLDER_PATH, 'action', 'template.hbs');

      const sendEmailOptions: any = {
        to: toEmail,
        from: this.defaultFromEmail,
        subject: subject,
      };

      if (!sendHtml) {
        sendEmailOptions.text = plainTextContents;
      } else {
        sendEmailOptions.template = emailTemplatePath;
        sendEmailOptions.attachments = [
          {
            filename: 'talsy_logo.png',
            path: join(PUBLIC_FOLDER_PATH, 'talsy_logo.png'),
            cid: 'logo'
          }
        ];
        sendEmailOptions.context = {
          recipient: firstName,
          otpCode,
          callToActionLink: callToActionLink,
          subject,
          message,
          info,
          buttonText,
          productName,
          productTagline,
          productWebsiteUrl,
          companyName,
          companyWebsiteUrl,
          companyContactEmail,
          companyAddress1,
          companyAddress2,
          year,
          password
        }
      }

      if (calendarEvent) {
        // Attach calendar event
        sendEmailOptions.attachments = [
          {
            filename: 'event.ics',
            content: calendarEvent,
            contentType: 'text/calendar',
            encoding: 'base64'
          }
        ];
      }

      console.log(`Sending email to: ${toEmail}`);
      console.log("emailDetails:", emailDetails);

      // Log the textContent for debugging
      // this.logger.log(`textContent: ${!sendHtml ? plainTextContents : 'undefined (sendHtml is true)'}`);

      // Log the final sendEmailOptions for debugging
      // this.logger.log(`sendEmailOptions: ${JSON.stringify(sendEmailOptions, null, 2)}`);

      const sendEmail = await this.mailerService.sendMail(sendEmailOptions);

      if (sendEmail) {
        this.logger.debug(`Email is sent...`);
      }
      return {
        status: 'ok',
        message: 'message has been sent',
      };
    } catch (error) {
      this.logger.error(`Error in sending email - example - ${error}.`);
      throw error;
    }

  }

  @OnEvent('org.custom.status.changed')
  async handleVendorStatusChangeEvent(payload: any) {
    try {
      const { existingOrg, updatedOrg } = payload;

      if (!updatedOrg?.contactDetails?.[0]?.contactEmail) {
        this.logger.error(`No contact email found for organization ${updatedOrg?._id}`);
        return;
      }

      const toEmail = updatedOrg.contactDetails[0].contactEmail;
      const vendorTitle = updatedOrg.title || 'Valued Vendor';

      // Determine if the org was approved or rejected based on isApproved and isRejected flags
      let emailSubject = '';
      let emailMessage = '';
      let emailInfo = '';

      if (updatedOrg.isApproved && !updatedOrg.isRejected && updatedOrg.customStatus.functionality === StatusFunctionality.ACTIVATE) {
        emailSubject = 'Vendor Account Activated';
        emailMessage = `Congratulations! Your vendor account "${vendorTitle}" has been activated`;
        emailInfo = 'You can now access the vendor portal using your credentials. If you have any query please react at <<EMAIL>>.';
      } else if (updatedOrg.isRejected) {
        emailSubject = 'Vendor Account Status Update';
        emailMessage = `Your vendor account "${vendorTitle}" has been rejected`;
        emailInfo = 'We regret to inform you that your vendor account registration has been rejected. For more information, please contact at  <<EMAIL>>..';
      } else {
        // Handle other status changes if needed
        emailSubject = 'Vendor Account Status Update';
        emailMessage = `Your vendor account "${vendorTitle}" status has been updated`;
        emailInfo = 'There has been an update to your vendor account status. Please log in to your account for more details.';
      }

      const emailDetails: EmailDetails = {
        firstName: vendorTitle,
        toEmail,
        subject: emailSubject,
        plainTextContents: `${emailMessage}\n\n${emailInfo}`,
        message: emailMessage,
        info: emailInfo,
        sendHtml: true,
        buttonText: EmailButtonText.Login,
        callToActionLink: `${this.Url}/auth/login`
      };

      await this.sendEmail(emailDetails);
      this.logger.log(`Status update email sent to ${toEmail} for vendor ${vendorTitle}`);

    } catch (error) {
      this.logger.error(`Failed to send vendor status update email: ${error.message}`, error);
      throw error;
    }
  }

  @OnEvent('onboarding.file.status')
  async handleFileStatusUpdateEvent(payload: any) {
    try {
      const { updatedFile, status, org, createdByUser, orgTitle, vendorEmail } = payload;

      // Fetch organization details to get contact email
      const organization = await this.orgModel
        .findById(org)
        .lean() as Org;
      // Get the contact email - first try org's contact details, then fallback to created by user
      let toEmail: string | undefined;

      const contactDetails = organization?.contactDetails;

      if (contactDetails && Array.isArray(contactDetails) && contactDetails.length > 0) {
        // Try to find primary contact
        const primaryContact = contactDetails.find(contact => contact?.isPrimary);

        if (primaryContact?.contactEmail) {
          toEmail = primaryContact.contactEmail;
          this.logger.log(`Using primary organization email: ${toEmail}`);
        } else if (contactDetails[0]?.contactEmail) {
          // Fallback to first contact if no primary or primary has no email
          toEmail = contactDetails[0].contactEmail;
          this.logger.log(`Using first organization email: ${toEmail}`);
        }
      }

      if (!toEmail && createdByUser?.email) {
        toEmail = createdByUser.email;
        this.logger.log(`No org contact email found for ${org}, falling back to user email ${toEmail}`);
      }

      if (!toEmail) {
        this.logger.error(`No valid email found for organization ${org} or user`);
        return;
      }

      // Prepare email content based on status
      let emailSubject = '';
      let emailMessage = '';
      let emailInfo = '';
      let buttonText = 'View Details';

      switch (status) {
        case 'approved':
          emailSubject = 'Document Approved';
          emailMessage = 'Your document has been approved';
          emailInfo = `The document "${updatedFile.originalName}" has been reviewed and approved by our team.`;
          break;
        case 'rejected':
          emailSubject = 'Document Rejected';
          emailMessage = 'Your document requires attention';
          emailInfo = `The document "${updatedFile.originalName}" has been reviewed and needs to be updated. Please review and resubmit.`;
          break;
        default:
          emailSubject = 'Document Status Update';
          emailMessage = 'Your document status has been updated';
          emailInfo = `The status of document "${updatedFile.originalName}" has been updated to ${status}.`;
      }

      const emailDetails: EmailDetails = {
        firstName: orgTitle || 'Valued Partner',
        toEmail,
        subject: emailSubject,
        plainTextContents: `${emailMessage}\n\n${emailInfo}`,
        message: emailMessage,
        info: emailInfo,
        sendHtml: true,
        buttonText: EmailButtonText.Login,
        callToActionLink: `${this.Url}/auth/login`
      };

      await this.sendEmail(emailDetails);
      this.logger.log(`Status update email sent to ${toEmail} for file ${updatedFile._id}`);
    } catch (error) {
      this.logger.error(`Failed to send file status update email: ${error.message}`, error);
    }
  }

  @OnEvent('file.status.approve.reject')
  async handleFileApproveRejectEvent(payload: any) {
    try {
      const { updatedFile, status, org, createdByUser } = payload;

      this.logger.log(JSON.stringify(payload))
      // Fetch organization details to get contact email
      const organization = await this.orgModel
        .findById(org)
        .lean() as Org;

      let toEmail: string | undefined;

      const contactDetails = organization?.contactDetails;

      if (contactDetails && Array.isArray(contactDetails) && contactDetails.length > 0) {
        // Try to find primary contact
        const primaryContact = contactDetails.find(contact => contact?.isPrimary);

        if (primaryContact?.contactEmail) {
          toEmail = primaryContact.contactEmail;
          this.logger.log(`Using primary organization email: ${toEmail}`);
        } else if (contactDetails[0]?.contactEmail) {
          // Fallback to first contact if no primary or primary has no email
          toEmail = contactDetails[0].contactEmail;
          this.logger.log(`Using first organization email: ${toEmail}`);
        }
      }

      if (!toEmail && createdByUser?.email) {
        toEmail = createdByUser.email;
        this.logger.log(`No org contact email found for ${org}, falling back to user email ${toEmail}`);
      }

      if (!toEmail) {
        this.logger.error(`No valid email found for organization ${org} or user`);
        return;
      }

      // Prepare email content based on status
      let emailSubject = '';
      let emailMessage = '';
      let emailInfo = '';

      const OrgencodeBase64 = Buffer.from(new TextEncoder().encode(org)).toString('base64');
      const frontEndUrl = this.configService.get<string>('API_URL');
      switch (status) {
        case 'approved':
          // emailSubject = 'Document Approved';
          // emailMessage = 'Your document has been approved';
          // emailInfo = `The document "${updatedFile.originalName}" has been reviewed and approved by our team.`;
          break;
        case 'rejected':
          emailSubject = 'Document Rejected';
          emailMessage = 'Your document requires attention';
          emailInfo = `The document "${updatedFile.originalName}" has been reviewed and needs to be updated. Please review and resubmit by visiting below Url<br><a href="${frontEndUrl}/re-upload/${OrgencodeBase64}">${frontEndUrl}/re-upload</a>`;
          break;
        default:
          emailSubject = 'Document Status Update';
          emailMessage = 'Your document status has been updated';
          emailInfo = `The status of document "${updatedFile.originalName}" has been updated to ${status}.`;
      }

      const emailDetails: EmailDetails = {
        firstName: organization.title || 'Valued Partner',
        toEmail,
        subject: emailSubject,
        plainTextContents: `${emailMessage}\n\n${emailInfo}`,
        message: emailMessage,
        info: emailInfo,
      };
      if (emailSubject) {
        await this.sendEmail(emailDetails);
      }
      this.logger.log(`Status update email sent to ${toEmail} for file ${updatedFile._id}`);
    } catch (error) {
      this.logger.error(`Failed to send file status update email: ${error.message}`, error);
    }
  }

  @OnEvent('candidate.file.status')
  async handleCandidateFileApproveRejectEvent(payload: any) {
    try {
      const { updatedFile, status, key, jobApp } = payload;

      this.logger.log(JSON.stringify(payload))
      // Fetch organization details to get contact email

      const toEmail = jobApp.contactDetails.contactEmail;
      function formatKeyLabel(key: string): string {
        return key
          .split('_')                         // ['pan', 'card']
          .map(word => word.charAt(0).toUpperCase() + word.slice(1)) // ['Pan', 'Card']
          .join(' ');                         // "Pan Card"
      }
      const doc_key = formatKeyLabel(key);
      // Prepare email content based on status
      let emailSubject = '';
      let emailMessage = '';
      let emailInfo = '';

      // const OrgencodeBase64 = Buffer.from(new TextEncoder().encode(org)).toString('base64');
      // const frontEndUrl = this.configService.get<string>('API_URL');
      switch (status) {
        case 'approved':
          // emailSubject = 'Document Approved';
          // emailMessage = 'Your document has been approved';
          // emailInfo = `The document "${updatedFile.originalName}" has been reviewed and approved by our team.`;
          break;
        case 'rejected':
          emailSubject = `Document Rejected - ${doc_key}`;
          emailMessage = 'Your document requires attention';
          emailInfo = `One of your submitted documents for ${doc_key} titled "${updatedFile.originalName}" has been <b>rejected</b> and needs to be updated. Please review and resubmit by visiting below Url<br>
          Please log in to your account using the link below and re-upload a valid and clear version of the document to proceed with your onboarding process.`;
          break;
      }

      const emailDetails: EmailDetails = {
        firstName: jobApp.firstName || 'Valued Partner',
        toEmail,
        subject: emailSubject,
        plainTextContents: `${emailMessage}\n\n${emailInfo}`,
        message: emailMessage,
        info: emailInfo,
        buttonText: EmailButtonText.Login,
        callToActionLink: `${this.Url}/auth/login`
      };
      if (emailSubject) {
        await this.sendEmail(emailDetails);
      }
      this.logger.log(`Status update email sent to ${toEmail} for file ${updatedFile._id}`);
    } catch (error) {
      this.logger.error(`Failed to send file status update email: ${error.message}`, error);
    }
  }

  @OnEvent('candidate.file.remainder')
  async handleCandidateFileRemainderEvent(payload: any) {
    try {
      const { docNames, jobApp } = payload;

      this.logger.log(JSON.stringify(payload))
      // Fetch organization details to get contact email

      const toEmail = jobApp.contactDetails.contactEmail;

      // const OrgencodeBase64 = Buffer.from(new TextEncoder().encode(org)).toString('base64');
      // const frontEndUrl = this.configService.get<string>('API_URL');
      const emailSubject = `Reminder: BGV Documents Pending`;
      const emailMessage = `Please upload the pending documents for your background verification (BGV).`;
      const emailInfo = `Below are the document statuses:<br><br>${docNames}<br><br>
      Please login and upload/resubmit the required documents to continue with the onboarding process.`;


      const emailDetails: EmailDetails = {
        firstName: jobApp.firstName || 'Valued Partner',
        toEmail,
        subject: emailSubject,
        plainTextContents: `${emailMessage}\n\n${emailInfo}`,
        message: emailMessage,
        info: emailInfo,
        buttonText: EmailButtonText.Login,
        callToActionLink: `${this.Url}/auth/login`
      };
      if (emailSubject) {
        await this.sendEmail(emailDetails);
      }
      this.logger.log(`Status update email sent to ${toEmail} for Remainders`);
    } catch (error) {
      this.logger.error(`Failed to send file status update email: ${error.message}`, error);
    }
  }

  @OnEvent('user.verify.email')
  async handleVerifyEmailEvent(payload: any) {
    try {
      this.logger.debug(payload);
      this.logger.debug("user created event in Email Alert service");
      const { firstName, verification, email, _id, otpCode, lastName } = payload;
      const resolvedfirstName = firstName || `${firstName} ${lastName}`.trim();
      const emailDetails: EmailDetails = {
        firstName: resolvedfirstName,
        toEmail: email,
        subject: EmailSubject.Welcome,
        plainTextContents: `This is a test for verify email alert. ${email}`,
        // verification,
        otpCode,
        message: EmailMessage.Welcome,
        info: EmailInfo.Welcome,
        buttonText: EmailButtonText.Welcome,
        sendHtml: true,
        // callToActionLink: `${this.Url}/auth/${_id}/confirm/${verification}`
      };
      await this.sendEmail(emailDetails);
      this.logger.debug("Email sent successfully.");
    } catch (error) {
      this.logger.error("Failed to send verification email", error);
    }
  }


  @OnEvent('vendor.invite.sent')
  async handleVerifyVendorEmailEvent(payload: any) {
    try {
      this.logger.debug(payload);
      this.logger.debug("user created event in Email Alert service");
      const { email, vendorName, userName, companyName, companyId } = payload;
      const resolvedfirstName = userName || `${userName} ${userName}`.trim();

      // Use URL constructor for safer URL building
      const registrationUrl = new URL('/auth/vendor', this.Url);
      registrationUrl.searchParams.append('email', email);
      registrationUrl.searchParams.append('vendorName', vendorName);
      registrationUrl.searchParams.append('companyId', companyId.toString());

      // let message = this.getFormattedMessage(vendorName, companyName)
      // message = message.replace(/\n/g, '<br>');


      const emailDetails: EmailDetails = {
        firstName: resolvedfirstName,
        toEmail: email,
        subject: `${companyName} Invites you register as a Vendor. `,
        plainTextContents: this.getPlainTextContent(email, vendorName, companyName),
        message: this.getFormattedMessage(vendorName, companyName),

        buttonText: EmailButtonText.SignUp,
        callToActionLink: registrationUrl.toString()
      };
      await this.sendEmail(emailDetails);
      this.logger.debug("Email sent successfully.");
    } catch (error) {
      this.logger.error("Failed to send verification email", error);
    }
  }



  @OnEvent('vendor.registered')
  async handleVerifyVendorRegistorEvent(payload: any) {
    try {
      this.logger.debug(payload);
      this.logger.debug("user created event in Email Alert service");
      const { email, vendorName, userName, companyName } = payload;
      const resolvedfirstName = userName || `${userName} ${userName}`.trim();



      const emailDetails: EmailDetails = {
        firstName: vendorName,
        toEmail: email,
        subject: `Welcome to Talsy - Complete Your Onboarding `,
        plainTextContents: this.getPlainTextRegisterContent(email, vendorName, companyName),
        message: this.getFormattedMessagetRegister(email, vendorName, companyName),
        buttonText: EmailButtonText.Login,
        callToActionLink: `${this.Url}/auth/login`

      };
      await this.sendEmail(emailDetails);
      this.logger.debug("Email sent successfully.");
    } catch (error) {
      this.logger.error("Failed to send verification email", error);
    }
  }


  @OnEvent('vendor.onboarding_submitted')
  async handleVerifyVendorOnboardingEvent(payload: any) {
    try {
      this.logger.debug(payload);
      this.logger.debug("user created event in Email Alert service");
      const { companyEmail, vendorName, companyName } = payload;
      // const resolvedfirstName = userName || `${userName} ${userName}`.trim();



      const emailDetails: EmailDetails = {
        firstName: companyName,
        toEmail: companyEmail,
        subject: `Vendor Onboarding Submission - Action Required  `,
        plainTextContents: this.getPlainTextOnboardContent(companyEmail, vendorName, companyName),
        message: this.getFormattedMessageOnboard(companyEmail, vendorName, companyName),
        buttonText: EmailButtonText.Login,
        callToActionLink: `${this.Url}/auth/login`

      };
      await this.sendEmail(emailDetails);
      this.logger.debug("Email sent successfully.");
    } catch (error) {
      this.logger.error("Failed to send verification email", error);
    }
  }

  @OnEvent('vendor.onboarding_updated')
  async handleVendorOnboardingUpdateEvent(payload: any) {
    try {
      this.logger.debug(payload);
      this.logger.debug("Vendor update event received in Email Alert service");

      const { companyEmail, vendorName, companyName } = payload;

      const emailDetails: EmailDetails = {
        firstName: companyName,
        toEmail: companyEmail,
        subject: `Vendor Onboarding Update - Review Required`,
        plainTextContents: this.getPlainTextOnboardUpdateContent(vendorName, companyName),
        message: this.getFormattedMessageOnboardUpdate(vendorName, companyName),
        buttonText: EmailButtonText.Login,
        callToActionLink: `${this.Url}/auth/login`
      };

      await this.sendEmail(emailDetails);
      this.logger.debug("Onboarding update email sent successfully.");
    } catch (error) {
      this.logger.error("Failed to send onboarding update email", error);
    }
  }

  // Plain text email content for onboarding update
  private getPlainTextOnboardUpdateContent(vendorName: string, companyName: string): string {
    return `
    The vendor "${vendorName}" has updated their onboarding documents.  
    Please review the updated information and take any necessary action in the portal.  

    If you have any questions, feel free to contact us at <<EMAIL>>.  

    Best regards,  
    Vendor Management Team
  `.trim();
  }

  // HTML formatted email content for onboarding update
  private getFormattedMessageOnboardUpdate(vendorName: string, companyName: string): string {
    return `
    The vendor "${vendorName}" has updated their onboarding documents.  
    Please review the updated information and take any necessary action in the portal.  

    If you have any questions, feel free to contact us at <<EMAIL>>.  

    Best regards,  
  Vendor Management Team
  `.trim();
  }



  @OnEvent('vendor.invite.reminder.sent')
  async handleVerifyVendorEmailEventRemainder(payload: any) {
    try {
      this.logger.debug(payload);
      this.logger.debug("user created event in Email Alert service");
      const { email, vendorName, userName, companyName } = payload;
      const resolvedfirstName = userName || `${userName} ${userName}`.trim();

      // Use URL constructor for safer URL building
      const registrationUrl = new URL('/auth/vendor', this.Url);
      registrationUrl.searchParams.append('email', email);
      registrationUrl.searchParams.append('vendorName', vendorName);

      // let message = this.getFormattedMessage(vendorName, companyName)
      // message = message.replace(/\n/g, '<br>');



      const emailDetails: EmailDetails = {
        firstName: resolvedfirstName,
        toEmail: email,
        subject: `Remainder: ${companyName} Invites you register as a Vendor. `,
        plainTextContents: this.getPlainTextContent(email, vendorName, companyName),
        message: this.getFormattedMessage(vendorName, companyName),

        buttonText: EmailButtonText.SignUp,
        callToActionLink: registrationUrl.toString()
      };
      await this.sendEmail(emailDetails);
      this.logger.debug("Email sent successfully.");
    } catch (error) {
      this.logger.error("Failed to send verification email", error);
    }
  }

  private getPlainTextContent(email: string, vendorName: string, companyName: string): string {
    return `
      ${companyName} invites ${vendorName} to register as a vendor.
      Please complete your registration at our vendor portal.
      Contact: ${email}
    `.trim();
  }

  private getPlainTextRegisterContent(email: string, vendorName: string, companyName: string): string {
    return `
     Dear ${vendorName}, 

     Congratulations! You have been successfully registered with ${companyName}.  
      Please log in to our vendor portal to complete your onboarding process.  

      If you have any questions, feel free to contact us at <<EMAIL>>.  

      Best regards,  
      ${companyName} Team
    `.trim();
  }

  private getPlainTextOnboardContent(email: string, vendorName: string, companyName: string): string {
    return `
  
     The vendor "${vendorName}" has submitted their onboarding form.  
     Please review the application and take the necessary action in the  portal.  

     If you have any questions or need further information, feel free to contact us at <<EMAIL>>.  

     Best regards,  
     Vendor Management Team
    `.trim();
  }

  private getFormattedMessageOnboard(email: string, vendorName: string, companyName: string): string {
    return `
    <div style="text-align: left;">
        The vendor "<strong>${vendorName}</strong>" has submitted their onboarding form.<br><br>

        Please review the application and take the necessary action in the portal.<br><br>

        If you have any questions or need further information, feel free to contact us at  
        <a href="mailto:<EMAIL>"><EMAIL></a>.<br><br>

        Best regards,<br>
        <strong>Vendor Management Team</strong>
    </div>
    `.trim();
  }




  private getFormattedMessagetRegister(email: string, vendorName: string, companyName: string): string {
    return `
    <div style="text-align: left;">
        Congratulations <strong>${vendorName}!</strong>! You have been successfully registered with ${companyName}.<br><br>

        Please log in to our vendor portal to complete your onboarding process.<br><br>

        If you have any questions, feel free to contact us at 
        <a href="mailto:<EMAIL>"><EMAIL></a>.<br><br>

        Best regards,<br>
        <strong>${companyName} Team</strong>
    </div>
    `.trim();
  }





  private getFormattedMessage(vendorName: string, companyName: string): string {
    return `
      <div style="text-align: left;">
        We are pleased to invite <strong>${vendorName}</strong> to register as a vendor on our official portal.<br><br>
        
        As part of our commitment to streamlining our procurement process and fostering strong partnerships, 
        we have developed a dedicated vendor registration system to facilitate seamless collaboration.<br><br>
        
        <strong>To complete the registration process, please follow these steps:</strong><br><br>
        1. Click the "Sign Up" button below<br>
        2. Fill in the required details<br>
        3. Upload the necessary documents<br>
        4. Submit your application for review<br><br>
        
        Once your registration is successfully completed and approved, you will gain access to:<br><br>
        • Potential business opportunities<br>
        • Streamlined communication<br>
        • Efficient transaction processing with <strong>${companyName}</strong><br><br>
        
        For any assistance during the registration process, please contact our support team.<br><br>
        
        We look forward to a successful partnership with <strong>${vendorName}</strong>.<br><br>
        
        <strong>Best Regards,</strong><br>
        ${companyName}
      </div>
    `.trim();
  }



  @OnEvent('user.verify.email.confirm')
  async handleVerifyEmailConfirmEvent(payload: any) {
    try {
      // this.logger.log(JSON.stringify(payload));
      this.logger.log("User created event in Email Alert service");
      const { firstName, email } = payload;
      const emailDetails: EmailDetails = {
        firstName,
        toEmail: email,
        subject: EmailSubject.Welcome,
        plainTextContents: `This is a test for confirmation of user email alert. ${email}`,
        message: EmailMessage.ConfirmEmail,
        info: EmailInfo.ConfirmEmail,
        buttonText: EmailButtonText.Login,
        callToActionLink: `${this.Url}/auth/login`,
      };
      // this.logger.log(JSON.stringify(emailDetails));
      await this.sendEmail(emailDetails);
      this.logger.debug("Email sent successfully.");
    } catch (error) {
      this.logger.error("Failed to send confirmation email", error);
    }
  }

  @OnEvent('customer.reApproved')
  async handleCustomerReapprovedEvent(payload: any) {
    try {
      // this.logger.log(JSON.stringify(payload));
      this.logger.log("Customer Reapproved event in Email Alert service");
      const { firstName, email } = payload;
      const emailDetails: EmailDetails = {
        firstName,
        toEmail: email,
        subject: EmailSubject.Welcome,
        plainTextContents: `This is a test for confirmation of Customer Approved alert. ${email}`,
        message: EmailMessage.CustomerReApproved,
        info: EmailInfo.CustomerReApproved,
        buttonText: EmailButtonText.Login,
        callToActionLink: `${this.Url}/auth/login`,
      };
      // this.logger.log(JSON.stringify(emailDetails));
      await this.sendEmail(emailDetails);
      this.logger.debug("Email sent successfully.");
    } catch (error) {
      this.logger.error("Failed to send confirmation email", error);
    }
  }


  @OnEvent('user.verify.email.confirm.password')
  async handleVerifyEmailConfirmEventWithPassword(payload: any) {
    try {
      // this.logger.log(JSON.stringify(payload));
      console.log("Payload: ", payload);
      // const doc = payload?._doc;
      const { firstName, email, password } = payload;

      this.logger.log("User created event in Email Alert service");
      // const {  password } = payload;
      // const { firstName, email } = doc;
      const emailDetails: EmailDetails = {
        firstName,
        toEmail: email,
        subject: EmailSubject.Welcome,
        plainTextContents: `This is a test for confirmation of user email alert. ${email}`,
        message: EmailMessage.ConfirmEmail,
        info: EmailInfo.ConfirmEmail,
        buttonText: EmailButtonText.Login,
        callToActionLink: `${this.Url}/auth/login`,
        password: password
      };
      // this.logger.log(JSON.stringify(emailDetails));
      console.log("Email Details: ", emailDetails);
      await this.sendEmail(emailDetails);
      this.logger.debug("Email sent successfully.");
    } catch (error) {
      this.logger.error("Failed to send confirmation email", error);
    }
  }

  @OnEvent('user.promoted.to.employee')
  async handleUserPromotedToEmployee(payload: any) {
    try {
      const { firstName, email } = payload;

      this.logger.log(`User promoted to employee: ${email}`);

      const emailDetails: EmailDetails = {
        firstName,
        toEmail: email,
        subject: EmailSubject.RoleUpdated,
        plainTextContents: `Hi ${firstName}, your role has been updated to Employee.`,
        message: `Welcome onboard as an employee!`,
        info: `Your account has been upgraded.`,
        buttonText: EmailButtonText.Login,
        callToActionLink: `${this.Url}/auth/login`,
      };

      await this.sendEmail(emailDetails);
      this.logger.debug("Promotion email sent successfully.");
    } catch (error) {
      this.logger.error("Failed to send employee promotion email", error);
    }
  }


  @OnEvent('candidate.verify.email.confirm.password')
  async handleCandidateVerifyEmailConfirmEventWithPassword(payload: any) {
    try {
      // this.logger.log(JSON.stringify(payload));
      console.log("Payload: ", payload);
      // const doc = payload?._doc;
      const { firstName, email, password, application, jobDetails } = payload;

      this.logger.log("User created event in Email Alert service");
      // Prepare email content based on status
      let emailSubject = '';
      let emailMessage = '';
      let emailInfo = '';

      const payRollOrg = jobDetails?.endClientOrg?.title;
      emailSubject = `Congratulations - "${payRollOrg}" - ${jobDetails.title}`;
      emailMessage = `Invite to the Portal`;
      // emailInfo = `The document "${updatedFile.originalName}" has been reviewed and needs to be updated. Please review and resubmit by visiting below Url<br><a href="${frontEndUrl}/re-upload/${OrgencodeBase64}">${frontEndUrl}/re-upload</a>`;
      emailInfo = `We are pleased to inform you that, An Offer Has be released for the position of <strong>${jobDetails.title}</strong> at <strong>${payRollOrg}</strong>.<br>
                  To proceed further, please log in to your account to upload the required background verification (BGV) documents.<br> Please ensure all documents are clear, valid, and submitted within the given timeline to avoid any delays in the onboarding process.<br><br>                  
                  <br>`;

      const emailDetails: EmailDetails = {
        firstName,
        toEmail: email,
        subject: emailSubject,
        plainTextContents: `This is a test for confirmation of user email alert. ${email}`,
        message: emailMessage,
        info: emailInfo,
        buttonText: EmailButtonText.Login,
        callToActionLink: `${this.Url}/auth/login`,
        password: password
      };
      // this.logger.log(JSON.stringify(emailDetails));
      console.log("Email Details: ", emailDetails);
      await this.sendEmail(emailDetails);
      this.logger.debug("Email sent successfully.");
    } catch (error) {
      this.logger.error("Failed to send confirmation email", error);
    }
  }

  @OnEvent('user.forgot.password')
  async handleForgotPasswordEvent(payload: any) {
    try {
      this.logger.debug(payload);
      this.logger.debug("Forgot password event in Email Alert service");
      const { firstName, email, otpCode } = payload;
      const emailDetails: EmailDetails = {
        firstName,
        toEmail: email,
        subject: EmailSubject.ForgotPassword,
        plainTextContents: `This is a test for forgot password alert. ${email}`,
        // verification,
        otpCode,
        message: EmailMessage.ForgotPassword,
        info: EmailInfo.ForgotPassword,
        buttonText: EmailButtonText.ForgotPassword,
        // callToActionLink: `${this.Url}/auth/forgot-password-verify` // working fine needs front end integration
      };
      await this.sendEmail(emailDetails);
      this.logger.debug("Forgot password email sent successfully.");
    } catch (error) {
      this.logger.error("Failed to send forgot password email", error);
    }
  }

  @OnEvent('user.forgot.password.done')
  async handleForgotPasswordDoneEvent(payload: any) {
    try {
      this.logger.debug(payload);
      this.logger.debug("Forgot password done event in Email Alert service");
      const { firstName, email } = payload;
      const emailDetails: EmailDetails = {
        firstName,
        toEmail: email,
        subject: EmailSubject.ForgotPasswordDone,
        plainTextContents: `This is a test for forgot password done alert. ${email}`,
        message: EmailMessage.ForgotPasswordDone,
        info: EmailInfo.ForgotPasswordDone,
        buttonText: EmailButtonText.Login,
        callToActionLink: `${this.Url}/auth/login`
      };
      await this.sendEmail(emailDetails);
      this.logger.debug("Forgot password done email sent successfully.");
    } catch (error) {
      this.logger.error("Failed to send forgot password done email", error);
    }
  }

  @OnEvent('user.resend.otp')
  async handleResendOtpEvent(payload: any) {
    try {
      this.logger.debug(payload);
      this.logger.debug("User OTP resend event in Email Alert service");
      const { firstName, email, otpCode } = payload;

      const resolvedFirstName = firstName || `${firstName} ${payload.lastName}`.trim();

      const emailDetails: EmailDetails = {
        firstName: resolvedFirstName,
        toEmail: email,
        subject: EmailSubject.OTPResend,
        plainTextContents: `Your OTP code is: ${otpCode}. It is valid for 10 minutes.`,
        otpCode,
        message: EmailMessage.OTPResend,
        info: EmailInfo.OTPResend,
        buttonText: EmailButtonText.Login,
        sendHtml: true,
      };

      await this.sendEmail(emailDetails);
      this.logger.debug("OTP resend email sent successfully.");
    } catch (error) {
      this.logger.error("Failed to send OTP resend email", error);
    }
  }


  @OnEvent('user.password.reset')
  async handleResetPasswordEvent(payload: any) {
    try {
      this.logger.debug(payload);
      this.logger.debug("Reset password event in Email Alert service");
      const { firstName, email } = payload;
      const emailDetails: EmailDetails = {
        firstName,
        toEmail: email,
        subject: EmailSubject.PasswordReset,
        plainTextContents: `This is a test for reset password alert. ${email}`,
        message: EmailMessage.PasswordReset,
        info: EmailInfo.PasswordReset,
      };
      await this.sendEmail(emailDetails);
      this.logger.debug("Reset password email sent successfully.");
    } catch (error) {
      this.logger.error("Failed to send reset password email", error);
    }
  }

  @OnEvent('user.password.change')
  async handleUpdatePasswordEvent(payload: any) {
    try {
      this.logger.debug(payload);
      this.logger.debug("Change password event in Email Alert service");
      const { firstName, email } = payload;
      const emailDetails: EmailDetails = {
        firstName,
        toEmail: email,
        subject: EmailSubject.PasswordChange,
        plainTextContents: `This is a test for change password alert. ${email}`,
        message: EmailMessage.PasswordChange,
        info: EmailInfo.PasswordChange
      };
      await this.sendEmail(emailDetails);
      this.logger.debug("Change password email sent successfully.");
    } catch (error) {
      this.logger.error("Failed to send change password email", error);
    }
  }


  @OnEvent('task.assignee.changed', { async: true })
  async onTaskUpdateAssigneeEvent(payload: any) {
    try {
      this.logger.debug(payload);
      const { updatedTask } = payload;
      const { title, assignee } = updatedTask;
      const { firstName, email } = assignee;
      this.logger.debug("Task assignee update event in Email Alert service");
      const id = updatedTask?._id;
      const emailDetails: EmailDetails = {
        firstName,
        toEmail: email,
        subject: EmailSubject.TaskAssigneeChanged,
        plainTextContents: `This is a test for task assignee update alert. ${email}`,
        message: `${title} task is assigned to ${firstName}`,
        info: EmailInfo.TaskAssigneeChanged,
        buttonText: EmailButtonText.TaskAssigneeChanged,
        // module: 'tasks',
        callToActionLink: `${this.Url}/tasks/${id}`
      };
      await this.sendEmail(emailDetails);
      this.logger.debug("Task assignee update email sent successfully.");
    } catch (error) {
      this.logger.error("Failed to send task assignee update email", error);
    }
  }

  @OnEvent('contact.assign_to.changed', { async: true })
  async onContactAssignToUpdateEvent(payload: any) {
    try {
      const { updatedContact } = payload;
      const { firstName, lastName, assignTo } = updatedContact;
      const { userName, email } = assignTo;
      this.logger.debug("Contact assign to update event in email alert service");
      const id = updatedContact?._id;
      const emailDetails: EmailDetails = {
        firstName,
        toEmail: email,
        subject: EmailSubject.ContactAssignToChanged,
        plainTextContents: `This is a test for contact assign to update alert. ${email}`,
        message: `${firstName + ' ' + lastName} contact is assigned to ${userName}`,
        info: EmailInfo.ContactAssignToChanged,
        buttonText: EmailButtonText.ContactAssignToChanged,
        // module: 'contacts',
        id,
        callToActionLink: `${this.Url}/contacts/${id}`
      };
      await this.sendEmail(emailDetails);
      this.logger.debug("Contact assign to update email sent successfully.");
    } catch (error) {
      this.logger.error("Failed to send contact assign to update email", error);
    }
  }

  @OnEvent('interview.schedule', { async: true })
  async handleInterviewScheduleEvent(payload: any) {
    try {
      // Log the entire payload for debugging purposes
      //  this.logger.log(`Payload: ${JSON.stringify(payload, null, 2)}`);

      // Destructure payload, providing default values or handling missing properties
      const {
        jobApplication: {
          _id = '',
          firstName = 'N/A',
          lastName = 'N/A',
          contactDetails = {},
        } = {},
        meetingUrl = 'N/A',
        meetingId = 'N/A',
        meetingCode = 'N/A',
        interviewDate = 'N/A',
        technicalPanel = []
      } = payload;

      // Ensure contactEmail is extracted safely with optional chaining
      const contactEmail = contactDetails?.contactEmail;

      //  this.logger.log(`Contact Email: ${contactEmail}`);

      // const createCalenderDto: CreateCalendarDto = {
      //   title: `${firstName} ${lastName} Interview`,
      //   startTime: new Date(interviewDate),
      //   location: contactDetails?.location || 'virtual',
      //   description: `Meeting ID: ${meetingId}, Meeting Code: ${meetingCode}`,
      //   url: meetingUrl,
      //   jobApplication: _id
      // };

      // const calendarEvent: any = await this.calendarService.create(createCalenderDto)

      const emailDetails: EmailDetails = {
        firstName: firstName,
        toEmail: contactEmail,
        subject: EmailSubject.InterviewSchedule,
        plainTextContents: `Interview email alert. ${contactEmail}`,
        // calendarEvent,
        message: EmailMessage.InterviewSchedule,
        info: `Meeting ID: ${meetingId}, Meeting Code: ${meetingCode}`,
        buttonText: EmailButtonText.InterviewSchedule,
        sendHtml: true,
      };
      // this.logger.log(`email: ${JSON.stringify(emailDetails, null, 2)}`);
      await this.sendEmail(emailDetails);
      this.logger.log(`Interview email sent successfully to ${contactEmail}`);

      technicalPanel.forEach((member: { firstName: any; email: any; }) => {
        const emailDetails: EmailDetails = {
          firstName: member.firstName,
          toEmail: member.email, // Send email to the technical panel member's email
          subject: `Interview scheduled with ${firstName} ${lastName}`,
          plainTextContents: `Interview email alert for ${firstName} ${lastName}`,
          // calendarEvent,
          message: `You have been scheduled to conduct an interview with the candidate. Below are the details of the interview:`,
          info: `Meeting ID: ${meetingId}, Meeting Code: ${meetingCode}`,
          buttonText: EmailButtonText.InterviewSchedule,
          sendHtml: true,
        };

        this.sendEmail(emailDetails);

        // Log the email details for debugging
        // this.logger.log(`Email details: ${JSON.stringify(emailDetails, null, 2)}`);
      });
    } catch (error) {
      this.logger.error(`Failed to send interview email- ${error}`);
    }
  }

  @OnEvent('assessment.schedule', { async: true })
  async handleAssessmentScheduleEvent(payload: any) {
    try {
      //  this.logger.log(`Payload: ${JSON.stringify(payload, null, 2)}`);

      const {
        jobApplication: {
          _id = '',
          firstName = 'N/A',
          lastName = 'N/A',
          contactDetails = {},
        } = {},
        message,
        subject,
        assessmentFiles = [] as FileMetadata[]
      } = payload;

      this.logger.log(`Contact Details: ${JSON.stringify(contactDetails, null, 2)}`);
      this.logger.log(`Assessment Files: ${JSON.stringify(assessmentFiles, null, 2)}`);


      const contactEmail = contactDetails?.contactEmail;
      const assessmentUrls = assessmentFiles
        .map((file: FileMetadata) => `<a href="${file.locationUrl}">${file.originalName}</a>`)
        .join('<br>');  // Join with line breaks for better readability

      const emailDetails: EmailDetails = {
        firstName: firstName,
        toEmail: contactEmail,
        subject: subject,
        plainTextContents: `Assessment email alert. ${contactEmail}`,
        message: `
                ${message}
                Assessment Links: 
                ${assessmentUrls}`,
        info: `Assessment Files: ${assessmentFiles.map((file: { originalName: any; }) => file.originalName).join(', ')}`,
        // buttonText: EmailButtonText.InterviewSchedule,
        sendHtml: true,
      };
      await this.sendEmail(emailDetails);
      this.logger.log(`Assessment email sent successfully to ${contactEmail}`);

    } catch (error) {
      this.logger.error(`Failed to send assessment email- ${error}`);
    }
  }

  @OnEvent('offer.released', { async: true })
  async handleOfferEvent(payload: any) {
    try {
      //  this.logger.log(`Payload: ${JSON.stringify(payload, null, 2)}`);

      const {
        jobApplication: {
          _id = '',
          firstName = 'N/A',
          lastName = 'N/A',
          contactDetails = {},
        } = {},
        salaryPerAnnum,
        dateOfJoining,
      } = payload;

      this.logger.log(`Contact Details: ${JSON.stringify(contactDetails, null, 2)}`);


      const contactEmail = contactDetails?.contactEmail;

      const emailDetails: EmailDetails = {
        firstName: firstName,
        toEmail: contactEmail,
        subject: "Offer Letter",
        plainTextContents: `Offer email alert. ${contactEmail}`,
        message: `Offer Letter released to ${firstName} ${lastName}`,
        info: `Offer letter released `,
        buttonText: "Accept",
        sendHtml: true,
      };
      await this.sendEmail(emailDetails);
      this.logger.log(`Assessment email sent successfully to ${contactEmail}`);

    } catch (error) {
      this.logger.error(`Failed to send assessment email- ${error}`);
    }
  }

  @OnEvent('org.created')
  async onOrgCreatedEvent(payload: any) {
    try {
      this.logger.debug(payload);
      this.logger.debug("Org created event in Email Alert service");
      const { title, email } = payload;
      const resolvedfirstName = title
      const emailDetails: EmailDetails = {
        firstName: resolvedfirstName,
        toEmail: email,
        subject: EmailSubject.OrgCreated,
        plainTextContents: `This is a test for verify email alert. ${email}`,
        message: EmailMessage.OrgCreated,
        info: EmailInfo.OrgCreated,
        sendHtml: true,
      };
      await this.sendEmail(emailDetails);
      this.logger.debug("Email sent successfully.");
    } catch (error) {
      this.logger.error("Failed to send verification email", error);
    }
  }

  @OnEvent('accountOrg.created')
  async onAccountOrgCreatedEvent(payload: any) {
    try {
      this.logger.debug(payload);
      this.logger.debug("Org created event in Email Alert service");
      const { title, email, createdBy } = payload;
      const resolvedfirstName = title
      const emailDetails: EmailDetails = {
        firstName: resolvedfirstName,
        toEmail: email,
        subject: EmailSubject.AccountCreated,
        plainTextContents: `This is a test for verify email alert. ${email}`,
        message: `${EmailMessage.AccountCreated} by ${createdBy}.`,
        info: EmailInfo.AccountCreated,
        sendHtml: true,
      };
      await this.sendEmail(emailDetails);
      this.logger.debug("Email sent successfully.");
    } catch (error) {
      this.logger.error("Failed to send verification email", error);
    }
  }

  //mail trigger to send an email to the user when the account is assigned to user
  @OnEvent('accountOrg.assignTo')
  async onAccountAssignedEvent(payload: any) {
    try {
      this.logger.debug(payload);
      this.logger.debug("Account assigned event in Email Alert service");
      const { title, email, assignTo, createdBy } = payload;
      const resolvedfirstName = assignTo;
      const emailDetails: EmailDetails = {
        firstName: resolvedfirstName,
        toEmail: email,
        subject: EmailSubject.AccountAssigned,
        plainTextContents: `This is a test for verify email alert. ${email}`,
        message: `${title} ${EmailMessage.AccountAssigned} by ${createdBy}.`,
        info: EmailInfo.AccountAssigned,
        sendHtml: true,
      };
      await this.sendEmail(emailDetails);
      this.logger.debug("Email sent successfully.");
    } catch (error) {
      this.logger.error("Failed to send verification email", error);
    }
  }

  //mail trigger to send an email to the user when the client is assigned to user
  @OnEvent('clientOrg.assignTo')
  async onClientAssignedEvent(payload: any) {
    try {
      this.logger.debug(payload);
      this.logger.debug("Client assigned event in Email Alert service");
      const { title, email, assignTo, createdBy } = payload;
      const resolvedfirstName = assignTo;
      const emailDetails: EmailDetails = {
        firstName: resolvedfirstName,
        toEmail: email,
        subject: EmailSubject.ClientAssigned,
        plainTextContents: `This is a test for verify email alert. ${email}`,
        message: `${title} ${EmailMessage.ClientAssigned} by ${createdBy}.`,
        info: EmailInfo.ClientAssigned,
        sendHtml: true,
      };
      await this.sendEmail(emailDetails);
      this.logger.debug("Email sent successfully.");
    } catch (error) {
      this.logger.error("Failed to send verification email", error);
    }
  }


  //mail trigger to send an email to the user when the contact is assigned to user
  @OnEvent('contact.assignTo')
  async onContactAssignedEvent(payload: any) {
    try {
      this.logger.debug(payload);
      this.logger.debug("Contact assigned event in Email Alert service");
      const { title, email, assignTo, createdBy } = payload;
      const resolvedfirstName = assignTo;
      const emailDetails: EmailDetails = {
        firstName: resolvedfirstName,
        toEmail: email,
        subject: EmailSubject.ContactAssigned,
        plainTextContents: `This is a test for verify email alert. ${email}`,
        message: `${title} ${EmailMessage.ContactAssigned} by ${createdBy}.`,
        info: EmailInfo.ContactAssigned,
        sendHtml: true,
      };
      await this.sendEmail(emailDetails);
      this.logger.debug("Email sent successfully.");
    } catch (error) {
      this.logger.error("Failed to send verification email", error);
    }
  }


  @OnEvent('org.created.verify')
  async onOrgVeifyEvent(payload: any) {
    try {
      this.logger.debug(payload);
      this.logger.debug("Org created otp sent event in Email Alert service");
      const { title, email, otpCode } = payload;
      const resolvedfirstName = title
      const emailDetails: EmailDetails = {
        firstName: resolvedfirstName,
        toEmail: email,
        otpCode,
        subject: EmailSubject.Welcome,
        plainTextContents: `This is a test for verify email alert. ${email}`,
        message: EmailMessage.OrgCreatedVerify,
        info: EmailInfo.OrgCreatedVerify,
        sendHtml: true,
      };
      await this.sendEmail(emailDetails);
      this.logger.debug("Email sent successfully.");
    } catch (error) {
      this.logger.error("Failed to send verification email", error);
    }
  }

  @OnEvent('org.resend.otp')
  async handleResendOrgOtpEvent(payload: any) {
    try {
      this.logger.debug(payload);
      this.logger.debug("OTP resend event in Email Alert service for org");
      const { title, email, otpCode } = payload;
      const resolvedfirstName = title
      const emailDetails: EmailDetails = {
        firstName: resolvedfirstName,
        toEmail: email,
        otpCode,
        subject: EmailSubject.OTPResend,
        plainTextContents: `Your OTP code is: ${otpCode}. It is valid for 10 minutes.`,
        message: 'Please use the OTP below to verify your email and complete your organization registration with Talsy.',
        sendHtml: true,
      };
      await this.sendEmail(emailDetails);
      this.logger.debug("Email sent successfully.");
    } catch (error) {
      this.logger.error("Failed to send OTP resend email", error);
    }
  }

  @OnEvent('org.rejectOrgAdmin')
  async handleOrgAdminRejected(payload: any) {
    try {
      const { existingOrg, updatedOrg } = payload;

      if (!updatedOrg?.contactDetails?.[0]?.contactEmail) {
        this.logger.error(`No contact email found for organization ${updatedOrg?._id}`);
        return;
      }

      const toEmail = updatedOrg.contactDetails[0].contactEmail;
      const customerTitle = updatedOrg.title || 'Valued Customer';

      // Determine if the org was approved or rejected based on isApproved and isRejected flags
      let emailSubject = '';
      let emailMessage = '';
      let emailInfo = '';

      // if (updatedOrg.isRejected || updatedOrg.isSuspended) {
      emailSubject = 'Customer Account Rejected';
      emailMessage = `Your Customer account "${customerTitle}" has been rejected`;
      emailInfo = 'We regret to inform you that your Customer account registration has been rejected.<br><br> For more information, please contact at <a href="mailto:<EMAIL>"><EMAIL></a>';
      // }

      const emailDetails: EmailDetails = {
        firstName: customerTitle,
        toEmail,
        subject: emailSubject,
        plainTextContents: `${emailMessage}\n\n${emailInfo}`,
        message: emailMessage,
        info: emailInfo,
        sendHtml: true,
        // buttonText: EmailButtonText.Login,
        // callToActionLink: `${this.Url}/auth/login`
      };

      await this.sendEmail(emailDetails);
      this.logger.log(`Status update email sent to ${toEmail} for Customer ${customerTitle}`);

    } catch (error) {
      this.logger.error(`Failed to send Customer status update email: ${error.message}`, error);
      throw error;
    }
  }

  @OnEvent('file.customer.reupload')
  async handleFileReUploadEvent(payload: any) {
    try {
      const { org } = payload;

      this.logger.log(JSON.stringify(payload))
      // Fetch organization details to get contact email
      const organization = await this.orgModel
        .findById(org)
        .lean() as Org;

      const adminEmail = this.configService.get<string>('ADMIN_EMAIL');
      let toEmail = adminEmail || '<EMAIL>';

      // Prepare email content based on status
      let emailSubject = 'Documents Re-uploaded';
      let emailMessage = `${org.title} has re-uploaded the documents`;
      let emailInfo = 'Please check the document list and update the status accordingly.';


      const emailDetails: EmailDetails = {
        firstName: 'Admin',
        toEmail: toEmail,
        subject: emailSubject,
        plainTextContents: `${emailMessage}\n\n${emailInfo}`,
        message: emailMessage,
        info: emailInfo,
      };

      await this.sendEmail(emailDetails);
      this.logger.log(`Status update email sent to ${toEmail}`);
    } catch (error) {
      this.logger.error(`Failed to send file status update email: ${error.message}`, error);
    }
  }

  @OnEvent('customer.register.admin')
  async handleCustomerRegisterEvent(payload: any) {
    try {
      const { title } = payload;

      // this.logger.log(JSON.stringify(payload))

      const adminEmail = this.configService.get<string>('ADMIN_EMAIL');
      let toEmail = adminEmail || '<EMAIL>';

      // Prepare email content based on status
      let emailSubject = `${title} Registered`;
      let emailMessage = `The organization <strong>${title}</strong> has registered on Talsy.`;
      let emailInfo = `Please review the registration details and update the status accordingly.`;

      const emailDetails: EmailDetails = {
        firstName: 'Admin',
        toEmail: toEmail,
        subject: emailSubject,
        plainTextContents: `${emailMessage}\n\n${emailInfo}`,
        message: emailMessage,
        info: emailInfo,
      };

      await this.sendEmail(emailDetails);
      this.logger.log(`Status update email sent to ${toEmail}`);
    } catch (error) {
      this.logger.error(`Failed to send file status update email: ${error.message}`, error);
    }
  }

  @OnEvent('bgv.document.upload')
  async handleBgvDocumentUploadEvent(payload: any) {
    try {
      const { application, jobDetails, applicationId } = payload;

      this.logger.log(JSON.stringify(payload))
      // Fetch organization details to get contact email
      // const organization = await this.orgModel
      //   .findById(org)
      //   .lean() as Org;

      let toEmail: string | undefined;
      const candidatename = `${application.firstName} ${application.lastName}`;

      toEmail = application.contactDetails?.contactEmail;
      if (!toEmail) {
        this.logger.error(`No valid email found for candidate ${candidatename}`);
        return;
      }

      // Prepare email content based on status
      let emailSubject = '';
      let emailMessage = '';
      let emailInfo = '';

      const applicationencodeBase64 = Buffer.from(new TextEncoder().encode(applicationId)).toString('base64');
      const frontEndUrl = this.configService.get<string>('API_URL');
      let payRollOrg;
      // if(jobDetails.employmentType === 'contract'){
      payRollOrg = jobDetails?.endClientOrg?.title;
      // }
      emailSubject = `"${payRollOrg}:" BGV Documents Upload`;
      emailMessage = `Please upload your BGV documents`;
      // emailInfo = `The document "${updatedFile.originalName}" has been reviewed and needs to be updated. Please review and resubmit by visiting below Url<br><a href="${frontEndUrl}/re-upload/${OrgencodeBase64}">${frontEndUrl}/re-upload</a>`;
      emailInfo = `We are pleased to inform you that, An Offer Has be relesed for you for ${jobDetails.title} position.<br>
                  Kindly click the link below to upload the required background verification (BGV) documents.<br> Please ensure all documents are clear, valid, and submitted within the given timeline to avoid any delays in the onboarding process.
                  <br><a href="${frontEndUrl}/candidate-bgv/${applicationencodeBase64}">${frontEndUrl}/candidate-bgv</a>`;


      const emailDetails: EmailDetails = {
        firstName: candidatename,
        toEmail,
        subject: emailSubject,
        plainTextContents: `${emailMessage}\n\n${emailInfo}`,
        message: emailMessage,
        info: emailInfo,
      };
      if (emailSubject) {
        await this.sendEmail(emailDetails);
      }
      this.logger.log(`Documents Collection email sent to ${toEmail} for ${candidatename}`);
    } catch (error) {
      this.logger.error(`Failed to send Documents Collection email: ${error.message}`, error);
    }
  }

  @OnEvent('bgv.initaited')
  async handleBgvInitiateEvent(payload: any) {
    try {
      const { application, jobDetails, bgvHandler, applicationId } = payload;

      this.logger.log(JSON.stringify(payload))
      // Fetch organization details to get contact email
      // const organization = await this.orgModel
      //   .findById(org)
      //   .lean() as Org;

      const toEmail = bgvHandler.email;
      const candidatename = `${application.firstName} ${application.lastName}`;

      // Prepare email content based on status
      let emailSubject = '';
      let emailMessage = '';
      let emailInfo = '';

      // let payRollOrg;
      // if(jobDetails.employmentType === 'contract'){
      // payRollOrg = jobDetails?.endClientOrg?.title;
      // }
      emailSubject = `BGV Assigned: Candidate ${candidatename} has been allocated`;
      emailMessage = `A candidate has been assigned to you for background verification`;
      // emailInfo = `The document "${updatedFile.originalName}" has been reviewed and needs to be updated. Please review and resubmit by visiting below Url<br><a href="${frontEndUrl}/re-upload/${OrgencodeBase64}">${frontEndUrl}/re-upload</a>`;
      emailInfo = `We would like to inform you that <strong>${candidatename}</strong> has been assigned to you for BGV processing for the position <strong>${jobDetails.title}</strong>.<br><br>
      Please log in to the platform and review the candidate's uploaded documents. Once reviewed, kindly upload the corresponding background verification (BGV) report to complete the process.<br><br>
      To proceed, log in to your account.<br>
      We appreciate your prompt action to help ensure timely onboarding.`;

      const emailDetails: EmailDetails = {
        firstName: bgvHandler.title,
        toEmail,
        subject: emailSubject,
        plainTextContents: `${emailMessage}\n\n${emailInfo}`,
        message: emailMessage,
        info: emailInfo,
        buttonText: EmailButtonText.Login,
        callToActionLink: `${this.Url}/auth/login`
      };
      if (emailSubject) {
        await this.sendEmail(emailDetails);
      }
      this.logger.log(`Documents Collection email sent to ${toEmail} for ${candidatename}`);
    } catch (error) {
      this.logger.error(`Failed to send Documents Collection email: ${error.message}`, error);
    }
  }

}