import { Injectable, Logger, BadRequestException, NotFoundException, UnauthorizedException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { CreateCalendarDto } from './dto/create-calender.dto';
import { UpdateCalendarDto } from './dto/update-calendar.dto';
import { Calendar, CalendarDocument } from './schemas/calender.schema';
import { google } from 'googleapis';
import { CalendarType, IntegrationType } from 'src/shared/constants';
import { Integration, IntegrationDocument } from 'src/integrations/schemas/integrations.schema';
import { format } from 'date-fns';
import { EventEmitter2 } from '@nestjs/event-emitter';
import * as crypto from 'crypto';
import { ZoomWebhookDto } from './dto/zoom-webhook.dto';
@Injectable()
export class CalendarService {
  private readonly logger = new Logger(CalendarService.name);

  constructor(
    @InjectModel(Calendar.name) private readonly calendarModel: Model<CalendarDocument>,
    @InjectModel(Integration.name) private readonly integrationModel: Model<IntegrationDocument>,
    private eventEmitter: EventEmitter2
  ) { }

  async create(createCalendarDto: CreateCalendarDto, user: any) {
    try {
      const calendarEvent = new this.calendarModel(createCalendarDto);
      if (createCalendarDto.calendarType === CalendarType.GoogleMeet) {
        const integration = await this.integrationModel.findOne({
          integrationType: IntegrationType.GoogleMeet,
          ...(user.org?._id
            ? { org: user.org._id }
            : { createdBy: user._id }),
        });

        // this.logger.log(JSON.stringify(integration))
        if (!integration || !integration.refreshToken) {
          throw new BadRequestException('Google Meet integration not found or not authorized');
        }

        const oauth2Client = new google.auth.OAuth2(
          integration.clientId,
          integration.clientSecret,
          'https://talency-frontend.vercel.app/settings/integrations',
        );

        oauth2Client.setCredentials({
          refresh_token: integration.refreshToken,
        });

        // Get a new access token
        const { credentials } = await oauth2Client.refreshAccessToken();
        oauth2Client.setCredentials(credentials);

        const calendar = google.calendar({ version: 'v3', auth: oauth2Client });

        const { startTime, endTime } = createCalendarDto;

        const event = {
          summary: createCalendarDto.title,
          description: createCalendarDto.description,
          start: {
            dateTime: startTime,
            timeZone: 'Asia/Kolkata',
          },
          end: {
            dateTime: endTime ?? new Date(new Date(startTime).getTime() + 60 * 60 * 1000).toISOString(),
            timeZone: 'Asia/Kolkata',
          },
          attendees: createCalendarDto.attendees?.map(email => ({ email })) ?? [],
          conferenceData: {
            createRequest: {
              requestId: `${Date.now()}`, // unique per request
              conferenceSolutionKey: { type: 'hangoutsMeet' },
            },
          },
        };

        const response = await calendar.events.insert({
          calendarId: 'primary',
          requestBody: event,
          conferenceDataVersion: 1,
        });

        const meetLink = response.data?.hangoutLink;
        calendarEvent.meetLink = meetLink || '';
      }

      if (createCalendarDto.calendarType === CalendarType.ZoomMeet || createCalendarDto.calendarType === CalendarType.ZoomMeetPremium) {
        const integration = await this.integrationModel.findOne({
          integrationType: createCalendarDto.calendarType,
          ...(user.org?._id
            ? { org: user.org._id }
            : { createdBy: user._id }),
        });

        // this.logger.log(JSON.stringify(integration))

        if (!integration || !integration.refreshToken) {
          throw new BadRequestException('Zoom integration not found or not authorized');
        }

        const zoomClientId = integration.clientId;
        const zoomClientSecret = integration.clientSecret;

        const basicAuthToken = Buffer.from(`${zoomClientId}:${zoomClientSecret}`).toString('base64');

        const tokenResponse = await fetch('https://zoom.us/oauth/token', {
          method: 'POST',
          headers: {
            'Authorization': `Basic ${basicAuthToken}`,
            'Content-Type': 'application/x-www-form-urlencoded',
          },
          body: new URLSearchParams({
            grant_type: 'refresh_token',
            refresh_token: integration.refreshToken,
          }),
        });

        const tokenData = await tokenResponse.json();

        if (!tokenResponse.ok) {
          throw new BadRequestException(`Failed to refresh Zoom token: ${tokenData.reason || tokenData.error}`);
        }

        const accessToken = tokenData.access_token;
        const newRefreshToken = tokenData.refresh_token; // Capture new refresh token

        // Update the integration with the new refresh token
        if (newRefreshToken) {
          integration.refreshToken = newRefreshToken;
          await integration.save(); // Save the updated integration with new refresh token
        }

        const meetingPassword = Math.floor(100000 + Math.random() * 900000).toString(); // 6-digit numeric password

        const isPremium = createCalendarDto.calendarType === CalendarType.ZoomMeetPremium;

        const zoomMeetingResponse = await fetch('https://api.zoom.us/v2/users/me/meetings', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            topic: createCalendarDto.title,
            type: 2, // Scheduled meeting
            start_time: createCalendarDto.startTime,
            duration: createCalendarDto.endTime
              ? (new Date(createCalendarDto.endTime).getTime() - new Date(createCalendarDto.startTime).getTime()) / 60000
              : 60,
            timezone: 'Asia/Kolkata',
            agenda: createCalendarDto.description,
            password: meetingPassword,
            settings: {
              join_before_host: false,
              approval_type: 0,
              registration_type: 1,
              enforce_login: false,
              waiting_room: true,
              ...(isPremium && {
                auto_recording: 'cloud',
                // alternative_hosts: user.email,
                mute_upon_entry: true,
                meeting_authentication: false,
                encryption_type: 'enhanced_encryption',
                focus_mode: true,
                host_video: false,
                participant_video: false,
                allow_multiple_devices: true,
                registrants_confirmation_email: true,
                registrants_email_notification: true,
                alternative_hosts_email_notification: true,
                approval_type: 0,
                audio: 'both',
                show_share_button: true,
                request_permission_to_unmute_participants: true,
                email_notification: true,
                breakout_room: {
                  enable: true,
                  rooms: [],
                },
                sign_language_interpretation: {
                  enable: false,
                  interpreters: [],
                },
                continuous_meeting_chat: {
                  enable: true,
                  auto_add_invited_external_users: false,
                },
                enable_dedicated_group_chat: true,
                push_change_to_calendar: true,
              }),
              ...(isPremium && createCalendarDto.attendees?.length && {
                meeting_invitees: createCalendarDto.attendees.map(email => ({ email })),
              }),
            },
          }),
        });

        const zoomData = await zoomMeetingResponse.json();

        if (!zoomMeetingResponse.ok) {
          throw new BadRequestException(`Failed to create Zoom meeting: ${zoomData.message}`);
        }

        calendarEvent.meetLink = zoomData.join_url;
        calendarEvent.meetingCode = meetingPassword;
        calendarEvent.zoomMeetingUUID = zoomData.uuid;
      }

      if (createCalendarDto.calendarType === CalendarType.MicrosoftTeams) {
        const integration = await this.integrationModel.findOne({
          integrationType: IntegrationType.MicrosoftTeams,
          ...(user.org?._id ? { org: user.org._id } : { createdBy: user._id }),
        });

        if (!integration || !integration.refreshToken) {
          throw new BadRequestException('Microsoft Teams integration not found or not authorized');
        }

        const hasDefaultScope = integration.scope?.includes('https://graph.microsoft.com/.default');
        const fallbackScopes = [
          'profile',
          'openid',
          'email',
          'https://graph.microsoft.com/User.Read',
          'https://graph.microsoft.com/OnlineMeetings.ReadWrite',
        ];

        const resolvedScope = hasDefaultScope
          ? 'https://graph.microsoft.com/.default'
          : (Array.isArray(integration.scope) && integration.scope.length > 0
            ? integration.scope.join(' ')
            : fallbackScopes.join(' '));

        const tokenResponse = await fetch(`https://login.microsoftonline.com/${integration.tenantId}/oauth2/v2.0/token`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
          body: new URLSearchParams({
            grant_type: 'refresh_token',
            client_id: integration.clientId!,
            client_secret: integration.clientSecret!,
            scope: resolvedScope,
            refresh_token: integration.refreshToken,
          }),
        });

        const tokenData = await tokenResponse.json();

        if (!tokenResponse.ok) {
          throw new BadRequestException(
            `Failed to get Microsoft access token: ${tokenData.error_description || tokenData.error}`
          );
        }

        const accessToken = tokenData.access_token;

        const meetingStart = createCalendarDto.startTime;
        const meetingEnd = createCalendarDto.endTime
          ?? new Date(new Date(meetingStart).getTime() + 60 * 60 * 1000).toISOString();

        const meetingPayload = {
          startDateTime: meetingStart,
          endDateTime: meetingEnd,
          subject: createCalendarDto.title,
          participants: {
            attendees: createCalendarDto.attendees?.map(email => ({
              upn: email,
              role: 'attendee',
            })) ?? [],
          },
        };

        const meetingResponse = await fetch('https://graph.microsoft.com/v1.0/me/onlineMeetings', {
          method: 'POST',
          headers: {
            Authorization: `Bearer ${accessToken}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(meetingPayload),
        });

        const meetingData = await meetingResponse.json();

        if (!meetingResponse.ok) {
          const teamsError = meetingData.error?.message || 'Unknown error';
          throw new BadRequestException(`Failed to create Microsoft Teams meeting: ${teamsError}`);
        }

        calendarEvent.meetLink = meetingData.joinWebUrl;
      }

      // Find the latest calendar event for the same job application
      const oldCalendar = await this.calendarModel.findOne({
        jobApplication: createCalendarDto.jobApplication.toString(),
      }).sort({ createdAt: -1 }); // Ensure you get the latest one

      // If an old calendar event exists, mark it as cancelled
      if (oldCalendar) {
        oldCalendar.isCancelled = true;
        await oldCalendar.save();
      }

      await calendarEvent.save();
      return calendarEvent;

    } catch (error) {
      this.logger.error(`Failed to create calendar: ${error.message}`, error.stack);
      throw new BadRequestException(`Error creating calendar. ${error.message}`);
    }
  }

  async findAll(page: number, limit: number, orgId?: string, title?: string, email?: string) {
    try {
      const filter: any = {};
      if (orgId) filter.org = orgId;
      if (title) {
        const regex = new RegExp(title, 'i');
        filter.title = { $regex: regex };
      }
      if (email) {
        filter.attendees = { $in: [email] };
      }

      const calendars = await this.calendarModel.find(filter)
        .populate({ path: 'createdBy', select: '_id roles firstName' })
        .skip((page - 1) * limit)
        .limit(limit)
        .sort({ createdAt: -1 })
        .exec();

      // Fetch Zoom recordings for ZoomMeetPremium events
      // const enrichedCalendars = await Promise.all(
      //   calendars.map(async (calendar) => {
      //     if (calendar.calendarType === CalendarType.ZoomMeetPremium) {
      //       try {
      //         const recordingDetails = await this.getZoomRecordings(calendar._id);
      //         this.logger.debug(`Zoom recording fetched for calendar ${calendar._id}`);
      //         return {
      //           ...calendar.toObject(),
      //           recordingDetails,
      //         };
      //       } catch (err) {
      //         // Log and proceed with the calendar without recordings
      //         // this.logger.error(`Zoom recording fetch failed for calendar ${calendar._id}: ${err.message}`);
      //         return calendar.toObject(); // still return the calendar
      //       }
      //     }
      //     return calendar.toObject();
      //   })
      // );

      return calendars;

    } catch (error) {
      this.logger.error(`Failed to fetch calendars: ${error.message}`, error.stack);
      throw new BadRequestException(`Error fetching calendars. ${error.message}`);
    }
  }

  async findByUser(page: number, limit: number, user: any) {
    try {
      return await this.calendarModel.find({ createdBy: user._id })
        .populate({ path: 'createdBy', select: '_id roles firstName' })
        .skip((page - 1) * limit)
        .limit(limit)
        .sort({ createdAt: -1 })
        .exec();
    } catch (error) {
      this.logger.error(`Failed to fetch calendars for user: ${error.message}`, error.stack);
      throw new BadRequestException(`Error fetching calendars. ${error.message}`);
    }
  }

  async findOne(calendarId: Types.ObjectId) {
    try {
      const calendar = await this.calendarModel.findById(calendarId);
      if (!calendar) {
        throw new NotFoundException('Calendar not found');
      }
      return calendar;
    } catch (error) {
      this.logger.error(`Failed to find calendar: ${error.message}`, error.stack);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(`Error retrieving calendar. ${error.message}`);
    }
  }

  async update(calendarId: Types.ObjectId, updateDto: UpdateCalendarDto, user: any) {
    try {
      const updated = await this.calendarModel.findByIdAndUpdate(calendarId, updateDto, { new: true });
      if (!updated) {
        throw new NotFoundException('Calendar not found');
      }

      const formattedScheduledAt = format(
        new Date(new Date(updated.startTime).getTime() + new Date().getTimezoneOffset()),
        'MMM dd yyyy, h:mm a'
      );

      // this.logger.log(formattedScheduledAt);
      // Emit an event for meeting update
      this.eventEmitter.emit('calendar.updated', {
        user: user, data: {
          ...updated.toJSON(),
          // scheduledAt: format(new Date(updatedMeeting.scheduledAt), 'MMM dd yyyy, h:mm a')
          startTime: formattedScheduledAt
        }
      });
      return updated;
    } catch (error) {
      this.logger.error(`Failed to update calendar: ${error.message}`, error.stack);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(`Error updating calendar. ${error.message}`);
    }
  }

  async remove(calendarId: Types.ObjectId) {
    try {
      const deleted = await this.calendarModel.findByIdAndDelete(calendarId);
      if (!deleted) {
        throw new NotFoundException('Calendar not found');
      }
      return deleted;
    } catch (error) {
      this.logger.error(`Failed to delete calendar: ${error.message}`, error.stack);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(`Error deleting calendar. ${error.message}`);
    }
  }

  async count(orgId?: string, email?: string, title?: string): Promise<Object> {
    const query: any = {};
    if (orgId) query.org = orgId;

    if (email) {
      query.attendees = { $in: [email] };
    }
    if (title) {
      const regex = new RegExp(title, 'i');
      query.title = { $regex: regex };
    }

    const meetingsCount = await this.calendarModel.countDocuments(query).exec();
    return {
      count: meetingsCount
    };
  }

  async findOneByLink(meetingLink: string) {
    // Find the meeting by the unique meeting link
    const meeting = await this.calendarModel
      .findOne({ meetLink: meetingLink })
      .populate({ path: 'org', select: '_id title', model: 'Org' })
      .populate({ path: 'createdBy', select: '_id roles firstName' })
      .exec();

    if (!meeting) {
      throw new NotFoundException('Meeting not found');
    }

    // Check current time
    const currentTime = new Date();

    const isValidLink =
      meeting.startTime >= currentTime;

    if (!isValidLink) {
      return {
        valid: false,
        reason: this.getDenyReason(meeting, currentTime)
      };
    }

    return {
      valid: true,
      meeting: meeting
    };
  }

  // Helper method to provide specific reasons for link invalidity
  private getDenyReason(meeting: Calendar, currentTime: Date): string {

    if (meeting.startTime < currentTime) {
      return 'Meeting time has already passed';
    }

    return 'Meeting is not currently available';
  }


  validateZoomWebhook(plainToken: string): { plainToken: string; encryptedToken: string } {

    const encryptedToken = crypto
      .createHmac('sha256', "SuXWkkSZQraplNsm1Rz6pg")
      .update(plainToken)
      .digest('hex');

    return {
      plainToken,
      encryptedToken,
    };
  }

  async handleZoomWebhook(body: ZoomWebhookDto): Promise<void> {
    const { event, payload } = body;

    if (event === 'recording.completed') {
      await this.handleRecordingCompleted(payload);
    } else {
      this.logger.error(`Unhandled Zoom event type: ${event}`);
    }
  }

  private async handleRecordingCompleted(payload: any): Promise<void> {
    const meetingId = payload.object.id;
    // const meetingTopic = payload.object.topic;
    const recordingFiles = payload.object.recording_files;

    const video = recordingFiles.find((f: { file_type: string }) => f.file_type === 'MP4');
    const transcript = recordingFiles.find((f: { file_type: string }) => f.file_type === 'TRANSCRIPT');

    this.logger.log(`Recording URL: ${video?.download_url}`);
    this.logger.log(`Transcript URL: ${transcript?.download_url}`);

    await this.calendarModel.updateOne({ meetingId }, { $set: { videoUrl: video?.download_url, transcriptUrl: transcript?.download_url } });
  }


  // calendar.service.ts

  async getZoomRecordings(calendarId: Types.ObjectId) {
    try {
      const calendar = await this.calendarModel.findById(calendarId);
      if (!calendar) {
        throw new NotFoundException('Calendar event not found');
      }

      if (!calendar.zoomMeetingUUID) {
        throw new BadRequestException('No Zoom meeting UUID found for this calendar event.');
      }

      const integration = await this.integrationModel.findOne({
        integrationType: CalendarType.ZoomMeetPremium,
        ...(calendar.org
          ? { org: calendar.org }
          : { createdBy: calendar.createdBy }),
      });

      if (!integration || !integration.refreshToken) {
        throw new BadRequestException('Zoom integration not found or not authorized.');
      }

      const basicAuth = Buffer.from(`${integration.clientId}:${integration.clientSecret}`).toString('base64');

      const tokenResponse = await fetch('https://zoom.us/oauth/token', {
        method: 'POST',
        headers: {
          Authorization: `Basic ${basicAuth}`,
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          grant_type: 'refresh_token',
          refresh_token: integration.refreshToken,
        }),
      });

      const tokenData = await tokenResponse.json();

      if (!tokenResponse.ok) {
        throw new BadRequestException(`Zoom token refresh failed: ${tokenData.reason || tokenData.error}`);
      }

      const accessToken = tokenData.access_token;
      const encodedUUID = encodeURIComponent(calendar.zoomMeetingUUID);

      const recordingRes = await fetch(`https://api.zoom.us/v2/meetings/${encodedUUID}/recordings`, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      });

      const recordingData = await recordingRes.json();

      if (!recordingRes.ok) {
        throw new BadRequestException(`Failed to fetch recordings: ${recordingData.message}`);
      }

      const newRefreshToken = tokenData.refresh_token; // Capture the new refresh token if available

      // Update the integration with the new refresh token if present
      if (newRefreshToken) {
        integration.refreshToken = newRefreshToken;
        await integration.save(); // Save the updated integration with the new refresh token
      }

      // return recordingData;
      return {
        meetingTopic: recordingData.topic,
        startTime: recordingData.start_time,
        totalSize: recordingData.total_size,
        recordingCount: recordingData.recording_count,
        shareUrl: recordingData.share_url,
        sharePasscode: recordingData.password,
        recording_play_passcode: recordingData.recording_play_passcode,
        recordings:
          recordingData.recording_files?.map((file: {
            id: string;
            file_type: string;
            recording_type: string;
            play_url: string;
            download_url: string;
            file_size: number;
            status: string;
          }) => ({
            id: file.id,
            fileType: file.file_type,
            recordingType: file.recording_type,
            playUrl: file.play_url,
            downloadUrl: file.download_url,
            fileSize: file.file_size,
            status: file.status,
          })),
      };
    } catch (error) {
      // this.logger.error(`Failed to fetch Zoom recordings: ${error.message}`, error.stack);
      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException(`Unexpected error: ${error.message}`);
    }
  }
}