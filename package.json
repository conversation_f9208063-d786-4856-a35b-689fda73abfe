{"name": "gana", "version": "0.0.343", "description": "The APIs for Talency.", "author": "CODING LIMITS PRIVATE LIMITED", "private": true, "license": "COPYRIGHTED", "scripts": {"build": "node --max-old-space-size=4096 ./node_modules/.bin/nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:debug": "nest start --debug --watch", "install:prod": "npm install --omit=dev --prefix dist", "start:prod": "node dist/main.js", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@aws-sdk/client-s3": "^3.608.0", "@aws-sdk/s3-request-presigner": "^3.609.0", "@fastify/compress": "^7.0.3", "@fastify/helmet": "^11.1.1", "@fastify/multipart": "^8.3.0", "@fastify/static": "^7.0.4", "@nest-lab/fastify-multer": "^1.2.0", "@nestjs-modules/mailer": "^2.0.2", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.3.0", "@nestjs/core": "^10.0.0", "@nestjs/event-emitter": "^2.0.4", "@nestjs/jwt": "^10.2.0", "@nestjs/mapped-types": "*", "@nestjs/mongoose": "^10.0.6", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^10.0.0", "@nestjs/platform-fastify": "^10.3.8", "@nestjs/platform-socket.io": "^10.4.6", "@nestjs/serve-static": "^4.0.2", "@nestjs/swagger": "^7.3.1", "@nestjs/websockets": "^10.4.6", "@socket.io/redis-adapter": "^8.3.0", "@types/imap-simple": "^4.2.9", "aws-sdk": "^2.1652.0", "axios": "^1.9.0", "bcrypt": "^5.1.1", "casbin": "^5.30.0", "casbin-mongoose-adapter": "^5.3.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "class-validator-mongo-object-id": "^1.4.0", "date-fns": "^3.6.0", "date-fns-tz": "^3.2.0", "googleapis": "^148.0.0", "handlebars": "^4.7.8", "ical-generator": "^7.1.0", "imap-simple": "^5.1.0", "ioredis": "^5.4.1", "json-path": "^0.1.3", "jsonpath-plus": "^10.1.0", "lodash": "^4.17.21", "mailparser": "^3.7.1", "mjml": "^4.15.3", "moment": "^2.30.1", "mongoose": "^8.3.4", "mongoose-sequence": "^6.0.1", "multer": "^1.4.5-lts.1", "nest-winston": "^1.10.0", "node-imap": "^0.9.6", "nodemailer": "^6.9.13", "passport": "^0.7.0", "passport-linkedin-oauth2": "^2.0.0", "pdf-parse": "^1.1.1", "pdf-to-text": "^0.0.7", "pino-http": "^9.0.0", "redis": "^4.7.0", "reflect-metadata": "^0.2.0", "rxjs": "^7.8.1", "sanitize-html": "^2.16.0", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "textract": "^2.5.0", "uuid": "^9.0.1", "winston": "^3.13.0"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/bcrypt": "^5.0.2", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/lodash": "^4.17.1", "@types/mailparser": "^3.4.5", "@types/mongoose-sequence": "^3.0.11", "@types/multer": "^1.4.11", "@types/node": "^20.3.1", "@types/node-imap": "^0.9.3", "@types/nodemailer": "^6.4.15", "@types/passport-linkedin-oauth2": "^1.5.6", "@types/pdf-parse": "^1.1.4", "@types/quoted-printable": "^1.0.2", "@types/sanitize-html": "^2.11.0", "@types/supertest": "^6.0.0", "@types/textract": "^2.4.5", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "cpx": "^1.5.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "npm-run-all": "^4.1.5", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}