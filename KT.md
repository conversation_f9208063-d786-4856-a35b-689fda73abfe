# Day one 

## Install NestJS starter app  
https://docs.nestjs.com/first-steps

`nest new gana --strict`

`--strict` helps the code to enforce typescript strict standards

## Switch the engine to Fastify instead of ExpressJS
https://docs.nestjs.com/techniques/performance

Fastify is supposed to be better in performance compared to ExpressJS

## To run your application in watch mode 
nest start --watch

## To hot reload your application
https://docs.nestjs.com/recipes/hot-reload


## Setting up config service  
https://docs.nestjs.com/techniques/configuration

Picks up data from .env file for Environment Variables.

Check usage in the services, app.module  for mongodb URI from .env file  
Create a .env file in the root folder and set up credentials  
```
APP_NAME=  
COMPANY_NAME=  
MONGODB_URI=  
SECRET_KEY=  
TOKEN_EXPIRY_PERIOD=
```

Import the config module globally in the app module

## Generate a module

`nest generate module things`

Generates a single file - things.module.ts

You could just learn about the basic module creation, and its imports, providers,   
https://docs.nestjs.com/modules

```
    imports: [],
    controllers: [],
    providers: [],
    exports: []
```

## Generate a resource  
https://docs.nestjs.com/recipes/crud-generator

`nest generate resource feature`

Options  - RESTful API, MicroService, WebSockets

```
  REST API 
  GraphQL (code first) 
  GraphQL (schema first) 
  Microservice (non-HTTP) 
  WebSockets
```
This creates a resource with RESTful APIs 

## Create DTOs 
Write fields in the create-DTO and update-DTO files. Use Validators and use these DTOs in controllers and services

## Integrate swagger

Follow the documentation in nestjs/swagger module

https://docs.nestjs.com/openapi/introduction  
we used swagger based operators - for documentation of the endpoints in controllers.

https://docs.nestjs.com/openapi/types-and-parameters  
we used swagger based annotations in DTOs for - again - documentation -  for validations as well.

## Some global settings at app level

Settings for compression, using a global prefix for API routes, CORS,

```

  // https://docs.nestjs.com/openapi/other-features#global-prefix
  app.setGlobalPrefix('api');

  // read https://docs.nestjs.com/security/cors
  app.enableCors();

  // read https://docs.nestjs.com/techniques/compression
  await app.register(compression);

```


## Setting up default nestjs/logger for application logging  

Implement an instance of Logger Class in the controller and service classes.Check usage in feature.service or feature.controller ...  
Log wherever possible

Note: File based logging yet to be implemented.

# Set up a schema folder 
Set up a schema folder in the src folder and in it create a schema file.  
Create schemas and add the definitions

## Connect with Mongodb database

https://docs.nestjs.com/techniques/mongodb  
Use the async configuration and connect to the mongodb database


## Do more DTO validations

we used `class-validator` library and tried using isNotEmpty, isEmail...etc  
https://dev.to/sarathsantoshdamaraju/nestjs-and-class-validator-cheat-sheet-13ao


## Exception handling and built in errors
https://docs.nestjs.com/exception-filters

this is a work in progress.

NotFoundException, UnauthorizedException, ... could be useful in sending a proper error status and message. 

Specific to MongoDB id type - done some error handling.  

Understand exceptions and report errors with try catch

### Validations detour - mongodb IDs in URL params and DTOs

For DTOs case- check `class-validator-mongo-object-id` on https://www.npmjs.com/package/class-validator-mongo-object-id

For URL params case - we used a custom pipe that takes the string value and validates for ObjectID format , throw an exception in case of invalidity.
Use the pipe in controllers.





## Implement Authentication  
https://docs.nestjs.com/security/authentication

End points that require the user to be logged in, needs to be protected.
Users should be able to login, register, change password, forgot password, delete account

## Implement Authorization - Role based access
https://docs.nestjs.com/security/authorization

An admin user can block a regular user
An admin user should be able to add a country, clients,
A recruiter cannot create a country, clients...
A candidate should be able to apply for a job, with logging, and without logging in....

1. Bring the list of users / user roles
2. Build a role based access control matrix

role | users | clients | countries | 
admin  | rwd | rwd | rwd
client | r | x | r

## Implement Nested Routing or Dynamic Routing 
https://docs.nestjs.com/recipes/router-module 

Create book and chapter resources. Implement nested routing by keeping book as parent module and chapter as child module.

## Implement event dispatcher
https://docs.nestjs.com/techniques/events

## Guards 
https://docs.nestjs.com/guards


## SchemaTypes

https://masteringjs.io/tutorials/mongoose/schematype

https://mongoosejs.com/docs/schematypes.html

https://mongoosejs.com/docs/schematypes.html#schematype-options

## Other Database

https://docs.nestjs.com/techniques/database


## Schema()

This is should be 

@Schema({timestamps:true})

Without this mongodb will not create "createdAt" and "updatedAt" fields. 

Without these fields - you can't build a timeline / activity for any document/ collection

## Postman
https://learning.postman.com/docs/collaborating-in-postman/sharing/

## regex search
https://stackoverflow.com/questions/********/how-to-find-items-using-regex-in-mongoose

## local time zone
https://stackoverflow.com/questions/********/how-to-obtain-a-local-timezone-date-from-a-date-string-in-javascript

## query 
https://www.mongodb.com/docs/manual/reference/operator/query/

https://github.com/nestjs/swagger/issues/280


## validate date
https://github.com/date-fns/date-fns/issues/1998

https://stackoverflow.com/questions/66068149/receive-a-string-and-verify-if-it-is-a-valid-date-with-date-fns

## sanitize

Why?

String inputs can have malicious data, often javascript stuff , that can lead to various issues related to security. 

https://stackoverflow.com/questions/64313399/how-to-properly-sanitize-nestjs-input

## principles

https://en.m.wikipedia.org/wiki/You_aren%27t_gonna_need_it










