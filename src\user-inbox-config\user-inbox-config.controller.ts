import { Controller, Get, Post, Body, Param, Put, Delete, UseGuards, Logger, Req, NotFoundException, BadRequestException, InternalServerErrorException, ParseUUIDPipe, Patch, Query, HttpException, HttpStatus } from '@nestjs/common';
import { UserInboxConfigService } from './user-inbox-config.service';
import { UserInboxConfig } from './schemas/user-inbox-config.schema';
import { CreateUserInboxConfigDto } from './dto/create-user-inbox-config.dto';
import { Roles } from 'src/auth/decorators/roles.decorator';
import { ApiBearerAuth, ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger'; // Import Swagger decorators
import { AuthJwtGuard } from 'src/auth/guards/auth-jwt/auth-jwt.guard';
import { RolesGuard } from 'src/auth/guards/roles/roles.guard';
import { Role } from 'src/auth/enums/role.enum';
import { validateObjectId } from 'src/utils/validation.utils';
import { ReplyEmailDto } from './dto/reply-email.dto';
import { ForwardEmailDto } from './dto/forward-email.dto';
import { SearchEmailDto } from './dto/search.dto';
import { CreateFolderDto } from './dto/create-folder.dto';
import { MoveEmailsDto } from './dto/move-email.dto';
import { FileMetadata } from 'src/file-upload/schemas/file-metadata.schema';
import { UpdateUserInboxConfigDto } from './dto/update-user-inbox-config.dto';

interface ConnectionTestResult {
  imapConnection: {
    success: boolean;
    error?: string;
  };
  smtpConnection: {
    success: boolean;
    error?: string;
  };
}


@Controller('')
@ApiTags('User-inbox-config')
export class UserInboxConfigController {
  private readonly logger = new Logger(UserInboxConfigController.name);

  constructor(private readonly userInboxConfigService: UserInboxConfigService) { }

  // POST: Create new User Inbox Configuration
  @Post()
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager, Role.TeamMember, Role.JobSeeker, Role.Freelancer)
  @Roles()
  @ApiResponse({ status: 201, description: `User inbox configuration is created.` })
  @ApiResponse({ status: 401, description: `Unauthorized.` })
  @ApiResponse({ status: 400, description: `Bad Request.` })
  // @ApiResponse({ status: 403, description: "Forbidden, the user doesn't have the required roles" })
  @ApiOperation({ summary: `Create a new User Inbox Configuration`, description: `This endpoint allows authorized users to create a new user inbox configuration. This endpoint is accessible to everyone.` })
  create(@Req() req: any, @Body() createUserInboxConfigDto: CreateUserInboxConfigDto) {
    return this.userInboxConfigService.create(createUserInboxConfigDto, req.user);
  }

  // PUT: Update User Inbox Configuration
  @Patch(':id')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager, Role.TeamMember, Role.JobSeeker, Role.Freelancer)
  @Roles()
  @ApiResponse({ status: 200, description: `User inbox configuration is updated.` })
  @ApiResponse({ status: 401, description: `Unauthorized.` })
  @ApiResponse({ status: 400, description: `Bad Request.` })
  // @ApiResponse({ status: 403, description: 'Forbidden.' })
  @ApiResponse({ status: 404, description: 'User inbox configuration not found.' })
  @ApiOperation({
    summary: `Update an existing User Inbox Configuration`,
    description: `This endpoint allows authorized users to update an existing user inbox configuration. This endpoint is accessible to everyone.`,
  })
  async update(
    @Param('id') id: string,
    @Req() req: any,
    @Body() updateUserInboxConfigDto: UpdateUserInboxConfigDto,
  ) {
    const objId = validateObjectId(id);

    // Call the service function to update the configuration
    return this.userInboxConfigService.update(objId, updateUserInboxConfigDto, req.user);
  }


  // POST: Create a new folder for the User Inbox Configuration
  @Post('create-folder')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager, Role.TeamMember, Role.JobSeeker, Role.Freelancer)
  @Roles()
  @ApiResponse({ status: 200, description: `Folder successfully created.` })
  @ApiResponse({ status: 404, description: `User Inbox Configuration not found.` })
  @ApiResponse({ status: 400, description: `Bad Request.` })
  // @ApiResponse({ status: 403, description: 'Forbidden,' })
  @ApiOperation({ summary: `Create a new folder in user inbox configuration`, description: `This endpoint allows authorized users to create a new folder in their inbox configuration. This endpoint is accessible to everyone` })
  async createFolder(
    @Req() req: any,
    @Body() createFolderDto: CreateFolderDto
  ): Promise<{ message: string }> {
    const userInboxConfigId = req.user.userInboxConfig._id;

    if (!userInboxConfigId) {
      throw new NotFoundException('User Inbox Configuration is missing.');
    }

    const userInboxConfig = await this.userInboxConfigService.findById(userInboxConfigId);

    if (!userInboxConfig) {
      throw new NotFoundException(`User Inbox Configuration with ID ${userInboxConfigId} not found.`);
    }
    try {
      // Call the service method to create the folder
      await this.userInboxConfigService.createFolder(userInboxConfig, createFolderDto.folderName);
      return { message: `Folder "${createFolderDto.folderName}" created successfully.` };
    } catch (error) {
      throw new InternalServerErrorException(`Failed to create folder: ${error.message}`);
    }
  }

  @Delete('delete-folder')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager, Role.TeamMember, Role.JobSeeker, Role.Freelancer)
  @Roles()
  @ApiResponse({ status: 200, description: `Folder successfully deleted.` })
  @ApiResponse({ status: 404, description: `User Inbox Configuration not found.` })
  @ApiResponse({ status: 400, description: `Bad Request.` })
  @ApiOperation({ summary: `Delete a folder from user inbox configuration`, description: `This endpoint allows authorized users to delete a folder in their inbox configuration. This endpoint is accessible to everyone.` })
  async deleteFolder(
    @Req() req: any,
    @Body() deleteFolderDto: CreateFolderDto
  ): Promise<{ message: string }> {
    const userInboxConfigId = req.user.userInboxConfig._id;

    if (!userInboxConfigId) {
      throw new NotFoundException('User Inbox Configuration is missing.');
    }

    const userInboxConfig = await this.userInboxConfigService.findById(userInboxConfigId);

    if (!userInboxConfig) {
      throw new NotFoundException(`User Inbox Configuration with ID ${userInboxConfigId} not found.`);
    }

    try {
      // Call the service method to delete the folder
      await this.userInboxConfigService.deleteFolder(userInboxConfig, deleteFolderDto.folderName);
      return { message: `Folder "${deleteFolderDto.folderName}" deleted successfully.` };
    } catch (error) {
      throw new InternalServerErrorException(`Failed to delete folder: ${error.message}`);
    }
  }


  // GET: Retrieve all User Inbox Configurations
  @Get()
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager, Role.TeamMember, Role.JobSeeker, Role.Freelancer)
  @Roles()
  @ApiResponse({ status: 200, description: `Retrieve all user inbox configurations.` })
  @ApiResponse({ status: 401, description: `Unauthorized.` })
  @ApiResponse({ status: 400, description: `Bad Request.` })
  // @ApiResponse({ status: 403, description: 'Forbidden,' })
  @ApiOperation({ summary: `Retrieve all User Inbox Configurations`, description: `This endpoint retrieves all user inbox configurations associated with the authenticated user. This endpoint is accessible to everyone` })
  findAll(): Promise<UserInboxConfig[]> {
    return this.userInboxConfigService.findAll();
  }

  @Get(':id')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager, Role.TeamMember, Role.JobSeeker, Role.Freelancer)
  @Roles()
  @ApiOperation({ summary: 'Retrieve a User Inbox Configuration by ID', description: 'This endpoint retrieves a user inbox configuration by ID associated with the authenticated user.' })
  @ApiResponse({ status: 200, description: 'Retrieve the user inbox configuration.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  @ApiResponse({ status: 404, description: 'Not Found.' })
  @ApiParam({ name: 'id', description: 'User inbox configuration ID' })
  async findById(@Param('id') id: string): Promise<UserInboxConfig> {
    const configId = validateObjectId(id); 
    return this.userInboxConfigService.findById(configId);
  }

  // GET: Test IMAP connection for a specific User Inbox Configuration
  // @Get(':id/test-connection')
  // @ApiBearerAuth()
  // @UseGuards(AuthJwtGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager, Role.TeamMember, Role.JobSeeker, Role.Freelancer)
  // @ApiResponse({ status: 200, description: `IMAP connection successful.` })
  // @ApiResponse({ status: 404, description: `User Inbox Configuration not found.` })
  // @ApiOperation({ summary: `Test IMAP Connection for User Inbox Configuration`, description: `This endpoint tests the connection to the IMAP server using the user's inbox configuration ID. The request will validate the ID and attempt to establish a connection to the IMAP server associated with the user's inbox configuration. If the connection is successful, a confirmation message will be returned. This endpoint is accessible to everyone` })
  // async testConnection(@Param('id') id: string): Promise<{ message: string }> {
  //   const userId = validateObjectId(id); // Validate the ID format
  //   const userInboxConfig = await this.userInboxConfigService.findById(userId);
  //   if (!userInboxConfig) {
  //     throw new NotFoundException(`User Inbox Configuration with ID ${userId} not found.`);
  //   }

  //   try {
  //     await this.userInboxConfigService.connectToImap(userInboxConfig); // Attempt to connect
  //     return { message: 'Connection to IMAP server successful.' };
  //   } catch (error) {
  //     throw new NotFoundException(`Connection to IMAP server failed: ${error.message}`);
  //   }
  // }

  @Get(':id/test-connection')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  @Roles(
    Role.Admin,
    Role.BUHead,
    Role.ResourceManager,
    Role.DeliveryManager,
    Role.TeamLead,
    Role.Recruiter,
    Role.AccountManager,
    Role.TeamMember,
    Role.JobSeeker,
    Role.Freelancer
  )
  @ApiOperation({
    summary: 'Test Email Connections',
    description: 'Tests both IMAP and SMTP connections using the user\'s inbox configuration ID. ' +
      'The endpoint validates the configuration and attempts to establish connections to both ' +
      'the IMAP and SMTP servers. Returns detailed connection status for both services.'
  })
  @ApiResponse({
    status: 200,
    description: 'Connection test results for both IMAP and SMTP servers.',
    schema: {
      type: 'object',
      properties: {
        imapConnection: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            error: { type: 'string', nullable: true }
          }
        },
        smtpConnection: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            error: { type: 'string', nullable: true }
          }
        }
      }
    }
  })
  @ApiResponse({ 
    status: 404, 
    description: 'User Inbox Configuration not found.' 
  })
  @ApiResponse({ 
    status: 500, 
    description: 'Internal server error during connection test.' 
  })
  async testConnections(
    @Param('id') id: string
  ): Promise<ConnectionTestResult> {
    const userId = validateObjectId(id);
    
    try {
      const result = await this.userInboxConfigService.testEmailConnections(userId);
      
      // If both connections failed, throw an error
      if (!result.imapConnection.success && !result.smtpConnection.success) {
        throw new HttpException({
          status: HttpStatus.BAD_REQUEST,
          error: 'Both IMAP and SMTP connections failed',
          details: result
        }, HttpStatus.BAD_REQUEST);
      }
      
      return result;
    } catch (error) {
      // Handle specific errors
      if (error instanceof NotFoundException) {
        throw error;
      }
      
      if (error instanceof HttpException) {
        throw error;
      }
      
      // Log unexpected errors
      this.logger.error(`Error testing connections: ${error.message}`, error.stack);
      throw new InternalServerErrorException(
        'An unexpected error occurred while testing connections'
      );
    }
  }


  @Get('fetch-emails-by-folder')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager, Role.TeamMember, Role.JobSeeker, Role.Freelancer)
  @Roles()
  @ApiOperation({ summary: `Fetch All Emails from User Folders`, description: `This endpoint retrieves emails from all folders based on the user's inbox configuration. It requires the user's inbox configuration ID, which is fetched from the authentication token. This endpoint is accessible to everyone` })
  async fetchEmailsByFolder(@Req() req: any,): Promise<any> {
    const userInboxConfigId = req.user.userInboxConfig._id;

    if (!userInboxConfigId) {
      throw new NotFoundException('User Inbox Configuration is missing.');
    }

    const userInboxConfig = await this.userInboxConfigService.findById(userInboxConfigId);

    if (!userInboxConfig) {
      throw new NotFoundException(`User Inbox Configuration with ID ${userInboxConfigId} not found.`);
    }
    try {
      // Fetch emails from all folders based on the user's inbox configuration
      const emails = await this.userInboxConfigService.fetchEmailsByFolders(userInboxConfig);
      return emails; // Return the fetched emails
    } catch (error) {
      // Handle any errors that may arise during the fetching process
      throw new InternalServerErrorException(`Failed to fetch emails: ${error.message}`);
    }
  }

  @Get('fetch-emails-by-folder/:folder')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager, Role.TeamMember, Role.JobSeeker, Role.Freelancer)
  @Roles()
  @ApiOperation({ summary: `Fetch Emails from a Specific Folder`, description: ` This endpoint allows users to fetch emails from a specific folder in their inbox. This endpoint is accessible to everyone` })
  async fetchEmailsFromFolder(
    @Req() req: any,
    @Param('folder') folder: string
  ): Promise<any> {
    // Fetch the UserInboxConfig based on configId
    const userInboxConfigId = req.user.userInboxConfig._id;


    if (!userInboxConfigId) {
      throw new NotFoundException('User Inbox Configuration is missing.');
    }

    const userInboxConfig = await this.userInboxConfigService.findById(userInboxConfigId);

    if (!userInboxConfig) {
      throw new NotFoundException(`User Inbox Configuration with ID ${userInboxConfigId} not found.`);
    }

    // Now fetch emails from the specified folder
    return await this.userInboxConfigService.fetchEmailsFromFolder(userInboxConfig, folder);
  }

  @Get('fetch-unseen/:folder')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager, Role.TeamMember, Role.JobSeeker, Role.Freelancer)
  @Roles()
  @ApiOperation({ summary: `Fetch Unseen Emails from Folder`, description: `This endpoint retrieves unseen emails from a specific folder for a user's inbox configuration. This endpoint is accessible to everyone` })
  async fetchUnseenEmailsFromFolder(
    @Req() req: any,
    @Param('folder') folder: string
  ): Promise<any> {
    const userInboxConfigId = req.user.userInboxConfig._id;

    if (!userInboxConfigId) {
      throw new NotFoundException('User Inbox Configuration is missing.');
    }

    const userInboxConfig = await this.userInboxConfigService.findById(userInboxConfigId);

    if (!userInboxConfig) {
      throw new NotFoundException(`User Inbox Configuration with ID ${userInboxConfigId} not found.`);
    }

    // Now fetch emails from the specified folder
    return await this.userInboxConfigService.fetchUnseenEmailsFromFolder(userInboxConfig, folder);
  }


  @Get('folder-unread-count/:folder')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager, Role.TeamMember, Role.JobSeeker, Role.Freelancer)
  @Roles()
  @ApiOperation({ summary: `Get unread email count for a specific folder`, description: `This endpoint retrieves the count of unread emails for a specified folder in the user's inbox configuration. This endpoint is accessible to everyone` })
  async countUnreadEmails(
    @Req() req: any,
    @Param('folder') folder: string
  ): Promise<{ unreadCount: number }> {
    const userInboxConfigId = req.user.userInboxConfig._id;

    if (!userInboxConfigId) {
      throw new NotFoundException('User Inbox Configuration is missing.');
    }

    const userInboxConfig = await this.userInboxConfigService.findById(userInboxConfigId);

    if (!userInboxConfig) {
      throw new NotFoundException(`User Inbox Configuration with ID ${userInboxConfigId} not found.`);
    }

    // Get the unread email count and assign it to the variable
    const unreadCount = await this.userInboxConfigService.countUnreadEmails(userInboxConfig, folder);

    // Return the unread count within an object
    return { unreadCount };
  }


  @Get('fetch-email/:uid/:folder')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager, Role.TeamMember, Role.JobSeeker, Role.Freelancer)
  @Roles()
  @ApiResponse({ status: 200, description: `Retrieve a single email from a folder.` })
  @ApiResponse({ status: 404, description: `User Inbox Configuration or Email not found.` })
  @ApiOperation({
    summary: `Retrieve a specific email by UID from a folder`,
    description: `This endpoint retrieves a single email identified by its UID from a specified folder in the user's inbox configuration. This endpoint is accessible to everyone.`
  })
  async fetchSingleEmail(
    @Req() req: any,
    @Param('uid') uid: string,
    @Param('folder') folder: string
  ): Promise<any> {
    const userInboxConfigId = req.user.userInboxConfig._id;

    if (!userInboxConfigId) {
      throw new NotFoundException('User Inbox Configuration is missing.');
    }

    const userInboxConfig = await this.userInboxConfigService.findById(userInboxConfigId);

    if (!userInboxConfig) {
      throw new NotFoundException(`User Inbox Configuration with ID ${userInboxConfigId} not found.`);
    }
    // Fetch available folders from the IMAP server for this user configuration
    const availableFolders = await this.userInboxConfigService.getMailboxFolders(userInboxConfig);

    // Check if the provided folder is valid
    if (!availableFolders.includes(folder)) {
      throw new BadRequestException(`Invalid folder name: ${folder}.`);
    }


    try {
      const email = await this.userInboxConfigService.fetchSingleEmail(userInboxConfig, uid, folder);
      if (!email) {
        throw new NotFoundException(`Email with ID ${uid} not found in folder ${folder}.`);
      }
      return email;
    } catch (error) {
      throw new InternalServerErrorException(`Failed to fetch email: ${error.message}`);
    }
  }


  @Get('mailbox-folders')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager, Role.TeamMember, Role.JobSeeker, Role.Freelancer)
  @Roles()
  @ApiResponse({ status: 200, description: `Mailbox folders retrieved successfully.` })
  @ApiResponse({ status: 404, description: `User Inbox Configuration not found.` })
  @ApiOperation({ summary: `Retrieve mailbox folders for a user`, description: `This endpoint fetches the list of mailbox folders from the IMAP server for the authenticated user's inbox configuration. This endpoint is accessible to everyone` })
  async getMailboxFolders(@Req() req: any,): Promise<any> {
    const userInboxConfigId = req.user.userInboxConfig._id;

    if (!userInboxConfigId) {
      throw new NotFoundException('User Inbox Configuration is missing.');
    }

    const userInboxConfig = await this.userInboxConfigService.findById(userInboxConfigId);

    if (!userInboxConfig) {
      throw new NotFoundException(`User Inbox Configuration with ID ${userInboxConfigId} not found.`);
    }
    return this.userInboxConfigService.getMailboxFolders(userInboxConfig);
  }


  @Delete('mailbox-folders/:folderName')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager, Role.TeamMember, Role.JobSeeker, Role.Freelancer)
  @Roles()
  @ApiResponse({ status: 200, description: 'Mailbox folder deleted successfully.' })
  @ApiResponse({ status: 404, description: 'User Inbox Configuration not found.' })
  @ApiOperation({ summary: 'Delete a mailbox folder for a user', description: "This endpoint deletes a specified mailbox folder from the IMAP server for the authenticated user's inbox configuration.  This endpoint is accessible to everyone" })
  async removeMailboxFolder(@Req() req: any, @Param('folderName') folderName: string): Promise<void> {
    const userInboxConfigId = req.user.userInboxConfig._id;

    if (!userInboxConfigId) {
      throw new NotFoundException('User Inbox Configuration is missing.');
    }

    const userInboxConfig = await this.userInboxConfigService.findById(userInboxConfigId);

    if (!userInboxConfig) {
      throw new NotFoundException(`User Inbox Configuration with ID ${userInboxConfigId} not found.`);
    }
    return this.userInboxConfigService.removeMailboxFolder(userInboxConfig, folderName);
  }

  @Post('reply')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager, Role.TeamMember, Role.JobSeeker, Role.Freelancer)
  @Roles()
  @ApiOperation({ summary: 'Reply to an email', description: "This endpoint allows the user to reply to an email. It uses the user's inbox configuration to send the reply and supports attachments.  This endpoint is accessible to everyone" })
  async replyToEmail(@Req() req: any,
    @Body() replyEmailDto: ReplyEmailDto
  ): Promise<string> {
    if (!req.user.userInboxConfig._id) {
      throw new Error('userInboxConfigId is required');
    }

    let formattedAttachments = undefined;
    if (replyEmailDto.attachments) {
      formattedAttachments = replyEmailDto.attachments.map((attachment) => {
        if (attachment instanceof FileMetadata) {
          return {
            filename: attachment.originalName,
            contentType: attachment.fileType,
            content: attachment.locationUrl || '', // `content` will be fetched if missing
          };
        } else {
          return attachment;
        }
      });
    }
    replyEmailDto.userInboxConfigId = req.user.userInboxConfig._id
    replyEmailDto.attachments = formattedAttachments

    // Use the service method to send the reply email
    return this.userInboxConfigService.replyToEmailByUid(replyEmailDto);
  }


  @Post('forward')
  @ApiResponse({ status: 200, description: 'Email forwarded successfully.' })
  @ApiResponse({ status: 404, description: 'User Inbox Configuration or Email not found.' })
  @ApiOperation({
    summary: 'Forward an email',
    description: 'This endpoint allows the user to forward an email by its UID from a specified folder to one or more recipients. This endpoint is accessible to everyone.',
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager, Role.TeamMember, Role.JobSeeker, Role.Freelancer)
  @Roles()
  async forwardEmail(@Req() req: any,
    @Body() forwardEmailDto: ForwardEmailDto
  ): Promise<string> {
    if (!req.user.userInboxConfig._id) {
      throw new Error('userInboxConfigId is required');
    }


    // Ensure that attachments are in the correct format if they exist
    let formattedAttachments = undefined;
    if (forwardEmailDto.attachments) {
      formattedAttachments = forwardEmailDto.attachments.map((attachment) => {
        if (attachment instanceof FileMetadata) {
          return {
            filename: attachment.originalName,
            contentType: attachment.fileType,
            content: attachment.locationUrl || '', // `content` will be fetched if missing
          };
        } else {
          return attachment;
        }
      });
    }

    forwardEmailDto.userInboxConfigId = req.user.userInboxConfig._id
    forwardEmailDto.attachments = formattedAttachments


    // Use the service method to forward the email with the formatted attachments
    return this.userInboxConfigService.forwardEmailByUid(forwardEmailDto);
  }


  // POST: Move emails from one folder to another
  @Post('move-emails')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager, Role.TeamMember, Role.JobSeeker, Role.Freelancer)
  @Roles()
  @ApiResponse({ status: 200, description: `Emails successfully moved.` })
  @ApiResponse({ status: 404, description: `User Inbox Configuration or Folder not found.` })
  @ApiResponse({ status: 400, description: `Bad Request.` })
  // @ApiResponse({ status: 403, description: 'Forbidden.' })
  @ApiOperation({
    summary: `Move emails between folders`,
    description: `This endpoint allows users to move specified emails by their UIDs from a source folder to a destination folder within their inbox configuration. This endpoint is accessible to everyone`
  })
  async moveEmailsToFolder(@Req() req: any,

    @Body() moveEmailsDto: MoveEmailsDto
  ): Promise<{ message: string }> {
    if (!req.user.userInboxConfig._id) {
      throw new Error('userInboxConfigId is required');
    }

    if (!moveEmailsDto.sourceFolder || !moveEmailsDto.destinationFolder) {
      throw new BadRequestException('Source folder and destination folder are required.');
    }

    if (!moveEmailsDto.emailUIDs || moveEmailsDto.emailUIDs.length === 0) {
      throw new BadRequestException('Email UIDs are required.');
    }

    moveEmailsDto.userInboxConfigId = req.user.userInboxConfig._id
    const sourceFolder = moveEmailsDto.sourceFolder;
    const destinationFolder = moveEmailsDto.destinationFolder;
    try {
      await this.userInboxConfigService.moveEmailsToFolder(
        moveEmailsDto
      );
      return { message: `Emails successfully moved from "${sourceFolder}" to "${destinationFolder}".` };
    } catch (error) {
      throw new InternalServerErrorException(`Failed to move emails: ${error.message}`);
    }
  }


  @Post('delete-email-to-trash/:uid/:folder')
  @ApiResponse({ status: 200, description: 'Email moved to Trash successfully.' })
  @ApiResponse({ status: 404, description: 'User Inbox Configuration or Email not found.' })
  @ApiOperation({
    summary: 'Move an email to the Trash folder',
    description: "This endpoint allows users to move an email to the Trash folder by providing the email's unique identifier (UID) and the source folder name. This endpoint is accessible to everyone",
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager, Role.TeamMember, Role.JobSeeker, Role.Freelancer)
  @Roles()
  async deleteEmailToTrash(
    @Req() req: any,
    @Param('uid') uid: string,
    @Param('folder') folder: string
  ): Promise<string> {
    const userInboxConfigId = req.user.userInboxConfig._id;

    if (!userInboxConfigId) {
      throw new NotFoundException('User Inbox Configuration is missing.');
    }

    const userInboxConfig = await this.userInboxConfigService.findById(userInboxConfigId);

    if (!userInboxConfig) {
      throw new NotFoundException(`User Inbox Configuration with ID ${userInboxConfigId} not found.`);
    }

    try {
      // Call the service method to move the email to Trash
      return await this.userInboxConfigService.deleteEmailToTrash(userInboxConfig, uid, folder);
    } catch (error) {
      throw new InternalServerErrorException(`Failed to move email to Trash: ${error.message}`);
    }
  }

  @Patch('folder/:folder/mark-seen/:uid')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager, Role.TeamMember, Role.JobSeeker, Role.Freelancer)
  @Roles()
  @ApiOperation({
    summary: 'Mark an email as seen',
    description: "This endpoint allows users to mark an email as 'seen' by providing the email's unique identifier (UID) and the folder name where the email is located. This endpoint is accessible to everyone",
  })
  async markEmailAsSeen(
    @Req() req: any,
    @Param('uid') uid: string,
    @Param('folder') folder: string,
  ): Promise<string> {
    const userInboxConfigId = req.user.userInboxConfig._id;

    if (!userInboxConfigId) {
      throw new NotFoundException('User Inbox Configuration is missing.');
    }

    const userInboxConfig = await this.userInboxConfigService.findById(userInboxConfigId);

    if (!userInboxConfig) {
      throw new NotFoundException(`User Inbox Configuration with ID ${userInboxConfigId} not found.`);
    }
    try {
      // Call the service method to move the email to Trash
      return await this.userInboxConfigService.markEmailAsSeen(userInboxConfig, uid, folder);
    } catch (error) {
      throw new InternalServerErrorException(`Failed to update the email : ${error.message}`);
    }

  }

  @Patch('folder/:folder/mark-unseen/:uid')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager, Role.TeamMember, Role.JobSeeker, Role.Freelancer)
  @Roles()
  @ApiOperation({
    summary: 'Mark an email as unseen',
    description: "This endpoint allows users to mark an email as 'unseen' by providing the email's unique identifier (UID) and the folder name where the email is located. This endpoint is accessible to everyone",
  })
  async markEmailAsUnseen(
    @Req() req: any,
    @Param('uid') uid: string,
    @Param('folder') folder: string,
  ): Promise<string> {
    const userInboxConfigId = req.user.userInboxConfig._id;

    if (!userInboxConfigId) {
      throw new NotFoundException('User Inbox Configuration is missing.');
    }

    const userInboxConfig = await this.userInboxConfigService.findById(userInboxConfigId);

    if (!userInboxConfig) {
      throw new NotFoundException(`User Inbox Configuration with ID ${userInboxConfigId} not found.`);
    }
    try {
      // Call the service method to move the email to Trash
      return await this.userInboxConfigService.markEmailAsUnseen(userInboxConfig, uid, folder);
    } catch (error) {
      throw new InternalServerErrorException(`Failed to update the email : ${error.message}`);
    }

  }

  @Delete('permanently-delete-email/:uid')
  @ApiResponse({ status: 200, description: 'Email permanently deleted successfully.' })
  @ApiResponse({ status: 404, description: 'User Inbox Configuration or Email not found.' })
  @ApiOperation({
    summary: 'Permanently delete an email from Trash',
    description: "This endpoint allows users to permanently delete an email from the Trash folder by providing the email's unique identifier (UID). This endpoint is accessible to everyone",
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager, Role.TeamMember, Role.JobSeeker, Role.Freelancer)
  @Roles()
  async permanentlyDeleteEmail(
    @Req() req: any,
    @Param('uid') uid: string
  ): Promise<string> {
    const userInboxConfigId = req.user.userInboxConfig._id;

    if (!userInboxConfigId) {
      throw new NotFoundException('User Inbox Configuration is missing.');
    }

    const userInboxConfig = await this.userInboxConfigService.findById(userInboxConfigId);

    if (!userInboxConfig) {
      throw new NotFoundException(`User Inbox Configuration with ID ${userInboxConfigId} not found.`);
    }
    try {
      // Call the service method to permanently delete the email (remove 'Trash')
      return await this.userInboxConfigService.permanentlyDeleteEmail(userInboxConfig, uid);
    } catch (error) {
      throw new InternalServerErrorException(`Failed to permanently delete email: ${error.message}`);
    }
  }

  @Get('search-emails/:folder')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager, Role.TeamMember, Role.JobSeeker, Role.Freelancer)
  @Roles()
  @ApiOperation({
    summary: 'Search Emails in Folder',
    description: 'This endpoint allows users to search for emails within a specific folder using a search term. This endpoint is accessible to everyone'
  })
  @ApiResponse({ status: 200, description: 'Emails retrieved successfully based on search term.' })
  @ApiResponse({ status: 404, description: 'User Inbox Configuration not found.' })
  async searchEmail(
    @Req() req: any,
    @Param('folder') folder: string,
    @Query('searchTerm') searchTerm: string // Using query parameter for search term
  ): Promise<any> {
    const userInboxConfigId = req.user.userInboxConfig._id;

    if (!userInboxConfigId) {
      throw new NotFoundException('User Inbox Configuration is missing.');
    }

    const userInboxConfig = await this.userInboxConfigService.findById(userInboxConfigId);

    if (!userInboxConfig) {
      throw new NotFoundException(`User Inbox Configuration with ID ${userInboxConfigId} not found.`);
    }
    try {
      // Call the service method with the search term directly
      const result = await this.userInboxConfigService.searchEmails(userInboxConfig, folder, searchTerm);
      return result;
    } catch (error) {
      throw new InternalServerErrorException(`Failed to search emails: ${error.message}`);
    }
  }

  @Post('search-emails/:folder')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager, Role.TeamMember, Role.JobSeeker, Role.Freelancer)
  @Roles()
  @ApiOperation({
    summary: 'Search Emails in Folder',
    description: 'This endpoint allows users to search for emails within a specific folder using a search term. This endpoint is accessible to everyone'
  })
  @ApiResponse({ status: 200, description: 'Emails retrieved successfully based on search term.' })
  @ApiResponse({ status: 404, description: 'User Inbox Configuration not found.' })
  async searchEmails(
    @Req() req: any,
    @Param('folder') folder: string,
    @Body() searchEmailDto: SearchEmailDto
  ): Promise<any> {
    const userInboxConfigId = req.user.userInboxConfig._id;

    if (!userInboxConfigId) {
      throw new NotFoundException('User Inbox Configuration is missing.');
    }

    const userInboxConfig = await this.userInboxConfigService.findById(userInboxConfigId);

    if (!userInboxConfig) {
      throw new NotFoundException(`User Inbox Configuration with ID ${userInboxConfigId} not found.`);
    }

    try {
      // Pass the search term to the service method instead of complex criteria
      const result = await this.userInboxConfigService.searchEmails(userInboxConfig, folder, searchEmailDto.searchTerm);
      return result;
    } catch (error) {
      throw new InternalServerErrorException(`Failed to search emails: ${error.message}`);
    }
  }




}
