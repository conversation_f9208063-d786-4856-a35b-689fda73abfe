import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Types } from 'mongoose';
import { Org } from 'src/org/schemas/org.schema';
import { EmailTemplateEvent } from 'src/shared/constants';
import { BasicUser } from 'src/user/schemas/basic-user.schema';

export type EmailTemplateDocument = HydratedDocument<EmailTemplate>;

@Schema({ timestamps: true })
export class EmailTemplate {

  @Prop({ required: true, trim: true })
  templateName: string;

  // The HTML content of the email
  @Prop({ required: false })
  templateMJMLContent?: string;

  @Prop({ required: false })
  templateHTMLContent?: string;

  @Prop({ required: false, type: Object })
  templateJsonContent?: object;

  @Prop({
    type: String,
    required: false,
    trim: true,
    enum: Object.values(EmailTemplateEvent),
  })
  eventName?: string;

  // Organization associated with the template
  @Prop({
    required: false,
    type: Types.ObjectId, ref: 'Org'
  })
  org?: Org;

  @Prop({
    required: true,
    type: Types.ObjectId, ref: 'BasicUser'
  })
  createdBy: BasicUser;

  @Prop({ default: false })
  isDeleted?: boolean;

  @Prop({ default: false })
  isDefault?: boolean;

  @Prop({ default: false })
  isGlobal?: boolean;

  @Prop({ default: false })
  canDelete?: boolean;

}

export const EmailTemplateSchema = SchemaFactory.createForClass(EmailTemplate);
