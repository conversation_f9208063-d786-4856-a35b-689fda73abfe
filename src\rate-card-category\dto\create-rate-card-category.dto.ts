import { ApiHideProperty, ApiProperty } from "@nestjs/swagger";
import {
    IsMongoId,
    IsNotEmpty,
    IsOptional,
    IsString,
    MaxLength,
} from "class-validator";
import { Transform, TransformFnParams } from "class-transformer";
import { sanitizeWithStyle } from "src/utils/sanitize-with-style";

export class CreateRateCardCategoryDto {

    @ApiProperty({
        type: String,
        required: true,
        description: 'Name of the rate card category',
    })
    @IsNotEmpty()
    @IsString()
    // @MaxLength(300)
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    label: string;


    @ApiProperty({
        type: String,
        required: false,
        description: 'Description of the rate card category',
    })
    @IsOptional()
    @IsString()
    // @MaxLength(500)
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    description?: string

    // @ApiProperty({
    //     type: String,
    //     required: false,
    //     description: 'ID of the parent category (self-referencing)',
    // })
    // @IsOptional()
    // @IsMongoId()
    // @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    // parentCategory?: string;

    @ApiHideProperty()
    @IsOptional()
    org?: string;

    @ApiHideProperty()
    @IsOptional()
    createdBy?: string;

    @ApiProperty({
        type: String,
        required: true,
        description: 'Client ID',
    })
    @IsString()
    @IsNotEmpty()
    @IsMongoId()
    client: string;

}
