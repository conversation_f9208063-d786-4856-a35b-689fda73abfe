import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Types } from 'mongoose';
import { Org } from 'src/org/schemas/org.schema';
import { BasicUser } from 'src/user/schemas/basic-user.schema';

export type UserInboxConfigDocument = HydratedDocument<UserInboxConfig>;

@Schema({
    timestamps: true,
})
export class UserInboxConfig {

    @Prop({
        type: Types.ObjectId,
        ref: 'Org',
        required: false,
    })
    org?: Org;

    @Prop({
        type: String,
        required: true,
        trim: true,
    })
    fromEmail: string;

    @Prop({
        type: String,
        required: true,
        trim: true,
    })
    fromName: string;

    @Prop({
        type: String,
        required: false,
        trim: true,
    })
    imapHost?: string;

    @Prop({
        type: Number,
        required: false,
    })
    imapPort?: number;

    @Prop({
        type: String,
        required: false,
        trim: true,
    })
    smtpHost?: string;

    @Prop({
        type: Number,
        required: false,
    })
    smtpPort?: number;

    @Prop({
        type: String,
        required: true,
        trim: true,
    })
    userName: string;

    @Prop({
        type: String,
        required: true,
        trim: true,
    })
    password: string;

    @Prop({
        type: Boolean,
        required: false,
        default: false,
    })
    isEnableSSL?: boolean;

    @Prop({
        type: Boolean,
        default: false,
    })
    isOrgDefault?: boolean;

    // @Prop({
    //     type: String,
    //     required: false,
    //     trim: true,
    //     enum: ['Inbox', 'Sent', 'Deleted', 'Drafts'], // Standard folders
    // })
    // folder?: string;

    // @Prop({
    //     type: String,
    //     required: false,
    //     trim: true,
    // })
    // subject?: string;

    // @Prop({
    //     type: String,
    //     trim: true,
    //     required: false,
    // })
    // body?: string;

    // @Prop({
    //     type: String,
    //     required: false,
    //     trim: true,
    //     default: '<p>Best regards,<br>Your Name</p>', // Default value
    // })
    // signature?: string; // New field for the email signature


    @Prop({
        type: Date,
    })
    receivedAt: Date;

    @Prop({
        type: Date,
    })
    sentAt: Date;

    @Prop({
        type: Types.ObjectId,
        required: false,
        ref: 'BasicUser'
    })
    createdBy?: BasicUser;
}

export const UserInboxConfigSchema = SchemaFactory.createForClass(UserInboxConfig);
