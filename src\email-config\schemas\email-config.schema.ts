import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose, { HydratedDocument, Types } from 'mongoose';
import { FileMetadata } from 'src/file-upload/schemas/file-metadata.schema';
import { UserInboxConfig } from 'src/user-inbox-config/schemas/user-inbox-config.schema';

export type EmailDocument = HydratedDocument<EmailConfig>;

@Schema({
    timestamps: true,
})
export class EmailConfig {

    @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'UserInboxConfig', required: false })
    userInboxConfigId: string;

    // @Prop({
    //     type: String,
    //     required: true,
    //     trim: true,
    // })
    // fromEmail: string;

    // @Prop({
    //     type: String,
    //     required: true,
    //     trim: true,
    // })
    // fromName: string;

    @Prop({
        type: [String],
        required: true,
    })
    to: string[];  // Array of recipient emails

    @Prop({
        type: [String],
    })
    cc?: string[];  // CC (optional)

    @Prop({
        type: [String],
    })
    bcc?: string[];  // BCC (optional)

    @Prop({
        type: String,
        required: true,
    })
    subject: string;

    @Prop({
        type: String,
        required: true,
    })
    body: string;  // Email body


    @Prop({
        required: false,
        type: Types.ObjectId,
        default: [],
        ref:'FileMetadata'
    })
    attachments?: FileMetadata[];

    @Prop({
        type: Date,
    })
    sentAt: Date;  // Sent time

    @Prop({
        type: Date,
    })
    receivedAt: Date;  // Received time

    @Prop({
        required: false,
        type: Types.ObjectId, ref: 'EmailTemplate'
    })
    templateId?: Types.ObjectId;

}

export const EmailConfigSchema = SchemaFactory.createForClass(EmailConfig);


//TODO: indexing