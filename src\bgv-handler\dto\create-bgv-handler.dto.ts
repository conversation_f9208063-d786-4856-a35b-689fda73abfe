import { ApiHideProperty, ApiProperty } from '@nestjs/swagger';
import { Transform, TransformFnParams } from 'class-transformer';
import { IsArray, IsEmail, IsNotEmpty, IsOptional, IsPhoneNumber, IsString } from 'class-validator';
import { sanitizeWithStyle } from "src/utils/sanitize-with-style";

export class CreateBgvHandlerDto {

    @ApiProperty({
        type: String,
        required: true,
        description: 'Title of the BGV handler',
    })
    @IsString()
    @IsNotEmpty()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    title: string;

    @ApiProperty({
        type: String,
        required: true,
        description: 'Email address of the BGV handler',
        format: 'email',
        default: '<EMAIL>',
    })
    @IsNotEmpty()
    @IsEmail()
    @IsString()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value).toLowerCase())
    email: string;

    @ApiProperty({
        type: String,
        required: false,
        description: 'BGV handler Contact number'
    })
    @IsString()
    @IsOptional()
    // @IsPhoneNumber()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    contactNumber?: string;

    @ApiHideProperty()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    createdBy?: string;

    @ApiHideProperty()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    org?: string;

    @ApiProperty({ type: Boolean, required: false, default: false, description: 'Default BGV Handler for Company Level' })
    @IsOptional()
    isDefault?: boolean;

    @ApiProperty({ example: ['passport_photo', 'qualification_certificates'] })
    @IsArray()
    @IsOptional()
    documentKeys?: string[];

    @ApiProperty({
        type: [String],
        required: false,
        description: 'Array of department IDs to assign',
    })
    @IsArray()
    @IsOptional()
    @Transform(({ value }) => {
        if (Array.isArray(value)) {
            return value.map((v) => sanitizeWithStyle(v));
        }
        return [];
    })
    assignToDepartment?: string[];

    @ApiProperty({
        type: [String],
        required: false,
        description: 'Array of department IDs to assign',
    })
    @IsArray()
    @IsOptional()
    @Transform(({ value }) => {
        if (Array.isArray(value)) {
            return value.map((v) => sanitizeWithStyle(v));
        }
        return [];
    })
    assignToOrg?: string[];

}
