
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Types } from 'mongoose';
import { Account } from 'src/account/schemas/account.schema';
import { Client } from 'src/client/schemas/client.schema';
import { Contact } from 'src/contact/schemas/contact.schema';
import { Org } from 'src/org/schemas/org.schema';
import { BasicUser } from 'src/user/schemas/basic-user.schema';

export type NoteDocument = HydratedDocument<Note>;


@Schema({ timestamps: true })
export class Note {

    @Prop({ type: Types.ObjectId, ref: 'Org' })
    org?: Org;

    @Prop({ type: Types.ObjectId, ref: 'Contact' })
    contact?: Contact;

    @Prop({
        type: String,
        required: false,
        trim: true,
    })
    title?: string;

    @Prop({
        type: String,
        required: false,
        trim: true,
    })
    summary?: string;


    @Prop({
        type: String,
        required: false,
        trim: true,
    })
    content?: string;

    @Prop({
        type: Boolean,
        required: false,
        trim: true,
        default: false,
    })
    isPrivate?: boolean;


    @Prop({
        required: true,
        type: Types.ObjectId, ref: 'BasicUser'
    })
    createdBy: BasicUser;

    @Prop({
        type: Boolean,
        default: false,
        required: false,
    })
    isDeleted?: boolean;

    @Prop({
        type: [String],
        default: [],
        required: false,
    })
    attachments?: string[];

    // @Prop({
    //     required: false,
    //     type: Types.ObjectId, ref: 'Org'
    // })
    // vendor?: Org;
}

export const NoteSchema = SchemaFactory.createForClass(Note);