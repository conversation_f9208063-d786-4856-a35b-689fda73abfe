// import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
// import { Observable } from 'rxjs';

// @Injectable()
// export class CustomRolesGuard implements CanActivate {
//   canActivate(
//     context: ExecutionContext,
//   ): boolean | Promise<boolean> | Observable<boolean> {
//     return true;
//   }
// }


import {
  Injectable,
  CanActivate,
  ExecutionContext,
  ForbiddenException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Observable } from 'rxjs';
import { CUSTOM_ROLES_KEY } from 'src/auth/decorators/custom-roles.decorator';

@Injectable()
export class CustomRolesGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(
    context: ExecutionContext,
  ): boolean | Promise<boolean> | Observable<boolean> {
    const requiredRoles = this.reflector.get<string[]>(CUSTOM_ROLES_KEY, context.getHandler());
    
    if (!requiredRoles) {
      return true; // If no roles specified, allow access
    }

    const request = context.switchToHttp().getRequest();
    const user = request.user;

    if (!user) {
      throw new ForbiddenException('User not found');
    }

    return this.validateRoles(user, requiredRoles);
  }

  private validateRoles(user: any, requiredRoles: string[]): boolean {
    // Check if user has at least one of the required roles
    const hasRole = requiredRoles.some(role => user.roles.includes(role));
    if (!hasRole) {
      throw new ForbiddenException('You do not have the necessary role');
    }

    // Optionally, check if the user belongs to the correct org or business unit
    if (!this.isUserInAllowedOrgOrBu(user)) {
      throw new ForbiddenException('Access to this resource is not allowed');
    }

    return true;
  }

  private isUserInAllowedOrgOrBu(user: any): boolean {
    // Logic to check if the user is in the allowed organization or business unit
    // For example, check if the user is part of the org and its departments (customers/vendors)
    const allowedOrgs = [user.org, ...user.org.customers, ...user.org.vendors];

    // Assuming the request resource is tied to an organization
    if (!allowedOrgs.includes(user.org)) {
      return false; // User is not in an allowed org
    }

    // You can add more logic here to verify specific business unit access if necessary
    return true;
  }

  /**
   * Function to differentiate between a vendor user and a customer user
   * This checks if the user is an employee in a vendor or customer org.
   */
  private async isUserVendorOrCustomer(user: any): Promise<string> {
    // Check if the user belongs to a vendor organization
    const userOrg = await user.populate('org').execPopulate();

    // Check if user is part of a vendor organization
    for (let vendorOrg of userOrg.org.vendors) {
      const vendor = await vendorOrg.populate('users').execPopulate();

      //Assuming, vendor employees are vendor.users
      if (vendor.users.some((emp:any) => emp._id.toString() === user._id.toString())) {
        return 'vendor';
      }
    }

    // Check if user is part of a customer organization
    for (let customerOrg of userOrg.org.customers) {
      //Assuming, vendor employees are vendor.users

      const customer = await customerOrg.populate('users').execPopulate();
      if (customer.users.some((emp:any) => emp._id.toString() === user._id.toString())) {
        return 'customer';
      }
    }

    // If the user is neither in a vendor nor customer organization
    return 'none';  // The user is not associated with any vendor or customer org
  }

  /**
   * Helper function to determine if a user is a vendor or customer based on the org relationships.
   */
  private async isVendorUser(user: any): Promise<boolean> {
    const result = await this.isUserVendorOrCustomer(user);
    return result === 'vendor';
  }

  private async isCustomerUser(user: any): Promise<boolean> {
    const result = await this.isUserVendorOrCustomer(user);
    return result === 'customer';
  }


  /**
   * Function to check if the user belongs to the correct organization (internal/external)
   * based on orgId from route parameters or query params.
   */
  private isInternalUser(user: any, request: any): boolean {
    const orgIdFromParams = request.params.orgId || request.query.orgId;  // Get orgId from route params or query

    if (!orgIdFromParams) {
      throw new ForbiddenException('Organization ID is missing from request');
    }

    // Check if the user's org matches the orgId from the request
    return user.org.toString() === orgIdFromParams.toString();
  }

    /**
   * Validates if the user has the necessary orgId and buId
   * from the request parameters or query.
   */
    private isOrgAndBuValid(user: any, request: any): void {
      const orgIdFromParams = request.params.orgId || request.query.orgId;  // Get orgId from route params or query
      const buIdFromParams = request.params.buId || request.query.buId;    // Get buId from route params or query
  
      if (!orgIdFromParams) {
        throw new ForbiddenException('Organization ID is missing from request');
      }
  
      if (!buIdFromParams) {
        throw new ForbiddenException('Business Unit ID is missing from request');
      }
  
      // Validate if user.org matches orgId from request
      if (user.org.toString() !== orgIdFromParams.toString()) {
        throw new ForbiddenException('User does not have access to the requested organization');
      }
  
      // Validate if user.businessUnit matches buId from request
      if (user.businessUnit.toString() !== buIdFromParams.toString()) {
        throw new ForbiddenException('User does not have access to the requested business unit');
      }
    }
  
}



