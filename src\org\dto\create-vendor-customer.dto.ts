import {  ApiProperty } from "@nestjs/swagger";
import {  IsNotEmpty, IsString } from 'class-validator';
import { Transform, TransformFnParams, Type } from 'class-transformer';
import sanitizeWithStyle from 'sanitize-html'


// Read about operators of swagger here - https://docs.nestjs.com/openapi/decorators
export class CreateRemoveVendorCustomerDto {

  @ApiProperty({
    type: String,
    required: true,
    description: 'Enter id of customer or vendor',
  })
  @IsNotEmpty()
  @IsString()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value).toLowerCase())
  id: string;

}
