import { ApiHideProperty, ApiProperty } from "@nestjs/swagger";
import { Transform, TransformFnParams } from "class-transformer";
import { IsMongoId, IsNotEmpty, IsOptional, IsString } from "class-validator";
import { sanitizeWithStyle } from "src/utils/sanitize-with-style";

export class CreateRegionDto {

  @ApiProperty({
    type: String,
    required: true,
    description: 'Id of the country',
  })
  @IsNotEmpty()
  @IsMongoId()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  country: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Id of the state',
  })
  @IsNotEmpty()
  @IsMongoId()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  state: string;

  @ApiHideProperty()
  @IsOptional()
  @IsString()
  createdBy?: string;


}
