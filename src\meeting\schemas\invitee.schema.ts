import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";
import {  HydratedDocument, Types } from "mongoose";
import { BasicUser } from "src/user/schemas/basic-user.schema";

export type InviteeDocument = HydratedDocument<Invitee>;

@Schema({ timestamps: true })
export class Invitee {
    @Prop({
        type: String,
        required: true,
        trim: true
    })
    emailAddress: string;

    @Prop({ required: false, default: false })
    isInternalUser?: boolean;

    @Prop({ type: Types.ObjectId, ref: 'BasicUser', required: false })
    user?: BasicUser;

    @Prop({ required: false, default: false })
    isInviteAccepted?: boolean;

    @Prop({ type: Types.ObjectId, ref: 'BasicUser', required: false })
    admittedBy?: BasicUser;

    @Prop({ type: Types.ObjectId, ref: 'BasicUser', required: false })
    deniedBy?: BasicUser;

    @Prop({ type: Date, required: false, default: null })
    joinedAt?: Date;

    @Prop({ type: Date, required: false, default: null })
    leftAt?: Date;
}

export const InviteeSchema = SchemaFactory.createForClass(Invitee);

InviteeSchema.index({ emailAddress: 1 });
InviteeSchema.index({ user: 1 });