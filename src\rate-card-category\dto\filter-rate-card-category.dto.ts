import { Transform } from 'class-transformer';
import { IsOptional, IsString, IsMongoId } from 'class-validator';

export class FilterRateCardCategoryDto {

    @IsOptional()
    @IsString()
    @Transform(({ value }) => (value === undefined || value === '0' ? '' : value))
    label?: string;

    @IsOptional()
    @IsMongoId()
    @Transform(({ value }) => (value === undefined || value === '0' ? '' : value))
    org?: string;


    // @IsOptional()
    // @IsString()
    // @Transform(({ value }) => (value === undefined || value === '0' ? '' : value))
    // parentCategory?: string;

    @IsOptional()
    @Transform(({ value }) => {
        return value > 0 ? value : 1;
    })
    page?: number = 1;


    @IsOptional()
    @Transform(({ value }) => {
        return value > 0 ? value : 10;
    })
    limit?: number = 10;

    @IsOptional()
    @IsMongoId()
    @Transform(({ value }) => (value === undefined || value === '0' ? '' : value))
    client?: string;
}
