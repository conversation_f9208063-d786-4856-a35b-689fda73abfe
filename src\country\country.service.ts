import { BadRequestException, HttpException, HttpStatus, Injectable, InternalServerErrorException, Logger, NotFoundException } from '@nestjs/common';
import { CreateCountryDto } from './dto/create-country.dto';
import { UpdateCountryDto } from './dto/update-country.dto';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Country, CountryDocument } from './schemas/country.schema';
import * as fs from 'fs/promises';
import * as path from 'path';

const countriesData = require('../public/data/countries/all-countries.json')

@Injectable()
export class CountryService {


  private readonly logger = new Logger(CountryService.name);

  constructor(@InjectModel(Country.name) private countryModel: Model<Country>,
  ) { }


  async seedCountriesFromFile() {
    try {
      const filePath = path.resolve(__dirname, '../public/data/countries/all-countries.json');
      const rawData = await fs.readFile(filePath, 'utf8');
      const countriesData = JSON.parse(rawData);

      // Prepare countries to insert
      const countriesToInsert = countriesData.map((country: any) => ({
        countryName: country.name,
        countryPhoneCode: country.phone_code,
        currencyCode: country.currency,
        isDeleted: true,
      }));

      // Mongoose bulkWrite for better performance
      const result = await this.countryModel.bulkWrite(
        countriesToInsert.map((doc: any) => ({
          insertOne: { document: doc },
        })),
      );

      return `Successfully seeded ${result.insertedCount} countries.`;
    } catch (error) {
      console.error('Error seeding countries:', error);
      throw error; // Handle or log the error as needed
    }
  }


  async create(createCountryDto: CreateCountryDto) {
    const { countryName, countryId } = createCountryDto;
    try {
      const existingCountry = await this.countryModel.findOne({
        $or: [
          { countryName: countryName.trim() },
          { countryId: countryId }
        ]
      });

      if (existingCountry) {
        this.logger.log(existingCountry)
        throw new BadRequestException("Country name or country ID already exists.");
      }
      return await new this.countryModel(createCountryDto).save();
    } catch (error) {
      throw new InternalServerErrorException(`Error while creating country: ${error?.message}`);
    }
  }

  async getAllSoftDeletedCountries(): Promise<CountryDocument[]> {
    // this.logger.debug('Initiating findAll method');
    const result = this.countryModel.find({ isDeleted: true }).sort({ isDeleted: 1, countryName: 1 }).exec();
    return result;
  }

  // if admin wants to see list of records include deleting records we need one more function
  async getAllCountriesWithSoftDeleted(): Promise<CountryDocument[]> {
    const result = this.countryModel.find()
      .sort({ isDeleted: 1, countryName: 1 })
      .exec();
    return result;
  }

  async getAllCountries(): Promise<CountryDocument[]> {
    const result = this.countryModel.find({ isDeleted: false }).sort({ countryName: 1 }).exec();
    return result;
  }

  async findOne(countryId: Types.ObjectId): Promise<CountryDocument> {
    return this.findById(countryId);
  }

  async findById(countryId: Types.ObjectId) {
    try {
      const country = await this.countryModel.findById(countryId);
      if (!country) {
        throw new NotFoundException(`Country not found with ID ${countryId}`);
      }
      return country;
    } catch (error) {
      // this.logger.error(error);
      this.logger.error(`An error occurred while fetching a country by ID ${countryId}. ${error?.message}`);
      // throw new UnprocessableEntityException(`May be invalid id format ${id}`);
      throw error;
    }
  }

  async searchByName(name: string) {
    try {
      if (!name) {
        throw new HttpException('Name parameter is required', HttpStatus.BAD_REQUEST);
      }
      const regex = new RegExp(name, 'i'); // 'i' for case-insensitive
      return await this.countryModel.find({ countryName: { $regex: regex } }).exec();
    } catch (error) {
      this.logger.error(`Error while searching for countries: ${error.message}`, error.stack);
      throw new InternalServerErrorException(`Error while searching for countries: ${error?.message}`);
    }
  }

  async update(countryId: Types.ObjectId, updateCountryDto: UpdateCountryDto) {
    try {
      await this.findById(countryId);
      const existingCountry = await this.countryModel.findOne({
        $or: [
          { countryName: updateCountryDto?.countryName?.trim() },
          { countryId: updateCountryDto?.countryId }
        ]
      });

      if (existingCountry) {
        this.logger.log(existingCountry)
        throw new BadRequestException("Country name or country ID already exists.");
      }
      const updatedCountry = await this.countryModel.findByIdAndUpdate(countryId, updateCountryDto, { new: true, runValidators: true });
      return updatedCountry;
    } catch (error) {
      this.logger.error(`An error occurred while updating country by ID ${countryId}. ${error?.message}`);
      throw error;
    }
  }

  async softDelete(countryId: Types.ObjectId) {
    try {
      const country = await this.findById(countryId);
      country.isDeleted = true;
      await country.save();
      return country;
    }
    catch (error) {
      this.logger.error(error);
      this.logger.error(`An error occurred while soft deleting country by ID ${countryId}. ${error?.message}`);
      throw error;
    }
  }

  async restoreSoftDeletedCountry(countryId: Types.ObjectId) {
    try {
      const country = await this.countryModel.findById(countryId);
      if (!country) {
        throw new NotFoundException(`The country with ID: "${countryId}" doesn't exist.`);
      }
      if (!country.isDeleted) {
        throw new BadRequestException(`The country with ID: "${countryId}" is not soft deleted.`);
      }
      country.isDeleted = false
      await country.save();
      return country
    }
    catch (error) {
      this.logger.error(`An error occurred while restoring Country by ID ${countryId}. ${error?.message}`);
      throw error;
    }
  }

  async restore() {
    try {
      const softDeletedCountries = await this.getAllSoftDeletedCountries();
      if (!softDeletedCountries.length) {
        this.logger.log('No soft-deleted countries found to restore.');
        return [];
      }
      for (const country of softDeletedCountries) {
        country.isDeleted = false;
        await country.save();
      }
      // this.logger.log('All soft-deleted countries have been restored.');
      return this.getAllCountries();
    } catch (error) {
      this.logger.error(`An error occurred while restoring soft-deleted countries. ${error.message}`);
      throw new InternalServerErrorException(`An error occurred while restoring soft-deleted countries. ${error.message}`);
    }
  }

}
