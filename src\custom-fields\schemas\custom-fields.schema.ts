import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";
import { HydratedDocument, Types } from "mongoose";
import { CandidateFields } from "src/shared/constants";

export type CustomFieldDocument = HydratedDocument<CustomField>;
@Schema({ timestamps: true })
export class CustomField  {

    @Prop({ required: true, enum: Object.values(CandidateFields), type: String })
    name: string;

    @Prop({ default: false, type: Boolean, required: false })
    isDefault?: boolean;  // Whether the field is a default field

    @Prop({ default: true, type: Boolean, required: true })
    isVisible: boolean;  // Whether the field is visible in UI

    @Prop({
        required: false,
        type: Types.ObjectId, ref: 'Org'
    })
    org?: Types.ObjectId;
}

export const CustomFieldSchema = SchemaFactory.createForClass(CustomField);
