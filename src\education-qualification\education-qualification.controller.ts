import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Req } from '@nestjs/common';
import { EducationQualificationService } from './education-qualification.service';
import { CreateEducationQualificationDto } from './dto/create-education-qualification.dto';
import { UpdateEducationQualificationDto } from './dto/update-education-qualification.dto';
import { ApiBearerAuth, ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { AuthJwtGuard } from 'src/auth/guards/auth-jwt/auth-jwt.guard';
import { RolesGuard } from 'src/auth/guards/roles/roles.guard';
import { Roles } from 'src/auth/decorators/roles.decorator';
import { Role } from 'src/auth/enums/role.enum';
import { validateObjectId } from 'src/utils/validation.utils';

@Controller('')
@ApiTags('Education-Qualifications')
export class EducationQualificationController {
  constructor(private readonly educationQualificationService: EducationQualificationService) { }


  @Post()
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.AccountManager, Role.TeamLead, Role.TeamMember, Role.Recruiter, Role.Vendor, Role.JobSeeker, Role.Freelancer)
  @Roles()
  @ApiResponse({ status: 201, description: 'Education Qualification is saved.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Data. ' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role "BUHead", "TeamLead", "DeliveryManager" and "Recruiter" can only access this end point.' })
  @ApiOperation({ summary: 'Create a education qualification', description: `This endpoint for creating a education qualification. This is only accessible for "BUHead", "TeamLead", "DeliveryManager" and "Recruiter".` })
  create(@Req() req: any, @Body() createEducationQualificationDto: CreateEducationQualificationDto) {
    createEducationQualificationDto.createdBy = req.user._id
    return this.educationQualificationService.create(createEducationQualificationDto);
  }

  @Get('all')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.AccountManager, Role.TeamLead, Role.TeamMember, Role.Recruiter, Role.Vendor, Role.JobSeeker, Role.Freelancer)
  @Roles()
  @ApiOperation({ summary: 'Retrieve all education qualifications', description: `This endpoint returns a list of all education qualifications. This is accessible for everyone. ` })
  @ApiResponse({ status: 200, description: 'education qualification retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  // @ApiResponse({ status: 403, description: 'Forbidden, user with role admin and salesrep  can only use this end point.' })
  findAll() {
    return this.educationQualificationService.findAll();
  }


  @Get(':educationQualificationId')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.AccountManager, Role.TeamLead, Role.TeamMember, Role.Recruiter, Role.Vendor, Role.JobSeeker, Role.Freelancer)
  @Roles()
  @ApiOperation({ summary: 'Retrieve an education qualification by Id', description: 'This endpoint returns an education qualification by its Id.This is accessible for everyone' })
  @ApiResponse({ status: 200, description: 'Education qualification is retrieved.' })
  @ApiResponse({ status: 404, description: 'Education qualification not found.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  // @ApiResponse({ status: 403, description: 'Forbidden, user with role admin can only use this end point.' })
  @ApiParam({ name: 'educationQualificationId', description: 'id of the educationQualification.' })
  findOne(@Param('educationQualificationId',) educationQualificationId: string) {
    const objId = validateObjectId(educationQualificationId);
    return this.educationQualificationService.findById(objId);
  }


  @Patch(':educationQualificationId')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.AccountManager, Role.TeamLead, Role.TeamMember, Role.Recruiter, Role.Vendor, Role.JobSeeker, Role.Freelancer)
  @Roles()
  @ApiOperation({ summary: 'Update an education qualification by Id', description: `This endpoint updates an education qualification by Id. This is only accessible for "BUHead", "TeamLead", "DeliveryManager" and "Recruiter".` })
  @ApiResponse({ status: 200, description: 'Education qualification is saved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role "BUHead", "TeamLead", "DeliveryManager" and "Recruiter" can only use this end point.' })
  @ApiParam({ name: 'educationQualificationId', description: 'id of the educationQualification.' })
  update(@Param('educationQualificationId',) educationQualificationId: string, @Body() updateeducationQualificationDto: UpdateEducationQualificationDto) {
    const objId = validateObjectId(educationQualificationId);
    return this.educationQualificationService.update(objId, updateeducationQualificationDto);
  }
  @Delete(':educationQualificationId/hard-delete')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.AccountManager, Role.TeamLead, Role.TeamMember, Role.Recruiter, Role.Vendor, Role.JobSeeker, Role.Freelancer)
  @Roles()
  @ApiOperation({ summary: 'Delete an education qualification by Id', description: 'This endpoint deletes an education qualification by Id. This is only accessible for "BUHead", "TeamLead", "DeliveryManager" and "Recruiter".' })
  @ApiResponse({ status: 200, description: 'Education qualification is hard deleted.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role "BUHead", "TeamLead", "DeliveryManager" and "Recruiter" can only use this end point.' })
  @ApiParam({ name: 'educationQualificationId', description: 'ID of the education qualification' })
  remove(@Param('educationQualificationId') educationQualificationId: string) {
    const objId = validateObjectId(educationQualificationId);
    return this.educationQualificationService.remove(objId);
  }

  @Delete('delete-all')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.AccountManager, Role.TeamLead, Role.TeamMember, Role.Recruiter, Role.Vendor, Role.JobSeeker, Role.Freelancer)
  @Roles()
  @ApiOperation({ summary: 'Remove  all education qualifications', description: `This endpoint deletes all education qualifications. This is accessible only for "BUHead", "TeamLead", "DeliveryManager" and "Recruiter".` })
  @ApiResponse({ status: 200, description: 'All education qualifications deleted.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  @ApiResponse({ status: 403, description: 'Forbidden, User with role "BUHead", "TeamLead", "DeliveryManager" and "Recruiter" can only use this end point.' })
  deleteAll() {
    return this.educationQualificationService.deleteAll();
  }

}
