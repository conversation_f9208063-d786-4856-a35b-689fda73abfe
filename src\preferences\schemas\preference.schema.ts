import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types, HydratedDocument } from 'mongoose';
import { Org } from 'src/org/schemas/org.schema';

export type PreferenceDocument = HydratedDocument<Preference>;

@Schema({ timestamps: true })  // ✅ Enable timestamps for each subdocument
class Skill {
  @Prop({ required: true })
  skill: string;

  @Prop({ default: () => new Date() }) // ✅ Manually ensure timestamp on creation
  createdAt?: Date;

  @Prop({ default: () => new Date() }) // ✅ Ensure `updatedAt` field
  updatedAt?: Date;
}

@Schema({ timestamps: true })
class Location {
  @Prop({ required: true })
  location: string;

  @Prop({ default: () => new Date() })
  createdAt?: Date;

  @Prop({ default: () => new Date() })
  updatedAt?: Date;
}

@Schema({ timestamps: true })
class Client {
  @Prop({
    required: true,
    type: Types.ObjectId, ref: 'Org'
  })
  clientId: Types.ObjectId;

  @Prop({ default: () => new Date() })
  createdAt?: Date;

  @Prop({ default: () => new Date() })
  updatedAt?: Date;
}

@Schema({ timestamps: true })
export class Preference {
  @Prop({ type: [Skill], default: [] })
  skills: Skill[];

  @Prop({ type: [Location], default: [] })
  locations: Location[];

  @Prop({ type: [Client], default: [] })
  clients: Client[];

  @Prop({ type: Types.ObjectId, ref: 'BasicUser', required: true }) // Ensure createdBy is required
  createdBy: Types.ObjectId;
}

export const PreferenceSchema = SchemaFactory.createForClass(Preference);