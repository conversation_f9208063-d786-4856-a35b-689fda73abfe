import { Controller, Get, Post, Body, Patch, Param, Delete, UnprocessableEntityException, UseGuards, Req, Query, BadRequestException } from '@nestjs/common';
import { OrgService } from './org.service';
import { CreateOrgDto } from './dto/create-org.dto';
import { UpdateOrgDto } from './dto/update-org.dto';
import { ApiBearerAuth, ApiBody, ApiOperation, ApiParam, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Types } from 'mongoose';
import { Logger } from '@nestjs/common';
import { ParseMongoObjectIdPipe } from 'src/pipes/parse-mongo-object-id/parse-mongo-object-id.pipe';
import { Roles } from 'src/auth/decorators/roles.decorator';
import { Role } from 'src/auth/enums/role.enum';
import { AuthJwtGuard } from 'src/auth/guards/auth-jwt/auth-jwt.guard';
import { RolesGuard } from 'src/auth/guards/roles/roles.guard';
import { validateObjectId } from 'src/utils/validation.utils';
import { QueryOrgDto } from './dto/query-org.dto';
import { Org } from './schemas/org.schema';
import { ChangeStatusDto } from 'src/common/dto/change-status.dto';
import { MoveToDto } from 'src/common/dto/move-to.dto';
import { OrgUserDto } from './dto/org-user.dto';
import { OrgTypeFilterDto } from './dto/org-type-filter.dto';
import { OrgAccessGuard } from 'src/auth/guards/org-access/org-access.guard';
import { CommentDto } from 'src/common/dto/comment.dto';
import { ChangeCustomStatusDto } from './dto/change-status.dto';
import { ChangeStatusOrgDto } from './dto/change-status-org.dto';
import { CreateMemberDto } from './dto/create-member.dto';
import { UpdateMemberDto } from './dto/update-member.dto';
import { PlaceholderDto } from './dto/placeholder.dto';
import { CreateRemoveVendorCustomerDto } from './dto/create-vendor-customer.dto';
import { UpdatePlaceholderDto } from './dto/update-placeholder.dto';
import { VendorJobAssignDto } from 'src/task/dto/assign-job-to-vendor.dto';
import { EmailTemplateEvent } from 'src/shared/constants';
import { AddToFavouriteOrgDto } from './dto/query-fav-org.dto';
import { UpdateCompanyProfileDto } from './dto/update-company-profile-dto';
import { VerifyOrgOtpDto } from './dto/verify-org-otp.dto';
import { ResendOtpDto } from './dto/resend-otp.dto';
import { ClientAddressDto } from 'src/common/dto/client-address.dto';
import { ClientOnboardingDto } from './dto/client-onboarding.dto';
import { UpdateInvoiceDetailsDto } from './dto/client-invoice.dto';
import { UpdateAddressDetailsDto, UpdateBankDetailsDto } from './dto/update-org-details.dto';
import { UpdateHikeSettingsDto } from './dto/hike-settings.dto';

@Controller('')
@ApiTags('Orgs')
export class OrgController {

  private readonly logger = new Logger(OrgController.name);

  constructor(private readonly orgService: OrgService) { }

  @Post()
  @ApiOperation({
    summary: 'Create a new org',
    description: `This endpoint allows you to create a new org. This endpoint is accessible to everyone.`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.AccountManager, Role.Vendor)
  @Roles()
  @ApiResponse({ status: 201, description: 'The org is created.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Data.' })
  @ApiResponse({ status: 401, description: 'Unauthorized access. Authentication is required.' })
  @ApiResponse({ status: 403, description: 'Forbidden. Only users with roles "Admin", "BUHead", "ResourceManager", "AccountManager" and "DeliveryManager" are permitted to use this endpoint.' })
  create(@Req() req: any, @Body() createOrgDto: CreateOrgDto) {
    if (req.user) {
      createOrgDto.createdBy = req.user._id;
      createOrgDto.createdByOrg = req.user.org._id;
      // createOrgDto.assignTo = req.user._id;
    }
    return this.orgService.create(createOrgDto);
  }

  @Post('register')
  @ApiOperation({
    summary: 'Create a new org',
    description: `This endpoint allows anyone to register a new organization.`
  })
  @ApiResponse({ status: 201, description: 'The org is created.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Data.' })
  createOrg(@Body() createOrgDto: CreateOrgDto) {
    return this.orgService.create(createOrgDto);
  }

  @Post('verify-org-otp')
  @ApiOperation({
    summary: 'Verify OTP for organization',
    description: 'This endpoint verifies the OTP sent to the organization contact email.',
  })
  @ApiResponse({ status: 200, description: 'Organization OTP verified successfully.' })
  @ApiResponse({ status: 400, description: 'Invalid or expired OTP code.' })
  @ApiBody({
    description: 'The request body for verifying organization OTP',
    required: true,
    type: VerifyOrgOtpDto,
  })
  async verifyOrgOTP(@Body() verifyOrgOtpDto: VerifyOrgOtpDto) {
    const { email, otpCode } = verifyOrgOtpDto;
    return this.orgService.verifyOTP(email, otpCode);
  }

  @Post('resend-org-otp')
  @ApiOperation({
    summary: 'Resend OTP to organization email',
    description: 'This endpoint generates and resends a new OTP to the organization email for completing registration.',
  })
  @ApiResponse({ status: 200, description: 'OTP resent successfully.' })
  @ApiResponse({ status: 404, description: 'Organization not found or has been deleted.' })
  @ApiBody({
    description: 'The request body for resending OTP to organization',
    required: true,
    type: ResendOtpDto,
  })
  async resendOrgOTP(@Body() resendOtpDto: ResendOtpDto): Promise<void> {
    const { email } = resendOtpDto;
    await this.orgService.resendOrgOTP(email);
  }

  @Post('vendor')
  @ApiOperation({
    summary: 'Create a new org',
    description: `This endpoint allows you to create a new org. This endpoint is accessible to authorized users.`,
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.AccountManager)
  @Roles()
  @ApiResponse({ status: 201, description: 'The org is created.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Data.' })
  @ApiResponse({ status: 401, description: 'Unauthorized access. Authentication is required.' })
  @ApiResponse({ status: 403, description: 'Forbidden. Only specific roles are permitted to use this endpoint.' })
  createVendor(@Req() req: any, @Body() createOrgDto: CreateOrgDto, @Query('isTemporary') isTemporary: boolean = false) {
    if (req.user) {
      createOrgDto.createdBy = req.user._id;
      createOrgDto.assignTo = req.user._id;
    }
    return this.orgService.createVendor(createOrgDto, isTemporary);
  }

  @Post('register-vendor')
  @ApiOperation({
    summary: 'Register a new org',
    description: `This endpoint allows anyone to register a new organization.`,
  })
  @ApiResponse({ status: 201, description: 'The org is created.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Data.' })
  createVendorOrg(@Body() createOrgDto: CreateOrgDto, @Query('isTemporary') isTemporary: boolean = true) {
    return this.orgService.createVendor(createOrgDto, isTemporary);
  }

  @Patch(':orgId/vendor-approve-reject')
  @ApiOperation({
    summary: 'Approve or Reject an Organization',
    description: `Endpoint to approve or reject an organization. Either "isApprove" or "isReject" query parameter must be true, but not both.`,
  })
  @ApiResponse({ status: 200, description: 'Organization status updated successfully.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Invalid Parameters.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 403, description: `Forbidden. Only users with specified roles are permitted to use this endpoint.` })
  @ApiResponse({ status: 404, description: 'Organization not found.' })
  @ApiParam({ name: 'orgId', description: 'ID of the organization' })
  @ApiQuery({ name: 'isApproved', required: false, type: Boolean, description: 'Set to true to approve the organization' })
  @ApiQuery({ name: 'isRejected', required: false, type: Boolean, description: 'Set to true to reject the organization' })
  async updateVendorStatus(@Param('orgId') orgId: string, @Query('isApproved') isApproved?: boolean, @Query('isRejected') isRejected?: boolean,) {
    const objId = validateObjectId(orgId);

    if (isApproved && isRejected) {
      throw new BadRequestException('Both "isApprove" and "isReject" cannot be true simultaneously.');
    }

    const updatedOrg = await this.orgService.updateVendorStatus(objId, isApproved, isRejected);
    return updatedOrg;
  }

  @Patch(':orgId/onboard')
  async markOrganizationAsOnboarded(@Param('orgId') orgId: string) {
    try {
      return await this.orgService.markAsOnboarded(new Types.ObjectId(orgId));
    } catch (error) {
      throw error;
    }
  }


  @Get()
  @ApiOperation({
    summary: 'Retrieve all active orgs',
    description: 'This endpoint returns a list of all active orgs. Accessible only to users with roles "Admin", "BUHead", "ResourceManager", "AccountManager" and "DeliveryManager".'
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.AccountManager, Role.Vendor)
  @Roles()
  @ApiResponse({ status: 200, description: 'All active orgs are retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 403, description: 'Forbidden. Only users with roles "Admin", "BUHead", "ResourceManager", "AccountManager" and "DeliveryManager" are permitted to use this endpoint.' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number', example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of items per page', example: 10 })
  getOnlyActiveOrgs(@Query('page') page: number = 1, @Query('limit') limit: number = 10) {
    return this.orgService.getOnlyActiveOrgs(page, limit);
  }

  @Get('all')
  @ApiOperation({
    summary: 'Retrieve all orgs ',
    description: `This endpoint retrieves a list of all orgs. Accessible only to users with roles "Admin", "BUHead", "ResourceManager", "AccountManager" and "DeliveryManager".`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.AccountManager, Role.Vendor)
  @Roles()
  @ApiResponse({ status: 200, description: 'All orgs including soft deleted are retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 403, description: `Forbidden. Only users with roles "Admin", "BUHead", "ResourceManager", "AccountManager" and "DeliveryManager" are permitted to use this endpoint.` })
  getAllOrgs(@Query() query: QueryOrgDto) {
    return this.orgService.getAllOrgs(query);
  }

  @Get('find-all-soft-deleted')
  @ApiOperation({
    summary: 'Retrieve all soft deleted orgs',
    description: `This endpoint retrieves a list of all soft deleted orgs. Accessible only to users with roles "Admin".`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin)
  @Roles()
  @ApiResponse({ status: 200, description: 'All soft deleted orgs are retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 403, description: `Forbidden. Only users with role "Admin" are permitted to use this endpoint.` })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number', example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of items per page', example: 10 })
  getOnlySoftDeletedOrgs(@Query('page') page: number = 1, @Query('limit') limit: number = 10) {
    return this.orgService.getOnlySoftDeletedOrgs(page, limit);
  }


  @Get(':orgId')
  @ApiOperation({
    summary: 'Retrieve an org by Id',
    description: 'This endpoint retrieves an org by its Id. Accessible only to users with roles "Admin", "BUHead", "ResourceManager", "AccountManager" and "DeliveryManager".'
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.AccountManager, Role.Vendor)
  @Roles()
  @ApiResponse({ status: 200, description: 'Org retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 403, description: 'Forbidden. Only users with roles "Admin", "BUHead", "ResourceManager", "AccountManager" and "DeliveryManager" are permitted to use this endpoint.' })
  @ApiResponse({ status: 404, description: 'Org not found.' })
  findOne(@Param('orgId') orgId: string) {
    const objId = validateObjectId(orgId);
    return this.orgService.findOne(objId);
  }

  @Get('org-type/count')
  @ApiOperation({
    summary: 'Filter orgs by type and return count of orgs',
    description: 'This endpoint filters orgs by type and returns the count of each type. This endpoint is accessible to everyone.'
  })
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.AccountManager, Role.TeamLead, Role.Recruiter, Role.Vendor)
  @Roles()
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  @ApiResponse({ status: 200, description: 'Filtered orgs and their counts are retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  getOrgCountByType(@Req() req: any, @Query() query: OrgTypeFilterDto) {
    const { orgType } = query;
    const userId = req.user._id;
    return this.orgService.getOrgCountByType(orgType, userId);
  }

  @Get('filter/org-type')
  @ApiOperation({
    summary: 'Filter orgs by type and return orgs',
    description: 'This endpoint filters orgs by type and returns orgs of each type. Accessible only to users "Admin", "BUHead", "ResourceManager", "AccountManager" and "DeliveryManager".'
  })
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.AccountManager, Role.Vendor, Role.JobSeeker)
  @Roles()
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  @ApiResponse({ status: 200, description: 'Filtered orgs and their counts are retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 403, description: 'Forbidden. Only users with roles "Admin", "BUHead", "ResourceManager", "AccountManager" and "DeliveryManager" are permitted to use this endpoint.' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number', example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of items per page', example: 10 })
  filterOrgsByType(@Req() req: any, @Query() query: OrgTypeFilterDto, @Query('page') page: number = 1, @Query('limit') limit: number = 10) {
    let userId: any;
    let orgId: any;
    let assignTo: any;
    if (req.user?.roles?.includes(Role.Admin)) {
      userId = req.user._id;
      orgId = req.user.org._id;
    }
    else if (req.user?.roles?.includes(Role.AccountManager)) {
      if (req.user.orgAdmin) {
        userId = req.user._id
      }
      assignTo = req.user._id;
      orgId = req.user.org._id;
    }
    else {
      userId = req.user._id
      orgId = req.user.org._id;
    }
    const { orgType, isDeleted, contactId } = query;
    // if(!isDeleted) {
    //   isDeleted = false;
    // }
    return this.orgService.filterOrgsByType(orgType, isDeleted ?? false, page, limit, userId, assignTo, orgId, contactId);
  }

  @Get('filter/all/org-type')
  @ApiOperation({
    summary: 'Filter orgs by type and return orgs',
    description: 'This endpoint filters orgs by type and returns orgs of each type. Accessible only to users "Admin", "BUHead", "ResourceManager", "AccountManager" and "DeliveryManager".'
  })
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.AccountManager, Role.Vendor, Role.JobSeeker)
  @Roles()
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  @ApiResponse({ status: 200, description: 'Filtered orgs and their counts are retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 403, description: 'Forbidden. Only users with roles "Admin", "BUHead", "ResourceManager", "AccountManager" and "DeliveryManager" are permitted to use this endpoint.' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number', example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of items per page', example: 10 })
  filterOrgsByTypeVendor(@Req() req: any, @Query() query: OrgTypeFilterDto, @Query('page') page: number = 1, @Query('limit') limit: number = 10) {
    const userId = req.user;
    const { orgType } = query;
    return this.orgService.filterOrgsByTypeVendor(orgType, page, limit, userId);
  }

  @Get('filter/all/org-and-vendor-invites')
  @ApiOperation({
    summary: 'Filter orgs and vendor invites by type',
    description: 'This endpoint filters orgs by type and also includes vendor invites. Accessible only to specific user roles.'
  })
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.AccountManager, Role.Vendor, Role.JobSeeker)
  @Roles()
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  @ApiResponse({ status: 200, description: 'Filtered orgs and vendor invites are retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 403, description: 'Forbidden. Only users with permitted roles can access this endpoint.' })
  @ApiQuery({ name: 'orgType', required: false, type: String, description: 'Organization type', example: 'vendor' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number', example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of items per page', example: 10 })
  @ApiQuery({ name: 'searchName', required: false, type: String, description: 'Search term for organization title/vendor name' })
  filterOrgsAndVendorInvites(
    @Req() req: any,
    @Query() query: OrgTypeFilterDto,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
    @Query('searchName') searchName?: string
  ) {
    const user = req.user;
    const { orgType } = query;
    return this.orgService.filterOrgsAndVendorInvites(orgType, page, limit, user, searchName);
  }


  @Patch(':orgId')
  @ApiOperation({
    summary: 'Update an org by Id',
    description: `This endpoint updates an org by Id. Accessible only to users with roles "Admin", "BUHead", "ResourceManager", "AccountManager" and "DeliveryManager" .`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.AccountManager, Role.Vendor)
  @Roles()
  @ApiResponse({ status: 200, description: 'Org is updated.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 403, description: `Forbidden. Only users with roles "Admin", "BUHead", "ResourceManager", "AccountManager" and "DeliveryManager" are permitted to use this endpoint.` })
  @ApiResponse({ status: 404, description: 'Org not found.' })
  @ApiParam({ name: 'orgId', description: 'ID of the org' })
  async update(@Req() req: any, @Param('orgId') orgId: string, @Body() updateOrgDto: UpdateOrgDto) {
    this.logger.log(Boolean(req.user))
    const objId = validateObjectId(orgId);
    this.logger.log(Boolean(req.user))
    return await this.orgService.update(objId, updateOrgDto, req.user);
  }

  @Get('search')
  @ApiOperation({
    summary: 'Search for org',
    description: `This endpoint allows you to search for org. Accessible only to users with roles "Admin", "BUHead", "ResourceManager", "AccountManager" and "DeliveryManager".`
  })
  @ApiBearerAuth()
  // @UseGuards(AuthJwtGuard, RolesGuard, OrgAccessGuard)
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.AccountManager, Role.Vendor)
  @Roles()
  @ApiResponse({ status: 200, description: 'Org found.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Data.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 403, description: `Forbidden. Only users with roles "Admin", "BUHead", "ResourceManager", "AccountManager" and "DeliveryManager" are permitted to use this endpoint.` })
  @ApiQuery({ name: 'title', required: true, type: String, description: 'Title of org', example: "Coding Limits" })
  search(@Query('title') title: string) {
    return this.orgService.searchOrgs(title);
  }

  @Get('filter')
  @ApiOperation({
    summary: 'Filter orgs',
    description: `This endpoint returns a list of orgs filtered by industry, country, account type, with pagination support. Accessible only to users with roles "Admin", "BUHead", "ResourceManager", "AccountManager" and "DeliveryManager".`
  })
  @ApiBearerAuth()
  // @UseGuards(AuthJwtGuard, RolesGuard, OrgAccessGuard)
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.AccountManager, Role.Vendor, Role.JobSeeker)
  @Roles()
  @ApiResponse({ status: 200, description: 'Orgs are retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 403, description: `Forbidden. Only users with roles "Admin", "BUHead", "ResourceManager", "AccountManager" and "DeliveryManager" are permitted to use this endpoint.` })
  @ApiResponse({ status: 404, description: 'Org not found.' })
  filterOrgs(@Req() req: any, @Query() query: QueryOrgDto) {
    const userId = req.user._id;
    const { title, industryId, countryId, accountTypeId, orgType, page, limit } = query;
    const queryOptions = { title, industryId, countryId, accountTypeId, orgType, page, limit };
    return this.orgService.filterOrgs(queryOptions, userId);
  }

  @Patch(':orgId/status')
  @ApiOperation({
    summary: 'Update status of an org by Id',
    description: `This endpoint updates the status of an org by Id. Accessible only to users with roles "Admin" and "BUHead".`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead)
  @Roles()
  @ApiResponse({ status: 200, description: 'Org status updated.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 403, description: `Forbidden. Only users with roles "Admin" and "BUHead" are permitted to use this endpoint.` })
  @ApiResponse({ status: 404, description: 'Org not found.' })
  @ApiParam({ name: 'orgId', description: 'ID of the org' })
  @ApiBody({ type: ChangeStatusOrgDto, description: 'The org details to change status' })
  changeStatus(@Req() req: any, @Param('orgId') orgId: string, @Body() changeStatusDto: ChangeStatusOrgDto) {
    this.logger.log(Boolean(req.user))
    const objId = validateObjectId(orgId);
    this.logger.log(Boolean(req.user))
    return this.orgService.changeStatus(objId, changeStatusDto, req.user);
  }

  @Patch('bulk-status')
  @ApiOperation({
    summary: 'Bulk update status of multiple orgs by their IDs',
    description: `This endpoint updates the status of multiple orgs by their IDs. Accessible only to users with roles "Admin" and "BUHead".`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead)
  @Roles()
  @ApiResponse({ status: 200, description: 'Org statuses updated.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 403, description: `Forbidden. Only users with roles "Admin" and "BUHead" are permitted to use this endpoint.` })
  @ApiResponse({ status: 404, description: 'Orgs not found.' })
  @ApiBody({ type: ChangeStatusOrgDto, description: 'The org IDs and new status' })
  @Patch('bulk-status')
  async bulkChangeStatus(@Req() req: any, @Body() bulkChangeStatusDto: ChangeStatusOrgDto) {
    const { orgIds } = bulkChangeStatusDto;
    if (orgIds && orgIds.length > 0) {
      const validOrgIds = orgIds.map(orgId => validateObjectId(orgId));
      return this.orgService.bulkChangeStatus(validOrgIds, bulkChangeStatusDto, req.user);
    } else {
      // Handle the case when orgIds is not provided or empty
      throw new BadRequestException('orgIds are required for bulk status update.');
    }
  }


  // @Post(':orgId/assign-admin')
  // @ApiOperation({
  //   summary: 'Assign admin to the org based on org type',
  //   description: `This endpoint assigns an admin to the org by Id. Accessible only to users with roles "${Role.SuperAdmin}", "${Role.Admin}", "${Role.CustomerOrgAdmin}", "${Role.AgencyOrgAdmin}" .`
  // })
  // @ApiBearerAuth()
  // @UseGuards(AuthJwtGuard, RolesGuard, OrgAccessGuard)
  // @Roles(Role.SuperAdmin, Role.Admin, Role.AgencyOrgAdmin, Role.CustomerOrgAdmin)
  // @ApiResponse({ status: 200, description: 'Admin assigned to org successfully.' })
  // @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  // @ApiResponse({ status: 403, description: `Forbidden. Only users with roles "${Role.SuperAdmin}", "${Role.Admin}", "${Role.CustomerOrgAdmin}", and "${Role.AgencyOrgAdmin}" are permitted to use this endpoint.` })
  // @ApiResponse({ status: 404, description: 'Org not found.' })
  // @ApiParam({ name: 'orgId', description: 'ID of the org' })
  // assignAdmin(@Param('orgId') orgId: string, @Body() orgUserDto: OrgUserDto) {
  //   const { userId } = orgUserDto;
  //   const objId = validateObjectId(orgId);
  //   const userObjId = validateObjectId(userId);
  //   return this.orgService.assignAdmin(objId, userObjId);
  // }

  @Post(':orgId/users/add')
  @ApiOperation({
    summary: 'Adds a user with roles to org',
    description: `Adds a user with roles to org. Accessible only to users with roles "Admin".`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin)
  @Roles()
  @ApiResponse({ status: 200, description: 'User added to org successfully.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 403, description: `Forbidden. Only users with roles "Admin" are permitted to use this endpoint.` })
  @ApiResponse({ status: 404, description: 'Org not found.' })
  @ApiParam({ name: 'orgId', description: 'ID of the org' })
  async addUser(@Param('orgId') orgId: string, @Body() orgUserDto: OrgUserDto) {
    const { userId, role } = orgUserDto;
    const objId = validateObjectId(orgId);
    const userObjId = validateObjectId(userId);
    return this.orgService.addUser(objId, userObjId, role);
  }

  @Post(':orgId/members')
  @ApiOperation({
    summary: 'Adds a member with roles to org',
    description: `Adds a member with roles to org. Accessible only to user with role "Admin".`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.Vendor)
  @Roles()
  @ApiResponse({ status: 200, description: 'Member added to org successfully.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 403, description: `Forbidden. Only user with role "Admin" and "Vendor" permitted to use this endpoint.` })
  @ApiResponse({ status: 404, description: 'Org not found.' })
  @ApiParam({ name: 'orgId', description: 'ID of the org' })
  async addMember(@Param('orgId') orgId: string, @Body() createMemberDto: CreateMemberDto) {
    const objId = validateObjectId(orgId);
    // const buId = validateObjectId(businessUnitId);
    return this.orgService.addMember(orgId, createMemberDto);
  }

  @Get(':orgId/business-units/:businessUnitId/members')
  @ApiOperation({
    summary: 'Retrieve list of members of a specific business-unit',
    description: `Retrieves list of members with roles to business-unit. Accessible only to users with roles "Admin", "BUHead", "ResourceManager", "AccountManager", "DeliveryManager" and "TeamLead" .`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.AccountManager, Role.TeamLead, Role.Vendor)
  @Roles()
  @ApiResponse({ status: 200, description: 'Org retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 403, description: 'Forbidden. Only users with roles "Admin", "BUHead", "ResourceManager", "DeliveryManager", "AccountManager" and "TeamLead" are permitted to use this endpoint.' })
  @ApiResponse({ status: 404, description: 'Org not found.' })
  @ApiQuery({ name: 'searchTerm', required: false, description: 'search members by name and email' })
  @ApiQuery({ name: 'userId', required: false, description: 'userId of the editing user' })
  @ApiQuery({
    name: 'roles',
    type: [String],
    required: false,
    description: 'Filter members by roles (array of strings)',
  })
  getAllMembers(@Param('orgId') orgId: string,
    @Param('businessUnitId') businessUnitId: string,
    @Query('searchTerm') searchTerm?: string,
    @Query('userId') userId?: string,
    @Query('roles') roles?: string[],
  ) {
    const org = validateObjectId(orgId);
    const bu = validateObjectId(businessUnitId);
    return this.orgService.getAllMembers(org, businessUnitId, searchTerm, roles, userId);
  }

  @Get(':orgId/business-units/:businessUnitId/Hierarchy-members')
  @ApiOperation({
    summary: 'Retrieve list of members of a specific business-unit',
    description: `Retrieves list of members with roles to business-unit. Accessible only to users with roles "Admin", "BUHead", "ResourceManager", "AccountManager", "DeliveryManager" and "TeamLead" .`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.AccountManager, Role.TeamLead, Role.Vendor)
  @Roles()
  @ApiResponse({ status: 200, description: 'Org retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 403, description: 'Forbidden. Only users with roles "Admin", "BUHead", "ResourceManager", "DeliveryManager", "AccountManager" and "TeamLead" are permitted to use this endpoint.' })
  @ApiResponse({ status: 404, description: 'Org not found.' })
  getAllDepartmentMembers(@Req() req: any, @Param('orgId') orgId: string,
    @Param('businessUnitId') businessUnitId: string,
  ) {
    const org = validateObjectId(orgId);
    const bu = validateObjectId(businessUnitId);
    const userId = req.user._id;
    return this.orgService.getAllDepartmentMembers(org, businessUnitId, userId);
  }

  @Get(':orgId/business-units/members')
  @ApiOperation({
    summary: 'Retrieve list of members of a specific business-unit',
    description: `Retrieves list of members with roles to business-unit. Accessible only to users with roles "Admin", "BUHead", "ResourceManager", "AccountManager", "DeliveryManager" and "TeamLead" .`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.AccountManager, Role.TeamLead, Role.Vendor)
  @Roles()
  @ApiResponse({ status: 200, description: 'Org retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 403, description: 'Forbidden. Only users with roles "Admin", "BUHead", "ResourceManager", "DeliveryManager", "AccountManager" and "TeamLead" are permitted to use this endpoint.' })
  @ApiResponse({ status: 404, description: 'Org not found.' })
  @ApiQuery({ name: 'searchTerm', required: false, description: 'search members by name and email' })
  @ApiQuery({ name: 'userId', required: false, description: 'userId of the editing user' })
  @ApiQuery({
    name: 'roles',
    type: [String],
    required: false,
    description: 'Filter members by roles (array of strings)',
  })
  @ApiQuery({
    name: 'businessUnitIds',
    type: [String],
    required: false,
    description: 'Filter members by businessUnitIds (array of strings)',
  })
  getAllMembersOrgs(@Param('orgId') orgId: string,
    // @Param('businessUnitId') businessUnitId: string,
    @Query('businessUnitIds') businessUnitIds: string[],
    @Query('searchTerm') searchTerm?: string,
    @Query('userId') userId?: string,
    @Query('roles') roles?: string[],
  ) {
    const org = validateObjectId(orgId);
    // const bu = validateObjectId(businessUnitId);
    // const buIdsAsStrings = businessUnitIds.map(id => id.toString());
    const buIdsAsStrings: string[] = businessUnitIds
      ? (Array.isArray(businessUnitIds) ? businessUnitIds : [businessUnitIds])
      : [];

    return this.orgService.getAllMembersOrg(org, buIdsAsStrings, searchTerm, roles, userId);
  }

  @Get(':orgId/business-units/:businessUnitId/team-members')
  @ApiOperation({
    summary: 'Retrieve list of members of a specific team lead',
    description: `Retrieves list of members with roles of team lead. Accessible only to users with roles "Admin", "BUHead", "ResourceManager", "DeliveryManager", "AccountManager" and "TeamLead" .`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.AccountManager, Role.TeamLead, Role.Vendor)
  @Roles()
  @ApiResponse({ status: 200, description: 'Org retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 403, description: 'Forbidden. Only users with roles "Admin", "BUHead", "ResourceManager", "DeliveryManager", "AccountManager" and "TeamLead" are permitted to use this endpoint.' })
  @ApiResponse({ status: 404, description: 'Org not found.' })
  @ApiQuery({ name: 'leadId', required: true, description: 'Lead member id' })
  getTeamMembersOfLead(@Param('orgId') orgId: string,
    @Param('businessUnitId') businessUnitId: string,
    @Query('leadId') leadId: string,
  ) {
    const org = validateObjectId(orgId);
    const bu = validateObjectId(businessUnitId);
    return this.orgService.getTeamMembersOfLead(org, businessUnitId, leadId);
  }


  // TODO: GET MEMBER BY Role

  // @Get(':orgId/business-units/:businessUnitId/members')
  // @ApiOperation({
  //   summary: 'Retrieve list of members of a specific business-unit',
  //   description: `Retrieves list of members with roles to business-unit. Accessible only to users with roles "${Role.SuperAdmin}", "${Role.Admin}", "${Role.CustomerOrgAdmin}", "${Role.AgencyOrgAdmin}" .`
  // })
  // @ApiBearerAuth()
  // @UseGuards(AuthJwtGuard)
  // @Roles(Role.SuperAdmin, Role.Admin, Role.SalesRep, Role.AgencyOrgAdmin)
  // @ApiResponse({ status: 200, description: 'Org retrieved.' })
  // @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  // @ApiResponse({ status: 403, description: 'Forbidden' })
  // @ApiResponse({ status: 404, description: 'Org not found.' })
  // @ApiQuery({ name: 'searchTerm', required: false, description: 'search members by name and email' })
  // get(@Param('orgId') orgId: string,
  //   @Param('businessUnitId') businessUnitId: string,
  //   @Query('searchTerm') searchTerm?: string,
  // ) {
  //   const org = validateObjectId(orgId);
  //   const bu = validateObjectId(businessUnitId);
  //   return this.orgService.getAllMembers(org, bu, searchTerm);
  // }

  @Get(':orgId/members')
  @ApiOperation({
    summary: 'Retrieve list of members of an org',
    description: `Retrieves list of members with roles to org. Accessible only to users with roles "Admin", "BUHead", "ResourceManager", "DeliveryManager", "AccountManager" and "TeamLead" .`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.AccountManager, Role.TeamLead, Role.Vendor)
  @Roles()
  @ApiResponse({ status: 200, description: 'Members retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 403, description: 'Forbidden. Only users with roles "Admin", "BUHead", "ResourceManager", "DeliveryManager", "AccountManager" and "TeamLead" are permitted to use this endpoint.' })
  @ApiResponse({ status: 404, description: 'Org not found.' })
  @ApiQuery({ name: 'searchTerm', required: false, description: 'search members by name and email' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number', example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of items per page', example: 10 })
  @ApiQuery({ name: 'departmentTreeId', required: false, type: String, description: 'ID of the department tree to filter members by' })
  getAllMembersByOrg(@Param('orgId') orgId: string,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
    @Query('searchTerm') searchTerm?: string,
    @Query('departmentTreeId') departmentTreeId?: string
  ) {
    const org = validateObjectId(orgId);
    const departmentIdObj = departmentTreeId ? validateObjectId(departmentTreeId) : undefined;
    return this.orgService.getAllMembersByOrg(org, page, limit, searchTerm, departmentIdObj);
  }

  @Get(':orgId/all-members')
  @ApiOperation({
    summary: 'Retrieve list of members of an org',
    description: `Retrieves list of members with roles to org. Accessible only to users with roles "Admin", "BUHead", "ResourceManager", "DeliveryManager", "AccountManager" and "TeamLead".`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.AccountManager, Role.TeamLead, Role.Vendor)
  @Roles()
  @ApiResponse({ status: 200, description: 'Members retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 403, description: 'Forbidden. Only users with roles "Admin", "BUHead", "ResourceManager", "DeliveryManager", "AccountManager" and "TeamLead" are permitted to use this endpoint' })
  @ApiResponse({ status: 404, description: 'Org not found.' })
  @ApiQuery({ name: 'searchTerm', required: false, description: 'search members by name and email' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number', example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of items per page', example: 10 })
  @ApiQuery({ name: 'departmentTreeId', required: false, type: String, description: 'ID of the department tree to filter members by' })
  getAllMembersByOrgAndRole(@Param('orgId') orgId: string,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
    @Query('searchTerm') searchTerm?: string,
    @Query('departmentTreeId') departmentTreeId?: string
  ) {
    const org = validateObjectId(orgId);
    const departmentIdObj = departmentTreeId ? validateObjectId(departmentTreeId) : undefined;
    return this.orgService.getAllMembersByOrgAndRole(org, page, limit, searchTerm, departmentIdObj);
  }


  @Get(':orgId/members/:memberId')
  @ApiOperation({
    summary: 'Retrieve a member by Id',
    description: `Get a member by id with roles. Accessible only to users with roles "Admin", "BUHead", "ResourceManager", "DeliveryManager", "AccountManager" and "TeamLead" .`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.AccountManager, Role.TeamLead, Role.Vendor)
  @Roles()
  @ApiResponse({ status: 200, description: 'Member retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 403, description: 'Forbidden. Only user with roles "Admin", "BUHead", "ResourceManager", "DeliveryManager", "AccountManager" and "TeamLead" are permitted to use this endpoint.' })
  @ApiResponse({ status: 404, description: 'Member not found.' })
  getMember(@Param('orgId') orgId: string, @Param('memberId') memberId: string,) {
    const objId = validateObjectId(orgId);
    const member = validateObjectId(memberId);
    return this.orgService.getMember(objId, member);
  }

  @Patch(':orgId/members/:memberId')
  @ApiOperation({
    summary: 'Updates a member by id',
    description: `Updates a member by id  org. Accessible only to users with role "Admin".`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.Vendor)
  @Roles()
  @ApiResponse({ status: 200, description: 'Member updated successfully.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 403, description: `Forbidden. Only users with role "Admin" are permitted to use this endpoint.` })
  @ApiResponse({ status: 404, description: 'Org not found.' })
  @ApiParam({ name: 'orgId', description: 'ID of the org' })
  async updateMember(@Req() req: any, @Param('orgId') orgId: string, @Param('memberId') memberId: string, @Body() updateMemberDto: UpdateMemberDto) {
    const org = validateObjectId(orgId);
    const member = validateObjectId(memberId);
    return this.orgService.updateMember(org, member, updateMemberDto, req.user._id);
  }

  @Delete(':orgId/members/:memberId/soft-delete')
  @ApiOperation({
    summary: 'Soft delete a member with roles to org',
    description: `Soft delete a member by id. Accessible only to users with role "Admin".`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.Vendor)
  @Roles()
  @ApiResponse({ status: 200, description: 'Member removed successfully.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 403, description: `Forbidden. Only users with role "Admin" are permitted to use this endpoint.` })
  @ApiResponse({ status: 404, description: 'Org not found.' })
  @ApiParam({ name: 'orgId', description: 'ID of the org' })
  async deleteMember(@Param('orgId') orgId: string, @Param('memberId') memberId: string) {
    const org = validateObjectId(orgId);
    const member = validateObjectId(memberId);
    return this.orgService.removeMember(org, member);
  }

  @Delete(':orgId/members/bulk-delete')
  @ApiOperation({
    summary: 'Soft delete multiple members from an org',
    description: `Soft delete multiple members by their IDs. Accessible only to users with role "Admin".`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.Vendor)
  @Roles()
  @ApiResponse({ status: 200, description: 'Members removed successfully.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 403, description: `Forbidden. Only users with role "Admin" are permitted to use this endpoint.` })
  @ApiResponse({ status: 404, description: 'Org or members not found.' })
  @ApiParam({ name: 'orgId', description: 'ID of the org' })
  @ApiBody({ type: [String], description: 'Array of member IDs to be soft deleted' }) // Describes the expected body
  async bulkDeleteMembers(@Param('orgId') orgId: string, @Body() memberIds: string[]) {
    // Validate the orgId and memberIds
    const org = validateObjectId(orgId);
    const members = memberIds.map(memberId => validateObjectId(memberId));

    this.logger.log(members, 'memberIds')
    // Call the service method to handle bulk deletion
    return this.orgService.bulkRemoveMembers(org, members);
    // return await org;
  }


  @Delete(':orgId/members/:memberId/hard-delete')
  @ApiOperation({
    summary: 'Permanently remove a member with roles to org',
    description: `Permanently remove a member with roles to org. Accessible only to users with role "Admin".`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.Vendor)
  @Roles()
  @ApiResponse({ status: 200, description: 'Member removed successfully.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 403, description: `Forbidden. Only users with role "Admin" are permitted to use this endpoint.` })
  @ApiResponse({ status: 404, description: 'Member not found.' })
  @ApiParam({ name: 'orgId', description: 'ID of the org' })
  async hardDeleteMember(@Param('orgId') orgId: string, @Param('memberId') memberId: string) {
    const org = validateObjectId(orgId);
    const member = validateObjectId(memberId);
    return this.orgService.hardDeleteMember(org, member);
  }

  @Get(':orgId/vendors')
  @ApiOperation({
    summary: 'Get all vendors of org',
    description: `Get all vendors of org. Accessible only to users with roles "Admin", "BUHead", "ResourceManager", "AccountManager", "DeliveryManager" and "TeamLead".`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.AccountManager, Role.TeamLead, Role.Vendor)
  @Roles()
  @ApiResponse({ status: 200, description: 'Get all vendors of org' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 403, description: `Forbidden. Only users with roles "Admin", "BUHead", "ResourceManager", "AccountManager", "DeliveryManager" and "TeamLead" are permitted to use this endpoint.` })
  @ApiResponse({ status: 404, description: 'Org not found.' })
  @ApiParam({ name: 'orgId', description: 'ID of the org' })
  async getAllVendors(@Param('orgId') orgId: string) {
    const objId = validateObjectId(orgId);
    return this.orgService.getAllVendors(objId);
  }

  @Post(':orgId/vendors')
  @ApiOperation({
    summary: 'Adds a vendor to org',
    description: `Adds a vendor to org. Accessible only to users with roles "Admin", "BUHead", "ResourceManager", "AccountManager", "DeliveryManager" and "TeamLead" .`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.AccountManager, Role.TeamLead, Role.Vendor)
  @Roles()
  @ApiResponse({ status: 200, description: 'Vendor added to org successfully.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 403, description: `Forbidden. Only users with roles "Admin", "BUHead", "ResourceManager", "AccountManager", "DeliveryManager" and "TeamLead" are permitted to use this endpoint.` })
  @ApiResponse({ status: 404, description: 'Org not found.' })
  @ApiParam({ name: 'orgId', description: 'ID of the org' })
  async addVendor(@Param('orgId') orgId: string, @Body() createRemoveVendorCustomerDto: CreateRemoveVendorCustomerDto) {
    const objId = validateObjectId(orgId);
    // const buId = validateObjectId(businessUnitId);
    return this.orgService.addVendor(objId, createRemoveVendorCustomerDto);
  }

  @Delete(':orgId/remove-vendor')
  @ApiOperation({
    summary: 'Removes a vendor from org',
    description: `Removes a vendor from org. Accessible only to users with roles "Admin", "BUHead", "ResourceManager", "AccountManager", "DeliveryManager" and "TeamLead".`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.AccountManager, Role.TeamLead, Role.Vendor)
  @Roles()
  @ApiResponse({ status: 200, description: 'Vendor removed from org successfully.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 403, description: `Forbidden. Only users with roles "Admin", "BUHead", "ResourceManager", "AccountManager", "DeliveryManager" and "TeamLead" are permitted to use this endpoint.` })
  @ApiResponse({ status: 404, description: 'Org not found.' })
  @ApiParam({ name: 'orgId', description: 'ID of the org' })
  async removeVendor(@Param('orgId') orgId: string, @Body() createRemoveVendorCustomerDto: CreateRemoveVendorCustomerDto) {
    const objId = validateObjectId(orgId);
    return this.orgService.removeVendor(objId, createRemoveVendorCustomerDto);
  }

  @Get(':orgId/customers')
  @ApiOperation({
    summary: 'Get all customers of org',
    description: `Get all customers of org. Accessible only to users with roles "Admin", "BUHead", "ResourceManager", "AccountManager", "DeliveryManager" and "TeamLead".`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.AccountManager, Role.TeamLead, Role.Vendor)
  @Roles()
  @ApiResponse({ status: 200, description: 'Get all customers of org' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 403, description: `Forbidden. Only users with roles "Admin", "BUHead", "ResourceManager", "AccountManager", "DeliveryManager" and "TeamLead" are permitted to use this endpoint.` })
  @ApiResponse({ status: 404, description: 'Org not found.' })
  @ApiParam({ name: 'orgId', description: 'ID of the org' })
  async getAllCustomers(@Param('orgId') orgId: string) {
    const objId = validateObjectId(orgId);
    return this.orgService.getAllCustomers(objId);
  }

  @Post(':orgId/customers')
  @ApiOperation({
    summary: 'Adds a customer to org',
    description: `Adds a customer to org. Accessible only to users with roles "Admin", "BUHead", "ResourceManager", "AccountManager", "DeliveryManager" and "TeamLead".`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.AccountManager, Role.TeamLead, Role.Vendor)
  @Roles()
  @ApiResponse({ status: 200, description: 'Customer added to org successfully.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 403, description: `Forbidden. Only users with roles "Admin", "BUHead", "ResourceManager", "AccountManager", "DeliveryManager" and "TeamLead" are permitted to use this endpoint.` })
  @ApiResponse({ status: 404, description: 'Org not found.' })
  @ApiParam({ name: 'orgId', description: 'ID of the org' })
  async addCustomer(@Param('orgId') orgId: string, @Body() createRemoveVendorCustomerDto: CreateRemoveVendorCustomerDto) {
    const objId = validateObjectId(orgId);
    return this.orgService.addCustomer(objId, createRemoveVendorCustomerDto);
  }

  @Delete(':orgId/remove-customer')
  @ApiOperation({
    summary: 'Removes a customer from org',
    description: `Removes a customer from org. Accessible only to users with roles "Admin", "BUHead", "ResourceManager", "AccountManager", "DeliveryManager" and "TeamLead" .`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.AccountManager, Role.TeamLead, Role.Vendor)
  @Roles()
  @ApiResponse({ status: 200, description: 'Customer removed from org successfully.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 403, description: `Forbidden. Only users with roles "Admin", "BUHead", "ResourceManager", "AccountManager", "DeliveryManager" and "TeamLead" are permitted to use this endpoint.` })
  @ApiResponse({ status: 404, description: 'Org not found.' })
  @ApiParam({ name: 'orgId', description: 'ID of the org' })
  async removeCustomer(@Param('orgId') orgId: string, @Body() createRemoveVendorCustomerDto: CreateRemoveVendorCustomerDto) {
    const objId = validateObjectId(orgId);
    return this.orgService.removeCustomer(objId, createRemoveVendorCustomerDto);
  }

  @Patch(':orgId/assign-to')
  @ApiOperation({
    summary: 'Move the ownership of an org by Id',
    description: `This endpoint moves the ownership of an org by Id. Accessible only to users with roles "Admin" and "BUHead".`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.Vendor)
  @Roles()
  @ApiResponse({ status: 200, description: 'Org ownership moved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 403, description: `Forbidden. Only users with roles "Admin" and "BUHead" are permitted to use this endpoint.` })
  @ApiResponse({ status: 404, description: 'Org not found.' })
  @ApiParam({ name: 'orgId', description: 'ID of the org' })
  @ApiBody({ type: MoveToDto, description: 'The org details to move' })
  moveOrg(@Req() req: any, @Param('orgId') orgId: string, @Body() moveToDto: MoveToDto) {
    const orgObjId = validateObjectId(orgId);
    const { moveTo, assignTo, comment } = moveToDto;
    // const newOwnerId = validateObjectId(moveTo);
    if (!assignTo) {
      throw new BadRequestException('assignTo is required.');
    }
    return this.orgService.assignTo(orgObjId, assignTo, comment, req.user);
  }


  @Patch(':orgId/suspend')
  @ApiOperation({
    summary: 'Suspends an org by Id',
    description: `This endpoint suspends an org by Id. Accessible only to users with role "Admin".`
  })
  @ApiBearerAuth()
  // @UseGuards(AuthJwtGuard, RolesGuard, OrgAccessGuard)
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.Vendor)
  @Roles()
  @ApiResponse({ status: 201, description: 'Org is suspended.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: `Forbidden. Only users with role "Admin" are permitted to use this endpoint.` })
  @ApiResponse({ status: 404, description: 'Org not found.' })
  @ApiParam({ name: 'orgId', description: 'ID of the org' })
  suspend(@Param('orgId') orgId: string, @Body() commentDto: CommentDto) {
    const objId = validateObjectId(orgId);
    return this.orgService.suspendOrg(objId, commentDto);
  }


  @Patch(':orgId/approve')
  @ApiOperation({
    summary: 'Approve an org by Id',
    description: `This endpoint approves an org by its Id. Accessible only to users with role "Admin".`
  })
  @ApiBearerAuth()
  // @UseGuards(AuthJwtGuard, RolesGuard, OrgAccessGuard)
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.Vendor)
  @Roles()
  @ApiResponse({ status: 201, description: 'Org is approved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: `Forbidden. Only users with role "Admin" are permitted to use this endpoint.` })
  @ApiResponse({ status: 404, description: 'Org not found.' })
  @ApiParam({ name: 'orgId', description: 'ID of the org' })
  approve(@Param('orgId') orgId: string) {
    const objId = validateObjectId(orgId);
    return this.orgService.approveOrg(objId);
  }

  @Patch(':orgId/reject')
  @ApiOperation({
    summary: 'Reject an org by Id',
    description: `This endpoint rejects an org by its Id. Accessible only to users with role "Admin".`
  })
  @ApiBearerAuth()
  // @UseGuards(AuthJwtGuard, RolesGuard, OrgAccessGuard)
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.Vendor)
  @Roles()
  @ApiResponse({ status: 201, description: 'Org is rejected.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: `Forbidden. Only users with role "Admin" are permitted to use this endpoint.` })
  @ApiResponse({ status: 404, description: 'Org not found.' })
  @ApiParam({ name: 'orgId', description: 'ID of the org' })
  reject(@Param('orgId') orgId: string) {
    const objId = validateObjectId(orgId);
    return this.orgService.rejectOrg(objId);
  }


  @Post(':orgId/comments')
  @ApiResponse({ status: 201, description: 'Comment added to the orgId.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Data.' })
  // @ApiResponse({ status: 403, description: 'Forbidden, User with role "super-admin", "admin" and "sales-rep" can only use this end point.' })
  @ApiOperation({ summary: 'Comment on the orgId.', description: 'This endpoint allows you to add comments on the orgId. This is accessible for everyone' })
  @ApiParam({ name: 'orgId', description: 'Id of the orgId.' })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.AccountManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.Vendor)
  @Roles()
  addComment(@Req() req: any, @Param('orgId') orgId: string, @Body() commentDto: CommentDto) {
    const objId = validateObjectId(orgId);
    commentDto.user = req.user._id;
    return this.orgService.addComment(objId, commentDto);
  }

  @Get(':orgId/comments')
  @ApiResponse({ status: 200, description: 'Comments retrieved successfully.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  // @ApiResponse({ status: 403, description: 'Forbidden, User with role "super-admin", "admin" and "sales-rep" can only use this end point.' })
  @ApiOperation({ summary: 'Get comments of the orgId.', description: 'This endpoint allows you to retrieve comments on the orgId. This is accessible  for everyone.' })
  @ApiParam({ name: 'orgId', description: 'Id of the orgId.' })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.AccountManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.Vendor)
  @Roles()
  getComments(@Param('orgId') orgId: string) {
    const objId = validateObjectId(orgId);
    return this.orgService.getComments(objId);
  }


  @Patch(':orgId/restore')
  @ApiOperation({
    summary: 'Restore soft deleted org by ID.',
    description: `This endpoint restores a soft-deleted org by its ID. Accessible only to users with role "Admin".`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.Vendor)
  @Roles()
  @ApiResponse({ status: 200, description: 'Org updated.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: `Forbidden. Only users with role "Admin" permitted to use this endpoint.` })
  @ApiParam({ name: 'orgId', description: 'ID of the org.' })
  async restoreSoftDeletedOrg(@Param('orgId') orgId: string) {
    const objId = validateObjectId(orgId);
    return await this.orgService.restoreSoftDeletedOrg(objId);
  }

  @Patch('restore')
  @ApiOperation({
    summary: 'Restore all soft deleted orgs',
    description: `This endpoint restores all soft deleted orgs. Accessible only to users with role "Admin".`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.Vendor)
  @Roles()
  @ApiResponse({ status: 201, description: 'All orgs are restored.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: `Forbidden. Only users with role "Admin" are permitted to use this endpoint.` })
  @ApiResponse({ status: 404, description: 'Org not found.' })
  restore() {
    return this.orgService.restore();
  }


  // @Delete('users/:userId/delete')
  // @ApiOperation({
  //   summary: 'Deletes a user with roles from org',
  //   description: `Deletes a user with roles from org. Accessible only to users with roles "${Role.SuperAdmin}", "${Role.Admin}", "${Role.CustomerOrgAdmin}", "${Role.AgencyOrgAdmin}" .`
  // })
  // @ApiBearerAuth()
  // @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.SuperAdmin, Role.Admin, Role.AgencyOrgAdmin, Role.CustomerOrgAdmin)
  // @ApiResponse({ status: 200, description: 'User deleted from org.' })
  // @ApiResponse({ status: 401, description: 'Unauthorized.' })
  // @ApiResponse({ status: 403, description: `Forbidden. Only users with roles "${Role.SuperAdmin}", "${Role.Admin}", "${Role.CustomerOrgAdmin}", "${Role.AgencyOrgAdmin}"  are permitted to use this endpoint.` })
  // @ApiResponse({ status: 404, description: 'User not found.' })
  // @ApiParam({ name: 'userId', description: 'ID of the user' })
  // async deleteUser(@Param('userId') userId: string) {
  //   const userObjId = validateObjectId(userId);
  //   return this.orgService.deleteUser(userObjId);
  // }

  @Delete(':orgId/soft-delete')
  @ApiOperation({
    summary: 'Soft delete an org by Id',
    description: `Soft deletes an org by Id. Accessible only to users with role "Admin".`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.Vendor)
  @Roles()
  @ApiResponse({ status: 200, description: 'Org soft deleted.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: `Forbidden. Only users with role "Admin" are permitted to use this endpoint.` })
  @ApiResponse({ status: 404, description: 'Org not found.' })
  @ApiParam({ name: 'orgId', description: 'ID of the org' })
  deleteSoftDeletedOrg(@Param('orgId') orgId: string, @Body() commentDto: CommentDto) {
    const objId = validateObjectId(orgId);
    return this.orgService.softDelete(objId, commentDto);
  }

  @Delete(':orgId/hard-delete')
  @ApiOperation({
    summary: 'Hard delete an org by Id',
    description: `Deletes an org permanently by its Id. This is accessible only for "Admin".`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.Vendor)
  @Roles()
  @ApiResponse({ status: 200, description: 'Org is hard deleted.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 403, description: `Forbidden. Only users with role "Admin" are permitted to use this endpoint.` })
  @ApiResponse({ status: 404, description: 'Org not found.' })
  @ApiParam({ name: 'orgId', description: 'ID of the org' })
  async remove(@Param('orgId') orgId: string) {
    const objId = validateObjectId(orgId);
    return await this.orgService.hardDelete(objId);
  }

  @Delete('delete-all')
  @ApiOperation({
    summary: 'Hard delete all orgs',
    description: `This endpoint hard deletes all orgs. This is accessible only for "Admin"`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin)
  @Roles()
  @ApiResponse({ status: 200, description: 'All orgs are deleted.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: `Forbidden. Only users with role "Admin" are permitted to use this endpoint.` })
  @ApiResponse({ status: 404, description: 'Orgs not found.' })
  deleteAll() {
    return this.orgService.hardDeleteAllOrgs();
  }

  @Delete('bulk-delete')
  @ApiOperation({
    summary: 'Soft delete multiple orgs',
    description: `This endpoint soft deletes multiple orgs by setting the 'isDeleted' flag to true. Accessible only for "Admin" role.`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.Vendor)
  @Roles()
  @ApiResponse({ status: 200, description: 'Orgs have been soft deleted.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: `Forbidden. Only users with role "Admin" can access this endpoint.` })
  @ApiResponse({ status: 404, description: 'No orgs found for the given IDs.' })
  async bulkSoftDelete(@Body() orgIds: Types.ObjectId[]): Promise<{ message: string }> {
    const result = await this.orgService.bulkDelete(orgIds); // Calling the service method to perform soft delete

    if (result.modifiedCount > 0) {
      return { message: `Successfully soft deleted ${result.modifiedCount} organization(s).` };
    } else {
      return { message: 'No organizations were soft deleted.' };
    }
  }

  @Patch(':orgId/custom-status')
  @ApiOperation({ summary: 'Update an Org Custom Status by Id', description: `This endpoint updates an org by Id. This is accessible only for "Admin".` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.Vendor)
  @Roles()
  @ApiResponse({ status: 200, description: 'Org Custom Status is updated.' })
  @ApiResponse({ status: 401, description: 'Unauthorized ' })
  @ApiResponse({ status: 403, description: 'Forbidden. Only users with role "Admin" can access this endpoint.' })
  @ApiResponse({ status: 404, description: 'Org not found.' })
  @ApiParam({ name: 'orgId', description: 'ID of the Org' })
  async changeCustomStatus(
    @Param('orgId') orgId: string,
    @Body() changeStatusDto: ChangeCustomStatusDto,
    @Req() req: any
  ) {
    const objId = validateObjectId(orgId);
    return await this.orgService.changeCustomStatus(objId, changeStatusDto, req.user);
  }

  @Get('counts-by-custom-status')
  @ApiOperation({ summary: 'Get counts by custom status', description: `This endpoint gives the count by Org status. This is accessible only for "Admin"` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.Vendor)
  @Roles()
  @ApiResponse({ status: 200, description: 'All counts are retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized ' })
  @ApiResponse({ status: 403, description: 'Forbidden user with role "Admin" can only use this end point.' })
  @ApiQuery({ name: 'orgType', description: 'Type of the org' })
  async getCountsByStatus(@Query('orgType') orgType: string) {
    return await this.orgService.getOrgsCountByCustomStatus(orgType);
  }

  @Get('find')
  @ApiOperation({ summary: 'Get list of Orgs by status', description: `This endpoint gives the list of Orgs by status. This is accessible only for "Admin"` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.Vendor)
  @Roles()
  @ApiResponse({ status: 200, description: 'All Orgs are retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized ' })
  @ApiResponse({ status: 403, description: 'Forbidden user with role "Admin" can only use this end point.' })
  @ApiQuery({ name: 'status', required: true, type: String, description: 'Status Id of Orgs' })
  async getOrgsByStatus(@Query('status') status: string, @Query('page') page: number = 1, @Query('limit') limit: number = 10) {
    if (!status) {
      throw new BadRequestException('Status query parameter is required');
    }
    const objId = validateObjectId(status);
    return await this.orgService.getOrgsByCustomStatus(objId, page, limit);
  }


  // Add a placeholder to org
  @Post('placeholders')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.Vendor)
  @Roles()
  @ApiOperation({ summary: 'Add a new placeholder to an org', description: 'This endpoint allows administrators to add a new placeholder to the organization they are associated with. This is accessible only for "Admin".' })
  @ApiResponse({ status: 201, description: 'Placeholder added successfully.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Invalid data.' })
  @ApiResponse({ status: 401, description: 'Unauthorized ' })
  @ApiResponse({ status: 403, description: `Forbidden. Only users with role "Admin" are permitted to use this endpoint.` })
  async addPlaceholder(@Req() req: any, @Body() placeholder: PlaceholderDto) {
    if (req.user.org) {
      placeholder.orgId = req.user.org._id;
    }
    return await this.orgService.addPlaceholder(placeholder);
  }

  // Update a specific placeholder by ID in an org
  @Patch('placeholders/:placeholderId')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin)
  @Roles()
  @ApiOperation({ summary: 'Update a specific placeholder in an org', description: 'This endpoint allows administrators to update the details of a specific placeholder within their organization. This is accessible only for "Admin".' })
  @ApiParam({ name: 'placeholderId', description: 'ID of the placeholder to be updated' })
  @ApiResponse({ status: 200, description: 'Placeholder updated successfully.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Invalid data.' })
  @ApiResponse({ status: 404, description: 'Placeholder not found.' })
  @ApiResponse({ status: 403, description: `Forbidden. Only users with role "Admin" are permitted to use this endpoint.` })
  @ApiResponse({ status: 401, description: 'Unauthorized ' })
  async updatePlaceholder(@Param('placeholderId') placeholderId: string, @Body() placeholder: UpdatePlaceholderDto) {
    return await this.orgService.updatePlaceholder(placeholderId, placeholder);
  }

  // Delete a placeholder by ID in an org
  @Delete('placeholders/:placeholderId')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin)
  @Roles()
  @ApiOperation({ summary: 'Delete a placeholder from an org', description: 'This endpoint allows authorized users to delete a specific placeholder associated with their organization. This is accessible only for "Admin".' })
  @ApiParam({ name: 'placeholderId', description: 'ID of the placeholder to be deleted' })
  @ApiResponse({ status: 200, description: 'Placeholder deleted successfully.' })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  @ApiResponse({ status: 401, description: 'Unauthorized ' })
  @ApiResponse({ status: 403, description: `Forbidden. Only users with role "Admin" are permitted to use this endpoint.` })
  @ApiResponse({ status: 404, description: 'Placeholder not found.' })
  async removePlaceholder(@Param('placeholderId') placeholderId: string) {
    return await this.orgService.removePlaceholder(placeholderId);
  }

  @Get('placeholders')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin)
  @Roles()
  @ApiOperation({ summary: 'Get all placeholders for an org by event name', description: 'This endpoint allows to retrieve placeholders for a specific organization, This is accessible only for "Admin".' })
  @ApiQuery({ name: 'orgId', description: 'ID of the organization', required: false })
  @ApiQuery({ name: 'emailTemplate', description: 'ID of the emailTemplate', required: false })
  @ApiQuery({ name: 'isDefault', description: 'Flag to indicate if default placeholders should be retrieved', required: false, type: Boolean })
  @ApiResponse({ status: 200, description: 'Placeholders retrieved successfully.' })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: `Forbidden. Only users with role "Admin" are permitted to use this endpoint.` })
  @ApiResponse({ status: 404, description: 'Placeholder not found.' })
  async getAllPlaceholders(@Req() req: any, @Query('orgId') orgId?: string, @Query('emailTemplate') emailTemplate?: string, @Query('isDefault') isDefault?: boolean) {
    // if (!orgId) {
    //   orgId = req.user.org._id;
    // }
    return await this.orgService.getAllPlaceholders(orgId, emailTemplate, isDefault);
  }

  @Get('placeholders/:placeholderId')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin)
  @Roles()
  @ApiOperation({ summary: 'Retrieve a placeholder by ID', description: 'This endpoint returns a placeholder by its ID. This accessible only for "Admin".' })
  @ApiResponse({ status: 200, description: 'Placeholder retrieved successfully.' })
  @ApiResponse({ status: 404, description: 'Placeholder not found.' })
  @ApiResponse({ status: 403, description: `Forbidden. Only users with role "Admin" are permitted to use this endpoint.` })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiParam({ name: 'placeholderId', description: 'ID of the placeholder.' })
  async getPlaceholder(@Param('placeholderId') placeholderId: string) {
    const objId = validateObjectId(placeholderId);
    return await this.orgService.getPlaceholder(objId);
  }

  @Patch('favourite')
  @ApiOperation({
    summary: 'Toggle favourite status for an organization',
    description: `This endpoint allows a user to add  organization to their favourites list.`,
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.AccountManager, Role.DeliveryManager, Role.Vendor)
  @Roles()
  @ApiResponse({ status: 200, description: 'Favourite status toggled successfully.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 404, description: 'Organization not found.' })
  @ApiBody({ type: AddToFavouriteOrgDto, description: 'Array of organization IDs to add to favourites' })

  toggleFavourite(@Req() req: any,
    @Body() body: AddToFavouriteOrgDto,) {
    const userId = req.user._id;
    const ids = body.orgIds.map(orgId => validateObjectId(orgId));
    return this.orgService.toggleFavourite(userId, ids);
  }

  @Get('favourites')
  @ApiOperation({ summary: 'Retrieve all favourite orgs' })
  // @ApiQuery({ name: 'page', required: false, type: Number, example: 1 })
  // @ApiQuery({ name: 'limit', required: false, type: Number, example: 10 })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.AccountManager, Role.DeliveryManager, Role.Vendor, Role.JobSeeker, Role.Freelancer, Role.Recruiter, Role.TeamLead, Role.TeamLead, Role.TechPanel, Role.TeamMember)
  @Roles()
  @ApiResponse({ status: 403, description: `Forbidden. Only users with roles "Admin", "BUHead", "ResourceManager", "AccountManager", "DeliveryManager"  are permitted to use this endpoint.` })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 404, description: 'Organization not found.' })
  @ApiResponse({ status: 200, description: 'Favourite orgs retrieved successfully.' })
  getFavourites(@Req() req: any) {
    const userId = req.user._id;
    return this.orgService.getFavouriteOrgs(userId);
  }

  @Delete('favourites')
  @ApiOperation({ summary: 'Remove orgs from user favourites' })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.AccountManager, Role.DeliveryManager, Role.Vendor, Role.JobSeeker, Role.Freelancer, Role.Recruiter, Role.TeamLead, Role.TeamLead, Role.TechPanel, Role.TeamMember)
  @Roles()
  @ApiBody({ type: AddToFavouriteOrgDto, description: 'Array of org IDs to remove from favourites' })
  @ApiResponse({ status: 200, description: 'orgs were successfully removed from favourites.' })
  @ApiResponse({ status: 400, description: 'Invalid request data or org IDs.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 403, description: `Forbidden. Only users with the "${Role.Admin}","${Role.BUHead}" role are allowed.` })
  @ApiResponse({ status: 404, description: 'One or more orgs not found in favourites.' })
  async removeFromFavourites(
    @Req() req: any,
    @Body() body: AddToFavouriteOrgDto,
  ) {
    const userId = req.user._id;
    const ids = body.orgIds.map(orgId => validateObjectId(orgId));
    return this.orgService.removeFromFavourites(userId, ids);
  }

  @Patch(':orgId/approve-reject')
  @ApiOperation({
    summary: 'Approve or Reject an Organization',
    description: `Endpoint to approve or reject an organization. Either "isApprove" or "isReject" query parameter must be true, but not both.`,
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.AccountManager, Role.Vendor)
  @Roles()
  @ApiResponse({ status: 200, description: 'Organization status updated successfully.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Invalid Parameters.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 403, description: `Forbidden. Only users with specified roles are permitted to use this endpoint.` })
  @ApiResponse({ status: 404, description: 'Organization not found.' })
  @ApiParam({ name: 'orgId', description: 'ID of the organization' })
  @ApiQuery({ name: 'isApproved', required: false, type: Boolean, description: 'Set to true to approve the organization' })
  @ApiQuery({ name: 'isRejected', required: false, type: Boolean, description: 'Set to true to reject the organization' })
  async updateOrgStatus(@Param('orgId') orgId: string, @Query('isApproved') isApproved?: boolean, @Query('isRejected') isRejected?: boolean,) {
    const objId = validateObjectId(orgId);

    if (isApproved && isRejected) {
      throw new BadRequestException('Both "isApprove" and "isReject" cannot be true simultaneously.');
    }

    const updatedOrg = await this.orgService.updateStatus(objId, isApproved, isRejected);
    return updatedOrg;
  }

  @Patch('vendor-accept-terms')
  @ApiOperation({
    summary: 'Accept Admin Terms and Conditions',
    description: `Endpoint to accept admin Terms. Either "isApprove" or "isReject" query parameter must be true, but not both.`,
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.AccountManager, Role.Vendor)
  @Roles()
  @ApiResponse({ status: 200, description: 'Organization status updated successfully.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Invalid Parameters.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 403, description: `Forbidden. Only users with specified roles are permitted to use this endpoint.` })
  @ApiResponse({ status: 404, description: 'Organization not found.' })
  @ApiQuery({ name: 'isAdminTermsAccepted', required: false, type: Boolean, description: 'Set to true to Accept the Terms and Conditions' })
  async acceptAdminTerms(@Req() req: any, @Query('isAdminTermsAccepted') isAdminTermsAccepted?: boolean) {
    const updatedOrg = await this.orgService.acceptAdminTerms(req.user, isAdminTermsAccepted);
    return updatedOrg;
  }

  @Get('findCompanyProfileDetails')
  @ApiOperation({
    summary: 'Fetch Company profile details',
    description: 'This endpoint returns Company profile details. Accessible only to users "Admin", "BUHead", "ResourceManager", "AccountManager" and "DeliveryManager".'
  })
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.AccountManager, Role.Vendor)
  @Roles()
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  @ApiResponse({ status: 200, description: 'Company profile details are retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 403, description: 'Forbidden. Only users with roles "Admin", "BUHead", "ResourceManager", "AccountManager" and "DeliveryManager" are permitted to use this endpoint.' })
  findCompanyProfileDetails() {
    return this.orgService.getCompanyProfileDetails();
  }

  @Patch(':orgId/companyProfile')
  @ApiOperation({
    summary: 'Update company profile details by Org Id',
    description: `This endpoint updates the company profile details by org Id. Accessible only to users with roles "Admin" and "BUHead".`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.Vendor)
  @Roles()
  @ApiResponse({ status: 200, description: 'Company profile details updated.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 403, description: `Forbidden. Only users with roles "Admin" and "BUHead" are permitted to use this endpoint.` })
  @ApiResponse({ status: 404, description: 'Org not found.' })
  @ApiResponse({ status: 409, description: 'Company profile name with this name already exists. ' })
  @ApiParam({ name: 'orgId', description: 'ID of the org' })
  @ApiBody({ type: UpdateCompanyProfileDto, description: 'The org details to update company profile' })
  updateCompanyProfile(@Req() req: any, @Param('orgId') orgId: string, @Body() updateCompanyProfileDto: UpdateCompanyProfileDto) {
    const objId = validateObjectId(orgId);
    return this.orgService.updateCompanyProfile(objId, updateCompanyProfileDto);
  }

  @Get('find-by-companyprofile')
  @ApiOperation({
    summary: 'Retrieve an org by company profile name',
    description: 'This endpoint retrieves an org by company profile name. Accessible only to users with roles "Admin", "BUHead", "ResourceManager", "AccountManager" and "DeliveryManager".'
  })
  // @ApiBearerAuth()
  // @UseGuards(AuthJwtGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.AccountManager)
  @ApiResponse({ status: 200, description: 'Org retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 403, description: 'Forbidden. Only users with roles "Admin", "BUHead", "ResourceManager", "AccountManager" and "DeliveryManager" are permitted to use this endpoint.' })
  @ApiResponse({ status: 404, description: 'Org not found.' })
  @ApiQuery({ name: 'companyProfile', description: 'Company Profile of the org' })
  findByCompanyProfile(@Query('companyProfile') companyProfile: string) {
    return this.orgService.findByCompanyProfile(companyProfile);
  }

  @Get('clients')
  @ApiOperation({
    summary: 'Retrieve all organizations with type "customer-org"',
    description: 'Job seekers can retrieve all organizations of type "customer-org". No additional filters are applied.'
  })
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.JobSeeker)
  @Roles()
  @ApiBearerAuth()
  @ApiResponse({ status: 200, description: 'List of customer organizations retrieved successfully.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 403, description: 'Forbidden. Only users with the JobSeeker role can access this endpoint.' })
  async getCustomerOrgs() {
    return this.orgService.getCustomerOrgs();
  }

  @Get('find/all')
  @ApiOperation({
    summary: 'Filter orgs by type and return orgs',
    description: 'This endpoint filters orgs by type and returns orgs of each type. Accessible only to users "Admin", "BUHead", "ResourceManager", "AccountManager" and "DeliveryManager".'
  })
  // @Roles(Role.Admin)
  @Roles()
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  @ApiResponse({ status: 200, description: 'Filtered orgs and their counts are retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 403, description: 'Forbidden. Only users with roles "Admin", "BUHead", "ResourceManager", "AccountManager" and "DeliveryManager" are permitted to use this endpoint.' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number', example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of items per page', example: 10 })
  @ApiQuery({ name: 'orgType', description: 'Type of the org' })
  findAllOrgsByOrgType(@Query('page') page: number = 1, @Query('limit') limit: number = 10, @Query('orgType') orgType: string) {
    return this.orgService.findAllOrgsByOrgType(page, limit, orgType);
  }

  @Get(':orgId/country-state')
  @ApiOperation({
    summary: 'Retrieve an org by Id',
    description: 'This endpoint retrieves an org country and state by its Id. Accessible only all'
  })
  @ApiResponse({ status: 200, description: 'Org retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 404, description: 'Org not found.' })
  findOrgCountryState(@Param('orgId') orgId: string) {
    const objId = validateObjectId(orgId);
    return this.orgService.findOrgCountryState(objId);
  }

  @Get(':orgId/members-contacts')
  @ApiOperation({
    summary: 'Retrieve list of members of an org',
    description: `Retrieves list of members with roles to org.Accessible only to users with roles "Admin", "BUHead", "ResourceManager", "DeliveryManager", "AccountManager" and "TeamLead".`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.AccountManager, Role.TeamLead, Role.Vendor)
  @Roles()
  @ApiResponse({ status: 200, description: 'Members retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 403, description: 'Forbidden. Only users with roles "Admin", "BUHead", "ResourceManager", "DeliveryManager", "AccountManager" and "TeamLead" are permitted to use this endpoint.' })
  @ApiResponse({ status: 404, description: 'Org not found.' })
  @ApiQuery({ name: 'searchTerm', required: false, description: 'search members by name and email' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number', example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of items per page', example: 10 })
  @ApiQuery({ name: 'departmentTreeId', required: false, type: String, description: 'ID of the department tree to filter members by' })
  getAllInternalContactsByOrg(@Param('orgId') orgId: string,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
    @Query('searchTerm') searchTerm?: string,
    @Query('departmentTreeId') departmentTreeId?: string
  ) {
    const org = validateObjectId(orgId);
    const departmentIdObj = departmentTreeId ? validateObjectId(departmentTreeId) : undefined;
    return this.orgService.getAllInternalContactsByOrg(org, page, limit, searchTerm, departmentIdObj);
  }

  @Get('filterByRole/org-type')
  @ApiOperation({
    summary: 'Filter orgs by type and return orgs',
    description: 'This endpoint filters orgs by type and returns orgs of each type. Accessible only to users "Admin", "BUHead", "ResourceManager", "AccountManager" and "DeliveryManager".'
  })
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.AccountManager, Role.Vendor, Role.JobSeeker)
  @Roles()
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  @ApiResponse({ status: 200, description: 'Filtered orgs and their counts are retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 403, description: 'Forbidden. Only users with roles "Admin", "BUHead", "ResourceManager", "AccountManager" and "DeliveryManager" are permitted to use this endpoint.' })
  // @ApiQuery({ name: 'role', required: false, type: String, description: 'Number of items per page', example: 'account_manager' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number', example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of items per page', example: 10 })
  @ApiQuery({ name: 'assignTo', required: false, type: String, description: 'User Id of the user' })

  filterOrgsByTypeForAcc_Manager(@Req() req: any, @Query() query: OrgTypeFilterDto, @Query('page') page: number = 1, @Query('limit') limit: number = 10, @Query('assignTo') assignTo: string) {
    let userId = req.user._id;
    if (req.user.orgAdmin) {
      userId = req.user.orgAdmin
    }
    const { orgType } = query;
    return this.orgService.filterOrgsByTypeForAcc_Manager(orgType, page, limit, userId, assignTo);
  }

  @Get('filterOrgs/org-type')
  @ApiOperation({
    summary: 'Filter orgs by type and return orgs',
    description: 'This endpoint filters orgs by type and returns orgs of each type. Accessible only to users "Admin", "BUHead", "ResourceManager", "AccountManager" and "DeliveryManager".'
  })
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.AccountManager, Role.Vendor, Role.JobSeeker)
  @Roles()
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  @ApiResponse({ status: 200, description: 'Filtered orgs and their counts are retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 403, description: 'Forbidden. Only users with roles "Admin", "BUHead", "ResourceManager", "AccountManager" and "DeliveryManager" are permitted to use this endpoint.' })
  // @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number', example: 1 })
  // @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of items per page', example: 10 })
  filterOrgsByTypeReq(@Req() req: any) {
    let userId = req.user._id;
    // if (req.user.orgAdmin) {
    //   userId = req.user.orgAdmin
    // }
    // const { orgType } = query;
    return this.orgService.filterOrgsByTypeReq(req.user);
  }

  @Patch(':orgId/client-onboard')
  @ApiOperation({
    summary: 'Onboard a client (update an account to client)',
    description: 'This endpoint updates an account to client.',
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  @ApiParam({ name: 'orgId', description: 'ID of the organization to onboard as client' })
  @ApiBody({ type: ClientOnboardingDto, description: 'Client address and GST details' })
  @ApiResponse({ status: 200, description: 'Organization onboarded as client successfully.' })
  @ApiResponse({ status: 400, description: 'Invalid input.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 404, description: 'Organization not found.' })
  async onboardClient(@Req() req: any, @Param('orgId') orgId: string, @Body() clientOnboardingDto: ClientOnboardingDto) {
    try {
      validateObjectId(orgId);
      const user = req.user;

      const result = await this.orgService.onboardClient(orgId, clientOnboardingDto, user);

      return result;
    } catch (error) {
      throw error;
    }
  }

  @Patch(':orgId/invoice-details')
  @ApiOperation({
    summary: 'Update invoice and payment settings of an organization',
    description: 'This endpoint updates invoice and payment duration details for the organization.'
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  @ApiParam({ name: 'orgId', description: 'ID of the organization to update' })
  @ApiBody({ type: UpdateInvoiceDetailsDto })
  @ApiResponse({ status: 200, description: 'Invoice details updated successfully.' })
  @ApiResponse({ status: 400, description: 'Invalid input.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 404, description: 'Organization not found.' })
  async updateInvoiceDetails(@Req() req: any, @Param('orgId') orgId: string, @Body() dto: UpdateInvoiceDetailsDto) {
    try {
      validateObjectId(orgId);
      const user = req.user;
      return await this.orgService.updateInvoiceDetails(orgId, dto, user);
    } catch (error) {
      throw error;
    }
  }

  @Patch(':orgId/bgvHandler/:bgvHandlerId')
  @ApiOperation({
    summary: 'Updates a bgvHandler by id',
    description: `Updates a bgvHandler by id  org. Accessible only to users with role "Admin".`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.Vendor)
  @Roles()
  @ApiResponse({ status: 200, description: 'Member updated successfully.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 403, description: `Forbidden. Only users with role "Admin" are permitted to use this endpoint.` })
  @ApiResponse({ status: 404, description: 'Org not found.' })
  @ApiParam({ name: 'orgId', description: 'ID of the org' })
  @ApiParam({ name: 'bgvHandlerId', description: 'ID of the org' })
  async updateBgvhandlerForOrg(@Req() req: any, @Param('orgId') orgId: string, @Param('bgvHandlerId') bgvHandlerId: string, @Body() updateMemberDto: UpdateMemberDto) {
    const org = validateObjectId(orgId);
    return this.orgService.updateBgvhandler(org, bgvHandlerId, req.user._id);
  }

  @Patch(':orgId/invoice/addressDetails')
  @ApiOperation({
    summary: 'Suspends an org by Id',
    description: `This endpoint suspends an org by Id. Accessible only to users with role "Admin".`
  })
  @ApiBearerAuth()
  // @UseGuards(AuthJwtGuard, RolesGuard, OrgAccessGuard)
  @UseGuards(AuthJwtGuard)
  // @Roles(Role.Admin, Role.Vendor)
  @Roles()
  @ApiResponse({ status: 201, description: 'Org is suspended.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: `Forbidden. Only users with role "Admin" are permitted to use this endpoint.` })
  @ApiResponse({ status: 404, description: 'Org not found.' })
  @ApiParam({ name: 'orgId', description: 'ID of the org' })
  saveInvoiceAddressDetails(@Param('orgId') orgId: string, @Body() updateAddressDetailsDto: UpdateAddressDetailsDto) {
    // const objId = validateObjectId(orgId);
    return this.orgService.updateAddressDetails(orgId, updateAddressDetailsDto);
  }

  @Patch('hikeSettings/save')
  @ApiOperation({
    summary: 'Suspends an org by Id',
    description: `This endpoint suspends an org by Id. Accessible only to users with role "Admin".`
  })
  @ApiBearerAuth()
  // @UseGuards(AuthJwtGuard, RolesGuard, OrgAccessGuard)
  @UseGuards(AuthJwtGuard)
  // @Roles(Role.Admin, Role.Vendor)
  @Roles()
  @ApiResponse({ status: 201, description: 'Org is suspended.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: `Forbidden. Only users with role "Admin" are permitted to use this endpoint.` })
  @ApiResponse({ status: 404, description: 'Org not found.' })
  saveHikeSettingsDetails(@Body() updateHikeSettingsDto: UpdateHikeSettingsDto) {
    // const objId = validateObjectId(orgId);
    return this.orgService.updateHikeSettings(updateHikeSettingsDto);
  }


  @Patch(':orgId/invoice/bank-details')
  @ApiOperation({
    summary: 'Suspends an org by Id',
    description: `This endpoint suspends an org by Id. Accessible only to users with role "Admin".`
  })
  @ApiBearerAuth()
  // @UseGuards(AuthJwtGuard, RolesGuard, OrgAccessGuard)
  @UseGuards(AuthJwtGuard)
  // @Roles(Role.Admin, Role.Vendor)
  @Roles()
  @ApiResponse({ status: 201, description: 'Org is suspended.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: `Forbidden. Only users with role "Admin" are permitted to use this endpoint.` })
  @ApiResponse({ status: 404, description: 'Org not found.' })
  @ApiParam({ name: 'orgId', description: 'ID of the org' })
  saveInvoiceBankDetails(@Param('orgId') orgId: string, @Body() updateAddressDetailsDto: UpdateBankDetailsDto) {
    // const objId = validateObjectId(orgId);
    return this.orgService.updateBankDetails(orgId, updateAddressDetailsDto);
  }

  @Get('hike-settings')
  @ApiOperation({
    summary: 'Get Hike Settings by orgs and departmentId by type and return orgs',
    description: 'This endpoint filters orgs by type and returns orgs of each type. Accessible only to users "Admin", "BUHead", "ResourceManager", "AccountManager" and "DeliveryManager".'
  })
  @Roles()
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  @ApiResponse({ status: 200, description: 'Filtered orgs and their counts are retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 403, description: 'Forbidden. Only users with roles "Admin", "BUHead", "ResourceManager", "AccountManager" and "DeliveryManager" are permitted to use this endpoint.' })
  @ApiQuery({ name: 'orgId', description: 'ID of the organization', required: false })
  @ApiQuery({ name: 'departmentId', description: 'ID of the department', required: false })
 
  getHikeSettings(@Req() req: any,@Query('orgId') orgId?: string,@Query('departmentId') departmentId?: string) {
    return this.orgService.getHikeSettings(orgId,departmentId);
  }

  // @Patch('bulkupdateAll')
  // async bulkUpdatecretedByOrgs() {
  //   try {
  //     return await this.orgService.updateCreatedByOrgFromUserModel();
  //   } catch (error) {
  //     throw error;
  //   }
  // }

}