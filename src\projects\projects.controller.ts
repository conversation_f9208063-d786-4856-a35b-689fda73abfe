import { Body, Controller, Delete, Get, Param, Patch, Post, Query, Req, UseGuards } from '@nestjs/common';
import { ProjectsService } from './projects.service';
import { CreateProjectDto } from './dto/create-project.dto';
import { AllocateUsersDto, ProjectAllocationDto } from './dto/project-allocation.dto';
import { ApiBearerAuth, ApiQuery, ApiTags } from '@nestjs/swagger';
import { AuthJwtGuard } from 'src/auth/guards/auth-jwt/auth-jwt.guard';
import { QueryProjectDto } from './dto/query-project.dto';

@Controller('')
@ApiTags('projects')
export class ProjectsController {
    constructor(private readonly service: ProjectsService) { }

    @Post()
    @ApiBearerAuth()
    @UseGuards(AuthJwtGuard)
    create(@Body() dto: CreateProjectDto) {
        return this.service.createProject(dto);
    }

    @Get()
    @ApiBearerAuth()
    @UseGuards(AuthJwtGuard)
    findAll(@Req() req: any, @Query() query: QueryProjectDto) {
        return this.service.getAllProjects(req.user, query);
    }

    @Get(':id')
    @ApiBearerAuth()
    @UseGuards(AuthJwtGuard)
    findOne(@Param('id') id: string) {
        return this.service.getProjectById(id);
    }

    @Patch(':id')
    @ApiBearerAuth()
    @UseGuards(AuthJwtGuard)
    update(@Param('id') id: string, @Body() dto: Partial<CreateProjectDto>) {
        return this.service.updateProject(id, dto);
    }

    @Delete(':id')
    @ApiBearerAuth()
    @UseGuards(AuthJwtGuard)
    remove(@Param('id') id: string) {
        return this.service.deleteProject(id);
    }

    @Post('allocate')
    @ApiBearerAuth()
    @UseGuards(AuthJwtGuard)
    allocate(@Body() dto: AllocateUsersDto) {
        return this.service.allocateUsers(dto);
    }

    @Get('/allocations/by-user')
    @ApiBearerAuth()
    @UseGuards(AuthJwtGuard)
    allocationsByUser(@Query('userId') userId: string) {
        return this.service.getAllocationsByUser(userId);
    }

    @Get('/allocations/by-project')
    @ApiBearerAuth()
    @UseGuards(AuthJwtGuard)
    allocationsByProject(@Query('projectId') projectId: string) {
        return this.service.getUsersByProject(projectId);
    }

    @Patch('/allocations/:id')
    @ApiBearerAuth()
    @UseGuards(AuthJwtGuard)
    updateAllocation(@Param('id') id: string, @Body() dto: Partial<ProjectAllocationDto>) {
        return this.service.updateAllocation(id, dto);
    }

    @Delete('/allocations/:id')
    @ApiBearerAuth()
    @UseGuards(AuthJwtGuard)
    deleteAllocation(@Param('id') id: string) {
        return this.service.deleteAllocation(id);
    }

    @Get('/employees/:id')
    @ApiBearerAuth()
    @UseGuards(AuthJwtGuard)
    findAllEmployees(@Req() req: any, @Param('id') id: string) {
        return this.service.findAllEmployeesByProject(req.user, id);
    }

    @Get('employees/client')
    @ApiBearerAuth()
    @UseGuards(AuthJwtGuard)
    @ApiQuery({ name: 'clientId', required: false })

    findAllEmployeesByClient(@Req() req: any,@Query('clientId') clientId?: string,) {
        return this.service.findAllEmployeesByClient(req.user, clientId);
    }
}