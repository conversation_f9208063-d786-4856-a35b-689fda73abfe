import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type LeavePolicyDocument = LeavePolicy & Document;


@Schema({ timestamps: true })
class LeaveType {
    @Prop({ required: true })
    name: string;

    @Prop({ required: true })
    days: number;

    @Prop({ enum: ['carryForward', 'zero', 'optional'], required: true })
    endOfYear: string;
}

@Schema({ timestamps: true })
class LeaveRequest {
    @Prop({ type: Types.ObjectId, required: true, ref: 'BasicUser' })
    requestedBy: Types.ObjectId;

    @Prop({ required: true })
    leaveType: string;

    @Prop({ required: true })
    fromDate: Date;

    @Prop({ required: true })
    toDate: Date;

    @Prop({ required: true })
    numberOfDays: number;

    @Prop()
    reason: string;

    @Prop({ enum: ['Pending', 'Approved', 'Rejected'], default: 'Pending' })
    status: string;

    @Prop({ type: Types.ObjectId, ref: 'BasicUser' })
    approvedBy?: Types.ObjectId;
}

@Schema({ timestamps: true })
export class LeavePolicy {
    @Prop({ required: true })
    orgId: Types.ObjectId;

    @Prop({ required: true })
    planName: string;

    @Prop({ type: [LeaveType], default: [] })
    leaveTypes: LeaveType[];

    @Prop({ type: [LeaveRequest], default: [] })
    leaveRequests: LeaveRequest[];
}

export const LeavePolicySchema = SchemaFactory.createForClass(LeavePolicy);
