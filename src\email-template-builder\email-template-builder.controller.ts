import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Req, Query, BadRequestException } from '@nestjs/common';
import { EmailTemplateBuilderService } from './email-template-builder.service';
import { CreateEmailTemplateDto } from './dto/create-email-template-builder.dto';
import { UpdateEmailTemplateDto } from './dto/update-email-template-builder.dto';
import { ApiBearerAuth, ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { AuthJwtGuard } from 'src/auth/guards/auth-jwt/auth-jwt.guard';
import { Roles } from 'src/auth/decorators/roles.decorator';
import { Role } from 'src/auth/enums/role.enum';
import { RolesGuard } from 'src/auth/guards/roles/roles.guard';
import { validateObjectId } from 'src/utils/validation.utils';
import { QueryEmailTemplateDto } from './dto/query-email.dto';
import { PlaceholderDto } from '../org/dto/placeholder.dto';

@Controller('')
@ApiTags('Email-Template-builder')
export class EmailTemplateBuilderController {
  constructor(private readonly emailTemplateBuilderService: EmailTemplateBuilderService) { }

  @Post()
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin)
  @Roles()
  @ApiOperation({ summary: 'Create an email template', description: 'This endpoint allows "Admin" to create an email template.' })
  @ApiResponse({ status: 201, description: 'Email template is created successfully.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Invalid data.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden, only "Admin" can access this endpoint.' })
  create(@Req() req: any, @Body() createEmailTemplateDto: CreateEmailTemplateDto) {
    createEmailTemplateDto.createdBy = req.user._id;
    return this.emailTemplateBuilderService.create(createEmailTemplateDto);
  }

  @Post('placeholders')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin)
  @Roles()
  @ApiOperation({ summary: 'Create an email template', description: 'This endpoint allows "Admin" to create an email template.' })
  @ApiResponse({ status: 201, description: 'Email template is created successfully.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Invalid data.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden, only "Admin" can access this endpoint.' })
  createEmailTemplate(@Req() req: any, @Body() createEmailTemplateDto: CreateEmailTemplateDto) {
    createEmailTemplateDto.createdBy = req.user._id;
    if (req.user.org) {
      createEmailTemplateDto.org = req.user.org._id;
    }
    return this.emailTemplateBuilderService.createEmailTemplate(createEmailTemplateDto,req?.user);
  }

  @Get('all')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin)
  @Roles()
  @ApiOperation({
    summary: 'Retrieve all email templates with pagination and search',
    description: 'This endpoint returns a list of email templates. Accessible by "Admin" with pagination and search support.',
  })
  @ApiResponse({ status: 200, description: 'Email templates retrieved successfully.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role "Admin" can only use this endpoint.' })
  findAll(@Req() req: any, @Query() query: QueryEmailTemplateDto) {
    const { page = 1, limit = 10, templateName } = query;
    return this.emailTemplateBuilderService.findAll(page, limit, req.user, templateName);
  }

  @Get(':templateId')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin)
  @Roles()
  @ApiOperation({ summary: 'Retrieve an email template by ID', description: 'This endpoint returns an email template by its ID. Accessible by "Admin" with pagination and search support.' })
  @ApiResponse({ status: 200, description: 'Email template retrieved successfully.' })
  @ApiResponse({ status: 404, description: 'Email template not found.' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role "Admin" can only use this endpoint.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiParam({ name: 'templateId', description: 'ID of the email template.' })
  findOne(@Param('templateId') templateId: string) {
    const objId = validateObjectId(templateId);
    return this.emailTemplateBuilderService.findById(objId);
  }

  @Patch(':templateId')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin)
  @Roles()
  @ApiOperation({ summary: 'Update an email template by ID', description: 'This endpoint allows admin to update an email template by ID.Accessible by "Admin" with pagination and search support.' })
  @ApiResponse({ status: 200, description: 'Email template updated successfully.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden, only "Admin" can access this endpoint.' })
  @ApiParam({ name: 'templateId', description: 'ID of the email template.' })
  update(@Req() req: any,@Param('templateId') templateId: string, @Body() updateEmailTemplateDto: UpdateEmailTemplateDto) {
    const objId = validateObjectId(templateId);
    return this.emailTemplateBuilderService.update(objId, updateEmailTemplateDto,req.user);
  }

  @Delete(':templateId/hard-delete')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin)
  @Roles()
  @ApiOperation({ summary: 'Delete an email template by ID', description: 'This endpoint allows "Admin" to delete an email template by ID.' })
  @ApiResponse({ status: 200, description: 'Email template deleted successfully.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden, only "Admin" can access this endpoint.' })
  @ApiParam({ name: 'templateId', description: 'ID of the email template.' })
  remove(@Param('templateId') templateId: string) {
    const objId = validateObjectId(templateId);
    return this.emailTemplateBuilderService.remove(objId);
  }

  @Delete(':templateId/soft-delete')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin)
  @Roles()
  @ApiOperation({ summary: 'Soft delete an email template by ID', description: 'This endpoint soft deletes an email template by its ID.This endpoint allows "Admin" to delete an email template by ID.' })
  @ApiResponse({ status: 200, description: 'Email template is soft deleted.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden, only "Admin" can access this endpoint.' })
  @ApiParam({ name: 'templateId', description: 'ID of the email template' })
  softDelete(@Req() req: any,@Param('templateId') templateId: string) {
    const objId = validateObjectId(templateId);
    return this.emailTemplateBuilderService.delete(objId, req.user);
  }

  @Delete('bulk-delete')
  @ApiOperation({
    summary: 'Soft delete multiple email templates',
    description: `This endpoint soft deletes multiple email templates by their IDs. This endpoint allows "Admin" to delete an email template by ID.`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin)
  @Roles()
  @ApiResponse({ status: 200, description: 'Email templates have been soft deleted.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden, only "Admin" can access this endpoint.' })
  @ApiResponse({ status: 404, description: 'No email templates found for the given IDs.' })
  @ApiResponse({ status: 400, description: 'Bad request. Template IDs are required.' })
  async bulkSoftDelete(@Body() templateIds: string[]): Promise<{ message: string }> {
    if (templateIds && templateIds.length > 0) {
      const validTemplateIds = templateIds.map((templateId: string) => validateObjectId(templateId));

      const result = await this.emailTemplateBuilderService.bulkDelete(validTemplateIds);

      if (result.modifiedCount > 0) {
        return { message: `Successfully soft deleted ${result.modifiedCount} email templates.` };
      } else {
        return { message: 'No email templates were soft deleted.' };
      }
    } else {
      throw new BadRequestException('Template IDs are required for bulk soft delete.');
    }
  }

  @Get('templates-count')
  @ApiResponse({ status: 200, description: `Email template count is retrieved.` })
  @ApiResponse({ status: 404, description: `Unable to find email template count.` })
  @ApiResponse({ status: 401, description: `Unauthorized.` })
  @ApiResponse({ status: 403, description: `Forbidden, only users with roles "Admin" can access this endpoint.` })
  @ApiOperation({ summary: `Retrieve email template count`, description: `Returns the count of email templates. This endpoint allows "Admin" to delete an email template by ID.` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin)
  @Roles()
  getEmailTemplatesCount(@Req() req: any) {
    return this.emailTemplateBuilderService.getEmailTemplatesCount(req.user);
  }
}
