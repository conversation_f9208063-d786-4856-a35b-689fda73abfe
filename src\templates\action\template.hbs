<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="x-apple-disable-message-reformatting">
    <title>{{subject}}</title>
    {{!--
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;700&display=swap" rel="stylesheet"> --}}
    <style>
        body {
            margin: 0;
            padding: 0 !important;
            background-color: #f1f1f1;
            font-family: 'Roboto', Arial, sans-serif;
            color: #333333;
            width: 100% !important;
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
        }

        .email-container {
            width: 100% !important;
            margin: 0 auto;
            padding: 1.5em;
            background: rgba(255, 255, 255, 0.7);
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .header {
            text-align: center;
            padding: 1.5em;
            background-color: rgb(0, 128, 128);
            color: #ffffff;
            border-top-left-radius: 10px;
            border-top-right-radius: 10px;
        }

        .header h1,
        .header p {
            margin: 0;
        }

        .logo {
            max-width: 150px;
            margin: 0 auto 5px;
        }

        .tagline {}

        .mailbox {
            max-width: 150px;
            margin: 0 auto 10px;
        }

        .content {
            padding: 1.5em;
            text-align: center;
        }

        .content h2 {
            margin: 0 0 1em;
            font-size: 26px;
            color: rgb(232, 105, 70);
        }

        .content h4 {
            margin: 0 0 1em;
            font-size: 20px;
        }

        .content p {
            margin: 1em 0;
            font-size: 18px;
        }

        .content {
            padding: 1.5em;
            text-align: left;
            /* Align content to the left */
        }

        .content h2 {
            text-align: center;
            /* Keep subject heading centered */
        }

        .header {
            text-align: center;
            /* Align header to the left */
        }

        .btn-primary {
            display: inline-block;
            margin: 20px 0;
            padding: 12px 24px;
            font-size: 18px;
            color: #ffffff;
            background-color: rgb(232, 105, 70);
            text-decoration: none;
            border-radius: 5px;
        }

        .footer {
            width: 100% !important;
            padding: 1.5em;
            background-color: rgba(241, 241, 241, 0.8);
            text-align: center;
            color: #888888;
            border-bottom-left-radius: 10px;
            border-bottom-right-radius: 10px;
        }

         .footer-box {
            width: 100% !important;
            padding: 1.5em;
            background-color: rgba(241, 241, 241, 0.8);
            text-align: center;
            color: #888888;
            border-bottom-left-radius: 10px;
            border-bottom-right-radius: 10px;
        }

        .footer a {
            color: rgb(232, 105, 70);
            text-decoration: none;
            display: inline-block;
            /* Keeps links readable */
            word-break: break-word;
        }

        .footer ul {
            list-style: none;
            padding: 0;
        }

        .footer li {
            margin: 0.5em 0;
        }

        .footer-row {
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;
            margin-top: 20px;
            text-align: center;
        }

        .box {
            overflow: hidden;
            /* Prevents text overflow */
            text-overflow: ellipsis;
            /* Adds "..." if text overflows */
            font-size: clamp(12px, 2vw, 18px);
            /* Responsive font size */
            padding: 20px;
            background-color: #f0f0f0;
            border: 1px solid #ccc;
            box-sizing: border-box;
            white-space: nowrap;
            /* Prevent text breaking on larger screens */
        }

        .box:first-child {
            flex: 1;
            /* Smaller width */
        }

        /* Keep other two boxes larger */
        .box:nth-child(2),
        .box:nth-child(3) {
            flex: 2;
        }

        .footer div {
            flex: 1;
            margin-bottom: 1em;
        }

        .footer-row div:last-child {
            margin-bottom: 1em;
            /* Remove bottom margin for the last div */
        }

        .footer-row h3 {
            margin-bottom: 10px;
        }

        .footer-row ul {
            list-style: none;
            padding: 0;
        }

        .footer-row ul li {
            margin-bottom: 5px;
        }

        .footer-row ul li a {
            text-decoration: none;
            color: #333;
            /* Optional link color */
        }

        .email-container {
            /* Ensures responsiveness */
            width: 100% !important;
            /* Forces full width */
            margin: 0 auto;
            /* Centers the box */
            padding: 1.5em;
            background: #ffffff;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            box-sizing: border-box;
        }

        @media screen and (max-width: 767px) {
            .email-container {
                padding: 1em;
            }

            .content,
            .footer {
                padding: 1em;
            }

            .header {
                padding: 1em;
            }

            .content h2 {
                font-size: 22px;
            }

            .content h4 {
                font-size: 18px;
            }

            .content p {
                font-size: 16px;
            }

            .btn-primary {
                padding: 10px 20px;
                font-size: 16px;
            }

            .footer-row {
                flex-direction: column;
                align-items: center;
            }

            .box {
                width: 100%;
                margin-bottom: 10px;
                /* Full width for small devices */
            }

            .footer div {
                width: 100%;
                max-width: none;
                margin-bottom: 10px;
            }

            .box {
                white-space: normal;
                /* Allows text to wrap */
                word-wrap: break-word;
                /* Breaks long words */
                overflow-wrap: break-word;
                /* Ensures wrapping in all cases */
                text-overflow: clip;
                /* Removes "..." in responsive mode */
            }
        }

        @media screen and (max-width: 400px) {
            .content h2 {
                font-size: 20px;
            }

            .content h4 {
                font-size: 16px;
            }

            .content p {
                font-size: 14px;
            }

            .btn-primary {
                padding: 8px 16px;
                font-size: 14px;
            }
        }
    </style>
</head>

<body>
    <div style="width: 100%; background-color: #f1f1f1;">
        <div class="email-container">
            <!-- BEGIN BODY -->
            <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
                <tr>
                    <td class="header">
                        {{!-- <p><img src="cid:logo" alt={{companyName}} class="logo"></p> --}}
                        <p><img src="cid:logo" class="logo" style="display: block; margin: 0 auto;"></p>
                        {{!-- <p class="tagline">{{productTagline}}</p> --}}
                        <p class="tagline"></p>
                    </td>
                </tr>
                <tr>
                    <td class="content">
                        <h2>{{subject}}</h2>
                        {{!-- <div class="mailbox"> --}}
                            {{!-- <p><img src="cid:mailbox" alt="mailbox" class="logo"
                                    style="max-width: 40px; height: auto;">
                            </p> --}}

                            {{!--
                        </div> --}}
                        <h4>Dear {{recipient}},</h4>
                        <p>{{{message}}}</p>
                        <p>{{{info}}}</p>
                        {{!-- <p><a href="{{callToActionLink}}" class="btn-primary">{{buttonText}}</a></p> --}}
                        {{#if otpCode}}
                        <p>Your OTP is: <strong>{{otpCode}}</strong></p>
                        {{/if}}
                        {{#if password}}
                        <p>Your Default Password is: <strong>{{password}}</strong></p>
                        {{/if}}
                        {{#if callToActionLink}}
                        <p style="text-align: center;"><a href="{{callToActionLink}}"
                                class="btn-primary">{{buttonText}}</a></p>
                        {{/if}}
                    </td>
                </tr>
                <tr class="footer-box">
                    <td class="footer">
                        <div class="footer-row">
                            <div class="box">
                                <h3>{{productName}}</h3>
                                <p>{{productTagline}}</p>
                            </div>
                            <div class="box">
                                <h3>Contact Info</h3>
                                <ul>
                                    <li><a href="{{companyWebsiteUrl}}">{{companyWebsiteUrl}}</a></li>
                                    <li><a href="mailto:{{companyContactEmail}}">{{companyContactEmail}}</a></li>
                                </ul>
                            </div>
                            <div class="box">
                                <h3>Quick Links</h3>
                                <ul>
                                    <li><a href="{{productWebsiteUrl}}">{{productName}}</a></li>
                                    <li><a href="{{companyWebsiteUrl}}">{{companyName}}</a></li>
                                </ul>
                            </div>
                        </div>
                        <p> {{companyAddress1}}<br>{{companyAddress2}}</p>
                        {{!-- <p> {{companyAddress2}}</p> --}}
                        <p>&copy; {{year}} {{companyName}} | All rights reserved.</p>
                    </td>
                </tr>
            </table>
        </div>
        </center>
</body>

</html>