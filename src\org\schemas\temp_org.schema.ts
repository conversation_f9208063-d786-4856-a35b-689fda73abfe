import { SchemaFactory, Prop, Schema } from '@nestjs/mongoose';
import { HydratedDocument, Types } from 'mongoose';
import { Org } from './org.schema'; // Import the Org schema if they share structure

export type TempOrgDocument = HydratedDocument<TempOrg>;

// Extend the Org schema for TempOrg if needed
@Schema({ collection: 'temp_org', timestamps: true }) // Specify a custom collection name
export class TempOrg extends Org {
  @Prop({ default: true })
  isTemporary?: boolean; // Additional property to differentiate temp orgs
}

export const TempOrgSchema = SchemaFactory.createForClass(TempOrg);
