
import { Transform } from 'class-transformer';
import { IsEnum, IsOptional, IsString } from 'class-validator';
import { OrgType } from 'src/shared/constants';

export class QueryOrgDto {

  @IsOptional()
  @IsString()
  @Transform(({ value }) => (value === undefined || value === '0' ? '' : value))
  title?: string;

  @IsOptional()
  @Transform(({ value }) => {
    return value === undefined || value === '0' ? '' : value;
  })
  industryId?: string;

  @IsOptional()
  // @IsString()
  @Transform(({ value }) => {
    return value === undefined || value === '0' ? '' : value;
  })
  countryId?: string;

  @IsOptional()
  @Transform(({ value }) => {
    return value === undefined || value === '0' ? '' : value;
  })
  accountTypeId?: string;

  @IsOptional()
  @IsEnum(OrgType)
  @Transform(({ value }) => (value === undefined || value === '0' ? '' : value))
  orgType?: OrgType;

  @IsOptional()
  @Transform(({ value }) => {
    return value > 0 ? value : 1;
  })
  page: number = 1;


  @IsOptional()
  @Transform(({ value }) => {
    return value > 0 ? value : 10;
  })
  limit: number = 10;
}