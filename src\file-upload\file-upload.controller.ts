import { Controller, UploadedFile, UseInterceptors, UploadedFiles, Logger, Get, Param, UseGuards, Res, Post, ParseFilePipe, FileTypeValidator, HttpStatus, MaxFileSizeValidator, NotFoundException, Delete, Req, StreamableFile, HttpException, Patch, Body, Query } from '@nestjs/common';
import { FileUploadService } from './file-upload.service';
import { ApiOperation, ApiConsumes, ApiBody, ApiTags, ApiBearerAuth, ApiParam, ApiResponse, ApiQuery } from '@nestjs/swagger';
import { FileInterceptor, File, FilesInterceptor } from '@nest-lab/fastify-multer';
import { AuthJwtGuard } from 'src/auth/guards/auth-jwt/auth-jwt.guard';
import { validateObjectId } from 'src/utils/validation.utils';
import { FastifyReply } from 'fastify';
import { UpdateStatusDto } from './dto/update-status.dto';
import { RolesGuard } from 'src/auth/guards/roles/roles.guard';
import { Role } from 'src/auth/enums/role.enum';
import { Roles } from 'src/auth/decorators/roles.decorator';

@Controller('')
@ApiTags('File-Uploads')
export class FileUploadController {
  private readonly logger = new Logger(FileUploadController.name);

  constructor(private readonly fileUploadService: FileUploadService) { }

  @Post("/")
  @ApiOperation({ summary: "Uploads a single file" })
  @ApiConsumes("multipart/form-data")
  @UseInterceptors(FileInterceptor("file"))
  // @ApiBearerAuth()
  // @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager, Role.Vendor)
  @ApiOperation({ summary: 'Upload a file', description: `This endpoint uploads a file. This is accessible for all` })
  @ApiResponse({ status: 200, description: 'File is uploaded.' })
  @ApiResponse({ status: 400, description: 'Bad Request: No file provided.' })
  @ApiResponse({ status: 422, description: 'Unprocessable Entity: Invalid file type or size.' })
  @ApiBody({
    required: true,
    schema: {
      type: "object",
      properties: {
        file: {
          type: "string",
          format: "binary",
        }
      }
    }
  })
  @ApiQuery({ name: 'identifierId', required: false, type: String, description: 'file identifier' })
  async singleFile(
    @UploadedFile(
      new ParseFilePipe({
        validators: [
          new MaxFileSizeValidator({ maxSize: ******** }),  // 10MB limit
          new FileTypeValidator({ fileType: 'audio/mpeg|audio/wav|audio/ogg|image/jpeg|image/png|application/pdf|application/msword|application/vnd.openxmlformats-officedocument.wordprocessingml.document|application/vnd.ms-excel|application/vnd.openxmlformats-officedocument.spreadsheetml.sheet|text/plain|application/json|image/webp|video/webm|video/x-ms-wmv|video/mp4|video/x-msvideo|video/quicktime|video/x-matroska|application/vnd.ms-powerpoint|application/vnd.openxmlformats-officedocument.presentationml.presentation|text/csv' })
        ],
        errorHttpStatusCode: HttpStatus.UNPROCESSABLE_ENTITY,
        fileIsRequired: true
      })
    )
    file: File,
    @Req() req: any,
    @Query('identifierId') identifierId?: string,
  ) {
    if (!file) {
      throw new Error('No file provided.');
    }
    if (identifierId) {
      const identifierObjId = validateObjectId(identifierId);
      return this.fileUploadService.saveFile(file, req, identifierObjId);
    }
    return this.fileUploadService.saveFile(file, req);
  }

  @Post("/multiple")
  @ApiOperation({ summary: "Uploads multiple files" })
  @ApiConsumes("multipart/form-data")
  @UseInterceptors(FilesInterceptor("files", 4))
  // @ApiBearerAuth()
  // @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager, Role.Vendor)
  @ApiOperation({ summary: 'Upload multiple files', description: `This endpoint uploads multiple file. This is accessible for all` })
  @ApiResponse({ status: 200, description: 'File is retrieved.' })
  @ApiBody({
    required: true,
    schema: {
      type: "object",
      properties: {
        files: {
          type: "array",
          items: {
            type: "string",
            format: "binary"
          }
        }
      }
    }
  })
  @ApiQuery({ name: 'identifierIds', required: false, type: [String], description: 'Array of file identifiers' }) // Array of identifierIds
  multipleFiles(
    @UploadedFiles(
      new ParseFilePipe({
        validators: [
          new MaxFileSizeValidator({ maxSize: ******** }),  // 10MB limit
          new FileTypeValidator({ fileType: '.(jpeg|jpg|png|pdf|msword|vnd\.openxmlformats-officedocument\.wordprocessingml\.document|vnd\.ms-excel|vnd\.openxmlformats-officedocument\.spreadsheetml\.sheet|plain|json|webp|webm|wmv|mp4|avi|mov|mkv|ppt|pptx|csv)' }),  // Only allow JPEG images
        ],
        errorHttpStatusCode: HttpStatus.UNPROCESSABLE_ENTITY,
        fileIsRequired: true,
      })
    )
    files: Array<File>,
    @Req() req: any,
    @Query('identifierIds') identifierIds?: string[],
  ) {
    if (identifierIds) {
      // this.logger.log('identifierIds', identifierIds)
      let identifierObjIds;
      if (typeof (identifierIds) == 'string') {
        identifierObjIds = [validateObjectId(identifierIds)]
      }
      else {
        identifierObjIds = identifierIds.map(identifierId => validateObjectId(identifierId))
      }
      return this.fileUploadService.saveFiles(files, req, identifierObjIds);
    }

    return this.fileUploadService.saveFiles(files, req);
  }


  @Get('all')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager, Role.Vendor)
  @Roles()
  @ApiOperation({ summary: 'Get all files metadata', description: 'This endpoint returns the metadata all files. This is accessible for all.' })
  @ApiResponse({ status: 200, description: 'Returns all uploaded files metadata.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  async getAllFiles() {
    return await this.fileUploadService.getAllFiles();
  }

  @Get(':fileId/metadata')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager, Role.Vendor)
  @Roles()
  @ApiOperation({ summary: 'Retrieve file metadata by Id', description: `This endpoint returns file metadata by its Id. This is accessible for all.` })
  @ApiResponse({ status: 200, description: 'File metadata is retrieved.' })
  @ApiResponse({ status: 400, description: 'Bad Request: Invalid ID format.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  @ApiResponse({ status: 404, description: 'File metadata not found.' })
  @ApiParam({ name: 'fileId', description: 'Id of the file metadata to retrieve' })
  async getFileMetadata(@Param('fileId') fileId: string) {
    const fileObjId = validateObjectId(fileId);
    const metadata = await this.fileUploadService.getMetadataById(fileObjId);
    if (!metadata) {
      throw new NotFoundException('File metadata not found');
    }
    return metadata;
  }

  @Get(':fileId/download')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager, Role.Vendor)
  @Roles()
  @ApiOperation({ summary: 'Download a file by Id', description: `This endpoint downloads file by its Id. This is accessible for all.` })
  @ApiResponse({ status: 200, description: 'File is downloaded.' })
  @ApiResponse({ status: 400, description: 'Bad Request: Invalid ID format.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  @ApiResponse({ status: 404, description: 'File not found.' })
  @ApiParam({ name: 'fileId', description: 'Id of the file metadata to retrieve' })
  async downloadFile(@Param('fileId') fileId: string, @Res() res: FastifyReply): Promise<void> {
    const fileObjId = validateObjectId(fileId);
    return this.fileUploadService.downloadFile(fileObjId, res);
  }

  @Delete(':fileId')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager, Role.Vendor)
  @Roles()
  @ApiOperation({ summary: 'Delete a file by Id', description: 'This endpoints deletes a file by id. This is accessible for all' })
  @ApiResponse({ status: 200, description: 'File deleted successfully.' })
  @ApiResponse({ status: 400, description: 'Bad Request: Invalid ID format.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 404, description: 'File not found.' })
  @ApiParam({ name: 'fileId', description: 'ID of the file' })
  async deleteFile(@Param('fileId') fileId: string) {
    const fileObjId = validateObjectId(fileId);
    return this.fileUploadService.deleteFile(fileObjId);
  }

  @Patch(':fileId/vendor-status')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager, Role.Vendor)
  @Roles()
  @ApiOperation({ summary: 'Update a file status by Id', description: 'This endpoints updates a file status by id. This is accessible for all' })
  @ApiResponse({ status: 200, description: 'File status updated successfully.' })
  @ApiResponse({ status: 400, description: 'Bad Request: Invalid ID format.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 404, description: 'File not found.' })
  @ApiParam({ name: 'fileId', description: 'ID of the file' })
  @ApiBody({
    description: 'Status update payload',
    type: UpdateStatusDto,
  })
  async updateVendorFileStatus(
    @Param('fileId') fileId: string,
    @Body() updateStatusDto: UpdateStatusDto ,
    @Req() req: any
  ) {
    const fileObjId = validateObjectId(fileId);
    return this.fileUploadService.updateVendorFileStatus(fileObjId, updateStatusDto,req);
  }

  @Post('parse')
  @ApiOperation({ summary: "Parse resume file" })
  @ApiConsumes("multipart/form-data")
  @UseInterceptors(FileInterceptor("file"))
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager, Role.Vendor)
  @Roles()
  @ApiOperation({ summary: 'Update a file status by Id', description: 'This endpoints updates a file status by id. This is accessible for all' })
  @ApiResponse({ status: 200, description: 'Resume parsed successfully.' })
  @ApiResponse({ status: 400, description: 'Unsupported file type.' })
  @ApiResponse({ status: 500, description: 'Internal Server Error.' })
  @ApiBody({
    required: true,
    schema: {
      type: "object",
      properties: {
        file: {
          type: "string",
          format: "binary",
        }
      }
    }
  })
  async parseResume(
    @UploadedFile(
      new ParseFilePipe({
        validators: [
          new MaxFileSizeValidator({ maxSize: ******** }),  // 10MB limit
          new FileTypeValidator({ fileType: '.(jpeg|jpg|png|pdf|msword|vnd.openxmlformats-officedocument.wordprocessingml.document|vnd.ms-excel|vnd.openxmlformats-officedocument.spreadsheetml.sheet|plain|json|webp|webm|wmv|mp4|avi|mov|mkv|ppt|pptx|csv|html|txt|doc|docx)' }),
        ],
        errorHttpStatusCode: HttpStatus.UNPROCESSABLE_ENTITY,
        fileIsRequired: true
      })
    )
    file: Express.Multer.File,
    @Req() req: any
  ) {
    if (!file) {
      throw new HttpException('No file provided.', HttpStatus.BAD_REQUEST);
    }
    return this.fileUploadService.parseResume(file);
  }

  @Patch(':fileId/status')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager)
  @Roles()
  @ApiOperation({ summary: 'Update a file status by Id', description: 'This endpoints updates a file status by id. This is accessible for all' })
  @ApiResponse({ status: 200, description: 'File status updated successfully.' })
  @ApiResponse({ status: 400, description: 'Bad Request: Invalid ID format.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 404, description: 'File not found.' })
  @ApiParam({ name: 'fileId', description: 'ID of the file' })
  @ApiBody({
    description: 'Status update payload',
    type: UpdateStatusDto,
  })
  async updateFileStatus(
    @Param('fileId') fileId: string,
    @Body() updateStatusDto: UpdateStatusDto ,
    @Req() req: any
  ) {
    const fileObjId = validateObjectId(fileId);
    return this.fileUploadService.updateStatus(fileObjId, updateStatusDto,req);
  }

}
