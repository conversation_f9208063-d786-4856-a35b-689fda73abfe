import { ApiHideProperty, ApiProperty } from "@nestjs/swagger";
import { IsEnum, IsNotEmpty, IsOptional, IsString } from "class-validator";
import { ClientStatus, HeadCount } from "src/shared/constants";
import { Transform, TransformFnParams } from 'class-transformer';
import { sanitizeWithStyle } from "src/utils/sanitize-with-style";

export class CreateClientDto {

    @ApiProperty({
        type: String,
        required: true,
        description: 'Client Name',
    })
    @IsNotEmpty()
    @IsString()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    name: string;

    @ApiProperty({ type: String, required: false, description: 'Reference to the ClientId' })
    @IsOptional()
    @IsString()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    parentAccount?: string;

    @ApiProperty({
        type: String,
        required: false,
        description: 'Website url',
    })
    @IsOptional()
    @IsString()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    websiteUrl?: string;

    @ApiProperty({
        type: String,
        required: false,
        description: 'Contact number',
    })
    @IsOptional()
    @IsString()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    contactNumber?: string;

    @ApiProperty({ type: String, description: 'Client Industry' })
    @IsOptional()
    @IsString()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    industry?: string;

    @ApiProperty({
        type: String,
        required: false,
        default: HeadCount.NOT_SPECIFIED,
        enum: HeadCount,
        description: 'Number of employees',
    })
    @IsOptional()
    @IsEnum(HeadCount)
    headCount?: HeadCount;

    @ApiProperty({
        type: String,
        required: false,
        description: 'Client description',
    })
    @IsOptional()
    @IsString()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    description?: string;

    @ApiProperty({ type: String, required: false, description: 'Client address', })
    @IsOptional()
    @IsString()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    address?: string;

    @ApiProperty({ type: String, required: false, description: 'Client city', })
    @IsOptional()
    @IsString()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    city?: string;

    @ApiProperty({ type: String, required: false, description: 'Client province', })
    @IsOptional()
    @IsString()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    stateOrProvince?: string;

    @ApiProperty({ type: String, required: false, description: 'Client country', })
    @IsOptional()
    @IsString()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    country?: string;

    @ApiProperty({ type: String, required: false, description: 'Client zip code', })
    @IsOptional()
    @IsString()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    pinOrZipCode?: string;


    @ApiProperty({
        required: false,
        type: String, description: 'Reference to the Owner Client',
    })
    @IsOptional()
    @IsString()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    ownedBy?: string;

    @ApiHideProperty()
    @IsOptional()
    @IsString()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    createdBy?: string;

    @ApiProperty({
        type: String,
        required: false,
        default: ClientStatus.PROSPECT,
        enum: ClientStatus,
        description: 'Client status',
    })
    @IsOptional()
    @IsEnum(ClientStatus)
    status?: ClientStatus


}

