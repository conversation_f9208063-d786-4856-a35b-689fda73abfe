import { Modu<PERSON> } from '@nestjs/common';
import { DashboardController } from './dashboard.controller';
import { DashboardService } from './dashboard.service';
import { JwtModule } from '@nestjs/jwt';
import { MongooseModule } from '@nestjs/mongoose';
import { DashboardMetadata, DashboardMetadataSchema } from './schemas/dashboard-metadata.schema';

@Module({
  imports: [JwtModule,
    MongooseModule.forFeature([
      { name: DashboardMetadata.name, schema: DashboardMetadataSchema },
    ]),
  ],
  controllers: [DashboardController],
  providers: [DashboardService]
})
export class DashboardModule { }
