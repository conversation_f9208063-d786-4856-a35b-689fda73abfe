import { Module } from '@nestjs/common';
import { RtmGateway } from './rtm/rtm.gateway';
import { RedisStoreService } from './redis-store/redis-store.service';
import { AuthModule } from 'src/auth/auth.module';
import { UserModule } from 'src/user/user.module';
import { JwtModule } from '@nestjs/jwt';
import { ConfigModule, ConfigService } from '@nestjs/config';

@Module({
  imports: [
    AuthModule, 
    UserModule, 
    // 
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get<string>('SECRET_KEY'),
        signOptions: {
          // expiresIn: '1h' ,
          expiresIn: configService.get('TOKEN_EXPIRTY_PERIOD')
        },
      }),
      inject: [ConfigService],
    }),
  ],
  providers: [RtmGateway, RedisStoreService]
})
export class RealTimeMessagingModule {}
