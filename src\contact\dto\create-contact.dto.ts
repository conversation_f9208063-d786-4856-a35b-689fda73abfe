import { ApiProperty, ApiHideProperty } from "@nestjs/swagger";
import { IsNotEmpty, IsString, IsOptional, IsBoolean, IsEnum, IsArray, IsEmail, IsMongoId, isMongoId, ValidateNested } from "class-validator";
import { Account } from "src/account/schemas/account.schema";
import { ContactInformation } from "src/common/schemas/contact-information.schema";
import { AccountStatus } from "src/shared/constants";
import { Industry } from "src/industry/schemas/industry.schema";
import { ContactInformationDto } from "src/common/dto/contact-information.dto";
import { Transform, TransformFnParams, Type } from "class-transformer";
import { sanitizeWithStyle } from "src/utils/sanitize-with-style";
import { AddressInformationDto } from "src/common/dto/address-information.dto";

export class CreateContactDto {

  // @ApiProperty({ 
  //   type: String,
  //   required: true,
  //   description: 'Salutation of contact',
  // })
  // @IsNotEmpty()
  // @IsString()
  // salutation: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'First name of contact',
  })
  @IsNotEmpty()
  @IsString()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  firstName: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Last name of contact',
  })
  @IsNotEmpty()
  @IsString()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  lastName: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Referrer',
  })
  @IsOptional()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  referredBy?: string;


  @ApiProperty({
    type: String,
    required: false,
    description: 'The logo of your contact',
  })
  @IsString()
  @IsOptional()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  logo?: string;


  // @ApiProperty({ 
  //   type: [String],
  //   required: true,
  //   description: 'Contact Email accounts',
  // })
  // @IsNotEmpty()
  // @IsArray()
  // contactEmail: string[];

  // @ApiProperty({ 
  //   type: [String],
  //   required: true,
  //   description: 'Contact phone numbers',
  // })
  // @IsNotEmpty()
  // @IsArray()
  // contactPhoneNumber: string[];

  // @ApiProperty({type: ContactInformation}) // {type: Name} can be omitted
  //  contactInformation: ContactInformation;

  @ApiProperty({
    type: [ContactInformationDto],
    required: false,
    description: 'Contact information of contact'
  })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => ContactInformationDto)
  contactDetails?: ContactInformationDto[];

  @ApiProperty({
    type: AddressInformationDto,
    required: false,
    description: 'Contact address'
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => AddressInformationDto)
  contactAddress?: AddressInformationDto;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Designation of the contact',
  })
  @IsOptional()
  @IsString()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  designation?: string;

  // @ApiProperty({ 
  //   type: String,
  //   required: true,
  //   description: 'Location of contact',
  // })
  // @IsNotEmpty()
  // @IsString()
  // @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  // location: string; 

  @ApiProperty({
    type: String,
    required: false,
    description: 'Industry',
  })
  @IsOptional()
  @IsString()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  industry?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Org to which the contact belongs',
  })
  @IsString()
  @IsOptional()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  accountOrg?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Org to which the sales representative belongs',
  })
  @IsString()
  @IsOptional()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  salesRepOrg?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Linkedin URL of contact',
  })
  @IsString()
  @IsOptional()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  linkedInUrl?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'A seperate section of company',
  })
  @IsString()
  @IsOptional()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  businessUnit?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Reporting to refers to another contact already in the app, who manages this contact at their company',
  })
  @IsString()
  @IsOptional()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  reportingTo?: string;

  // @ApiProperty({
  //   type: String,
  //   required: false,
  //   description: 'Represents a sales representative who manages the contact',
  // })
  // @IsString()
  // @IsOptional()
  // @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  // assignTo?: string;

  @ApiProperty({
    type: [String],
    required: false,
    description: 'Represents a sales representative who manages the contact',
  })
  @IsArray()
  @IsOptional()
  @Transform(({ value }) => {
    if (Array.isArray(value)) {
      return value.map((v) => sanitizeWithStyle(v));
    }
    return [];
  })
  assignTo?: string[];

  @ApiProperty({
    type: String,
    required: false,
    default: AccountStatus.PROSPECT,
    enum: AccountStatus,
    description: 'Status of contact',
  })
  @IsOptional()
  @IsString()
  @IsEnum(AccountStatus)
  status?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: "Profile picture of the contact"
  })
  @IsOptional()
  @IsString()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  imageUrl?: string;

  @ApiProperty({
    type: Boolean,
    required: false,
    default: false
  })
  @IsOptional()
  @IsBoolean()
  favourite?: boolean;

  @ApiProperty({
    type: String,
    required: false,
    description: "Enter city"
  })
  @IsMongoId()
  @IsOptional()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value).toLowerCase())
  city?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: "Enter state"
  })
  @IsMongoId()
  @IsOptional()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value).toLowerCase())
  state?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: "Enter country"
  })
  @IsMongoId()
  @IsOptional()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value).toLowerCase())
  country?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: "Comments on contacts"
  })
  @IsOptional()
  @IsString()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  comment?: string;

  @ApiHideProperty()
  createdBy: string;

  @ApiHideProperty()
  @IsOptional()
  @IsString()
  createdByOrg?: string;

  @ApiProperty({
    type: Boolean,
    required: false,
    default: false
  })
  @IsOptional()
  @IsBoolean()
  isInternal?: boolean;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Vendor to which the contact belongs',
  })
  @IsString()
  @IsOptional()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  vendor?: string;

}
