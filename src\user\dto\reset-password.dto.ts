import { IsNotE<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, IsS<PERSON> } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Transform, TransformFnParams } from 'class-transformer';
import sanitizeWithStyle from 'sanitize-html'
export class ResetPasswordDto {
  @ApiProperty({
    type: String,
    example: '<EMAIL>',
    description: 'The email of the User',
    format: 'email',
    uniqueItems: true,
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  @IsEmail()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value).toLowerCase())
  email: string;

  @ApiProperty({
    example: 'secret password change me!',
    description: 'The password of the User',
    type: String,
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  @MinLength(8)
  @MaxLength(128)
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  password: string;
}
