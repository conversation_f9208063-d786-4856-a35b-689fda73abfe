import { Prop, SchemaFactory, Schema } from '@nestjs/mongoose';
import { Types } from 'mongoose';
import { JobAllocationBase, JobAllocationBaseSchema } from './job-allocation-base.schema';
import { JobAllocationType } from 'src/shared/constants';

@Schema({
    timestamps: true,
})
export class JobAllocationToAiRecruiters extends JobAllocationBase {
    @Prop({ required: true, type: Boolean, default: false })
    isAvailableInAiRecruiterPool: boolean;

    @Prop({ required: true, type: Boolean, default: false })
    uptoAIRecruiter: boolean;
}

export const JobAllocationToAiRecruitersSchema = JobAllocationBaseSchema.discriminator(
    JobAllocationType.AI_RECRUITERS,  // Use enum instead of string
    SchemaFactory.createForClass(JobAllocationToAiRecruiters),
);