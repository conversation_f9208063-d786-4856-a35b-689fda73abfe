import { Transform } from "class-transformer";
import { IsOptional, IsString } from "class-validator";

export class QueryEmailDto {

    @IsOptional()
    @IsString()
    to?:string[]

    @IsOptional()
    @IsString()
    from?:string

    @IsOptional()
    @IsString()
    @Transform(({ value }) => (value === undefined || value === '0' ? '' : value))
    subject?:string

    
    @IsOptional()
    @IsString()
    @Transform(({ value }) => (value === undefined || value === '0' ? '' : value))
    body?:string


}