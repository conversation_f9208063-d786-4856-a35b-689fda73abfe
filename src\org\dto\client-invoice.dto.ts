import { ApiHideProperty, ApiProperty } from "@nestjs/swagger";
import { IsBoolean, IsDate, IsEnum, IsISO8601, IsMongoId, IsNumber, IsOptional, IsString, Length } from "class-validator";
import { DurationUnit } from "src/shared/constants"; // Assuming DurationUnit is imported from the correct path

export class UpdateInvoiceDetailsDto  {

    @ApiProperty({
        type: Date,
        required: false,
        description: "The date the recurring invoice starts.",
        default: new Date(Date.UTC(new Date().getUTCFullYear(), new Date().getUTCMonth(), new Date().getUTCDate(), new Date().getUTCHours(), new Date().getUTCMinutes())),
    })
    @IsISO8601({ strict: true })
    @IsOptional()
    startDate?: Date;

    @ApiProperty({
        type: Number,
        required: false,
        description: "The duration value for the recurrence.",
    })
    @IsNumber()
    @IsOptional()
    invoiceDurationValue?: number;

    @ApiProperty({
        type: String,
        required: false,
        enum: Object.values(DurationUnit),
        description: "The duration unit for the recurrence (e.g., days, months, etc.).",
    })
    @IsEnum(DurationUnit)
    @IsOptional()
    invoiceDurationUnit?: string;

    @ApiProperty({
        type: Number,
        required: false,
        description: "The duration in days after which payment is due (e.g., 30 days, 60 days).",
    })
    @IsNumber()
    @IsOptional()
    paymentDurationValue?: number;

    @ApiProperty({
        type: Boolean,
        required: false,
        default: false,
        description: "Indicates if the recurring invoice never expires. If false, an `endDate` is required.",
    })
    @IsBoolean()
    @IsOptional()
    neverExpires?: boolean;

    @ApiProperty({
        type: Date,
        required: false,
        description: "The end date of the recurring invoice (required if `neverExpires` is false).",
        default: new Date(Date.UTC(new Date().getUTCFullYear(), new Date().getUTCMonth(), new Date().getUTCDate(), new Date().getUTCHours(), new Date().getUTCMinutes())),
    })
    @IsISO8601({ strict: true })
    @IsOptional()
    endDate?: Date;

}
