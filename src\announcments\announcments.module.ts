import { <PERSON>du<PERSON> } from '@nestjs/common';
import { AnnouncmentsService } from './announcments.service';
import { AnnouncmentsController } from './announcments.controller';
import { MongooseModule } from '@nestjs/mongoose';
import { Announcments, AnnouncmentsSchema } from './schemas/announcment.schema';
import { ConfigModule } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { JobAllocationBase, JobAllocationBaseSchema } from 'src/job-allocation/schemas/job-allocation-base.schema';

@Module({
  controllers: [AnnouncmentsController],
  providers: [AnnouncmentsService],
    imports: [
     ConfigModule,
     JwtModule,
      MongooseModule.forFeature([
        { name: Announcments.name, schema: AnnouncmentsSchema },
        {name: JobAllocationBase.name, schema: JobAllocationBaseSchema},
      
      ]),
    ],
})
export class AnnouncmentsModule {}
