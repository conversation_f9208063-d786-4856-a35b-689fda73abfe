import { ApiProperty } from "@nestjs/swagger";
import { IsMongoId, IsOptional } from "class-validator";
import { CreateJobAllocationBaseDto } from "./create-job-allocation.dto";

export class CreateJobAllocationToAssigneesDto extends CreateJobAllocationBaseDto {
    @ApiProperty({ type: String, required: false, description: 'Assignee user ID' })
    @IsMongoId()
    @IsOptional()  // Make assignee optional
    assignee?: string;

    @ApiProperty({
        type: [String],
        required: false,
        description: 'List of user IDs assigned to the job allocation',
    })
    @IsOptional()
    @IsMongoId({ each: true })
    assignees?: string[];
}