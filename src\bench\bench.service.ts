import { BadRequestException, Injectable, InternalServerErrorException, Logger, NotFoundException } from '@nestjs/common';
import { CreateBenchDto } from './dto/create-bench.dto';
import { UpdateBenchDto } from './dto/update-bench.dto';
import { ConfigService } from '@nestjs/config';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Bench } from './schemas/bench.schema';
import { Job } from 'src/job/schemas/job.schema';
import { Org } from 'src/org/schemas/org.schema';
import { JobAllocationBase } from 'src/job-allocation/schemas/job-allocation-base.schema';
import { OrgType } from 'src/shared/constants';

@Injectable()
export class BenchService {
  private readonly logger = new Logger(BenchService.name);

  constructor(private configService: ConfigService,
    @InjectModel(Bench.name) private benchModel: Model<Bench>,
    @InjectModel(Org.name) private orgsModel: Model<Org>,
    @InjectModel(JobAllocationBase.name) private jobAllocationBasesModel: Model<JobAllocationBase>,
    @InjectModel(Job.name) private jobModel: Model<Job>) { }
  async create(user: any, createBenchDto: CreateBenchDto) {

    try {
      // List of fields to check
      const fieldsToCheck: (keyof UpdateBenchDto)[] = ['resumeMetadata', 'candidateImage'];

      // Iterate through each field, check for empty string, and remove it from createBenchDto
      fieldsToCheck.forEach((field) => {
        if (createBenchDto[field] === '') {
          delete createBenchDto[field]; // Remove the field if it's an empty string
        }
      });
      const createBenchForm = new this.benchModel(createBenchDto);
      const savedBenchCandidate = await createBenchForm.save();
      return savedBenchCandidate
    } catch (error) {
      this.logger.error(`Failed to create Bench Candidate. ${error}`);
      throw new InternalServerErrorException(`Failed to create Bench Candidate. ${error.message}`);
    }
  }

  async findAll(userId: string) {
    const populateOptions = this.getPopulateOptions();
    try {
      // Find all bench candidates created by the specified user
      const benchCandidates = await this.benchModel
        .find({ createdBy: userId, isDeleted: false })
        .populate(populateOptions)
        .sort({ createdAt: -1 })
        .exec();
      return benchCandidates;
    } catch (error) {
      this.logger.error(`Failed to fetch Bench Candidates. ${error}`);
      throw new InternalServerErrorException(
        `Failed to fetch Bench Candidates. ${error.message}`,
      );
    }
  }

  async update(id: string, updateBenchDto: UpdateBenchDto) {

    const unsetData: any = {};

    // List of fields to check
    const fieldsToCheck: (keyof UpdateBenchDto)[] = ['resumeMetadata', 'candidateImage'];

    // Iterate through each field, check for empty string, remove it from updateContactDto and mark for unset
    fieldsToCheck.forEach((field) => {
      if (updateBenchDto[field] === '') {
        unsetData[field] = ''; // Mark for removal
        delete updateBenchDto[field]; // Remove from updateContactDto
      }
    });

    try {
      const existingBench = await this.benchModel.findByIdAndUpdate(
        id,
        {
          $set: updateBenchDto, // Apply the updated fields
          $unset: unsetData, // Remove the fields marked for deletion
        },
        { new: true },
      ).exec();

      if (!existingBench) {
        throw new NotFoundException(`Bench Candidate with ID ${id} not found`);
      }

      return existingBench;
    } catch (error) {
      this.logger.error(`Failed to update Bench Candidate. ${error}`);
      throw new InternalServerErrorException(`Failed to update Bench Candidate. ${error.message}`);
    }
  }

  async findOne(id: string) {
    const populateOptions = this.getPopulateOptions();
    try {
      const benchCandidate = await this.benchModel
        .findById(id)
        .populate(populateOptions)
        .exec();

      if (!benchCandidate) {
        throw new NotFoundException(`Bench Candidate with ID ${id} not found`);
      }

      return benchCandidate;
    } catch (error) {
      this.logger.error(`Failed to fetch Bench Candidate with ID ${id}. ${error}`);
      throw new InternalServerErrorException(`Failed to fetch Bench Candidate. ${error.message}`);
    }
  }

  async getVendorBenchAndMatchingJobs(user: any, userId: string, companyId: string, fromDate?: string, toDate?: string) {
    this.logger.log("companyId", companyId)

    try {

      // Step 1: Construct the date filter for bench profiles
      const dateFilter: any = { createdBy: userId , isDeleted: false };
      const createdAtFilter: any = {};

      if (fromDate) createdAtFilter.$gte = new Date(fromDate);
      if (toDate) {
        const toDateObj = new Date(toDate);
        toDateObj.setDate(toDateObj.getDate() + 1); // Include full day
        createdAtFilter.$lt = toDateObj;
      }

      if (Object.keys(createdAtFilter).length) {
        dateFilter.createdAt = createdAtFilter;
      }

      this.logger.log(JSON.stringify(dateFilter))
      // Step 1: Get all bench profiles for the vendor's org
      const benchProfiles = await this.benchModel.find(dateFilter).exec();
      const benchCount = benchProfiles.length;

      if (benchCount === 0) {
        return { benchCount: 0, totalMatchingJobsCount: 0 };
      }

      // Step 2: Extract skills from evaluation forms
      // const benchSkills = new Set<string>();
      // console.log("bench skills", benchSkills)
      // benchProfiles.forEach(bench => {
      //   bench.evaluationForm?.forEach(evaluation => {
      //     benchSkills.add(evaluation.skill.toLowerCase());
      //   });
      // });
      const benchSkills = new Set<string>();

      benchProfiles.forEach(bench => {
        if (bench.skills && Array.isArray(bench.skills)) {
          bench.skills.forEach(skillObj => {
            if (skillObj.skill) {
              benchSkills.add(skillObj.skill.toLowerCase());
            }
          });
        }
      });


      // console.log("bench profiles", benchProfiles)
      // console.log("bench skills after for each", benchSkills)

      // Step 3: Find matching jobs
      // const matchingJobsCount = await this.jobModel.countDocuments({
      //   postingOrg: companyId,
      //   $or: [
      //     { primarySkills: { $in: Array.from(benchSkills) } },
      //     { secondarySkills: { $in: Array.from(benchSkills) } }
      //   ]
      // });

      // const matchingJobsFreelancersCount = await this.jobModel.countDocuments({
      //   shareWithFreelancers: true,
      //   $or: [
      //     { primarySkills: { $in: Array.from(benchSkills) } },
      //     { secondarySkills: { $in: Array.from(benchSkills) } }
      //   ]
      // });

      const benchSkillsRegex = Array.from(benchSkills).map(skill => new RegExp(`^${skill}$`, 'i'));

      // Step 3: Construct the date filter for job postings
      const jobDateFilter: any = {};
      if (fromDate) jobDateFilter.$gte = new Date(fromDate);
      if (toDate) {
        const toDateObj = new Date(toDate);
        toDateObj.setDate(toDateObj.getDate() + 1);
        jobDateFilter.$lt = toDateObj;
      }

      // Step 1: Get all matching vendor orgs
      const existingOrgs = await this.orgsModel.find({ 'contactDetails.contactEmail': user.email, isDeleted: false, orgType: OrgType.VENDOR_ORG }).exec();

      // Step 2: Extract org _ids
      const vendorOrgIds = existingOrgs.map(org => org._id.toString());
      console.log("vendorOrgIds", vendorOrgIds);
      // Step 3: Prepare allocation query
      const queryConditions: any = {
        kind: 'JobAllocationToVendors',
        isDeleted: false,
        vendor: { $in: vendorOrgIds } // Match any of the vendor orgs
      };
      // Step 4: Query allocations
      const jobAllocations = await this.jobAllocationBasesModel.find(queryConditions).exec();
      const allocatedjobIds = jobAllocations.map(allocation => new Types.ObjectId(allocation.job.toString()));


      const matchingJobsCount = await this.jobModel.aggregate([
        {
          $match: {
            $and: [
              {
                $or: [
                  { _id: { $in: allocatedjobIds } },
                  { shareWithFreelancers: true }
                ]
              },
              {
                $or: [
                  { primarySkills: { $in: benchSkillsRegex } },
                  { secondarySkills: { $in: benchSkillsRegex } }
                  // { primarySkills: { $in: Array.from(benchSkills) } },
                  // { secondarySkills: { $in: Array.from(benchSkills) } }
                ]
              },
              ...(Object.keys(jobDateFilter).length
                ? [{ createdAt: jobDateFilter }]
                : []),
            ]
          }
        },
        {
          $group: {
            _id: "$_id" // Ensures unique job IDs
          }
        },
        {
          $count: "totalCount" // Counts unique job IDs
        }
      ]);

      const totalMatchingJobsCount = matchingJobsCount.length > 0 ? matchingJobsCount[0].totalCount : 0;


      // const totalMatchingJobsCount = matchingJobsCount.length > 0 ? matchingJobsCount[0].totalCount : 0;



      return { benchCount, totalMatchingJobsCount };

      // const matchingJobs = await this.jobModel.find({
      //   postingOrg: companyId,
      //    shareWithFreelancers: true,
      //   $or: [
      //     { primarySkills: { $in: Array.from(benchSkills) } },
      //     { secondarySkills: { $in: Array.from(benchSkills) } }
      //   ]
      // });

      // console.log("Matching Jobs:", matchingJobs.map(job => ({
      //   jobId: job._id,
      //   title: job.title,
      //   primarySkills: job.primarySkills,
      //   secondarySkills: job.secondarySkills
      // })));

      // return { 
      //   benchCount, 
      //   matchingJobsCount: matchingJobs.length,
      //   matchingJobs 
      // };
    } catch (error) {
      this.logger.error(`Failed to get vendor bench stats. ${error}`);
      throw new InternalServerErrorException('Failed to retrieve stats.');
    }
  }

  //hard-delete
  // async delete(id: string) {
  //   try {
  //     const deletedBench = await this.benchModel.findByIdAndDelete(id).exec();

  //     if (!deletedBench) {
  //       throw new NotFoundException(`Bench Candidate with ID ${id} not found`);
  //     }

  //     return { message: `Bench Candidate with ID ${id} deleted successfully` };
  //   } catch (error) {
  //     this.logger.error(`Failed to delete Bench Candidate with ID ${id}. ${error}`);
  //     throw new InternalServerErrorException(`Failed to delete Bench Candidate. ${error.message}`);
  //   }
  // }

  async delete(id: string) {
    try {
      const updatedBench = await this.benchModel.findByIdAndUpdate(
        id,
        { isDeleted: true },
        { new: true } // Returns the updated document
      ).exec();

      if (!updatedBench) {
        throw new NotFoundException(`Bench Candidate with ID ${id} not found`);
      }
      return { message: `Bench Candidate with ID ${id} deleted successfully` };
    } catch (error) {
      this.logger.error(`Failed to delete Bench Candidate with ID ${id}. ${error}`);
      throw new BadRequestException(`Failed to delete Bench Candidate. ${error.message}`);
    }
  }



  getPopulateOptions(): any[] {
    const populateOptions = [
      { path: 'createdBy', select: '_id roles firstName', model: 'BasicUser' },
      { path: 'country', select: '_id countryId countryName countryPhoneCode currencyCode', model: 'Country' },
      { path: 'state', select: '_id stateId stateName country', model: 'State' },
      { path: 'city', select: '_id name', model: 'City' },
      { path: 'org', select: '_id title orgType description', model: 'Org' },
      { path: 'candidateImage', select: '_id locationUrl originalName fileSize fileType', model: 'FileMetadata' },
      { path: 'resumeMetadata', select: '_id locationUrl originalName fileSize fileType', model: 'FileMetadata' },
      // { path: 'evaluationForm', select: '_id skill years months rating isPrimary' },
      { path: 'reLocation', select: '_id city state country postalCode', model: 'JobLocation' },
    ];

    return populateOptions;
  }


}
