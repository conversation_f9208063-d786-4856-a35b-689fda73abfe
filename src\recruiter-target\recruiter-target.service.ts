import { Injectable, InternalServerErrorException, Logger, NotFoundException } from '@nestjs/common';
import { CreateRecruiterTargetDto } from './dto/create-recruiter-target.dto';
import { UpdateRecruiterTargetDto } from './dto/update-recruiter-target.dto';
import { ConfigService } from '@nestjs/config';
import { InjectModel } from '@nestjs/mongoose';
import { RecruiterTarget } from './schemas/recuriter-target.schema';
import { Model, Types } from 'mongoose';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';


@Injectable()
export class RecruiterTargetService {
   private readonly logger = new Logger(RecruiterTargetService.name);
  
    constructor(private configService: ConfigService,
      @InjectModel(RecruiterTarget.name) private recruiterTargetModel: Model<RecruiterTarget>,
      private eventEmitter: EventEmitter2,
    ) { }

  async create(createRecruiterTargetDto: CreateRecruiterTargetDto) {
    try{
      const { recruiterId, targetMonth } = createRecruiterTargetDto;

      const existingTarget = await this.recruiterTargetModel.findOne({
        recruiterId : recruiterId,
        targetMonth: targetMonth,
      });
  
      if (existingTarget) {
        // Update the existing target
        // Object.assign(existingTarget, createRecruiterTargetDto);
        existingTarget.targetNumber = createRecruiterTargetDto.targetNumber;
        await existingTarget.save();
        return existingTarget;
      } else {
        // Create new target
        const createTarget = new this.recruiterTargetModel(createRecruiterTargetDto);
        await createTarget.save();
        return createTarget;
      }
      // const createTarget = new this.recruiterTargetModel(createRecruiterTargetDto)
      // await createTarget.save();
      // return createTarget;

    }catch(error){
      this.logger.error('Failed to create target', error);
            throw new InternalServerErrorException('Unknown error when creating target.');

    }
  }

 
    // ✅ Get all recruiter targets
    async findAll(filters: { createdBy: string; org: string,recruiterId?: string }): Promise<RecruiterTarget[]> {
      return await this.recruiterTargetModel.find({
        createdBy: filters.createdBy,
        org: filters.org,
        recruiterId: filters.recruiterId,
      })
      .populate('createdBy')
      .populate('org')
      .sort({ createdAt: -1 })
      .exec();
    }

  // ✅ Get a single recruiter target by ID
  async findOne(id: string): Promise<RecruiterTarget> {
      try {
          const recruiterTarget = await this.recruiterTargetModel.findById(id).populate('org createdBy').exec();
          if (!recruiterTarget) {
              throw new NotFoundException(`Recruiter target with ID ${id} not found`);
          }
          return recruiterTarget;
      } catch (error) {
          this.logger.error(`Failed to retrieve recruiter target with ID ${id}`, error);
          throw new InternalServerErrorException('Error retrieving recruiter target.');
      }
  }

  // ✅ Update a recruiter target
  async update(id: string, updateRecruiterTargetDto: UpdateRecruiterTargetDto): Promise<RecruiterTarget> {
      try {
          const updatedTarget = await this.recruiterTargetModel.findByIdAndUpdate(
              id,
              updateRecruiterTargetDto,
              { new: true, runValidators: true }
          ).exec();

          if (!updatedTarget) {
              throw new NotFoundException(`Recruiter target with ID ${id} not found`);
          }

          return updatedTarget;
      } catch (error) {
          this.logger.error(`Failed to update recruiter target with ID ${id}`, error);
          throw new InternalServerErrorException('Error updating recruiter target.');
      }
  }

  // ✅ Delete a recruiter target
  async remove(id: string): Promise<void> {
      try {
          const result = await this.recruiterTargetModel.findByIdAndDelete(id).exec();
          if (!result) {
              throw new NotFoundException(`Recruiter target with ID ${id} not found`);
          }
      } catch (error) {
          this.logger.error(`Failed to delete recruiter target with ID ${id}`, error);
          throw new InternalServerErrorException('Error deleting recruiter target.');
      }
  }
}
