import { BadRequestException, HttpException, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { CreateIntegrationDto } from './dto/create-integration.dto';
import { UpdateIntegrationDto } from './dto/update-integration.dto';
import { Integration, IntegrationDocument } from './schemas/integrations.schema';
import { Model, Types } from 'mongoose';
import { InjectModel } from '@nestjs/mongoose';
import { IntegrationType } from 'src/shared/constants';
import { google } from 'googleapis';
import { GoogleOAuthCallbackDto } from './dto/google-oauth-callback.dto';
import axios from 'axios';
import { URLSearchParams } from 'url';
import { ZoomOAuthCallbackDto } from './dto/zoom-oauth-callback.dto';
import { MicrosoftOAuthCallbackDto } from './dto/microsoft-oauth-callback.dto';
import { SendWhatsAppDto } from './dto/send-whatsapp.dto';

@Injectable()
export class IntegrationService {
    private readonly logger = new Logger(IntegrationService.name);

    constructor(
        @InjectModel(Integration.name) private readonly integrationModel: Model<IntegrationDocument>,
    ) { }

    async create(createIntegrationDto: CreateIntegrationDto, user: any) {
        try {

            const existing = await this.integrationModel.findOne({
                createdBy: user._id,
                integrationType: createIntegrationDto.integrationType,
                ...(user.org?._id && { org: user.org._id }),
                isDeleted: false,
            });

            if (existing) {
                throw new BadRequestException(`Integration already exists for ${createIntegrationDto.integrationType}.`);
            }

            const { integrationType } = createIntegrationDto;

            if (integrationType === IntegrationType.GoogleMeet) {

                if (!createIntegrationDto.clientId || !createIntegrationDto.clientSecret) {
                    throw new BadRequestException('Client ID and Client Secret are required.');
                }

                const { clientId, clientSecret } = createIntegrationDto;

                const created = await new this.integrationModel(createIntegrationDto).save();

                const oauth2Client = new google.auth.OAuth2(
                    clientId,
                    clientSecret,
                    process.env.GOOGLE_REDIRECT_URI || 'https://yourfrontend.com/settings/integrations',
                );

                const scopes = [
                    'https://www.googleapis.com/auth/calendar',
                    'https://www.googleapis.com/auth/calendar.events',
                    // 'offline_access'
                ];

                const authUrl = oauth2Client.generateAuthUrl({
                    access_type: 'offline',
                    prompt: 'consent',
                    scope: scopes,
                    state: created._id.toString(),
                });

                return {
                    message: 'Integration created, redirect user to complete OAuth',
                    authUrl,
                    integration: created
                };
            }

            if (integrationType === IntegrationType.ZoomMeet || integrationType === IntegrationType.ZoomMeetPremium) {
                const { clientId, clientSecret, webhookSecretToken } = createIntegrationDto;
                if (!clientId || !clientSecret) {
                    throw new BadRequestException('Client ID and Client Secret are required.');
                }
                // if (createIntegrationDto.integrationType === IntegrationType.ZoomMeetPremium && !webhookSecretToken) {
                //     throw new BadRequestException('Webhook secret token is required for premium integration');
                // }

                const created = await new this.integrationModel(createIntegrationDto).save();

                const redirectUri = process.env.ZOOM_REDIRECT_URI || 'https://yourfrontend.com/settings/integrations';

                const authUrl = `https://zoom.us/oauth/authorize?response_type=code&client_id=${clientId}&redirect_uri=${redirectUri}&state=${created._id}`;

                return {
                    message: 'Zoom integration created. Redirect to complete OAuth.',
                    authUrl,
                    integration: created,
                };
            }
            if (integrationType === IntegrationType.MicrosoftTeams) {
                const { clientId, clientSecret, tenantId } = createIntegrationDto;

                if (!clientId || !clientSecret || !tenantId) {
                    throw new BadRequestException('Client ID, Client Secret and Tenant ID are required.');
                }

                const created = await new this.integrationModel(createIntegrationDto).save();

                const redirectUri = process.env.MS_TEAMS_REDIRECT_URI || 'https://yourfrontend.com/settings/integrations';

                const scopes = [
                    'User.Read',
                    'OnlineMeetings.ReadWrite',
                    'offline_access',
                ];

                const authUrl = `https://login.microsoftonline.com/${tenantId}/oauth2/v2.0/authorize?` +
                    new URLSearchParams({
                        client_id: clientId,
                        response_type: 'code',
                        redirect_uri: redirectUri,
                        response_mode: 'query',
                        scope: scopes.join(' '),
                        state: created._id.toString(),
                    }).toString();

                return {
                    message: 'Microsoft Teams integration created. Redirect to complete OAuth.',
                    authUrl,
                    integration: created,
                };
            }

            if (integrationType === IntegrationType.WhatsApp) {
                const { whatsappPhoneNumber, whatsappAccessToken, whatsappBusinessAccountId, whatsappTemplates } = createIntegrationDto;

                if (!whatsappBusinessAccountId || !whatsappAccessToken || !whatsappPhoneNumber) {
                    throw new BadRequestException('Business Account ID, WhatsApp Access Token and WhatsApp Phone Number are required.');
                }

                const approvedTemplateNames = await this.fetchWhatsAppTemplates(whatsappAccessToken, whatsappBusinessAccountId);

                if (whatsappTemplates) {
                    for (const [type, name] of Object.entries(whatsappTemplates)) {
                        if (!approvedTemplateNames.includes(name)) {
                            throw new BadRequestException(`WhatsApp template "${name}" for type "${type}" is not approved in Meta.`);
                        }
                    }
                }
                const created = await new this.integrationModel(createIntegrationDto).save();

                return {
                    message: 'WhatsApp integration saved successfully.',
                    integration: created,
                };
            }

            if (integrationType === IntegrationType.HuggingFace) {
                const { huggingFaceApiKey } = createIntegrationDto;
                if (!huggingFaceApiKey) {
                    throw new BadRequestException('Hugging Face API Key is required.');
                }

                const created = await new this.integrationModel(createIntegrationDto).save();

                return {
                    message: 'Hugging Face integration saved successfully.',
                    integration: created,
                };
            }

            if (integrationType === IntegrationType.OpenAI) {
                const { openAiApiKey } = createIntegrationDto;
                if (!openAiApiKey) {
                    throw new BadRequestException('OpenAI API Key is required.');
                }

                const created = await new this.integrationModel(createIntegrationDto).save();

                return {
                    message: 'OpenAI integration saved successfully.',
                    integration: created,
                };
            }

            if (
                integrationType === IntegrationType.JioCX ||
                integrationType === IntegrationType.AirtelIQ ||
                integrationType === IntegrationType.TataTele
              ) {
                const { smsSenderId, smsApiKey, smsApiSecret } = createIntegrationDto;
              
                if (!smsSenderId || !smsApiKey || !smsApiSecret) {
                  throw new BadRequestException('Sender ID, API Key, and API Secret are required for SMS integration.');
                }
              
                const smsBaseUrls = {
                  [IntegrationType.JioCX]: 'https://api.jiocx.com',
                  [IntegrationType.AirtelIQ]: 'https://api.airteliq.in',
                  [IntegrationType.TataTele]: 'https://api.tatateleservices.com',
                };
              
                const smsBaseUrl = smsBaseUrls[integrationType];
              
                const created = await new this.integrationModel({
                  ...createIntegrationDto,
                  smsBaseUrl,
                }).save();
              
                return {
                  message: `${integrationType} SMS integration saved successfully.`,
                  integration: created,
                };
              }
              
            throw new BadRequestException('Invalid integration type');

        } catch (error) {
            this.logger.error(`Failed to create integration: ${error.message}`, error.stack);
            throw new BadRequestException(`Error creating integration. ${error.message}`);
        }
    }


    // Authorization codes are meant to be single-use.
    // Once an authorization code is used to obtain an access token, it becomes invalid.
    // Any subsequent attempts to use the same code will result in an error.
    async handleGoogleCallback({ code, state }: GoogleOAuthCallbackDto) {
        // this.logger.log(`Handling Google OAuth callback with code: ${code} and state: ${state}`);
        const integration = await this.integrationModel.findOne(new Types.ObjectId(state));
        if (!integration) {
            throw new BadRequestException('Invalid or expired integration state');
        }

        // this.logger.log(`Handling Google OAuth callback for integration ${integration._id}`);

        if (!integration.clientId || !integration.clientSecret) {
            throw new Error('Missing required configuration for OAuth.');
        }
        // return integration
        try {
            const params = new URLSearchParams();
            params.append('code', code);
            params.append('client_id', integration.clientId);
            params.append('client_secret', integration.clientSecret);
            // params.append('redirect_uri', 'https://talency-frontend.vercel.app/settings/integrations');
            params.append('redirect_uri', process.env.GOOGLE_REDIRECT_URI || 'https://portal.talsy.ai/settings/integrations');
            params.append('grant_type', 'authorization_code');

            // this.logger.log(`Google token exchange params: ${params.toString()}`);
            const tokenRes = await axios.post(
                'https://oauth2.googleapis.com/token',
                params.toString(),
                {
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                },
            );

            // this.logger.log(`Google token exchange response: ${JSON.stringify(tokenRes.data)}`);
            const { access_token, refresh_token, scope, expires_in } = tokenRes.data;

            // integration.accessToken = access_token;
            integration.scope = scope?.split(' ') ?? [];

            if (refresh_token) {
                integration.refreshToken = refresh_token;
            }

            // if (expires_in) {
            //     integration.accessTokenExpiresAt = new Date(Date.now() + expires_in * 1000);
            // }

            await integration.save();

            this.logger.log(`Integration ${integration._id} saved successfully`);

            return {
                message: 'Google integration successful',
                integrationId: integration._id,
            };
        } catch (error) {
            throw new BadRequestException(`Google token exchange failed: ${error.message}`);
        }
    }

    async regenerateConsentUrl(integrationId: string) {
        const integration = await this.integrationModel.findById(integrationId);
        if (!integration) {
            throw new BadRequestException('Invalid integration ID');
        }

        if (integration.integrationType !== IntegrationType.GoogleMeet) {
            throw new BadRequestException('Consent URL regeneration is only supported for Google Meet integration');
        }

        const { clientId, clientSecret } = integration;

        const oauth2Client = new google.auth.OAuth2(
            clientId,
            clientSecret,
            process.env.GOOGLE_REDIRECT_URI || 'https://portal.talsy.ai/settings/integrations', // frontend redirect URI
        );

        const scopes = [
            'https://www.googleapis.com/auth/calendar',
            'https://www.googleapis.com/auth/calendar.events',
        ];

        const authUrl = oauth2Client.generateAuthUrl({
            access_type: 'offline',
            prompt: 'consent',
            scope: scopes,
            state: integration._id.toString(),
        });

        return {
            message: 'Consent URL regenerated, redirect user to complete OAuth',
            authUrl,
            integration: integration
        };
    }

    async handleZoomCallback({ code, state }: ZoomOAuthCallbackDto) {
        const integration = await this.integrationModel.findById(state);
        if (!integration) {
            throw new BadRequestException('Invalid or expired integration state');
        }

        try {
            const basicAuth = Buffer.from(
                `${integration.clientId}:${integration.clientSecret}`,
            ).toString('base64');

            const tokenRes = await axios.post(
                'https://zoom.us/oauth/token',
                null,
                {
                    params: {
                        grant_type: 'authorization_code',
                        code,
                        redirect_uri: process.env.ZOOM_REDIRECT_URI || 'https://portal.talsy.ai/settings/integrations',
                    },
                    headers: {
                        Authorization: `Basic ${basicAuth}`,
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                },
            );

            // this.logger.log(`Zoom token exchange response: ${JSON.stringify(tokenRes.data)}`);
            const { access_token, refresh_token, expires_in, scope } = tokenRes.data;

            integration.refreshToken = refresh_token;
            integration.scope = scope?.split(' ') ?? [];

            await integration.save();

            this.logger.log(`Zoom Integration ${integration._id} updated successfully`);

            return {
                message: 'Zoom integration successful',
                integrationId: integration._id,
            };
        } catch (error) {
            this.logger.error(`Zoom token exchange failed: ${error.message}`, error.stack);
            throw new BadRequestException(`Zoom token exchange failed: ${error.message}`);
        }
    }

    async regenerateZoomConsentUrl(integrationId: string) {
        const integration = await this.integrationModel.findById(integrationId);
        if (!integration) {
            throw new BadRequestException('Invalid integration ID');
        }

        if (integration.integrationType !== IntegrationType.ZoomMeet && integration.integrationType !== IntegrationType.ZoomMeetPremium) {
            throw new BadRequestException('Consent URL regeneration is only supported for Zoom integration');
        }

        const { clientId } = integration;

        const zoomRedirectUri = process.env.ZOOM_REDIRECT_URI || 'https://portal.talsy.ai/settings/integrations';

        const authUrl = `https://zoom.us/oauth/authorize?response_type=code&client_id=${clientId}&redirect_uri=${encodeURIComponent(
            zoomRedirectUri,
        )}&state=${integration._id.toString()}`;

        return {
            message: 'Consent URL regenerated, redirect user to complete OAuth',
            authUrl,
            integration,
        };
    }

    async handleMicrosoftCallback({ code, state }: MicrosoftOAuthCallbackDto) {
        const integration = await this.integrationModel.findById(new Types.ObjectId(state));
        if (!integration) {
            throw new BadRequestException('Invalid or expired integration state');
        }

        if (!integration.clientId || !integration.clientSecret) {
            throw new Error('Missing required configuration for OAuth.');
        }

        try {
            const tokenUrl = `https://login.microsoftonline.com/${integration.tenantId}/oauth2/v2.0/token`;

            const params = new URLSearchParams();
            params.append('client_id', integration.clientId);
            params.append('scope', 'https://graph.microsoft.com/.default');
            params.append('code', code);
            params.append('redirect_uri', process.env.MS_TEAMS_REDIRECT_URI || 'https://yourfrontend.com/settings/integrations');
            params.append('grant_type', 'authorization_code');
            params.append('client_secret', integration.clientSecret); // ✅ Make sure this is the actual value

            const response = await axios.post(tokenUrl, params.toString(), {
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
            });

            // this.logger.log(`Microsoft token exchange response: ${JSON.stringify(response.data)}`);
            const { access_token, refresh_token, expires_in, scope } = response.data;

            integration.refreshToken = refresh_token;
            integration.scope = scope?.split(' ') ?? [];

            await integration.save();

            return { message: 'Microsoft Teams integration successful', integration };
        } catch (error) {
            this.logger.error(`Microsoft token exchange failed: ${error.message}`, error.stack);
            throw new BadRequestException(`Microsoft token exchange failed: ${error.message}`);
        }
    }

    async regenerateMicrosoftConsentUrl(integrationId: string) {
        const integration = await this.integrationModel.findById(integrationId);
        if (!integration) {
            throw new BadRequestException('Invalid integration ID');
        }

        if (integration.integrationType !== IntegrationType.MicrosoftTeams) {
            throw new BadRequestException('Consent URL regeneration is only supported for Microsoft Teams integration');
        }

        const { clientId, tenantId } = integration;

        const redirectUri = process.env.MS_TEAMS_REDIRECT_URI || 'https://yourfrontend.com/settings/integrations';

        const scopes = [
            'User.Read',
            'OnlineMeetings.ReadWrite',
            'offline_access',
        ];

        if (!clientId || !redirectUri || !tenantId || !integration?._id) {
            throw new Error('Missing required configuration for OAuth.');
        }


        const authUrl = `https://login.microsoftonline.com/${tenantId}/oauth2/v2.0/authorize?` +
            new URLSearchParams({
                client_id: clientId,
                response_type: 'code',
                redirect_uri: redirectUri,
                response_mode: 'query',
                scope: scopes.join(' '),
                state: integration._id.toString(),
            }).toString();

        return {
            message: 'Microsoft Teams consent URL regenerated. Redirect to complete OAuth.',
            authUrl,
            integration,
        };
    }

    async findAll(page: number, limit: number, org?: string,type? :string) {
        try {
            const filter: any = {isDeleted: false};
            if (org) {
                filter.org = org;
            }
            if(type) {
                filter.type = type;
                filter.refreshToken = { $exists: true, $ne: '' };
            }
            return await this.integrationModel.find(filter)
                .populate({ path: 'createdBy', select: '_id roles firstName' })
                .skip((page - 1) * limit)
                .limit(limit)
                .sort({ createdAt: -1 })
                .exec();
        } catch (error) {
            this.logger.error(`Failed to fetch integrations: ${error.message}`, error.stack);
            throw new BadRequestException(`Error fetching integrations. ${error.message}`);
        }
    }

    async findByUser(page: number, limit: number, user: any) {
        try {
            return await this.integrationModel.find({ createdBy: user._id,isDeleted: false })
                .populate({ path: 'createdBy', select: '_id roles firstName' })
                .skip((page - 1) * limit)
                .limit(limit)
                .sort({ createdAt: -1 })
                .exec();
        } catch (error) {
            this.logger.error(`Failed to fetch integrations: ${error.message}`, error.stack);
            throw new BadRequestException(`Error fetching integrations. ${error.message}`);
        }
    }

    async findOne(integrationId: Types.ObjectId) {
        try {
            const integration = await this.integrationModel.findById(integrationId);
            if (!integration) {
                throw new NotFoundException('Integration not found');
            }
            return integration;
        } catch (error) {
            this.logger.error(`Failed to find integration: ${error.message}`, error.stack);
            if (error instanceof NotFoundException) {
                throw error;
            }
            throw new BadRequestException(`Error retrieving integration. ${error.message}`);
        }
    }

    async update(integrationId: Types.ObjectId, updateDto: UpdateIntegrationDto) {
        try {
            const existingIntegration = await this.integrationModel.findById(integrationId);
            if (!existingIntegration) {
                throw new NotFoundException('Integration not found');
            }
            const isGoogleMeet = existingIntegration.integrationType === IntegrationType.GoogleMeet;
            const clientIdChanged = updateDto.clientId && updateDto.clientId !== existingIntegration.clientId;
            const clientSecretChanged = updateDto.clientSecret && updateDto.clientSecret !== existingIntegration.clientSecret;

            if (isGoogleMeet && (clientIdChanged || clientSecretChanged)) {

                const newClientId = updateDto.clientId;
                const newClientSecret = updateDto.clientSecret;

                try {
                    const oauth2Client = new google.auth.OAuth2(
                        newClientId,
                        newClientSecret,
                        process.env.GOOGLE_REDIRECT_URI || 'https://portal.talsy.ai/settings/integrations', //  redirect URI
                    );

                    const scopes = [
                        'https://www.googleapis.com/auth/calendar',
                        'https://www.googleapis.com/auth/calendar.events',
                    ];

                    const authUrl = oauth2Client.generateAuthUrl({
                        access_type: 'offline',
                        prompt: 'consent',
                        scope: scopes,
                        state: existingIntegration._id.toString(),
                    });

                    const updtaed = await this.integrationModel.findByIdAndUpdate(integrationId, {
                        $unset: { refreshToken: 1 },
                        $set: updateDto
                    });

                    return {
                        message: 'Integration created, redirect user to complete OAuth',
                        authUrl,
                        integration: updtaed
                    };
                } catch (authError) {
                    // Clear the refreshToken if validation fails
                    await this.integrationModel.findByIdAndUpdate(integrationId, {
                        $unset: { refreshToken: 1 },
                        $set: updateDto
                    });
                    throw new BadRequestException(
                        'Invalid Client ID or Secret. Please re-authenticate to regenerate a valid refresh token.'
                    );
                }
            }
            const updated = await this.integrationModel.findByIdAndUpdate(integrationId, updateDto, { new: true });
            if (!updated) {
                throw new NotFoundException('Integration not found');
            }
            return updated;
        } catch (error) {
            this.logger.error(`Failed to update integration: ${error.message}`, error.stack);
            if (error instanceof NotFoundException) {
                throw error;
            }
            throw new BadRequestException(`${error.message}`);
        }
    }

    async remove(integrationId: Types.ObjectId) {
        try {
            // const deleted = await this.integrationModel.findByIdAndDelete(integrationId);
            const deleted = await this.integrationModel.findByIdAndUpdate(
                integrationId,
                { isDeleted: true },
                { new: true }
            );
            if (!deleted) {
                throw new NotFoundException('Integration not found');
            }
            return deleted;
        } catch (error) {
            this.logger.error(`Failed to delete integration: ${error.message}`, error.stack);
            if (error instanceof NotFoundException) {
                throw error;
            }
            throw new BadRequestException(`Error deleting integration. ${error.message}`);
        }
    }
    async validateWhatsAppToken(whatsappAccessToken: string) {
        try {
            // WhatsApp API URL to check token
            const response = await axios.get('https://graph.facebook.com/v14.0/me', {
                headers: {
                    Authorization: `Bearer ${whatsappAccessToken}`,
                },
            });

            if (response.status === 200) {
                return {
                    message: 'WhatsApp access token is valid.',
                    data: response.data,
                };
            }

            throw new BadRequestException('Invalid WhatsApp access token.');
        } catch (error) {
            throw new BadRequestException(`${error.message}`);
        }
    }

    private async fetchWhatsAppTemplates(accessToken: string, wabaId: string): Promise<string[]> {
        try {
            // Step 1: Get message templates from WABA
            const templatesRes = await axios.get(
                `https://graph.facebook.com/v19.0/${wabaId}/message_templates`,
                {
                    headers: { Authorization: `Bearer ${accessToken}` },
                }
            );

            this.logger.log(`WhatsApp templates response: ${JSON.stringify(templatesRes.data)}`);
            const templates = templatesRes.data?.data;

            if (!templates || !Array.isArray(templates)) {
                throw new BadRequestException('No WhatsApp message templates found for the business account.');
            }

            // Step 2: Extract and return template names
            return templates.map((template: any) => template.name);

        } catch (error: any) {
            const metaError = error.response?.data?.error;
            const errorMsg = metaError?.message || error.message;

            console.error('Error fetching WhatsApp templates:', metaError || error.message);

            if (metaError?.code === 100 && metaError?.error_subcode === 33) {
                throw new BadRequestException('Invalid or unauthorized WABA ID or access token.');
            }

            throw new BadRequestException(`Meta API Error: ${errorMsg}`);
        }
    }

    async sendWhatsAppMessage(sendWhatsAppDto: SendWhatsAppDto, user: any) {
        try {
            // Find the WhatsApp integration for the user/org
            const integration = await this.integrationModel.findOne({
                integrationType: IntegrationType.WhatsApp,
                createdBy: user._id,
                ...(user.org?._id && { org: user.org._id }),
            });

            if (!integration) {
                throw new NotFoundException('WhatsApp integration not found');
            }

            if (!integration.whatsappAccessToken || !integration.whatsappBusinessAccountId || !integration.whatsappPhoneNumber) {
                throw new BadRequestException('WhatsApp access token or business account ID is missing');
            }
            // Get the phone number ID using the business account ID and access token
            const phoneNumberId = await this.getWhatsAppPhoneNumberId(
                integration.whatsappAccessToken,
                integration.whatsappBusinessAccountId,
                integration.whatsappPhoneNumber
            );

            this.logger.log(`WhatsApp phone number ID: ${phoneNumberId}`);
            if (!phoneNumberId) {
                throw new BadRequestException('Failed to retrieve WhatsApp phone number ID');
            }

            // Prepare the message payload based on message type
            let messagePayload: any;

            if (sendWhatsAppDto.type === 'template') {
                // Validate template message
                if (!sendWhatsAppDto.templateName) {
                    throw new BadRequestException('Template name is required for template messages');
                }

                messagePayload = {
                    messaging_product: 'whatsapp',
                    to: this.formatPhoneNumber(sendWhatsAppDto.to),
                    type: 'template',
                    template: {
                        name: sendWhatsAppDto.templateName,
                        language: {
                            code: sendWhatsAppDto.languageCode || 'en_US'
                        }
                    }
                };

                // Add components if provided
                if (sendWhatsAppDto.components && sendWhatsAppDto.components.length > 0) {
                    messagePayload.template.components = sendWhatsAppDto.components;
                }
            } else {
                // Validate text message
                if (!sendWhatsAppDto.text) {
                    throw new BadRequestException('Text content is required for text messages');
                }

                messagePayload = {
                    messaging_product: 'whatsapp',
                    to: this.formatPhoneNumber(sendWhatsAppDto.to),
                    type: 'text',
                    text: {
                        body: sendWhatsAppDto.text
                    }
                };
            }

            // Send the message to WhatsApp API
            const response = await axios.post(
                `https://graph.facebook.com/v17.0/${phoneNumberId}/messages`,
                messagePayload,
                {
                    headers: {
                        'Authorization': `Bearer ${integration.whatsappAccessToken}`,
                        'Content-Type': 'application/json'
                    }
                }
            );

            return {
                message: 'WhatsApp message sent successfully',
                whatsappResponse: response.data
            };
        } catch (error) {
            this.logger.error(`Failed to send WhatsApp message: ${error.message}`, error.stack);
            if (error instanceof HttpException) {
                throw error;
            }
            throw new BadRequestException(`Error sending WhatsApp message: ${error.message}`);
        }
    }

    // Helper method to format phone number
    private formatPhoneNumber(phoneNumber: string): string {
        // Remove any non-digit characters from the phone number
        const digits = phoneNumber.replace(/\D/g, '');

        // Ensure the phone number has the country code
        return digits;
    }

    // Helper method to get WhatsApp phone number ID
    private async getWhatsAppPhoneNumberId(
        accessToken: string,
        businessAccountId: string,
        phoneNumber: string
    ): Promise<string | null> {
        try {
            // Fetch phone numbers associated with the business account
            const response = await axios.get(
                `https://graph.facebook.com/v17.0/${businessAccountId}/phone_numbers`,
                {
                    headers: {
                        'Authorization': `Bearer ${accessToken}`
                    }
                }
            );

            // Find the phone number ID that matches our stored phone number
            const formattedStoredNumber = phoneNumber.replace(/\D/g, '');

            if (response.data && response.data.data && response.data.data.length > 0) {
                for (const phone of response.data.data) {
                    const formattedDisplayNumber = phone.display_phone_number.replace(/\D/g, '');
                    if (formattedDisplayNumber.includes(formattedStoredNumber) ||
                        formattedStoredNumber.includes(formattedDisplayNumber)) {
                        return phone.id;
                    }
                }
            }

            // If specific phone number not found but there's at least one number, use the first one
            if (response.data && response.data.data && response.data.data.length > 0) {
                this.logger.warn(`Exact phone number match not found. Using first available phone number ID.`);
                return response.data.data[0].id;
            }

            throw new Error('No phone numbers found for the WhatsApp Business Account');
        } catch (error) {
            this.logger.error(`Failed to get WhatsApp phone number ID: ${error.message}`, error.stack);
            return null;
        }
    }

}


