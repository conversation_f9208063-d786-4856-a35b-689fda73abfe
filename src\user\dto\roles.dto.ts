import { ApiProperty } from "@nestjs/swagger";
import { <PERSON>Array, IsEnum, IsNotEmpty } from 'class-validator';
import { Role } from "src/auth/enums/role.enum";


// Read about operators of swagger here - https://docs.nestjs.com/openapi/decorators
export class RolesDto {

    @ApiProperty({
        type: [Role],
        required: true,
        description: 'Roles assigned to the user',
        enum: Role,
        default: [Role.User],
        isArray: true,
    })
    @IsArray()
    @IsNotEmpty()
    @IsEnum(Role, { each: true })
    roles: Role[];

}
