
import { Prop, Schema, SchemaFactory, raw } from "@nestjs/mongoose";
import { Types, Schema as MongooseSchema } from "mongoose";
import { AddressInformation } from "src/common/schemas/address-information.schema";
import { ContactInformation } from "src/common/schemas/contact-information.schema";
import { Country } from "src/country/schemas/country.schema";
import { EvaluationForm } from "src/evaluation-form/schemas/evaluation-form.schema";
import { FileMetadata } from "src/file-upload/schemas/file-metadata.schema";
import { JobLocation } from "src/job-location/schemas/job-location.schema";
import { Org } from "src/org/schemas/org.schema";
import { Currency, FieldType, Gender, WorkMode } from "src/shared/constants";
import { City } from "src/state/schemas/city.schema";
import { State } from "src/state/schemas/state.schema";
import { BasicUser } from "src/user/schemas/basic-user.schema";
import { Document } from 'mongoose';


@Schema()
export class EducationQualification extends Document {
  @Prop({
    type: String,
    required: false,
    trim: true,
  })
  courseName?: string;

  @Prop({
    type: String,
    required: false,
    trim: true,
  })
  university?: string;

  @Prop({
    type: Date,
    required: false,
    default: null,
  })
  startDate?: Date;

  @Prop({
    type: Date,
    required: false,
    default: null,
  })
  endDate?: Date;
}

export const EducationQualificationSchema = SchemaFactory.createForClass(EducationQualification);


@Schema({
    timestamps: true
})
export class WorkExperience extends Document {
    @Prop({
        type: String,
        required: false,
        trim: true,
    })
    companyName?: string;

    @Prop({
        type: Date,
        required: false,
    })
    jobStartDate?: Date;

    @Prop({
        type: Date,
        required: false,
    })
    jobEndDate?: Date;

    @Prop({
        type: Boolean,
        required: false,
    })
    currentlyWorking?: boolean;
}

export const WorkExperienceSchema = SchemaFactory.createForClass(WorkExperience);

@Schema({
    timestamps: true
})
export class Bench {
    @Prop({
        type: String,
        required: false,
        trim: true
      })
      description?: string;
    

    @Prop({
        type: Types.ObjectId,
        required: false,
    })
    resumeMetadata?: FileMetadata;

    @Prop({
        type: Types.ObjectId,
        required: false,
    })
    candidateImage?: FileMetadata;


    @Prop({
        type: String,
        required: true,
        trim: true,
    })
    firstName: string;

    @Prop({
        type: String,
        required: true,
        trim: true,
    })
    lastName: string;

    @Prop({
        type: String,
        required: false,
        match: /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/, // Validate PAN Number format
        uppercase: true,
    })
    panNumber: string;

    @Prop({
        type: ContactInformation,
        required: false,
        ref: 'ContactInformation'
    })
    contactDetails?: ContactInformation;

    @Prop({ type: AddressInformation, required: false })
    contactAddress?: AddressInformation;

    @Prop({
        type: Types.ObjectId,
        required: false,
        ref: 'Country'
    })
    country?: Country;

    @Prop({
        type: Types.ObjectId,
        required: false,
        ref: 'State'
    })
    state?: State;

    @Prop({
        type: Types.ObjectId,
        required: false,
        ref: 'City'
    })
    city?: City;

    @Prop({
        type: Date,
        required: false,
    })
    dob?: Date;

    @Prop({
        type: String,
        required: false,
        trim: true,
        enum: Object.values(Gender),
    })
    gender?: string;

    @Prop({
        type: Boolean,
        required: false,
    })
    disability?: boolean;

    @Prop({
        type: String,
        required: false,
        trim: true,
    })
    linkedInUrl?: string;

    @Prop({
        type: String,
        required: false,
        trim: true,
    })
    websiteOrBlogUrl?: string;

    @Prop({
        type: Boolean,
        required: false,
    })
    isExperienced?: boolean;

    @Prop({
        type: Number,
        required: false,
    })
    yearsOfExperience?: number;

    @Prop({
        type: [WorkExperienceSchema],
        required: false,
    })
    workExperience?: WorkExperience[];

    @Prop({
        type: [EducationQualificationSchema],
        required: false,
      })
      educationQualification?: EducationQualification[];

    @Prop({
        type: String,
        required: false,
        trim: true,
    })
    middleName?: string;

    @Prop({
        type: String,
        required: false,
        trim: true,
    })
    designation?: string;

    @Prop({ type: String, required: false, trim: true })
    mobileNumber?: string;


    @Prop({
        type: Boolean,
        required: false,
        default: false
    })
    isScreenSelected?: boolean;

    @Prop({
        type: Boolean,
        required: false,
        default: false
    })
    isRejected?: boolean;

    @Prop({
        type: Boolean,
        required: false,
        default: false
    })
    bgvVerified?: boolean;

    @Prop({
        type: Number,
        required: false,
        default: false
    })
    communicationSkillRating?: number;

    @Prop({
        required: false,
        type: Boolean,
        default: false
    })
    isDraft?: boolean;

    @Prop({
        type: String,
        required: false,
        trim: true,
    })
    preferredLocation?: string;

    @Prop({
        type: Number,
        required: false,
    })
    currentCTC?: number;

    @Prop({
        type: Number,
        required: false,
    })
    expectedCTC?: number;

    @Prop({
        type: Number,
        required: false,
    })
    ctcPercentage?: number;

    @Prop({
        type: String,
        required: false,
        trim: true,
        default: Currency.INR,
        enum: Object.values(Currency),
    })
    currency?: string;

    @Prop({
        type: Boolean,
        required: false,
    })
    companyNorms?: boolean;

    @Prop({
        required: false,
        type: Types.ObjectId, ref: 'Org'
    })
    org?: Org;

    @Prop({
        required: true,
        type: Types.ObjectId, ref: 'BasicUser'
    })
    createdBy: BasicUser;

    // @Prop({
    //     required: false,
    //     type: [Types.ObjectId], ref: 'EvaluationForm'
    // })
    // evaluationForm?: EvaluationForm[];

    @Prop({
        type: [
          {
            skill: { type: String, required: false, trim: true },
            years: { type: Number, required: false },
            months: { type: Number, required: false },
            rating: { type: Number, required: false },
            isPrimary: { type: Boolean, required: false, default: false },
          },
        ],
        required: false,
      })
      skills?: {
        skill?: string;
        years?: number;
        months?: number;
        rating?: number;
        isPrimary?: boolean;
      }[];
    

    @Prop({
        type: Number,
        required: false,
    })
    noticePeriodDays?: number;

    @Prop({
        type: Boolean,
        required: false,
    })
    servingNoticePeriod?: boolean;

    @Prop({
        type: Date,
        required: false,
    })
    lastWorkingDate?: Date;

    @Prop({
        type: String,
        required: false,
        trim: true,
    })
    currentLocation?: string;

    @Prop({
        type: Boolean,
        required: false,
    })
    willingToRelocate?: boolean;

    @Prop({
        required: false,
        type: [Types.ObjectId], ref: 'Re-location'
    })
    reLocation?: JobLocation[];

    
  @Prop({
    required: false,
    type: Boolean,
    default: false
  })
  isDeleted?: boolean;

}

export const BenchSchema = SchemaFactory.createForClass(Bench);