import { <PERSON>, Get, Post, Body, Patch, Param, Delete, UseGuards, Req, Query } from '@nestjs/common';
import { PrivilegeService } from './privilege.service';
import { CreatePrivilegeDto } from './dto/create-privilege.dto';
import { UpdatePrivilegeDto } from './dto/update-privilege.dto';
import { ApiBearerAuth, ApiOperation, ApiParam, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { AuthJwtGuard } from 'src/auth/guards/auth-jwt/auth-jwt.guard';
import { validateObjectId } from 'src/utils/validation.utils';

@Controller('')
@ApiTags('privileges')
export class PrivilegeController {
  constructor(private readonly privilegeService: PrivilegeService) { }

  // @Post()
  // @ApiResponse({ status: 201, description: `Privilege is saved.` })
  // @ApiResponse({ status: 401, description: `Unauthorized.` })
  // @ApiResponse({ status: 400, description: `Bad Request / Data.` })
  // @ApiOperation({ summary: `Create a new Privilege.`, description: `This endpoint allows to create a new privilege.` })
  // @ApiBearerAuth()
  // @UseGuards(AuthJwtGuard)
  // create(@Req() req: any, @Body() createPrivilegeDto: CreatePrivilegeDto) {
  //   // createPrivilegeDto.createdBy = req.user._id;
  //   return this.privilegeService.create(createPrivilegeDto);
  // }

  // @Get('all')
  // @ApiResponse({ status: 200, description: `Privileges retrieved.` })
  // @ApiResponse({ status: 401, description: `Unauthorized.` })
  // @ApiOperation({ summary: `Retrieve all privileges`, description: `This endpoint returns a list of all privileges` })
  // @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number', example: 1 })
  // @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of items per page', example: 10 })
  // @ApiBearerAuth()
  // @UseGuards(AuthJwtGuard)
  // findAll(@Query('page') page: number = 1, @Query('limit') limit: number = 0) {
  //   return this.privilegeService.findAll(page, limit);
  // }

  // @Get(':privilegeId')
  // @ApiResponse({ status: 200, description: `Privilege is retrieved.` })
  // @ApiResponse({ status: 404, description: `Privilege not found.` })
  // @ApiResponse({ status: 401, description: `Unauthorized.` })
  // @ApiOperation({ summary: `Retrieve a privilege by Id`, description: `This endpoint returns a privilege by its Id` })
  // @ApiParam({ name: 'privilegeId', description: `Id of the privilege.` })
  // @ApiBearerAuth()
  // @UseGuards(AuthJwtGuard)
  // findOne(@Param('privilegeId') privilegeId: string) {
  //   const objId = validateObjectId(privilegeId);
  //   return this.privilegeService.findOne(objId);
  // }

  // @Patch(':privilegeId')
  // @ApiParam({ name: 'privilegeId', description: `Id of the Privilege.` })
  // @ApiResponse({ status: 200, description: `Privilege updated.` })
  // @ApiResponse({ status: 401, description: `Unauthorized.` })
  // @ApiOperation({ summary: `Update a privilege by id`, description: `This endpoint updates a privilege by Id` })
  // @ApiBearerAuth()
  // @UseGuards(AuthJwtGuard)
  // update(@Param('privilegeId') privilegeId: string, @Body() updatePrivilegeDto: UpdatePrivilegeDto) {
  //   const objId = validateObjectId(privilegeId);
  //   return this.privilegeService.update(objId, updatePrivilegeDto);
  // }

  // @Delete(':privilegeId')
  // @ApiResponse({ status: 200, description: `Privilege is deleted.` })
  // @ApiResponse({ status: 401, description: `Unauthorized.` })
  // @ApiResponse({ status: 403, description: `Forbidden, only Admin can delete privileges.` })
  // @ApiParam({ name: 'privilegeId', description: `Id of the privilege.` })
  // @ApiOperation({ summary: `Delete a privilege by Id`, description: `This endpoint deletes a privilege by Id. Accessible only for Admin.` })
  // @ApiBearerAuth()
  // @UseGuards(AuthJwtGuard)
  // remove(@Param('privilegeId') privilegeId: string) {
  //   const objId = validateObjectId(privilegeId);
  //   return this.privilegeService.remove(objId);
  // }

  // @Post('bulk-add')
  // @ApiResponse({ status: 201, description: `Privileges are saved.` })
  // @ApiResponse({ status: 401, description: `Unauthorized.` })
  // @ApiResponse({ status: 400, description: `Bad Request / Data.` })
  // @ApiOperation({
  //   summary: `Bulk add Privileges.`,
  //   description: `This endpoint allows adding multiple privileges at once, skipping existing ones.`,
  // })
  // @ApiBearerAuth()
  // @UseGuards(AuthJwtGuard)
  // async bulkCreate(@Req() req: any, @Body() createPrivilegesDto: CreatePrivilegeDto[]) {
  //   return this.privilegeService.bulkCreate(createPrivilegesDto);
  // }
}
