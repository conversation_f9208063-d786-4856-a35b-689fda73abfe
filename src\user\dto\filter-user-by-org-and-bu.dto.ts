import { Transform } from 'class-transformer';
import { <PERSON>Optional, IsString, <PERSON>Int, Min } from 'class-validator';

export class FilterUsersDto {
  
  @IsOptional()
  @IsString()
  @Transform(({ value }) => (value === undefined || value === '0' ? '' : value))
  org?: string;
  
  @IsOptional()
  @IsString()
  @Transform(({ value }) => (value === undefined || value === '0' ? '' : value))
  businessUnit?: string;

  @IsOptional()
  @Transform(({ value }) => {
    return value > 0 ? value : 1;
  })
  page: number = 1;


  @IsOptional()
  @Transform(({ value }) => {
    return value > 0 ? value : 10;
  })
  limit: number = 10;
}
