import {
  Controller,
  Post,
  Get,
  Patch,
  Delete,
  Body,
  Param,
  Query,
  Req,
  UseGuards,
  Logger,
  BadRequestException,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { RateCardCategoryService } from './rate-card-category.service';
import { CreateRateCardCategoryDto } from './dto/create-rate-card-category.dto';
import { UpdateRateCardCategoryDto } from './dto/update-rate-card-category.dto';
import { AuthJwtGuard } from 'src/auth/guards/auth-jwt/auth-jwt.guard';
import { RolesGuard } from 'src/auth/guards/roles/roles.guard';
import { validateObjectId } from 'src/utils/validation.utils';
import { Roles } from 'src/auth/decorators/roles.decorator';
import { FilterRateCardCategoryDto } from './dto/filter-rate-card-category.dto';

@ApiTags('rate-card-category')
@Controller('')
export class RateCardCategoryController {
  private readonly logger = new Logger(RateCardCategoryController.name);

  constructor(private readonly rateCardCategoryService: RateCardCategoryService) { }

  @Post()
  @ApiOperation({ summary: 'Create a new rate card category' })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  @Roles()
  @ApiResponse({ status: 201, description: 'Rate card category created successfully.' })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  create(@Req() req: any, @Body() createRateCardCategoryDto: CreateRateCardCategoryDto) {

    createRateCardCategoryDto.createdBy = req.user._id;
    if (req.user.org._id) {
      createRateCardCategoryDto.org = req.user.org._id
    }
    else {
      throw new BadRequestException('User does not have org id');
    }
    return this.rateCardCategoryService.create(createRateCardCategoryDto, req.user);
  }

  @Get()
  @ApiOperation({ summary: 'Get all rate card categories' })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  @Roles()
  @ApiResponse({ status: 200, description: 'Rate card categories retrieved successfully.' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  findAll(@Req() req: any, @Query() query: FilterRateCardCategoryDto) {
    if (!query.org && req.user.org) {
      query.org = req.user.org._id;
    }
    return this.rateCardCategoryService.findAll(query);
  }

  @Get(':rateCardCategoryId')
  @ApiOperation({ summary: 'Get rate card category by ID' })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  @Roles()
  @ApiResponse({ status: 200, description: 'Rate card category retrieved successfully.' })
  @ApiResponse({ status: 404, description: 'Rate card category not found.' })
  @ApiParam({ name: 'rateCardCategoryId', description: 'ID of the rate card category' })
  findOne(@Param('rateCardCategoryId') rateCardCategoryId: string) {
    return this.rateCardCategoryService.findOne(validateObjectId(rateCardCategoryId));
  }

  @Get('count')
  @ApiOperation({ summary: 'Get count of rate card categories' })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  @Roles()
  @ApiResponse({ status: 200, description: 'Count retrieved successfully.' })
  getCount(@Req() req: any, @Query() query: FilterRateCardCategoryDto) {
    if (!query.org && req.user.org) {
      query.org = req.user.org._id;
    }
    return this.rateCardCategoryService.countAll(query);
  }

  @Patch(':rateCardCategoryId')
  @ApiOperation({ summary: 'Update rate card category by ID' })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  @Roles()
  @ApiResponse({ status: 200, description: 'Rate card category updated successfully.' })
  @ApiResponse({ status: 404, description: 'Rate card category not found.' })
  @ApiParam({ name: 'rateCardCategoryId', description: 'ID of the rate card category' })
  update(@Req() req: any, @Param('rateCardCategoryId') rateCardCategoryId: string, @Body() dto: UpdateRateCardCategoryDto) {
    return this.rateCardCategoryService.update(validateObjectId(rateCardCategoryId), dto, req.user);
  }

  @Delete(':rateCardCategoryId/hard-delete')
  @ApiOperation({ summary: 'Hard delete rate card category by ID' })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  @Roles()
  @ApiResponse({ status: 200, description: 'Rate card category hard deleted successfully.' })
  @ApiResponse({ status: 404, description: 'Rate card category not found.' })
  @ApiParam({ name: 'id', description: 'ID of the rate card category' })
  hardDelete(@Param('rateCardCategoryId') rateCardCategoryId: string, @Req() req: any) {
    return this.rateCardCategoryService.hardDelete(validateObjectId(rateCardCategoryId), req.user);
  }

  @Delete(':rateCardCategoryId/soft-delete')
  @ApiOperation({ summary: 'Soft delete rate card category by ID' })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  @Roles()
  @ApiResponse({ status: 200, description: 'Rate card category soft deleted successfully.' })
  @ApiResponse({ status: 404, description: 'Rate card category not found.' })
  @ApiParam({ name: 'rateCardCategoryId', description: 'ID of the rate card category' })
  softDelete(@Param('rateCardCategoryId') rateCardCategoryId: string, @Req() req: any) {
    return this.rateCardCategoryService.softDelete(validateObjectId(rateCardCategoryId), req.user);
  }

  @Get('root-categories')
  @ApiOperation({ summary: 'Get root-level rate card categories (no parentCategory)' })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  @Roles()
  @ApiResponse({ status: 200, description: 'Root-level categories retrieved successfully.' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  getRootCategories(@Req() req: any, @Query() query: FilterRateCardCategoryDto) {
    if (!query.org && req.user.org) {
      query.org = req.user.org._id;
    }
    return this.rateCardCategoryService.getRootCategories(query);
  }

}
