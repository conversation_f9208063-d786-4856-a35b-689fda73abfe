import { forwardRef, Module } from '@nestjs/common';
import { WorkflowService } from './workflow.service';
import { WorkflowController } from './workflow.controller';
import { ConfigModule } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { Workflow, WorkflowSchema } from './schemas/workflow.schema';
import { MongooseModule } from '@nestjs/mongoose';
import { StageModule } from 'src/stage/stage.module';
import { EmailTemplate, EmailTemplateSchema } from 'src/email-template-builder/schemas/email-template-builder.schema';
import { Placeholder, PlaceholderSchema } from 'src/org/schemas/org.schema';
import { JobApplication, JobApplicationSchema } from 'src/job-application-form/schemas/job-application.schema';
import { EndpointsRolesModule } from 'src/endpoints-roles/endpoints-roles.module';
import { JobApplicationFormModule } from 'src/job-application-form/job-application-form.module';
import { InterviewModule } from 'src/interview/interview.module';
@Module({
  controllers: [WorkflowController],
  providers: [WorkflowService],
  imports: [
    ConfigModule,
    StageModule,
    forwardRef(() => JobApplicationFormModule),
    forwardRef(() => InterviewModule),
    JwtModule, EndpointsRolesModule,
    MongooseModule.forFeature([
      { name: Workflow.name, schema: WorkflowSchema },
      { name: EmailTemplate.name, schema: EmailTemplateSchema },
      { name: Placeholder.name, schema: PlaceholderSchema },
      { name: JobApplication.name, schema: JobApplicationSchema },
    ])
  ],
  exports: [MongooseModule, WorkflowService]
})
export class WorkflowModule { }
