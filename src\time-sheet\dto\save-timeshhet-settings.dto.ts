import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsArray, IsDateString, IsOptional, <PERSON>N<PERSON>ber, <PERSON>, <PERSON> } from 'class-validator';

export class SaveTimeSheetSettingsDto {
    @ApiProperty({ type: [String], required: false })
    @IsOptional()
    projectId?: string[];

    @ApiProperty({ type: [String], required: false })
    @IsOptional()
    taskId?: string[];


    @IsString()
    weekStartDay: string;

    @IsArray()
    @IsString({ each: true })
    weekends: string[];



    monthlyCycleStartDay: number;

    @IsOptional()

    monthlyCycleEndDay?: number;
}
