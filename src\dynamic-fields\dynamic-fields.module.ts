import { Lo<PERSON>, Modu<PERSON> } from '@nestjs/common';
import { DynamicFieldService } from './dynamic-fields.service'; 
import { DynamicFieldsController } from './dynamic-fields.controller'; 
import { ConfigModule, ConfigService } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { UserModule } from 'src/user/user.module';
import { MongooseModule, getConnectionToken } from '@nestjs/mongoose';
import { DynamicField, DynamicFieldSchema} from './schemas/dynamic-field.schema';
import { StageModule } from 'src/stage/stage.module';
import { WorkflowModule } from 'src/workflow/workflow.module';
import { snakeCase } from 'lodash';
import { EmailTemplate, EmailTemplateSchema } from 'src/email-template-builder/schemas/email-template-builder.schema';
import { Placeholder, PlaceholderSchema } from 'src/org/schemas/org.schema';
import { BasicUser, BasicUserSchema } from 'src/user/schemas/basic-user.schema';
import { Job, JobSchema } from 'src/job/schemas/job.schema';
import { Offer, OfferSchema } from 'src/offer/schemas/offer.schema';
import { RecruiterTarget, RecruiterTargetSchema } from 'src/recruiter-target/schemas/recuriter-target.schema';
import { Interview, InterviewSchema } from 'src/interview/schemas/interview.schema';
import { EndpointsRolesModule } from 'src/endpoints-roles/endpoints-roles.module';
@Module({
  controllers: [DynamicFieldsController],
  imports: [
    ConfigModule,
    JwtModule, EndpointsRolesModule,
    UserModule,
    StageModule,
    WorkflowModule,
    // MongooseModule.forFeature([{ name:JobApplication.name, schema: JobApplicationSchema}])
    // MongooseModule.forFeature([{ name: DynamicField.name, schema: DynamicFieldSchema }]),
    MongooseModule.forFeature([
      { name: EmailTemplate.name, schema: EmailTemplateSchema },
      { name: Placeholder.name, schema: PlaceholderSchema },
      { name: Job.name, schema: JobSchema },
      { name: Offer.name, schema: OfferSchema },
      { name: Interview.name, schema: InterviewSchema },
      { name: RecruiterTarget.name, schema: RecruiterTargetSchema }
    ]),
    MongooseModule.forFeatureAsync([
      {
        name: DynamicField.name,
        imports: [ConfigModule],
        useFactory: (configService: ConfigService) => {
          const logger = new Logger('DynamicFieldPreHook')
          const schema = DynamicFieldSchema;
          logger.log("pre hook on DynamicField schema")
          // Pre-save hook to set `fieldName` from `title`
          schema.pre('save', function (next) {
            logger.debug("pre hook on DynamicField schema")

            this.title = snakeCase(this.title);  // Convert title to snake_case

            next();
          });

          return schema;
        },
        inject: [getConnectionToken(), ConfigService],
      },
    ]),


  ],
  providers: [DynamicFieldService],
})
export class DynamicFieldModule { }
const AutoIncrementFactory = require('mongoose-sequence');