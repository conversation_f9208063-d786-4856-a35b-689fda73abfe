import {
    IsMongoId,
    IsNotEmpty,
    IsObject,
    IsString,
    ValidateIf,
    IsOptional,
    IsArray,
    IsDefined,
  } from 'class-validator';
  import { Transform, TransformFnParams } from 'class-transformer';
  import { ApiHideProperty, ApiProperty } from '@nestjs/swagger';
  import { sanitizeWithStyle } from "src/utils/sanitize-with-style";
  
  class SkillQA {
    @IsNotEmpty()
    @IsString()
    question: string;
  
    @ValidateIf((o) => o.answer !== undefined && o.answer !== null && o.answer !== '')
    @IsString()
    answer?: string;
  }
  
  export class CreateAIRecruiterQADto {
    @ApiProperty({ type: String, required: true, description: 'Job ID' })
    @IsMongoId()
    @IsNotEmpty()
    jobId: string;
  
    @ApiHideProperty()
    @IsOptional()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    createdBy?: string;
  
    @IsObject()
    @IsDefined()
    skillQAPairs: Record<string, SkillQA[]>;

    @ApiProperty({
      type: String,
      required: false,
    })
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    @IsString()
    @IsOptional()
    @IsMongoId()
    QAMetadata?: string;

    @ApiProperty({ description: 'Array of Evaluation Topics for AI', type: [String], required: false })
    @IsArray()
    @IsString({ each: true })
    @IsOptional()
    @Transform((params: TransformFnParams) => params.value.map((value: string) => sanitizeWithStyle(value)))
    evaluationCriteria?: string[];

  }
  