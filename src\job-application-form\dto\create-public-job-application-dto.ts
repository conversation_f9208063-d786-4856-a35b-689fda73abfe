import { ApiProperty } from "@nestjs/swagger";
import { IsEmail, IsEnum, IsISO8601, IsMongoId, IsNotEmpty, IsOptional, IsString, Length, ValidateNested } from "class-validator";
import { Transform, TransformFnParams, Type } from 'class-transformer';
import { sanitizeWithStyle } from "src/utils/sanitize-with-style";
import { Gender } from "src/shared/constants";
import { ContactInformationDto } from "src/common/dto/contact-information.dto";


export class CreatePublicJobApplicationFormDto {
    @ApiProperty({
        type: String,
        required: true,
    })
    @IsNotEmpty()
    @IsMongoId()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    jobId: string;

    @ApiProperty({
        type: String,
        required: true,
    })
    @IsMongoId()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    workflow: string;

    @ApiProperty({
        type: String,
        required: true,
    })
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    @IsString()
    @IsNotEmpty()
    @IsMongoId()
    resumeMetadata?: string;

    @ApiProperty({
        type: String,
        required: true,
    })
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    @IsString()
    firstName: string;

    @ApiProperty({
        type: String,
        required: true,
    })
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    @IsString()
    lastName: string;

    @ApiProperty({
        type: Date,
        required: false,
        default: new Date(Date.UTC(new Date().getUTCFullYear(), new Date().getUTCMonth()))
    })
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    @IsISO8601({ strict: true })
    @Length(10, 24)
    @IsOptional()
    dob?: string;

    @ApiProperty({
        type: String,
        required: false,
        enum: Gender,
    })
    @IsOptional()
    @IsEnum(Gender)
    gender?: Gender;

    @ApiProperty({
        type: String,
        required: false,
    })
    @IsOptional()
    @IsMongoId()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    org?: string;

    //candidate email and phone number
    @ApiProperty({
        type: ContactInformationDto,
        required: false,
        description: 'Contact information of job applicant'
    })
    @IsOptional()
    @ValidateNested()
    @Type(() => ContactInformationDto)
    contactDetails?: ContactInformationDto;
}








