import { ApiHideProperty, ApiProperty } from "@nestjs/swagger";
import { IsEnum, IsNotEmpty, IsOptional, IsString } from "class-validator";
import { AccountStatus, HeadCount } from "src/shared/constants";
import { Transform, TransformFnParams } from 'class-transformer';
import { sanitizeWithStyle } from "src/utils/sanitize-with-style";

export class CreateAccountDto {

    @ApiProperty({
        type: String,
        required: true,
        description: 'Account Name',
    })
    @IsNotEmpty()
    @IsString()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    name: string;

    @ApiProperty({ type: String, required: false, description: 'Reference to the AccountId' })
    @IsOptional()
    @IsString()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    parentAccount?: string;

    @ApiProperty({
        type: String,
        required: false,
        description: 'Website url',
    })
    @IsOptional()
    @IsString()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    websiteUrl?: string;

    @ApiProperty({
        type: String,
        required: false,
        description: 'Contact number',
    })
    @IsOptional()
    @IsString()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    contactNumber?: string;

    @ApiProperty({ type: String, description: ' Account Industry' })
    @IsOptional()
    @IsString()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    industry?: string;

    @ApiProperty({
        type: String,
        required: false,
        default: HeadCount.NOT_SPECIFIED,
        enum: HeadCount,
        description: 'Number of employees',
    })
    @IsOptional()
    @IsEnum(HeadCount)
    headCount?: HeadCount;

    @ApiProperty({
        type: String,
        required: false,
        description: 'Account description',
    })
    @IsOptional()
    @IsString()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    description?: string;

    @ApiProperty({ type: String, required: false, description: 'Reference to the Account type' })
    @IsOptional()
    @IsString()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    accountType?: string;

    @ApiProperty({ type: String, required: false, description: 'Account address', })
    @IsOptional()
    @IsString()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    address?: string;

    @ApiProperty({ type: String, required: false, description: 'Account city', })
    @IsOptional()
    @IsString()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    city?: string;

    @ApiProperty({ type: String, required: false, description: 'Account province', })
    @IsOptional()
    @IsString()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    stateOrProvince?: string;

    @ApiProperty({ type: String, required: false, description: 'Account country', })
    @IsOptional()
    @IsString()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    country?: string;

    @ApiProperty({ type: String, required: false, description: 'Account zip code', })
    @IsOptional()
    @IsString()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    pinOrZipCode?: string;


    @ApiProperty({
        required: false,
        type: String, description: 'Reference to the Owner Account',
    })
    @IsOptional()
    @IsString()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    ownedBy?: string;

    @ApiHideProperty()
    @IsOptional()
    @IsString()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    createdBy?: string;

    @ApiProperty({
        type: String,
        required: false,
        default: AccountStatus.PROSPECT,
        enum: AccountStatus,
        description: 'Account status',
    })
    @IsOptional()
    @IsEnum(AccountStatus)
    status?: AccountStatus

}
