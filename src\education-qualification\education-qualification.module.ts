import { Module } from '@nestjs/common';
import { EducationQualificationService } from './education-qualification.service';
import { EducationQualificationController } from './education-qualification.controller';
import { ConfigModule } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { UserModule } from 'src/user/user.module';
import { MongooseModule } from '@nestjs/mongoose';
import { EducationQualification, EducationQualificationSchema } from './schemas/education-qualifaction.schema';
import { EndpointsRolesModule } from 'src/endpoints-roles/endpoints-roles.module';
@Module({
  imports: [
    ConfigModule,
    JwtModule, EndpointsRolesModule,
    UserModule,
    MongooseModule.forFeature([{ name: EducationQualification.name, schema: EducationQualificationSchema }])
  ],
  controllers: [EducationQualificationController],
  providers: [EducationQualificationService],
})
export class EducationQualificationModule {}
