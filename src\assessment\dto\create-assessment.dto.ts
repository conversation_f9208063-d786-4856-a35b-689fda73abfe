import { ApiProperty } from "@nestjs/swagger";
import { Transform, TransformFnParams } from "class-transformer";
import { IsArray, IsBoolean, IsISO8601, IsMongoId, IsNotEmpty, IsOptional, IsString } from "class-validator";
import { sanitizeWithStyle } from "src/utils/sanitize-with-style";
import { FileMetadata } from "src/file-upload/schemas/file-metadata.schema";

export class CreateAssessmentDto {


  @ApiProperty({
    type: String,
    required: true,
    description: ''
  })
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  @IsMongoId()
  jobApplication: string;

  @ApiProperty({
    type: Date,
    required: false,
    description: 'Assesment due date',
    default: new Date(Date.UTC(new Date().getUTCFullYear(), new Date().getUTCMonth(), new Date().getUTCDate(), new Date().getUTCHours(), new Date().getUTCMinutes())),
  })
  @IsISO8601({ strict: true })
  @IsOptional()
  dueDate?: Date;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Assesment link',
  })
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  @IsString()
  @IsOptional()
  assessmentLink?: string;

  @ApiProperty({
    type: [FileMetadata],
    required: false,
  })
  @IsArray()
  @IsOptional()
  assessmentFiles?: FileMetadata[];

}

