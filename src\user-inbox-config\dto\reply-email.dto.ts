import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString, IsArray, IsBoolean, IsEmail, IsMongoId } from 'class-validator';
import { sanitizeWithStyle } from "src/utils/sanitize-with-style";
import { Transform, TransformFnParams } from 'class-transformer';
import { FileMetadata } from 'src/file-upload/schemas/file-metadata.schema';


export class ReplyEmailDto {

  @ApiProperty({
    type: String,
    required: false,
    description: 'Id for the email connection.',
  })
  @IsOptional()
  @IsMongoId()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  userInboxConfigId: string;  // ID of the user's inbox configuration

  @ApiProperty({
    type: String,
    required: true,
    description: 'UID of the email.',
  })
  @IsNotEmpty()
  @IsString()
  uid: string;  // UID of the email being replied to

  @ApiProperty({
    type: [String],
    required: true,
    description: 'Recipient email addresses for the reply.',
  })
  @IsNotEmpty() // Make this field required if you want to enforce it
  @IsArray()
  @IsEmail({}, { each: true })
  @Transform((params: TransformFnParams) => params.value.map((email: string) => sanitizeWithStyle(email).toLowerCase()))
  to: string[]; // List of 'To' recipients

  @ApiProperty({
    type: String,
    required: true,
    description: 'Folders.',
  })
  @IsNotEmpty()
  @IsString()
  folder: string;  // Folder where the email is located (e.g., 'Inbox', 'Sent')

  @ApiProperty({
    type: String,
    required: true,
    description: 'Body of the email.',
  })
  @IsNotEmpty()
  @IsString()
  body: string;  // Reply body (can be plain text or HTML)

  @ApiProperty({
    type: [String],
    required: false,
    description: 'Other reciptents.',
  })
  @IsOptional()
  @IsArray()
  @IsEmail({}, { each: true })
  cc?: string[];  // Optional CC recipients


  @ApiProperty({
    type: [String],
    required: false,
    description: 'Other reciptents.',
  })
  @IsOptional()
  @IsArray()
  @IsEmail({}, { each: true })
  bcc?: string[];  // Optional BCC recipients

  @ApiProperty({
    type: String,
    required: false,
    description: 'Subject of the reply email.',
  })
  @IsOptional()
  @IsString()
  subject: string;  // Optional custom subject for the reply email

  @ApiProperty({
    description: 'Array of attachments',
    type: [FileMetadata],
    required: false,
    example: [
      { filename: 'doc.pdf', contentType: 'application/pdf', content: 'base64string...' },
      { originalName: 'image.png', locationUrl: 'http://server.com/image.png', fileType: 'image/png' },
    ]
  })
  @IsArray()
  @IsOptional()
  attachments?: (FileMetadata | { filename: string, content: string, contentType: string })[];

  @ApiProperty({
    type: Boolean,
    required: true,
    default: false,
    description: 'Indicates reply or reply all.'
  })
  @IsNotEmpty()
  @IsBoolean()
  replyAll: boolean   // New flag to indicate "Reply All"
}
