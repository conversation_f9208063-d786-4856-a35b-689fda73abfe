import { ApiProperty } from "@nestjs/swagger";
import { IsArray, IsEmail, IsMongoId, IsNotEmpty, IsOptional, IsString, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ValidateNested } from 'class-validator';
import { Transform, TransformFnParams, Type } from 'class-transformer';
import sanitizeWithStyle from 'sanitize-html'
import { ContactInformationDto } from "src/common/dto/contact-information.dto";
import { AddressInformationDto } from "src/common/dto/address-information.dto";

// Read about operators of swagger here - https://docs.nestjs.com/openapi/decorators
export class CreateFreelancerDto {

  @ApiProperty({
    type: String,
    required: true,
    description: 'Enter email of your account',
    format: 'email',
    default: '<EMAIL>'
  })
  @IsNotEmpty()
  @IsEmail()
  @IsString()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value).toLowerCase())
  email: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'First name of user'
  })
  @IsString()
  @IsNotEmpty()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  firstName: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Last name of user'
  })
  @IsString()
  @IsNotEmpty()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  lastName: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'MongoDB ID of the country of the user',
  })
  @IsOptional()
  @IsMongoId({ message: 'Country must be a valid MongoDB ObjectId.' })
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  country?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'MongoDB ID of the state of the user',
  })
  @IsOptional()
  @IsMongoId({ message: 'State must be a valid MongoDB ObjectId.' })
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  state?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'MongoDB ID of the city of the user',
  })
  @IsOptional()
  @IsMongoId({ message: 'City must be a valid MongoDB ObjectId.' })
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  city?: string;


  @ApiProperty({
    type: [ContactInformationDto],
    required: false,
    description: 'Contact information of user'
  })
  @IsOptional()
  contactDetails?: ContactInformationDto[];

  @ApiProperty({
    type: AddressInformationDto,
    required: false,
    description: 'Contact address'
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => AddressInformationDto)
  contactAddress?: AddressInformationDto[];

  // @ApiProperty({
  //   type: String,
  //   required: false,
  //   description: 'Org to which the user belongs to',
  // })
  // @IsString()
  // @IsOptional()
  // @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  // org?: string;

  @ApiProperty({ type: [String], description: 'List of Interested Countries', required: false })
  @IsArray()
  @IsOptional()
  @IsString({ each: true })
  @Transform((params: TransformFnParams) => params.value.map((value: string) => sanitizeWithStyle(value)))
  interestedCountries?: string[];

  @ApiProperty({ type: [String], description: 'List of Interested Domains', required: false })
  @IsArray()
  @IsOptional()
  @IsString({ each: true })
  @Transform((params: TransformFnParams) => params.value.map((value: string) => sanitizeWithStyle(value)))
  interestedDomains?: string[];

  @ApiProperty({ type: [String], description: 'List of Interested Skills', required: false })
  @IsArray()
  @IsOptional()
  @IsString({ each: true })
  @Transform((params: TransformFnParams) => params.value.map((value: string) => sanitizeWithStyle(value)))
  interestedSkills?: string[];

  @ApiProperty({
    type: String,
    required: true,
    description: 'Enter user password'
  })
  @MinLength(8)
  @MaxLength(128)
  @IsString()
  @IsNotEmpty()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  password: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'The logo of your org',
  })
  @IsOptional()
  @IsString()
  @IsMongoId({ message: 'Logo must be a valid MongoDB ObjectId.' })
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  logo?: string;

}
