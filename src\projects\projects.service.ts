import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Project } from './schemas/project.schema';
import { ProjectAllocation } from './schemas/project-allocation.schema';
import { CreateProjectDto } from './dto/create-project.dto';
import { AllocateUsersDto, ProjectAllocationDto } from './dto/project-allocation.dto';
import { BasicUser } from 'src/user/schemas/basic-user.schema';
import { QueryProjectDto } from './dto/query-project.dto';
import { Employee } from 'src/employee/schemas/employee.schema';

@Injectable()
export class ProjectsService {
      private readonly logger = new Logger(ProjectsService.name);
    
    constructor(
        @InjectModel(Project.name) private projectModel: Model<Project>,
        @InjectModel(ProjectAllocation.name) private allocationModel: Model<ProjectAllocation>,
        @InjectModel(BasicUser.name) private basicUserModel: Model<BasicUser>,
        @InjectModel(Employee.name) private employeeModel: Model<Employee>,

    ) { }

    async createProject(dto: CreateProjectDto): Promise<Project> {
        const project = await this.projectModel.create(dto);

        // Allocate project manager
        if (dto.projectManagerId) {
            await this.allocationModel.create({
                projectId: project._id,
                userId: dto.projectManagerId,
                roles: ['Project Manager'],
                allocation: 1,
                departments: dto.departments,
                allocatedAt: new Date(),
            });

            await this.basicUserModel.updateOne(
                { _id: dto.projectManagerId },
                { $addToSet: { projectIds: project._id } }
            );
        }

        return project;
    }

    async getAllProjects(user: any, filters: QueryProjectDto): Promise<Project[]> {
        const query: any = { isDeleted: false };

        // Filter by org
        if (user?.org?._id) {
            query.org = user?.org._id;
        }

        if (filters.projectType) {
            query.projectType = filters.projectType;
        }
        if (filters.client) {
            query.client = filters.client;
        }
        if (filters.department) {
            query.departments = filters.department;
        }

        if (filters.startDate) {
            query.startDate = { $gte: new Date(filters.startDate) };
        }
        if (filters.endDate) {
            query.endDate = { ...query.endDate, $lte: new Date(filters.endDate) };
        }

        return this.projectModel.find(query)
            .populate([
                {
                    path: 'projectManagerId',
                    model: 'BasicUser',
                    select: '_id firstName lastName email',
                },
                {
                    path: 'client',
                    model: 'Org',
                    select: '_id title',
                },
                {
                    path: 'org',
                    model: 'Org',
                    select: '_id title',
                },
                // Only if `departments` are ObjectId references (skip otherwise)
                {
                    path: 'departments',
                    model: 'BusinessUnit',
                    select: '_id label',
                    //   strictPopulate: false, // to avoid error if not referenced
                },
            ])
            .exec();
    }

    async getProjectById(id: string) {
        return this.projectModel.findById(id)
            .populate([
                {
                    path: 'projectManagerId',
                    model: 'BasicUser',
                    select: '_id firstName lastName email',
                },
                {
                    path: 'client',
                    model: 'Org',
                    select: '_id title',
                },
                {
                    path: 'org',
                    model: 'Org',
                    select: '_id title',
                },
                // Only if `departments` are ObjectId references (skip otherwise)
                {
                    path: 'departments',
                    model: 'BusinessUnit',
                    select: '_id label',
                    //   strictPopulate: false, // to avoid error if not referenced
                },
            ]).exec();
    }

    async updateProject(id: string, dto: Partial<CreateProjectDto>) {
        return this.projectModel.findByIdAndUpdate(id, dto, { new: true }).exec();
    }

    async deleteProject(id: string) {
        await this.allocationModel.updateMany({ projectId: new Types.ObjectId(id) }, { $set: { isDeleted: true } });
        return this.projectModel.findByIdAndUpdate(id, { $set: { isDeleted: true } });
    }

    async allocateUsers(dto: AllocateUsersDto) {
        const projectId = new Types.ObjectId(dto.projectId);
        const bulkOps = dto.allocations.map((a) => ({
            updateOne: {
                filter: { projectId, userId: a.userId },
                update: {
                    $set: {
                        roles: a.roles,
                        allocation: a.allocation || 1,
                        departments: a.departments || [],
                        reportingTo: a.reportingTo?.map(id => new Types.ObjectId(id)) || [],
                        allocatedAt: new Date(),
                        isDeleted: false,
                    },
                },
                upsert: true,
            },
        }));

        await this.allocationModel.bulkWrite(bulkOps);

        for (const alloc of dto.allocations) {
            await this.basicUserModel.updateOne(
                { _id: alloc.userId },
                { $addToSet: { projectIds: projectId } },
                { upsert: false }
            );
        }

        return { success: true };
    }

    async getAllocationsByUser(userId: string) {
        return this.allocationModel.find({ userId, isDeleted: false })
            .populate([
                {
                    path: 'projectId',
                    populate: [
                        {
                            path: 'projectManagerId',
                            model: 'BasicUser',
                            select: '_id firstName lastName email',
                        },
                        {
                            path: 'client',
                            model: 'Org', // Change if your client is another type
                            select: '_id title',
                        },
                        {
                            path: 'departments',
                            model: 'BusinessUnit', // Change if your client is another type
                            select: '_id label key',
                        },

                        {
                            path: 'org',
                            model: 'Org', // Only if you add this field in schema
                            select: '_id title',
                        },
                    ],
                },
                {
                    path: 'userId',
                    model: 'BasicUser',
                    select: '_id firstName lastName email',
                },
                {
                    path: 'reportingTo',
                    model: 'BasicUser',
                    select: '_id firstName lastName email',
                },
            ])
            .exec();
    }

    async getUsersByProject(projectId: string) {
        return this.allocationModel.find({ projectId: new Types.ObjectId(projectId), isDeleted: false })
            .populate([
                {
                    path: 'userId',
                    model: 'BasicUser',
                    select: '_id firstName lastName email',
                },
                {
                    path: 'reportingTo',
                    model: 'BasicUser',
                    select: '_id firstName lastName email',
                },
            ])
            .exec();
    }

    async updateAllocation(id: string, updates: Partial<ProjectAllocationDto>) {
        return this.allocationModel.findByIdAndUpdate(id, updates, { new: true });
    }

    async deleteAllocation(id: string) {
        return this.allocationModel.findByIdAndUpdate(id, { $set: { isDeleted: true } });
    }

    async findAllEmployeesByProject(user: any, id: string) {
        try {
            const orgId = user.org?._id?.toString();
            if (!orgId) {
                throw new BadRequestException('Invalid organization');
            }

            const existingProject = await this.projectModel.findById(id).exec();

            const conditions: any = {
                payRollOrg: orgId,
                isDeleted: false,
            };

            if (existingProject?.isInternal === true) {
                conditions.endClientOrg = orgId;
            } else {
                conditions.endClientOrg = existingProject?.client;
            }

            const employees = await this.employeeModel.find(conditions)
                .populate([
                    { path: 'payRollOrg', select: '_id title' },
                    { path: 'createdBy', select: '_id firstName lastName email' },
                ]).exec();

            return employees;
        } catch (error) {
            this.logger.error(`An error occurred while fetching bank details: ${error.message}`);
            throw error;

        }
    }

    async findAllEmployeesByClient(user: any, clientId?: string) {
        try {
            const orgId = user.org?._id?.toString();
            if (!orgId) {
                throw new BadRequestException('Invalid organization');
            }

            const conditions: any = {
                payRollOrg: orgId,
                isDeleted: false,
            };

            if (clientId) {
                conditions.endClientOrg = clientId;
            }else{
                conditions.endClientOrg = orgId;
            }

            const employees = await this.employeeModel.find(conditions)
                .populate([
                    { path: 'payRollOrg', select: '_id title' },
                    { path: 'createdBy', select: '_id firstName lastName email' },
                ]).exec();

            return employees;
        } catch (error) {
            this.logger.error(`An error occurred while fetching bank details: ${error.message}`);
            throw error;

        }
    }

}
