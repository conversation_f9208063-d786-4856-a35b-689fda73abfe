import { Injectable, InternalServerErrorException, Logger, NotFoundException } from '@nestjs/common';
import { CreateEvaluationFormDto } from './dto/create-evaluation-form.dto';
import { UpdateEvaluationFormDto } from './dto/update-evaluation-form.dto';
import { EvaluationForm } from './schemas/evaluation-form.schema';
import { Model, Types } from 'mongoose';
import { ConfigService } from '@nestjs/config';
import { InjectModel } from '@nestjs/mongoose';

@Injectable()
export class EvaluationFormService {


  private readonly logger = new Logger(EvaluationFormService.name);

  constructor(private configService: ConfigService, @InjectModel(EvaluationForm.name) private EvaluationFormModel: Model<EvaluationForm>) { }

  async create(createEvaluationFormDto: CreateEvaluationFormDto) {
    try {
      const createdEvaluationForm = new this.EvaluationFormModel(createEvaluationFormDto);
      return await createdEvaluationForm.save();
    } catch (error) {
      this.logger.error(`Failed to add a new skill in evaluation form. ${error}`);
      throw new InternalServerErrorException(`Error while adding a new skill in evaluation form. ${error?.message}`);
    }
  }

  // findAll() {
  //   return `This action returns all evaluationForm`;
  // }

  // findOne(id: number) {
  //   return `This action returns a #${id} evaluationForm`;
  // }

  async update(skillId: Types.ObjectId, updateEvaluationFormDto: UpdateEvaluationFormDto) {
    try {
      const skill = await this.EvaluationFormModel.findById(skillId)
      if (!skill) {
        throw new NotFoundException(`Skill not found with ID ${skillId}`);
      }
      return await this.EvaluationFormModel.findByIdAndUpdate(skillId, updateEvaluationFormDto, { new: true });
    } catch (error) {
      this.logger.error(error);
      this.logger.error(`An error occurred while updating the skill by ID ${skillId}. ${error?.message}`);
      throw error;
    }
  }

  async remove(skillId: Types.ObjectId) {
    try {
      const skill = await this.EvaluationFormModel.findById(skillId)
      if (!skill) {
        throw new NotFoundException(`Skill not found with ID ${skillId}`);
      }
      await this.EvaluationFormModel.deleteOne({ _id: skillId });
      return { message: 'Skill deleted' };
    } catch (error) {
      this.logger.error(`An error occurred while deleting skill by ID ${skillId}. ${error?.message}`);
      throw new InternalServerErrorException('An error occurred while deleting the skill');
    }
  }

  async filterSkill(status: string) {
    try {
      const query: any = {
        isPrimary : false
      };

      if (status == 'primary') {
        query.isPrimary = true;
      }

      const skills = await this.EvaluationFormModel.find(query)

      return skills;

    } catch (error) {
      throw new Error(`Error while fetching skills with "${status}". ${error.message}`);
    }
  }
}
