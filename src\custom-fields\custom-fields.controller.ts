import {
  Controller, Post, Patch, Delete, Get, Body, Param, Query, Req, UseGuards, Logger
} from "@nestjs/common";
import { ApiTags, ApiBearerAuth, Api<PERSON>peration, ApiParam, ApiQuery, ApiResponse } from "@nestjs/swagger";
import { Roles } from "src/auth/decorators/roles.decorator";
import { UpdateCustomFieldDto } from "./dto/update-custom-field.dto";
import { AuthJwtGuard } from "src/auth/guards/auth-jwt/auth-jwt.guard";
import { RolesGuard } from "src/auth/guards/roles/roles.guard";
import { CustomFieldsService } from "./custom-fields.service";
import { CreateCustomFieldDto } from "./dto/create-custom-field.dto";
import { Role } from "src/auth/enums/role.enum";

@Controller('')
@ApiTags('custom-fields')
export class CustomFieldsController {
  constructor(private readonly customFieldService: CustomFieldsService) { }

  private readonly logger = new Logger(CustomFieldsService.name);

  // Add a new custom field
  @Post('')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  @Roles()
  @ApiOperation({ summary: 'Add a new custom field', description: 'Accessible only for Admins.' })
  @ApiResponse({ status: 201, description: 'Custom field added successfully.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Invalid data.' })
  @ApiResponse({ status: 403, description: 'Forbidden, only Admins can use this endpoint.' })
  async addCustomField(@Req() req: any, @Body() createCustomFieldDto: CreateCustomFieldDto) {
    return await this.customFieldService.addCustomFields(createCustomFieldDto, req.user);
  }

  // Update a specific custom field by ID
  // @Patch(':fieldId')
  // @ApiBearerAuth()
  // @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles()
  // @ApiOperation({ summary: 'Update a specific custom field', description: 'Accessible only for Admins.' })
  // @ApiParam({ name: 'fieldId', description: 'ID of the field to be updated' })
  // @ApiResponse({ status: 200, description: 'Custom field updated successfully.' })
  // @ApiResponse({ status: 400, description: 'Bad Request / Invalid data.' })
  // @ApiResponse({ status: 403, description: 'Forbidden, only Admins can use this endpoint.' })
  // @ApiResponse({ status: 404, description: 'Custom field not found.' })
  // async updateCustomField(@Param('fieldId') fieldId: string, @Body() updateCustomFieldDto: UpdateCustomFieldDto) {
  //   return await this.customFieldService.updateCustomField(fieldId, updateCustomFieldDto);
  // }

  // Delete a specific custom field by ID
  @Delete(':fieldId')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  @Roles()
  @ApiOperation({ summary: 'Delete a specific custom field', description: 'Accessible only for Admins.' })
  @ApiParam({ name: 'fieldId', description: 'ID of the field to be deleted' })
  @ApiResponse({ status: 200, description: 'Custom field deleted successfully.' })
  @ApiResponse({ status: 403, description: 'Forbidden, only Admins can use this endpoint.' })
  @ApiResponse({ status: 404, description: 'Custom field not found.' })
  async deleteCustomField(@Param('fieldId') fieldId: string) {
    return await this.customFieldService.deleteCustomField(fieldId);
  }

  // Get all custom fields
  @Get('all')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  @Roles()
  @ApiOperation({ summary: 'Get all custom fields for an organization', description: 'Accessible only for Admins.' })
  @ApiQuery({ name: 'orgId', description: 'ID of the organization', required: false })
  @ApiQuery({ name: 'isDefault', description: 'Filter by default fields', required: false })
  @ApiResponse({ status: 403, description: 'Forbidden, only Admins can use this endpoint.' })
  @ApiResponse({ status: 200, description: 'Custom fields retrieved successfully.' })
  async getAllCustomFields(@Req() req: any, @Query('orgId') orgId?: string, @Query('isDefault') isDefault?: boolean) {

    if (!orgId && req.user.org && !req.user.roles.includes(Role.SuperAdmin)) {
      orgId = req.user.org._id;
    }
    return await this.customFieldService.getAllCustomFields(orgId, isDefault);
  }

  // Get a specific custom field by ID
  @Get(':fieldId')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  @Roles()
  @ApiOperation({ summary: 'Retrieve a specific custom field by ID', description: 'Accessible only for Admins.' })
  @ApiParam({ name: 'fieldId', description: 'ID of the field' })
  @ApiResponse({ status: 200, description: 'Custom field retrieved successfully.' })
  @ApiResponse({ status: 403, description: 'Forbidden, only Admins can use this endpoint.' })
  @ApiResponse({ status: 404, description: 'Custom field not found.' })
  async getCustomField(@Param('fieldId') fieldId: string) {
    return await this.customFieldService.getCustomField(fieldId);
  }
}
