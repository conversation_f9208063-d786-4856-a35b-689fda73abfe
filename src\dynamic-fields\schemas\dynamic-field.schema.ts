import { <PERSON>p, Schem<PERSON>, SchemaFactory, raw } from "@nestjs/mongoose";
import mongoose, { Types, Schema as MongooseSchema } from "mongoose";
import { AddressInformation } from "src/common/schemas/address-information.schema";
import { ContactInformation } from "src/common/schemas/contact-information.schema";
import { Country } from "src/country/schemas/country.schema";
import { EducationQualification } from "src/education-qualification/schemas/education-qualifaction.schema";
import { EvaluationForm } from "src/evaluation-form/schemas/evaluation-form.schema";
import { FileMetadata } from "src/file-upload/schemas/file-metadata.schema";
import { JobLocation } from "src/job-location/schemas/job-location.schema";
import { Job } from "src/job/schemas/job.schema";
import { Org } from "src/org/schemas/org.schema";
import { Currency, FieldType, Gender, WorkMode } from "src/shared/constants";
import { City } from "src/state/schemas/city.schema";
import { State } from "src/state/schemas/state.schema";
import { BasicUser } from "src/user/schemas/basic-user.schema";
import { WorkExperience } from "src/work-experience/schemas/work-experience.schema";
import { Workflow } from "src/workflow/schemas/workflow.schema";

@Schema({ _id: false })  // Disables automatic _id generation for options
export class OptionSchema {
    @Prop({ type: String, required: true })  // Make key required
    label: string;

    @Prop({ type: String, required: true })  // Make value required
    value: string;
}

@Schema({ _id: false })
export class DateFieldPropertiesSchema {
    @Prop({ type: String, required: false })  // ISO 8601 format
    minDate?: string;

    @Prop({ type: String, required: false })  // ISO 8601 format
    maxDate?: string;

    @Prop({ type: String, required: false })  // Date format (e.g., YYYY-MM-DD)
    format?: string;
}

@Schema({ timestamps: true })
export class DynamicField {

    // @Prop({ type: String, required: true })
    // title: string;  // Label or display name of the field

    @Prop({ type: String, required: true, trim: true, lowercase: false })
    title: string;

    @Prop({ required: true, enum: FieldType })
    type: FieldType;

    @Prop({ required: false })
    placeholder?: string; // Placeholder text for the field

    // @Prop({ type: [{ label: String, value: String }], default: [] })  // ✅ FIXED: Now options will be stored as an array of objects
    // options?: { label: string; value: string }[];

    @Prop({ type: [OptionSchema], _id: false })  // **This prevents ObjectId creation**
    options?: OptionSchema[];

    // @Prop({ type: [String], _id: false })  // **This prevents ObjectId creation**
    // options?: string[];

    @Prop({ required: false, ref: 'Org' })
    orgId?: Types.ObjectId;


   @Prop({ ref: 'Contact', required: false })
    contactId?: Types.ObjectId;


    
    @Prop({ default: false })
    isRequired?: boolean;

    @Prop({ default: false })
    isJobApplicationField?: boolean; // Indicates if this field is related to job applications

    @Prop({ default: false })
    isJobField?: boolean; // Indicates if this field is related to jobs

    @Prop({ default: false })
    isDefault?: boolean; // Indicates if this field is default to org

    @Prop({ default: false })
    isDeleted?: boolean; // Indicates if this field is delete

    @Prop({ type: DateFieldPropertiesSchema, required: false, _id: false })
    dateProperties?: DateFieldPropertiesSchema;  // ✅ Now supports date field properties

    @Prop()
    createdAt?: Date;

    @Prop()
    updatedAt?: Date;

    @Prop({ default: false })
    canDelete?: boolean; // Indicates if this field can be deleted

    @Prop({ type: Number, default: 0 }) // Order field
    order: number;

    @Prop({ default: false })
    isVisible?: boolean; // Indicates if this field can be visible to user

    @Prop({ type: String })
    name: string; //indicates payload name of the field

    @Prop({ default: true })
    canEdit?: boolean; // Indicates if this field can be editable to user

    @Prop({ required: false, ref: 'BusinessUnit' })
    departmentId?: Types.ObjectId;
}
export const DynamicFieldSchema = SchemaFactory.createForClass(DynamicField);

// export type DynamicFieldValue = {
//     label: string;
//     value?: string | number | boolean | string[] | null | undefined;
// }



// dynamicFields: {
//     aadhaar: '12345',
//     pan: '12344',
// },
