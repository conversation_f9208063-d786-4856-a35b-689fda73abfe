import { Modu<PERSON> } from '@nestjs/common';
import { ActivityService } from './activity.service';
import { ActivityController } from './activity.controller';
import { ConfigModule } from '@nestjs/config';
import { UserModule } from 'src/user/user.module';
import { JwtModule } from '@nestjs/jwt';
import { AuthModule } from 'src/auth/auth.module';
import { MongooseModule } from '@nestjs/mongoose';
import { Activity, ActivitySchema } from './schemas/activity.schema';
import { AlertModule } from 'src/alert/alert.module';
import { CommonModule } from 'src/common/common.module';
import { EndpointsRolesModule } from 'src/endpoints-roles/endpoints-roles.module';

@Module({
  imports: [
    ConfigModule,
    UserModule,
    JwtModule, EndpointsRolesModule,
    AuthModule,
    AlertModule,
    CommonModule,
    MongooseModule.forFeature([
      { name: Activity.name, schema: ActivitySchema }
    ])
  ],
  controllers: [ActivityController],
  providers: [ActivityService],
})
export class ActivityModule {}
