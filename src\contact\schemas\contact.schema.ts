import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, SchemaFactory } from "@nestjs/mongoose";
import { HydratedDocument, Types } from 'mongoose';
import { ContactInformation } from "src/common/schemas/contact-information.schema";
import { AccountStatus } from 'src/shared/constants';
import { Industry } from 'src/industry/schemas/industry.schema';
import { BasicUser } from 'src/user/schemas/basic-user.schema';
import { Org } from "src/org/schemas/org.schema";
import { AddressInformation } from "src/common/schemas/address-information.schema";
import { State } from "src/state/schemas/state.schema";
import { Country } from "src/country/schemas/country.schema";
import { City } from "src/state/schemas/city.schema";
import { FileMetadata } from "src/file-upload/schemas/file-metadata.schema";


export type ContactDocument = HydratedDocument<Contact>;

// Prop decorator can accept more options - read here - https://mongoosejs.com/docs/schematypes.html#schematype-options
// and here - https://docs.nestjs.com/techniques/mongodb#model-injection
@Schema({
  timestamps: true
})
export class Contact {

  // @Prop({
  //   type: String,
  //   required: true,
  //   trim: true
  // })
  // salutation: string; // TODO: enum, check require

  @Prop({
    type: String,
    required: true,
    trim: true
  })
  firstName: string;

  @Prop({
    type: String,
    required: true,
    trim: true
  })
  lastName: string;

  // TODO: we should be able to fetch all the internal contacts if it is referred by an internal user.
  // If it is referred by an external user then it should be simple string
  // If contact is not in database or external user, recommend the user the create another contact. This will be useful in report and tracking.
  @Prop({
    type: String,
    required: false
  })
  referredBy?: string;

  // TODO: how do we mark either a contactEmail or contactNumber as primary and secondary????
  //contactInfo  - will be a new schema, contactInfo- contactNumber, contactEmail , comments, address info, geo location co ordinates
  // @Prop({
  //   type: [String],
  //   required: true,
  //   trim: true
  // })
  // contactEmail: string[]; 

  // @Prop({
  //   type: [String],
  //   required: true,
  //   trim: true
  // })
  // contactPhoneNumber: string[];

  //  this is single row. 
  //  @Prop({type: ContactInformation}) // {type: Name} can be omitted
  //  contactInformation: ContactInformation;

  // this is an array
  @Prop({
    type: [ContactInformation],
    required: false,
  })
  contactDetails?: ContactInformation[];

  @Prop({ type: AddressInformation, required: false })
  contactAddress?: AddressInformation;

  @Prop({
    type: String,
    required: false,
    trim: true
  })
  designation?: string;

  @Prop({
    type: Types.ObjectId,
    required: false
  })
  logo?: FileMetadata;


  // TODO: The Location field should be in such a way that user should be able to search and selected from the dropdown options
  // Location is the address info of contact
  // TODO: Contact address is added in contact information schema

  // @Prop({
  //   type: String,
  //   required: true,
  //   trim: true
  // })
  // location: string;  

  @Prop({
    type: Types.ObjectId,
    ref: 'Industry',
    required: false
  })
  industry?: Industry;


  @Prop({
    type: Types.ObjectId,
    ref: 'Org',
    required: false
  })
  accountOrg?: Org;

  @Prop({
    type: Types.ObjectId,
    ref: 'Org',
    required: false
  })
  salesRepOrg?: Org;

  @Prop({
    type: String,
    required: false,
    trim: true
  })
  linkedInUrl?: string;

  @Prop({
    type: Types.ObjectId,
    required: false,
    trim: true
  })
  businessUnit?: Org;


  @Prop({
    type: Types.ObjectId,
    required: false,
    ref: 'Contact'
  })
  reportingTo?: Contact;

  // @Prop({
  //   type: Types.ObjectId,
  //   required: false,
  //   ref: 'BasicUser'
  // })
  // assignTo?: BasicUser;

  @Prop({
    type: [Types.ObjectId],
    required: false,
    ref: 'BasicUser',
  })
  assignTo?: BasicUser[]; // or BasicUser[] if you plan to populate

  @Prop({
    type: String,
    required: false,
    trim: true,
    default: AccountStatus.PROSPECT,
    enum: Object.values(AccountStatus),
  })
  status?: string;

  // Profile picture of the contact that is publicly available
  @Prop({
    type: String,
    required: false,
    trim: true
  })
  imageUrl?: string;

  @Prop({
    type: Boolean,
    required: false,
    default: false
  })
  favourite?: boolean;

  @Prop({
    type: Boolean,
    required: false,
    default: false
  })
  isDeleted?: boolean;

  @Prop({
    type: Types.ObjectId,
    required: false,
    ref: 'Country'
  })
  country?: Country;

  @Prop({
    type: Types.ObjectId,
    required: false,
    ref: 'State'
  })
  state?: State;

  @Prop({
    type: Types.ObjectId,
    required: false,
    ref: 'City'
  })
  city?: City;

  @Prop({
    type: String,
    required: false,
    trim: true
  })
  comment?: string;

  @Prop({
    type: [Types.ObjectId],
    required: false,
    ref: 'BasicUser',
    default: [],
  })
  favourites?: Types.ObjectId[];


  @Prop({
    type: Types.ObjectId,
    required: true,
    ref: 'BasicUser'
  })
  createdBy: BasicUser;

  @Prop({
    type: Boolean,
    required: false,
    default: false
  })
  isInternal?: boolean;

  @Prop({
    type: Types.ObjectId,
    required: false,
    ref: 'Org'
  })
  createdByOrg?: Org;

  @Prop({
    type: Types.ObjectId,
    ref: 'Org',
    required: false
  })
  vendor?: Org;

}

export const ContactSchema = SchemaFactory.createForClass(Contact);
