import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsE<PERSON>, IsNotE<PERSON>y, <PERSON>, <PERSON>, <PERSON>Length } from 'class-validator';
import { IsObjectId } from 'class-validator-mongo-object-id';
import { Transform, TransformFnParams } from 'class-transformer';
import sanitizeWithStyle from 'sanitize-html'


// Read about operators of swagger here - https://docs.nestjs.com/openapi/decorators
export class ForgotPasswordDto {

    @ApiProperty({
        type: String,
        required: true,
        description: 'The EMAIL of your account',
        format: 'email',
    })
    @IsNotEmpty()
    @IsEmail()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value).toLowerCase())
    email: string;
}
