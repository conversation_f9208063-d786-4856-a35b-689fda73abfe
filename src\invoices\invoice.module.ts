// src/invoice/invoice.module.ts
import { forwardRef, Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { InvoiceService } from './invoice.service';
import { InvoiceController } from './invoice.controller';
import { Invoice, InvoiceSchema } from './schemas/invoice.schema';
import { JwtModule } from '@nestjs/jwt';
import { EndpointsRolesModule } from 'src/endpoints-roles/endpoints-roles.module';
import { Org, OrgSchema } from 'src/org/schemas/org.schema';
import { Offer, OfferSchema } from 'src/offer/schemas/offer.schema';
import { BasicUser, BasicUserSchema } from 'src/user/schemas/basic-user.schema';
import { JobApplication, JobApplicationSchema } from 'src/job-application-form/schemas/job-application.schema';
import { Job, JobSchema } from 'src/job/schemas/job.schema';
import { RateCard, RateCardSchema } from 'src/rate-card/schemas/rate-card.schema';
import { OrgModule } from 'src/org/org.module';
import { EmailTemplate, EmailTemplateSchema } from 'src/email-template-builder/schemas/email-template-builder.schema';
import { Placeholder, PlaceholderSchema } from 'src/org/schemas/org.schema';
import { Bgv, BgvSchema } from 'src/offer/schemas/bgv.schema';
import { HikeApproval, HikeApprovalSchema } from 'src/offer/schemas/hike-approval.schema';
import { Employee, EmployeeSchema } from 'src/employee/schemas/employee.schema';

@Module({
  imports: [JwtModule, EndpointsRolesModule,
    forwardRef(() => OrgModule),  // Use forwardRef here to

    MongooseModule.forFeature([
      { name: Invoice.name, schema: InvoiceSchema },
      { name: Org.name, schema: OrgSchema },
      { name: Offer.name, schema: OfferSchema },
      { name: BasicUser.name, schema: BasicUserSchema },
      { name: JobApplication.name, schema: JobApplicationSchema },
      { name: Job.name, schema: JobSchema },
      { name: RateCard.name, schema: RateCardSchema },
      { name: EmailTemplate.name, schema: EmailTemplateSchema },
      { name: Placeholder.name, schema: PlaceholderSchema },
      { name: Bgv.name, schema: BgvSchema },
      {name: HikeApproval.name, schema:HikeApprovalSchema},
      {name:Employee.name, schema:EmployeeSchema},
      

    ])],
  controllers: [InvoiceController],
  providers: [InvoiceService],
  exports: [InvoiceService],
})
export class InvoiceModule { }
