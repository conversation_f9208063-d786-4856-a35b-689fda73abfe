import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Req, Query } from '@nestjs/common';
import { ClientService } from './client.service';
import { CreateClientDto } from './dto/create-client.dto';
import { UpdateClientDto } from './dto/update-client.dto';
import { ApiBearerAuth, ApiBody, ApiOperation, ApiParam, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Role } from 'src/auth/enums/role.enum';
import { AuthJwtGuard } from 'src/auth/guards/auth-jwt/auth-jwt.guard';
import { RolesGuard } from 'src/auth/guards/roles/roles.guard';
import { Roles } from 'src/auth/decorators/roles.decorator';
import { validateObjectId } from 'src/utils/validation.utils';
import { MoveToDto } from 'src/common/dto/move-to.dto';
import { ChangeStatusDto } from 'src/common/dto/change-status.dto';

@Controller('')
@ApiTags('Clients')
export class ClientController {
  constructor(private readonly clientService: ClientService) { }

  @Post()
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.SalesRep)
  @Roles()
  @ApiOperation({ summary: 'Create a new client', description: `This endpoint allows you to create a new client.This is accessible only for "admin" and "sales-rep"` })
  @ApiResponse({ status: 201, description: 'Client is saved. ' })
  @ApiResponse({ status: 400, description: 'Bad Request / Data. ' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  @ApiResponse({ status: 403, description: 'Forbidden. User with role admin or sales-rep can only use this end point.' })
  @ApiResponse({ status: 409, description: 'Client with this name already exists. ' })
  @ApiBody({ type: CreateClientDto, description: 'The client details to create' })
  async create(@Req() req: any, @Body() createClientDto: CreateClientDto) {
    createClientDto.createdBy = req.user._id;
    return await this.clientService.create(createClientDto);
  }

  @Get('all')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.SalesRep)
  @Roles()
  @ApiOperation({ summary: 'Retrieve all clients', description: `This endpoint returns a list of all clients. This is accessible only for "admin" and "sales-rep"` })
  @ApiResponse({ status: 200, description: 'Clients are retrieved.' })
  @ApiResponse({ status: 401, description: 'User not logged in.' })
  @ApiResponse({ status: 403, description: 'Forbidden. User with role admin or sales-rep  can only use this end point.' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number', example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of items per page', example: 10 })
  findAll(@Query('page') page: number = 1, @Query('limit') limit: number = 10) {
    return this.clientService.findAll(page, limit);
  }

  @Get('find-all-soft-deleted')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin)
  @Roles()
  @ApiOperation({ summary: 'Retrieve all clients that are soft deleted', description: `This endpoint returns a list of all clients that are soft deleted clients. This is accessible only for "admin"` })
  @ApiResponse({ status: 200, description: 'Clients are retrieved.' })
  @ApiResponse({ status: 401, description: 'User not logged in.' })
  @ApiResponse({ status: 403, description: 'Forbidden. User with role admin can only use this end point.' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number', example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of items per page', example: 10 })
  findAllWithSoftDelete(@Query('page') page: number = 1, @Query('limit') limit: number = 10) {
    return this.clientService.findAllWithSoftDelete(page, limit);
  }


  @Get(':clientId')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.SalesRep)
  @Roles()
  @ApiOperation({ summary: 'Retrieve an client by Id', description: `This endpoint returns an client by its Id. This is accessible only for "admin" and "sales-rep"` })
  @ApiResponse({ status: 200, description: 'Client is retrieved.' })
  @ApiResponse({ status: 400, description: 'Bad Request: Invalid ID format.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  @ApiResponse({ status: 403, description: 'Forbidden. User with role admin or sales-rep can only use this end point.' })
  @ApiResponse({ status: 404, description: 'Client not found.' })
  @ApiParam({ name: 'clientId', description: 'id of the client' })
  findOne(@Param('clientId') id: string) {
    const objId = validateObjectId(id);
    return this.clientService.findOne(objId);
  }

  @Patch(':clientId')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.SalesRep)
  @Roles()
  @ApiOperation({ summary: 'Update an client by Id', description: `This endpoint updates an client by Id. This is accessible only for "admin" and "sales-rep"` })
  @ApiParam({ name: 'clientId', description: 'id of the client' })
  @ApiResponse({ status: 200, description: 'Client is updated.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  @ApiResponse({ status: 403, description: 'Forbidden. User with role admin or sales-rep can only use this end point.' })
  @ApiResponse({ status: 404, description: 'Client not found.' })
  update(@Param('clientId') id: string, @Body() updateClientDto: UpdateClientDto) {
    const objId = validateObjectId(id);
    return this.clientService.update(objId, updateClientDto);
  }

  @Patch(':clientId/restore')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin)
  @Roles()
  @ApiOperation({ summary: 'Restore a soft-deleted client by Id', description: `This endpoint restores a soft-deleted client by Id. This is accessible only for "admin"` })
  @ApiParam({ name: 'clientId', description: 'id of the client' })
  @ApiResponse({ status: 200, description: 'Client is restored.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  @ApiResponse({ status: 403, description: 'Forbidden. User with role admin can only use this end point.' })
  @ApiResponse({ status: 404, description: 'Client not found.' })
  restoreSoftDeletedClients(@Param('clientId') id: string) {
    const objId = validateObjectId(id);
    return this.clientService.restoreSoftDeletedClients(objId);
  }

  // @Patch(':clientId/status')
  // @ApiBearerAuth()
  // @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.SalesRep)
  // @ApiOperation({ summary: 'Update status of an client by Id', description: `This endpoint updates status of an client by Id. This is accessible only for "admin" and "sales-rep"` })
  // @ApiParam({ name: 'clientId', description: 'id of the client' })
  // @ApiResponse({ status: 200, description: 'client is updated.' })
  // @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  // @ApiResponse({ status: 403, description: 'Forbidden. User with role admin or sales-rep can only use this end point.' })
  // @ApiResponse({ status: 404, description: 'client not found.' })
  // @ApiBody({ type: ChangeStatusDto, description: 'The client details to create' })
  // changeStatus(@Param('clientId') clientId: string, @Body() changeStatusDto: ChangeStatusDto) {
  //   const objId = validateObjectId(clientId);
  //   const { status, comment, attachments } = changeStatusDto;
  //   return this.clientService.changeStatus(objId, status, comment, attachments);
  // }

  @Patch(':clientId/move')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.SalesRep)
  @Roles()
  @ApiOperation({ summary: 'Move the ownership of an client by Id', description: `This endpoint moves the ownership of an client by Id. This is accessible only for "admin" and "sales-rep"` })
  @ApiParam({ name: 'clientId', description: 'id of the client' })
  @ApiResponse({ status: 200, description: 'Client is updated.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  @ApiResponse({ status: 403, description: 'Forbidden. User with role admin or sales-rep can only use this end point.' })
  @ApiResponse({ status: 404, description: 'Client not found.' })
  moveClient(@Param('clientId') clientId: string, @Body() moveToDto: MoveToDto) {
    const clientObjId = validateObjectId(clientId);
    // const { ownedBy, comment, attachments } = moveToDto;
    // const userObjId = validateObjectId(ownedBy);
    // return this.clientService.moveClient(clientObjId, userObjId, comment, attachments);
  }

  @Delete(':clientId/delete')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin)
  @Roles()
  @ApiOperation({ summary: 'Delete an client by Id', description: `This endpoint deletes an client by Id. This is accessible only for "admin"` })
  @ApiResponse({ status: 200, description: 'Client is deleted.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  @ApiResponse({ status: 403, description: 'Forbidden. User with role admin can only use this end point.' })
  @ApiResponse({ status: 404, description: 'Client not found.' })
  @ApiParam({ name: 'clientId', description: 'id of the client' })
  remove(@Param('clientId') id: string) {

    const objId = validateObjectId(id);
    return this.clientService.remove(objId);
  }

  @Delete(':clientId/soft-delete')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin)
  @Roles()
  @ApiOperation({ summary: 'Soft Delete an client by Id', description: `This endpoint soft deletes an client by Id. This is accessible only for "admin"` })
  @ApiResponse({ status: 200, description: 'Client is deleted.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  @ApiResponse({ status: 403, description: 'Forbidden. User with role admin can only use this end point.' })
  @ApiParam({ name: 'clientId', description: 'id of the client' })
  delete(@Param('clientId') id: string) {

    const objId = validateObjectId(id);
    return this.clientService.delete(objId);
  }

  @Delete('all')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin)
  @Roles()
  @ApiOperation({ summary: 'Remove  all clients', description: `This endpoint deletes all clients. This is accessible only for "admin"` })
  @ApiResponse({ status: 200, description: 'All clients deleted.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  @ApiResponse({ status: 403, description: 'Forbidden. User with role admin can only use this end point.' })
  deleteAll() {
    return this.clientService.deleteAll();
  }
}
