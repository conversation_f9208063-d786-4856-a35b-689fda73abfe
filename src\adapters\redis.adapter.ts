import { IoAdapter } from '@nestjs/platform-socket.io';
import { ServerOptions } from 'socket.io';
import { createAdapter } from '@socket.io/redis-adapter';
import { Logger } from '@nestjs/common';
import { Cluster } from 'ioredis';
import { ConfigService } from '@nestjs/config';

export class RedisIoAdapter extends IoAdapter {
  private readonly logger = new Logger(RedisIoAdapter.name);

  private adapterConstructor: ReturnType<typeof createAdapter>;
  
  constructor(app:any, private readonly configService: ConfigService) {
    // Or simply get the ConfigService in here, since you have the `app` instance anyway
    super(app);
  }
  async connectToRedis(): Promise<void> {
    try {
      let host = this.configService.get<string>('REDIS_HOST');
      let port = this.configService.get<number>('REDIS_PORT');

      if (!host || !port) {
        this.logger.warn(`Redis Host or Port not found in environment variables. WebSocket clustering will be disabled.`);
        // Don't create Redis adapter if no URL is provided
        // This allows the app to start without Redis for development or single-instance deployments
        return;
      }

      this.logger.log(`Attempting to connect to Redis at host: ${host} and port: ${port}`);

      // this is for ioredis. Read - https://socket.io/docs/v4/redis-adapter/
      const pubClient = new Cluster([
        {
          host: host,
          port: port,
        },
      ], {
        // Add connection timeout and retry options
        // connectTimeout: 10000,
        lazyConnect: true,
        retryDelayOnFailover: 100,
      });

      const subClient = pubClient.duplicate();

      // Set up error handlers before connecting
      pubClient.on("error", (err) => {
        this.logger.error(`Error on Redis pubClient: ${err.message}`);
      });

      subClient.on("error", (err) => {
        this.logger.error(`Error on Redis subClient: ${err.message}`);
      });

      pubClient.on("connect", () => {
        this.logger.log(`Redis pubClient connected successfully`);
      });

      subClient.on("connect", () => {
        this.logger.log(`Redis subClient connected successfully`);
      });

      // Try to connect with timeout
      await Promise.race([
        Promise.all([pubClient.connect(), subClient.connect()]),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Redis connection timeout')), 15000)
        )
      ]);

      this.adapterConstructor = createAdapter(pubClient, subClient);
      this.logger.log(`Redis adapter created successfully`);

    } catch (error) {
      this.logger.error(`Failed to connect to Redis: ${error.message}`);
      this.logger.warn(`Continuing without Redis clustering. WebSocket will work in single-instance mode.`);
      // Don't throw the error - allow the app to start without Redis
    }
  }

  createIOServer(port: number, options?: ServerOptions): any {
    const server = super.createIOServer(port, options);

    // Only use Redis adapter if it was successfully created
    if (this.adapterConstructor) {
      server.adapter(this.adapterConstructor);
      this.logger.log(`WebSocket server created with Redis clustering`);
    } else {
      this.logger.log(`WebSocket server created without Redis clustering (single-instance mode)`);
    }

    return server;
  }
}
