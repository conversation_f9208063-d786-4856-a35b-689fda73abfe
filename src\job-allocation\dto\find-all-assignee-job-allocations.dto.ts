import { Transform } from 'class-transformer';
import { IsOptional, IsString, IsInt, Min, IsEnum, IsDate, IsISO8601 } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Status, Priority } from 'src/shared/constants';

export class FindAllAssigneeJobAllocationsDto {

  @ApiProperty({ required: false, description: 'Assignee ID to filter by' })
  @IsOptional()
  @IsString()
  @Transform(({ value }) => (value === undefined || value === '0' ? '' : value))
  assignee?: string;

  @ApiProperty({ required: false, enum: Priority, description: 'Priority to filter by' })
  @IsOptional()
  @IsEnum(Priority)
  @Transform(({ value }) => (value === undefined || value === '0' ? '' : value))
  priority?: string;

  @ApiProperty({ required: false, description: 'Due date to filter by (ISO 8601 format)' })
  @IsOptional()
  @IsISO8601()
  dueDate?: string;

  @ApiProperty({ required: false, enum: ['prev', 'next'], description: 'Direction for date navigation' })
  @IsOptional()
  @IsString()
  dateDirection?: 'prev' | 'next';

  @ApiProperty({ required: false, type: Number, description: 'Page number', default: 1, minimum: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Transform(({ value }) => {
    const parsed = parseInt(value);
    return !isNaN(parsed) && parsed > 0 ? parsed : 1;
  })
  page: number = 1;

  @ApiProperty({ required: false, type: Number, description: 'Number of items per page', default: 10, minimum: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Transform(({ value }) => {
    const parsed = parseInt(value);
    return !isNaN(parsed) && parsed > 0 ? parsed : 10;
  })
  limit: number = 10;
}
