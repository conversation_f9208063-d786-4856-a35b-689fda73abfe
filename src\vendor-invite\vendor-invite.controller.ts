import { Controller, Get, Post, Body, Patch, Param, Delete, Req, UseGuards, BadRequestException, Query } from '@nestjs/common';
import { VendorInviteService } from './vendor-invite.service';
import { CreateVendorInviteDto } from './dto/create-vendor-invite.dto';
import { UpdateVendorInviteDto } from './dto/update-vendor-invite.dto';
import { Roles } from 'src/auth/decorators/roles.decorator';
import { AuthJwtGuard } from 'src/auth/guards/auth-jwt/auth-jwt.guard';
import { RolesGuard } from 'src/auth/guards/roles/roles.guard';
import { Role } from 'src/auth/enums/role.enum';
import { ApiBearerAuth, ApiQuery, ApiTags } from '@nestjs/swagger';

@Controller('')
@ApiTags('vendor-invite')

export class VendorInviteController {
  constructor(private readonly vendorInviteService: VendorInviteService) { }

  @Post()
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.BUHead, Role.TeamLead, Role.DeliveryManager, Role.Recruiter, Role.Admin)
  @Roles()
  create(@Req() req: any, @Body() createVendorInviteDto: CreateVendorInviteDto) {
    createVendorInviteDto.createdBy = req.user._id
    return this.vendorInviteService.create(createVendorInviteDto,req?.user);
  }

  @Get()
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.BUHead, Role.TeamLead, Role.DeliveryManager, Role.Recruiter, Role.Vendor, Role.Admin)
  @Roles()
  findAll(@Req() req: any,) {
    // Pass the logged-in user's ID to the service
    return this.vendorInviteService.findAll(req.user._id);
  }

  @Get(':id')
  async findById(@Param('id') id: string) {
    if (!id) {
      throw new BadRequestException('Vendor invite ID is required.');
    }
    return await this.vendorInviteService.findById(id);
  }
  

  @Post('remove-invite-if-registered')
  async removeInviteIfUserRegistered(@Body() body: { email: string, isTemp: boolean }) {
      const { email, isTemp } = body;
      await this.vendorInviteService.removeInviteIfUserRegistered(email, isTemp);
      return { message: 'Invite removed if the user is registered.' };
  }
  
   // New route to send reminder to invitee
   @Post('send-reminder')
  //  @ApiBearerAuth()
  //  @UseGuards(AuthJwtGuard, RolesGuard)
  //  @Roles(Role.BUHead, Role.TeamLead, Role.DeliveryManager, Role.Recruiter)
    @ApiQuery({ name: 'email', description: 'vendor email', required: true })
   async sendReminder(@Query('email') email: string) {
     if (!email) {
       throw new BadRequestException('Email is required.');
     }
     return await this.vendorInviteService.sendReminderToInvitee(email);
   }

}
