import { <PERSON>, Get, Post, Body, Patch, Param, Delete, Logger, UseGuards, Req, NotFoundException, Query, BadRequestException, ForbiddenException, InternalServerErrorException } from '@nestjs/common';
import { AttendanceService } from './attendance.service';
import { CreateAttendanceDto } from './dto/create-attendance.dto';
import { UpdateAttendanceDto } from './dto/update-attendance.dto';
import { CreateAttendanceSessionDto } from './dto/create-attendance-session.dto';
import { UpdateAttendanceSessionDto } from './dto/update-attendance-session.dto';
import { ApiBearerAuth, ApiOperation, ApiParam, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { AuthJwtGuard } from 'src/auth/guards/auth-jwt/auth-jwt.guard';
import { RolesGuard } from 'src/auth/guards/roles/roles.guard';
import { Roles } from 'src/auth/decorators/roles.decorator';
import { Attendance } from './schemas/attendance.schema';


@Controller()
@ApiTags('Attendance')
export class AttendanceController {
  constructor(private readonly attendanceService: AttendanceService) { }

  private readonly logger = new Logger(AttendanceController.name);

  // @Post('check-in')
  // @ApiBearerAuth()
  // @UseGuards(AuthJwtGuard)
  // async checkIn(@Req() req: any) {
  //   const userId = req.user._id;
  //   const today = new Date().toDateString();

  //   const projectId = '68513791e0278c064c8f3905';

  //   // Check if attendance exists for today
  //   let attendance = await this.attendanceService.findOrCreateTodayAttendance(userId);

  //   // Create session with current checkIn time
  //   const session = await this.attendanceService.createSession(attendance._id as string, {
  //     checkIn: new Date().toISOString(),
  //     projectId

  //   });

  //   return { message: 'Checked in successfully.', session };
  // }

  @Post('check-in')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  @ApiQuery({ name: 'projectId', required: false, type: String })
  async checkIn(@Req() req: any, @Query('projectId') projectId?: string) {
    const userId = req.user._id;
    const userOrgId = req.user.org._id;

    // If projectId is provided, validate it
    if (projectId) {
      const project = await this.attendanceService.getProjectIfValid(projectId, userOrgId);
      if (!project) {
        throw new ForbiddenException('Invalid project or organization mismatch');
      }
     
    }

    // Get or create today's attendance
    const attendance = await this.attendanceService.findOrCreateTodayAttendance(userId);
    if (!attendance || !attendance._id) {
      throw new InternalServerErrorException('Could not create or retrieve today\'s attendance');
    }
    

    // Create new session with or without projectId
    const session = await this.attendanceService.createSession(attendance._id.toString(), {
      checkIn: new Date().toISOString(),
      projectId,
    });
   

    return { message: 'Checked in successfully.', session };
  }


  @Post('check-out')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  @ApiQuery({ name: 'projectId', required: false, type: String })
  async checkOut(@Req() req: any, @Query('projectId') projectId?: string) {
    const userId = req.user._id;

    const attendanceRaw: any = await this.attendanceService.findTodayAttendance(userId);
     if (!attendanceRaw) throw new NotFoundException('No attendance found for today');
     const attendance: any = attendanceRaw as Attendance;
    const attendanceId = attendance._id.toString(); 
   

    let session;

    if (projectId) {
      // ✅ Find session for specific project
      session = await this.attendanceService.findActiveSessionForProject(attendanceId, projectId);

     
    } else {
      // ✅ Fallback: find any active session
      session = await this.attendanceService.findActiveSession(attendanceId);
      
    }

    if (!session) throw new NotFoundException('No active session found');

    const updateDto: UpdateAttendanceSessionDto = {
      checkOut: new Date().toISOString(),
    };

    const updated = await this.attendanceService.updateSession(session._id as string, updateDto);

    

    return { message: 'Checked out successfully.', session: updated };
  }




  @Post()
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  @ApiResponse({ status: 201, description: 'Attendance is saved.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Data. ' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role "BUHead", "TeamLead", "DeliveryManager" and "Recruiter" can only use this end point.' })
  @ApiOperation({ summary: 'Create a Attendance', description: `This endpoint for creating a attendance. This is only accessible for "BUHead", "TeamLead", "DeliveryManager" and "Recruiter".` })
  // @Roles()
  create(@Req() req: any, @Body() createAttendanceDto: CreateAttendanceDto) {
    createAttendanceDto.userId = req.user._id;
    return this.attendanceService.create(createAttendanceDto);
  }

  @Get()
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  @ApiResponse({ status: 201, description: 'Attendance is saved.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Data. ' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role "BUHead", "TeamLead", "DeliveryManager" and "Recruiter" can only use this end point.' })
  @ApiOperation({ summary: 'Get a attendance', description: `This endpoint for getting all attendances. This is only accessible for "BUHead", "TeamLead", "DeliveryManager" and "Recruiter".` })
  findAll(@Req() req: any,) {
    return this.attendanceService.findAll(req.user._id);
  }

  @Get(':id')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  @ApiParam({ name: 'id', description: 'The ID of the Attendance  Candidate to retrieve' })
  @ApiResponse({ status: 201, description: 'Attendance  is saved.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Data. ' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role "BUHead", "TeamLead", "DeliveryManager" and "Recruiter" can only use this end point.' })
  @ApiOperation({ summary: 'Get a specific Attendance  Candidate', description: `Fetches details of a Attendance  Candidate by ID.` })
  findOne(@Param('id') id: string) {
    return this.attendanceService.findOne(id);
  }

  @Patch(':id')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  @ApiParam({ name: 'id', description: 'The ID of the Attendance Candidate to update' })
  @ApiResponse({ status: 200, description: 'Attendance Candidate updated successfully.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Invalid Data.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden. Only specific roles can access this endpoint.' })
  @ApiResponse({ status: 404, description: 'Attendance Candidate not found.' })
  @ApiOperation({ summary: 'Update a Attendance Candidate', description: `This endpoint Attendance a Bench Candidate by ID. Only accessible by "BUHead", "TeamLead", "DeliveryManager", and "Recruiter".` })
  update(@Param('id') id: string, @Body() updateAttendanceDto: UpdateAttendanceDto) {
    return this.attendanceService.update(id, updateAttendanceDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.attendanceService.remove(id);
  }

  @Post(':id/sessions')
  createSession(
    @Param('id') attendanceId: string,
    @Body() dto: CreateAttendanceSessionDto,
  ) {
    return this.attendanceService.createSession(attendanceId, dto);
  }

  @Patch('session/:id')
  updateSession(
    @Param('id') sessionId: string,
    @Body() dto: UpdateAttendanceSessionDto,
  ) {
    return this.attendanceService.updateSession(sessionId, dto);
  }

  @Delete('session/:id')
  removeSession(@Param('id') sessionId: string) {
    return this.attendanceService.removeSession(sessionId);
  }
}
