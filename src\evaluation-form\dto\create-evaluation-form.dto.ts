import { ApiProperty } from "@nestjs/swagger";
import { Transform, TransformFnParams } from "class-transformer";
import { IsBoolean, IsMongoId, IsNotEmpty, IsNumber, IsString, Length, Max, Min } from "class-validator";
import { sanitizeWithStyle } from "src/utils/sanitize-with-style";

export class CreateEvaluationFormDto {

    @ApiProperty({
        type: String,
        required: true,
    })
    @IsNotEmpty()
    @IsMongoId()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    jobId: string;

    @ApiProperty({
        type: String,
        required: true,
        description: 'Name of the skill',
    })
    @IsNotEmpty()
    @IsString()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    skill: string;

    @ApiProperty({
        type: Number,
        required: false,
        description: 'Years of expertise',
    })
    @IsNotEmpty()
    @IsNumber()
    years?: number;

    @ApiProperty({
        type: Number,
        required: false,
        description: 'Months of expertise',
    })
    @IsNumber()
    months?: number;

    @ApiProperty({
        type: Number,
        required: true,
        description: 'Rate your expertise in the skill mentioned above',
    })
    @IsNotEmpty()
    @IsNumber()
    @Min(1)
    @Max(10)
    rating: number;

    @ApiProperty({
        type: Boolean,
        required: true,
        default: false,
        description: 'Primary skill',
    })
    @IsNotEmpty()
    @IsBoolean()
    isPrimary: boolean;

}
