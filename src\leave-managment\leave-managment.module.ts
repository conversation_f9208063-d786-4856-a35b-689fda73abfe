import { Modu<PERSON> } from '@nestjs/common';

import { LeaveManagmentController } from './leave-managment.controller';
import { LeavePolicyService } from './leave-managment.service';
import { BasicUser, BasicUserSchema } from 'src/user/schemas/basic-user.schema';
import { JwtModule } from '@nestjs/jwt';
import { UserModule } from 'src/user/user.module';
import { CommonModule } from 'src/common/common.module';
import { EndpointsRolesModule } from 'src/endpoints-roles/endpoints-roles.module';
import { ConfigModule } from '@nestjs/config';
import { MongooseModule } from '@nestjs/mongoose';
import { LeavePolicySchema } from './schemas/leave-managment.schema';

@Module({

  controllers: [LeaveManagmentController],
   imports: [
      ConfigModule,
      JwtModule, EndpointsRolesModule,
      UserModule,
      CommonModule,
      MongooseModule.forFeature([{ name: 'LeavePolicy', schema: LeavePolicySchema },

      { name: BasicUser.name, schema: BasicUserSchema },
      ]),
    ],
  providers: [LeavePolicyService],
})
export class LeaveManagmentModule {}
