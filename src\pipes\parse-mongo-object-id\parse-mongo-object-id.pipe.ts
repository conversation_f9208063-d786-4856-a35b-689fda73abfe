import { ArgumentMetadata, Injectable, PipeTransform, UnprocessableEntityException } from '@nestjs/common';
import { Types } from 'mongoose';

@Injectable()
export class ParseMongoObjectIdPipe implements PipeTransform {
  transform(value: string, metadata: ArgumentMetadata):Types.ObjectId {
    if(!Types.ObjectId.isValid(value)){
      throw new UnprocessableEntityException(`ID is not a valid MongoDb ObjectId. ${value}`)
    }
    return new Types.ObjectId(value);
  }
}
