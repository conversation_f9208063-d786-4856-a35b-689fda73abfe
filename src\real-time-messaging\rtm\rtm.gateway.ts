import { SubscribeMessage, WebSocketGateway } from '@nestjs/websockets';
import { MessageBody,  WebSocketServer,WsResponse,} from '@nestjs/websockets';
import { Namespace, Server, Socket } from 'socket.io';
import {
  OnGatewayInit,
  OnGatewayConnection,
  OnGatewayDisconnect,
  ConnectedSocket,
} from '@nestjs/websockets';
import { Logger, UseFilters, UseGuards } from '@nestjs/common';
import { EventEmitter2, OnEvent, OnEventMetadata } from '@nestjs/event-emitter';
import { WebSocketAuthGuard } from 'src/auth/guards/websocket-auth/websocket-auth.guard';
import { WsUnauthorizedExceptionFilter } from 'src/auth/filters/WsUnauthorizedExceptionFilter';
import { ConfigService } from '@nestjs/config';
import { RedisStoreService } from '../redis-store/redis-store.service';

@WebSocketGateway(
  {
    namespace: 'rtm',
    path: '/rtm',
    cors: {
      origin: '*',
    },
    maxHttpBufferSize: 1e8 //100 mb
    // // Read about server options here - https://socket.io/docs/v4/server-options/#

  }
)
export class RtmGateway implements OnGatewayInit, OnGatewayDisconnect{
  private logger: Logger = new Logger(RtmGateway.name);

  @WebSocketServer()
  server: Server;

  // @WebSocketServer({ namespace: 'rtm' })
  // namespace: Namespace;
  
  constructor(private configService: ConfigService, private readonly redisStoreService: RedisStoreService){
        
  }

  private activeSockets: { room: string; id: string }[] = [];


  @SubscribeMessage('auth.test')
  @UseGuards(WebSocketAuthGuard)
  handleAuthTest(@ConnectedSocket() client: Socket, payload: any): any {
    // return 'Hello world!';
    return client.emit('auth.success', {message: 'auth succeeded.' });
  }

  @SubscribeMessage('message')
  @UseGuards(WebSocketAuthGuard)
  handleMessage(@ConnectedSocket() client: Socket, payload: any): any {
    // return 'Hello world!';
    return client.emit('message.received', {message: 'msg recieved.' });
  }

  @SubscribeMessage('ping')
  // @UseGuards(WebSocketAuthGuard)
  handlePing(@ConnectedSocket() client: Socket, payload: any): any {
    client.emit('pong', {id: client.id});
    return ;
  }



  public afterInit(server: Server): void {
    this.redisStoreService.storeObject('activeSockets', this.activeSockets)
    this.logger.log('Init');
  }

  public handleDisconnect(@ConnectedSocket() client: Socket): void {
    const existingSocket = this.activeSockets.find(
      (socket) => socket.id === client.id,
    );

    if (!existingSocket) return;

    this.activeSockets = this.activeSockets.filter(
      (socket) => socket.id !== client.id,
    );

    client.broadcast.emit(`${existingSocket.room}-remove-user`, {
      socketId: client.id,
    });

    this.logger.log(`Client disconnected: ${client.id}`);
  }

  @SubscribeMessage('joinRoom')
  public joinRoom(@ConnectedSocket() client: Socket, room: string): void {
    /*
    client.join(room);
    client.emit('joinedRoom', room);
    */

    const existingSocket = this.activeSockets?.find(
      (socket) => socket.room === room && socket.id === client.id,
    );

    if (!existingSocket) {
      this.activeSockets = [...this.activeSockets, { id: client.id, room }];
      client.emit(`${room}-update-user-list`, {
        users: this.activeSockets
          .filter((socket) => socket.room === room && socket.id !== client.id)
          .map((existingSocket) => existingSocket.id),
        current: client.id,
      });

      client.broadcast.emit(`${room}-add-user`, {
        user: client.id,
      });
    }

    return this.logger.log(`Client ${client.id} joined ${room}`);
  }

  @SubscribeMessage('call-user')
  public callUser(@ConnectedSocket() client: Socket, data: any): void {
    client.to(data.to).emit('call-made', {
      offer: data.offer,
      socket: client.id,
    });
  }

  @SubscribeMessage('make-answer')
  public makeAnswer(@ConnectedSocket() client: Socket, data: any): void {
    client.to(data.to).emit('answer-made', {
      socket: client.id,
      answer: data.answer,
    });
  }

  @SubscribeMessage('reject-call')
  public rejectCall(@ConnectedSocket() client: Socket, data: any): void {
    client.to(data.from).emit('call-rejected', {
      socket: client.id,
    });
  }

}
