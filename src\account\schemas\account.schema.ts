import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Types } from 'mongoose';
import { AccountType } from 'src/account-type/schemas/account-type.schema';
import { AccountStatus, HeadCount } from 'src/shared/constants';
import { Country } from 'src/country/schemas/country.schema';
import { Industry } from 'src/industry/schemas/industry.schema';
import { BasicUser } from 'src/user/schemas/basic-user.schema';

export type AccountDocument = HydratedDocument<Account>;

// Prop decorator can accept more options - read here - https://mongoosejs.com/docs/schematypes.html#schematype-options
// and here - https://docs.nestjs.com/techniques/mongodb#model-injection
@Schema({ timestamps: true })
export class Account {

  @Prop({
    required: true,
    unique: true,
    trim: true,
  })
  name: string;

  @Prop({ required: false, type: Types.ObjectId, ref: 'Account' })
  parentAccount?: Account;

  @Prop({
    required: false,
    trim: true,
  })
  websiteUrl?: string;

  @Prop({
    required: false,
    trim: true,
  })
  contactNumber?: string;

  @Prop({ required: false, type: Types.ObjectId, ref: 'Industry' })
  industry?: Industry;

  @Prop({
    type: String,
    required: false,
    trim: true,
    default: HeadCount.NOT_SPECIFIED,
    enum: Object.values(HeadCount),
  })
  headCount?: string;

  @Prop({
    required: false,
    trim: true,
  })
  description?: string;

  @Prop({ type: Types.ObjectId, ref: 'AccountType' })
  accountType?: AccountType;

  @Prop({ type: String, required: false, trim: true })
  address?: string;

  @Prop({ type: String, required: false, trim: true })
  city?: string;

  @Prop({ type: String, required: false, trim: true })
  stateOrProvince?: string;

  @Prop({
    required: false,
    type: Types.ObjectId, ref: 'Country'
  })
  country?: Country;

  @Prop({ type: String, required: false, trim: true })
  pinOrZipCode?: string;

  @Prop({
    required: false,
    type: Types.ObjectId,
    ref: 'BasicUser',
  })
  ownedBy?: BasicUser;

  @Prop({
    required: true,
    type: Types.ObjectId, ref: 'BasicUser'
  })
  createdBy: BasicUser;

  @Prop({
    required: false,
    default: false,
    type: Boolean,
  })
  isDeleted?: boolean;

  @Prop({
    required: false,
    default: false,
    type: Boolean,
  })
  isDuplicate?: boolean;

  @Prop({
    type: String,
    required: false,
    trim: true,
    default: AccountStatus.PROSPECT,
    enum: Object.values(AccountStatus),
  })
  status?: string;

  @Prop({
    required: false,
    type: Types.ObjectId, ref: 'BasicUser'
  })
  updatedBy: BasicUser;

}

export const AccountSchema = SchemaFactory.createForClass(Account);

