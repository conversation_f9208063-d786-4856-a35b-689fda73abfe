import {
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { InjectModel } from '@nestjs/mongoose';
import axios from 'axios';
import { format } from 'date-fns';
import { toZonedTime } from 'date-fns-tz';
import { JSONPath } from 'jsonpath-plus';
import { unescape } from 'lodash';
import { Model, Types } from 'mongoose';
import { AuthService } from 'src/auth/auth.service';
import {
  Calendar,
  CalendarDocument,
} from 'src/calendar/schemas/calender.schema';
import { EmailTemplate } from 'src/email-template-builder/schemas/email-template-builder.schema';
import { Integration } from 'src/integrations/schemas/integrations.schema';
import { JobApplication } from 'src/job-application-form/schemas/job-application.schema';
import { Placeholder } from 'src/org/schemas/org.schema';
import {
  EmailTemplateEvent,
  IntegrationType,
  ScreeningType,
  WhatsAppTemplateType,
} from 'src/shared/constants';
import { Stage } from 'src/stage/schemas/stage.schema';
import { CreateInterviewDto } from './dto/create-interview.dto';
import { UpdateInterviewDto } from './dto/update-interview.dto';
import { Interview } from './schemas/interview.schema';
@Injectable()
export class InterviewService {
  private readonly logger = new Logger(InterviewService.name);

  constructor(
    private authService: AuthService,
    private configService: ConfigService,
    @InjectModel(Interview.name) private interviewModel: Model<Interview>,
    private eventEmitter: EventEmitter2,
    @InjectModel(EmailTemplate.name)
    private emailTemplateService: Model<EmailTemplate>,
    @InjectModel(JobApplication.name)
    private jobApplicationService: Model<JobApplication>,
    @InjectModel(Placeholder.name)
    private placeholderService: Model<Placeholder>,
    @InjectModel(Stage.name) private stageModel: Model<Stage>,
    @InjectModel(Integration.name) private integrationModel: Model<Integration>,
    @InjectModel(Calendar.name)
    private readonly calendarModel: Model<CalendarDocument>,
  ) {}

  async create(user: any, createInterviewDto: CreateInterviewDto) {
    try {
      const { screeningType, isRescheduled } = createInterviewDto;

      const createdInterview = new this.interviewModel(createInterviewDto);
      await createdInterview.save();

      const interview = await this.interviewModel
        .findById(createdInterview._id)
        .populate({
          path: 'jobApplication',
          populate: [
            {
              path: 'jobId',
              select: '_id title',
            },
            {
              path: 'postingOrg',
              select: '_id title',
            },
          ],
          select:
            '_id jobId firstName lastName contactDetails contactAddress postingOrg',
          model: 'JobApplication',
        })
        .exec();

      // Rename 'postingOrg' to 'org' in the jobApplication field
      if (interview?.jobApplication) {
        const jobApp = interview.jobApplication as any;
        jobApp.org = jobApp.postingOrg;
        if ('postingOrg' in jobApp) {
          delete jobApp.postingOrg;
        }
      }

      console.log(interview);

      if (!interview) {
        throw new Error('Failed to fetch interview details after creation.');
      }

      if (
        screeningType === ScreeningType.AI_SCREENING &&
        interview?.jobApplication
      ) {
        const now = new Date();
        const targetDate = new Date(createInterviewDto.interviewDate);
        const diffInMs = targetDate.getTime() - now.getTime();
        const diffInHours = diffInMs / (1000 * 60 * 60); // ms → hours
        const tokenDetails = await this.authService.generateInterviewToken(
          user.email,
          `${Math.ceil(diffInHours) + 1}h`,
        );
        const aiInterviewData = await this.createAiInterview(
          interview.jobApplication,
          tokenDetails,
          interview._id.toString(),
          createInterviewDto.jobApplication,
        );
        interview.meetingId = aiInterviewData.session_id;
        interview.meetingUrl = `https://interview.talsy.ai/interview/${interview.meetingId}`;
      }

      const integration = await this.integrationModel
        .findOne({
          org: user.org._id,
          integrationType: IntegrationType.WhatsApp,
        })
        .exec();

      if (
        integration?.whatsappAccessToken &&
        integration?.whatsappPhoneNumber
      ) {
        const phone = interview.jobApplication.contactDetails?.contactNumber;

        // Check if they provided a WhatsApp template for INTERVIEW
        const interviewTemplateName =
          integration.whatsappTemplates?.[WhatsAppTemplateType.INTERVIEW];

        if (phone && interviewTemplateName) {
          const interviewDateFormatted = format(
            new Date(interview.interviewDate),
            'MMM dd yyyy, h:mm a',
          );

          // Fill in template variables - adjust as per your approved template
          const templateComponents = [
            {
              type: 'body',
              parameters: [
                {
                  type: 'text',
                  text: `${interview.jobApplication.firstName} ${interview.jobApplication.lastName}`,
                },
                {
                  type: 'text',
                  text: interview.jobApplication.jobId?.title || 'Job Title',
                },
                { type: 'text', text: interviewDateFormatted },
              ],
            },
          ];

          await this.sendWhatsAppTemplateMessage({
            accessToken: integration.whatsappAccessToken,
            phoneNumberId: integration.whatsappPhoneNumber,
            to: phone,
            templateName: interviewTemplateName,
            languageCode: 'en_US',
            components: templateComponents,
          });
        }
      }

      // Determine the event name based on isRescheduled flag
      const eventName = isRescheduled
        ? EmailTemplateEvent.WORKFLOW_INTERVIEW_RESCHEDULING
        : await this.getEventNameFromScreeningType(screeningType);

      // Fetch the appropriate email template
      let emailTemplate = await this.emailTemplateService
        .findOne({
          eventName,
          isDefault: true,
          org: user.org._id,
          isDeleted: false,
        })
        .exec();

      if (!emailTemplate) {
        emailTemplate = await this.emailTemplateService
          .findOne({
            eventName,
            canDelete: false,
            org: user.org._id,
            isDeleted: false,
          })
          .exec();
      }

      // Error handling if no template is found
      if (!emailTemplate) {
        throw new Error(
          `Email template not found for the event: ${eventName}.`,
        );
      }

      // Generate the email content with placeholder replacements
      let body = emailTemplate.templateHTMLContent
        ? unescape(emailTemplate.templateHTMLContent)
        : '';
      const placeholders = await this.placeholderService
        .find({ emailTemplate: emailTemplate._id.toString() })
        .exec();
      const istTimeZone = 'Asia/Kolkata';
      placeholders.forEach((placeholder) => {
        const path = placeholder.jsonPath;
        const value = JSONPath({
          path,
          json: {
            user,
            data: interview.toJSON().jobApplication,
            interview: {
              ...interview.toJSON(),
              interviewDate: format(
                toZonedTime(new Date(interview.interviewDate), istTimeZone),
                'MMM dd yyyy, h:mm a',
              ),
            },
          },
        });

        const regex = new RegExp(`#${placeholder.name}`, 'g');

        if (value.length > 0 && value[0].trim() !== '') {
          // Replace with bold value
          body = body.replace(regex, `<b>${value[0]}</b>`);
        } else {
          // ✅ Remove the entire tag/block that contains the placeholder
          const cleanRegex = new RegExp(
            `<[^>]+>[^<]*#${placeholder.name}[^<]*<\\/[^>]+>`, // matches <p>Meeting Code: #meeting code</p> etc.
            'gi',
          );
          body = body.replace(cleanRegex, '');
          // Final cleanup to remove empty label lines (e.g. "Meeting Code: ")
          body = body.replace(/<p[^>]*>\s*[^<]*:\s*<\/p>/gi, '');
          body = body.replace(/<div[^>]*>\s*[^<]*:\s*<\/div>/gi, '');

          // 🧼 Optional: also remove stray placeholder if used inline without tags
          body = body.replace(regex, '');
        }
      });

      // Clean up unnecessary HTML and return the result
      const cleanedHtml = body.replace(
        /<span[^>]*class="TiptapEditor_mention__[^"]*"[^>]*>(.*?)<\/span>/g,
        '$1',
      );

      // this.logger.log(`Triggered ${eventName} event`);
      if (screeningType === ScreeningType.AI_SCREENING) {
        this.emitEvent(EmailTemplateEvent.WORKFLOW_INTERVIEW_AI, {
          user: user,
          data: interview.jobApplication,
          body: cleanedHtml,
        });
      } else {
        this.emitEvent('technicalPanel.invite', {
          user: user,
          data: {
            ...interview.toJSON(),
            interviewDate: format(
              new Date(interview.interviewDate),
              'MMM dd yyyy, h:mm a',
            ),
          },
        });
      }

      return cleanedHtml;
    } catch (error) {
      this.logger.error(`Failed to create interview. ${error}`);
      throw new InternalServerErrorException(
        `Error when creating interview. ${error?.message}`,
      );
    }
  }

  private async createAiInterview(
    jobApplication: JobApplication,
    tokenDetails: any,
    interviewId: string,
    jobId: string,
  ): Promise<any> {
    if (jobApplication.contactDetails?.contactEmail) {
      const details = {
        email: jobApplication.contactDetails.contactEmail,
        first_name: jobApplication.firstName,
        last_name: jobApplication.lastName,
        token_details: tokenDetails,
        jobId: jobId,
      };
      const payload = {
        user_id: jobApplication.contactDetails.contactEmail
          .replace('@', '_')
          .replace('.', '_'),
        skills: ['multi-threading', 'oops'],
        exp_min: 50,
        details: details,
        interview_id: interviewId,
      };
      // console.log("interview payload : ", payload);
      const aiServiceUrl =
        process.env.AI_SERVICE_URL || 'http://localhost:6000';
      const url = `${aiServiceUrl}/api/v1/interview/create`;
      try {
        const response = await axios.post(url, payload, {
          headers: {
            'Content-Type': 'application/json',
          },
        });

        if (response.status === 200 || response.status === 201) {
          this.logger.log('AI interview created successfully.');
          return response.data;
        }
        throw new Error('Failed to create ai interview.');
      } catch (err) {
        this.logger.error(`AI interview creation failed: ${err.message}`);
      }
    } else {
      throw new Error('Failed to fetch jobapplication details.');
    }
  }

  private async sendWhatsAppTemplateMessage({
    accessToken,
    phoneNumberId,
    to,
    templateName,
    languageCode,
    components,
  }: {
    accessToken: string;
    phoneNumberId: string;
    to: string; // recipient's phone number in international format
    templateName: string;
    languageCode: string;
    components: any[]; // usually contains body parameters
  }): Promise<void> {
    const url = `https://graph.facebook.com/v19.0/${phoneNumberId}/messages`;

    const payload = {
      messaging_product: 'whatsapp',
      to,
      type: 'template',
      template: {
        name: templateName,
        language: { code: languageCode },
        components,
      },
    };

    try {
      const response = await axios.post(url, payload, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.status === 200 || response.status === 201) {
        this.logger.log('WhatsApp message sent successfully.');
      }
    } catch (err) {
      this.logger.error(`WhatsApp Cloud send failed: ${err.message}`);
    }
  }

  // Helper function to map screening type to event name
  private async getEventNameFromScreeningType(
    screeningType: ScreeningType,
  ): Promise<string> {
    switch (screeningType) {
      case ScreeningType.PHONE_SCREENING:
        return EmailTemplateEvent.WORKFLOW_INTERVIEW_TELEPHONIC;
      case ScreeningType.VIDEO_SCREENING:
        return EmailTemplateEvent.WORKFLOW_INTERVIEW_VIDEO;
      case ScreeningType.IN_PERSON_SCREENING:
        return EmailTemplateEvent.WORKFLOW_INTERVIEW_IN_PERSON;
      case ScreeningType.AI_SCREENING:
        return EmailTemplateEvent.WORKFLOW_INTERVIEW_AI;
      default:
        throw new Error(`Unsupported screening type: ${screeningType}`);
    }
  }

  async getRejectionEmailTemplate(user: any, jobApplicationId: string) {
    try {
      const jobApplication = await this.jobApplicationService
        .findById(jobApplicationId)
        .populate({ path: 'jobId', select: '_id title', model: 'Job' })
        .populate({ path: 'org', select: '_id title', model: 'Org' })
        .exec();

      if (!jobApplication) {
        throw new NotFoundException(
          `Job application with id: "${jobApplicationId}" not found.`,
        );
      }

      const eventName = EmailTemplateEvent.WORKFLOW_REJECTION;
      const emailTemplate = await this.getEmailTemplate(eventName, user);

      if (!emailTemplate) {
        throw new NotFoundException(
          `Email template not found for the event: ${eventName}.`,
        );
      }

      let body = emailTemplate.templateHTMLContent
        ? unescape(emailTemplate.templateHTMLContent)
        : '';
      const placeholders = await this.placeholderService
        .find({ emailTemplate: emailTemplate._id.toString() })
        .exec();

      placeholders.forEach((placeholder) => {
        const path = placeholder.jsonPath;
        const value = JSONPath({
          path,
          json: { user, data: jobApplication.toJSON() },
        });

        const regex = new RegExp(`#${placeholder.name}`, 'g');
        if (value.length > 0) {
          body = body.replace(regex, `<b>${value[0]}</b>`);
        }
      });

      const cleanedHtml = body.replace(
        /<span[^>]*class="TiptapEditor_mention__[^"]*"[^>]*>(.*?)<\/span>/g,
        '$1',
      );

      return cleanedHtml;
    } catch (error) {
      this.logger.error(
        `Error fetching rejection email template for job application ID ${jobApplicationId}. ${error.message}`,
      );
      throw new InternalServerErrorException(
        `Failed to fetch email template. ${error.message}`,
      );
    }
  }
  async rejectInterview(user: any, jobApplicationId: string) {
    try {
      const jobApplication = await this.jobApplicationService
        .findById(jobApplicationId)
        .exec();
      if (!jobApplication) {
        throw new NotFoundException(
          `Job application with id: "${jobApplicationId}" not found.`,
        );
      }

      if (!jobApplication.isRejected) {
        jobApplication.isRejected = true;
        await jobApplication.save();
      }

      if (jobApplication.stage) {
        await this.stageModel.findByIdAndUpdate(
          jobApplication.stage,
          { $inc: { jobApplicationsCount: -1 } },
          { new: true },
        );
      }

      this.logger.log(
        `Job application ${jobApplicationId} marked as rejected.`,
      );

      return { message: 'Candidate rejected successfully.' };
    } catch (error) {
      this.logger.error(
        `Error rejecting interview for job application ID ${jobApplicationId}. ${error.message}`,
      );
      throw new InternalServerErrorException(
        `Failed to reject interview. ${error.message}`,
      );
    }
  }

  findAll() {
    return this.interviewModel
      .find()
      .populate({
        path: 'jobApplication',
        select: '_id firstName lastName contactDetails',
        model: 'JobApplication',
      })
      .exec();
  }

  async findOne(interviewId: Types.ObjectId) {
    try {
      const interview = await this.interviewModel
        .findById(interviewId)
        .populate({
          path: 'jobApplication',
          select: '_id firstName lastName contactDetails',
          model: 'JobApplication',
        })
        .exec();
      if (!interview) {
        throw new NotFoundException(
          `The interview with id: "${interviewId}" doesn't exist.`,
        );
      }
      return interview;
    } catch (error) {
      this.logger.error(
        `An error occurred in fetching interview by Id ${interviewId}. ${error?.message}`,
      );
      throw error;
    }
  }

  async update(
    interviewId: Types.ObjectId,
    updateInterviewDto: UpdateInterviewDto,
  ) {
    try {
      const interview = await this.interviewModel.findById(interviewId).exec();

      if (!interview) {
        throw new NotFoundException(
          `The interview with id: "${interviewId}" doesn't exist.`,
        );
      }
      const updatedInterview = await this.interviewModel.findByIdAndUpdate(
        interviewId,
        updateInterviewDto,
        { new: true },
      );
      // this.emitEvent('interview.schedule', updatedInterview);
      return updatedInterview;
    } catch (error) {
      this.logger.error(
        `An error occurred while updating interview by ID ${interviewId}. ${error?.message}`,
      );
      throw error;
    }
  }

  async remove(interviewId: Types.ObjectId) {
    try {
      const interview = await this.interviewModel.findById(interviewId);
      if (!interview) {
        throw new NotFoundException(
          `The interview with id: "${interviewId}" doesn't exist.`,
        );
      }
      await this.interviewModel.findByIdAndDelete(interviewId);
      return { message: `Interview deleted.` };
    } catch (error) {
      this.logger.error(
        `An error occurred while deleting interview by ID ${interviewId}. ${error?.message}`,
      );
      throw error;
    }
  }

  // Helper function to retrieve job application
  async getJobApplication(jobApplicationId: string, user: any) {
    const jobApplication = await this.jobApplicationService
      .findById(jobApplicationId)
      .populate({ path: 'jobId', select: '_id title', model: 'Job' })
      .populate({ path: 'postingOrg', select: '_id title', model: 'Org' })
      .exec();

    if (!jobApplication) {
      throw new Error('Job application not found.');
    }

    const jobApp = jobApplication.toObject() as any; // Convert to plain JS object

    jobApp.org = jobApp.postingOrg;

    return jobApp;
  }

  // Helper function to get email template
  async getEmailTemplate(eventName: EmailTemplateEvent, user: any) {
    let emailTemplate = await this.emailTemplateService
      .findOne({
        eventName,
        isDefault: true,
        org: user.org._id,
        isDeleted: false,
      })
      .exec();

    if (!emailTemplate) {
      emailTemplate = await this.emailTemplateService
        .findOne({
          eventName,
          canDelete: false,
          org: user.org._id,
          isDeleted: false,
        })
        .exec();
    }

    if (!emailTemplate) {
      throw new Error(`Email template not found for the event: ${eventName}.`);
    }

    return emailTemplate;
  }

  // Helper function to replace placeholders in the email body
  async replacePlaceholders(
    body: string,
    user: any,
    jobApplication: any,
    emailTemplateId: string,
    interview: any,
  ) {
    const placeholders = await this.placeholderService
      .find({ emailTemplate: emailTemplateId })
      .exec();

    placeholders.forEach((placeholder) => {
      const path = placeholder.jsonPath;
      const value = JSONPath({
        path,
        json: { user, data: jobApplication, interview: interview },
      });

      const regex = new RegExp(`#${placeholder.name}`, 'g');
      if (value.length > 0) {
        body = body.replace(regex, `<b>${value[0]}</b>`);
      }
    });

    return body;
  }

  async cancelInterview(user: any, jobApplicationId: string): Promise<string> {
    try {
      const jobApplication = await this.getJobApplication(
        jobApplicationId,
        user,
      );
      console.log(jobApplication);

      const latestInterview = await this.interviewModel
        .findOne({
          jobApplication: jobApplicationId,
        })
        .sort({ createdAt: -1 })
        .exec();

      if (!latestInterview) {
        throw new Error('No interview found for this job application.');
      }

      // Find the latest calendar event for the same job application
      const oldCalendar = await this.calendarModel
        .findOne({
          jobApplication: jobApplicationId.toString(),
        })
        .sort({ createdAt: -1 }); // Ensure you get the latest one

      // If an old calendar event exists, mark it as cancelled
      if (oldCalendar) {
        oldCalendar.isCancelled = true;
        await oldCalendar.save();
      }

      const eventName = EmailTemplateEvent.WORKFLOW_INTERVIEW_CANCELLATION;
      const emailTemplate = await this.getEmailTemplate(eventName, user);

      let body = emailTemplate.templateHTMLContent
        ? unescape(emailTemplate.templateHTMLContent)
        : '';
      body = await this.replacePlaceholders(
        body,
        user,
        jobApplication,
        emailTemplate._id.toString(),
        {
          ...latestInterview.toJSON(),
          interviewDate: format(
            new Date(latestInterview.interviewDate),
            'MMM dd yyyy, h:mm a',
          ),
        },
      );

      this.logger.log(
        `Triggered ${eventName} event for interview cancellation.`,
      );
      const cleanedHtml = body.replace(
        /<span[^>]*class="TiptapEditor_mention__[^"]*"[^>]*>(.*?)<\/span>/g,
        '$1',
      );
      return cleanedHtml;
    } catch (error) {
      this.logger.error(`Failed to cancel interview. ${error.message}`);
      throw new InternalServerErrorException(
        `Error when canceling interview. ${error?.message}`,
      );
    }
  }

  async remindInterview(user: any, jobApplicationId: string): Promise<string> {
    try {
      const jobApplication = await this.getJobApplication(
        jobApplicationId,
        user,
      );
      console.log(jobApplication);
      const latestInterview = await this.interviewModel
        .findOne({
          jobApplication: jobApplicationId,
        })
        .sort({ createdAt: -1 })
        .exec();

      if (!latestInterview) {
        throw new Error('No interview found for this job application.');
      }

      const eventName = EmailTemplateEvent.WORKFLOW_INTERVIEW_REMINDER;
      const emailTemplate = await this.getEmailTemplate(eventName, user);
      const istTimeZone = 'Asia/Kolkata';

      let body = emailTemplate.templateHTMLContent
        ? unescape(emailTemplate.templateHTMLContent)
        : '';
      body = await this.replacePlaceholders(
        body,
        user,
        jobApplication,
        emailTemplate._id.toString(),
        {
          ...latestInterview.toJSON(),
          interviewDate: format(
            toZonedTime(new Date(latestInterview.interviewDate), istTimeZone),
            'MMM dd yyyy, h:mm a',
          ),
          // interviewDate: format(new Date(latestInterview.interviewDate), 'MMM dd yyyy, h:mm a')
        },
      );

      this.logger.log(`Triggered ${eventName} event for interview reminder.`);
      const cleanedHtml = body.replace(
        /<span[^>]*class="TiptapEditor_mention__[^"]*"[^>]*>(.*?)<\/span>/g,
        '$1',
      );
      return cleanedHtml;
    } catch (error) {
      this.logger.error(`Failed to send interview reminder. ${error.message}`);
      throw new InternalServerErrorException(
        `Error when sending interview reminder. ${error?.message}`,
      );
    }
  }

  async calculateInterviewRatio(
    userId: string,
  ): Promise<{ showUpRatio: number; attended: number; scheduled: number }> {
    try {
      // Fetch job applications created by the specific user
      const jobApplications = await this.jobApplicationService.find({
        createdBy: userId,
      });

      if (!jobApplications.length) {
        return { showUpRatio: 0, attended: 0, scheduled: 0 };
      }

      // Extract all related interviews from job applications
      const jobApplicationIds = jobApplications.map((app) =>
        app._id.toString(),
      );
      const interviews = await this.interviewModel.find({
        jobApplication: { $in: jobApplicationIds },
      });

      // If no interviews are found, return default values
      if (!interviews?.length) {
        return { showUpRatio: 0, attended: 0, scheduled: 0 };
      }

      const scheduled = interviews.length;
      const attended = interviews.filter(
        (interview) => interview.isCandidateAttended,
      ).length;

      // Calculate the ratio
      const showUpRatio = scheduled ? attended / scheduled : 0;

      return { showUpRatio, attended, scheduled };
    } catch (error) {
      throw new InternalServerErrorException(
        `Failed to calculate the interview ratio. Please try again later. ${error.message}`,
      );
    }
  }

  async acceptMostRecentInterview(
    jobApplicationId: string,
  ): Promise<{ message: string }> {
    try {
      // Fetch the most recent interview for the specified job application
      const mostRecentInterview = await this.interviewModel
        .findOne({ jobApplication: jobApplicationId })
        .sort({ updatedAt: -1 });

      // If no interviews are found, throw an exception
      if (!mostRecentInterview) {
        throw new NotFoundException(
          'No interviews found for the specified job application.',
        );
      }

      mostRecentInterview.isCandidateAttended = true;
      await mostRecentInterview.save();

      return { message: 'The interview Candidate show-up has done.' };
    } catch (error) {
      this.logger.error(
        `Error accepting the most recent interview: ${error.message}`,
      );
      throw new InternalServerErrorException(
        `Failed to accept the interview. ${error.message}`,
      );
    }
  }

  emitEvent(eventName: string, payload: any) {
    // this.logger.log(`Event Name: ${eventName}`);
    // this.logger.log(`Payload: ${JSON.stringify(payload, null, 2)}`);
    this.eventEmitter.emit(eventName, payload);
  }
}
