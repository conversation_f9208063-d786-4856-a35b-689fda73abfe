import { ApiProperty } from "@nestjs/swagger";
import { Transform, TransformFnParams } from "class-transformer";
import { IsNotEmpty, IsNumber, IsString, Max, <PERSON> } from "class-validator";
import { sanitizeWithStyle } from "src/utils/sanitize-with-style";

export class CreateCountryDto {

  @ApiProperty({
    type: Number,
    required: true,
    description: 'Id of the country',
  })
  @IsNotEmpty()
  @IsNumber()
  @Min(252, { message: 'Country ID must be at least 252.' })
  @Max(1000, { message: 'Country ID must not exceed 1000.' })
  countryId: number;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Name of the country',
  })
  @IsNotEmpty()
  @IsString()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  countryName: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Phone code of country',
  })
  @IsNotEmpty()
  @IsString()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  countryPhoneCode: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Currency code of country',
  })
  @IsNotEmpty()
  @IsString()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  currencyCode: string;


}
