import { ApiHideProperty, ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsArray, IsEnum, IsMongoId, IsNotEmpty, IsObject, IsOptional, IsString } from 'class-validator';
import { IntegrationType } from 'src/shared/constants';
import { Transform, TransformFnParams, Type } from "class-transformer";
import { sanitizeWithStyle } from "src/utils/sanitize-with-style";
export class CreateIntegrationDto {

    @ApiProperty({
        description: 'Type of integration',
        enum: IntegrationType,
        default: IntegrationType.GoogleMeet,
        required: true
    })
    @IsEnum(IntegrationType)
    @IsNotEmpty()
    integrationType: IntegrationType;

    @ApiProperty({
        description: 'Type of integration',
        required: false
    })
    @IsNotEmpty()
    type?: string;

    @ApiProperty({
        description: 'Client ID for the integration',
        example: '1234567890-abc.apps.googleusercontent.com',
        required: false
    })
    @IsOptional()
    @IsString()
    // @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    clientId?: string;

    @ApiProperty({
        description: 'Client Secret for the integration',
        example: 'GOCSPX-ExampleSecretString',
        required: false
    })
    @IsOptional()
    @IsString()
    // @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    clientSecret?: string;

    @ApiProperty({
        description: 'Webhook Secret Token for verifying event notifications from Zoom',
        example: 'SuXWkkSZQraplNsm1Rz6sd',
        required: false
    })
    @IsOptional()
    @IsString()
    // @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value)) 
    webhookSecretToken?: string; // Optional since it might not be provided initially


    @ApiProperty({
        description: 'tenant id for the integration',
        required: false,
        example: '3910a1c9-6d52-4fb7-b22e-6bbf7572f4be'
    })
    @IsOptional()
    @IsString()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    tenantId?: string;


    // @ApiPropertyOptional({
    //     description: 'Access token for the integration, if available',
    //     example: 'ya29.a0AfH6SMCXbExampleAccessToken',
    // })
    // @IsOptional()
    // @IsString()
    // @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    // accessToken?: string;


    @ApiHideProperty()
    @IsOptional()
    createdBy?: string;

    // @ApiPropertyOptional({
    //     description: 'Scope values provided for the integration',
    //     example: ['https://www.googleapis.com/auth/calendar'],
    //     type: [String],
    // })
    // @IsOptional()
    // @IsArray()
    // @IsString({ each: true })
    @ApiHideProperty()
    @IsOptional()
    scope?: string[];

    @ApiHideProperty()
    @IsOptional()
    org?: string;

    @ApiPropertyOptional({
        description: 'Phone number ID from WhatsApp Business account',
        example: '***************',
        required: false,
    })
    @IsOptional()
    @IsString()
    whatsappPhoneNumber?: string;

    @ApiPropertyOptional({
        description: 'Permanent WhatsApp access token from Meta Developer portal',
        example: 'EAAGm0PX4ZCpsBAKZA...',
        required: false,
    })
    @IsOptional()
    @IsString()
    whatsappAccessToken?: string;

    @ApiPropertyOptional({
        description: 'WhatsApp templates mapping event name to approved template name',
        example: {
            interview_scheduled: 'template_1',
            offer_letter: 'template_2',
        },
        type: Object,
    })
    @IsOptional()
    @IsObject()
    whatsappTemplates?: Record<string, string>;

    @ApiPropertyOptional({
        description: 'WhatsApp Business Account ID (WABA ID) used for accessing templates and sending messages',
        example: '****************',
        required: false,
    })
    @IsOptional()
    @IsString()
    whatsappBusinessAccountId?: string;

    @ApiPropertyOptional({
        description: 'API key for Hugging Face integration',
        example: 'hf_abcdefgh1234567890',
        required: false,
    })
    @IsOptional()
    @IsString()
    huggingFaceApiKey?: string;

    @ApiPropertyOptional({
        description: 'API key for OpenAI integration',
        example: 'sk-abcdefgh1234567890',
        required: false,
    })
    @IsOptional()
    @IsString()
    openAiApiKey?: string;

    @ApiPropertyOptional({
        description: 'Sender ID used for SMS messages',
        example: 'MYBRAND',
        required: false
    })
    @IsOptional()
    @IsString()
    smsSenderId?: string;

    @ApiPropertyOptional({
        description: 'API key for SMS gateway',
        example: 'abc123xyz890smsapikey',
        required: false
    })
    @IsOptional()
    @IsString()
    smsApiKey?: string;

    @ApiPropertyOptional({
        description: 'API secret for SMS gateway',
        example: 's3cr3tK3yForSMS',
        required: false
    })
    @IsOptional()
    @IsString()
    smsApiSecret?: string;
}
