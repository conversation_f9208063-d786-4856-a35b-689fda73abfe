import { Injectable, InternalServerErrorException, Logger, NotFoundException } from '@nestjs/common';
import { CreateEducationQualificationDto } from './dto/create-education-qualification.dto';
import { UpdateEducationQualificationDto } from './dto/update-education-qualification.dto';
import { ConfigService } from '@nestjs/config';
import { InjectModel } from '@nestjs/mongoose';
import { EducationQualification, EducationQualificationDocument } from './schemas/education-qualifaction.schema';
import { Model, Types } from 'mongoose';

@Injectable()
export class EducationQualificationService {
  private readonly logger = new Logger(EducationQualificationService.name);
  
  constructor(private configService: ConfigService,
    @InjectModel(EducationQualification.name) private educationQualificationModel: Model<EducationQualification>,
  ) { }

  

  async create(createEducationQualificationDto: CreateEducationQualificationDto) {
    try {
      const createdEducationQualification = new this.educationQualificationModel(createEducationQualificationDto);
      return await createdEducationQualification.save();
    } catch (error) {
      this.logger.error('Failed to create education qualification', error);
      throw new InternalServerErrorException('Unknown error when creating the education qualification.');
    }
  }

  async findAll(): Promise<EducationQualificationDocument[]> {
    return this.educationQualificationModel.find({ isDeleted: false })
      .populate({ path: 'createdBy', select: '_id roles firstName' })
      .exec();
  }

  async findOne(educationQualificationId: Types.ObjectId): Promise<EducationQualificationDocument> {
    try {
      const educationQualification = await this.educationQualificationModel.findById(educationQualificationId)
        .populate({ path: 'createdBy', select: '_id roles firstName' })
      if (!educationQualification) {
        throw new NotFoundException(`Education Qualification not found with ID ${educationQualificationId}`);
      }
      return educationQualification;
    } catch (error) {
      this.logger.error(error);
      this.logger.error(`An error occurred in fetching education qualification by Id ${educationQualificationId}. ${error?.message}`);
      throw error;
    }
  }

  async findById(educationQualificationId: Types.ObjectId): Promise<EducationQualificationDocument> {
    try {
      const educationQualification = await this.educationQualificationModel.findById(educationQualificationId)
        .populate({ path: 'createdBy', select: '_id roles firstName' })
        .exec();
      if (!educationQualification || educationQualification.isDeleted) {
        throw new NotFoundException(`Note not found with ID ${educationQualificationId}`);
      }
      return educationQualification;
    } catch (error) {
      this.logger.error(error);
      this.logger.error(`An error occurred in feching note by Id ${educationQualificationId}. ${error?.message}`);
      throw error;

    }
  }

  async update(educationQualificationId: Types.ObjectId, updateEducationQualificationDto: UpdateEducationQualificationDto) {
    try {
      const educationQualification = await this.findById(educationQualificationId);
      if (!educationQualification ||educationQualification.isDeleted) {
        throw new NotFoundException(`Education qualification not found with ID ${educationQualificationId}`);
      }
      const updated = await this.educationQualificationModel.findByIdAndUpdate(educationQualificationId, updateEducationQualificationDto, { new: true });
      return updated;
    } catch (error) {
      this.logger.error(error);
      this.logger.error(`An error occurred while updating education qualification by ID ${educationQualificationId}. ${error?.message}`);
      throw error;
    }
  }

  async remove(educationQualificationId: Types.ObjectId) {
    try {
      const educationQualification = await this.findById(educationQualificationId);
      if (!educationQualification) {
        throw new NotFoundException(`Education qualification not found with ID ${EducationQualification}`);
      }
      await this.educationQualificationModel.deleteOne({ _id: educationQualificationId });
      return { message: 'Education qualification deleted' };
    } catch (error) {
      this.logger.error(`An error occurred while hard deleting by ID ${educationQualificationId}. ${error?.message}`);
      throw new InternalServerErrorException('An error occurred while hard deleting the education qualification');
    }
  }

  async deleteAll() {
    await this.educationQualificationModel.deleteMany();
    return "All education qualification deleted"
  }


}
