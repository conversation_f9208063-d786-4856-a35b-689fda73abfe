import { HttpException, HttpStatus, Injectable, <PERSON><PERSON>, <PERSON>q, Re<PERSON> } from '@nestjs/common';
import { basename } from 'path';
import { File } from '@nest-lab/fastify-multer';
import { FileMetadata, FileMetadataDocument } from './schemas/file-metadata.schema';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { S3Client, PutObjectCommand, DeleteObjectCommand, GetObjectCommand } from '@aws-sdk/client-s3';
import { ConfigService } from '@nestjs/config';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { FastifyReply } from 'fastify';
import textract from 'textract';
import pdfParse from 'pdf-parse';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { UpdateStatusDto } from './dto/update-status.dto';
import { NotFoundException, BadRequestException } from '@nestjs/common';
import { Onboarding } from 'src/onboarding/schemas/onboarding.schema';
import { Org } from 'src/org/schemas/org.schema';
import { OrgService } from 'src/org/org.service';
import { OrgType, StatusConfigType, StatusFunctionality } from 'src/shared/constants';
import { ChangeCustomStatusDto } from 'src/user/dto/change-status.dto';
import { StatusModule } from 'src/status/status.module';
import { Status } from 'src/status/schemas/status.schema';


@Injectable()
export class FileUploadService {

  private readonly s3: S3Client;
  private readonly logger = new Logger(FileUploadService.name);

  // https://docs.digitalocean.com/products/spaces/how-to/use-aws-sdks/

  constructor(
    @InjectModel(FileMetadata.name) private readonly fileMetadataModel: Model<FileMetadataDocument>,
    @InjectModel(Onboarding.name) private readonly onboardingModel: Model<Onboarding>,
    @InjectModel(Status.name) private readonly statusModel: Model<Status>,
    private orgService: OrgService,
    private configService: ConfigService,
    private eventEmitter: EventEmitter2
  ) {
    this.s3 = new S3Client({
      endpoint: this.configService.get<string>('DO_SPACES_ENDPOINT'),
      region: 'us-east-1',
      forcePathStyle: false,
      bucketEndpoint: false,
      credentials: {
        accessKeyId: this.configService.get<string>('DO_SPACES_ACCESS_KEY')!,
        secretAccessKey: this.configService.get<string>('DO_SPACES_SECRET')!,
      },
    });
  }

  async saveFile(file: File, @Req() req: any, identifierId?: Types.ObjectId) {

    const sanitizedFilename = basename(file.originalname).replace(/[^a-z0-9\.\-_]/gi, '_');
    const uniqueFileName = `${Date.now()}-${sanitizedFilename}`;


    try {
      this.logger.log(`Sanitized filename: ${sanitizedFilename}`);

      const bucket = this.configService.get<string>('DO_SPACES_BUCKET');
      if (!bucket) {
        throw new Error('Bucket name is not defined');
      }

      const params = new PutObjectCommand({
        Bucket: bucket,
        Key: uniqueFileName,
        Body: file.buffer,
        ContentType: file.mimetype,
        // ACL: 'private',
        ACL: 'public-read',
        Metadata: {
          originalName: file.originalname,
        },
      });

      const uploadResult = await this.s3.send(params);
      this.logger.log(`Upload result: ${JSON.stringify(uploadResult)}`);

      const endpoint = this.configService.get<string>('DO_SPACES_ENDPOINT');
      // Remove the 'https://' part from the endpoint
      let sanitizedEndpoint
      if (endpoint) {
        sanitizedEndpoint = endpoint.replace(/^https?:\/\//, '');
      }

      const metadata: FileMetadata = {
        originalName: file.originalname,
        uniqueName: uniqueFileName,
        fileSize: file.size || 0,
        fileType: file.mimetype,
        // uploadedBy: req.user._id,
        locationUrl: `https://${bucket}.${sanitizedEndpoint}/${uniqueFileName}`,
        etag: uploadResult.ETag
      };

      if (identifierId)
        metadata.identifier = identifierId;

      // Generate signed URL
      // const signedUrl = await this.generateSignedUrl(bucket, uniqueFileName);
      // metadata.locationUrl = signedUrl;

      this.logger.log(`File uploaded successfully to: ${uniqueFileName}`);

      const createdMetadata = new this.fileMetadataModel(metadata);
      await createdMetadata.save();

      this.logger.log(`File saved: ${uniqueFileName}`);

      return { ...metadata, _id: createdMetadata._id };
    } catch (error) {
      this.logger.error('Error saving file:', error.message);
      throw new HttpException('Could not save file', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async saveFiles(files: File[], @Req() req: any, identifierIds?: Types.ObjectId[]) {
    const savedFilesMetadata = [];

    try {
      if (identifierIds) {
        for (let i = 0; i < files.length; i++) {
          const file = files[i];
          const identifierId = identifierIds[i];
          const metadata = await this.saveFile(file, req, identifierId);
          savedFilesMetadata.push(metadata);
        }
      } else {
        for (const file of files) {
          const metadata = await this.saveFile(file, req);
          savedFilesMetadata.push(metadata);
        }
      }
      return savedFilesMetadata;
    } catch (error) {
      this.logger.error('Error saving files:', error.message);
      throw new HttpException('Could not save files', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  // https://docs.aws.amazon.com/AWSJavaScriptSDK/v3/latest/Package/-aws-sdk-s3-request-presigner/
  async generateSignedUrl(bucket: string, filePath: string): Promise<string> {
    const command = new GetObjectCommand({
      Bucket: bucket,
      Key: filePath,
      // URL expires in 1 hour (adjust as per your requirements)
    });
    const signedUrl = await getSignedUrl(this.s3, command, { expiresIn: 15 * 60 });
    return signedUrl;
  }

  async getMetadataById(fileId: Types.ObjectId): Promise<FileMetadata | null> {
    try {
      const metadata = await this.fileMetadataModel.findById(fileId).exec();
      if (!metadata) {
        throw new HttpException('File not found', HttpStatus.NOT_FOUND);
      }

      // const bucket = this.configService.get<string>('DO_SPACES_BUCKET');
      // if (!bucket) {
      //   throw new Error('Bucket name is not defined');
      // }

      // const signedUrl = await this.generateSignedUrl(bucket, metadata.uniqueName);
      // metadata.locationUrl = signedUrl;
      return metadata;
    } catch (error) {
      this.logger.error('Error fetching file metadata:', error);
      throw new HttpException('Could not fetch file metadata', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async getAllFiles() {
    try {
      return await this.fileMetadataModel.find().exec();
    } catch (error) {
      this.logger.error('Error fetching all files metadata:', error);
      throw new HttpException('Could not fetch all files metadata', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async downloadFile(fileObjId: Types.ObjectId, @Res() res: FastifyReply) {
    try {
      const metadata = await this.getMetadataById(fileObjId);
      if (!metadata) {
        throw new HttpException('File not found', HttpStatus.NOT_FOUND);
      }

      // const bucket = this.configService.get<string>('DO_SPACES_BUCKET');
      // if (!bucket) {
      //   throw new Error('Bucket name is not defined');
      // }

      // const filePath = metadata.uniqueName;

      // const signedUrl = await this.generateSignedUrl(bucket, filePath);

      // Redirect the client (browser) to the presigned URL for download
      if (metadata.locationUrl)
        res.redirect(metadata.locationUrl);
    } catch (error) {
      // Handle errors gracefully
      console.error(error);
      if (error.status) {
        throw new HttpException(error.message, error.status);
      } else {
        throw new HttpException('Internal server error', HttpStatus.INTERNAL_SERVER_ERROR);
      }
    }

  }

  async deleteFile(fileObjId: Types.ObjectId) {
    try {
      const metadata = await this.fileMetadataModel.findByIdAndDelete(fileObjId).exec();
      if (!metadata) {
        throw new HttpException('File not found', HttpStatus.NOT_FOUND);
      }

      const filePath = metadata.uniqueName;
      const bucket = this.configService.get<string>('DO_SPACES_BUCKET');
      if (!bucket) {
        throw new Error('Bucket name is not defined');
      }

      const params = new DeleteObjectCommand({
        Bucket: bucket,
        Key: filePath,
      });

      await this.s3.send(params);
      this.logger.log(`File deleted: ${filePath}`);
      return { message: 'File deleted successfully' };
    } catch (error) {
      this.logger.error('Error deleting file:', error.message);
      throw new HttpException('Could not delete file', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async updateStatus(fileObjId: Types.ObjectId, updateStatusDto: UpdateStatusDto, @Req() req: any) {
    const { status, user, org } = updateStatusDto;
    const updatedFile = await this.fileMetadataModel.findByIdAndUpdate(fileObjId, { status }, { new: true });
    if (!updatedFile) {
      throw new NotFoundException(`File with ID ${fileObjId} not found`);
    }
    const createdByUser = req.user;
    console.log('updatedFile', updatedFile)

    if (updateStatusDto.org && status === 'rejected') {

      let orgId = new Types.ObjectId(updateStatusDto.org);

      const pendingStatus = await this.statusModel.findOne({ functionality: StatusFunctionality.PENDING, statusType: StatusConfigType.ADMIN_CUSTOMER_ORG }).select('_id');
      if (pendingStatus) {
        const changeStatusDto: ChangeCustomStatusDto = { customStatus: pendingStatus._id.toString() };
        await this.orgService.changeCustomStatus(orgId, changeStatusDto, createdByUser);
      }
    }
    this.emitEvent('onboarding.file.status.updated', {
      updatedFile,
      status,
      user,
      org,
      createdByUser,
    });

    this.emitEvent('file.status.approve.reject', {
      updatedFile,
      status,
      user,
      org: org,
      createdByUser
    });
    return updatedFile;
  }

  async updateVendorFileStatus(fileObjId: Types.ObjectId, updateStatusDto: UpdateStatusDto, @Req() req: any) {
    const { status, user, org } = updateStatusDto;
    const updatedFile = await this.fileMetadataModel.findByIdAndUpdate(fileObjId, { status }, { new: true });
    if (!updatedFile) {
      throw new NotFoundException(`File with ID ${fileObjId} not found`);
    }
    const createdByUser = req.user;
    console.log('updatedFile', updatedFile)


    // 2. Fetch the onboarding data using the file ID
    const onboardingData = await this.onboardingModel
      .findOne({ identifiers: { $elemMatch: { attachmentUrls: fileObjId } } })
      .select('identifiers') // Don't populate the org field
      .populate({
        path: 'identifiers.attachmentUrls',
        select: '_id status',
      })
      .populate({
        path: 'org',
        select: '_id title orgType description customers vendors isAdminOrg logo isOnboarded isApproved companyId',
        model: 'Org',
        populate: [
          {
            path: 'logo',
            select: '_id originalName uniqueName fileSize fileType locationUrl etag status',
            model: 'FileMetadata',
          },
          {
            path: 'contactAddress',
            select: 'apartment street city postalCode',
            model: 'AddressInformation',
          },
          //  {
          //   path: 'contactDetails',
          //   match: { _id: { $ne: null } },
          //   select: '_id contactEmail contactNumber',
          // },
          {
            path: 'companyId', // ✅ Populate companyId (another Org)
            select: '_id title orgType description customers vendors isAdminOrg logo isOnboarded isApproved',
            model: 'Org',
            populate: [
              {
                path: 'logo',
                select: '_id originalName uniqueName fileSize fileType locationUrl etag status',
                model: 'FileMetadata',
              },
              {
                path: 'contactAddress',
                select: 'apartment street city postalCode',
                model: 'AddressInformation',
              },
              // {
              //   path: 'contactDetails',
              //   match: { _id: { $ne: null } },
              //   select: '_id contactEmail contactNumber',
              // }

            ],
          },
        ],
      })
    if (!onboardingData) {
      throw new NotFoundException(`Onboarding data for file with ID ${fileObjId} not found`);
    }

    // Safely get the org ID
    const orgDoc = onboardingData.org as any;
    console.log("orgDoc", orgDoc)
    if (!orgDoc || !orgDoc._id) {
      throw new BadRequestException('Organization ID is missing');
    }

    const orgId = new Types.ObjectId(orgDoc._id);


    // 4. Check the status of all files in the onboarding process
    let allApproved = true;
    let anyRejected = false;
    let anyPending = false; // ✅ Track if at least one file is still pending

    onboardingData.identifiers.forEach(identifier => {
      if (identifier.attachmentUrls && Array.isArray(identifier.attachmentUrls)) {
        identifier.attachmentUrls.forEach(file => {
          if (file.status === 'rejected') {
            anyRejected = true;
          }
          if (file.status === 'pending') {
            anyPending = true; // ✅ Identify if any file is still pending
          }
          if (file.status !== 'approved') {
            allApproved = false;
          }
        });
      }
    });


    // Fetch status IDs from statusModel
    const pendingStatus = await this.statusModel.findOne({ functionality: StatusFunctionality.PENDING, statusType: "vendor-org" }).select('_id');
    const activateStatus = await this.statusModel.findOne({ functionality: StatusFunctionality.ACTIVATE, statusType: "vendor-org" }).select('_id');
    const rejectStatus = await this.statusModel.findOne({ functionality: StatusFunctionality.REJECT, statusType: "vendor-org" }).select('_id');
    const onHoldStatus = await this.statusModel.findOne({ functionality: StatusFunctionality.ONHOLD, statusType: "vendor-org" }).select('_id');
    if (!pendingStatus || !activateStatus || !rejectStatus || !onHoldStatus) {
      throw new NotFoundException(`Could not find status IDs for "PENDING", "ACTIVATE", or "REJECT" under "vendor-org".`);
    }



    let newCustomStatus;

    if (anyRejected) {
      newCustomStatus = rejectStatus._id; // ✅ If any file is rejected, use REJECT status
    } else if (allApproved) {
      if (orgDoc.isApproved === true) {
        newCustomStatus = activateStatus._id; // ✅ All files approved & org isApproved → Activate
      } else {
        newCustomStatus = pendingStatus._id; // ✅ All files approved but org is NOT approved → On Hold 
      }
    } else if (anyPending) {
      newCustomStatus = pendingStatus._id; // ✅ If at least one file is pending, use PENDING
    } else {
      newCustomStatus = pendingStatus._id; // ✅ Default to PENDING (fallback case)
    }

    // Emit event with proper org ID
    this.emitEvent('onboarding.file.status.updated', {
      updatedFile,
      status,
      user,
      org: orgId.toString(),
      createdByUser,
    });

    this.emitEvent('onboarding.file.status', {
      updatedFile,
      status,
      user,
      org: orgId.toString(),
      createdByUser,
      orgTitle: orgDoc.title, // Include org title in the payload
      vendorEmail: orgDoc.companyId.contactEmail
    });

    if (orgId) {

      const changeStatusDto: ChangeCustomStatusDto = { customStatus: newCustomStatus.toString() };
      await this.orgService.changeCustomStatus(orgId, changeStatusDto, createdByUser);
    }

    return updatedFile;
  }


  //parseWithTextract is help method for parseResume if the file type is not a pdf it will handle other file types

  async parseResume(file: Express.Multer.File): Promise<any> {
    try {
      if (!file.buffer || !(file.buffer instanceof Buffer)) {
        throw new HttpException('No valid file buffer provided', HttpStatus.BAD_REQUEST);
      }

      const mimeType = file.mimetype;
      let text: string;

      if (mimeType === 'application/pdf') {
        const data = await pdfParse(file.buffer);
        text = data.text;
      } else {
        text = await this.parseWithTextract(file.buffer, mimeType);
      }

      return { text };
    } catch (error) {
      this.logger.error(`Error parsing resume for file ${file.originalname}: ${error.message}`);
      throw new HttpException('Could not parse resume', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  private parseWithTextract(buffer: Buffer, mimeType: string): Promise<string> {
    return new Promise((resolve, reject) => {
      textract.fromBufferWithMime(mimeType, buffer, (err, text) => {
        if (err) {
          reject(err);
        } else {
          resolve(text);
        }
      });
    });
  }

  emitEvent(eventName: string, payload: any) {
    this.logger.debug(payload)
    this.eventEmitter.emit(eventName, payload);
  }

}
