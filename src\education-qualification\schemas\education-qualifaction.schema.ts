import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Types } from 'mongoose';
import { Job } from 'src/job/schemas/job.schema';
import { BasicUser } from 'src/user/schemas/basic-user.schema';


export type EducationQualificationDocument = HydratedDocument<EducationQualification>;

@Schema({
  timestamps: true
})
export class EducationQualification {

  @Prop({
    type: Types.ObjectId,
    required: true,
    ref: 'Job'
  })
  jobId: Job;

  @Prop({
    type: String,
    required: true,
    trim: true,
  })
  courseName: string;

  @Prop({
    type: String,
    required: true,
    trim: true,
  })
  university: string;

  @Prop({
    type: Date,
    required: false,
    default: null
  })
  startDate?: Date;

  @Prop({
    type: Date,
    required: false,
    default: null
  })
  endDate?: Date;

  @Prop({
    type: Boolean,
    default: false,
    required: false,
  })
  isDeleted?: boolean;

  @Prop({
    required: true,
    type: Types.ObjectId, ref: 'BasicUser'
  })
  createdBy: BasicUser;

}

export const EducationQualificationSchema = SchemaFactory.createForClass(EducationQualification);
