import sanitizeHtml from 'sanitize-html';

// Custom sanitize-html configuration to allow color and bold tags
const sanitizeHtmlConfig = {
  allowedTags: ['p', 'br', 'strong', 'b', 'em', 'i', 'u', 'span', 'font', 'div', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6'],
  allowedAttributes: {
    'span': ['style'],
    'font': ['color', 'size', 'face'],
    'div': ['style', 'class'],
    'p': ['style', 'class'],
    '*': ['class']
  },
  allowedStyles: {
    '*': {
      'color': [/^#(?:[0-9a-fA-F]{3}){1,2}$/, /^rgb\(/, /^rgba\(/],
      'background-color': [/^#(?:[0-9a-fA-F]{3}){1,2}$/, /^rgb\(/, /^rgba\(/],
      'font-weight': [/^(bold|normal|[1-9]00)$/],
      'font-style': [/^(italic|normal)$/],
      'text-decoration': [/^(underline|none)$/],
      'font-size': [/^(?:\d+(?:\.\d+)?(?:px|em|rem|%))$/]
    }
  },
  allowedSchemes: ['http', 'https', 'ftp', 'mailto'],
  parseStyleAttributes: true
};

/**
 * Sanitize HTML content with custom configuration that allows color and bold tags
 * @param html - The HTML content to sanitize
 * @returns Sanitized HTML string
 */
export function sanitizeWithStyle(html: string): string {
  return sanitizeHtml(html, sanitizeHtmlConfig);
}
