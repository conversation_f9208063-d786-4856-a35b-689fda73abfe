<!DOCTYPE html>
<html>
    <head>
        <title>Gana | APIs for Talency</title>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <script src="https://cdn.tailwindcss.com"></script>
        <script src="https://cdn.socket.io/4.3.2/socket.io.min.js" integrity="sha384-KAZ4DtjNhLChOB/hxXuKqhMLYvx3b5MlT55xPEiNmREKRzeEm+RVPlTnAn0ajQNs" crossorigin="anonymous"></script>
    <script>
      const _localSocket = io('ws://localhost:3002');
      const socket = io('wss://apis.gana.talency.in');

      socket.on('connect', function() {
        console.log('Connected');
        socket.emit('ping', { test: 'test' });

        socket.emit('events', { test: 'test' });
        socket.emit('identity', 0, response =>
          console.log('Identity:', response),
        );
      });
      socket.on('events', function(data) {
        console.log('event', data);
      });

      socket.on('ping', function(data) {
        console.log('ping', data);
      });

      socket.on('pong', function(data) {
        console.log('pong', data);
      });
      socket.on('exception', function(data) {
        console.log('event', data);
      });
      socket.on('disconnect', function() {
        console.log('Disconnected');
      });
    </script>
      </head>
    <body>
        <main class=" min-h-screen flex-col justify-center">
            <section class="flex items-center justify-center min-h-screen  subpixel-antialiased bg-gray-50">
                <div class="relative flex m-auto flex-col justify-center overflow-hidden bg-gray-50 py-6 sm:py-30 sm:px-10 items-center">
                    <h1 class="text-4xl tracking-widest	font-extrabold	text-red-600 animate-pulse">Gana</h1>
                    <h2 class="text-2xl tracking-wide py-4 font-semibold">APIs for Talency.</h2>
                    <p class="text-xl">Gana is built on top of NestJS, with Fastify Engine for better performance.</p>
                    <h2 class="text-2xl tracking-wide py-4 font-semibold">API Explorer</h2>
                    <p class="text-xl">You can try the explorer <a  class="cursor-pointer underline decoration-black underline-offset-4	decoration-2" href="./explorer">here</a>.</p>
                </div>
            </section>
            <!-- <section>
                
            </section> -->
        </main>
    </body>
</html>