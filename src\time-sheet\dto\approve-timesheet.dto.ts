import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsNumber, Min, MaxLength } from 'class-validator';

export class ApproveTimesheetDto {
  @ApiProperty({ 
    description: 'Optional comments for approval',
    required: false,
    maxLength: 500
  })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  comments?: string;

  @ApiProperty({ 
    description: 'Approved hours (if different from submitted hours)',
    required: false,
    minimum: 0
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  approvedHours?: number;
}
