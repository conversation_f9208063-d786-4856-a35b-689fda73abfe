import { <PERSON>, Get, Post, Body, Patch, Param, Delete, Logger, UseG<PERSON>s, Query, BadRequestException, HttpException, HttpStatus, Req } from '@nestjs/common';
import { RolesService } from './roles.service';
import { CreateRolesDto } from './dto/create-roles.dto';
// import { UpdateStatusDto } from './dto/update-status.dto';
import { ApiBearerAuth, ApiOperation, ApiParam, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Roles } from 'src/auth/decorators/roles.decorator';
import { Role } from 'src/auth/enums/role.enum';
import { AuthJwtGuard } from 'src/auth/guards/auth-jwt/auth-jwt.guard';
import { validateObjectId } from 'src/utils/validation.utils';
import { UpdateRolesDto } from './dto/update-roles.dto';

@Controller('')
@ApiTags('Roles')
export class RolesController {
    private readonly logger = new Logger(RolesController.name);

    constructor(private readonly rolesService: RolesService) { }

    @Post()
    @ApiOperation({
        summary: 'Create a new Role',
        description: `This endpoint allows you to create a new Role.`
    })
    @ApiBearerAuth()
    @UseGuards(AuthJwtGuard)
    @ApiResponse({ status: 201, description: 'The Role is created.' })
    @ApiResponse({ status: 400, description: 'Bad Request / Data.' })
    @ApiResponse({ status: 401, description: 'Unauthorized access. Authentication is required.' })
    create(@Req() req: any, @Body() createStatusDto: CreateRolesDto) {
        if (createStatusDto.isDefault === undefined) {
            createStatusDto.isDefault = false;
        }
        if (!createStatusDto.isDefault) {
            if(!req.user.org){
                throw new BadRequestException('orgId is not found for this user');
            }
            createStatusDto.orgId = req.user.org._id;
        }
        createStatusDto.createdBy = req.user._id;
        return this.rolesService.create(createStatusDto);
    }

    @Get('all')
    @ApiOperation({ summary: 'Get list of Roles by orgId', description: `This endpoint gives the list of Roles by type.` })
    @ApiBearerAuth()
    @UseGuards(AuthJwtGuard)
    @ApiResponse({ status: 200, description: 'All Roles are retrieved.' })
    @ApiResponse({ status: 401, description: 'Unauthorized ' })
    @ApiQuery({ name: 'orgId', required: false, type: String, description: 'OrgId to fetch Roles' })
    @ApiQuery({ name: 'isDefault', required: false, type: Boolean, description: 'to fetch default roles' })
    @ApiQuery({ name: 'search', required: false, type: String, description: 'search param for roles' })
    @ApiQuery({ name: 'page', required: true, type: Number, description: 'Page number for pagination', example: 1 })
    @ApiQuery({ name: 'limit', required: true, type: Number, description: 'Number of records per page', example: 10 })

    
    async getCountriesByStatus(@Query('orgId') orgId?: string,@Query('isDefault') isDefault?: boolean,@Query('search') search?: string,
    @Query('page') page: number = 1, @Query('limit') limit: number = 10) {
        // if (!orgId) {
        //     throw new BadRequestException('orgId query parameter is required');
        // }
        console.log(isDefault)
        return await this.rolesService.findAllRolesByOrg(orgId,isDefault,search,page,limit);
    }

    // @Get('all')
    // @ApiOperation({
    //     summary: 'Retrieve all Roles',
    //     description: `This endpoint returns a list of all Roles.`
    // })
    // @ApiBearerAuth()
    // @UseGuards(AuthJwtGuard)
    // @ApiResponse({ status: 200, description: 'All Roles are retrieved.' })
    // @ApiResponse({ status: 400, description: 'Bad Request / Data.' })
    // @ApiResponse({ status: 401, description: 'Unauthorized access. Authentication is required.' })
    // findAll() {
    //     return this.rolesService.findAll();
    // }

    @Get(':roleId')
    @ApiOperation({
        summary: 'Retrieve a Role by Id',
        description: `This endpoint retrieves a Role by its Id.`
    })
    @ApiBearerAuth()
    @UseGuards(AuthJwtGuard)
    @ApiResponse({ status: 200, description: 'Role is retrieved.' })
    @ApiResponse({ status: 401, description: 'Unauthorized access. Authentication is required.' })
    @ApiResponse({ status: 404, description: 'Role not found.' })
    @ApiParam({ name: 'roleId', description: 'ID of the role' })
    findOne(@Param('roleId') roleId: string) {
        const roleObjId = validateObjectId(roleId);
        return this.rolesService.findById(roleObjId);
    }

    @Patch(':roleId')
    @ApiOperation({
        summary: 'Update a Role by Id',
        description: `This endpoint updates a Role by Id.`
    })
    @ApiBearerAuth()
    @UseGuards(AuthJwtGuard)
    @ApiResponse({ status: 200, description: 'Role is updated.' })
    @ApiResponse({ status: 401, description: 'Unauthorized access. Authentication is required.' })
    @ApiResponse({ status: 404, description: 'Role not found.' })
    @ApiParam({ name: 'roleId', description: 'ID of the Role' })
    update(@Req() req: any,@Param('roleId') roleId: string, @Body() updateStatusDto: UpdateRolesDto) {
        const statusObjId = validateObjectId(roleId);
        if (updateStatusDto.isDefault === undefined) {
            updateStatusDto.isDefault = false;
        }
        if (!updateStatusDto.isDefault) {
            if(!req.user.org){
                throw new BadRequestException('orgId is not found for this user');
            }
            updateStatusDto.orgId = req.user.org._id;
        }
        updateStatusDto.createdBy = req.user._id;
        
        return this.rolesService.update(statusObjId, updateStatusDto);
    }

    @Delete(':roleId')
    @ApiOperation({
        summary: 'Delete a role by Id',
        description: `This endpoint deletes a role by Id.`
    })
    @ApiBearerAuth()
    @UseGuards(AuthJwtGuard)
    @ApiResponse({ status: 200, description: 'role is deleted' })
    @ApiResponse({ status: 401, description: 'Unauthorized access. Authentication is required.' })
    @ApiResponse({ status: 404, description: 'role not found.' })
    @ApiParam({ name: 'roleId', description: 'ID of the role' })
    remove(@Param('roleId') roleId: string) {
        const roleObjId = validateObjectId(roleId);
        return this.rolesService.delete(roleObjId);
    }
}
