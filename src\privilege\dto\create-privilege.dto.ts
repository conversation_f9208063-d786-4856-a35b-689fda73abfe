import { ApiProperty, PartialType } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from 'class-validator';

export class CreatePrivilegeDto {
  @ApiProperty({
    description: 'Name of the privilege',
    example: 'jobsRead',
  })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Detailed description of the privilege',
    example: 'Allows jobs to be read (retrieved)',
  })
  @IsNotEmpty()
  @IsString()
  description: string;

  @ApiProperty({
    description: 'Resource associated with the privilege',
    example: 'jobs',
  })
  @IsNotEmpty()
  @IsString()
  resource: string;

  @ApiProperty({
    description: 'Action associated with the privilege',
    example: 'read',
  })
  @IsNotEmpty()
  @IsString()
  action: string;
}
