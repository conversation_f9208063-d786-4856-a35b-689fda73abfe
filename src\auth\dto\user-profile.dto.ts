import { ApiProperty } from "@nestjs/swagger";
import { IsString, IsArray, IsEnum, IsMongoId, IsOptional, ValidateNested } from "class-validator";
import { Transform, TransformFnParams, Type } from 'class-transformer';
import sanitizeWithStyle from 'sanitize-html'
import { AddressInformationDto } from "src/common/dto/address-information.dto";
import { ContactInformationDto } from "src/common/dto/contact-information.dto";
import { Country } from "src/country/schemas/country.schema";
import { SourceType } from "src/shared/constants";

export class UserProfileDto {

  @ApiProperty({
    type: String,
    required: false,
    description: 'Country of the user',
  })
  @IsOptional()
  @IsString()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  country?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'State of the user',
  })
  @IsOptional()
  @IsString()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  state?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'City of the user',
  })
  @IsOptional()
  @IsString()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  city?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Postal code of the user',
  })
  @IsOptional()
  @IsString()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  postalCode?: string;

  @ApiProperty({
    type: [ContactInformationDto],
    required: false,
    description: 'Contact information of user'
  })
  @IsOptional()
  contactDetails?: ContactInformationDto[];

  @ApiProperty({
    type: AddressInformationDto,
    required: false,
    description: 'Contact address'
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => AddressInformationDto)
  contactAddress?: AddressInformationDto[];

  // @ApiProperty({
  //   type: String,
  //   required: false,
  //   default: UserStatus.PENDING,
  //   enum: UserStatus,
  //   description: 'Freelancer status',
  // })
  // @IsOptional()
  // @IsString()
  // @IsEnum(UserStatus)
  // status?: UserStatus

  @ApiProperty({
    type: String,
    required: false,
    default: SourceType.ADMIN_PORTAL,
    enum: SourceType,
    description: 'Source where the organization was added',
  })
  @IsString()
  @IsOptional()
  @IsEnum(SourceType)
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  source?: string;

  @ApiProperty({ type: [String], description: 'List of Interested Countries', required: false })
  @IsArray()
  @IsOptional()
  @IsString({ each: true })
  @Transform((params: TransformFnParams) => params.value.map((value: string) => sanitizeWithStyle(value)))
  interestedCountries?: string[];

  @ApiProperty({ type: [String], description: 'List of Interested Domains', required: false })
  @IsArray()
  @IsOptional()
  @IsString({ each: true })
  @Transform((params: TransformFnParams) => params.value.map((value: string) => sanitizeWithStyle(value)))
  interestedDomains?: string[];

  @ApiProperty({ type: [String], description: 'List of Interested Skills', required: false })
  @IsArray()
  @IsOptional()
  @IsString({ each: true })
  @Transform((params: TransformFnParams) => params.value.map((value: string) => sanitizeWithStyle(value)))
  interestedSkills?: string[];


}