// src/invoice/dto/create-invoice.dto.ts
import { ApiHideProperty, ApiProperty } from '@nestjs/swagger';
import { Transform, TransformFnParams } from 'class-transformer';
import { IsString, IsNotEmpty, IsNumber, IsEnum, IsDateString, IsOptional, IsISO8601, IsMongoId, IsArray } from 'class-validator';
import { sanitizeWithStyle } from "src/utils/sanitize-with-style";

export class CreateInvoiceDto {
  @ApiProperty({
    type: String,
    required: false,
    description: 'BGV handler name',
  })
  @IsString()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  invoiceNumber: string;

  @ApiProperty({
    type: String,
    required: false,
    description: ''
  })
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  jobApplication?: string;


  @ApiProperty({
    type: Number,
    required: true,
    description: ''
  })
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  @IsNotEmpty()
  amount: number;


  @IsOptional()
  @IsEnum(['paid', 'unpaid', 'overdue'])
  status?: string;

  @ApiProperty({
    type: Date,
    required: false,
    description: 'Date of joining',
    default: new Date(Date.UTC(new Date().getUTCFullYear(), new Date().getUTCMonth(), new Date().getUTCDate(), new Date().getUTCHours(), new Date().getUTCMinutes())),
  })
  // YYYY-MM-DDThh:mm:ssZ - example : 2024-05-27T00:00:00.000Z
  @IsISO8601({ strict: true })
  @IsOptional()
  dueDate?: Date;

  @ApiProperty({
    type: Date,
    required: false,
    description: 'Date of joining',
    default: new Date(Date.UTC(new Date().getUTCFullYear(), new Date().getUTCMonth(), new Date().getUTCDate(), new Date().getUTCHours(), new Date().getUTCMinutes())),
  })
  // YYYY-MM-DDThh:mm:ssZ - example : 2024-05-27T00:00:00.000Z
  @IsISO8601({ strict: true })
  @IsOptional()
  raisedDate?: Date;

  @ApiProperty({
    example: ['665cdb6be21e7d396c3a2f1a', '665cdb6be21e7d396c3a2f2a'],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsMongoId({ each: true })
  fileMetaDataId?: string[];

  @ApiProperty({
    type: String,
    required: false,
  })
  @IsOptional()
  // @IsMongoId()
  @IsString()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  billedByOrg?: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  @IsOptional()
  // @IsMongoId()
  @IsString()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  employee?: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  @IsOptional()
  // @IsMongoId()
  @IsString()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  billedToOrg?: string;

  @ApiHideProperty()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  generatedBy?: string;

  @ApiHideProperty()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  forwardedBy?: string;
  // 🔽 NEW FIELDS BELOW

  @ApiProperty({
    enum: ['weekly', 'monthly'],
    required: false,
  })
  @IsEnum(['weekly', 'monthly'])
  invoiceType?: 'weekly' | 'monthly';

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  @Transform(({ value }) => sanitizeWithStyle(value))
  weekLabel?: string;

  @ApiProperty({ type: Date, required: false })
  @IsOptional()
  @IsISO8601({ strict: true })
  weekStartDate?: Date;

  @ApiProperty({ type: Date, required: false })
  @IsOptional()
  @IsISO8601({ strict: true })
  weekEndDate?: Date;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  @Transform(({ value }) => sanitizeWithStyle(value))
  period?: string;

  @ApiProperty({ required: false })
  @IsNotEmpty()
  hours?: number;

}
