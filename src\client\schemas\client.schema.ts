import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Types } from 'mongoose';
import { Country } from 'src/country/schemas/country.schema';
import { Industry } from 'src/industry/schemas/industry.schema';
import { ClientStatus, HeadCount } from 'src/shared/constants';
import { BasicUser } from 'src/user/schemas/basic-user.schema';

export type ClientDocument = HydratedDocument<Client>;

@Schema({ timestamps: true })
export class Client {

  @Prop({
    required: true,
    unique: true,
    trim: true,
  })
  name: string;

  @Prop({ type: Types.ObjectId, ref: 'Client' })
  parentClient: Client;

  @Prop({
    required: false,
    trim: true,
  })
  websiteUrl?: string;

  @Prop({
    required: false,
    trim: true,
  })
  contactNumber?: string;

  @Prop({ type: Types.ObjectId, ref: 'Industry' })
  industry?: Industry;

  @Prop({
    type: String,
    required: false,
    trim: true,
    default: HeadCount.NOT_SPECIFIED,
    enum: Object.values(HeadCount),
  })
  headCount?: string;


  @Prop({
    required: false,
    trim: true,
  })
  description?: string;

  @Prop({ type: String, required: false, trim: true })
  address?: string;

  @Prop({ type: String, required: false, trim: true })
  city?: string;

  @Prop({ type: String, required: false, trim: true })
  stateOrProvince?: string;

  @Prop({
    required: false,
    type: Types.ObjectId, ref: 'Country'
  })
  country?: Country;

  @Prop({ type: String, required: false, trim: true })
  pinOrZipCode?: string;

  @Prop({
    required: false,
    type: Types.ObjectId, ref: 'BasicUser'
  })
  ownedBy?: BasicUser;

  @Prop({
    required: true,
    type: Types.ObjectId, ref: 'BasicUser'
  })
  createdBy: BasicUser;

  @Prop({
    required: false,
    default: false,
    type: Boolean,
  })
  isDeleted?: boolean;

  @Prop({
    required: false,
    default: false,
    type: Boolean,
  })
  isDuplicate?: boolean;

  @Prop({
    type: String,
    required: false,
    trim: true,
    default:ClientStatus.PROSPECT,
    enum: Object.values(ClientStatus),
  })
  status?: string;





}

export const ClientSchema = SchemaFactory.createForClass(Client);

