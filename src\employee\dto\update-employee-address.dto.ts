import { IsBoolean, IsOptional, IsString, IsMongoId } from 'class-validator';
import { Transform, TransformFnParams, Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';
import { sanitizeWithStyle } from "src/utils/sanitize-with-style";

class AddressDto {
    @ApiProperty({
        type: String,
        required: true,
    })
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    @IsString()
    addressLine1: string;

    @ApiProperty({
        type: String,
        required: false,
    })
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    @IsOptional()
    @IsString()
    landmark?: string;

    @ApiProperty({
        type: String,
        required: false,
        description: 'MongoDB ID of the country of the user',
    })
    @IsOptional()
    @IsMongoId({ message: 'Country must be a valid MongoDB ObjectId.' })
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    country?: string;

    @ApiProperty({
        type: String,
        required: false,
        description: 'MongoDB ID of the state of the user',
    })
    @IsOptional()
    @IsMongoId({ message: 'State must be a valid MongoDB ObjectId.' })
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    state?: string;

    @ApiProperty({
        type: String,
        required: false,
        description: 'MongoDB ID of the city of the user',
    })
    @IsOptional()
    @IsMongoId({ message: 'City must be a valid MongoDB ObjectId.' })
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    city?: string;


    @ApiProperty({
        type: String,
        required: false,
    })
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    @IsString()
    pinCode?: string;

    @ApiProperty({
        example: '665cdb6be21e7d396c3a2f1a',
        required: false,
    })
    @IsOptional()
    @IsMongoId()
    addressProofDoc?: string;
}

export class UpdateEmployeeAddressDto {
   
    @IsOptional()
    @Type(() => AddressDto)
    currentAddress: AddressDto;

    @IsOptional()
    @Type(() => AddressDto)
    permanentAddress?: AddressDto;

    @ApiProperty({
        type: Boolean,
        required: false,
        description: 'To indicate if the permanent address is the same as the current address',
    })
    @IsBoolean()
    @IsOptional()
    sameAsCurrentAddress?: boolean;
}
