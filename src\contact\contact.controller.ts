import { Controller, Get, Post, Body, Patch, Param, Delete, Req, UseGuards, Query, Logger, BadRequestException } from '@nestjs/common';
import { ContactService } from './contact.service';
import { CreateContactDto } from './dto/create-contact.dto';
import { UpdateContactDto } from './dto/update-contact.dto';
import { ApiOperation, ApiBearerAuth, ApiResponse, ApiParam, ApiTags, ApiQuery, ApiBody } from '@nestjs/swagger';
import { Roles } from 'src/auth/decorators/roles.decorator';
import { Role } from 'src/auth/enums/role.enum';
import { AuthJwtGuard } from 'src/auth/guards/auth-jwt/auth-jwt.guard';
import { RolesGuard } from 'src/auth/guards/roles/roles.guard';
import { validateObjectId } from 'src/utils/validation.utils';
import { MoveToDto } from 'src/common/dto/move-to.dto';
import { ChangeStatusDto } from 'src/common/dto/change-status.dto';
import { QueryContactDto } from './dto/query-contact.dto';
import { CommentDto } from 'src/common/dto/comment.dto';
import { Types } from 'mongoose';
import { ChangeStatusContactDto } from 'src/common/dto/contact-change-status.dto';
import { AddToFavouritesDto } from './dto/query-fav.dto';
import { QueryContactCountDto } from './dto/query-contact-count.dto';

@Controller('')
@ApiTags('Contacts')
export class ContactController {

  private readonly logger = new Logger(ContactController.name);

  constructor(private readonly contactService: ContactService) { }


  @Post()
  @ApiOperation({ summary: 'Creates a new contact', description: `This endpoint allows you to create a new contact. This is accessible only for "${Role.BUHead}"` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager, Role.Vendor)
  @Roles()
  @ApiResponse({ status: 201, description: 'Contact is saved.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Data ' })
  @ApiResponse({ status: 401, description: 'Unauthorized ' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role "BUHead" can only use this end point.' })
  create(@Req() req: any, @Body() createContactDto: CreateContactDto) {
    return this.contactService.create(createContactDto, req.user);
  }

  @Get()
  @ApiOperation({ summary: 'Retrieve all contacts', description: 'This endpoint returns a list of all contacts. This endpoint accessible by everyone.' })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager, Role.Vendor)
  @Roles()
  @ApiResponse({ status: 200, description: 'All contacts are retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized ' })
  // @ApiResponse({ status: 403, description: 'Forbidden, user with role super admin, admin and salesrep  can only use this end point.' })
  findAll(@Req() req: any, @Query() query: QueryContactDto) {
    let userId: any;
    let orgId: any;
    let assignTo: any;
    if (req.user?.roles?.includes(Role.Admin)) {
      userId = req.user._id;
      orgId = req.user.org._id;
    }
    else if (req.user?.roles?.includes(Role.AccountManager)) {
      if (req.user.orgAdmin) {
        userId = req.user._id
      }
      assignTo = req.user._id;
      orgId = req.user.org._id;
    }
    else {
      userId = req.user._id
      orgId = req.user.org._id;
    }
    // let userId = req.user._id;
    // if (req.user.orgAdmin) {
    //   userId = req.user.orgAdmin
    // }
    return this.contactService.getOnlyActiveContacts(query, userId, assignTo, orgId);
  }

  @Get('all')
  @ApiOperation({ summary: 'Retrieve all contacts including soft deleted contacts', description: `This endpoint returns a list of all contacts. This endpoint accessible by everyone.` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager, Role.Vendor)
  @Roles()
  @ApiResponse({ status: 200, description: 'All contacts are retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized ' })
  // @ApiResponse({ status: 403, description: 'Forbidden user with role super admin and admin can only use this end point.' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number', example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of items per page', example: 10 })
  getAllContacts(@Query('page') page: number = 1, @Query('limit') limit: number = 10) {
    return this.contactService.getAllContacts(page, limit);
  }

  @Get('find-all-soft-deleted')
  @ApiOperation({ summary: 'Retrieves only soft delete contacts', description: `This endpoint returns a list of all soft deleted contacts. This endpoint accessible by everyone.` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager, Role.Vendor)
  @Roles()
  @ApiResponse({ status: 200, description: 'All contacts are retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized ' })
  // @ApiResponse({ status: 403, description: 'Forbidden user with role super admin and admin can only use this end point.' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number', example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of items per page', example: 10 })
  getAllSoftDeletedcontacts(@Query('page') page: number = 1, @Query('limit') limit: number = 10) {
    return this.contactService.getOnlySoftDeletedContacts(page, limit);
  }

  @Get(':contactId')
  @ApiOperation({ summary: 'Retrieve an contact by Id', description: 'This endpoint returns an contact by its Id. This endpoint accessible by everyone.' })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard) // 
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager, Role.Vendor)
  @Roles()
  @ApiResponse({ status: 200, description: 'Contact is retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized ' })
  // @ApiResponse({ status: 403, description: 'Forbidden' })
  @ApiResponse({ status: 404, description: 'Contacts not found.' })
  findOne(@Param('contactId') contactId: string) {
    const objId = validateObjectId(contactId);
    return this.contactService.findOne(objId);
  }

  @Get('count')
  @ApiResponse({ status: 200, description: `Contact's count is retrieved.` })
  @ApiResponse({ status: 404, description: `Unable to find contact's count.` })
  @ApiResponse({ status: 401, description: `Unauthorized. ` })
  // @ApiResponse({ status: 403, description: `Forbidden user with role "super-admin", "admin" and "sales-rep" can only use this end point.` })
  @ApiOperation({ summary: `Retrieve Contact's count.`, description: `This endpoint returns Contact's count. This endpoint accessible by everyone.` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager, Role.Vendor)
  @Roles()
  async getContactsCount(@Req() req: any, @Query() query: QueryContactCountDto) {
    const userId = req.user._id;
    return this.contactService.getContactCounts(query, userId);
  }


  @Get('internal-count')
  @ApiResponse({ status: 200, description: `Internal contact's count is retrieved.` })
  @ApiResponse({ status: 404, description: `Unable to find contact's count.` })
  @ApiResponse({ status: 401, description: `Unauthorized. ` })
  // @ApiResponse({ status: 403, description: `Forbidden user with role "super-admin", "admin" and "sales-rep" can only use this end point.` })
  @ApiOperation({ summary: `Retrieve Internal Contact's count.`, description: `This endpoint returns Internal contact's count. This endpoint accessible by everyone.` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager, Role.Vendor)
  @Roles()
  async getInternalContactCounts(@Req() req: any,) {
    const userId = req.user._id;
    return this.contactService.getInternalContactCounts(userId);
  }



  @Patch(':contactId')
  @ApiOperation({ summary: 'Update an contact by Id', description: `This endpoint updates an contact by Id. This is accessible only for "${Role.BUHead}"` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.OrgAdmin)
  @Roles()
  @ApiResponse({ status: 200, description: 'Contact is updated.' })
  @ApiResponse({ status: 401, description: 'Unauthorized ' })
  @ApiResponse({ status: 403, description: 'Forbidden user with role "BUHead" can only use this end point.' })
  @ApiResponse({ status: 404, description: 'Contact not found.' })
  @ApiParam({ name: 'contactId', description: 'ID of the contact' })
  async update(@Req() req: any, @Param('contactId') contactId: string, @Body() updatecontactDto: UpdateContactDto) {
    const objId = validateObjectId(contactId);
    return await this.contactService.update(objId, updatecontactDto, req.user);
  }

  @Post('search')
  @ApiOperation({ summary: 'Search for contact', description: `This endpoint allows you to search for contact. This is accessible only for "${Role.BUHead}"` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.BUHead)
  @Roles()
  @ApiResponse({ status: 201, description: 'Contact found.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Data ' })
  @ApiResponse({ status: 401, description: 'Unauthorized ' })
  @ApiResponse({ status: 403, description: 'Forbidden user with role "BUHead" can only use this end point.' })
  @ApiQuery({ name: 'name', required: true, type: String, description: 'Name of contact', example: "John doe" })
  search(@Query('name') name: string) {
    return this.contactService.searchContacts(name);
  }

  // @Get('filter')
  // @ApiBearerAuth()
  // @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.SuperAdmin, Role.SalesRep)
  // @ApiOperation({ summary: 'Filter contacts', description: 'This endpoint returns a contact filtered by its account, industry, location and designation.' })
  // @ApiResponse({ status: 200, description: 'Contact is retrieved.' })
  // @ApiResponse({ status: 404, description: 'Contact not found.' })
  // @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  // @ApiResponse({ status: 403, description: 'Forbidden, user with role admin and salesrep can only use this end point.' })
  // @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number', example: 1 })
  // @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of items per page', example: 10 })
  // filterContacts(@Query() query: QueryContactDto, @Query('page') page: number = 1, @Query('limit') limit: number = 10) {
  //   const { accountOrgId, industryId, location, designation } = query;//query.filter
  //   const queryOptions = { accountOrgId, industryId, location, designation };
  //   return this.contactService.filterContacts(queryOptions, page, limit);
  // }

  @Patch(':contactId/status')
  @ApiOperation({ summary: 'Update status of an contact by Id', description: `This endpoint updates status of an contact by Id. This is accessible only for "Admin".` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin,Role.BUHead)
  @Roles()
  @ApiResponse({ status: 200, description: 'Contact is updated.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role "Admin" can only use this end point.' })
  @ApiResponse({ status: 404, description: 'Contact not found.' })
  @ApiParam({ name: 'contactId', description: 'ID of the contact' })
  @ApiBody({ type: ChangeStatusDto, description: 'The contact details to change status' })
  changeStatus(@Req() req: any, @Param('contactId') contactId: string, @Body() changeStatusDto: ChangeStatusDto) {
    const objId = validateObjectId(contactId);
    const { status, comment } = changeStatusDto;
    return this.contactService.changeStatus(objId, status, comment, req.user);
  }

  @Patch(':contactId/assign-to')
  @ApiOperation({ summary: 'Move the ownership of an contact by Id', description: `This endpoint moves the ownership of an contact by Id. This is accessible only for "Admin". ` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin,Role.BUHead)
  @Roles()
  @ApiResponse({ status: 200, description: 'Contact is updated.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role "Admin" can only use this end point.' })
  @ApiResponse({ status: 404, description: 'Contact not found.' })
  @ApiParam({ name: 'contactId', description: 'ID of the contact' })
  @ApiBody({ type: MoveToDto, description: 'The contact details to move' })
  moveContact(@Req() req: any, @Param('contactId') contactId: string, @Body() moveToDto: MoveToDto) {
    const contactObjId = validateObjectId(contactId);
    const { moveTo, assignTo, comment } = moveToDto;
    // const newAssigneeId = validateObjectId(moveTo);
    if (!assignTo || assignTo.length === 0) {
      throw new BadRequestException('AssignTo is required.');
    }
    return this.contactService.assignTo(contactObjId, assignTo, comment, req.user);
  }


  @Post(':contactId/comments')
  @ApiOperation({ summary: 'Comment on the contactId.', description: 'This endpoint allows you to add comments on the contact. This is accessible only for "BUHead".' })
  @ApiResponse({ status: 201, description: 'Comment added to the contact.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Data.' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role "BUHead" can only use this end point.' })
  @ApiParam({ name: 'contactId', description: 'Id of the contactId.' })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.BUHead)
  @Roles()
  addComment(@Req() req: any, @Param('contactId') contactId: string, @Body() commentDto: CommentDto) {
    const objId = validateObjectId(contactId);
    commentDto.user = req.user._id;
    return this.contactService.addComment(objId, commentDto);
  }

  @Get(':contactId/comments')
  @ApiResponse({ status: 200, description: 'Comments retrieved successfully.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  // @ApiResponse({ status: 403, description: 'Forbidden, User with role "super-admin", "admin" and "sales-rep" can only use this end point.' })
  @ApiOperation({ summary: 'Get comments of the contactId.', description: 'This endpoint allows you to retrieve comments on the contactId. This is accessible only for everyone' })
  @ApiParam({ name: 'contactId', description: 'Id of the contactId.' })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager)
  @Roles()
  getComments(@Param('contactId') contactId: string) {
    const objId = validateObjectId(contactId);
    return this.contactService.getComments(objId);
  }


  //TODO: usecase 
  // @Patch(':contactId/favourite')
  // @ApiOperation({ summary: 'Mark a contact as a favorite using its ID.', description: `This endpoint soft deletes an contact by Id. This is accessible only for "${Role.BUHead}".` })
  // @ApiBearerAuth()
  // @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.BUHead)
  // @ApiResponse({ status: 200, description: 'Contact marked as favourite.' })
  // @ApiResponse({ status: 401, description: 'Unauthorized ' })
  // @ApiResponse({ status: 403, description: 'Forbidden user with role "BUHead" can only use this end point.' })
  // @ApiResponse({ status: 404, description: 'contact not found.' })
  // @ApiQuery({ name: 'contactId', description: 'ID of the contact' })
  // favourite(@Param('contactId') contactId: string) {
  //   const objId = validateObjectId(contactId);
  //   return this.contactService.favourite(objId);
  // }

  @Patch(':contactId/restore')
  @ApiOperation({ summary: 'Restore soft deleted contact by ID.', description: `This endpoint restores a soft-deleted contact by ID. This is accessible for "${Role.BUHead}".` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.BUHead)
  @Roles()
  @ApiResponse({ status: 200, description: 'Contact updated.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role "BUHead" can only use this end point.' })
  @ApiParam({ name: 'contactId', description: 'ID of the Contact.' })
  async restoreSoftDeletedContact(@Param('contactId') contactId: string) {
    const objId = validateObjectId(contactId);
    return await this.contactService.restoreSoftDeletedContact(objId);
  }

  @Patch('restore-all')
  @ApiOperation({ summary: 'Restore all soft deleted contacts', description: `This endpoint Restore soft deleted contacts. This is accessible only for "${Role.BUHead}".` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.BUHead)
  @Roles()
  @ApiResponse({ status: 201, description: 'All contacts are restored.' })
  @ApiResponse({ status: 401, description: 'Unauthorized ' })
  @ApiResponse({ status: 403, description: 'Forbidden user with role "BUHead" can only use this end point.' })
  @ApiResponse({ status: 404, description: 'contact not found.' })
  restore() {
    return this.contactService.restore();
  }

  @Delete(':contactId/hard-delete')
  @ApiOperation({ summary: 'Hard delete an contact by Id', description: `This endpoint deletes an contact by Id. This is accessible only for "${Role.BUHead}".` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.BUHead)
  @Roles()
  @ApiResponse({ status: 200, description: 'contact is hard deleted.' })
  @ApiResponse({ status: 401, description: 'Unauthorized ' })
  @ApiResponse({ status: 403, description: 'Forbidden user with role "BUHead" can only use this end point.' })
  @ApiResponse({ status: 404, description: 'contact not found.' })
  @ApiParam({ name: 'contactId', description: 'ID of the contact' })
  remove(@Param('contactId') contactId: string) {
    const objId = validateObjectId(contactId);
    return this.contactService.hardDelete(objId);
  }

  @Delete(':contactId/soft-delete')
  @ApiOperation({ summary: 'Soft delete an contact by Id', description: `This endpoint soft deletes an contact by Id. This is accessible only for "${Role.Admin}".` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin,Role.BUHead)
  @Roles()
  @ApiResponse({ status: 200, description: 'contact is soft deleted.' })
  @ApiResponse({ status: 401, description: 'Unauthorized ' })
  @ApiResponse({ status: 403, description: 'Forbidden user with role "Admin" can only use this end point.' })
  @ApiResponse({ status: 404, description: 'contact not found.' })
  @ApiParam({ name: 'contactId', description: 'ID of the contact' })
  delete(@Param('contactId') contactId: string) {
    const objId = validateObjectId(contactId);
    return this.contactService.softDelete(objId);
  }

  @Delete('delete-all')
  @ApiOperation({ summary: 'Hard delete all contacts', description: `This endpoint hard deletes all contacts. This is accessible only for "${Role.BUHead}".` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.BUHead)
  @Roles()
  @ApiResponse({ status: 200, description: 'All contacts are deleted.' })
  @ApiResponse({ status: 401, description: 'Unauthorized ' })
  @ApiResponse({ status: 403, description: 'Forbidden user with role "BUHead" can only use this end point.' })
  @ApiResponse({ status: 404, description: 'contacts not found.' })
  deleteAll() {
    return this.contactService.hardDeleteAllContacts();
  }

  @Patch('bulk-status')
  @ApiOperation({
    summary: 'Bulk update status of multiple orgs by their IDs',
    description: `This endpoint updates the status of multiple orgs by their IDs. Accessible only to users with roles "${Role.Admin}".`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin,Role.BUHead)
  @Roles()
  @ApiResponse({ status: 200, description: 'Org statuses updated.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 403, description: `Forbidden user with role "Admin" can only use this end point.` })
  @ApiResponse({ status: 404, description: 'Orgs not found.' })
  @ApiBody({ type: ChangeStatusContactDto, description: 'The org IDs and new status' })
  @Patch('bulk-status')
  async bulkChangeStatus(@Req() req: any, @Body() bulkChangeStatusDto: ChangeStatusContactDto) {
    const { contactIds } = bulkChangeStatusDto;
    if (contactIds && contactIds.length > 0) {
      const validOrgIds = contactIds.map(contactIds => validateObjectId(contactIds));
      return this.contactService.bulkChangeStatus(validOrgIds, bulkChangeStatusDto, req.user);
    } else {
      // Handle the case when orgIds is not provided or empty
      throw new BadRequestException('orgIds are required for bulk status update.');
    }
  }

  @Delete('bulk-delete')
  @ApiOperation({
    summary: 'Soft delete multiple orgs',
    description: `This endpoint soft deletes multiple orgs by setting the 'isDeleted' flag to true. Accessible only for "${Role.Admin}".`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin,Role.BUHead)
  @Roles()
  @ApiResponse({ status: 200, description: 'Orgs have been soft deleted.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: `Forbidden user with role "Admin" can only use this end point.` })
  @ApiResponse({ status: 404, description: 'No orgs found for the given IDs.' })
  async bulkSoftDelete(@Body() orgIds: Types.ObjectId[]): Promise<{ message: string }> {
    const result = await this.contactService.bulkDelete(orgIds); // Calling the service method to perform soft delete

    if (result.modifiedCount > 0) {
      return { message: `Successfully soft deleted ${result.modifiedCount} organization(s).` };
    } else {
      return { message: 'No organizations were soft deleted.' };
    }
  }

  @Patch('favourites')
  @ApiOperation({ summary: 'Add contacts to user favourites' })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead)
  @Roles()
  @ApiBody({ type: AddToFavouritesDto, description: 'Array of contact IDs to add to favourites' })
  @ApiResponse({ status: 200, description: 'Contacts were successfully added to favourites.' })
  @ApiResponse({ status: 400, description: 'Invalid request data or contact IDs.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 403, description: `Forbidden. Only users with the "${Role.BUHead}" and  "${Role.Admin}"  role are allowed.` })
  @ApiResponse({ status: 404, description: 'One or more contacts not found.' })
  async addToFavourites(
    @Req() req: any,
    @Body() body: AddToFavouritesDto,
  ) {
    const userId = req.user._id;
    const ids = body.contactIds.map(contactId => validateObjectId(contactId));
    return this.contactService.addToFavourites(userId, ids);
  }

  @Get('favourites')
  @ApiOperation({ summary: 'Retrieve all favourite contacts' })
  // @ApiQuery({ name: 'page', required: false, type: Number, example: 1 })
  // @ApiQuery({ name: 'limit', required: false, type: Number, example: 10 })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead)
  @Roles()
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 403, description: `Forbidden. Only users with the "${Role.BUHead}" and  "${Role.Admin}"  role are allowed.` })
  getFavourites(@Req() req: any) {
    const userId = req.user._id;
    return this.contactService.getFavouriteContacts(userId);
  }

  @Delete('favourites')
  @ApiOperation({ summary: 'Remove contacts from user favourites' })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead)
  @Roles()
  @ApiBody({ type: AddToFavouritesDto, description: 'Array of contact IDs to remove from favourites' })
  @ApiResponse({ status: 200, description: 'Contacts were successfully removed from favourites.' })
  @ApiResponse({ status: 400, description: 'Invalid request data or contact IDs.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 403, description: `Forbidden. Only users with the "${Role.BUHead}" and  "${Role.Admin}"  role are allowed.` })
  @ApiResponse({ status: 404, description: 'One or more contacts not found in favourites.' })
  async removeFromFavourites(
    @Req() req: any,
    @Body() body: AddToFavouritesDto,
  ) {
    const userId = req.user._id;
    const ids = body.contactIds.map(contactId => validateObjectId(contactId)); // validate the contactIds
    return this.contactService.removeFromFavourites(userId, ids);
  }

  // @Patch('bulkupdateAll')
  // async bulkUpdatecretedByOrgs() {
  //   try {
  //     return await this.contactService.updateCreatedByOrgFromUserModel();
  //   } catch (error) {
  //     throw error;
  //   }
  // }

}
