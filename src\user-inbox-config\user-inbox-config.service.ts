import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { UserInboxConfig, UserInboxConfigDocument } from './schemas/user-inbox-config.schema';
import Imap from 'node-imap';
import { CreateUserInboxConfigDto } from './dto/create-user-inbox-config.dto';
import { promisify } from 'util';
import * as mailparser from 'mailparser';
import * as nodemailer from 'nodemailer';
import * as ImapSimple from 'imap-simple';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { FileMetadata } from 'src/file-upload/schemas/file-metadata.schema';
import { ReplyEmailDto } from './dto/reply-email.dto';
import { ForwardEmailDto } from './dto/forward-email.dto';
import { MoveEmailsDto } from './dto/move-email.dto';
import { UpdateUserInboxConfigDto } from './dto/update-user-inbox-config.dto';

interface Mailbox {
  [key: string]: {
    // Define any properties of the mailbox if known
    children?: Mailbox; // If there are nested folders, define their structure
    // Add any other relevant properties here
  };
}

interface ConnectionTestResult {
  imapConnection: {
    success: boolean;
    error?: string;
  };
  smtpConnection: {
    success: boolean;
    error?: string;
  };
}

@Injectable()
export class UserInboxConfigService {
  private readonly logger = new Logger(UserInboxConfigService.name);

  constructor(
    @InjectModel(UserInboxConfig.name) private userInboxConfigModel: Model<UserInboxConfigDocument>,
    @InjectModel(FileMetadata.name) private fileMetadataModel: Model<FileMetadata>,
    private eventEmitter: EventEmitter2
  ) { }


  private async getOrgDefaultConfig(orgId: Types.ObjectId): Promise<UserInboxConfig | null> {
    return this.userInboxConfigModel.findOne({
      org: new Types.ObjectId(orgId),
        isOrgDefault: true
    }).exec();
}

//   async create(userInboxConfigDto: CreateUserInboxConfigDto, user: any,): Promise<UserInboxConfigDocument> {
   
//     let finalConfig = { ...userInboxConfigDto };

//     console.log("user data i am getting ", user)

//     console.log("final config details", finalConfig)

//     const isAdmin = user.roles.includes('admin');

//     console.log("isAdmin", isAdmin)
//     if (user.org) {
//       // Check if this is the first config for the org
//       const orgDefaultConfig = await this.getOrgDefaultConfig(user.org._id);

    

//       if (!orgDefaultConfig && !isAdmin) {
//         throw new NotFoundException('Organization email settings not configured. Please contact your organization admin.');
//     }

//     if (orgDefaultConfig) {
//         if (isAdmin) {
//             // If user is org admin and this is the first config, set as org default
//             finalConfig = {
//                 ...finalConfig,
//                 org: user.org._id,
//                 isOrgDefault: true
//             } as any;
//         } else {
//             // Use org default settings for all subsequent configs
//             finalConfig = {
//                 fromEmail: finalConfig.fromEmail,
//                 userName: finalConfig.userName,
//                 fromName: finalConfig.fromName,
//                 password: finalConfig.password,
//                 org: user.org._id,
//                 imapHost: orgDefaultConfig.imapHost,
//                 imapPort: orgDefaultConfig.imapPort,
//                 smtpHost: orgDefaultConfig.smtpHost,
//                 smtpPort: orgDefaultConfig.smtpPort,
//                 isEnableSSL: orgDefaultConfig.isEnableSSL,
//                 isOrgDefault: false
//             } as any;
//         }
//     }
// }

//   finalConfig.createdBy = user._id; // Set createdBy field if applicable
//     const createUserInboxConfig = new this.userInboxConfigModel(finalConfig);
//     const createdUserInboxConfig = await createUserInboxConfig.save();
//     this.emitEvent('user-inbox-config.created', { createdUserInboxConfig, user });
//     return createdUserInboxConfig;
//   }

// async create(userInboxConfigDto: CreateUserInboxConfigDto, user: any): Promise<UserInboxConfigDocument> {
//   const isAdmin = user.roles.includes("admin");


// console.log("user", user)

//   let defaultConfig = await this.userInboxConfigModel.findOne({
//     org: user.org._id,
//     isOrgDefault: true,
//   }).exec();

//   if (!defaultConfig && isAdmin) {
//     const adminConfigData = {
//       org: userInboxConfigDto.org,
//       fromEmail: userInboxConfigDto.fromEmail,
//       fromName: userInboxConfigDto.fromName,
//       imapHost: userInboxConfigDto.imapHost,
//       imapPort: userInboxConfigDto.imapPort,
//       smtpHost: userInboxConfigDto.smtpHost,
//       smtpPort: userInboxConfigDto.smtpPort,
//       userName: userInboxConfigDto.userName,
//       password: userInboxConfigDto.password,
//       isEnableSSL: false,
//       isOrgDefault: true,
//       createdBy: user._id.toString()// Explicitly set createdBy here
//     };

//     console.log("create userInbox config ", adminConfigData);
//     const newAdminConfig = new this.userInboxConfigModel(
//       {...adminConfigData,
//         createdBy: user._id.toString()
//       });
//     console.log("new Admin org ", newAdminConfig.toObject());

//     const createdAdminConfig = await newAdminConfig.save();
//     console.log("create admin onfig", createdAdminConfig)
//     this.emitEvent('user-inbox-config.created', { createdAdminConfig, user });

//     return createdAdminConfig;
//   }



//   if (!defaultConfig) {
//     throw new Error('Default configuration not found for the given org');
//   }

//   const {
//     _id, __v,  ...defaultSettings
//   } = defaultConfig.toObject();

//   // Merge recruiter's input with the default configuration
//   const mergedConfig = {
//     ...defaultSettings, // Use only necessary fields from the default config
//     ...userInboxConfigDto, // Spread recruiter's input
//     createdBy: user._id, // Set createdBy field to the recruiter's ID
//     isOrgDefault: false, // Ensure this is not marked as default
//   };

//   console.log("merged values", mergedConfig)


//   // Create and save the new user inbox configuration
//   const createUserInboxConfig = new this.userInboxConfigModel(mergedConfig);
//   const createdUserInboxConfig = await createUserInboxConfig.save();

//   // Emit an event if needed
//   this.emitEvent('user-inbox-config.created', { createdUserInboxConfig, user });

//   return createdUserInboxConfig;
// }



async create(userInboxConfigDto: CreateUserInboxConfigDto, user: any): Promise<UserInboxConfigDocument> {

  userInboxConfigDto.createdBy = user._id; // Set createdBy field if applicable
  const createUserInboxConfig = new this.userInboxConfigModel(userInboxConfigDto);
  const createdUserInboxConfig = await createUserInboxConfig.save();
  this.emitEvent('user-inbox-config.created', { createdUserInboxConfig, user });
  return createdUserInboxConfig;
}


  async findAll(): Promise<UserInboxConfigDocument[]> {
    return this.userInboxConfigModel.find().exec();
  }

  async findById(id: Types.ObjectId): Promise<UserInboxConfig> {
    const inbox = await this.userInboxConfigModel.findById(id).exec();
    if (!inbox) {
      return {} as UserInboxConfig; // Returning a default empty object (but not recommended unless suitable)
    }
    return inbox;
  }
  

  async update(
    id: Types.ObjectId,
    updateUserInboxConfigDto: UpdateUserInboxConfigDto,
    user: any
  ): Promise<UserInboxConfigDocument> {
    const updatedInboxConfig = await this.userInboxConfigModel.findByIdAndUpdate(
      id,
      {
        ...updateUserInboxConfigDto,
        updatedBy: user._id, // Optionally track who made the update
        updatedAt: new Date(), // Track the update time
      },
      { new: true, runValidators: true } // Return the updated document and run schema validations
    ).exec();

    if (!updatedInboxConfig) {
      throw new Error(`User Inbox Config with ID ${id} not found`);
    }

    // Emit an event after updating
    this.emitEvent('user-inbox-config.updated', { updatedInboxConfig, user });

    return updatedInboxConfig;
  }



  async createFolder(userInboxConfig: UserInboxConfig, folderName: string): Promise<void> {
    const imap = await this.connectToImap(userInboxConfig);

    return new Promise((resolve, reject) => {
      // Create the new folder
      imap.addBox(folderName, (err) => {
        if (err) {
          console.error(`Error creating folder ${folderName}: ${err.message}`);
          imap.end();
          return reject(err);
        }

        console.log(`Successfully created folder: ${folderName}`);
        imap.getBoxes(() => {
          if (err) {
            console.error('Error fetching folder list:');
            imap.end();
            return reject(err);
          }

          console.log('Folder list after creation:');
          imap.end();
          resolve();

        })

      });
    });
  }

  async deleteFolder(userInboxConfig: UserInboxConfig, folderName: string): Promise<void> {
    const imap = await this.connectToImap(userInboxConfig);
  
    return new Promise((resolve, reject) => {
      // Delete the folder
      imap.delBox(folderName, (err) => {
        if (err) {
          console.error(`Error deleting folder ${folderName}: ${err.message}`);
          imap.end();
          return reject(err);
        }
  
        console.log(`Successfully deleted folder: ${folderName}`);
        imap.end();
        resolve();
      });
    });
  }
  


  async connectToImap(userInboxConfig: UserInboxConfig): Promise<Imap> {
    const imap = new Imap({
      user: userInboxConfig.userName,
      password: userInboxConfig.password,
      host: userInboxConfig.imapHost,
      port: userInboxConfig.imapPort,
      tls: userInboxConfig.isEnableSSL,
    });

    return new Promise((resolve, reject) => {
      imap.once('ready', () => resolve(imap));
      imap.once('error', reject);
      imap.connect(); // This establishes the connection
    });
  }

  private async testSmtpConnection(userInboxConfig: UserInboxConfig): Promise<boolean> {
    const transporter = nodemailer.createTransport({
      host: userInboxConfig.smtpHost,
      port: userInboxConfig.smtpPort,
      secure: userInboxConfig.isEnableSSL,
      auth: {
        user: userInboxConfig.userName,
        pass: userInboxConfig.password,
      },
    });

    try {
      await transporter.verify();
      return true;
    } catch (error) {
      this.logger.error(`SMTP connection failed: ${error.message}`);
      return false;
    }
  }

  async testEmailConnections(id: Types.ObjectId): Promise<ConnectionTestResult> {
    const userInboxConfig = await this.findById(id);
    if (!userInboxConfig || !Object.keys(userInboxConfig).length) {
      throw new Error(`User Inbox Config with ID ${id} not found`);
    }

    const result: ConnectionTestResult = {
      imapConnection: {
        success: false
      },
      smtpConnection: {
        success: false
      }
    };

    // Test IMAP connection
    try {
      const imap = await this.connectToImap(userInboxConfig);
      result.imapConnection.success = true;
      // Properly close the connection
      imap.end();
    } catch (error) {
      result.imapConnection.success = false;
      result.imapConnection.error = error.message;
      this.logger.error(`IMAP connection failed: ${error.message}`);
    }

    // Test SMTP connection
    try {
      result.smtpConnection.success = await this.testSmtpConnection(userInboxConfig);
      if (!result.smtpConnection.success) {
        result.smtpConnection.error = 'SMTP verification failed';
      }
    } catch (error) {
      result.smtpConnection.success = false;
      result.smtpConnection.error = error.message;
      this.logger.error(`SMTP connection failed: ${error.message}`);
    }


    return result;
  }

  

  async fetchEmailsByFolders(userInboxConfig: UserInboxConfig): Promise<any> {
    const result: { [key: string]: { emails: any[], attribs: string[] } } = {};

    // Fetch mailbox folders dynamically
    const folderNames = await this.getMailboxFolders(userInboxConfig);

    // Function to fetch emails from a specific folder
    const fetchEmailsFromFolder = async (folder: string): Promise<void> => {
      const imap = await this.connectToImap(userInboxConfig); // Create new connection per folder
      result[folder] = {
        emails: [],
        attribs: [] // Add default attributes as needed
      };

      console.log(`Fetching emails from folder: ${folder}`);

      return new Promise((resolve, reject) => {
        imap.openBox(folder, true, (err) => {
          if (err) {
            console.error(`Error opening folder ${folder}: ${err.message}`);
            imap.end();
            return reject(err);
          }
          console.log(`Successfully opened folder: ${folder}`);

          imap.search(['ALL'], (err, results) => {
            if (err) {
              console.error(`Error searching for emails in folder ${folder}: ${err.message}`);
              imap.end();
              return reject(err);
            }

            console.log(`Found ${results.length} emails in folder ${folder}`);
            if (results.length === 0) {
              imap.end();
              resolve();
              return;
            }

            const f = imap.fetch(results, {
              bodies: '', // Fetch full body including headers and all MIME parts
              markSeen: false,
            });

            f.on('message', (msg) => {
              const email: { headers: any | null, body: string | null, uid: string | null, flags: string[] } = {
                headers: null,
                body: null,
                uid: null,
                flags: []
              };

              let buffer = '';

              msg.on('body', (stream) => {
                stream.on('data', (chunk) => {
                  buffer += chunk.toString('utf8');
                });

                stream.once('end', async () => {
                  try {
                    // Step 1: Parse only the body part using simpleParser
                    const parsed = await mailparser.simpleParser(buffer);

                    // Step 2: Extract and structure headers manually from the raw header string
                    const rawHeaders = buffer.split('\r\n\r\n')[0]; // Split headers from the body
                    const headersArray = rawHeaders.split('\r\n'); // Split headers into an array of lines

                    // Initialize an object to store formatted headers
                    const formattedHeaders: any = {};

                    headersArray.forEach(headerLine => {
                      const [key, value] = headerLine.split(': ');
                      if (key && value) {
                        let decodedValue = value;
                        // Decode UTF-8 encoded subject
                        if (key === 'Subject') {
                          const match = value.match(/=\?UTF-8\?B\?(.*)\?=/);
                          if (match) {
                            decodedValue = Buffer.from(match[1], 'base64').toString('utf8');
                          } else {
                            const match2 = value.match(/=\?UTF-8\?Q\?(.*)\?=/);
                            if (match2) {
                              decodedValue = decodeURIComponent(match2[1].replace(/_/g, ' '));
                            }
                          }
                        }

                        formattedHeaders[key] = decodedValue;
                      }
                    });

                    // Format headers for display (e.g., From, To, Subject, Date)
                    email.headers = {
                      From: formattedHeaders['From'] || 'Unknown sender',
                      To: formattedHeaders['To'] || 'Unknown recipient',
                      Subject: formattedHeaders['Subject'] || 'No subject',
                      Date: formattedHeaders['Date'] || 'Unknown date'
                    };


                    // Step 3: Store the parsed body
                    email.body = parsed.html || parsed.text || null; // Fallback to null if both are undefined
                    // Use HTML if available, else fallback to plain text

                    result[folder].emails.push(email); // Store the email in the result object
                  } catch (err) {
                    console.error(`Failed to parse email body: ${err.message}`);
                  }
                });
              });

              msg.once('attributes', (attrs) => {
                email.uid = attrs.uid.toString();
                email.flags = attrs.flags;
              });

              msg.once('end', () => {
                console.log(`Finished fetching email from folder ${folder}`);
              });
            });

            f.once('error', (err) => {
              console.error(`Fetch error in folder ${folder}: ${err.message}`);
              imap.end();
              reject(err);
            });

            f.once('end', () => {
              console.log(`Finished fetching emails from folder ${folder}`);
              result[folder].attribs = ['\\HasNoChildren', '\\UnMarked']; // Customize attributes
              imap.end();
              resolve();
            });
          });
        });
      });
    };

    // Fetch emails from all folders
    const fetchPromises = folderNames.map(folder => fetchEmailsFromFolder(folder));
    await Promise.all(fetchPromises);

    for (const folder in result) {
      result[folder].emails.sort((a, b) => {
        const dateA = new Date(a.headers.Date).getTime();
        const dateB = new Date(b.headers.Date).getTime();
        return dateB - dateA; // Newest emails first
      });
    }

    return result; // Return the structured result
  }

  async fetchEmailsFromFolder(userInboxConfig: UserInboxConfig, folder: string): Promise<any> {

    const result: { emails: any[], attribs: string[] } = {
      emails: [],
      attribs: [] // Add default attributes as needed
    };

    const imap = await this.connectToImap(userInboxConfig);

    console.log(`Fetching emails from folder: ${folder}`);

    return new Promise((resolve, reject) => {
      imap.openBox(folder, true, (err) => {
        if (err) {
          console.error(`Error opening folder ${folder}: ${err.message}`);
          imap.end();
          return reject(err);
        }
        console.log(`Successfully opened folder: ${folder}`);

        imap.search(['ALL'], (err, results) => {
          if (err) {
            console.error(`Error searching for emails in folder ${folder}: ${err.message}`);
            imap.end();
            return reject(err);
          }

          console.log(`Found ${results.length} emails in folder ${folder}`);
          if (results.length === 0) {
            imap.end();
            resolve(result); // Return empty result
            return;
          }

          const emailPromises: Promise<void>[] = []; // Array to hold promises for each email

          const f = imap.fetch(results, {
            bodies: '', // Fetch full body including headers and all MIME parts
            markSeen: false,
          });

          f.on('message', (msg) => {
            let buffer = '';
            const email: { headers: any | null, body: string | null, uid: string | null, flags: string[], attachments: any[] } = {
              headers: null,
              body: null,
              uid: null,
              flags: [],
              attachments: []

            };

            // Create a promise for each message
            const emailPromise = new Promise<void>((resolveEmail) => {
              msg.on('body', (stream) => {
                stream.on('data', (chunk) => {
                  buffer += chunk.toString('utf8');
                });

                stream.once('end', async () => {
                  try {
                    // Step 1: Parse only the body part using simpleParser
                    const parsed = await mailparser.simpleParser(buffer);

                    // Step 2: Extract and structure headers manually from the raw header string
                    const rawHeaders = buffer.split('\r\n\r\n')[0]; // Split headers from the body
                    const headersArray = rawHeaders.split('\r\n'); // Split headers into an array of lines

                    // Initialize an object to store formatted headers
                    const formattedHeaders: any = {};

                    headersArray.forEach(headerLine => {
                      const [key, value] = headerLine.split(': ');
                      if (key && value) {
                        let decodedValue = value;
                        // Decode UTF-8 encoded subject
                        if (key === 'Subject') {
                          const match = value.match(/=\?UTF-8\?B\?(.*)\?=/);
                          if (match) {
                            decodedValue = Buffer.from(match[1], 'base64').toString('utf8');
                          } else {
                            const match2 = value.match(/=\?UTF-8\?Q\?(.*)\?=/);
                            if (match2) {
                              decodedValue = decodeURIComponent(match2[1].replace(/_/g, ' '));
                            }
                          }
                        }

                        // Split the 'From' field into name and email
                        if (key === 'From') {
                          const fromMatch = value.match(/(.*)<(.*)>/);
                          if (fromMatch) {
                            formattedHeaders['FromName'] = fromMatch[1].trim(); // Extract the name
                            formattedHeaders['FromEmail'] = fromMatch[2].trim(); // Extract the email
                          } else {
                            // If no name provided, the value is the email
                            formattedHeaders['FromName'] = null; // No name available
                            formattedHeaders['FromEmail'] = value.trim(); // Use value as the email
                          }
                        }

                        // Split the 'To' field into an array of recipients (name-email pairs)
                        if (key === 'To') {
                          const toList = value.split(',').map(recipient => {
                            const toMatch = recipient.trim().match(/(.*)<(.*)>/);
                            if (toMatch) {
                              return {
                                name: toMatch[1].trim(),
                                email: toMatch[2].trim(),
                              };
                            } else {
                              // If no name, the recipient is just an email
                              return {
                                name: null, // No name available
                                email: recipient.trim(), // Use value as the email
                              };
                            }
                          });
                          formattedHeaders['To'] = toList; // Store array of recipients
                        } else {
                          formattedHeaders[key] = decodedValue;
                        }
                      }
                    });


                    // Format headers for display (e.g., From, To, Subject, Date)
                    email.headers = {
                      FromName: formattedHeaders['FromName'] || 'Unknown sender',
                      FromEmail: formattedHeaders['FromEmail'] || 'Unknown email',
                      To: formattedHeaders['To'] || [{ name: 'Unknown recipient', email: null }],
                      Subject: formattedHeaders['Subject'] || 'No subject',
                      Date: formattedHeaders['Date'] || 'Unknown date'
                    };

                    // Step 3: Store the parsed body
                    email.body = parsed.html || parsed.text || null; // Fallback to null if both are undefined
                    // Use HTML if available, else fallback to plain text

                    // Step 3: Extract attachments if present
                    if (parsed.attachments) {
                      parsed.attachments.forEach(attachment => {
                        email.attachments.push({
                          filename: attachment.filename,
                          contentType: attachment.contentType,
                          contentDisposition: attachment.contentDisposition,
                          size: attachment.size,
                          contentId: attachment.cid,
                          content: attachment.content.toString('base64'), // Store content as base64 for later retrieval
                        });
                      });
                    }

                    result.emails.push(email); // Store the email with attachments
                    resolveEmail();
                  } catch (err) {
                    console.error(`Failed to parse email body: ${err.message}`);
                    resolveEmail(); // Resolve even if there's an error
                  }
                });
              });

              msg.once('attributes', (attrs) => {
                email.uid = attrs.uid.toString();
                email.flags = attrs.flags;
              });
            });

            emailPromises.push(emailPromise); // Add the promise to the array
          });

          f.once('error', (err) => {
            console.error(`Fetch error in folder ${folder}: ${err.message}`);
            imap.end();
            reject(err);
          });

          f.once('end', async () => {
            console.log(`Finished fetching emails from folder ${folder}`);
            result.attribs = ['\\HasNoChildren', '\\UnMarked']; // Customize attributes
            imap.end();
            await Promise.all(emailPromises); // Wait for all emails to be processed

            // Sort emails manually by date (most recent first)
            result.emails.sort((a, b) => {
              const dateA = new Date(a.headers.Date || 0);
              const dateB = new Date(b.headers.Date || 0);
              return dateB.getTime() - dateA.getTime(); // Sort by descending date
            });
            resolve(result); // Now resolve the final result
          });
        });
      });
    });
  }

  async fetchSingleEmail(userInboxConfig: UserInboxConfig, uid: string, folder: string,): Promise<any> {
    const imap = await this.connectToImap(userInboxConfig);

    return new Promise((resolve, reject) => {
      // Open the inbox folder
      imap.openBox(folder, true, (err) => {
        if (err) {
          imap.end();
          return reject(`Error opening inbox: ${err.message}`);
        }

        // Fetch the email by UID (emailId)
        imap.fetch(uid, {
          bodies: '',
          markSeen: false,
        }).on('message', (msg) => {
          const email: { headers: any | null, body: string | null, uid: string | null, flags: string[], attachments: any[] } = {
            headers: null,
            body: null,
            uid: null,
            flags: [],
            attachments: [],
          };

          let buffer = '';

          msg.on('body', (stream) => {
            stream.on('data', (chunk) => {
              buffer += chunk.toString('utf8');
            });

            stream.once('end', async () => {
              try {
                // Step 1: Parse the email using simpleParser
                const parsed = await mailparser.simpleParser(buffer);

                // Step 2: Extract and format headers manually
                const rawHeaders = buffer.split('\r\n\r\n')[0]; // Split headers from the body
                const headersArray = rawHeaders.split('\r\n');

                const formattedHeaders: any = {};

                headersArray.forEach(headerLine => {
                  const [key, value] = headerLine.split(': ');
                  if (key && value) {
                    let decodedValue = value;
                    // Decode UTF-8 encoded subject if necessary
                    if (key === 'Subject') {
                      const match = value.match(/=\?UTF-8\?B\?(.*)\?=/);
                      if (match) {
                        decodedValue = Buffer.from(match[1], 'base64').toString('utf8');
                      }
                    }
                    formattedHeaders[key] = decodedValue;
                  }
                });

                email.headers = {
                  From: formattedHeaders['From'] || 'Unknown sender',
                  To: formattedHeaders['To'] || 'Unknown recipient',
                  Subject: formattedHeaders['Subject'] || 'No subject',
                  Date: formattedHeaders['Date'] || 'Unknown date',
                };

                // Step 3: Store the parsed body
                email.body = parsed.html || parsed.text || null;

                // Step 4: Handle attachments
                if (parsed.attachments && parsed.attachments.length > 0) {
                  email.attachments = parsed.attachments.map(att => ({
                    filename: att.filename,
                    contentType: att.contentType,
                    contentDisposition: att.contentDisposition,
                    content: att.content.toString('base64'), // Convert attachment to base64
                    size: att.size,
                    contentId: att.cid,
                  }));
                }

                // Assign UID
                email.uid = uid;

                resolve(email); // Return the formatted email
              } catch (err) {
                reject(`Failed to parse email body: ${err.message}`);
              }
            });
          });

          msg.once('attributes', (attrs) => {
            email.uid = attrs.uid.toString();
            email.flags = attrs.flags;
          });

          msg.once('end', () => {
            console.log('Finished fetching single email');
          });
        });

        imap.once('error', (err) => {
          imap.end();
          reject(`IMAP fetch error: ${err.message}`);
        });

        imap.once('end', () => {
          console.log('Connection closed');
          imap.end();
        });
      });
    });
  }

  async getMailboxFolders(userInboxConfig: UserInboxConfig): Promise<string[]> {
    const imap = await this.connectToImap(userInboxConfig);
    const getBoxes = promisify(imap.getBoxes).bind(imap);

    try {
      // Use type assertion to specify the expected type of boxes
      const boxes = (await getBoxes()) as Mailbox;
      imap.end(); // Close the connection after retrieving folders

      // Get the list of folders
      const folderNames = Object.keys(boxes);

      // Define the order of folders that you want to prioritize
      const prioritizedFolders = ['INBOX', 'Sent', 'Trash', 'Spam'];

      // Create a sorted array based on prioritized folders first, followed by any others
      const sortedFolders = [
        ...prioritizedFolders.filter(folder => folderNames.includes(folder)), // Add prioritized folders that exist
        ...folderNames.filter(folder => !prioritizedFolders.includes(folder))
          .reverse(), // Add any other folders
      ];

      return sortedFolders;
    } catch (err) {
      imap.end();
      throw new Error(`Failed to fetch mailboxes: ${err.message}`);
    }
  }

  async removeMailboxFolder(userInboxConfig: UserInboxConfig, folderName: string): Promise<void> {
    const imap = await this.connectToImap(userInboxConfig); // Reuse the connectToImap method to establish the IMAP connection

    return new Promise((resolve, reject) => {
      imap.once('ready', () => {
        imap.delBox(folderName, (err) => {
          if (err) {
            console.error(`Error deleting folder ${folderName}: ${err.message}`);
            imap.end();
            return reject(`Error deleting folder ${folderName}: ${err.message}`);
          }

          console.log(`Successfully deleted folder: ${folderName}`);
          imap.end();
          resolve();
        });
      });

      imap.once('error', (err) => {
        console.error(`IMAP connection error while deleting folder ${folderName}: ${err.message}`);
        reject(err);
      });

      imap.connect(); // Establish the connection
    });
  }

  async moveEmailsToFolder(
    moveEmailDto: MoveEmailsDto
  ) {

    const { userInboxConfigId, sourceFolder, destinationFolder, emailUIDs } = moveEmailDto;


    if (!userInboxConfigId) {
      throw new Error('userInboxConfigId is required');  // Handle missing field
    }


    const userInboxConfig = await this.findById(new Types.ObjectId(userInboxConfigId));

    if (!userInboxConfig) {
      throw new Error('userInboxConfigId is required');  // Handle missing field
    }

    const imap = await this.connectToImap(userInboxConfig);

    return new Promise<void>((resolve, reject) => {
      // Open the source folder
      imap.openBox(sourceFolder, false, (err) => {
        if (err) {
          console.error(`Error opening source folder "${sourceFolder}": ${err.message}`);
          imap.end();
          return reject(err);
        }

        console.log(`Successfully opened source folder: ${sourceFolder}`);

        // Move emails to the destination folder
        imap.move(emailUIDs, destinationFolder, (moveErr) => {
          if (moveErr) {
            console.error(`Error moving emails to folder "${destinationFolder}": ${moveErr.message}`);
            imap.end();
            return reject(moveErr);
          }

          console.log(`Successfully moved emails to folder: ${destinationFolder}`);
          imap.end();
          resolve();
        });
      });

      imap.once('error', (imapErr) => {
        console.error(`IMAP error: ${imapErr.message}`);
        imap.end();
        reject(imapErr);
      });

      imap.once('end', () => {
        console.log('Connection to IMAP server closed.');
      });
    });
  }

  async replyToEmailByUid(
    replyEmailDto: ReplyEmailDto // Handle different attachment formats
  ) {
    const { userInboxConfigId, uid, folder, to, cc, bcc, subject, body, attachments, replyAll } = replyEmailDto;


    if (!userInboxConfigId) {
      throw new Error('userInboxConfigId is required');  // Handle missing field
    }


    const userInboxConfig = await this.findById(new Types.ObjectId(userInboxConfigId));

    if (!userInboxConfig) {
      throw new Error('userInboxConfigId is required');  // Handle missing field
    }


    // Step 1: Fetch the original email using uid
    const originalEmail = await this.fetchSingleEmail(userInboxConfig, uid, folder);
    if (!originalEmail) {
      throw new Error('Original email not found');
    }


    // Step 2: Compose the reply
    const replySubject = subject ? subject : `Re: ${originalEmail.headers.Subject}`;

    const attachmentIds: { filename: string; contentType: string; content: string }[] = []; // Attachments info


    // Format the quoted original email directly within the main function
    const quotedOriginal = `
  <div style="margin-top: 15px;">
    <p>----- Original Message -----<br>
    From: ${originalEmail.headers.From} | To: ${originalEmail.headers.To}</p>
    <p>On ${originalEmail.headers.Date}, ${originalEmail.headers.From} wrote:</p>
    <blockquote style="border-left: 1px solid #ccc; margin: 0; padding-left: 10px; color: #555;">
      ${originalEmail.body}
    </blockquote>
  </div>
`;


    const fullHtmlBody = `
      <html>
        <head>
          <meta charset="UTF-8">
        </head>
        <body>
          ${body}
          ${quotedOriginal}
        </body>
      </html>
    `;

    if (attachments && attachments.length > 0) {
      for (const attachment of attachments) {
        if (typeof attachment === "string") {
          // Fetch FileMetadata by ID
          const file = await this.fileMetadataModel.findById(attachment);
          if (file && file.locationUrl) {
            const response = await fetch(file.locationUrl);
            if (!response.ok) {
              throw new Error(`Failed to fetch attachment: ${file.originalName}`);
            }
            const arrayBuffer = await response.arrayBuffer();
            const base64Content = Buffer.from(arrayBuffer).toString("base64");
            attachmentIds.push({
              filename: file.originalName,
              contentType: file.fileType,
              content: base64Content,
            });
          }
        } else if (attachment instanceof FileMetadata) {
          // Process FileMetadata directly
          const file = attachment as FileMetadata;
          if (file.locationUrl) {
            const response = await fetch(file.locationUrl);
            if (!response.ok) {
              throw new Error(`Failed to fetch attachment: ${file.originalName}`);
            }
            const arrayBuffer = await response.arrayBuffer();
            const base64Content = Buffer.from(arrayBuffer).toString("base64");
            attachmentIds.push({
              filename: file.originalName,
              contentType: file.fileType,
              content: base64Content,
            });
          }
        } else if ("filename" in attachment && "content" in attachment && "contentType" in attachment) {
          // Handle plain attachment objects
          attachmentIds.push(attachment as { filename: string; content: string; contentType: string });
        } else {
          throw new Error("Invalid attachment format");
        }
      }
    }
    // Step 3: Configure the SMTP transporter
    const transporter = nodemailer.createTransport({
      host: userInboxConfig.smtpHost,
      port: userInboxConfig.smtpPort,
      secure: userInboxConfig.isEnableSSL,
      auth: {
        user: userInboxConfig.userName,
        pass: userInboxConfig.password,
      },
    });

    // Step 4: Prepare mail options based on reply type
    let mailOptions: any = {
      from: userInboxConfig.fromEmail,
      to: to.join(','),  // Reply to the original sender
      subject: replySubject,
      text: body,
      html: fullHtmlBody,
      attachments: attachmentIds,
    };
    // Conditionally add cc and bcc if replyAll is true and values are provided
    if (replyAll) {
      if (cc && cc.length > 0) {
        mailOptions.cc = cc.join(',');
      }
      if (bcc && bcc.length > 0) {
        mailOptions.bcc = bcc.join(',');
      }
    }

    try {
      // Step 5: Send the email
      await transporter.sendMail(mailOptions);




      // Step 6: Store the reply in the "Sent" folder
      const imapConfig = {
        imap: {
          user: userInboxConfig.userName,
          password: userInboxConfig.password,
          host: userInboxConfig.imapHost,
          port: userInboxConfig.imapPort,
          tls: userInboxConfig.isEnableSSL,
          authTimeout: 3000,
        },
      };

      const connection = await ImapSimple.connect(imapConfig);

      let rawEmail = `From: ${userInboxConfig.fromEmail}\r\n` +
        `To: ${to.join(',')}\r\n` +  // Include 'To' recipients
        (cc && cc.length > 0 ? `Cc: ${cc.join(',')}\r\n` : '') +  // Include CC if provided
        (bcc && bcc.length > 0 ? `Bcc: ${bcc.join(',')}\r\n` : '') +
        `Subject: ${replySubject}\r\n` +
        `Date: ${new Date().toUTCString()}\r\n` +
        `MIME-Version: 1.0\r\n` +
        `Content-Type: multipart/mixed; boundary="boundary1"\r\n\r\n` +
        `--boundary1\r\n` +
        `Content-Type: text/html; charset=UTF-8\r\n` +
        `Content-Transfer-Encoding: 7bit\r\n\r\n` +
        `${fullHtmlBody}\r\n\r\n`;

      attachmentIds.forEach(att => {
        rawEmail += `--boundary1\r\n` +
          `Content-Type: ${att.contentType}; name="${att.filename}"\r\n` +
          `Content-Disposition: attachment; filename="${att.filename}"\r\n` +
          `Content-Transfer-Encoding: base64\r\n\r\n` +
          `${att.content}\r\n\r\n`;
      });

      rawEmail += `--boundary1--`;  // End of multipart

      const mailboxes = await connection.getBoxes();

      // List of common "Sent" folder variations
      const sentFolderVariations = ['Sent', 'Sent Mail', 'Sent Items', 'Sent Messages', 'Envoyés', 'Enviados'];

      let sentFolder = null;

      // Check if any of the common variations exist
      for (const variation of sentFolderVariations) {
        if (mailboxes[variation]) {
          sentFolder = variation;
          break;
        }
      }

      // If no folder found, default to "Sent" and create it if necessary
      if (!sentFolder) {
        sentFolder = 'Sent';  // Default to "Sent"
        if (!mailboxes[sentFolder]) {
          await connection.addBox(sentFolder);  // Create the folder if it doesn't exist
          this.logger.log(`Created new Sent folder: ${sentFolder}`);
        }
      }

      // Automatically subscribe to the "Sent" folder if not already subscribed
      const imap = connection.imap;
      imap.subscribeBox(sentFolder, (err) => {
        if (err) {
          this.logger.error(`Failed to subscribe to folder: ${sentFolder}`, err.message);
        } else {
          this.logger.log(`Successfully subscribed to folder: ${sentFolder}`);
        }
      });

      // Append the reply to the Sent folder
      await connection.append(rawEmail, {
        mailbox: sentFolder, // The target mailbox (folder)
        flags: ['\\Seen'],   // Mark it as read
      });


      await connection.end();

      this.logger.log('Reply successfully sent and appended to Sent folder');
      return rawEmail; // Return the reply response
    } catch (error) {
      this.logger.error('Error sending reply or appending to Sent folder:', error);
      throw new Error(`Failed to send reply: ${error.message}`);
    }
  }

  async forwardEmailByUid(
    forwardEmailDto: ForwardEmailDto
  ) {

    const { userInboxConfigId, uid, folder, to, subject, body, cc, bcc, attachments } = forwardEmailDto;

    if (!userInboxConfigId) {
      throw new Error('userInboxConfigId is required');  // Handle missing field
    }


    const userInboxConfig = await this.findById(new Types.ObjectId(userInboxConfigId));

    if (!userInboxConfig) {
      throw new Error('userInboxConfigId is required');  // Handle missing field
    }

    // Step 1: Fetch the original email using uid
    const originalEmail = await this.fetchSingleEmail(userInboxConfig, uid, folder);
    if (!originalEmail) {
      throw new Error('Original email not found');
    }

    // Step 2: Compose the forward subject
    const forwardSubject = subject ? subject : `Fwd: ${originalEmail.headers.Subject}`;

    const attachmentIds: { filename: string; contentType: string; content: string }[] = []; // Attachments info

    // Step 3: Include the original email's body as quoted content
    const quotedOriginal = `
      <p>On ${originalEmail.headers.Date}, ${originalEmail.headers.From} wrote:</p>
      <blockquote style="margin: 10px 0; padding: 10px; border-left: 2px solid #ccc;">
        ${originalEmail.body}  
      </blockquote> 
    `;

    // Combine the new body and the quoted original email content
    const fullHtmlBody = `
      <html>
        <head>
          <meta charset="UTF-8">
        </head>
        <body>
          ${body.trim() ? `<p>${body}</p>` : ''}
          ${quotedOriginal}
        </body>
      </html>
    `;

    // Step 4: Process attachments
    if (attachments && attachments.length > 0) {
      for (const attachment of attachments) {
        if (typeof attachment === "string") {
          // Fetch FileMetadata by ID
          const file = await this.fileMetadataModel.findById(attachment);
          if (file && file.locationUrl) {
            const response = await fetch(file.locationUrl);
            if (!response.ok) {
              throw new Error(`Failed to fetch attachment: ${file.originalName}`);
            }
            const arrayBuffer = await response.arrayBuffer();
            const base64Content = Buffer.from(arrayBuffer).toString("base64");
            attachmentIds.push({
              filename: file.originalName,
              contentType: file.fileType,
              content: base64Content,
            });
          }
        } else if (attachment instanceof FileMetadata) {
          // Process FileMetadata directly
          const file = attachment as FileMetadata;
          if (file.locationUrl) {
            const response = await fetch(file.locationUrl);
            if (!response.ok) {
              throw new Error(`Failed to fetch attachment: ${file.originalName}`);
            }
            const arrayBuffer = await response.arrayBuffer();
            const base64Content = Buffer.from(arrayBuffer).toString("base64");
            attachmentIds.push({
              filename: file.originalName,
              contentType: file.fileType,
              content: base64Content,
            });
          }
        } else if ("filename" in attachment && "content" in attachment && "contentType" in attachment) {
          // Handle plain attachment objects
          attachmentIds.push(attachment as { filename: string; content: string; contentType: string });
        } else {
          throw new Error("Invalid attachment format");
        }
      }
    }


    // Step 5: Configure the SMTP transporter
    const transporter = nodemailer.createTransport({
      host: userInboxConfig.smtpHost,
      port: userInboxConfig.smtpPort,
      secure: userInboxConfig.isEnableSSL, // True if SSL/TLS should be used
      auth: {
        user: userInboxConfig.userName,   // SMTP user (e.g., email address)
        pass: userInboxConfig.password,   // SMTP password
      },
    });

    // Step 6: Prepare the mail options for forwarding
    const mailOptions = {
      from: userInboxConfig.fromEmail, // Sender's email
      to: to.join(','),                // Recipients
      cc: cc ? cc.join(',') : undefined, // CC (if provided)
      bcc: bcc ? bcc.join(',') : undefined, // BCC (if provided)
      subject: forwardSubject,         // Forwarded subject
      html: fullHtmlBody,              // HTML body
      attachments: attachmentIds,      // Attachments
    };

    // Step 7: Send the forwarded email
    try {
      const info = await transporter.sendMail(mailOptions);
      console.log('Forwarded email sent:', info.messageId);
         // Step 8: Store the forwarded email in the "Sent" folder
    const imapConfig = {
      imap: {
        user: userInboxConfig.userName,
        password: userInboxConfig.password,
        host: userInboxConfig.imapHost,
        port: userInboxConfig.imapPort,
        tls: userInboxConfig.isEnableSSL,
        authTimeout: 3000,
      },
    };

    const connection = await ImapSimple.connect(imapConfig);

    // Create raw email content for storing in Sent folder
    let rawEmail = `From: ${userInboxConfig.fromEmail}\r\n` +
      `To: ${to.join(',')}\r\n` +
      (cc && cc.length > 0 ? `Cc: ${cc.join(',')}\r\n` : '') +
      (bcc && bcc.length > 0 ? `Bcc: ${bcc.join(',')}\r\n` : '') +
      `Subject: ${forwardSubject}\r\n` +
      `Date: ${new Date().toUTCString()}\r\n` +
      `MIME-Version: 1.0\r\n` +
      `Content-Type: multipart/mixed; boundary="boundary1"\r\n\r\n` +
      `--boundary1\r\n` +
      `Content-Type: text/html; charset=UTF-8\r\n` +
      `Content-Transfer-Encoding: 7bit\r\n\r\n` +
      `${fullHtmlBody}\r\n\r\n`;

    // Add attachments to raw email if any
    attachmentIds.forEach(att => {
      rawEmail += `--boundary1\r\n` +
        `Content-Type: ${att.contentType}; name="${att.filename}"\r\n` +
        `Content-Disposition: attachment; filename="${att.filename}"\r\n` +
        `Content-Transfer-Encoding: base64\r\n\r\n` +
        `${att.content}\r\n\r\n`;
    });

    rawEmail += `--boundary1--`;  // End of multipart

    // Get available mailboxes
    const mailboxes = await connection.getBoxes();

    // List of common "Sent" folder variations
    const sentFolderVariations = ['Sent', 'Sent Mail', 'Sent Items', 'Sent Messages', 'Envoyés', 'Enviados'];

    let sentFolder = null;

    // Check if any of the common variations exist
    for (const variation of sentFolderVariations) {
      if (mailboxes[variation]) {
        sentFolder = variation;
        break;
      }
    }

    // If no folder found, default to "Sent" and create it if necessary
    if (!sentFolder) {
      sentFolder = 'Sent';  // Default to "Sent"
      if (!mailboxes[sentFolder]) {
        await connection.addBox(sentFolder);  // Create the folder if it doesn't exist
        console.log(`Created new Sent folder: ${sentFolder}`);
      }
    }

    // Automatically subscribe to the "Sent" folder if not already subscribed
    const imap = connection.imap;
    imap.subscribeBox(sentFolder, (err) => {
      if (err) {
        console.error(`Failed to subscribe to folder: ${sentFolder}`, err.message);
      } else {
        console.log(`Successfully subscribed to folder: ${sentFolder}`);
      }
    });

    // Append the forwarded email to the Sent folder
    await connection.append(rawEmail, {
      mailbox: sentFolder, // The target mailbox (folder)
      flags: ['\\Seen'],   // Mark it as read
    });

    await connection.end();

    console.log('Forwarded email successfully sent and appended to Sent folder');
      return 'Forward message successfully.';  // Return the message ID of the sent email
    } catch (err) {
      console.error('Error forwarding email:', err);
      throw new Error('Failed to forward the email');
    }
  }

  // Method to move the email to the Trash folder
  async deleteEmailToTrash(userInboxConfig: UserInboxConfig, uid: string, folder: string): Promise<string> {
    const imap = await this.connectToImap(userInboxConfig);

    return new Promise((resolve, reject) => {
      imap.openBox(folder, false, (err) => {
        if (err) {
          imap.end();
          return reject(`Error opening folder ${folder}: ${err.message}`);
        }

        // Move the email to the Trash folder
        imap.move([uid], 'Trash', (err) => {
          if (err) {
            imap.end();
            return reject(`Error moving email to Trash: ${err.message}`);
          }
          console.log(`Email ${uid} moved to Trash successfully.`);
          imap.end();
          resolve(`Email ${uid} moved to Trash.`);
        });
      });
    });
  }

  // Method to permanently delete the email from the Trash folder
  async permanentlyDeleteEmail(userInboxConfig: UserInboxConfig, uid: string): Promise<string> {
    const imap = await this.connectToImap(userInboxConfig);

    return new Promise((resolve, reject) => {
      console.log('Starting to permanently delete email:', uid);

      // Fetch available folders
      imap.getBoxes((err, boxes) => {
        if (err) {
          console.error('Error fetching folders:', err.message);
          imap.end();
          return reject(`Error fetching folders: ${err.message}`);
        }

        // Check if 'Trash' folder is present
        if (!boxes['Trash']) {
          console.error('Trash folder not found.');
          imap.end();
          return reject('Trash folder not found.');
        }

        // Open Trash folder
        imap.openBox('Trash', false, (err) => {
          if (err) {
            console.error('Error opening Trash folder:', err.message);
            imap.end();
            return reject(`Error opening Trash folder: ${err.message}`);
          }

          // Search for the email UID using UID-based search
          imap.search([['UID', uid]], (err, uids) => {
            if (err) {
              console.error('Error searching for email in Trash:', err.message);
              imap.end();
              return reject(`Error searching for email in Trash: ${err.message}`);
            }

            const emailUid = uids.find(ud => ud.toString() === uid);
            if (!emailUid) {
              console.error(`Email ${uid} not found in Trash.`);
              imap.end();
              return reject(`Email ${uid} not found in Trash.`);
            }

            // Mark email as deleted
            imap.addFlags([emailUid], ['\\Deleted'], (err) => {
              if (err) {
                console.error('Error marking email for deletion:', err.message);
                imap.end();
                return reject(`Error marking email for deletion: ${err.message}`);
              }

              // Expunge to permanently delete the email
              imap.expunge((err) => {
                if (err) {
                  console.error('Error expunging email from Trash:', err.message);
                  imap.end();
                  return reject(`Error expunging email from Trash: ${err.message}`);
                }

                console.log(`Email ${emailUid} successfully expunged from Trash.`);
                imap.end();
                resolve(`Email ${emailUid} permanently deleted.`);
              });
            });
          });
        });
      });
    });
  }

  async countUnreadEmails(userInboxConfig: UserInboxConfig, folder: string): Promise<number> {
    const imap = await this.connectToImap(userInboxConfig);

    return new Promise((resolve, reject) => {
      imap.openBox(folder, true, (err, box) => {
        if (err) {
          console.error(`Error opening folder ${folder} for unread count: ${err.message}`);
          imap.end();
          return reject(err);
        }
        console.log(`Counting unread emails in folder: ${folder}`);

        // Search for emails that are unread
        imap.search(['UNSEEN'], (err, results) => {
          if (err) {
            console.error(`Error searching for unread emails in folder ${folder}: ${err.message}`);
            imap.end();
            return reject(err);
          }

          console.log(`Unread emails in folder ${folder}: ${results.length}`);
          imap.end(); // Close the IMAP connection
          resolve(results.length); // Return the count of unread emails
        });
      });
    });
  }

  async fetchUnseenEmailsFromFolder(userInboxConfig: UserInboxConfig, folder: string): Promise<any> {
    const result: { emails: any[], attribs: string[] } = {
      emails: [],
      attribs: []
    };

    const imap = await this.connectToImap(userInboxConfig);

    console.log(`Fetching unseen emails from folder: ${folder}`);

    return new Promise((resolve, reject) => {
      imap.openBox(folder, true, (err) => {
        if (err) {
          console.error(`Error opening folder ${folder}: ${err.message}`);
          imap.end();
          return reject(err);
        }
        console.log(`Successfully opened folder: ${folder}`);

        // Search for unseen emails
        imap.search(['UNSEEN'], (err, results) => {
          if (err) {
            console.error(`Error searching for unseen emails in folder ${folder}: ${err.message}`);
            imap.end();
            return reject(err);
          }

          console.log(`Found ${results.length} unseen emails in folder ${folder}`);
          if (results.length === 0) {
            imap.end();
            resolve(result);
            return;
          }

          const emailPromises: Promise<void>[] = [];
          const f = imap.fetch(results, {
            bodies: '',
            markSeen: false, // Don't mark as seen
          });

          f.on('message', (msg) => {
            let buffer = '';
            const email: { headers: any | null, body: string | null, uid: string | null, flags: string[] } = {
              headers: null,
              body: null,
              uid: null,
              flags: []
            };

            const emailPromise = new Promise<void>((resolveEmail) => {
              msg.on('body', (stream) => {
                stream.on('data', (chunk) => {
                  buffer += chunk.toString('utf8');
                });

                stream.once('end', async () => {
                  try {
                    const parsed = await mailparser.simpleParser(buffer);

                    const rawHeaders = buffer.split('\r\n\r\n')[0];
                    const headersArray = rawHeaders.split('\r\n');
                    const formattedHeaders: any = {};

                    headersArray.forEach(headerLine => {
                      const [key, value] = headerLine.split(': ');
                      if (key && value) {
                        let decodedValue = value;
                        if (key === 'Subject') {
                          const match = value.match(/=\?UTF-8\?B\?(.*)\?=/);
                          if (match) {
                            decodedValue = Buffer.from(match[1], 'base64').toString('utf8');
                          }
                        }
                        formattedHeaders[key] = decodedValue;
                      }
                    });

                    email.headers = {
                      From: formattedHeaders['From'] || 'Unknown sender',
                      To: formattedHeaders['To'] || 'Unknown recipient',
                      Subject: formattedHeaders['Subject'] || 'No subject',
                      Date: formattedHeaders['Date'] || 'Unknown date',
                    };

                    email.body = parsed.html || parsed.text || null;
                    result.emails.push(email);
                    resolveEmail();
                  } catch (err) {
                    console.error(`Failed to parse email body: ${err.message}`);
                    resolveEmail();
                  }
                });
              });

              msg.once('attributes', (attrs) => {
                email.uid = attrs.uid.toString();
                email.flags = attrs.flags;
              });
            });

            emailPromises.push(emailPromise);
          });

          f.once('error', (err) => {
            console.error(`Fetch error in folder ${folder}: ${err.message}`);
            imap.end();
            reject(err);
          });

          f.once('end', async () => {
            console.log(`Finished fetching unseen emails from folder ${folder}`);
            result.attribs = ['\\HasNoChildren', '\\UnMarked'];
            imap.end();
            await Promise.all(emailPromises);

            result.emails.sort((a, b) => {
              const dateA = new Date(a.headers.Date || 0);
              const dateB = new Date(b.headers.Date || 0);
              return dateB.getTime() - dateA.getTime();
            });
            resolve(result);
          });
        });
      });
    });
  }

  async markEmailAsSeen(userInboxConfig: UserInboxConfig, uid: string, folder: string): Promise<string> {
    const imap = await this.connectToImap(userInboxConfig);

    return new Promise((resolve, reject) => {
      // Open the specified folder
      imap.openBox(folder, false, (err) => {
        if (err) {
          console.error(`Error opening folder ${folder}: ${err.message}`);
          imap.end();
          return reject(err);
        }

        // Mark the email as seen
        imap.addFlags(uid, '\\Seen', (err) => {
          imap.end(); // Close the IMAP connection
          if (err) {
            console.error(`Error marking email as seen: ${err.message}`);
            return reject(err);
          }

          console.log(`Email with UID ${uid} marked as seen in folder ${folder}`);
          resolve(`Email with UID ${uid} marked as seen.`);
        });
      });
    });
  }

  async markEmailAsUnseen(userInboxConfig: UserInboxConfig, uid: string, folder: string): Promise<string> {
    const imap = await this.connectToImap(userInboxConfig);

    return new Promise((resolve, reject) => {
      // Open the specified folder
      imap.openBox(folder, false, (err) => {
        if (err) {
          console.error(`Error opening folder ${folder}: ${err.message}`);
          imap.end();
          return reject(err);
        }

        imap.delFlags(uid, '\\Seen', (err) => {
          if (err) {
            console.error(`Error marking emails as unseen: ${err.message}`);
            imap.end();
            return reject(err);
          }
          console.log(`Email with UID ${uid} marked as unseen in folder ${folder}`);
          resolve(`Email with UID ${uid} marked as unseen.`);
        });
      });
    });
  }

  async searchEmails(
    userInboxConfig: UserInboxConfig,
    folder: string,
    searchTerm: string // Single search term from the search box
  ): Promise<any> {
    const term = searchTerm.toLowerCase();
    const result: { emails: any[], attribs: string[] } = { emails: [], attribs: [] };

    // Fetch emails from the specified folder
    const emails = await this.fetchEmailsFromFolder(userInboxConfig, folder);

    // Filter emails based on search criteria
    result.emails = emails.emails.filter((email: { headers: { Subject: string; From: string; To: { email: string; }[]; }; body: string; }) => {
      const subject = typeof email.headers.Subject === 'string' ? email.headers.Subject.toLowerCase() : '';
      const from = typeof email.headers.From === 'string' ? email.headers.From.toLowerCase() : '';
      const body = typeof email.body === 'string' ? email.body.toLowerCase() : '';
      const to = email.headers.To.map((recipient: { email: string; }) => recipient.email.toLowerCase()).join(',');

      return [subject, from, body, to].some((field) => field.includes(term));
    });

    // Return the filtered result
    return result;
  }

  // Add more methods as needed for your application

  emitEvent(eventName: string, payload: any) {
    // payload['event']= eventName;
    this.logger.debug(payload)
    this.eventEmitter.emit(eventName, payload);
  }

}
