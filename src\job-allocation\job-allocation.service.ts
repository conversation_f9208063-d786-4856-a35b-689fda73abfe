import { Injectable, NotFoundException, Logger, InternalServerErrorException, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { JobAllocationBase, JobAllocationBaseDocument } from './schemas/job-allocation-base.schema';
import { JobAllocationToAssignees } from './schemas/job-allocation-to-assignees.schema';
import { JobAllocationToFreelancers } from './schemas/job-allocation-to-freelancers.schema';
import { JobAllocationToVendors } from './schemas/job-allocation-to-vendors.schema';
import { CreateJobAllocationToAssigneesDto } from './dto/create-job-allocation-to-assignees.dto';
import { FindAllAssigneeJobAllocationsDto } from './dto/find-all-assignee-job-allocations.dto';
import { UpdateJobAllocationToAssigneesDto } from './dto/update-dto/update-job-allocation-to-assignees.dto';
import { CreateJobAllocationToVendorsDto } from './dto/create-job-allocation-to-vendors.dto';
import { UpdateJobAllocationToVendorsDto } from './dto/update-dto/update-job-allocation-to-vendors.dto';
import { FindAllVendorJobAllocationsDto } from './dto/find-all-vendor-job-allocations.dto';
import { CreateJobAllocationToFreelancersDto } from './dto/create-job-allocation-to-freelancers.dto';
import { FindAllFreelancerJobAllocationsDto } from './dto/find-all-freelancer-job-allocations.dto';
import { UpdateJobAllocationToFreelancersDto } from './dto/update-dto/update-job-allocation-to-freelancers.dto';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';
import { FilterByDueDateDto } from './dto/filter-by-due-date.dto';
import { UpdateJobAllocationBaseDto } from './dto/update-dto/update-job-allocation-base.dto';
import { Role } from 'src/auth/enums/role.enum';
import { Job } from 'src/job/schemas/job.schema';
import { NotificationsService } from 'src/notification/notifications.service';
import { NotificationType } from 'src/shared/constants';
import { BasicUser } from 'src/user/schemas/basic-user.schema';
import { AIRecruiterQA, AIRecruiterQADocument } from './schemas/ai-recruiter-qa.schema';
import { CreateAIRecruiterQADto } from './dto/create-ai-recruiter-qa.dto';
import { CreateJobAllocationToAiRecruiterDto } from './dto/create-job-allocation-to-aiRecruiter.dto';
import { JobAllocationToAiRecruiters } from './schemas/job-allocation-to-aiRecruiter.schema';
import { FindAllAiRecruiterJobAllocationsDto } from './dto/find-all-aiRecruiters-job-allocations.dto';
import { UpdateJobAllocationToAiRecruiterDto } from './dto/update-dto/update-job-allocation-to-aiRecruiter.dto';

@Injectable()
export class JobAllocationService {
  private readonly logger = new Logger(JobAllocationService.name);

  constructor(
    @InjectModel(JobAllocationBase.name) private readonly jobAllocationModel: Model<JobAllocationBase>,
    @InjectModel(JobAllocationToAssignees.name)
    private readonly assigneesModel: Model<JobAllocationToAssignees>,
    @InjectModel(JobAllocationToVendors.name) private readonly vendorsModel: Model<JobAllocationToVendors>,
    @InjectModel(JobAllocationToFreelancers.name) private readonly freelancersModel: Model<JobAllocationToFreelancers>,
    @InjectModel(JobAllocationToAiRecruiters.name) private readonly aiRecruiterModel: Model<JobAllocationToAiRecruiters>,
    @InjectModel(Job.name) private readonly jobsModel: Model<Job>,
    @InjectModel(BasicUser.name) private readonly basicUserModel: Model<BasicUser>,
    private eventEmitter: EventEmitter2,
    private readonly notificationsService: NotificationsService,
    @InjectModel(AIRecruiterQA.name) private readonly AIRecruiterQAModel: Model<AIRecruiterQADocument>,
  ) { }

  // Helper method to get populate options based on allocation kind
  private getPopulateOptions(kind: string): any[] {
    const baseOptions = [
      { path: 'job', select: '_id title description status priority postingOrg', model: 'Job' },
      { path: 'createdBy', select: '_id roles firstName email', model: 'BasicUser' }
    ];

    switch (kind) {
      case 'JobAllocationToAssignees':
        return [
          ...baseOptions,
          { path: 'assignee', select: '_id roles firstName lastName email', model: 'BasicUser' },
          { path: 'assignees', select: '_id roles firstName lastName email', model: 'BasicUser' }
        ];

      case 'JobAllocationToVendors':
        return [
          ...baseOptions,
          { path: 'vendor', select: '_id name description ', model: 'Org' },
          { path: 'vendors', select: '_id name description', model: 'Org' },
        ];

      case 'JobAllocationToFreelancers':
        return baseOptions; // Freelancers only need base options
      case 'JobAllocationToAiRecruiters':
        return baseOptions; // AiRecruiter only need base options

      default:
        this.logger.log(`Unknown job allocation kind: ${kind}, using base populate options`);
        return baseOptions;
    }
  }

  // Helper method to get query conditions with proper kind handling
  private getBaseQueryConditions(query: Partial<FindAllAssigneeJobAllocationsDto>): any {
    const conditions: any = { isDeleted: false };

    const { priority, dueDate, dateDirection } = query;

    if (priority) {
      conditions.priority = priority;
    }

    if (dueDate) {
      const currentDate = new Date(dueDate);
      currentDate.setHours(0, 0, 0, 0);
      const nextDate = new Date(currentDate);
      nextDate.setDate(nextDate.getDate() + 1);

      if (dateDirection === 'prev') {
        conditions.dueDate = { $lt: currentDate };
      } else if (dateDirection === 'next') {
        conditions.dueDate = { $gt: nextDate };
      } else {
        // If no direction specified, get jobs for the current date
        conditions.dueDate = {
          $gte: currentDate,
          $lt: nextDate
        };
      }
    }

    return conditions;
  }

  async createJoballocation(createDto: any) {
    try {
      const createdJobAllocation = new this.jobAllocationModel(createDto);
      const savedAllocation = await createdJobAllocation.save();
      this.emitEvent('job.allocation.created', savedAllocation);
      return savedAllocation;
    } catch (error) {
      if (error.name === 'ValidationError') {
        throw new BadRequestException(error.message);
      }
      throw new InternalServerErrorException('Error while creating Job Allocation.');
    }
  }

  async findOneJobAllocation(id: string): Promise<JobAllocationBaseDocument> {
    try {
      const jobAllocation = await this.jobAllocationModel
        .findById(id)
        .populate('job')
        .populate('createdBy')
        .exec();

      if (!jobAllocation || jobAllocation.isDeleted) {
        throw new NotFoundException(`Job allocation with ID ${id} not found`);
      }

      return jobAllocation;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException('Error while fetching job allocation');
    }
  }

  async updateJoballocation(jobAllocationId: Types.ObjectId, updateDto: any) {
    try {
      const updatedJobAllocation = await this.jobAllocationModel.findByIdAndUpdate(
        jobAllocationId,
        updateDto,
        { new: true }
      );

      if (!updatedJobAllocation) {
        throw new NotFoundException('Job allocation not found');
      }

      this.emitEvent('job.allocation.created', updatedJobAllocation);
      return updatedJobAllocation;
    } catch (error) {
      if (error.name === 'ValidationError') {
        throw new BadRequestException(error.message);
      }
      throw new InternalServerErrorException('Error while updating Job Allocation.');
    }
  }

  async getJobAllocationsByJob(jobId: string) {
    try {
      const jobAllocations = await this.jobAllocationModel
        .find({
          job: jobId,
          isDeleted: { $ne: true }
        })
        .populate('job')
        .populate('createdBy')
        .exec();

      if (!jobAllocations || jobAllocations.length === 0) {
        throw new NotFoundException(`No job allocations found for job ID: ${jobId}`);
      }

      return jobAllocations;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException('Error while fetching job allocations');
    }
  }

  // Create for Assignees
  // async createForAssignees(createDto: CreateJobAllocationToAssigneesDto): Promise<JobAllocationBaseDocument | JobAllocationBaseDocument[]> {
  //   try {
  //     // If assignees array is provided and not empty, create multiple allocations
  //     if (createDto.assignees && createDto.assignees.length > 0) {
  //       const allocations = await Promise.all(
  //         createDto.assignees.map(async (assigneeId) => {
  //           const allocationData = {
  //             ...createDto,
  //             assignee: assigneeId,
  //             assignees: undefined // Remove assignees array from individual allocations
  //           };
  //           const createdJobAllocation = new this.assigneesModel(allocationData);
  //           const savedAllocation = await createdJobAllocation.save();
  //           this.emitEvent('assignee.job.allocation.created', savedAllocation);
  //           return savedAllocation;
  //         })
  //       );
  //       return allocations;
  //     }

  //     // If only single assignee, create one allocation
  //     const createdJobAllocation = new this.assigneesModel(createDto);
  //     const savedAllocation = await createdJobAllocation.save();
  //     this.emitEvent('assignee.job.allocation.created', savedAllocation);
  //     return savedAllocation;
  //   } catch (error) {
  //     this.logger.error(`Failed to create Job Allocation for Assignees: ${error.message}`);
  //     if (error.name === 'ValidationError') {
  //       throw new BadRequestException(error.message);
  //     }
  //     throw new InternalServerErrorException('Error while creating Job Allocation.');
  //   }
  // }

  async createForAssignees(createDto: CreateJobAllocationToAssigneesDto): Promise<JobAllocationBaseDocument[]> {
    try {

      // Case 1: Single assignee
      if (createDto.assignee) {
        const allocationData = {
          ...createDto,
          assignees: [], // Empty array as per requirement
        };

        const createdJobAllocation = new this.assigneesModel(allocationData);
        const savedAllocation = await createdJobAllocation.save();

        const job = await this.jobsModel.findById(savedAllocation.job).populate('endClientOrg').exec();
        const allocation = await this.assigneesModel.findById(savedAllocation._id).populate('createdBy').populate('assignee').exec();
        const jobTitle = job?.title || 'a job';
        const clientName = job?.endClientOrg?.title || 'a client';
        const createdbyName = (allocation?.createdBy?.firstName ?? '') +' '+(allocation?.createdBy?.lastName ?? '') || 'the admin';
        const assigneeName = (allocation?.assignee?.firstName ?? '') +' '+(allocation?.assignee?.lastName ?? '') || 'the admin';

        const message_1 = `You have been allocated to work on ${jobTitle} by ${createdbyName}.`;
        const message_2 = `You have allocated ${jobTitle} to ${assigneeName}.`;

        // notify the user who is assigned with job
        await this.notificationsService.createAndNotify(
          createDto.assignee, // the user receiving the job
          message_1,
          createDto.createdBy, // the person who assigned (could be admin/manager)
          {},
          NotificationType.JOB_ALLOCATION,
          savedAllocation.job.toString()
        );

        // notify the user who assigned the job
        await this.notificationsService.createAndNotify(
          savedAllocation.createdBy.toString(), 
          message_2,
          savedAllocation.createdBy.toString(),
          {},
          NotificationType.JOB_ALLOCATION,
          savedAllocation.job.toString()
        );

        this.emitEvent('assignee.job.allocation.created', savedAllocation);
        return [savedAllocation];
      }

      // Case 2: Multiple assignees
      if (!createDto.assignees || createDto.assignees.length === 0) {
        throw new BadRequestException('Either assignee or at least one assignee in assignees array is required');
      }

      const allocations = await Promise.all(
        createDto.assignees.map(async (assigneeId) => {
          const allocationData = {
            ...createDto,
            assignee: assigneeId,
            assignees: [], // Empty array as per requirement
          };

          const createdJobAllocation = new this.assigneesModel(allocationData);
          const savedAllocation = await createdJobAllocation.save();
          this.emitEvent('assignee.job.allocation.created', savedAllocation);
          return savedAllocation;
        })
      );

      return allocations;
    } catch (error) {
      this.logger.error(`Failed to create Job Allocation for Assignees: ${error.message}`);
      if (error.name === 'ValidationError') {
        throw new BadRequestException(error.message);
      }
      throw new InternalServerErrorException('Error while creating Job Allocation.');
    }
  }

  // Find all for Assignees (with pagination)
  async findAllAssigneeJobAllocations(query: FindAllAssigneeJobAllocationsDto) {
    const { assignee, page = 1, limit = 10 } = query;

    const conditions = {
      ...this.getBaseQueryConditions(query),
      kind: 'JobAllocationToAssignees'
    };

    if (assignee) {
      if (!Types.ObjectId.isValid(assignee)) {
        throw new BadRequestException('Invalid assignee ID format');
      }
      conditions.assignee = new Types.ObjectId(assignee);
    }

    try {

      const total = await this.assigneesModel.countDocuments(conditions);

      const jobAllocations = await this.assigneesModel.find(conditions)
        .skip((page - 1) * limit)
        .limit(limit)
        .populate(this.getPopulateOptions('JobAllocationToAssignees'))
        .sort({ updatedAt: -1 })
        .exec();

      return jobAllocations;
    } catch (error) {
      this.logger.error(`Error fetching job allocations: ${error.message}`);
      if (error.name === 'CastError') {
        throw new BadRequestException('Invalid ID format provided');
      }
      throw new InternalServerErrorException('Error while fetching job allocations');
    }
  }

  // Find job allocations by date
  async findJobAllocationsByDate(query: FindAllAssigneeJobAllocationsDto) {
    const { page = 1, limit = 10 } = query;

    try {
      const conditions = {
        ...this.getBaseQueryConditions(query),
        kind: 'JobAllocationToAssignees'
      };

      const total = await this.assigneesModel.countDocuments(conditions);

      const jobAllocations = await this.assigneesModel.find(conditions)
        .skip((page - 1) * limit)
        .limit(limit)
        .populate(this.getPopulateOptions('JobAllocationToAssignees'))
        .sort({ dueDate: query.dateDirection === 'prev' ? -1 : 1 })
        .exec();

      return jobAllocations;
    } catch (error) {
      this.logger.error(`Error fetching job allocations by date: ${error.message}`);
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new InternalServerErrorException('Error while fetching job allocations');
    }
  }

  // Find all job allocations for a specific job
  async findJobAllocationsForJob(jobId: string, query: FindAllAssigneeJobAllocationsDto) {
    const { page = 1, limit = 10 } = query;

    try {
      const conditions = {
        ...this.getBaseQueryConditions(query),
        kind: 'JobAllocationToAssignees',
        job: jobId,
        isDeleted: { $ne: true }
      };

      const jobAllocations = await this.assigneesModel.find(conditions)
        .skip((page - 1) * limit)
        .limit(limit)
        .populate(this.getPopulateOptions('JobAllocationToAssignees'))
        .sort({ updatedAt: -1 })
        .exec();

      return jobAllocations;
    } catch (error) {
      this.logger.error(`Error fetching job allocations for job ${jobId}: ${error.message}`);
      if (error.name === 'CastError') {
        throw new BadRequestException('Invalid job ID format');
      }
      throw new InternalServerErrorException('Error while fetching job allocations');
    }
  }

  async updateAllAllocations(jobId: string, updateDto: UpdateJobAllocationBaseDto, userId: any) {
    try {
      const jobAllocations = await this.jobAllocationModel
        .find({
          job: jobId,
          createdBy: userId.toString(),
          isDeleted: false,
          kind: {
            $in: [
              'JobAllocationToAssignees',
              'JobAllocationToVendor',
              'JobAllocationToFreelancer'
            ]
          }
        })
        .populate({
          path: 'job',
          select: '_id title description status priority',
          model: 'Job'
        })
        .lean()
        .exec();

      if (!jobAllocations.length) {
        throw new NotFoundException(`No active allocations found for job ${jobId}`);
      }

      // Group allocations by type with proper typing
      const allocationsByType = jobAllocations.reduce<Record<string, number>>((acc, allocation) => {
        acc[allocation.kind] = (acc[allocation.kind] || 0) + 1;
        return acc;
      }, {});

      jobAllocations.forEach(allocation => {
        this.eventEmitter.emit('job.allocation.update', {
          jobAllocation: allocation,
          changes: updateDto,
          user: userId || allocation.createdBy
        });
      });

      return {
        message: `Update requested for ${jobAllocations.length} allocations`,
        count: jobAllocations.length,
        breakdown: allocationsByType
      };
    } catch (error) {
      this.logger.error(`Error initiating updates for job ${jobId}: ${error.message}`);
      throw error instanceof NotFoundException
        ? error
        : new InternalServerErrorException(`Error while initiating updates: ${error.message}`);
    }
  }



  @OnEvent('job.allocation.update')
  async handleJobAllocationUpdate(payload: any) {
    const { jobAllocation, changes, user } = payload;
    try {
      const updated = await this.jobAllocationModel
        .findByIdAndUpdate(
          jobAllocation._id,
          { $set: changes },
          { new: true }
        ).exec();

      if (updated) {
        this.eventEmitter.emit('job.allocation.updated', {
          previous: jobAllocation,
          updated,
          changes,
          user
        });
      }
    } catch (error) {
      this.logger.error(`Update failed for allocation ${jobAllocation._id}: ${error.message}`);
    }
  }

  //   async findOneAssigneeJobAllocation(id: string): Promise<JobAllocationBaseDocument> {
  //     try {
  //       console.log(id);
  //       const jobAllocation = await this.assigneesModel.findOne({
  //         assignee: id,
  //         kind: 'JobAllocationToAssignees',
  //         isDeleted: false,
  //       })
  //       .populate(this.getPopulateOptions('JobAllocationToAssignees'))
  //       .exec();

  //       if (!jobAllocation) {
  //         throw new NotFoundException('Job allocation not found');
  //       }
  //       console.log(jobAllocation);
  //       return jobAllocation;
  //     } catch (error) {
  //       if (error instanceof NotFoundException) {
  //         throw error;
  //       }
  //       this.logger.error(`Error fetching job allocation: ${error.message}`);
  //       throw new InternalServerErrorException('Error while fetching job allocation');
  //     }
  // }

  async findOneAssigneeJobAllocation(id: string): Promise<JobAllocationBaseDocument[]> {
    try {
      const jobAllocations = await this.assigneesModel.find({
        assignee: id,
        kind: 'JobAllocationToAssignees',
        isDeleted: false,
      })
        .populate(this.getPopulateOptions('JobAllocationToAssignees'))
        .exec();

      return jobAllocations;
    } catch (error) {
      this.logger.error(`Error fetching job allocations: ${error.message}`);
      throw new InternalServerErrorException('Error while fetching job allocations');
    }
  }

  // Get all job allocations for multiple assignees
  async findMultipleAssigneeJobAllocations(assigneeIds: string[], query: FindAllAssigneeJobAllocationsDto) {
    const { page = 1, limit = 10 } = query;

    const validIds = assigneeIds.filter(id => Types.ObjectId.isValid(id));
    if (validIds.length !== assigneeIds.length) {
      throw new BadRequestException('One or more invalid assignee IDs provided');
    }

    const conditions = {
      ...this.getBaseQueryConditions(query),
      kind: 'JobAllocationToAssignees',
      assignee: { $in: validIds.map(id => new Types.ObjectId(id)) }
    };

    try {
      const total = await this.assigneesModel.countDocuments(conditions);

      const jobAllocations = await this.assigneesModel.find(conditions)
        .skip((page - 1) * limit)
        .limit(limit)
        .populate(this.getPopulateOptions('JobAllocationToAssignees'))
        .sort({ updatedAt: -1 })
        .exec();

      return jobAllocations;
    } catch (error) {
      this.logger.error(`Error fetching multiple assignee job allocations: ${error.message}`);
      throw new InternalServerErrorException('Error while fetching job allocations');
    }
  }

  async updateAssigneeJobAllocation(id: Types.ObjectId, updateDto: UpdateJobAllocationToAssigneesDto) {
    try {
      const jobAllocation = await this.assigneesModel.findOneAndUpdate(
        {
          _id: id,
          kind: 'JobAllocationToAssignees'
        },
        updateDto,
        {
          new: true,
          runValidators: true
        }
      )
        .populate(this.getPopulateOptions('JobAllocationToAssignees'))
        .exec();

      if (!jobAllocation) {
        throw new NotFoundException(`Job allocation with ID ${id} not found`);
      }

      return jobAllocation;
    } catch (error) {
      if (error instanceof BadRequestException || error instanceof NotFoundException) {
        throw error;
      }
      if (error.name === 'ValidationError') {
        throw new BadRequestException(error.message);
      }
      this.logger.error(`Error updating job allocation: ${error.message}`);
      throw new InternalServerErrorException('Error while updating job allocation');
    }
  }

  //  async getAnalytics(jobId: Types.ObjectId) {
  //      try {
  //          const query = this.jobAllocationModel.find({ job: jobId });
  //          const populateOptions = [
  //              { path: 'assignee', select: '_id roles firstName lastName email reportingTo', model: 'BasicUser' },
  //              { path: 'job', select: '_id title jobType employmentType workflow', model: 'Job' },
  //              { path: 'org', select: '_id title', model: 'Org' },
  //          ];

  //          const jobAllocations = await query.populate(populateOptions);
  //          let memberCount = 0;
  //          let vendorCount = 0;
  //          const teamLeads = new Set<Types.ObjectId>();

  //          for (const allocation of jobAllocations) {
  //              if (allocation.vendor) {
  //                  vendorCount++;
  //              }

  //              if (allocation.assignee?.roles) {
  //                  if (allocation.assignee.roles.includes(Role.TeamMember)) {
  //                      memberCount++;
  //                      if (allocation.assignee.reportingTo) {
  //                          teamLeads.add(allocation.assignee.reportingTo);
  //                      }
  //                  }
  //                  if (allocation.assignee.roles.includes(Role.TeamLead)) {
  //                      teamLeads.add(allocation.assignee._id);
  //                  }
  //              }
  //          }

  //          return {
  //              vendorCount,
  //              teamCount: teamLeads.size,
  //              memberCount,
  //          };
  //      } catch (error) {
  //          const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
  //          throw new Error(`Failed to retrieve analytics: ${errorMessage}`);
  //      }
  //  }

  async getAnalytics(jobId: any) {
    try {
      const analyticsCount = await this.jobAllocationModel.aggregate([
        {
          $match: { job: jobId ,isDeleted : false}
        },
        { "$addFields": { "assignee": { "$toObjectId": "$assignee" } } },
        {
          $lookup: {
            from: "basicusers",
            localField: "assignee",
            foreignField: "_id",
            as: "assigneeDetails"
          }
        },
        { $unwind: { path: "$assigneeDetails", preserveNullAndEmptyArrays: true } },
        {
          $match: { "assigneeDetails.roles": { $in: [Role.TeamMember] } } // Ensure assignee has "team_member" role
        },
        { "$addFields": { "teamLeadId": { "$toObjectId": "$assigneeDetails.reportingTo" } } },
        {
          $lookup: {
            from: "basicusers",
            localField: "teamLeadId",
            foreignField: "_id",
            as: "reportingToDetails"
          }
        },
        { $unwind: { path: "$reportingToDetails", preserveNullAndEmptyArrays: true } },
        {
          $match: { "reportingToDetails.roles": { $in: [Role.TeamLead] } } // Ensure reporting_to user has "team_lead" role
        },
        {
          $group: {
            _id: "$assigneeDetails.reportingTo", // Group by Team Lead ID
            teamLeadName: { $first: "$reportingToDetails.firstName" },
            teamLeadEmail: { $first: "$reportingToDetails.email" },
            assigneeCount: { $sum: 1 } // Count assignees per Team Lead
          }
        },
        {
          $group: {
            _id: null,
            totalTeams: { $sum: 1 }, // Count unique teams (Team Leads)
            totalAssignees: { $sum: "$assigneeCount" }, // Total assignees across all teams
            // teams: {
            //   $push: {
            //     // teamLeadId: "$_id",
            //     teamLeadName: "$teamLeadName",
            //     teamLeadEmail: "$teamLeadEmail",
            //     assigneeCount: "$assigneeCount"
            //   }
            // }
          }
        },
        {
          $project: {
            _id: 0,
            totalTeams: 1,
            totalAssignees: 1,
            // teams: 1
          }
        }
      ])

      return analyticsCount;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      throw new Error(`Failed to retrieve analytics: ${errorMessage}`);
    }
  }


  async removeAssigneeJobAllocation(id: Types.ObjectId) {

    try {
      const jobAllocation = await this.assigneesModel.findOneAndDelete({
        _id: id,
        kind: 'JobAllocationToAssignees'
      }).exec();

      if (!jobAllocation) {
        throw new NotFoundException(`Job allocation with ID ${id} not found`);
      }

      return {
        message: 'Job allocation successfully deleted',
        id: jobAllocation._id
      };
    } catch (error) {
      if (error instanceof BadRequestException || error instanceof NotFoundException) {
        throw error;
      }
      if (error.name === 'CastError') {
        throw new BadRequestException('Invalid job allocation ID format');
      }
      this.logger.error(`Error deleting job allocation: ${error.message}`);
      throw new InternalServerErrorException('Error while deleting job allocation');
    }
  }


  async removeJobAllocation(jobAllocationId: Types.ObjectId, userId : string) {
    try {
      const jobAllocation = await this.jobAllocationModel.findById(jobAllocationId);

      // Add a null check
      if (!jobAllocation) {
        throw new NotFoundException(`Job allocation with ID ${jobAllocationId} not found`);
      }

      jobAllocation.isDeleted = true;
      await jobAllocation.save();
      if(jobAllocation.kind === "JobAllocationToAssignees") {
        const job = await this.jobsModel.findById(jobAllocation.job).populate('endClientOrg').exec();
        const allocation = await this.assigneesModel.findById(jobAllocation._id).populate('createdBy').populate('assignee').exec();
        const removedUser = await this.basicUserModel.findById(userId).exec();
        const jobTitle = job?.title || 'a job';
        const assigneeName = (allocation?.assignee?.firstName ?? '') + ' ' + (allocation?.assignee?.lastName ?? '') || 'the admin';
        const removedName = (removedUser?.firstName ?? '') + ' ' + (removedUser?.lastName ?? '') || 'the admin';
        const message_1 = `Your allocation for ${jobTitle} has been removed by ${removedName}.`;
        const message_2 = `You have removed allocation for ${jobTitle} to ${assigneeName}.`;

        // notify the user who is removed with job
        await this.notificationsService.createAndNotify(
          (jobAllocation as JobAllocationToAssignees)?.assignee?.toString() ?? '', // the user whose allocation is removed the job
          message_1,
          removedUser?._id.toString(), // the person who removed (could be admin/manager)
          {},
          NotificationType.JOB_ALLOCATION,
          allocation?.job.toString()
        );

        // notify the user who assigned the job
        await this.notificationsService.createAndNotify(
          removedUser?._id.toString() ?? '',
          message_2,
          removedUser?._id.toString(), // the person who removed (could be admin/manager)
          {},
          NotificationType.JOB_ALLOCATION,
          allocation?.job.toString()
        );
      }
      


      console.log(jobAllocation);
      // Step 2: Fetch job details
      if(jobAllocation.kind === "JobAllocationToVendors") {
        const vendorAllocation = jobAllocation as unknown as JobAllocationToVendors;
        // const vendorAllocation = jobAllocation; // No need for casting now

        const job = await this.jobsModel.findById(vendorAllocation.job);

        console.log(job);
        if (!job) {
          this.logger.warn(`Job with ID ${vendorAllocation.job} not found for job allocation ${jobAllocationId}`);
        } else {
          // Step 3: Remove vendorId from vendors array
          job.vendors = (job.vendors || []).filter(
            (vendor: any) => vendor?.toString() !== vendorAllocation.vendor?.toString()
          );
        const allocation = await this.vendorsModel.findById(jobAllocation._id).populate('createdBy').populate('vendor').exec();
        const vendorUser = await this.basicUserModel.findOne({
          org: new Types.ObjectId(vendorAllocation.vendor.toString()),
        }).exec();
        const removedUser = await this.basicUserModel.findById(userId).exec();
        const jobTitle = job?.title || 'a job';
        const vendorName = (vendorUser?.firstName ?? '') + ' ' + (vendorUser?.lastName ?? '') || 'the admin';
        const removedName = (removedUser?.firstName ?? '') + ' ' + (removedUser?.lastName ?? '') || 'the admin';
        const message_1 = `Your allocation for ${jobTitle} has been removed by ${removedName}.`;
        const message_2 = `You have removed allocation for ${jobTitle} to vendor ${vendorName}.`;

        // notify the vendor who is removed with job
        await this.notificationsService.createAndNotify(
          allocation?.vendor?.toString() ?? '', // the user whose allocation is removed the job
          message_1,
          removedUser?._id.toString(), // the person who removed (could be admin/manager)
          {},
          NotificationType.JOB_ALLOCATION,
          allocation?.job.toString()
        );

        // notify the user who assigned the job
        await this.notificationsService.createAndNotify(
          removedUser?._id.toString() ?? '',
          message_2,
          removedUser?._id.toString(), // the person who removed (could be admin/manager)
          {},
          NotificationType.JOB_ALLOCATION,
          allocation?.job.toString()
        );
          await job.save();
        }
      }

      if(jobAllocation.kind === "JobAllocationToFreelancers") {
        const freelancerAllocation = jobAllocation as unknown as JobAllocationToFreelancers;
        // const freelancerAllocation = jobAllocation; // No need for casting now

        const job = await this.jobsModel.findById(freelancerAllocation.job);

        console.log(job);
        if (!job) {
          this.logger.warn(`Job with ID ${freelancerAllocation.job} not found for job allocation ${jobAllocationId}`);
        } else {
          // Step 3: Remove freelancerId from freelancers array
          job.shareWithFreelancers = false;
          await job.save();
        }
      }
    

      return jobAllocation;
    }
    catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Error deleting job allocation: ${error.message}`);
      throw new InternalServerErrorException('Error while deleting job allocation');
    }
  }


  // Vendor methods
  async createForVendors(createDto: CreateJobAllocationToVendorsDto): Promise<JobAllocationBaseDocument> {
    try {

      const { job,vendor } = createDto;

      // Check if job is already assigned to this vendor
      const existingAllocation = await this.vendorsModel.findOne({
        job,
        vendor,
        isDeleted: false
      });
  
      if (existingAllocation) {
        throw new BadRequestException('This job has already been assigned to the vendor.');
      }

      const createdJobAllocation = new this.vendorsModel(createDto);

      const jobDetails = await this.jobsModel.findById(createdJobAllocation.job).populate('endClientOrg').exec();
      const allocation = await this.vendorsModel.findById(createdJobAllocation._id).populate('createdBy').populate('vendor').exec();
      const vendorUser = await this.basicUserModel.findOne({
        org: new Types.ObjectId(allocation?.vendor.toString()),
      }).exec();
      const jobTitle = jobDetails?.title || 'a job';
      const clientName = jobDetails?.endClientOrg?.title || 'a client';
      const createdbyName = (allocation?.createdBy?.firstName ?? '') +' '+(allocation?.createdBy?.lastName ?? '') || 'the admin';
      const vendorName = (vendorUser?.firstName ?? '') +' '+(vendorUser?.lastName ?? '') || 'the admin';

      const message_1 = `You have been allocated to work on ${jobTitle} by ${createdbyName}.`;
      const message_2 = `You have allocated ${jobTitle} to vendor ${vendorName}.`;

      // notify the user who is assigned with job
      await this.notificationsService.createAndNotify(
        createdJobAllocation.vendor.toString(), // the user receiving the job
        message_1,
        createdJobAllocation.createdBy.toString(), // the person who assigned (could be admin/manager)
        {},
        NotificationType.JOB_ALLOCATION,
        createdJobAllocation.job.toString()
      );

      // notify the user who assigned the job
      await this.notificationsService.createAndNotify(
        createdJobAllocation.createdBy.toString(), 
        message_2,
        createdJobAllocation.createdBy.toString(),
        {},
        NotificationType.JOB_ALLOCATION,
        createdJobAllocation.job.toString()
      );

      return await createdJobAllocation.save();
    } catch (error) {
      this.logger.error(`Failed to create Job Allocation for Vendors: ${error.message}`);
      if (error instanceof BadRequestException) {
        throw error; // rethrow so the client sees the correct message
      }
      if (error.name === 'ValidationError') {
        throw new BadRequestException(error.message);
      }
      throw new InternalServerErrorException('Error while creating Job Allocation.');
    }
  }

  async findAllVendorJobAllocations(query: FindAllVendorJobAllocationsDto) {
    const { vendor, page = 1, limit = 10 } = query;

    const conditions = {
      ...this.getBaseQueryConditions({
        priority: query.priority
      }),
      kind: 'JobAllocationToVendors'
    };

    if (vendor) {
      if (!Types.ObjectId.isValid(vendor)) {
        throw new BadRequestException('Invalid vendor ID format');
      }
      conditions.vendor = new Types.ObjectId(vendor);
    }

    try {
      const total = await this.vendorsModel.countDocuments(conditions);

      const jobAllocations = await this.vendorsModel.find(conditions)
        .skip((page - 1) * limit)
        .limit(limit)
        .populate(this.getPopulateOptions('JobAllocationToVendors'))
        .sort({ updatedAt: -1 })
        .exec();

      return jobAllocations;
    } catch (error) {
      this.logger.error(`Error fetching vendor job allocations: ${error.message}`);
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new InternalServerErrorException('Error while fetching job allocations');
    }
  }

  async findOneForVendors(id: Types.ObjectId): Promise<JobAllocationToVendors> {
    try {
      const jobAllocation = await this.vendorsModel.findOne({
        _id: id,
        kind: 'JobAllocationToVendors'
      })
        .populate(this.getPopulateOptions('JobAllocationToVendors'))
        .exec();

      if (!jobAllocation) {
        throw new NotFoundException(`Job allocation with ID ${id} not found`);
      }

      return jobAllocation;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Error fetching vendor job allocation: ${error.message}`);
      throw new InternalServerErrorException('Error while fetching job allocation');
    }
  }

  async updateVendorJobAllocation(id: Types.ObjectId, updateDto: UpdateJobAllocationToVendorsDto) {
    try {
      const jobAllocation = await this.vendorsModel.findOneAndUpdate(
        {
          _id: id,
          kind: 'JobAllocationToVendors'
        },
        updateDto,
        {
          new: true,
          runValidators: true
        }
      )
        .populate(this.getPopulateOptions('JobAllocationToVendors'))
        .exec();

      if (!jobAllocation) {
        throw new NotFoundException(`Job allocation with ID ${id} not found`);
      }

      return jobAllocation;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      if (error.name === 'ValidationError') {
        throw new BadRequestException(error.message);
      }
      this.logger.error(`Error updating vendor job allocation: ${error.message}`);
      throw new InternalServerErrorException('Error while updating job allocation');
    }
  }

  async removeVendorJobAllocation(id: Types.ObjectId) {
    try {
      const jobAllocation = await this.vendorsModel.findOneAndDelete({
        _id: id,
        kind: 'JobAllocationToVendors'
      }).exec();

      if (!jobAllocation) {
        throw new NotFoundException(`Job allocation with ID ${id} not found`);
      }

      return {
        message: 'Vendor job allocation successfully deleted',
        id: jobAllocation._id
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      if (error.name === 'CastError') {
        throw new BadRequestException('Invalid job allocation ID format');
      }
      this.logger.error(`Error deleting vendor job allocation: ${error.message}`);
      throw new InternalServerErrorException('Error while deleting job allocation');
    }
  }

  async findJobAllocationsForVendors(jobId: Types.ObjectId, query: FindAllAssigneeJobAllocationsDto) {
    const { page = 1, limit = 10 } = query;

    try {
      const conditions = {
        ...this.getBaseQueryConditions(query),
        kind: 'JobAllocationToVendors',
        job: jobId,
        isDeleted: { $ne: true }
      };

      const total = await this.vendorsModel.countDocuments(conditions);

      const jobAllocations = await this.vendorsModel.find(conditions)
        .skip((page - 1) * limit)
        .limit(limit)
        .populate(this.getPopulateOptions('JobAllocationToVendors'))
        .sort({ updatedAt: -1 })
        .exec();

      return jobAllocations;
    } catch (error) {
      this.logger.error(`Error fetching job allocations for vendors of job ${jobId}: ${error.message}`);
      if (error.name === 'CastError') {
        throw new BadRequestException('Invalid job ID format');
      }
      throw new InternalServerErrorException('Error while fetching job allocations');
    }
  }

  async findVendorJobAllocationsByDate(query: FindAllAssigneeJobAllocationsDto) {
    const { page = 1, limit = 10 } = query;

    try {
      const conditions = {
        ...this.getBaseQueryConditions(query),
        kind: 'JobAllocationToVendors'
      };

      const total = await this.vendorsModel.countDocuments(conditions);

      const jobAllocations = await this.vendorsModel.find(conditions)
        .skip((page - 1) * limit)
        .limit(limit)
        .populate(this.getPopulateOptions('JobAllocationToVendors'))
        .sort({ dueDate: query.dateDirection === 'prev' ? -1 : 1 })
        .exec();

      return jobAllocations;
    } catch (error) {
      this.logger.error(`Error fetching vendor job allocations by date: ${error.message}`);
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new InternalServerErrorException('Error while fetching vendor job allocations');
    }
  }

  // async findJobAllocationsOfAssigneeByDueDate(assigneeId: string, query: FilterByDueDateDto) {
  //   try {
  //     const { dueDate, page = 1, limit = 10 } = query;
  //     const givenDate = new Date(dueDate);

  //     // Set start and end of the given date in UTC
  //     const startOfDay = new Date(Date.UTC(
  //       givenDate.getUTCFullYear(),
  //       givenDate.getUTCMonth(),
  //       givenDate.getUTCDate(),
  //       0, 0, 0, 0
  //     ));

  //     const endOfDay = new Date(Date.UTC(
  //       givenDate.getUTCFullYear(),
  //       givenDate.getUTCMonth(),
  //       givenDate.getUTCDate(),
  //       23, 59, 59, 999
  //     ));

  //     // Query to find both date-based allocations and untilPositionClosed ones
  //     const jobAllocations = await this.assigneesModel.find({
  //       assignee: assigneeId,
  //       kind: 'JobAllocationToAssignees',
  //       isDeleted: false,
  //       $or: [
  //         // Case 1: Job starts and ends on the given date
  //         {
  //           startDate: { $gte: startOfDay, $lte: endOfDay },
  //           dueDate: { $gte: startOfDay, $lte: endOfDay },
  //           untilPositionClosed: false
  //         },
  //         // Case 2: Job starts before and ends after the given date
  //         {
  //           startDate: { $lte: endOfDay },
  //           dueDate: { $gte: startOfDay },
  //           untilPositionClosed: false
  //         },
  //         // Case 3: Jobs with untilPositionClosed true and start date before or on the given date
  //         {
  //           startDate: { $lte: endOfDay },
  //           untilPositionClosed: true
  //         }
  //       ]
  //     })
  //       .populate(this.getPopulateOptions('JobAllocationToAssignees'))
  //       .sort({ startDate: 1, dueDate: 1 })
  //       .skip((page - 1) * limit)
  //       .limit(limit)
  //       .exec();

  //     return jobAllocations;

  //   } catch (error) {
  //     this.logger.error(`Error fetching assignee job allocations by date: ${error.message}`);
  //     throw new InternalServerErrorException('Error while fetching job allocations');
  //   }
  // }

  async findAssigneeJobAllocationsOfByDueDate(assigneeId: string, query: FilterByDueDateDto) {
    try {
      const { dueDate, page = 1, limit = 10 ,departmentId,leadId} = query;
      const givenDate = new Date(dueDate);
      console.log(givenDate)
      // Set start and end of the given date in UTC
      const startOfDay = new Date(Date.UTC(
        givenDate.getUTCFullYear(),
        givenDate.getUTCMonth(),
        givenDate.getUTCDate(),
        0, 0, 0, 0
      ));

      const endOfDay = new Date(Date.UTC(
        givenDate.getUTCFullYear(),
        givenDate.getUTCMonth(),
        givenDate.getUTCDate(),
        23, 59, 59, 999
      ));

      const queryConditions: any = {
        assignee: assigneeId,
        kind: 'JobAllocationToAssignees',
        isDeleted: false,
        departmentId : departmentId,
        startDate: { $lte: endOfDay },
        $or: [
          { dueDate: { $gte: startOfDay, $lte: endOfDay } },
          { dueDate: { $gte: endOfDay } },
          { untilPositionClosed: true }
        ]
      };
      // Only add leadId condition if it's explicitly present
      if (leadId) {
        queryConditions.leadId = leadId;
      }

      const jobAllocations = await this.assigneesModel.find(queryConditions)
        .populate(this.getPopulateOptions('JobAllocationToAssignees'))
        .sort({ startDate: 1, dueDate: 1 })
        .skip((page - 1) * limit)
        .limit(limit)
        .exec();

      return jobAllocations;

    } catch (error) {
      this.logger.error(`Error fetching assignee job allocations by date: ${error.message}`);
      throw new InternalServerErrorException('Error while fetching job allocations');
    }
  }

  // Get all job allocations for multiple vendors
  async findMultipleVendorJobAllocations(vendorIds: string[], query: FindAllAssigneeJobAllocationsDto) {
    const { page = 1, limit = 10 } = query;

    const validIds = vendorIds.filter(id => Types.ObjectId.isValid(id));
    if (validIds.length !== vendorIds.length) {
      throw new BadRequestException('One or more invalid vendor IDs provided');
    }

    const conditions = {
      ...this.getBaseQueryConditions(query),
      kind: 'JobAllocationToVendors',
      vendor: { $in: validIds.map(id => new Types.ObjectId(id)) }
    };

    try {
      const total = await this.vendorsModel.countDocuments(conditions);

      const jobAllocations = await this.vendorsModel.find(conditions)
        .skip((page - 1) * limit)
        .limit(limit)
        .populate(this.getPopulateOptions('JobAllocationToVendors'))
        .sort({ updatedAt: -1 })
        .exec();

      return jobAllocations;
    } catch (error) {
      this.logger.error(`Error fetching multiple vendor job allocations: ${error.message}`);
      throw new InternalServerErrorException('Error while fetching job allocations');
    }
  }

  // Freelancer methods
  async createForFreelancers(createDto: CreateJobAllocationToFreelancersDto): Promise<JobAllocationBaseDocument> {
    try {
      // Validate reward amount
      if (createDto.reward && createDto.reward < 0) {
        throw new BadRequestException('Reward amount must be non-negative');
      }

      // Check for existing allocation for same job and freelancer
      const existingAllocation = await this.freelancersModel.findOne({
        job: createDto.job,
        isAvailableInFreeLancerPool: true,
        isDeleted: false// Optional: ignore deleted entries
      });

      if (existingAllocation) {
        throw new BadRequestException('Job has already been allocated to this freelancer');
      }

      const createdJobAllocation = new this.freelancersModel({
        ...createDto,
        isAvailableInFreeLancerPool: true // Always start as available
      });

      return await createdJobAllocation.save();
    } catch (error) {
      this.logger.error(`Failed to create Job Allocation for Freelancers: ${error.message}`);
      if (error instanceof BadRequestException) {
        throw error;
      }
      if (error.name === 'ValidationError') {
        throw new BadRequestException(error.message);
      }
      throw new InternalServerErrorException('Error while creating job allocation for freelancers');
    }
  }

  async findAllFreelancerJobAllocations(query: FindAllFreelancerJobAllocationsDto,user : any) {
    const {
      page = 1,
      limit = 10,
      isAvailableInFreeLancerPool,
      minReward,
      maxReward,
      dueDate
    } = query;

    const givenDate = new Date(dueDate);
    console.log(givenDate)
    // Set start and end of the given date in UTC
    const startOfDay = new Date(Date.UTC(
      givenDate.getUTCFullYear(),
      givenDate.getUTCMonth(),
      givenDate.getUTCDate(),
      0, 0, 0, 0
    ));

    const endOfDay = new Date(Date.UTC(
      givenDate.getUTCFullYear(),
      givenDate.getUTCMonth(),
      givenDate.getUTCDate(),
      23, 59, 59, 999
    ));

    const conditions: any = {
      // ...this.getBaseQueryConditions(query),
      kind: 'JobAllocationToFreelancers',
      isDeleted: false,
      startDate: { $lte: endOfDay },
      $or: [
        { dueDate: { $gte: startOfDay, $lte: endOfDay } },
        { dueDate: { $gte: endOfDay } },
        { untilPositionClosed: true }
      ]
    };

    // Filter by pool availability
    if (typeof isAvailableInFreeLancerPool === 'boolean') {
      conditions.isAvailableInFreeLancerPool = isAvailableInFreeLancerPool;
    }

    // Filter by reward range
    if (minReward !== undefined || maxReward !== undefined) {
      conditions.reward = {};
      if (minReward !== undefined) {
        if (minReward < 0) {
          throw new BadRequestException('Minimum reward must be non-negative');
        }
        conditions.reward.$gte = minReward;
      }
      if (maxReward !== undefined) {
        if (maxReward < 0) {
          throw new BadRequestException('Maximum reward must be non-negative');
        }
        if (minReward !== undefined && maxReward < minReward) {
          throw new BadRequestException('Maximum reward must be greater than minimum reward');
        }
        conditions.reward.$lte = maxReward;
      }
    }

    try {
      const total = await this.freelancersModel.countDocuments(conditions);

      const jobAllocations = await this.freelancersModel.find(conditions)
        .skip((page - 1) * limit)
        .limit(limit)
        .populate(this.getPopulateOptions('JobAllocationToFreelancers'))
        .sort({ reward: -1 })
        .exec();

      // Filter in-memory by comparing org IDs
      const filteredAllocations = jobAllocations.filter((allocation) => {
        const postingOrg = allocation.job?.postingOrg;
        return postingOrg?.toString() === user.org._id.toString();
      });

      return filteredAllocations;
    } catch (error) {
      this.logger.error(`Error fetching freelancer job allocations: ${error.message}`);
      if (error instanceof BadRequestException) {
        throw error;
      }
      if (error.name === 'CastError') {
        throw new BadRequestException('Invalid ID format provided');
      }
      throw new InternalServerErrorException('Error while fetching freelancer job allocations');
    }
  }

  async findOneForFreelancers(id: Types.ObjectId): Promise<JobAllocationToFreelancers> {
    try {
      const jobAllocation = await this.freelancersModel.findOne({
        _id: id,
        kind: 'JobAllocationToFreelancers'
      })
        .populate(this.getPopulateOptions('JobAllocationToFreelancers'))
        .exec();

      if (!jobAllocation) {
        throw new NotFoundException(`Job allocation with ID ${id} not found`);
      }

      return jobAllocation;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Error fetching freelancer job allocation: ${error.message}`);
      if (error.name === 'CastError') {
        throw new BadRequestException('Invalid job allocation ID format');
      }
      throw new InternalServerErrorException('Error while fetching freelancer job allocation');
    }
  }

  async updateFreelancerJobAllocation(id: Types.ObjectId, updateDto: UpdateJobAllocationToFreelancersDto) {
    try {
      // Validate reward if provided
      if (updateDto.reward !== undefined && updateDto.reward < 0) {
        throw new BadRequestException('Reward amount must be non-negative');
      }

      const jobAllocation = await this.freelancersModel.findOneAndUpdate(
        {
          _id: id,
          kind: 'JobAllocationToFreelancers'
        },
        updateDto,
        {
          new: true,
          runValidators: true
        }
      )
        .populate(this.getPopulateOptions('JobAllocationToFreelancers'))
        .exec();

      if (!jobAllocation) {
        throw new NotFoundException(`Job allocation with ID ${id} not found`);
      }

      return jobAllocation;
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error;
      }
      if (error.name === 'ValidationError') {
        throw new BadRequestException(error.message);
      }
      this.logger.error(`Error updating freelancer job allocation: ${error.message}`);
      throw new InternalServerErrorException('Error while updating freelancer job allocation');
    }
  }

  async removeFreelancerJobAllocation(id: Types.ObjectId) {
    try {
      const jobAllocation = await this.freelancersModel.findOneAndDelete({
        _id: id,
        kind: 'JobAllocationToFreelancers'
      }).exec();

      if (!jobAllocation) {
        throw new NotFoundException(`Job allocation with ID ${id} not found`);
      }

      await this.jobsModel.findByIdAndUpdate(
        jobAllocation.job, // the job ID
        { $set: { shareWithFreelancers: false } },
        { new: true } // optional: returns the updated document
      );

      return {
        message: 'Freelancer job allocation successfully deleted',
        id: jobAllocation._id
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      if (error.name === 'CastError') {
        throw new BadRequestException('Invalid job allocation ID format');
      }
      this.logger.error(`Error deleting freelancer job allocation: ${error.message}`);
      throw new InternalServerErrorException('Error while deleting freelancer job allocation');
    }
  }

  async findJobAllocationsForFreelancers(jobId: Types.ObjectId) {
    try {
      const jobAllocations = await this.freelancersModel.find({
        job: jobId,
        kind: 'JobAllocationToFreelancers',
        isAvailableInFreeLancerPool: true
      })
        .populate(this.getPopulateOptions('JobAllocationToFreelancers'))
        .sort({ reward: -1 })
        .exec();

      return jobAllocations;
    } catch (error) {
      this.logger.error(`Error fetching freelancer job allocations for job: ${error.message}`);
      if (error.name === 'CastError') {
        throw new BadRequestException('Invalid job ID format');
      }
      throw new InternalServerErrorException('Error while fetching freelancer job allocations');
    }
  }

  async findVendorJobAllocationsOfByDueDate(vendorId: string, query: FilterByDueDateDto) {
    try {
      const { dueDate, page = 1, limit = 10} = query;
      const givenDate = new Date(dueDate);
      console.log(givenDate)
      // Set start and end of the given date in UTC
      const startOfDay = new Date(Date.UTC(
        givenDate.getUTCFullYear(),
        givenDate.getUTCMonth(),
        givenDate.getUTCDate(),
        0, 0, 0, 0
      ));

      const endOfDay = new Date(Date.UTC(
        givenDate.getUTCFullYear(),
        givenDate.getUTCMonth(),
        givenDate.getUTCDate(),
        23, 59, 59, 999
      ));

      const queryConditions: any = {
        vendor: vendorId,
        kind: 'JobAllocationToVendors',
        isDeleted: false,
        startDate: { $lte: endOfDay },
        $or: [
          { dueDate: { $gte: startOfDay, $lte: endOfDay } },
          { dueDate: { $gte: endOfDay } },
          { untilPositionClosed: true }
        ]
      };

      const jobAllocations = await this.vendorsModel.find(queryConditions)
        .populate(this.getPopulateOptions('JobAllocationToVendors'))
        .sort({ startDate: 1, dueDate: 1 })
        .skip((page - 1) * limit)
        .limit(limit)
        .exec();

      return jobAllocations;

    } catch (error) {
      this.logger.error(`Error fetching assignee job allocations by date: ${error.message}`);
      throw new InternalServerErrorException('Error while fetching job allocations');
    }
  }

  async create(dto: CreateAIRecruiterQADto) {
    const cleanedSkillQAPairs: Record<string, { question: string; answer?: string }[]> = {};
  
    for (const [skill, qaArray] of Object.entries(dto.skillQAPairs)) {
      const cleanedQAs = qaArray
        .map((qa) => {
          const question = qa.question?.trim();
          if (!question) return null; // Skip if question is empty, null, or whitespace
  
          const cleanQA: { question: string; answer?: string } = { question };
          if (qa.answer?.trim()) {
            cleanQA.answer = qa.answer.trim();
          }
          return cleanQA;
        })
        .filter((qa): qa is { question: string; answer?: string } => qa !== null); // Only valid QAs
  
      // Always include the skill in the object, even if it's an empty array
      cleanedSkillQAPairs[skill] = cleanedQAs;
    }
  
    return this.AIRecruiterQAModel.create({
      jobId: dto.jobId?.toString(),
      skillQAPairs: cleanedSkillQAPairs,
      createdBy: dto.createdBy?.toString(),
    });
  }
  
  
  
  
  
  async getByJobId(jobId: string) {
    const qa = await this.AIRecruiterQAModel.findOne({ jobId: jobId }).exec();
    if (!qa) throw new NotFoundException('No QA found for this job');
    return qa;
  }

  emitEvent(eventName: string, payload: any) {
    // payload['event']= eventName;
    // this.logger.debug(payload)
    this.eventEmitter.emit(eventName, payload);
  }

  async createForAiRecruiter(createDto: CreateJobAllocationToAiRecruiterDto): Promise<JobAllocationBaseDocument> {
    try {

      // Check for existing allocation for same job and freelancer
      const existingAllocation = await this.aiRecruiterModel.findOne({
        job: createDto.job,
        isAvailableInAiRecruiterPool: true,
        isDeleted: false// Optional: ignore deleted entries
      });

      if (existingAllocation) {
        throw new BadRequestException('Job has already been allocated to this AI recruiter');
      }

      const createdJobAllocation = new this.aiRecruiterModel({
        ...createDto,
        isAvailableInAiRecruiterPool: true // Always start as available
      });

      return await createdJobAllocation.save();
    } catch (error) {
      this.logger.error(`Failed to create Job Allocation for Freelancers: ${error.message}`);
      if (error instanceof BadRequestException) {
        throw error;
      }
      if (error.name === 'ValidationError') {
        throw new BadRequestException(error.message);
      }
      throw new InternalServerErrorException('Error while creating job allocation for AI recruiter');
    }
  }

  async findAllAiRecruiterJobAllocations(query: FindAllAiRecruiterJobAllocationsDto,user : any) {
    const {
      page = 1,
      limit = 10,
      isAvailableInAiRecruiterPool,
      dueDate
    } = query;
    const givenDate = new Date(dueDate);
    console.log(givenDate)
    // Set start and end of the given date in UTC
    const startOfDay = new Date(Date.UTC(
      givenDate.getUTCFullYear(),
      givenDate.getUTCMonth(),
      givenDate.getUTCDate(),
      0, 0, 0, 0
    ));

    const endOfDay = new Date(Date.UTC(
      givenDate.getUTCFullYear(),
      givenDate.getUTCMonth(),
      givenDate.getUTCDate(),
      23, 59, 59, 999
    ));
    const conditions: any = {
      // ...this.getBaseQueryConditions(query),
      kind: 'JobAllocationToAiRecruiters',
      isDeleted: false,
      startDate: { $lte: endOfDay },
      $or: [
        { dueDate: { $gte: startOfDay, $lte: endOfDay } },
        { dueDate: { $gte: endOfDay } },
        { untilPositionClosed: true }
      ]
    };

    // Filter by pool availability
    if (typeof isAvailableInAiRecruiterPool === 'boolean') {
      conditions.isAvailableInAiRecruiterPool = isAvailableInAiRecruiterPool;
    }

    try {
      const total = await this.aiRecruiterModel.countDocuments(conditions);

      const jobAllocations = await this.aiRecruiterModel.find(conditions)
        .skip((page - 1) * limit)
        .limit(limit)
        .populate(this.getPopulateOptions('JobAllocationToAiRecruiters'))
        .sort({ reward: -1 })
        .exec();

        const filteredAllocations = jobAllocations.filter((allocation) => {
          const postingOrg = allocation.job?.postingOrg;
          return postingOrg?.toString() === user?.org._id.toString();
        });

      return filteredAllocations;
    } catch (error) {
      this.logger.error(`Error fetching freelancer job allocations: ${error.message}`);
      if (error instanceof BadRequestException) {
        throw error;
      }
      if (error.name === 'CastError') {
        throw new BadRequestException('Invalid ID format provided');
      }
      throw new InternalServerErrorException('Error while fetching freelancer job allocations');
    }
  }

  async findOneForAiRecruiter(id: Types.ObjectId): Promise<JobAllocationToAiRecruiters> {
    try {
      const jobAllocation = await this.aiRecruiterModel.findOne({
        _id: id,
        kind: 'JobAllocationToAiRecruiters'
      })
        .populate(this.getPopulateOptions('JobAllocationToAiRecruiters'))
        .exec();

      if (!jobAllocation) {
        throw new NotFoundException(`Job allocation with ID ${id} not found`);
      }

      return jobAllocation;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Error fetching Ai recruiter job allocation: ${error.message}`);
      if (error.name === 'CastError') {
        throw new BadRequestException('Invalid job allocation ID format');
      }
      throw new InternalServerErrorException('Error while fetching Ai recruiter job allocation');
    }
  }

  async updateAiRecruiterJobAllocation(id: Types.ObjectId, updateDto: UpdateJobAllocationToAiRecruiterDto) {
    try {

      const jobAllocation = await this.aiRecruiterModel.findOneAndUpdate(
        {
          _id: id,
          kind: 'JobAllocationToAiRecruiters'
        },
        updateDto,
        {
          new: true,
          runValidators: true
        }
      )
        .populate(this.getPopulateOptions('JobAllocationToAiRecruiters'))
        .exec();

      if (!jobAllocation) {
        throw new NotFoundException(`Job allocation with ID ${id} not found`);
      }

      return jobAllocation;
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error;
      }
      if (error.name === 'ValidationError') {
        throw new BadRequestException(error.message);
      }
      this.logger.error(`Error updating Ai recruiter job allocation: ${error.message}`);
      throw new InternalServerErrorException('Error while updating Ai recruiter job allocation');
    }
  }

  async removeAiRecruiterJobAllocation(id: Types.ObjectId) {
    try {
      const jobAllocation = await this.aiRecruiterModel.findOneAndDelete({
        _id: id,
        kind: 'JobAllocationToAiRecruiters'
      }).exec();

      if (!jobAllocation) {
        throw new NotFoundException(`Job allocation with ID ${id} not found`);
      }

      return {
        message: 'Ai recruiter job allocation successfully deleted',
        id: jobAllocation._id
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      if (error.name === 'CastError') {
        throw new BadRequestException('Invalid job allocation ID format');
      }
      this.logger.error(`Error deleting Ai recruiter job allocation: ${error.message}`);
      throw new InternalServerErrorException('Error while deleting Ai recruiter job allocation');
    }
  }

  async findJobAllocationsForAiRecruiter(jobId: Types.ObjectId) {
    try {
      const jobAllocations = await this.freelancersModel.find({
        job: jobId,
        kind: 'JobAllocationToAiRecruiters',
        isAvailableInAiRecruiterPool: true
      })
        .populate(this.getPopulateOptions('JobAllocationToAiRecruiters'))
        .sort({ reward: -1 })
        .exec();

      return jobAllocations;
    } catch (error) {
      this.logger.error(`Error fetching Ai recruiter job allocations for job: ${error.message}`);
      if (error.name === 'CastError') {
        throw new BadRequestException('Invalid job ID format');
      }
      throw new InternalServerErrorException('Error while fetching Ai recruiter job allocations');
    }
  }

}
