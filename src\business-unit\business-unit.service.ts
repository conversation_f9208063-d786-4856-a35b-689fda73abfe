import {
  BadRequestException,
  ConflictException,
  HttpException,
  HttpStatus,
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
  ServiceUnavailableException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectModel } from '@nestjs/mongoose';
import { Document, Model, Types } from 'mongoose';
import { validateObjectId } from 'src/utils/validation.utils';
import { BusinessUnit, BusinessUnitDocument } from './schemas/business-unit.schema';
import { CreateBusinessUnitDto } from './dto/create-business-unit.dto';
import { UpdateBusinessUnitDto } from './dto/update-business-unit.dto';
import { FilterBusinessUnitsDto } from './dto/filter-business-unit.dto';
import { BusinessUnitType, OrgType } from 'src/shared/constants';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { BasicUser, BasicUserDocument } from 'src/user/schemas/basic-user.schema';
import { FilterUsersDto } from './dto/filter-users.dto';
import { Org } from 'src/org/schemas/org.schema';
import { Role } from 'src/auth/enums/role.enum';

@Injectable()
export class BusinessUnitService {
  private readonly logger = new Logger(BusinessUnitService.name);

  constructor(
    private configService: ConfigService,
    private eventEmitter: EventEmitter2,
    @InjectModel(BusinessUnit.name) private businessUnitModel: Model<BusinessUnit>,
    @InjectModel(BasicUser.name) private basicUserModel: Model<BasicUserDocument>,
    @InjectModel(Org.name) private orgsModel: Model<Org>,
  ) { }

  async create(createBusinessUnitDto: CreateBusinessUnitDto, user: any) {
    createBusinessUnitDto.createdBy = user._id;

    if (!createBusinessUnitDto.parentBusinessUnit) {
      const existingDepartment = await this.businessUnitModel.findOne({
        label: createBusinessUnitDto.label,
        type: createBusinessUnitDto.type,
        parentBusinessUnit: undefined,
        isDeleted: false,
        org: user.org?._id?? undefined
      });

      if (existingDepartment) {
        throw new ConflictException('A root-level department with the same name and type already exists.');
      }
    }

    let breadcrumb = createBusinessUnitDto.label;

    if (createBusinessUnitDto.parentBusinessUnit) {
      const parentBusinessUnit = await this.businessUnitModel.findById(createBusinessUnitDto.parentBusinessUnit);

      if (!parentBusinessUnit) {
        throw new NotFoundException('Parent business unit not found.');
      }

      breadcrumb = `${parentBusinessUnit.breadcrumb} -> ${createBusinessUnitDto.label}`;
    }

    const createBusinessUnit = new this.businessUnitModel({
      ...createBusinessUnitDto,
      breadcrumb
    });

    try {

      const createdBusinessUnit = await createBusinessUnit.save();

      if (createBusinessUnitDto.parentBusinessUnit) {
        const objId = validateObjectId(createBusinessUnitDto.parentBusinessUnit);
        await this.businessUnitModel.findByIdAndUpdate(
          objId,
          { $push: { children: createdBusinessUnit._id } },
          { new: true }
        );
      }

      if(createBusinessUnitDto.departmentHead) {
        const user = await this.basicUserModel.findById(createBusinessUnitDto.departmentHead).exec();
        if (!user) {
          throw new NotFoundException('Department head not found.');
        }
        // Ensure it's initialized as an array
        if (!Array.isArray(user.businessUnit)) {
          user.businessUnit = [];
        }
        // Convert both sides to string (important for ObjectId comparisons)
        const existingUnits = user.businessUnit.map(unit => unit.toString());
        const newUnit = createdBusinessUnit._id.toString();
        if (!existingUnits.includes(newUnit)) {
          user.businessUnit.push(newUnit);
        }
        await user.save();
      }

      this.emitEvent('business-unit.created', { createdBusinessUnit, user });
      this.emitEvent('business-unit.tracker', { createdBusinessUnit, user });//clone org tracker setting for created department

      return createdBusinessUnit;
    } catch (error) {
      throw new InternalServerErrorException(
        `Error while creating businessUnit: ${error?.message}`,
      );
    }
  }


  async populateChildren(businessUnitId: any) {
    const populatedDept = await this.businessUnitModel
      .findById(businessUnitId)
      .populate({
        path: 'children',
        select: '_id name label key isDeleted',
        model: 'BusinessUnit',
      })
      .exec();

    if (!populatedDept) {
      return null;
    }

    populatedDept.children = populatedDept.children || [];
    populatedDept.children = populatedDept.children.filter(child => {
      const businessUnitChild = child as BusinessUnit;
      return !businessUnitChild.isDeleted;
    });


    for (let i = 0; i < populatedDept.children.length; i++) {
      const child = populatedDept.children[i];


      const populatedChild = await this.populateChildren(child);

      if (populatedChild) {
        // Replace the child with its populated version in the children array
        populatedDept.children[i] = populatedChild;
      }
    }

    return populatedDept;
  };

  async getCompleteDepartmentTree(userId: string,orgId: String, type?: string, deptName?: string,isJobAllocation? : boolean) {
    const query: any = {
      key: { $regex: /^[0-9]+$/ },
      isDeleted: false,
      org: orgId,
    };

    if (deptName) {
      query.label = new RegExp(deptName, 'i'); // Case-insensitive search
    }
    if (type) {
      query.type = type;
    } else {
      query.type = { $ne: BusinessUnitType.RECRUITMENT };
    }

    // Fetch root departments
    const rootDepartments = await this.businessUnitModel.find(query).exec();
    const departmentTree: (BusinessUnit | null)[] = [];

    // console.log(rootDepartments);

    // Collect Business Unit IDs (root + children)
    const businessUnitIdsSet = new Set<string>();

    const collectBusinessUnits = async (departmentId: string) => {
      if (!departmentId) return;

      // Fetch department details
      const department = await this.businessUnitModel.findById(departmentId).exec();
      if (!department) return;

      businessUnitIdsSet.add(departmentId); // Store ID

      // Populate children
      const children = await this.businessUnitModel.find({ parentBusinessUnit: departmentId, isDeleted: false }).exec();
      const populatedDepartment = {
        ...department.toObject(),
        children: [] as BusinessUnit[],
      };

      // Recursively add child departments
      for (const child of children) {
        const childTree = await collectBusinessUnits(child._id.toString());
        if (childTree) {

          populatedDepartment.children.push(childTree);
        }
      }

      return populatedDepartment;
    };


    for (const department of rootDepartments) {
      const populatedDepartment = await this.populateChildren(department._id);
      departmentTree.push(populatedDepartment);

      // Recursively collect all business unit IDs
      await collectBusinessUnits(department._id.toString());
    }

    const businessUnitIds = Array.from(businessUnitIdsSet); // Convert Set to Array
    // this.logger.log(JSON.stringify(businessUnitIds));

    // Aggregate user count for each department
    const userCounts = await this.basicUserModel.aggregate([
      {
        $match: {
          businessUnit: { $in: businessUnitIds },
          isDeleted: false
        }
      },
      {
        $group: {
          _id: "$businessUnit",
          count: { $sum: 1 }
        }
      }
    ]).exec();

    // Convert aggregation result into a dictionary for quick lookup
    const userCountMap = userCounts.reduce((acc, item) => {
      acc[item._id.toString()] = item.count;
      return acc;
    }, {} as Record<string, number>);

    // this.logger.log(JSON.stringify(userCountMap));


    const cleanObject = (doc: any,userBusinessUnits: string[]): any => {
      if (!doc) return null;

      // Get own user count
      const userCount = userCountMap[doc._id.toString()] || 0;

      // Process children recursively
      // const children = doc.children ? doc.children.map(cleanObject) : [];
      const children = doc.children ? doc.children.map((child: any) => cleanObject(child, userBusinessUnits)) : [];

      // Compute total user count (only if children exist)
      const totalUserCountIncludingChildren = children.length > 0
        ? userCount + children.reduce((sum: any, child: { totalUserCount: any; userCount: any; }) => sum + (child.totalUserCount || child.userCount), 0)
        : undefined; // Exclude if no children

      const childrenCount = children.length;
      const hasAccess = userBusinessUnits.includes(doc._id.toString());


      return {
        _id: doc._id.toString(),
        label: doc.label,
        key: doc.key,
        org: doc.org.toString(),
        isDeleted: doc.isDeleted,
        createdBy: doc.createdBy.toString(),
        level: doc.level,
        type: doc.type,
        breadcrumb: doc.breadcrumb,
        createdAt: doc.createdAt,
        updatedAt: doc.updatedAt,
        departmentHead: doc.departmentHead ? doc.departmentHead.toString() : null,
        isDemand: doc.isDemand,
        isSupply: doc.isSupply,
        userCount,       // Direct users in this department
        ...(totalUserCountIncludingChildren !== undefined && { totalUserCountIncludingChildren }), // Include only if children exist
        childrenCount,   // Number of children
        children,
        hasAccess, // <-- Indicates if user has access to this BU
      };
    };

    const user = await this.basicUserModel.findById(userId).select('businessUnit roles').exec();
    // console.log(user)
    const userBusinessUnits = user?.businessUnit?.map(id => id.toString()) || [];
    // const departmentTreeWithCounts = departmentTree.map(cleanObject);
    const departmentTreeWithCounts = departmentTree.map(doc =>
      cleanObject(doc, userBusinessUnits)
    );

     const managerRoles = [Role.AccountManager, Role.ResourceManager, Role.DeliveryManager];
    //  console.log('managerRoles', managerRoles)
     const hasManagerRole = user?.roles?.some(role => managerRoles.includes(role));
    //  console.log('hasManagerRole', hasManagerRole)
     function extractAccessibleDepartments(department: any): any[] {
      // Recursively get accessible children
      const accessibleChildren = (department.children || [])
        .flatMap(extractAccessibleDepartments);
    
      if (department.hasAccess) {
        return [{
          ...department,
          children: accessibleChildren
        }];
      } else {
        // Promote only the accessible children to the top
        return accessibleChildren;
      }
    }
    // console.log(hasManagerRole)
    if (!hasManagerRole && isJobAllocation) {
      return departmentTreeWithCounts.flatMap(extractAccessibleDepartments);
    }
    // console.log(departmentTreeWithCounts)
    // const departmentTreeWithCounts = departmentTree.map(attachUserCounts);

    return departmentTreeWithCounts;
  };


  // async getCompleteDepartmentTree(orgId: String, type?: string,deptName?: string) {
  //   // const objectIdOrgId = typeof orgId === 'string' ? new Types.ObjectId(orgId) : orgId;

  //   const query: any = {
  //     key: { $regex: /^[0-9]+$/ },
  //     isDeleted: false,
  //     org: orgId,
  //   };

  //   if (deptName) {
  //     const regex = new RegExp(deptName, 'i'); // 'i' for case-insensitive search
  //     query.label = regex;
  //   }
  //   if (type) {
  //     query.type = type;
  //   } else {
  //     query.type = { $ne: BusinessUnitType.RECRUITMENT };
  //   }
  //   const rootDepartments = await this.businessUnitModel.find(query).exec();
  //   const departmentTree: (BusinessUnit | null)[] = [];

  //   console.log(rootDepartments)
  //   for (const department of rootDepartments) {
  //     const populatedDepartment = await this.populateChildren(department._id);
  //     departmentTree.push(populatedDepartment);
  //   }
  //   console.log(departmentTree)
  //   // After departmentTree is created, count users
  //   const businessUnitIds = rootDepartments.map(dept => (dept as any)?._id.toString()).filter(Boolean);

  //   this.logger.log(JSON.stringify(businessUnitIds))

  //   // Aggregate user count for each department
  //   const userCounts = await this.basicUserModel.aggregate([
  //     {
  //       $match: {
  //         businessUnit: { $in: businessUnitIds },
  //         isDeleted: false
  //       }
  //     },
  //     {
  //       $group: {
  //         _id: "$businessUnit",
  //         count: { $sum: 1 }
  //       }
  //     }
  //   ]).exec();

  //   // Convert aggregation result into a dictionary for quick lookup
  //   const userCountMap = userCounts.reduce((acc, item) => {
  //     acc[item._id.toString()] = item.count;
  //     return acc;
  //   }, {} as Record<string, number>);

  //   this.logger.log(JSON.stringify(userCountMap))

  //   // Attach user count to each department in departmentTree
  //   const departmentTreeWithCounts = departmentTree.map(dept => ({
  //     ...(dept as any).toObject(),
  //     userCount: userCountMap[(dept as any)?._id.toString()] || 0
  //   }));


  //   return departmentTreeWithCounts;

  // };

  async getAllDepartmentIds(businessUnitId: Types.ObjectId) {

    try {
      // Fetch the department by its ID
      const ids: string[] = [];
      const department = await this.businessUnitModel.findById(businessUnitId).exec();

      // If department exists, add its ID as string to the result array
      if (department && !department.isDeleted) {
        ids.push(department._id.toString()); // Convert ObjectId to string

        // Recursively fetch child departments if they exist
        if (department.children && department.children.length > 0) {
          for (const child of department.children) {
            // Recursively get child department ids
            if (child instanceof Types.ObjectId) {
              const childIds = await this.getAllDepartmentIds(child);
              ids.push(...childIds);
            }
          }
        }
      }
      return ids;
    } catch (error) {
      throw new InternalServerErrorException(
        `Error while fetching department IDs: ${error?.message}`
      );
    }

  }


  async getDepartmentIdsByKeyValues(orgId: string,keys: string[]): Promise<string[]> {
    try {
      const departments = await this.businessUnitModel.find({
        key: { $in: keys },
        isDeleted: false,
        org: orgId,
        type : "department"
      }, { _id: 1 }).exec();
  
      return departments.map(dept => dept._id.toString());
    } catch (error) {
      throw new InternalServerErrorException(
        `Error while fetching department IDs by keys: ${error?.message}`
      );
    }
  }

  async getDepartmentsByIds(departmentIds: string[]): Promise<BusinessUnit[]> {
    const objectIds = departmentIds.map(id => new Types.ObjectId(id));

    return this.businessUnitModel.find({
      _id: { $in: objectIds },
    }).exec();
  }

  // async getMembersWithRoles(businessUnitId: Types.ObjectId) {
  //   try {
  //     // Get the business unit and all its children
  //     const businessUnits = await this.getAllChildBusinessUnits(businessUnitId);
  //     const businessUnitIds = [businessUnitId, ...businessUnits.map(bu => bu._id)];

  //     // Get all users from these business units
  //     const users = await this.basicUserModel
  //       .find({
  //         businessUnit: { $in: businessUnitIds },
  //         isDeleted: false
  //       })
  //       .populate({
  //         path: 'businessUnit',
  //         select: '_id label type breadcrumb'
  //       })
  //       .populate({
  //         path: 'reportingTo',
  //         select: '_id firstName firstName lastName roles'
  //       })
  //       .lean()
  //       .exec();

  //     // Group users by role
  //     const membersByRole = users.reduce((acc, user) => {
  //       const roles = user.roles || [];
  //       roles.forEach(role => {
  //         if (!acc[role]) {
  //           acc[role] = [];
  //         }
  //         acc[role].push({
  //           _id: user._id,
  //           firstName: user.firstName,
  //           email: user.email,
  //           businessUnit: user.businessUnit,
  //           reportingTo: user.reportingTo
  //         });
  //       });
  //       return acc;
  //     }, {});

  //     return {
  //       totalMembers: users.length,
  //       membersByRole
  //     };
  //   } catch (error) {
  //     throw new InternalServerErrorException(
  //       `Error getting members with roles: ${error.message}`
  //     );
  //   }
  // }

  private async getAllChildBusinessUnits(parentId: Types.ObjectId): Promise<BusinessUnit[]> {
    try {
      const children = await this.businessUnitModel
        .find({
          parentBusinessUnit: parentId,
          isDeleted: false
        })
        .exec();

      const descendents = await Promise.all(
        children.map(child => this.getAllChildBusinessUnits(child._id))
      );

      return [...children, ...descendents.flat()];
    } catch (error) {
      throw new InternalServerErrorException(
        `Error getting child business units: ${error.message}`
      );
    }
  }


  async moveBusinessUnitToRoot(movingId: string, orgId: string, user: any): Promise<BusinessUnit> {
    try {
      // Validate Object IDs
      const movingObjId = validateObjectId(movingId);

      // Find the moving business unit
      const movingBusinessUnit = await this.businessUnitModel.findById(movingObjId);
      if (!movingBusinessUnit) {
        throw new NotFoundException('Moving business unit not found.');
      }

      if (movingBusinessUnit.level === 1) {
        throw new BadRequestException('This business unit is already at the root level.');
      }

      // Remove from current parent's children if exists
      if (movingBusinessUnit.parentBusinessUnit) {
        await this.businessUnitModel.findByIdAndUpdate(
          movingBusinessUnit.parentBusinessUnit,
          { $pull: { children: movingObjId } }
        );
      }

      // Set parentBusinessUnit to null for root level
      movingBusinessUnit.parentBusinessUnit = undefined;

      // Get current root-level children count
      const rootChildrenCount = await this.businessUnitModel.find({ parentBusinessUnit: undefined, org: orgId });
      // console.log('rootChildrenCount', rootChildrenCount);

      // Set the key for the moving business unit
      movingBusinessUnit.key = `${rootChildrenCount.length}`;
      movingBusinessUnit.level = 1;
      movingBusinessUnit.breadcrumb = movingBusinessUnit.label;

      // Save the moving business unit
      await movingBusinessUnit.save();

      // Update levels of the moving business unit and its children
      await this.updateChildren(movingBusinessUnit);

      this.emitEvent('business-unit.moved.root', { movingBusinessUnit, user });
      return movingBusinessUnit;
    } catch (error) {
      throw new BadRequestException(`${error?.message}`);
    }
  }


  async moveBusinessUnitToDestination(movingId: string, destinationId: string, user: any): Promise<BusinessUnit> {
    try {
      // Validate Object IDs
      const movingObjId = validateObjectId(movingId);
      const destinationObjId = validateObjectId(destinationId);


      // Find the moving business unit
      const movingBusinessUnit = await this.businessUnitModel.findById(movingObjId);
      if (!movingBusinessUnit) {
        throw new NotFoundException('Moving business unit not found.');
      }

      // Find the destination business unit
      const destinationBusinessUnit = await this.businessUnitModel.findById(destinationObjId);
      // const destinationBusinessUnit = await this.businessUnitModel.findOne({key: destinationKey});
      if (!destinationBusinessUnit) {
        throw new NotFoundException('Destination business unit not found.');
      }

      // Find the current parent and remove the moving business unit from its children
      if (movingBusinessUnit.parentBusinessUnit) {
        await this.businessUnitModel.findByIdAndUpdate(
          movingBusinessUnit.parentBusinessUnit,
          { $pull: { children: movingObjId } },
        );
      }

      // const destinationObjId = destinationBusinessUnit._id;

      // Update the parent ID of the moving business unit
      movingBusinessUnit.parentBusinessUnit = destinationObjId.toString();

      // Update the destination's children to include the moving business unit
      destinationBusinessUnit.children = destinationBusinessUnit.children ?? [];
      destinationBusinessUnit.children.push(movingObjId);
      await destinationBusinessUnit.save();

      // Calculate the new key for the moving business unit
      const newKey = this.calculateNewKey(destinationBusinessUnit.key, destinationBusinessUnit.children.length - 1);
      movingBusinessUnit.key = newKey;
      movingBusinessUnit.level = destinationBusinessUnit.level + 1;
      movingBusinessUnit.type = destinationBusinessUnit.type;
      movingBusinessUnit.breadcrumb = `${destinationBusinessUnit.breadcrumb} -> ${movingBusinessUnit.label}`;

      await movingBusinessUnit.save();

      // Update levels and key of the moving business unit children
      await this.updateChildren(movingBusinessUnit);

      this.emitEvent('business-unit.moved.destination', { movingBusinessUnit, destinationBusinessUnit, user });
      return movingBusinessUnit;
    } catch (error) {
      throw new InternalServerErrorException(`Error while moving business unit: ${error?.message}`);
    }
  }

  async mergeBusinessUnit(movingId: string, destinationId: string,departmentHead: string,user: any): Promise<BusinessUnit> {
    try {
      // Validate Object IDs
      const movingObjId = validateObjectId(movingId);
      const destinationObjId = validateObjectId(destinationId);

      // Find the moving business unit
      const movingBusinessUnit = await this.businessUnitModel.findById(movingObjId);
      if (!movingBusinessUnit) {
        throw new NotFoundException('Moving business unit not found.');
      }

      // Find the destination business unit
      // const destinationBusinessUnit = await this.businessUnitModel.findOne({key: destinationKey});
      const destinationBusinessUnit = await this.businessUnitModel.findById(destinationObjId);
      if (!destinationBusinessUnit) {
        throw new NotFoundException('Destination business unit not found.');
      }

      // const destinationObjId = destinationBusinessUnit._id;

      // Transfer all children of the moving business unit to the destination business unit
      const children = await this.businessUnitModel.find({ parentBusinessUnit: movingId });
      for (const child of children) {
        // Update each child's parent to the destination business unit
        child.parentBusinessUnit = destinationObjId.toString();
        child.level = destinationBusinessUnit.level + 1;
        child.type = destinationBusinessUnit.type;
        child.breadcrumb = `${destinationBusinessUnit.breadcrumb} -> ${child.label}`;

        destinationBusinessUnit.children = destinationBusinessUnit.children ?? [];
        // Update the child's key based on the destination
        const newChildKey = `${destinationBusinessUnit.key}-${destinationBusinessUnit.children.length}`;
        child.key = newChildKey;

        // Add child to destination's children array
        destinationBusinessUnit.children.push(child._id);
        destinationBusinessUnit.departmentHead = departmentHead.toString();
      
        // Save updated child
        await child.save();

        await this.updateChildren(child);
      }

      // Update and save the destination business unit
      await destinationBusinessUnit.save();

      // First find affected users
      const affectedUserIds = await this.basicUserModel.find(
        { businessUnit: movingObjId.toString() },
        { _id: 1 }
      ).lean();

      // Extract user IDs
      const userIds = affectedUserIds.map(u => u._id);
      console.log('userIds', userIds);

      // Now update only affected users
      if (userIds.length > 0) {
        // 1️⃣ Pull moving BU
        await this.basicUserModel.updateMany(
          { _id: { $in: userIds } },
          { $pull: { businessUnit: movingObjId.toString() } }
        );

        // 2️⃣ Add destination BU
        await this.basicUserModel.updateMany(
          { _id: { $in: userIds } },
          { $addToSet: { businessUnit: destinationObjId.toString() } }
        );
      }
      
      // Soft-delete the moving business unit (mark it as deleted, or use an isDeleted flag)
      movingBusinessUnit.isDeleted = true;
      await movingBusinessUnit.save();

      this.emitEvent('business-unit.merged.destination', { movingBusinessUnit, destinationBusinessUnit, user });

      return movingBusinessUnit;
    } catch (error) {
      throw new InternalServerErrorException(`Error while merging business units: ${error?.message}`);
    }
  }

  async getDepartmentsHeads(movingId: string, destinationId: string,user: any){
    try {
      // Validate Object IDs
      const movingObjId = validateObjectId(movingId);
      const destinationObjId = validateObjectId(destinationId);

      // Find the moving business unit
      const movingBusinessUnit = await this.businessUnitModel.findById(movingObjId).populate({
        path: 'departmentHead', // Assuming departmentHead is a reference field
        select: 'firstName lastName _id', // Select only firstName, lastName, and _id fields
      });
      if (!movingBusinessUnit) {
        throw new NotFoundException('Moving business unit not found.');
      }

      // Find the destination business unit
      // const destinationBusinessUnit = await this.businessUnitModel.findOne({key: destinationKey});
      const destinationBusinessUnit = await this.businessUnitModel.findById(destinationObjId).populate({
        path: 'departmentHead', // Assuming departmentHead is a reference field
        select: 'firstName lastName _id', // Select only firstName, lastName, and _id fields
      });
      if (!destinationBusinessUnit) {
        throw new NotFoundException('Destination business unit not found.');
      }

      // Collect the department heads in an array
      const departmentHeads = [
        movingBusinessUnit.departmentHead,
        destinationBusinessUnit.departmentHead,
      ];

      return departmentHeads;
    } catch (error) {
      throw new InternalServerErrorException(`Error while merging business units: ${error?.message}`);
    }
  }

  async assignTo(businessUnitId: Types.ObjectId, newOwnerId: string) {
      const populateOptions = this.getPopulateOptions();
      try {
        // Find the moving business unit
        const movingBusinessUnit = await this.businessUnitModel.findById(businessUnitId);
        if (!movingBusinessUnit) {
          throw new NotFoundException('business unit not found.');
        }
        movingBusinessUnit.departmentHead = newOwnerId.toString();
        // Update and save the business unit
        await movingBusinessUnit.save();

        if(newOwnerId) {
          const user = await this.basicUserModel.findById(newOwnerId).exec();
          if (!user) {
            throw new NotFoundException('Department head not found.');
          }
          // Ensure it's initialized as an array
          if (!Array.isArray(user.businessUnit)) {
            user.businessUnit = [];
          }
          // Convert both sides to string (important for ObjectId comparisons)
          const existingUnits = user.businessUnit.map(unit => unit.toString());
          const newUnit = businessUnitId.toString();
          if (!existingUnits.includes(newUnit)) {
            user.businessUnit.push(newUnit);
          }
          await user.save();
        }
        
        return movingBusinessUnit;
      } catch (error) {
        throw new InternalServerErrorException(`Error while changing business units head: ${error?.message}`);
      }
    }

  private calculateNewKey(parentKey: string, siblingCount: number): string {
    // Assuming keys are in format "parentKey-siblingCount"
    return `${parentKey}-${siblingCount}`;
  }

  private async updateChildren(businessUnit: Document<unknown, {}, BusinessUnit> & BusinessUnit & { _id: Types.ObjectId; }) {
    const children = await this.businessUnitModel.find({ parentBusinessUnit: businessUnit._id.toString() });

    // this.logger.log(children)

    for (const [index, child] of children.entries()) {
      child.level = businessUnit.level + 1; // Update child's level
      const newChildKey = `${businessUnit.key}-${index}`;
      const newChildBreadCrumb = `${businessUnit.breadcrumb} -> ${child.label}`;
      child.key = newChildKey;
      child.type = businessUnit.type;
      child.breadcrumb = newChildBreadCrumb;
      await child.save(); // Save updated child
      await this.updateChildren(child); // Recursively update children's levels
    }
  }

  async getOnlyActiveBusinessUnits(query: FilterBusinessUnitsDto) {
    const populateOptions = this.getPopulateOptions();

    try {
      const { name, org, parentBusinessUnit, page, limit, type } = query;
      // Validate page and limit to be positive integers
      if (page <= 0 || limit <= 0) {
        throw new BadRequestException('Page and limit must be positive integers.');
      }
      let conditions: any = { isDeleted: false };
      // let conditions: any = {key: '0' };

      if (name) {
        const regex = new RegExp(name, 'i'); // 'i' for case-insensitive search
        conditions.name = regex;
      }

      if (org) {
        conditions.org = org;
      }

      if (parentBusinessUnit) {
        conditions.parentBusinessUnit = parentBusinessUnit;
      }

      if (type) {
        conditions.type = type;
      }

      const businessunits = await this.businessUnitModel.find(conditions)
        .populate(populateOptions)
        .skip((page - 1) * limit)
        .limit(limit)
        .exec();
      // this.logger.log(businessunits)
      return businessunits;
    } catch (error) {
      // Handling specific Mongoose validation errors
      if (error.name === 'ValidationError') {
        this.logger.error(`Validation Error: ${error.message}`);
        throw new BadRequestException(`Validation Error: ${error.message}`);
      }

      // Handling errors related to querying database (like syntax or structure issues)
      if (error.name === 'MongoError' && error.code === 2) {
        this.logger.error(`Query Error: ${error.message}`);
        throw new BadRequestException(`Invalid query structure: ${error.message}`);
      }

      // Handling MongoDB connection issues
      if (error.name === 'MongoNetworkError') {
        this.logger.error(`Database Connection Error: ${error.message}`);
        throw new ServiceUnavailableException(`Database connection error: ${error.message}`);
      }

      // Handling timeout errors
      if (error.name === 'MongooseTimeoutError') {
        this.logger.error(`Query Timeout: ${error.message}`);
        throw new ServiceUnavailableException(`Request timed out: ${error.message}`);
      }

      // Generic fallback error handling
      this.logger.error(error);
      this.logger.error(error?.message);
      throw new InternalServerErrorException(`Error while fetching active businessunits: ${error?.message}`);
    }
  }

  async getOnlySoftDeletedBusinessUnits(
    page: number,
    limit: number,
  ): Promise<BusinessUnitDocument[]> {
    const populateOptions = this.getPopulateOptions();
    try {
      return await this.businessUnitModel
        .find({ isDeleted: true })
        .populate(populateOptions)
        .skip((page - 1) * limit)
        .limit(limit)
        .exec();
    } catch (error) {
      throw new InternalServerErrorException(
        `Error while fetching only soft deleted businessUnits: ${error?.message}`,
      );
    }
  }

  // if admin wants to see list of records include deleting records we need one more function
  async getAllBusinessUnits(
    page: number,
    limit: number,
    orgId?: Types.ObjectId,
    type?: BusinessUnitType
  ): Promise<BusinessUnitDocument[]> {
    const populateOptions = this.getPopulateOptions();
    try {
      const query: any = { isDeleted: false };
      if (orgId) {
        query.org = orgId;
      }
      if (type) {
        query.type = type;
      }
      return await this.businessUnitModel
        .find(query)
        .populate(populateOptions)
        .skip((page - 1) * limit)
        .limit(limit)
        .exec();
    } catch (error) {
      throw new InternalServerErrorException(
        `Error while fetching all businessUnits: ${error?.message}`,
      );
    }
  }

  async findOne(businessUnitId: Types.ObjectId) {
    const populateOptions = this.getPopulateOptions();
    try {
      const businessUnit = await this.businessUnitModel
        .findById(businessUnitId)
        .populate(populateOptions)
        .exec();
      if (!businessUnit || businessUnit.isDeleted) {
        throw new NotFoundException(
          `BusinessUnit not found with ID: ${businessUnitId}`,
        );
      }
      return businessUnit;
    } catch (error) {
      throw new InternalServerErrorException(
        `Error while fetching business unit by ID ${businessUnitId}: ${error?.message}`,
      );
    }
  }

  async getBusinessUnitCounts() {
    try {
      const count = await this.businessUnitModel
        .countDocuments({ isDeleted: false })
        .exec();
      return {
        count,
      };
    } catch (error) {
      throw new InternalServerErrorException(
        `Error while fetching business unit counts: ${error.message}`,
      );
    }
  }

  async searchBusinessUnits(name: string) {
    const populateOptions = this.getPopulateOptions();
    try {
      if (!name) {
        throw new HttpException(
          'Name parameter is required',
          HttpStatus.BAD_REQUEST,
        );
      }
      const regex = new RegExp(name, 'i'); // 'i' for case-insensitive
      return await this.businessUnitModel
        .find({
          name: { $regex: regex },
          isDeleted: false,
        })
        .populate(populateOptions)
        .exec();
    } catch (error) {
      this.logger.error(
        `Error while searching for businessUnits: ${error.message}`,
        error.stack,
      );
      throw new InternalServerErrorException(
        `Error while searching for businessUnits: ${error?.message}`,
      );
    }
  }

  async filterBusinessUnits(
    queryDto: FilterBusinessUnitsDto,
  ): Promise<BusinessUnitDocument[]> {
    const { name, org, parentBusinessUnit, page, limit } = queryDto;
    const populateOptions = this.getPopulateOptions();
    try {
      const query: any = {};

      if (org) {
        query.org = org;
      }

      if (parentBusinessUnit) {
        query.parentBusinessUnit = parentBusinessUnit;
      }

      if (name) {
        query.name = { $regex: name, $options: 'i' };
      }

      const businessUnits = await this.businessUnitModel
        .find(query)
        .populate(populateOptions)
        .skip((page - 1) * limit)
        .limit(limit)
        .exec();

      return businessUnits;
    } catch (error) {
      throw new InternalServerErrorException(
        `An error occurred while fetching businessUnits: ${error?.message}`,
      );
    }
  }

  async update(
    businessUnitId: Types.ObjectId,
    updateBusinessUnitDto: UpdateBusinessUnitDto,
    user: any
  ) {
    const populateOptions = this.getPopulateOptions();

    try {

      const existingBusinessUnit = await this.businessUnitModel.findById(businessUnitId).exec();

      const updatedBusinessUnit = await this.businessUnitModel
        .findByIdAndUpdate(businessUnitId, updateBusinessUnitDto, { new: true })
        .populate(populateOptions)
        .exec();

      if (!updatedBusinessUnit) {
        throw new InternalServerErrorException(
          `Failed to update business unit with ID ${businessUnitId}`,
        );
      }

      if(updateBusinessUnitDto.departmentHead) {
        const user = await this.basicUserModel.findById(updateBusinessUnitDto.departmentHead).exec();
        if (!user) {
          throw new NotFoundException('Department head not found.');
        }
        // Ensure it's initialized as an array
        if (!Array.isArray(user.businessUnit)) {
          user.businessUnit = [];
        }
        // Convert both sides to string (important for ObjectId comparisons)
        const existingUnits = user.businessUnit.map(unit => unit.toString());
        const newUnit = updatedBusinessUnit._id.toString();
        if (!existingUnits.includes(newUnit)) {
          user.businessUnit.push(newUnit);
        }
        await user.save();

        // Find the old department head (before update)
        const oldHeadId = existingBusinessUnit?.departmentHead?.toString();
        if (oldHeadId && oldHeadId !== user._id.toString()) {
          const oldHead = await this.basicUserModel.findById(oldHeadId).exec();
          if (oldHead) {
            // Ensure reportingTo is an array
            if (!Array.isArray(oldHead.reportingTo)) {
              oldHead.reportingTo = [];
            }
            const reportingIds = oldHead.reportingTo.map(id => id.toString());
            if (!reportingIds.includes(user._id.toString())) {
              oldHead.reportingTo.push(user._id.toString());
            }
            await oldHead.save();
          }
        }

      }

     
      this.emitEvent('business-unit.updated', { updatedBusinessUnit, user });

      return updatedBusinessUnit;
    } catch (error) {
      this.logger.error(
        `An error occurred while updating business unit with ID ${businessUnitId}:`,
        error.stack,
      );
      throw new InternalServerErrorException(
        `An error occurred while updating business unit with ID ${businessUnitId}: ${error.message}`,
      );
    }
  }

  async hardDelete(businessUnitId: Types.ObjectId, user: any) {
    try {
      const businessUnit = await this.findOne(businessUnitId);
      this.emitEvent('business-unit.hard.delete', { businessUnit, user });
      return await this.businessUnitModel.findByIdAndDelete(businessUnitId);
    } catch (error) {
      throw new InternalServerErrorException(
        `An error occurred in fetching business unit by Id ${businessUnitId}: ${error?.message}`,
      );
    }
  }

  async softDelete(businessUnitId: Types.ObjectId, user: any) {
    try {
      const businessUnit = await this.findOne(businessUnitId);
      businessUnit.isDeleted = true;
      await businessUnit.save();

      // ✅ Remove this department from all users' businessUnit arrays
      await this.basicUserModel.updateMany(
        { businessUnit: { $in: [businessUnit._id.toString()] }, // match inside array,
          },
        { $pull: { businessUnit: businessUnit._id.toString() } }
      );

      this.emitEvent('business-unit.soft.delete', { businessUnit, user });
      return businessUnit;
    } catch (error) {
      throw new InternalServerErrorException(
        `An error occurred in fetching business unit by Id ${businessUnitId}: ${error?.message}`,
      );
    }
  }

  async hardDeleteAllBusinessUnits() {
    try {
      const businessUnitsToDelete = await this.businessUnitModel.find();
      if (!businessUnitsToDelete.length) {
        // this.logger.log('No businessUnits found to delete.');
        throw new NotFoundException('No businessUnits found to delete.');
      }
      return await this.businessUnitModel.deleteMany();
    } catch (error) {
      // this.logger.error('Error while hard deleting all businessUnits', error.stack);
      throw new InternalServerErrorException(
        'Error while hard deleting all businessUnits',
      );
    }
  }

  async restoreSoftDeletedBusinessUnit(businessUnitId: Types.ObjectId, user: any) {
    try {
      const businessUnit =
        await this.businessUnitModel.findById(businessUnitId);
      if (!businessUnit) {
        throw new NotFoundException(
          `The business unit with ID: "${businessUnitId}" doesn't exist.`,
        );
      }
      if (!businessUnit.isDeleted) {
        throw new BadRequestException(
          `The business unit with ID: "${businessUnitId}" is not soft deleted.`,
        );
      }
      businessUnit.isDeleted = false;
      await businessUnit.save();
      this.emitEvent('business-unit.restore', { businessUnit, user });
      return businessUnit;
    } catch (error) {
      // this.logger.error(`An error occurred while restoring BusinessUnit by ID ${businessUnitId}. ${error?.message}`);
      throw new InternalServerErrorException(
        `An error occurred in fetching business unit by Id ${businessUnitId}: ${error?.message}`,
      );
    }
  }

  async restore() {
    const populateOptions = this.getPopulateOptions();
    try {
      const softDeletedBusinessUnits = await this.businessUnitModel.find({
        isDeleted: true,
      });
      if (!softDeletedBusinessUnits.length) {
        this.logger.log('No soft-deleted businessUnits found to restore.');
        return [];
      }
      for (const businessUnit of softDeletedBusinessUnits) {
        businessUnit.isDeleted = false;
        await businessUnit.save();
      }
      // this.logger.log('All soft-deleted businessUnits have been restored.');
      return this.businessUnitModel.find().populate(populateOptions).exec();
    } catch (error) {
      this.logger.error(
        `An error occurred while restoring soft-deleted businessUnits. ${error.message}`,
      );
      throw new InternalServerErrorException(
        `An error occurred while restoring soft-deleted businessUnits. ${error.message}`,
      );
    }
  }

  getPopulateOptions(): any[] {
    const populateOptions = [
      {
        path: 'createdBy',
        select: '_id roles firstName email',
        model: 'BasicUser',
      },
    ];

    if (this.businessUnitModel.schema.paths['org']) {
      populateOptions.push({
        path: 'org',
        select: '_id title description',
        model: 'Org',
      });
    }

    if (this.businessUnitModel.schema.paths['parentBusinessUnit']) {
      populateOptions.push({
        path: 'parentBusinessUnit',
        select: '_id name label key',
        model: 'BusinessUnit',
      });
    }

    if (this.businessUnitModel.schema.paths['children']) {
      populateOptions.push({
        path: 'children',
        select: '_id name label key',
        model: 'BusinessUnit',
      });
    }


    return populateOptions;
  }

  async getUsersByBusinessUnitType(query: FilterUsersDto) {
    try {

      const { org, type } = query;

      let conditions: any = { isDeleted: false };

      if (org) {
        conditions.org = org;
      }

      if (type) {
        conditions.type = type;
      }

      // this.logger.log(JSON.stringify(conditions))
      const businessUnits = await this.businessUnitModel.find(conditions).exec();

      if (!businessUnits || businessUnits.length === 0) {
        throw new NotFoundException(`No business units found.`);
      }

      // Extract business unit IDs
      const businessUnitIds = businessUnits.map((unit) => unit._id.toString());

      // this.logger.log(JSON.stringify(businessUnitIds))

      // Fetch users associated with the retrieved business unit IDs
      const users = await this.basicUserModel
        .find({ businessUnit: { $in: businessUnitIds }, isDeleted: false })
        .exec();

      // this.logger.log(JSON.stringify(users))

      return users;
    } catch (error) {
      throw new InternalServerErrorException(
        `An error occurred while fetching users for business unit: ${error?.message}`,
      );
    }
  }

  async getClientsByBusinessunit(user: any, businessUnitId: Types.ObjectId) {
    try {
      const postingOrg = user.org._id;
      let conditions: any = { isDeleted: false, _id: businessUnitId, org: postingOrg };

      // this.logger.log(JSON.stringify(conditions))
      const businessUnits = await this.businessUnitModel.find(conditions).exec();

      // if (!businessUnits || businessUnits.length === 0) {
      //   throw new NotFoundException(`No business units found.`);
      // }

      // Extract business unit IDs
      const businessUnitIds = businessUnits.map((unit) => unit._id.toString());

      // this.logger.log(JSON.stringify(businessUnitIds))

      // Fetch users associated with the retrieved business unit IDs
      console.log(businessUnitId)
      console.log(postingOrg)
      const clients = await this.orgsModel
        .find({ businessUnit: { $in: businessUnitIds }, orgType: OrgType.CUSTOMER_ORG, isDeleted: false }, { _id: 1, title: 1 })
        .exec();

      // this.logger.log(JSON.stringify(users))

      return clients;
    } catch (error) {
      throw new InternalServerErrorException(
        `An error occurred while fetching clients for business unit: ${error?.message}`,
      );
    }
  }

  emitEvent(eventName: string, payload: any) {
    this.eventEmitter.emit(eventName, payload);
  }

  async updateBgvhandler(
    businessUnitId: Types.ObjectId,bgvHandlerId: string,user: any
  ) {
    const populateOptions = this.getPopulateOptions();

    try {

      const updatedBusinessUnit = await this.businessUnitModel
      .findByIdAndUpdate(
        businessUnitId,
        {
          bgvHandlerId: bgvHandlerId.toString(),
          updatedAt: new Date(), // optional auditing
        },
        { new: true },
      )
      .populate(populateOptions)
      .exec();


      if (!updatedBusinessUnit) {
        throw new InternalServerErrorException(
          `Failed to update business unit with ID ${businessUnitId}`,
        );
      }
      return updatedBusinessUnit;
    } catch (error) {
      this.logger.error(
        `An error occurred while updating business unit with ID ${businessUnitId}:`,
        error.stack,
      );
      throw new InternalServerErrorException(
        `An error occurred while updating business unit with ID ${businessUnitId}: ${error.message}`,
      );
    }
  }

}
