import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsOptional, IsString, IsInt, Min, IsEnum, IsMongoId } from 'class-validator';
import { BusinessUnitType } from 'src/shared/constants';

export class FilterUsersDto {
  
  @IsOptional()
  @IsMongoId()
  @Transform(({ value }) => (value === undefined || value === '0' ? '' : value))
  org?: string;
 
  @ApiProperty({
    type: String,
    required: false,
    default: BusinessUnitType.UNSPECIFIED,
    enum: BusinessUnitType,
    description: "Business Unit type",
  })
  @IsOptional()
  @IsString()
  @IsEnum(BusinessUnitType)
  type?: string;


}
