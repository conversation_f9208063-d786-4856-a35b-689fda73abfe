import { Controller, Get, Post, Body, Patch, Param, Delete, Logger, UseGuards, Req, NotFoundException, InternalServerErrorException, Query } from '@nestjs/common';
import { BenchService } from './bench.service';
import { CreateBenchDto } from './dto/create-bench.dto';
import { UpdateBenchDto } from './dto/update-bench.dto';
import { ApiBearerAuth, ApiOperation, ApiParam, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Roles } from 'src/auth/decorators/roles.decorator';
import { AuthJwtGuard } from 'src/auth/guards/auth-jwt/auth-jwt.guard';
import { RolesGuard } from 'src/auth/guards/roles/roles.guard';
import { Role } from 'src/auth/enums/role.enum';

@Controller()
@ApiTags('Bench')
export class BenchController {
  constructor(private readonly benchService: BenchService) { }

  private readonly logger = new Logger(BenchController.name);

  @Post()
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.BUHead, Role.TeamLead, Role.DeliveryManager, Role.Recruiter, Role.Vendor)
  @Roles()
  @ApiResponse({ status: 201, description: 'Job application is saved.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Data. ' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role "BUHead", "TeamLead", "DeliveryManager" and "Recruiter" can only use this end point.' })
  @ApiOperation({ summary: 'Create a Bench Candidate', description: `This endpoint for creating a job application. This is only accessible for "BUHead", "TeamLead", "DeliveryManager" and "Recruiter".` })
  create(@Req() req: any, @Body() createBenchDto: CreateBenchDto) {
    createBenchDto.createdBy = req.user._id
    return this.benchService.create(req.user, createBenchDto);
  }

  @Get()
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.BUHead, Role.TeamLead, Role.DeliveryManager, Role.Recruiter, Role.Vendor)
  @Roles()
  @ApiResponse({ status: 201, description: 'Job application is saved.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Data. ' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role "BUHead", "TeamLead", "DeliveryManager" and "Recruiter" can only use this end point.' })
  @ApiOperation({ summary: 'Create a Bench Candidate', description: `This endpoint for creating a job application. This is only accessible for "BUHead", "TeamLead", "DeliveryManager" and "Recruiter".` })
  findAll(@Req() req: any,) {
    // Pass the logged-in user's ID to the service
    return this.benchService.findAll(req.user._id);
  }

  @Patch(':id')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.BUHead, Role.TeamLead, Role.DeliveryManager, Role.Recruiter, Role.Vendor)
  @Roles()
  @ApiParam({ name: 'id', description: 'The ID of the Bench Candidate to update' })
  @ApiResponse({ status: 200, description: 'Bench Candidate updated successfully.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Invalid Data.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden. Only specific roles can access this endpoint.' })
  @ApiResponse({ status: 404, description: 'Bench Candidate not found.' })
  @ApiOperation({ summary: 'Update a Bench Candidate', description: `This endpoint updates a Bench Candidate by ID. Only accessible by "BUHead", "TeamLead", "DeliveryManager", and "Recruiter".` })
  async update(@Param('id') id: string, @Body() updateBenchDto: UpdateBenchDto) {
    try {
      const updatedBench = await this.benchService.update(id, updateBenchDto);
      if (!updatedBench) {
        throw new NotFoundException(`Bench Candidate with ID ${id} not found`);
      }
      return updatedBench;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException(`Failed to update Bench Candidate: ${error.message}`);
    }
  }

  @Get(':id')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.BUHead, Role.TeamLead, Role.DeliveryManager, Role.Recruiter, Role.Vendor)
  @Roles()
  @ApiParam({ name: 'id', description: 'The ID of the Bench Candidate to retrieve' })
  @ApiResponse({ status: 201, description: 'Job application is saved.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Data. ' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role "BUHead", "TeamLead", "DeliveryManager" and "Recruiter" can only use this end point.' })
  @ApiOperation({ summary: 'Get a specific Bench Candidate', description: `Fetches details of a Bench Candidate by ID.` })
  async findOne(@Param('id') id: string) {
    // Pass the logged-in user's ID to the service
    return this.benchService.findOne(id);
  }

  @Get('/stats')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.BUHead, Role.TeamLead, Role.DeliveryManager, Role.Recruiter, Role.Vendor)
  @Roles()
  @ApiQuery({ name: 'fromDate', required: false, type: String, description: 'Start date in YYYY-MM-DD format' })
  @ApiQuery({ name: 'toDate', required: false, type: String, description: 'End date in YYYY-MM-DD format' })
  async getVendorStats(@Req() req: any, @Query('fromDate') fromDate?: string,
    @Query('toDate') toDate?: string) {
    return this.benchService.getVendorBenchAndMatchingJobs(req.user,req.user._id, req.user.companyId._id, fromDate,
      toDate);
  }

  @Delete(':id')
  async deleteBench(@Param('id') id: string) {
    return this.benchService.delete(id);
  }


}
