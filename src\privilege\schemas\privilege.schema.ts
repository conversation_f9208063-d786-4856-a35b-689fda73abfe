import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';

export type PrivilegeDocument = HydratedDocument<Privilege>;

@Schema({ timestamps: true })
export class Privilege {

    @Prop({ required: true, unique: true, trim: true })
    name: string;

    @Prop({ required: true })
    description: string;

    @Prop({ required: true, trim: true })
    resource: string;

    @Prop({ required: true, trim: true })
    action: string;
}

export const PrivilegeSchema = SchemaFactory.createForClass(Privilege);
