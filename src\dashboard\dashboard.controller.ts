// dashboard.controller.ts
import { Body, Controller, Get, Post, Req, UseGuards } from '@nestjs/common';
import { DashboardService } from './dashboard.service';
import { ApiBearerAuth } from '@nestjs/swagger';
import { AuthJwtGuard } from 'src/auth/guards/auth-jwt/auth-jwt.guard';
import { CreateDashboardDto } from './dto/createDashboard.dto';

@Controller('dashboard')
export class DashboardController {
  constructor(private readonly dashboardService: DashboardService) { }

  @Post()
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  async createDashboard(@Body() dto: CreateDashboardDto) {
    return this.dashboardService.saveDashboard(dto);
  }

  @Get('url')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  async getUserDashboard(@Req() req: any) {
    // const userId = req.user._id.toString();// Get from session/JWT
    const dashboardUrl = await this.dashboardService.generateDashboardUrl(req?.user);
    return { dashboardUrl };
  }
}

