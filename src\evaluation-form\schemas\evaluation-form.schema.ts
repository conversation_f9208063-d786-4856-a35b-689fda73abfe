import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Types } from 'mongoose';
import { Job } from 'src/job/schemas/job.schema';

export type EvaluationFormDocument = HydratedDocument<EvaluationForm>;

@Schema({
  timestamps: true
})
export class EvaluationForm {

  @Prop({
    type: Types.ObjectId,
    required: true,
    ref: 'Job'
  })
  jobId: Job;

  //Name of the skill  
  @Prop({
    type: String,
    required: true,
    trim: true
  })
  skill: string;

  // Years of expertise in the skill mentioned above
  @Prop({
    type: Number,
    required: false,
  })
  years?: number;

  // Months of expertise in the skill mentioned above
  @Prop({
    type: Number,
    required: false,
  })
  months?: number;

  //Rate your expertise in the skill mentioned above
  @Prop({
    type: Number,
    required: true,
  })
  rating: number;

  // skill can be either primary or secondary
  @Prop({
    type: Boolean,
    required: true,
    default: false
  })
  isPrimary: boolean;

}

export const EvaluationFormSchema = SchemaFactory.createForClass(EvaluationForm);

