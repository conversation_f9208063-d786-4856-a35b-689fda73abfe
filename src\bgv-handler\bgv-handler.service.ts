import { BadRequestException, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { BgvHandler, BgvHandlerDocument } from './schemas/bgv-handler.schema';
import { CreateBgvHandlerDto } from './dto/create-bgv-handler.dto';
import { UpdateBgvHandlerDto } from './dto/update-bgv-handler.dto';
import { BusinessUnit } from 'src/business-unit/schemas/business-unit.schema';
import { Org, OrgDocument } from 'src/org/schemas/org.schema';
import { OffersQueryDTO } from './dto/query-offers.dto';
import { Offer } from 'src/offer/schemas/offer.schema';
import * as bcrypt from 'bcrypt';
import { BasicUser } from 'src/user/schemas/basic-user.schema';
import { Role } from 'src/auth/enums/role.enum';
import { omit } from 'lodash';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { CreateBgvDto } from 'src/offer/dto/create-bgv.dto';
import { Bgv } from 'src/offer/schemas/bgv.schema';
import { OnboardingStatus } from 'src/shared/constants';
import { BgvHandlerQueryDto } from './dto/query-all.dto';


@Injectable()
export class BgvHandlerService {
    private readonly logger = new Logger(BgvHandlerService.name);
    constructor(
        @InjectModel(BgvHandler.name) private readonly bgvHandlerModel: Model<BgvHandlerDocument>,
        @InjectModel(BusinessUnit.name) private businessUnitModel: Model<BusinessUnit>,
        @InjectModel(Org.name) private orgModel: Model<OrgDocument>,
        @InjectModel(Offer.name) private offerModel: Model<Offer>, private eventEmitter: EventEmitter2,
        @InjectModel(BasicUser.name) private basicUserModel: Model<BasicUser>,
        @InjectModel(Bgv.name) private bgvModel: Model<Bgv>,

    ) { }

    emitEvent(eventName: string, payload: any) {
        // payload['event']= eventName;
        // this.logger.debug(payload)
        this.eventEmitter.emit(eventName, payload);
    }

    async create(user: any, dto: CreateBgvHandlerDto) {
        dto.createdBy = user?._id?.toString();
        dto.org = user?.org?._id.toString();

        const existingUser = await this.basicUserModel.findOne({ email: dto.email });
        if (existingUser) {
            throw new BadRequestException('A user with this email already exists. Cannot create BGV handler.');
        }

        // Step 1: If this is being marked as default
        if (dto.isDefault) {
            // Step 2: Find existing default for this org and unset it
            await this.bgvHandlerModel.updateMany(
                { org: dto.org, isDefault: true },
                { $set: { isDefault: false } },
            );
        }
        console.log("dto", dto)

        const newHandler = await this.bgvHandlerModel.create(dto);

        console.log("newHandler", newHandler)
        // Step 3: Process Department Assignments
        if (dto.assignToDepartment?.length) {
            const validDepartments: string[] = [];

            for (const deptId of dto.assignToDepartment) {
                const dept = await this.businessUnitModel.findById(deptId).lean();
                if (dept?.bgvHandlerId) {
                    // Already assigned, remove from handler
                    await this.bgvHandlerModel.findByIdAndUpdate(dept?.bgvHandlerId, {
                        $pull: { assignToDepartment: deptId },
                    });
                    await this.businessUnitModel.findByIdAndUpdate(deptId, {
                        $set: { bgvHandlerId: newHandler._id?.toString() },
                    });
                } else {
                    validDepartments.push(deptId);
                    await this.businessUnitModel.findByIdAndUpdate(deptId, {
                        $set: { bgvHandlerId: newHandler._id?.toString() },
                    });
                }
            }
        }

        // Step 4: Process Org Assignments
        if (dto.assignToOrg?.length) {
            const validOrgs: string[] = [];

            for (const orgId of dto.assignToOrg) {
                const org = await this.orgModel.findById(orgId).lean();
                if (org?.bgvHandlerId) {
                    await this.bgvHandlerModel.findByIdAndUpdate(org?.bgvHandlerId, {
                        $pull: { assignToOrg: orgId },
                    });
                    await this.orgModel.findByIdAndUpdate(orgId, {
                        $set: { bgvHandlerId: newHandler._id?.toString() },
                    });
                } else {
                    validOrgs.push(orgId);
                    await this.orgModel.findByIdAndUpdate(orgId, {
                        $set: { bgvHandlerId: newHandler._id?.toString() },
                    });
                }
            }
        }

        const generateStrongPassword = () => {
            const uppercase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
            const lowercase = "abcdefghijklmnopqrstuvwxyz";
            const numbers = "0123456789";
            const specialChars = "!@#$%^&*()_-+=<>?";
            const allChars = uppercase + lowercase + numbers + specialChars;

            let password = '';
            password += uppercase[Math.floor(Math.random() * uppercase.length)];
            password += numbers[Math.floor(Math.random() * numbers.length)];
            password += specialChars[Math.floor(Math.random() * specialChars.length)];

            for (let i = 3; i < 8; i++) {
                password += allChars[Math.floor(Math.random() * allChars.length)];
            }

            return password.split('').sort(() => 0.5 - Math.random()).join(''); // Shuffle the password
        };
        // Generate and hash the password
        const password = generateStrongPassword();
        const hashedPassword = await bcrypt.hash(password, 10);


        const createMember = new this.basicUserModel({
            email: newHandler.email,
            firstName: newHandler.title,
            lastName: newHandler.title,
            contactNumber: newHandler.contactNumber,
            roles: [Role.BgvHandler], // Assign the Employee role
            bgvHandlerId: newHandler._id?.toString(),
            // ...createMemberDto,
            password: hashedPassword,
            isVerified: true,
        });

        const createdMember = await createMember.save();

        this.emitEvent('user.verify.email.confirm.password', { ...createMember.toObject(), password });
        const sanitizedUser = omit(createdMember.toObject(), ['password', '__v']);
        return sanitizedUser;


        // return newHandler;

        // return this.bgvHandlerModel.create(dto);
    }

    async findAll(user: any, query: BgvHandlerQueryDto): Promise<{
        data: any[];
        total: number;
        totalPages: number;
        page: number;
        limit: number;
    }> {
        const {
            page = 1,
            limit = 10,
            isDefault,
            search,
            email,
            title,
            documentKeys,
            assignToOrgId,
            assignToDepartmentId,
        } = query;
        const baseFilter: any = {
            isDeleted: false,
            org: user?.org?._id.toString(),
        };
        if (search) {
            const regex = new RegExp(search, 'i'); // case-insensitive match
            baseFilter.$or = [
                { title: regex },
                { email: regex },
                { contactNumber: regex },
            ];
        }
        if (isDefault !== undefined) {
            baseFilter.isDefault = isDefault;
        }
        if (email) {
            baseFilter.email = { $regex: new RegExp(email, 'i') }; // case-insensitive
        }
        if (title) {
            baseFilter.title = { $regex: new RegExp(title, 'i') };
        }

        if (documentKeys && documentKeys.length > 0) {
            baseFilter.documentKeys = { $all: query.documentKeys };
        }

        if (assignToOrgId) {
            baseFilter.assignToOrg = assignToOrgId;
        }

        if (assignToDepartmentId) {
            baseFilter.assignToDepartment = assignToDepartmentId;
        }

        // Query for assignToOrg and assignToDepartment after population
        const handlers = await this.bgvHandlerModel.find(baseFilter)
            .populate([
                {
                    path: 'assignToDepartment',
                    model: 'BusinessUnit',
                    select: 'label _id',
                },
                {
                    path: 'assignToOrg',
                    model: 'Org',
                    select: 'title _id',
                },
            ])
            .lean()
            .exec();

        const total = handlers.length;
        const totalPages = Math.ceil(total / limit);
        const startIndex = (page - 1) * limit;
        const paginatedData = handlers.slice(startIndex, startIndex + limit);

        return {
            data: paginatedData,
            total,
            totalPages,
            page,
            limit,
        };
    }

    async findOne(user: any, id: string) {
        const handler = await this.bgvHandlerModel.findById(id)
            .populate([
                {
                    path: 'assignToDepartment',
                    model: 'BusinessUnit', // ✅ Must match what you registered
                    select: 'label _id',
                },
                {
                    path: 'assignToOrg',
                    model: 'Org', // ✅ Must match your registration
                    select: 'title _id',
                },
            ])
            .exec();
        if (!handler || handler.isDeleted) throw new NotFoundException('Handler not found');
        return handler;
    }

    async update(user: any, id: string, dto: UpdateBgvHandlerDto) {
        const existed = await this.bgvHandlerModel.findById(id);
        const updated = await this.bgvHandlerModel.findByIdAndUpdate(id, dto, { new: true }).exec();
        // If the handler was default and is no longer default
        if (existed?.isDefault && !updated?.isDefault) {
            // Find another handler from the same org (excluding this one)
            const fallback = await this.bgvHandlerModel.findOne({
                org: updated?.org?.toString(),
                // _id: { $ne: updated?._id },
            });

            if (fallback) {
                await this.bgvHandlerModel.findByIdAndUpdate(fallback._id, { isDefault: true });
            }
        }
        // If the updated handler is now default, unset others
        if (updated?.isDefault) {
            await this.bgvHandlerModel.updateMany(
                {
                    org: updated.org,
                    _id: { $ne: updated._id },
                    isDefault: true,
                },
                { $set: { isDefault: false } }
            );
        }

        // Step 3: Assign to departments
        if (dto.assignToDepartment && dto.assignToDepartment.length) {
            for (const deptId of dto.assignToDepartment) {
                await this.businessUnitModel.findByIdAndUpdate(deptId, {
                    $set: { bgvHandlerId: updated?._id?.toString() },
                });
            }
        }

        // Step 4: Assign to orgs
        if (dto.assignToOrg && dto.assignToOrg.length) {
            for (const orgId of dto.assignToOrg) {
                await this.orgModel.findByIdAndUpdate(orgId, {
                    $set: { bgvHandlerId: updated?._id?.toString() },
                });
            }
        }
        if (!updated || updated.isDeleted) throw new NotFoundException('Handler not found or deleted');
        return updated;
    }

    async remove(user: any, id: string) {
        const result = await this.bgvHandlerModel.findByIdAndUpdate(id, { isDeleted: true }).exec();
        if (!result) throw new NotFoundException('Handler not found');
        return { success: true };
    }

    async getAllPendingBgvs(user: any, query: OffersQueryDTO): Promise<any[]> {

        const { postingOrg, endClientOrg, name, page = 1, limit = 10, jobType, employmentType, fromDate, toDate } = query;

        const currentUser = await this.basicUserModel.findById(user._id).exec();
        console.log(currentUser)
        const offers = await this.offerModel.find({
            isApproved: true,
            bgvHandlerId: currentUser?.bgvHandlerId.toString()
            //   jobApplication: { $in: appIds }
        }).populate([
            {
                path: 'createdBy', // offer.createdBy
                model: 'BasicUser', // or your actual user model name
                select: '_id firstName lastName email'
            },
            {
                path: 'jobApplication',
                populate: [
                    {
                        path: 'createdBy', // jobApplication.createdBy
                        model: 'BasicUser',
                        select: '_id firstName lastName email roles'
                    },
                    {
                        path: 'stage',
                        model: 'Stage',
                        select: 'type name'
                    },
                    {
                        path: 'jobId',
                        populate: {
                            path: 'endClientOrg',
                            model: 'Org',
                            select: '_id title'
                        },
                        model: 'Job',
                        select: '_id title endClientOrg employmentType'
                    }
                ],
                model: 'JobApplication',
                // select: '_id firstName lastName contactDetails contactAddress jobId stage org createdBy'
            }
        ])
            .sort({ createdAt: -1 }) // Sort latest bgvs at the top
            .skip((page - 1) * limit)
            .limit(limit)
            .exec();

        // const filteredOffers = offers.filter(
        //   offer => (offer.jobApplication?.stage as any)?.type === 'workflow.offer'
        // );
        console.log(offers)
        const filteredOffers = offers.filter((offer: any) => {
            const stageType = (offer.jobApplication?.stage as any)?.type;
            const job = offer.jobApplication?.jobId;
            const candidate = offer.jobApplication;

            // Stage type must be workflow.offer
            if (stageType !== 'workflow.offer') return false;

            // Filter by endClientOrg
            if (endClientOrg && job?.endClientOrg?._id?.toString() !== endClientOrg) return false;

            // Filter by employmentType
            if (employmentType && job?.employmentType !== employmentType) return false;

            // Filter by date range on offer createdAt
            if (fromDate) {
                const from = new Date(fromDate);
                if (new Date(offer.createdAt) < from) return false;
            }
            if (toDate) {
                const to = new Date(toDate);
                if (new Date(offer.createdAt) > to) return false;
            }

            // Filter by candidate name
            if (name) {
                const regex = new RegExp(name, 'i');
                const candidateFullName = `${candidate?.firstName ?? ''} ${candidate?.lastName ?? ''}`;
                const candidateCreatedFullName = `${candidate?.createdBy?.firstName ?? ''} ${candidate?.createdBy?.lastName ?? ''}`;
                // if (!regex.test(candidateFullName)) return false;
                const searchTarget = [
                    job?.title,
                    job?.endClientOrg?.title,
                    job?.employmentType,
                    candidateFullName,
                    candidateCreatedFullName,
                    candidate?.contactDetails?.contactNumber,
                ]
                    .filter(Boolean)
                    .join(' ');

                if (!regex.test(searchTarget)) return false;
            }

            return true;
        });


        return filteredOffers;

    }

    async saveInterimBgv(user: any, createBgvDto: CreateBgvDto) {

        const existingBgv = await this.bgvModel.findOne({ jobApplication: createBgvDto.jobApplication.toString() });

        if (!existingBgv) {
            throw new NotFoundException('No BGV record found');
        }

        const updatedDocs = createBgvDto.interimBgvDocs?.map(newDoc => {
            // const previous = existingBgv.interimBgvDocs?.find(
            //   doc => doc.fileMetadataId.toString() === newDoc.toString()
            // );

            return {
                fileMetadataId: newDoc.toString(),
                status: 'pending',
                uploadedAt: new Date(),
                // history: previous
                //   ? [
                //     ...(previous.history || []),
                //     {
                //       fileMetadataId: previous.fileMetadataId,
                //       status: previous.status,
                //       uploadedAt: previous.uploadedAt,
                //     },
                //   ]
                //   : [],
            };
        }) || [];

        existingBgv.interimBgvDocs = updatedDocs;

        return existingBgv.save();
    }

    async submitIntrimBgv(applicationId: Types.ObjectId) {

        const offer = await this.offerModel.findOne({ jobApplication: applicationId.toString() })
            .sort({ createdAt: -1 }) // 👉 Ensures the latest offer is returned
            .exec();
        // Step 3: Update the offer with the bgvId
        if (!offer) {
            throw new NotFoundException('No offer found for the specified job application.');
        }
        offer.status = OnboardingStatus.INTERIM_BGV_UPLOADED; // Set status to CLIENT_BGV_APPROVAL_PENDING
        // offer.stepperKey = 3;
        await offer.save();

        return offer;
    }

    async findBgvDocumentsByCandidateId(applicationId: Types.ObjectId) {
        try {
            const existingBgv = await this.bgvModel.findOne({ jobApplication: applicationId.toString() })
                .populate({
                    path: 'interimBgvDocs.fileMetadataId',
                    model: 'FileMetadata',
                    select: 'originalName uniqueName fileSize fileType uploadedBy createdAt locationUrl',
                }).exec();
            console.log("existingBgv", existingBgv);

            return existingBgv;
        }
        catch (error) {
            this.logger.error(`An error occurred in fetching offer by Id ${applicationId}. ${error?.message}`);
            throw error;

        }
    }
}
