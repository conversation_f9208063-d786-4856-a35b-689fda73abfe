import { Module } from '@nestjs/common';
import { IndustryService } from './industry.service';
import { IndustryController } from './industry.controller';
import { ConfigModule } from '@nestjs/config';
import { MongooseModule } from '@nestjs/mongoose';
import { Industry, IndustrySchema } from './schemas/industry.schema';
import { JwtModule } from '@nestjs/jwt';
import { EndpointsRolesModule } from 'src/endpoints-roles/endpoints-roles.module';
@Module({
  imports: [
    ConfigModule,
    JwtModule, EndpointsRolesModule,
    MongooseModule.forFeature([{ name: Industry.name, schema: IndustrySchema}])
  ],
  controllers: [IndustryController],
  providers: [IndustryService],
  exports: [IndustryService]
})
export class IndustryModule {}
