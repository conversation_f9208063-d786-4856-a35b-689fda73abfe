import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Req, Query } from '@nestjs/common';
import { RolePrivilegeService } from './role-privilege.service';
import { CreateRolePrivilegeDto } from './dto/create-role-privilege.dto';
import { ApiBearerAuth, ApiOperation, ApiParam, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { AuthJwtGuard } from 'src/auth/guards/auth-jwt/auth-jwt.guard';
import { validateObjectId } from 'src/utils/validation.utils';

@Controller('')
@ApiTags('role-privilege')
export class RolePrivilegeController {
  constructor(private readonly rolePrivilegeService: RolePrivilegeService) { }

  // @Post()
  // @ApiResponse({ status: 201, description: 'Role privileges assigned successfully.' })
  // @ApiResponse({ status: 400, description: 'Bad Request / Data validation failed.' })
  // @ApiResponse({ status: 401, description: 'Unauthorized.' })
  // @ApiOperation({ summary: 'Assign privileges to a role', description: 'Assign multiple privileges to a role.' })
  // @ApiBearerAuth()
  // @UseGuards(AuthJwtGuard)
  // create(@Req() req: any, @Body() createRolePrivilegeDto: CreateRolePrivilegeDto) {
  //   return this.rolePrivilegeService.create(createRolePrivilegeDto);
  // }

  // @Get()
  // @ApiResponse({ status: 200, description: 'Retrieved all role-privilege mappings.' })
  // @ApiResponse({ status: 401, description: 'Unauthorized.' })
  // @ApiOperation({ summary: 'Get all role-privilege mappings', description: 'Retrieve all role-privilege assignments.' })
  // @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number', example: 1 })
  // @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of items per page', example: 10 })
  // @ApiBearerAuth()
  // @UseGuards(AuthJwtGuard)
  // findAll(@Query('page') page: number = 1, @Query('limit') limit: number = 10) {
  //   return this.rolePrivilegeService.findAll(page, limit);
  // }

  // @Get(':rolePrivilegeId')
  // @ApiResponse({ status: 200, description: 'Role privileges retrieved successfully.' })
  // @ApiResponse({ status: 404, description: 'Role privilege not found.' })
  // @ApiResponse({ status: 401, description: 'Unauthorized.' })
  // @ApiOperation({ summary: 'Retrieve role privileges by role privilege ID', description: 'Retrieve role privilege.' })
  // @ApiParam({ name: 'rolePrivilegeId', description: 'Role privilege ID.' })
  // @ApiBearerAuth()
  // @UseGuards(AuthJwtGuard)
  // findOne(@Param('rolePrivilegeId') rolePrivilegeId: string) {
  //   const objId = validateObjectId(rolePrivilegeId);
  //   return this.rolePrivilegeService.findOne(objId);
  // }

  // @Delete(':rolePrivilegeId')
  // @ApiResponse({ status: 200, description: 'Privileges removed successfully.' })
  // @ApiResponse({ status: 401, description: 'Unauthorized.' })
  // @ApiResponse({ status: 404, description: 'Role privilege not found.' })
  // @ApiParam({ name: 'rolePrivilegeId', description: 'Role privilege ID to remove.' })
  // @ApiOperation({ summary: 'Remove privileges.', description: 'Deletes role privilege.' })
  // @ApiBearerAuth()
  // @UseGuards(AuthJwtGuard)
  // remove(@Param('rolePrivilegeId') rolePrivilegeId: string) {
  //   const objId = validateObjectId(rolePrivilegeId);
  //   return this.rolePrivilegeService.remove(objId);
  // }

  // @Get(':roleId/role')
  // @ApiResponse({ status: 200, description: 'Role privileges for roleId retrieved successfully.' })
  // @ApiResponse({ status: 404, description: 'Role not found.' })
  // @ApiResponse({ status: 401, description: 'Unauthorized.' })
  // @ApiOperation({ summary: 'Retrieve privileges by role ID', description: 'Retrieve all privileges assigned to a role.' })
  // @ApiParam({ name: 'roleId', description: 'Role ID to retrieve privileges for.' })
  // @ApiBearerAuth()
  // @UseGuards(AuthJwtGuard)
  // findPrivilegeByRole(@Param('roleId') roleId: string) {
  //   validateObjectId(roleId);
  //   return this.rolePrivilegeService.findPrivilegeByRole(roleId);
  // }
}
