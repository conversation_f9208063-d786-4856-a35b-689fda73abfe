import { <PERSON>, Get, Post, Body, Patch, Param, Delete, UseGuards, Logger, NotFoundException, BadRequestException, Req } from '@nestjs/common';
import { IdentifierService } from './identifier.service';
import { CreateIdentifierDto } from './dto/create-identifier.dto';
import { UpdateIdentifierDto } from './dto/update-identifier.dto';
import { ApiOperation, ApiBearerAuth, ApiResponse, ApiQuery, ApiParam, ApiTags } from '@nestjs/swagger';
import { Roles } from 'src/auth/decorators/roles.decorator';
import { Role } from 'src/auth/enums/role.enum';
import { AuthJwtGuard } from 'src/auth/guards/auth-jwt/auth-jwt.guard';
import { RolesGuard } from 'src/auth/guards/roles/roles.guard';
import { validateObjectId } from 'src/utils/validation.utils';

@Controller('')
@ApiTags('Identifiers')
export class IdentifierController {

  private readonly logger = new Logger(IdentifierController.name);

  constructor(private readonly identifierService: IdentifierService) { }

  @Post()
  @ApiOperation({
    summary: 'Create a new identifier',
    description: `This endpoint allows you to create a new identifier. Accessible only to users with role "${Role.Admin}".`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin)
  @Roles()
  @ApiResponse({ status: 201, description: 'The identifier is created.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Data.' })
  @ApiResponse({ status: 401, description: 'Unauthorized access. Authentication is required.' })
  @ApiResponse({ status: 403, description: `Forbidden. Only users with role "${Role.Admin}" are permitted to use this endpoint.` })
  create(@Body() createIdentifierDto: CreateIdentifierDto ,@Req() req: any) {
    return this.identifierService.create(createIdentifierDto, req.user);
  }

  @Get('all')
  @ApiOperation({
    summary: 'Retrieve all identifiers',
    description: `This endpoint returns a list of all identifiers. Accessible only to users with role "${Role.Admin}"`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin)
  @Roles()
  @ApiResponse({ status: 200, description: 'All identifiers are retrieved.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Data.' })
  @ApiResponse({ status: 401, description: 'Unauthorized access. Authentication is required.' })
  @ApiResponse({ status: 403, description: `Forbidden. Only users with role "${Role.Admin}" are permitted to use this endpoint.` })
  findAll() {
    return this.identifierService.findAll();
  }

  @Get(':regionId/find-all-by-region')
  @ApiOperation({
    summary: 'Retrieve all identifiers by region',
    description: `This endpoint returns a list of all identifiers by a region Id. Accessible by everyone.`
  })
  // @ApiBearerAuth()
  // @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager)
  @ApiResponse({ status: 200, description: 'All identifiers are retrieved.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Data.' })
  @ApiResponse({ status: 401, description: 'Unauthorized access. Authentication is required.' })
  // @ApiResponse({ status: 403, description: `Forbidden. Only users with role "${Role.SuperAdmin}" are permitted to use this endpoint.` })
  @ApiParam({ name: 'regionId', description: 'ID of the region' })
  async findAllByRegion(@Param('regionId') regionId: string) {
    this.logger.log(`Received regionId: ${regionId}`);

    // this.logger.log(`Validated regionObjId: ${regionObjId}`);
    try {
      const regionObjId = validateObjectId(regionId);
      const identifiers = await this.identifierService.findAllByRegion(regionId);
      return identifiers;
    } catch (error) {
      if (error instanceof BadRequestException) {
        // Handle BadRequestException from validateObjectId
        this.logger.error(`Invalid regionId: ${error.message}`);
        throw new BadRequestException(`Invalid regionId: ${error.message}`);
      } else {
        // Handle other errors
        this.logger.error(`Error finding identifiers: ${error.message}`);
        throw error; // Let NestJS handle the error response
      }
    }
  }

  @Get(':identifierId')
  @ApiOperation({
    summary: 'Retrieve an identifier by Id',
    description: `This endpoint retrieves an identifier by its Id. Accessible only to users with role "${Role.Admin}"`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin)
  @Roles()
  @ApiResponse({ status: 200, description: 'Identifier is retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized access. Authentication is required.' })
  @ApiResponse({ status: 403, description: `Forbidden. Only users with role "${Role.Admin}" are permitted to use this endpoint.` })
  @ApiResponse({ status: 404, description: 'Identifier not found.' })
  @ApiParam({ name: 'identifierId', description: 'ID of the identifier' })
  findOne(@Param('identifierId') identifierId: string) {
    const identifierObjId = validateObjectId(identifierId);
    return this.identifierService.findById(identifierObjId);
  }

  @Patch(':identifierId')
  @ApiOperation({
    summary: 'Update an identifier by Id',
    description: `This endpoint updates an identifier by Id. Accessible only to users with roles "${Role.Admin}".`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin)
  @Roles()
  @ApiResponse({ status: 200, description: 'Identifier is updated.' })
  @ApiResponse({ status: 401, description: 'Unauthorized access. Authentication is required.' })
  @ApiResponse({ status: 403, description: `Forbidden. Only users with role "${Role.Admin}" are permitted to use this endpoint.` })
  @ApiResponse({ status: 404, description: 'Identifier not found.' })
  @ApiParam({ name: 'identifierId', description: 'ID of the identifier' })
  update(@Param('identifierId') identifierId: string, @Body() updateIdentifierDto: UpdateIdentifierDto,@Req() req: any) {
    const identifierObjId = validateObjectId(identifierId);
    return this.identifierService.update(identifierObjId, updateIdentifierDto, req.user);
  }

  @Delete(':identifierId')
  @ApiOperation({
    summary: 'Delete an identifier by Id',
    description: `This endpoint deletes an identifier by Id. Accessible only to users with roles "${Role.Admin}".`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin)
  @Roles()
  @ApiResponse({ status: 200, description: 'Identifier is deleted' })
  @ApiResponse({ status: 401, description: 'Unauthorized access. Authentication is required.' })
  @ApiResponse({ status: 403, description: `Forbidden. Only users with role "${Role.Admin}" are permitted to use this endpoint.` })
  @ApiResponse({ status: 404, description: 'Identifier not found.' })
  @ApiParam({ name: 'identifierId', description: 'ID of the identifier' })
  remove(@Param('identifierId') identifierId: string, @Req() req: any) {
    const identifierObjId = validateObjectId(identifierId);
    return this.identifierService.delete(identifierObjId,req.user);
  }
}
