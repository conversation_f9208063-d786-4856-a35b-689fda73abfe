import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty, IsEnum, IsOptional, IsBoolean, IsArray } from "class-validator";
import { CommentDto } from "src/common/dto/comment.dto";
import { Status } from "src/shared/constants";


export class ChangeStatusJobDto {

    @ApiProperty({
        type: Boolean,
        required: true,
        description: 'Status of the Job',
    })
    @IsNotEmpty()
    @IsBoolean()
    isOpen: boolean

    @ApiProperty({
        type: Boolean,
        default: false,
    })
    @IsNotEmpty()
    @IsBoolean()
    isDraft: boolean

    @ApiProperty({
        type: [String],
        required: false,
        description: 'Array of job IDs to update the status',
    })
    @IsOptional() 
    @IsArray({ message: 'jobIds must be an array' }) 
    jobIds?: string[];

    // @ApiProperty({
    //     type: CommentDto,
    //     required: false
    // })
    // @IsOptional()
    // comment?: CommentDto;
}
