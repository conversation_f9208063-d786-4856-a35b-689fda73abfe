import { HttpException, HttpStatus, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { CreateClientDto } from './dto/create-client.dto';
import { UpdateClientDto } from './dto/update-client.dto';
import { ConfigService } from '@nestjs/config';
import { InjectModel } from '@nestjs/mongoose';
import { Client, ClientDocument } from './schemas/client.schema';
import { Model, Types } from 'mongoose';

@Injectable()
export class ClientService {
  private readonly logger = new Logger(ClientService.name);

  constructor(private configService: ConfigService, @InjectModel(Client.name) private clientModel: Model<Client>) {

  }

  async create(createClientDto: CreateClientDto) {
    try {

      this.logger.debug('Checking for existing client with the same name. ');
      const existingClient = await this.clientModel.findOne({
        name: createClientDto.name.trim(),
      })

      if (!existingClient)
        this.logger.debug('Creating a new client document');

      const createdClient = new this.clientModel(createClientDto);
      await createdClient.save();
    } catch (error) {
      if (error.code === 11000) { // 11000 is the error code for duplicate key error in MongoDB
        this.logger.error('Duplicate key error', error);
        throw new HttpException('Client with this name already exists', HttpStatus.CONFLICT);
      } else {
        this.logger.error('Error creating client', error);
        throw new HttpException('Error creating client', HttpStatus.INTERNAL_SERVER_ERROR);
      }
    }
  }


  async findAll(page: number, limit: number): Promise<ClientDocument[]> {
    this.logger.debug('Initiating findAll method');
    return this.clientModel.find({ isDeleted: false })
      .populate({ path: 'createdBy', select: '_id roles firstName' })
      .skip((page - 1) * limit)
      .limit(limit)
      .exec();
  }

  async findClientById(id: Types.ObjectId) {
    const client = await this.clientModel.findById(id);
    if (!client || client.isDeleted) {
      throw new NotFoundException(`Client not found with ID ${id}`);
    }
    return client;
  }


  async findOne(id: Types.ObjectId) {
    try {
      return await (this.findClientById(id));
    } catch (error) {
      this.logger.error(error);
      this.logger.error(`An error occurred in fetching client by Id ${id}. ${error?.client}`);
      throw error;
    }
  }

  
  async findAllWithSoftDelete(page: number, limit: number): Promise<ClientDocument[]> {
    return this.clientModel.find({ isDeleted: true })
      .populate({ path: 'createdBy', select: '_id roles firstName' })
      .skip((page - 1) * limit)
      .limit(limit)
      .exec();
  }



  async update(id: Types.ObjectId, updateClientDto: UpdateClientDto) {

    try {
      await this.findClientById(id);
      const updatedClient = await this.clientModel.findByIdAndUpdate(id, updateClientDto, { new: true });
      return updatedClient;
    } catch (error) {
      this.logger.error(error);
      this.logger.error(`An error occurred in fetching client by Id ${id}. ${error?.client}`);
      throw error;
    }

  }

  async moveClient(clientId: Types.ObjectId, userId: Types.ObjectId, comment: string, attachments?: string[]) {
    try {
      await this.findClientById(clientId);
      const updatedClient = this.clientModel.findByIdAndUpdate(
        clientId,
        { ownedBy: userId },
        { new: true }
      );
      // this.emitEvent('client.moved', updatedClient);
      return updatedClient;
    } catch (error) {
      this.logger.error(error);
      this.logger.error(`An error occurred in fetching client by Id ${userId}. ${error?.client}`);
      throw error;
    }
  }

  async changeStatus(id: Types.ObjectId, status: string, comment: string, attachments?: string[]) {
    try {
      const client = await this.findClientById(id);
      client.status = status;
      await client.save();
      // this.emitEvent('account.statuschanged', account);
      return client;
    } catch (error) {
      this.logger.error(error);
      this.logger.error(`An error occurred in fetching account by Id ${id}. ${error?.account}`);
      throw error;
    }

  }

  async remove(id: Types.ObjectId) {

    try {
      const client = await this.clientModel.findById(id);
      if (!client) {
        throw new NotFoundException(`Client not found with ID ${id}`);
      }
      return this.clientModel.findByIdAndDelete(id);
    } catch (error) {
      this.logger.error(error);
      this.logger.error(`An error occurred in fetching client by Id ${id}. ${error?.client}`);
      throw error;
    }
  }

  async restoreSoftDeletedClients(id: Types.ObjectId) {
    const client = await this.clientModel.findById(id);
    if (!client || !client.isDeleted) {
      throw new NotFoundException(`Client not found with ID ${id}`);
    }
    client.isDeleted = false;
    await client.save();

    return client;
  }

  async delete(id: Types.ObjectId) {
    try {
      const client = await this.findClientById(id);
      client.isDeleted = true;
      await client.save();
      return client;
    }
    catch (error) {
      this.logger.error(error);
      this.logger.error(`An error occurred in fetching client by Id ${id}. ${error?.client}`);
      throw error;
    }
  }

  deleteAll() {
    return this.clientModel.deleteMany();
  }

}
