import { BadRequestException, Injectable, InternalServerErrorException, Logger, NotFoundException } from '@nestjs/common';
import { CreateStageDto } from './dto/create-stage.dto';
import { UpdateStageDto } from './dto/update-stage.dto';
import { InjectModel } from '@nestjs/mongoose';
import { Stage, StageDocument, StageSchema } from './schemas/stage.schema';
import { Model, Types } from 'mongoose';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class StageService {

  
  private readonly logger = new Logger(StageService.name);
  constructor(private configService: ConfigService,
    @InjectModel(Stage.name) private stageModel: Model<Stage>,
  ) { }
  
  async create(createStageDto: CreateStageDto): Promise<StageDocument> {
    try {
      const createdStage = new this.stageModel(createStageDto);
      await createdStage.save();
      return createdStage;
    } catch (error) {
      this.logger.log(`${JSON.stringify(error, null, 2)}`);
      if (error.name === 'ValidationError') {
        this.logger.error(`Validation failed when creating a stage. ${error.message}`);
        throw new BadRequestException(`Invalid data: ${error.message}`);
      }
      this.logger.error(`Failed to create a stage. ${error.message}`);
      throw new InternalServerErrorException(`Error when creating a stage. ${error?.message}`);
    }
  }

  async findAll(): Promise<StageDocument[]> {
    try {
      const stages = await this.stageModel.find().exec();
  
       // Ensure jobApplicationsCount is never negative or undefined
       stages.forEach((stage) => {
        if (stage.jobApplicationsCount === undefined || stage.jobApplicationsCount === null || stage.jobApplicationsCount < 0) {
          stage.jobApplicationsCount = 0;
        }
      });
  
      return stages;
    } catch (error) {
      this.logger.error(`An error occurred while fetching stages: ${error.message}`);
      throw new InternalServerErrorException(`Error fetching stages: ${error.message}`);
    }
  }
  

  async findOne(stageId: Types.ObjectId): Promise<StageDocument>   {
    try {
      const stage = await this.stageModel.findById(stageId).exec();
      if (!stage) {
        throw new NotFoundException(`The stage with id: "${stageId}" doesn't exist.`);
      }
      if (stage.jobApplicationsCount === undefined || stage.jobApplicationsCount === null || stage.jobApplicationsCount < 0) {
        stage.jobApplicationsCount = 0;
      }
  
      return stage;
    }
    catch (error) {
      this.logger.error(`An error occurred in fetching stage by Id ${stageId}. ${error?.message}`);
      throw error;

    }
  }

  async update(stageId: Types.ObjectId, updateStageDto: UpdateStageDto) {
    try {
      const stage = await this.stageModel.findById(stageId).exec();
      if (!stage) {
        throw new NotFoundException(`The stage with id: "${stageId}" doesn't exist.`);
      }
      const updatedStage = await this.stageModel.findByIdAndUpdate(stageId, updateStageDto, { new: true })
      return updatedStage;
    }
    catch (error) {
      this.logger.error(`An error occurred while updating stage by ID ${stageId}. ${error?.message}`)
      throw error;
    }
  }

  async remove(stageId:  Types.ObjectId) {
    try {
      const stage = await this.stageModel.findById(stageId).exec();
      if (!stage) {
        throw new NotFoundException(`The stage with id: "${stageId}" doesn't exist.`);
      }
      return await this.stageModel.findByIdAndDelete(stageId);
    }
    catch (error) {
      this.logger.error(`An error occurred while deleting stage by ID ${stageId}. ${error?.message}`);
      throw error;
    }
  }
}
