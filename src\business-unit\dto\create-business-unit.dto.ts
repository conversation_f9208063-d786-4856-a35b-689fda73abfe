import { Api<PERSON>ideProperty, ApiProperty } from "@nestjs/swagger";
import { IsArray, IsBoolean, IsEnum, IsMongoId, IsNotEmpty, IsNumber, IsOptional, IsString, <PERSON>, <PERSON>, <PERSON> } from "class-validator";
import { Transform, TransformFnParams, Type } from "class-transformer";
import { sanitizeWithStyle } from "src/utils/sanitize-with-style";
import { BusinessUnitDocument } from "../schemas/business-unit.schema";
import { BusinessUnitType } from "src/shared/constants";

export class CreateBusinessUnitDto {

  @ApiProperty({
    type: String,
    required: true,
    description: 'Name of business unit',
  })
  @IsNotEmpty()
  @IsString()
  @MaxLength(100)
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  label: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Key of business unit',
  })
  @IsNotEmpty()
  @IsString()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  key: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'ID of the parent business unit (required for child units)',
  })
  @IsMongoId()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  @IsOptional()
  parentBusinessUnit?: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'The name of the organization to which the business unit belongs.',
  })
  @IsNotEmpty()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  org: string;

  @ApiProperty({
    type: Boolean,
    required: false,
    default: false
  })
  @IsOptional()
  isDeleted?: boolean;

  @ApiHideProperty()
  createdBy: string;

  @ApiProperty({ type: [String], description: 'List of Children Business unit IDs', required: false })
  @IsArray()
  @IsOptional()
  @IsMongoId({ each: true }) // Validate each item as a MongoDB ObjectId
  children?: string[];

  @ApiProperty({
    type: Number,
    required: false,
    default: 1
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  level?: number;

  @ApiProperty({
    type: String,
    required: true,
    default: BusinessUnitType.RECRUITMENT,
    enum: BusinessUnitType,
    description: "Business-unit type"
  })
  @IsNotEmpty()
  @IsString()
  @IsEnum(BusinessUnitType)
  type: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'The name of the head of the business unit.',
  })
  @IsNotEmpty()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  departmentHead: string;

  @ApiProperty({
    type: Boolean,
    required: false,
    default: false
  })
  @IsOptional()
  isDemand?: boolean;

  @ApiProperty({
    type: Boolean,
    required: false,
    default: false
  })
  @IsOptional()
  isSupply?: boolean;

}

