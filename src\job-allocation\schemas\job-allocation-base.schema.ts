import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Types } from 'mongoose';
import { Job } from 'src/job/schemas/job.schema';
import { BasicUser } from 'src/user/schemas/basic-user.schema';
import { Priority } from 'src/shared/constants';
import { BusinessUnit } from 'src/business-unit/schemas/business-unit.schema';

export type JobAllocationBaseDocument = HydratedDocument<JobAllocationBase>;

@Schema({
  timestamps: true,
  discriminatorKey: 'kind', // Field used for discriminators
})
export class JobAllocationBase {
  @Prop({ required: true, type: Types.ObjectId, ref: 'Job' })
  job: Job;

  @Prop({ type: Date, required: true })
  startDate: Date;

  @Prop({ type: Date, required: false })
  dueDate?: Date;

  @Prop({ required: true, type: Number, default: 1 })
  targetProfiles: number;

  @Prop({
    type: String,
    required: true,
    trim: true,
    default: Priority.HIGH,
    enum: Priority,
  })
  priority: Priority;

  @Prop({ required: false, default: false, type: Boolean })
  isDeleted?: boolean;

  @Prop({ required: true, default: false, type: Boolean })
  untilPositionClosed: boolean;

  // The type field will be handled by the discriminator key
  kind: string; // This is just for TypeScript, not stored in MongoDB

  @Prop({ required: true, type: Types.ObjectId, ref: 'BasicUser' })
  createdBy: BasicUser;

  @Prop({ required: false, type: Types.ObjectId, ref: 'BasicUser' })
  leadId?: BasicUser;

  @Prop({ required: false, type: Types.ObjectId, ref: 'BusinessUnit' })
  departmentId?: BusinessUnit;

}

export const JobAllocationBaseSchema = SchemaFactory.createForClass(JobAllocationBase);
