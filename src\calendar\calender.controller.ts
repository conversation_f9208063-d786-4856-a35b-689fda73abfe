// src/calendar/calendar.controller.ts
import { Body, Controller, Get, Post, Param, Patch, Delete, Query, Req, UseGuards, HttpCode, HttpStatus, Res, HttpException } from '@nestjs/common';
import { ApiTags, ApiBearerAuth, ApiOperation, ApiResponse, ApiParam, ApiQuery, ApiBody } from '@nestjs/swagger';
import { CalendarService } from './calendar.service';
import { AuthJwtGuard } from 'src/auth/guards/auth-jwt/auth-jwt.guard';
import { RolesGuard } from 'src/auth/guards/roles/roles.guard';
import { Roles } from 'src/auth/decorators/roles.decorator';
import { CreateCalendarDto } from './dto/create-calender.dto';
import { validateObjectId } from 'src/utils/validation.utils';
import { UpdateCalendarDto } from './dto/update-calendar.dto';
import { ZoomWebhookDto } from './dto/zoom-webhook.dto';
import { Response } from 'express';


@Controller('')
@ApiTags('Calendar')
export class CalendarController {
    constructor(private readonly calendarService: CalendarService) { }

    @Post()
    @ApiOperation({ summary: 'Create calendar event', description: 'Allows creation of a new calendar event.' })
    @ApiResponse({ status: 201, description: 'Calendar event created.' })
    @ApiResponse({ status: 400, description: 'Bad Request' })
    @ApiResponse({ status: 401, description: 'Unauthorized.' })
    @ApiBearerAuth()
    @UseGuards(AuthJwtGuard, RolesGuard)
    @Roles()
    create(@Req() req: any, @Body() createCalendarDto: CreateCalendarDto) {
        createCalendarDto.createdBy = req.user._id;
        if (req.user.org) {
            createCalendarDto.org = req.user.org._id;
        }
        return this.calendarService.create(createCalendarDto, req.user);
    }

    @Get('all')
    @ApiOperation({ summary: 'Get all calendar events', description: 'Returns all calendar events.' })
    @ApiResponse({ status: 200, description: 'Calendar events retrieved.' })
    @ApiResponse({ status: 401, description: 'Unauthorized.' })
    @ApiQuery({ name: 'orgId', description: 'ID of the organization', required: false })
    @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number', example: 1 })
    @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of items per page', example: 10 })
    @ApiQuery({ name: 'title', required: false, type: String, description: 'title of the calendar', example: "Tracker meeting" })
    @ApiQuery({ name: 'email', required: false, type: String, description: 'Email of the invitee', example: "<EMAIL>" })
    @ApiBearerAuth()
    @UseGuards(AuthJwtGuard, RolesGuard)
    @Roles()
    findAll(@Req() req: any, @Query('orgId') orgId?: string, @Query('title') title?: string, @Query('email') email?: string, @Query('page') page: number = 1, @Query('limit') limit: number = 10) {
        if (!orgId && req.user.org) {
            orgId = req.user.org._id;
        }
        return this.calendarService.findAll(page, limit, orgId, title, email);
    }

    @Get('user')
    @ApiOperation({ summary: 'Get user calendar events', description: 'Returns calendar events created by the logged-in user.' })
    @ApiResponse({ status: 200, description: 'Calendar events retrieved.' })
    @ApiResponse({ status: 401, description: 'Unauthorized.' })
    @ApiQuery({ name: 'page', required: false, type: Number, example: 1 })
    @ApiQuery({ name: 'limit', required: false, type: Number, example: 10 })
    @ApiBearerAuth()
    @UseGuards(AuthJwtGuard, RolesGuard)
    @Roles()
    findByUser(@Req() req: any, @Query('page') page: number = 1, @Query('limit') limit: number = 10) {
        return this.calendarService.findByUser(page, limit, req.user);
    }

    @Get(':calendarId')
    @ApiOperation({ summary: 'Get calendar event by ID', description: 'Fetch a specific calendar event by its ID.' })
    @ApiResponse({ status: 200, description: 'Calendar event retrieved.' })
    @ApiResponse({ status: 404, description: 'Calendar event not found.' })
    @ApiResponse({ status: 401, description: 'Unauthorized.' })
    @ApiBearerAuth()
    @UseGuards(AuthJwtGuard, RolesGuard)
    @Roles()
    findOne(@Param('calendarId') calendarId: string) {
        const objId = validateObjectId(calendarId);
        return this.calendarService.findOne(objId);
    }

    @Patch(':calendarId')
    @ApiOperation({ summary: 'Update calendar event', description: 'Update a specific calendar event.' })
    @ApiResponse({ status: 200, description: 'Calendar event updated.' })
    @ApiResponse({ status: 404, description: 'Calendar event not found.' })
    @ApiResponse({ status: 401, description: 'Unauthorized.' })
    @ApiBearerAuth()
    @UseGuards(AuthJwtGuard, RolesGuard)
    @Roles()
    update(@Req() req: any, @Param('calendarId') calendarId: string, @Body() updateCalendarDto: UpdateCalendarDto) {
        const objId = validateObjectId(calendarId);
        return this.calendarService.update(objId, updateCalendarDto, req.user);
    }

    @Delete(':calendarId')
    @ApiOperation({ summary: 'Delete calendar event', description: 'Delete a specific calendar event.' })
    @ApiResponse({ status: 200, description: 'Calendar event deleted.' })
    @ApiResponse({ status: 404, description: 'Calendar event not found.' })
    @ApiResponse({ status: 401, description: 'Unauthorized.' })
    @ApiBearerAuth()
    @UseGuards(AuthJwtGuard, RolesGuard)
    @Roles()
    remove(@Param('calendarId') calendarId: string) {
        const objId = validateObjectId(calendarId);
        return this.calendarService.remove(objId);
    }

    @Get('count')
    @ApiOperation({ summary: 'Get meeting count', description: 'Fetch the total count of meetings with optional filters.' })
    @ApiBearerAuth()
    @UseGuards(AuthJwtGuard)
    // @Roles()
    @ApiResponse({ status: 200, description: 'Meeting count fetched successfully' })
    @ApiResponse({ status: 401, description: 'Unauthorized' })
    @ApiResponse({ status: 403, description: `Forbidden. Only users with roles "BUHead", "ResourceManager", "DeliveryManager", "TeamLead", "AccountManager", "Recuriter", and "TeamMember" are permitted to use this endpoint.` })
    @ApiResponse({ status: 500, description: 'Internal server error' })
    @ApiQuery({ name: 'orgId', description: 'ID of the organization', required: false })
    @ApiQuery({ name: 'email', required: false, type: String, description: 'Email of the invitee', example: "<EMAIL>" })
    @ApiQuery({ name: 'title', required: false, type: String, description: 'title of the calendar', example: "Tracker meeting" })
    async count(@Req() req: any, @Query('orgId') orgId?: string, @Query('email') email?: string, @Query('title') title?: string) {
        if (!orgId && req.user.org) {
            orgId = req.user.org._id; // Default to the logged-in user's organization
        }
        return await this.calendarService.count(orgId, email, title);
    }

    @Get('validate-link')
    @ApiOperation({ summary: 'Validate a meeting link', description: 'Validates if a meeting link is active and valid' })
    @ApiQuery({ name: 'meetingLink', type: String, required: true, description: 'The unique meeting link' })
    @ApiResponse({ status: 200, description: 'Meeting link is valid' })
    @ApiResponse({ status: 400, description: 'Invalid meeting link' })
    @ApiResponse({ status: 404, description: 'Meeting not found' })
    async validateMeetingLink(@Query('meetingLink') meetingLink: string) {
        return await this.calendarService.findOneByLink(meetingLink);
    }

    @Get('zoom/webhook')
    @ApiOperation({ summary: 'Zoom webhook validation test endpoint' })
    async validateZoomWebhookTest(@Query('plainToken') plainToken: string, @Res() res: Response) {
        return this.calendarService.validateZoomWebhook(plainToken);

    }

    @Post('zoom/webhook')
    @ApiOperation({ summary: 'Handle Zoom Webhook Events including validation' })
    @ApiBody({ type: ZoomWebhookDto })
    @ApiResponse({ status: 200, description: 'Webhook handled successfully' })
    async handleWebhook(@Body() body: ZoomWebhookDto, @Res() res: Response) {
        if (body.event === 'endpoint.url_validation') {
            this.calendarService.validateZoomWebhook(body.payload.plainToken);
        }
        return await this.calendarService.handleZoomWebhook(body);
    }

    @Get(':calendarId/recordings')
    @ApiOperation({
        summary: 'Get Zoom meeting recordings',
        description: 'Fetch video files, transcripts, sharable link, and passcode for a Zoom meeting.',
    })
    @ApiParam({ name: 'calendarId', description: 'Calendar Event ID' })
    @ApiResponse({ status: 200, description: 'Recording details retrieved successfully.' })
    @ApiResponse({ status: 404, description: 'Calendar event or recording not found.' })
    @ApiResponse({ status: 400, description: 'Bad request or Zoom error.' })
    @ApiResponse({ status: 401, description: 'Unauthorized.' })
    @ApiBearerAuth()
    @UseGuards(AuthJwtGuard)
    // @Roles()
    async getRecordings(@Param('calendarId') calendarId: string) {
        const objId = validateObjectId(calendarId);
        return this.calendarService.getZoomRecordings(objId);
    }
}
