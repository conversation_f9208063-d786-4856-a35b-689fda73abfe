import { PartialType } from '@nestjs/swagger';
import { CreateLeaveManagmentDto } from './create-leave-managment.dto';
import { IsArray, IsOptional, IsString, ValidateNested } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';

export class UpdateLeavePolicyDto {
  @IsOptional()
  @IsString()
  @ApiProperty({ example: 'New Plan Name', required: false })
  planName?: string;

  @IsOptional()
  @ApiProperty({ type: [CreateLeaveManagmentDto], required: false })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateLeaveManagmentDto)
  leaveTypes?: CreateLeaveManagmentDto[];
}

export class UpdateLeaveManagmentDto extends PartialType(CreateLeaveManagmentDto) {}
