// src/invoice/invoice.controller.ts
import { Controller, Get, Post, Body, Param, Patch, Delete, UseGuards, Req, Query } from '@nestjs/common';
import { InvoiceService } from './invoice.service';
import { CreateInvoiceDto } from './dto/create-invoice.dto';
import { UpdateInvoiceDto } from './dto/update-invoice.dto';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { AuthJwtGuard } from 'src/auth/guards/auth-jwt/auth-jwt.guard';
import { Roles } from 'src/auth/decorators/roles.decorator';
import { validateObjectId } from 'src/utils/validation.utils';
import { ApproveRejectInterimBgvDto } from 'src/offer/dto/approve-reject-interim-bgv.dto';
import { GetInvoicesQueryDto } from './dto/get-invoices-query.dto';

@Controller('')
@ApiTags('invoices')
export class InvoiceController {
  constructor(private readonly invoiceService: InvoiceService) { }

  @Get()
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  @Roles()
  findAll(@Query() query: GetInvoicesQueryDto,@Req() req : any) {
    return this.invoiceService.findInvoicesByBilledOrg(req.user,query);
  }

  @Get(':id')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  @Roles()
  findOne(@Param('id') id: string) {
    return this.invoiceService.findOne(id);
  }

  @Post()
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  // @Roles(Role.BUHead, Role.TeamLead, Role.DeliveryManager, Role.Recruiter, Role.Vendor)
  @Roles()
  @ApiOperation({ summary: 'Create a Bgv handler', description: `This endpoint for creating a job application. This is only accessible for "BUHead", "TeamLead", "DeliveryManager" and "Recruiter".` })
  create(@Req() req: any,@Body() createInvoiceDto: CreateInvoiceDto) {
    return this.invoiceService.create(req.user,createInvoiceDto);
  }

  @Patch(':id')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  @Roles()
  update(@Param('id') id: string, @Body() updateInvoiceDto: UpdateInvoiceDto) {
    return this.invoiceService.update(id, updateInvoiceDto);
  }

  @Delete(':id')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  @Roles()
  delete(@Param('id') id: string) {
    return this.invoiceService.delete(id);
  }

  @Get(':applicationId/rateCard')
  @ApiOperation({
    summary: 'Retrieve an rate Card by Application Id',
    description: 'This endpoint retrieves an rate Card by its Application Id. This is accessible only for everyone..'
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  @Roles()
  findRateCardByApplicationId(@Req() req: any, @Param('applicationId') applicationId: string) {
    const objId = validateObjectId(applicationId);
    return this.invoiceService.findRateCardByApplicationId(req.user, objId);
  }

  @Get(':applicationId/invoiceDetails')
  @ApiOperation({
    summary: 'Retrieve an rate Card by Application Id',
    description: 'This endpoint retrieves an rate Card by its Application Id. This is accessible only for everyone..'
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  @Roles()
  findAddressByApplicationId(@Req() req: any, @Param('applicationId') applicationId: string) {
    const objId = validateObjectId(applicationId);
    return this.invoiceService.findInvoiceDetailsByApplicationId(req.user, objId);
  }

  @Get(':employeeId/:timesheetId/invoiceDetails')
@ApiOperation({
  summary: 'Retrieve invoice details by Employee ID and Timesheet ID',
  description: 'This endpoint retrieves invoice details based on the employee and timesheet. Accessible to authenticated users.',
})
@ApiBearerAuth()
@UseGuards(AuthJwtGuard)
@Roles()
async findInvoiceDetailsByEmployeeAndTimesheetId(
  @Req() req: any,
  @Param('employeeId') employeeId: string,
  @Param('timesheetId') timesheetId: string
) {
  const validatedEmployeeId = validateObjectId(employeeId);
  const validatedTimesheetId = validateObjectId(timesheetId);

  return this.invoiceService.findInvoiceDetailsByEmployeeAndTimesheetId(
    req.user,
    validatedEmployeeId,
    validatedTimesheetId
  );
}


  @Get(':applicationId/findInvoiceEligibility')
  @ApiOperation({
    summary: 'Retrieve an rate Card by Application Id',
    description: 'This endpoint retrieves an rate Card by its Application Id. This is accessible only for everyone..'
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  @Roles()
  findInvoiceEligibilityByApplicationId(@Req() req: any, @Param('applicationId') applicationId: string) {
    const objId = validateObjectId(applicationId);
    return this.invoiceService.findInvoceEligibilityApplicationId(req.user, objId);
  }

  
  @Get('raise-bgv-mail')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  async raiseBgvMail(
    @Req() req : any,
    @Query('billedByOrgId') billedByOrgId: string,
    @Query('billedToOrgId') billedToOrgId: string,
    @Query('jobApplicationId') jobApplicationId: string,
  ): Promise<string> {
    return this.invoiceService.raiseBgvMail(req.user, billedByOrgId, billedToOrgId, jobApplicationId);
  }

  @Patch(':applicationId/interim-bgv-decision')
    @ApiOperation({
      summary: 'Retrieve an bgv documents by Application Id',
      description: 'This endpoint retrieves an bgv documents by its Application Id. This is accessible only for everyone..'
    })
    @ApiBearerAuth()
    @UseGuards(AuthJwtGuard)
    // @Roles(Role.Admin, Role.Recruiter, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager)
    @Roles()
    approveCandidateInterimBgv(@Req() req: any,@Param('applicationId') applicationId: string,@Body() payload: ApproveRejectInterimBgvDto) {
      const objId = validateObjectId(applicationId);
      return this.invoiceService.raiseInvoiceOnBgvApproval(req.user,objId,payload);
    }

}
