import { ApiHideProperty, ApiProperty } from "@nestjs/swagger";
import { Transform, TransformFnParams, Type } from "class-transformer";
import { IsDate, IsEnum, IsISO8601, IsMongoId, IsNotEmpty, IsOptional, IsString, Length, Matches } from "class-validator";
import { sanitizeWithStyle } from "src/utils/sanitize-with-style";
import { BlogStatus, BlogType } from "src/shared/constants";


export class CreateBlogDto {

    @ApiProperty({
        type: String,
        required: true,
        description: 'Blog title',
    })
    @IsString()
    @IsNotEmpty()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    title: string;

    @ApiProperty({
        type: String,
        required: true, 
        description: 'Type of the blog (e.g., Freelancers/Companies/Agencies)',
        default: BlogType.COMPANIES,
        enum: BlogType
    })
    @IsString()
    @IsNotEmpty()
    @IsEnum(BlogType)
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    type: string;


    @ApiProperty({
        type: String,
        required: false,
        description: 'Blog description',
    })
    @IsString()
    @IsOptional()
    // @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    description?: string;


    @ApiProperty({
        type: String,
        required: true,
        description: 'Status of the blog (e.g., Active/Inactive)',
        default: BlogStatus.ACTIVE,
        enum: BlogStatus
    })
    @IsString()
    @IsNotEmpty()
    @IsEnum(BlogStatus)
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    status: string;

    @ApiHideProperty()
    @IsOptional()
    @IsString()
    createdBy?: string;

}