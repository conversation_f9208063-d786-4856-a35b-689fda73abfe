import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { RecruitementTeamService } from './recruitement-team.service';
import { JwtModule } from '@nestjs/jwt';
import { MongooseModule } from '@nestjs/mongoose';
import { RecruitementTeam, RecruitementTeamSchema } from './schemas/recruitement-team.schema';
import { RecruitementTeamController } from './recruitement-team.controller';
import { EndpointsRolesModule } from 'src/endpoints-roles/endpoints-roles.module';

@Module({
  imports: [
    ConfigModule,
    JwtModule, EndpointsRolesModule,
    MongooseModule.forFeature([{ name: RecruitementTeam.name, schema: RecruitementTeamSchema }])
  ],
  controllers: [RecruitementTeamController],
  providers: [RecruitementTeamService],
  exports: [RecruitementTeamService]
})
export class RecruitementTeamModule { }