Schema

1. Schema should have timestamps true 
2. Schema should have trim true used for all string-based types
3. The schema should have unique true wherever applicable 
4. Schema should use exported {SchemaName} Document exported ( to be used in return types in service layers) 
5. Schema should have required false and ?: in sync wherever applicable (for optional fields)
6. Schema should have required true and NOT ?: wherever applicable


DTO

1.Dto should have class-validators like @IsNotEmpty, @IsString, IsOptional
2.Dto should have decorators like @ApiProperty() etc to make it visible for the swagger
3.Dto should have required true or false


Controller

1. Controller should have  ApiTags, ApiResponse, ApiBearerAuth, ApiOperation.
2. Should use authJwtGuard, and RolesGuard, in @UseGuards  and this has to import from the auth
3. In ApiResponses you should give proper status and it’s descriptions
4. In the description we have to use backticks and in “User”,” admin” etc for the roles.
4. proper names for the methods we are using id’s eg:accountTypeId


Service

Implement try-catch blocks
Initiate logger for every service class and write proper logger statements in the catch blocks of every endpoint
For the Create endpoint, check if the given collection is already present in the database. If yes, then throw the Duplicate key error
While returning all the collections in a findAll endpoint, return as a Promise of Document
While returning all the collections in a findAll endpoint, if the given schema is related to another secondary schema, use populate to retrieve all the fields from the secondary schema and return the collections
While trying to return a particular collection using ID, check whether that particular collection is present and soft-deleted. If it is either not present or soft-deleted then throw a NotFoundException
While trying to update or delete a particular collection, check if that particular collection is present and not soft-deleted.
While trying to restore a particular collection, check if that particular collection is present and soft-deleted.

