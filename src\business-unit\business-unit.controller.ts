import { Controller, Get, Post, Body, Patch, Param, Delete, Req, UseGuards, Query, Logger, BadRequestException, NotFoundException, HttpCode, HttpStatus } from '@nestjs/common';
import { ApiOperation, ApiBearerAuth, ApiResponse, ApiParam, ApiTags, ApiQuery, ApiBody } from '@nestjs/swagger';
import { Roles } from 'src/auth/decorators/roles.decorator';
import { Role } from 'src/auth/enums/role.enum';
import { AuthJwtGuard } from 'src/auth/guards/auth-jwt/auth-jwt.guard';
import { RolesGuard } from 'src/auth/guards/roles/roles.guard';
import { validateObjectId } from 'src/utils/validation.utils';
import { CommentDto } from 'src/common/dto/comment.dto';
import { Types } from 'mongoose';
import { BusinessUnitService } from './business-unit.service';
import { CreateBusinessUnitDto } from './dto/create-business-unit.dto';
import { FilterBusinessUnitsDto } from './dto/filter-business-unit.dto';
import { UpdateBusinessUnitDto } from './dto/update-business-unit.dto';
import { BusinessUnitType } from 'src/shared/constants';
import { FilterUsersDto } from './dto/filter-users.dto';
import { GetDepartmentTreeDto } from './dto/department-tree.dto';

@Controller('')
@ApiTags('Business Units')
export class BusinessUnitController {

  private readonly logger = new Logger(BusinessUnitController.name);

  constructor(private readonly businessUnitService: BusinessUnitService) { }

  @Post()
  @ApiOperation({ summary: 'Creates a new business unit', description: `This endpoint allows you to create a new business unit. This is accessible only for "Admin"` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.Vendor)
  @Roles()
  @ApiResponse({ status: 201, description: 'Business Unit is saved.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Data ' })
  @ApiResponse({ status: 401, description: 'Unauthorized ' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role admin can only use this end point.' })
  create(@Req() req: any, @Body() createBusinessUnitDto: CreateBusinessUnitDto) {
    return this.businessUnitService.create(createBusinessUnitDto, req.user);
  }

  @Get()
  @ApiOperation({ summary: 'Retrieve all business units', description: 'This endpoint returns a list of all business units. This endpoint accessible by "Admin".' })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.Vendor, Role.JobSeeker, Role.Recruiter, Role.DeliveryManager, Role.AccountManager, Role.TeamLead, Role.TeamMember, Role.BUHead, Role.TechPanel, Role.ResourceManager)
  @Roles()
  @ApiResponse({ status: 200, description: 'All business units are retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized ' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role admin can only use this end point.' })
  findAll(@Req() req: any, @Query() query: FilterBusinessUnitsDto) {
    if (!query.org && req.user.org) {
      query.org = req.user.org._id
    }
    return this.businessUnitService.getOnlyActiveBusinessUnits(query);
  }

  @Get(':businessUnitId/descendants')
  @ApiOperation({
    summary: 'Retrieve all IDs of a business unit and its nested child units',
    description: 'This endpoint returns the IDs of a business unit and all its nested child business units.This is accessible only for "Admin"',
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard) // Apply the AuthJwtGuard to ensure only authorized users can access this endpoint
  // @Roles(Role.Admin, Role.Vendor)
  @Roles()
  @ApiResponse({ status: 200, description: 'All business unit IDs (including descendants) retrieved successfully.' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 400, description: 'Invalid business unit ID provided.' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role admin can only use this end point.' })
  async getBusinessUnitDescendants(
    @Param('businessUnitId') businessUnitId: string,
  ) {

    const businessUnitObjId = validateObjectId(businessUnitId)

    // Call the service method to get all the IDs of the business unit and its nested children
    const businessUnitIds = await this.businessUnitService.getAllDepartmentIds(businessUnitObjId);

    // Return the array of business unit IDs
    return businessUnitIds;
  }

  @Get(':businessUnitId')
  @ApiOperation({ summary: 'Retrieve an business unit by Id', description: 'This endpoint returns an business unit by its Id. This is accessible only for "Admin".' })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard) // 
  // @Roles(Role.Admin, Role.Vendor)
  @Roles()
  @ApiResponse({ status: 200, description: 'Business Unit is retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized ' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role admin can only use this end point.' })
  @ApiResponse({ status: 404, description: 'Business Units not found.' })
  findOne(@Param('businessUnitId') businessUnitId: string) {
    const objId = validateObjectId(businessUnitId);
    return this.businessUnitService.findOne(objId);
  }

  @Get('count')
  @ApiResponse({ status: 200, description: `BusinessUnit's count is retrieved.` })
  @ApiResponse({ status: 404, description: `Unable to find business unit's count.` })
  @ApiResponse({ status: 401, description: `Unauthorized. ` })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role admin can only use this end point.' })
  @ApiOperation({ summary: `Retrieve BusinessUnit's count.`, description: `This endpoint returns BusinessUnit's count.This is accessible only for "Admin".` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.Vendor)
  @Roles()
  getBusinessUnitsCount() {
    return this.businessUnitService.getBusinessUnitCounts();
  }

  @Patch(':businessUnitId')
  @ApiOperation({ summary: 'Update an business unit by Id', description: `This endpoint updates an business unit by Id. This is accessible only for "Admin".` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.Vendor)
  @Roles()
  @ApiResponse({ status: 200, description: 'Business Unit is updated.' })
  @ApiResponse({ status: 401, description: 'Unauthorized ' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role admin can only use this end point.' })
  @ApiResponse({ status: 404, description: 'Business Unit not found.' })
  @ApiParam({ name: 'businessUnitId', description: 'ID of the business unit' })
  async update(@Req() req: any, @Param('businessUnitId') businessUnitId: string, @Body() updateBusinessUnitDto: UpdateBusinessUnitDto) {
    const objId = validateObjectId(businessUnitId);
    return await this.businessUnitService.update(objId, updateBusinessUnitDto, req.user);
  }

  @Get(':orgId/tree')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.AccountManager, Role.TeamLead, Role.TeamMember, Role.Recruiter, Role.Vendor, Role.Freelancer)
  @Roles()
  @ApiOperation({ summary: 'Get complete department tree', description: 'This is accessible only for everyone' })
  @ApiResponse({ status: 200, description: 'Successfully retrieved department tree' })
  // @ApiResponse({ status: 403, description: 'Forbidden, user with role admin can only use this end point.' })
  @ApiResponse({ status: 404, description: 'Organization not found' })
  @ApiParam({ name: 'orgId', required: true, description: 'The ID of the organization' })
  async getCompleteDepartmentTree(@Req() req: any,@Param('orgId') orgId: string, @Query() query: GetDepartmentTreeDto) {
    const { type, deptName,isJobAllocation } = query;
    const objId = validateObjectId(orgId);
    const userId = req.user._id;
    const tree = await this.businessUnitService.getCompleteDepartmentTree(userId,orgId, type, deptName,isJobAllocation);
    console.log(orgId || tree.length === 0)
    if (!tree) {
      throw new NotFoundException('Organization not found');
    }
    return tree;
  }

  @Get(':businessUnitId/children')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.Vendor)
  @Roles()
  @ApiOperation({ summary: 'Get children of a specific department', description: 'This is accessible only for "Admin"' })
  @ApiResponse({ status: 200, description: 'Successfully retrieved children' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role admin can only use this end point.' })
  @ApiResponse({ status: 404, description: 'Department not found' })
  @ApiParam({ name: 'businessUnitId', required: true, description: 'The ID of the department' })
  async getChildren(@Param('businessUnitId') businessUnitId: string) {
    const objId = validateObjectId(businessUnitId);
    const children = await this.businessUnitService.populateChildren(objId);
    if (!children) {
      throw new NotFoundException('Department not found');
    }
    return children;
  }

  @Patch(':movingId/move-to-root/:orgId')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Move a business unit by Id to the root level', description: `This endpoint moves a business unit by Id to the root level.This is accessible only for "Admin"` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.Vendor)
  @Roles()
  @ApiParam({ name: 'movingId', required: true, description: 'ID of the business unit to move' })
  @ApiParam({ name: 'orgId', required: true, description: 'ID of the organization' })
  @ApiResponse({ status: 200, description: 'Business unit moved to root level' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role admin can only use this end point.' })
  @ApiResponse({ status: 404, description: 'Business unit not found' })
  @ApiResponse({ status: 500, description: 'Error while moving business unit' })
  @ApiParam({ name: 'movingId', description: 'ID of the business unit' })
  @ApiParam({ name: 'orgId', description: 'ID of the destination unit' })
  async moveBusinessUnitToRoot(
    @Param('movingId') movingId: string,
    @Param('orgId') orgId: string,
    @Req() req: any
  ) {
    validateObjectId(orgId);
    return this.businessUnitService.moveBusinessUnitToRoot(movingId, orgId, req.user);
  }

  @Patch(':businessUnitId/move-to/:destinationId')
  @ApiOperation({ summary: 'Move a business unit by Id to the destination', description: `This endpoint moves a business unit by Id to its destination. This is accessible only for "Admin"` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.Vendor)
  @Roles()
  @ApiResponse({ status: 200, description: 'The business unit has been successfully moved.' })
  @ApiResponse({ status: 400, description: 'Invalid request parameters.' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role admin can only use this end point.' })
  @ApiResponse({ status: 404, description: 'Business unit or destination not found.' })
  @ApiResponse({ status: 500, description: 'Internal server error.' })
  @ApiParam({ name: 'businessUnitId', description: 'ID of the moving business unit' })
  @ApiParam({ name: 'destinationId', description: 'ID of the destination business unit' })
  async move(@Param('businessUnitId') movingId: string, @Param('destinationId') destinationId: string, @Req() req: any) {
    // const keyFormat = /^\d[\d-]*$/;
    // if (!keyFormat.test(destinationKey)) {
    //   throw new BadRequestException('Invalid destination key format. It must start with a digit and contain only digits and dashes.');
    // }
    return await this.businessUnitService.moveBusinessUnitToDestination(movingId, destinationId, req.user)
  }

  @Patch(':businessUnitId/merge/:destinationId')
  @ApiOperation({ summary: 'Merge a business unit by Id to the destination', description: `This endpoint merges a business unit by Id and its children to its destination.This is accessible only for "Admin"` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.Vendor)
  @Roles()
  @ApiResponse({ status: 200, description: 'The business unit has been successfully merged.' })
  @ApiResponse({ status: 400, description: 'Invalid request parameters.' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role admin can only use this end point.' })
  @ApiResponse({ status: 404, description: 'Business unit or destination not found.' })
  @ApiResponse({ status: 500, description: 'Internal server error.' })
  @ApiParam({ name: 'businessUnitId', description: 'ID of the business unit' })
  @ApiParam({ name: 'destinationId', description: 'ID  of the destination business unit' })
  @ApiQuery({ name: 'departmentHead', required: true, type: String, description: 'Id of departmentHead' })
  async mergeBusinessUnit(
    @Param('businessUnitId') businessUnitId: string,
    @Param('destinationId') destinationId: string,
    @Query('departmentHead') departmentHead: string,
    @Req() req: any
  ) {
    // const keyFormat = /^\d[\d-]*$/;
    // if (!keyFormat.test(destinationKey)) {
    //   throw new BadRequestException('Invalid destination key format. It must start with a digit and contain only digits and dashes.');
    // }
    return this.businessUnitService.mergeBusinessUnit(businessUnitId, destinationId,departmentHead,req.user);
  }

  @Get(':businessUnitId/getDepartmentHeads/:destinationId')
  @ApiOperation({ summary: 'Merge a business unit by Id to the destination', description: `This endpoint merges a business unit by Id and its children to its destination.This is accessible only for "Admin"` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.Vendor)
  @Roles()
  @ApiResponse({ status: 200, description: 'The business unit has been successfully merged.' })
  @ApiResponse({ status: 400, description: 'Invalid request parameters.' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role admin can only use this end point.' })
  @ApiResponse({ status: 404, description: 'Business unit or destination not found.' })
  @ApiResponse({ status: 500, description: 'Internal server error.' })
  @ApiParam({ name: 'businessUnitId', description: 'ID of the business unit' })
  @ApiParam({ name: 'destinationId', description: 'ID  of the destination business unit' })
  async getBusinessUnitHeads(
    @Param('businessUnitId') businessUnitId: string,
    @Param('destinationId') destinationId: string,
    @Req() req: any
  ) {
    // const keyFormat = /^\d[\d-]*$/;
    // if (!keyFormat.test(destinationKey)) {
    //   throw new BadRequestException('Invalid destination key format. It must start with a digit and contain only digits and dashes.');
    // }
    return this.businessUnitService.getDepartmentsHeads(businessUnitId, destinationId,req.user);
  }

  @Patch(':businessUnitId/assign-to/:assignTo')
    @ApiOperation({
      summary: 'Move the ownership of an department by Id',
      description: `This endpoint moves the ownership of an department by Id. Accessible only to users with roles "Admin" and "BUHead".`
    })
    @ApiBearerAuth()
    @UseGuards(AuthJwtGuard, RolesGuard)
    // @Roles(Role.Admin, Role.BUHead, Role.Vendor)
    @Roles()
    @ApiResponse({ status: 200, description: 'department ownership moved.' })
    @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
    @ApiResponse({ status: 403, description: `Forbidden. Only users with roles "Admin" and "BUHead" are permitted to use this endpoint.` })
    @ApiResponse({ status: 404, description: 'department not found.' })
    @ApiParam({ name: 'businessUnitId', description: 'ID of the department' })
    @ApiParam({ name: 'assignTo', description: 'ID of the departmentHead' })
    moveDepartment(@Req() req: any, @Param('businessUnitId') businessUnitId: string,@Param('assignTo') assignTo: string) {
      const businessUnitObjId = validateObjectId(businessUnitId);
      const newOwnerId = validateObjectId(assignTo);
      if (!assignTo) {
        throw new BadRequestException('assignTo is required.');
      }
      return this.businessUnitService.assignTo(businessUnitObjId, assignTo);
    }

  @Delete(':businessUnitId/hard-delete')
  @ApiOperation({ summary: 'Hard delete an business unit by Id', description: `This endpoint deletes an business unit by Id. This is accessible only for "Admin"` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.Vendor)
  @Roles()
  @ApiResponse({ status: 200, description: 'business unit is hard deleted.' })
  @ApiResponse({ status: 401, description: 'Unauthorized ' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role admin can only use this end point.' })
  @ApiResponse({ status: 404, description: 'business unit not found.' })
  @ApiParam({ name: 'businessUnitId', description: 'ID of the business unit' })
  remove(@Param('businessUnitId') businessUnitId: string, @Req() req: any) {
    const objId = validateObjectId(businessUnitId);
    return this.businessUnitService.hardDelete(objId, req.user);
  }



  @Delete(':businessUnitId/soft-delete')
  @ApiOperation({ summary: 'Soft delete an business unit by Id', description: `This endpoint soft deletes an business unit by Id. This is accessible only for "Admin"` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.Vendor)
  @Roles()
  @ApiResponse({ status: 200, description: 'business unit is soft deleted.' })
  @ApiResponse({ status: 401, description: 'Unauthorized ' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role admin can only use this end point.' })
  @ApiResponse({ status: 404, description: 'business unit not found.' })
  @ApiParam({ name: 'businessUnitId', description: 'ID of the business unit' })
  delete(@Param('businessUnitId') businessUnitId: string, @Req() req: any) {
    const objId = validateObjectId(businessUnitId);
    return this.businessUnitService.softDelete(objId, req.user);
  }

  // @Delete('delete-all')
  // @ApiOperation({ summary: 'Hard delete all business units', description: `This endpoint hard deletes all business units. This is accessible only for "${Role.SuperAdmin}" and "${Role.Admin}"` })
  // @ApiBearerAuth()	
  // @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles( Role.SuperAdmin, Role.Admin )
  // @ApiResponse({ status: 200, description: 'All business units are deleted.'})
  // @ApiResponse({ status: 401, description: 'Unauthorized '})
  // @ApiResponse({ status: 403, description: 'Forbidden' })
  // @ApiResponse({ status: 404, description: 'business units not found.'})
  // deleteAll() {
  //   return this.businessUnitService.hardDeleteAllBusinessUnits();
  // }


  @Get('users-by-business-unit-type')
  @ApiOperation({
    summary: 'Retrieve users based on business unit type',
    description: `Fetches users associated with business units of a specified type in an organization. This is accessible only for "Admin".`,
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.Vendor)
  @Roles()
  @ApiResponse({ status: 200, description: 'Users retrieved successfully.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role admin can only use this end point.' })
  @ApiResponse({ status: 404, description: 'Business unit or organization not found.' })
  async getUsersByBusinessUnitType(@Req() req: any, @Query() query: FilterUsersDto) {
    if (!query.org) {
      query.org = req.user.org._id
    }
    return this.businessUnitService.getUsersByBusinessUnitType(query);
  }

  @Get(':businessUnitId/clients')
  @ApiOperation({ summary: 'Retrieve clients of a business unit by Id', description: 'This endpoint returns clients of a business unit by Id.' })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard) // 
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.TeamLead, Role.TeamMember, Role.Vendor, Role.JobSeeker, Role.DeliveryManager)
  @Roles() // TODO: Customer org member can also post jobs. Add role here. 
  @ApiResponse({ status: 200, description: 'Clients of a business unit is retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized ' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role "BUHead" and "ResourceManager" can use this end point.' })
  @ApiResponse({ status: 404, description: 'Clients of a business unit not found.' })
  @ApiParam({ name: 'businessUnitId', description: 'ID of the business unit' })
  getClientsByBusinessunit(@Req() req: any,@Param('businessUnitId') businessUnitId: string) {
    if(!req.user.org._id)
      {
        throw new BadRequestException('User does not have org id');
      }
    const objId = validateObjectId(businessUnitId);
    return this.businessUnitService.getClientsByBusinessunit(req.user,objId);
  }

  @Patch(':businessUnitId/bgv-Handler/:bgvHandlerId')
  @ApiOperation({ summary: 'Update an BGV handler for business unit by Id', description: `This endpoint updates an business unit by Id. This is accessible only for "Admin".` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.Vendor)
  @Roles()
  @ApiResponse({ status: 200, description: 'Business Unit is updated.' })
  @ApiResponse({ status: 401, description: 'Unauthorized ' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role admin can only use this end point.' })
  @ApiResponse({ status: 404, description: 'Business Unit not found.' })
  @ApiParam({ name: 'businessUnitId', description: 'ID of the business unit' })
  @ApiParam({ name: 'bgvHandlerId', description: 'ID of the bgvHandler' })
  async updateBgvhandler(@Req() req: any, @Param('businessUnitId') businessUnitId: string,@Param('bgvHandlerId') bgvHandlerId: string,@Body() updateBusinessUnitDto: UpdateBusinessUnitDto) {
    const objId = validateObjectId(businessUnitId);
    if (!bgvHandlerId) {
      throw new BadRequestException('assignTo is required.');
    }
    return await this.businessUnitService.updateBgvhandler(objId,bgvHandlerId, req.user);
  }

}
