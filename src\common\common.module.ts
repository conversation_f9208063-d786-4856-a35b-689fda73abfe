import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { Comment, CommentSchema } from './schemas/comment.schema';
import { ContactInformation, ContactInformationSchema } from './schemas/contact-information.schema';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: ContactInformation.name, schema: ContactInformationSchema },
      { name: Comment.name, schema: CommentSchema },
    ]),
  ],
  exports: [MongooseModule]
})
export class CommonModule {}
