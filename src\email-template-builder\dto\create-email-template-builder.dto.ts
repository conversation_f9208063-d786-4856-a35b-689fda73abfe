import { ApiHideProperty, ApiProperty } from "@nestjs/swagger";
import { Transform, TransformFnParams, Type } from "class-transformer";
import { IsArray, IsEnum, IsMongoId, IsNotEmpty, IsOptional, IsString, ValidateNested } from "class-validator";
import { sanitizeWithStyle } from "src/utils/sanitize-with-style";
import { EmailTemplateEvent } from "src/shared/constants";
import { PlaceholderDto } from "../../org/dto/placeholder.dto";

export class CreateEmailTemplateDto {

  @ApiProperty({ type: String, required: true, description: 'Template name' })
  @IsString()
  @IsNotEmpty()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  templateName: string;

  @ApiProperty({ type: String, required: false, description: 'Template MJML content' })
  @IsOptional()
  templateMJMLContent: string;

  @ApiProperty({ type: String, required: false, description: 'Template HTML content' })
  @IsOptional()
  // @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  templateHTMLContent?: string;

  @ApiProperty({ type: Object, required: false, description: 'Template Json content' })
  @IsOptional()
  templateJsonContent: object;

  @ApiProperty({
    type: String,
    required: false,
    enum: EmailTemplateEvent,
    description: 'Event name triggering the email',
  })
  @IsEnum(EmailTemplateEvent)
  @IsString()
  @IsOptional()
  eventName?: EmailTemplateEvent;

  //Organization associated with the template
  @ApiHideProperty()
  @IsMongoId()
  @IsOptional()
  org?: string;

  @ApiHideProperty()
  @IsOptional()
  createdBy?: string;

  @ApiProperty({ type: Boolean, required: false, default: false, description: 'Soft delete the email template' })
  @IsOptional()
  isDeleted?: boolean;

  @ApiProperty({ type: Boolean, required: false, default: false, description: 'Default email template' })
  @IsOptional()
  isDefault?: boolean;

  @ApiProperty({ type: Boolean, required: false, default: false, description: 'Can Delete template' })
  @IsOptional()
  canDelete?: boolean;

}
