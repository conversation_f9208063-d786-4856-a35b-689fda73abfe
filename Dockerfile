# FROM --platform=linux/amd64 node:20
# RUN mkdir -p /usr/src/app
# WORKDIR /usr/src/app
# # COPY ./package.json /app/
# # COPY ./tsconfig.json /app/
# # COPY ./nest-cli.json /app/
# COPY . .
# RUN yarn install
# ENV NODE_OPTIONS=--max-old-space-size=4096
# ENV NODE_ENV=production
# RUN yarn build
# ENV PORT 80
# EXPOSE 80
# CMD [ "yarn", "start:prod" ]

FROM node:23-alpine AS base

# Install build dependencies for native modules
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    libc6-compat \
    cairo-dev \
    pango-dev \
    giflib-dev \
    librsvg-dev

WORKDIR /app

FROM base AS builder

WORKDIR /app

RUN apk add --no-cache \
    libc6-compat \
    cairo \
    pango \
    giflib \
    librsvg

# Copy source code and build
COPY . .

RUN yarn install
RUN yarn build

RUN ls

# Production stage
FROM base AS runner

WORKDIR /app

# Copy built node_modules from builder
COPY --from=builder /app/ ./

# Copy built application
# COPY --from=builder /usr/src/app/dist ./dist

# Copy other necessary files
# COPY package.json ./

ENV PORT 80
EXPOSE 80

# Start the application
CMD ["yarn", "start:prod"]
