// import { Consumer, Producer, Router, Transport } from 'mediasoup/node/lib/types';
import { join } from 'path';
import { Calendar } from 'src/calendar/schemas/calender.schema';
import { Interview } from 'src/interview/schemas/interview.schema';
import { JobApplication } from 'src/job-application-form/schemas/job-application.schema';
import { Meeting } from 'src/meeting/schemas/meeting.schema';
import { BasicUser } from 'src/user/schemas/basic-user.schema';


export const EMAIL_TEMPLATES_FOLDER_PATH = join(__dirname, '..', 'templates/');
export const PUBLIC_FOLDER_PATH = join(__dirname, '..', 'public/');
export const UPLOADS_FOLDER_PATH = join(__dirname, '..', 'uploads/');


// export enum MemberRole {
//     // SuperAdmin = 'super-admin',
//     OrgAdmin = 'org-admin',
//     OrgUser = 'org-user', // default
//     BUHead = 'bu-head',
//     AccountManager = 'account-manager',
//     ResourceManager = 'resource-manager',
//     DeliveryManager = 'delivery-manager',
//     TeamLead = 'team-lead',
//     TeamMember = 'team-member',
// }

export enum HeadCount {
    NOT_SPECIFIED = 'not-specified',
    ONE_TO_TEN = '1-to-10',
    ELEVEN_TO_HUNDRED = '11-to-100',
    HUNDRED_AND_ONE_TO_THOUSAND = '101-to-1000',
    THOUSAND_PLUS = '1000-plus',
}

export enum AccountStatus {
    QUALIFIED = 'qualified',
    PROSPECT = 'prospect',
    DORMANT = 'dormant',
    CLIENT = 'client',
    CUSTOMER = 'customer',
    DEAD = 'dead',
}

export enum TimesheetType {
  DAILY = 'Daily',
  WEEKLY = 'Weekly',
  MONTHLY = 'Monthly',
}

export enum ClientStatus {
    QUALIFIED = 'qualified',
    PROSPECT = 'prospect',
    DORMANT = 'dormant',
    CLIENT = 'client',
    DEAD = 'dead',
}

export enum OrgStatus {
    QUALIFIED = 'qualified',
    PROSPECT = 'prospect',
    DORMANT = 'dormant',
    CLIENT = 'client',
    CUSTOMER = 'customer',
    DEAD = 'dead',
}

// export enum UserStatus {
//     APPROVED = 'approved',
//     REJECTED = 'rejected',
//     SUSPENDED = 'suspended',
//     PENDING = 'pending',
//     BLOCKED = 'blocked',
// }

export enum FileStatus {
    APPROVED = 'approved',
    REJECTED = 'rejected',
    PENDING = 'pending'
}

export enum OrgType {
    NONE = 'none',
    // Note: Use Root Org instead
    // ADMIN_ORG = 'admin-org',
    AGENCY_ORG = 'agency-org',
    CUSTOMER_ORG = 'customer-org',
    ADMIN_CUSTOMER_ORG = 'admin-customer-org',
    PREMIUM_CUSTOMER_ORG = 'premium-customer-org',
    ACCOUNT_ORG = 'account-org',
    ROOT_ORG = 'root-org',
    ADMIN_ORG = 'admin-org', // Admin in this org (Coding Limits) is super admin (Shankar sir),
    VENDOR_ORG = 'vendor-org',
      JOB_ORG = "JOB_ORG",

}

export enum AddressType {
    HOME = 'home',
    OFFICE = 'office',
    BILLING = 'billing',
    SHIPPING = 'shipping',
    WORK = 'work',
    OTHER = 'other'
}


export enum StageType {
    NONE = 'none',
    WORKFLOW_SOURCING = 'workflow.sourcing',
    WORKFLOW_SCREENING = 'workflow.screening',
    WORKFLOW_ASSESSMENT = 'workflow.assessment',
    WORKFLOW_INTERVIEW_TELEPHONIC = 'workflow.interview.telephonic',
    WORKFLOW_INTERVIEW_AI = 'workflow.interview.ai',
    WORKFLOW_INTERVIEW_VIDEO = 'workflow.interview.video',
    WORKFLOW_INTERVIEW_IN_PERSON = 'workflow.interview.inperson',
    WORKFLOW_OFFER = 'workflow.offer',
    WORKFLOW_REJECTION = 'workflow.rejection',
}


//Task Priority
export enum Priority {
    LOW = 'LOW',
    MEDIUM = 'MEDIUM',
    HIGH = 'HIGH'
}

export enum JobAllocationType {
    ASSIGNEES = 'JobAllocationToAssignees',
    VENDORS = 'JobAllocationToVendors',
    FREELANCERS = 'JobAllocationToFreelancers',
    AI_RECRUITERS = 'JobAllocationToAiRecruiters',
}

//Task Status
export enum Status {
    TO_DO = 'TO-DO',
    IN_PROGRESS = 'IN-PROGRESS',
    COMPLETED = 'COMPLETED'
}

export enum EventTitle {
    NOTE_CREATED = 'New Note Created',
    NOTE_UPDATED = ' Note Updated.',
    NOTE_DELETED = 'Note Deleted.',
    TASK_CREATED = 'New Task Created.',
    TASK_UPDATED = 'Task Updated.',
    TASK_CHANGEASSIGNEE = 'Task Assignee Changed.',
    TASK_CHANGESTATUS = 'Task Status Changed.',
    TASK_DELETED = 'Task Deleted.',
    CONTACT_CREATED = '${newContact.createdBy} added a new contact ${newContact.name}.',
    CONTACT_UPDATED = 'Contact Updated.',
    CONTACT_CHANGESTATUS = 'Contact Status Changed.',
    CONTACT_MOVED = 'Contact Ownership Modified.',
    ACCOUNT_CREATED = `{accountCreatedBy} added a new account {accountName}.`,
    ACCOUNT_UPDATED = 'Account Updated.',
    ACCOUNT_CHANGESTATUS = 'Account Status Changed.',
    ACCOUNT_MOVED = 'Account Ownership Modified.'
}

export enum RecurrenceInterval {
    DAILY = 'DAILY',
    WEEKLY = 'MONTHLY',
    YEARLY = 'YEARLY'
}

export enum EmailSubject {
    Welcome = 'Welcome to Talsy!',
    ConfirmEmail = 'Your Email is Confirmed!',
    ForgotPassword = 'Verify to reset your password',
    ForgotPasswordDone = 'Your Email is Confirmed to reset password!',
    PasswordReset = 'Password is reset.',
    PasswordChange = 'Password is changed.',
    TaskAssigneeChanged = 'Task is assigned to you.',
    ContactAssignToChanged = 'Contact is assigned to you.',
    InterviewSchedule = 'Your interview is scheduled',
    OrgCreated = 'Organization has been registered with Talsy',
    AccountCreated = 'Account has been created with Talsy',
    AccountAssigned = 'Account is assigned to you',
    ClientAssigned = 'Client is assigned to you',
    ContactAssigned = 'Contact is assigned to you.',
    OTPResend = 'Resend OTP',
     RoleUpdated = 'Your role has been updated',
    // CustomerReApproved = 'Your account has been Approved',

}

export enum EmailMessage {
    Welcome = 'Thank you for registering with us.',
    ConfirmEmail = 'We are excited to have you on board.',
    ForgotPassword = 'We received a request for forgot password.',
    ForgotPasswordDone = 'Your Email is Confirmed to reset password!',
    PasswordReset = 'Your password has been reset.',
    PasswordChange = 'Your password has been changed.',
    TaskAssigneeChanged = 'Task is assigned to you.',
    ContactAssignToChanged = 'Contact is assigned to you.',
    InterviewSchedule = 'We are pleased to inform you that you have been scheduled for an interview',
    OrgCreated = 'Your organization has been successfully created. Welcome onboard!',
    AccountCreated = 'Your Account has been successfully created',
    AccountAssigned =  'has been successfully assigned to you',
    ClientAssigned = 'has been successfully assigned to you',
    ContactAssigned = 'has been successfully assigned to you',
    OrgCreatedVerify = 'Your organization has been successfully created.',
    OTPResend = 'Here is your OTP. Which you requested. ',
    VendorRegister = "[Company Name] Invites you register as a Vendor.",
    CustomerReApproved = 'Your organization has been successfully approved.',
}

export enum EmailInfo {
    Welcome = 'Please confirm your email address by clicking the button below.',
    ConfirmEmail = 'Your email address has been successfully confirmed. You can now access all the features of our platform.',
    ForgotPassword = 'Please confirm your email address by clicking the button below.',
    ForgotPasswordDone = 'Please reset your password.',
    PasswordReset = 'Please login with your new password.',
    PasswordChange = 'Please login with your new password',
    TaskAssigneeChanged = 'Please view by clicking the button below.',
    ContactAssignToChanged = 'Contact is assigned to you.',
    InterviewSchedule = 'Please contact us if you have any questions or need to reschedule',
    OrgCreated = 'Your organization has been successfully created.',
    AccountCreated = 'Welcome Onboard!',
    AccountAssigned = 'You’ve been assigned a new account. Head over to your dashboard to explore and manage it.',
    ClientAssigned = 'You’ve been assigned a new client. Head over to your dashboard to explore and manage it.',
    ContactAssigned = 'You’ve been assigned a new Contact. Head over to your dashboard to explore and manage it.',
    OrgCreatedVerify = 'Please verify your email to complete your organization registration.',
    OTPResend = 'Please confirm your email address by clicking the button below.',
    VendorRegister = 'Dear Vendor we are pleased to invite you ',
    CustomerReApproved = 'You now have full access to our platform and can start exploring all our features. <br> <p>✔ <strong>Login to Your Account:</strong> Access your dashboard and start using our features.</p>',

}

export enum EmailButtonText {
    Welcome = 'Confirm Email',
    Login = 'Login',
    ForgotPassword = 'Change Password',
    TaskAssigneeChanged = 'View Task',
    ContactAssignToChanged = 'View Contact',
    InterviewSchedule = 'view interview',
    SignUp = 'SignUp'
}

export enum EmploymentType {
    FullTime = 'full-time',
    PartTime = 'part-time',
    Contract = 'contract',
    Temporary = 'temporary'
}

export enum AccountType {
    SAVINGS = 'Savings',
    CURRENT = 'Current',
    SALARY = 'Salary',
    OTHER = 'Other',
  }

export enum WorkMode {
    Remote = 'remote',
    Hybird = 'hybrid',
    OnSite = 'onsite'
}

export enum HiringMode {
    Scheduled_Interview = 'scheduled-interview',
    Walk_In = 'walk-in',
    Drive = 'drive',
    On_Campus = 'on-campus',
    Off_Campus = 'off-campus'
}


export enum Currency {
    INR = 'INR',
    USD = 'USD'
}

export enum Gender {
    Male = 'Male',
    Female = 'Female',
    Other = 'Other'
}


export enum JobType {
    Internal = 'Internal',
    External = 'External'
}

export enum IdentifierType {
    Company = 'Company',
    Individual = 'Individual'
}

export enum IntegrationType {
    GoogleMeet = 'GoogleMeet',
    ZoomMeet = 'ZoomMeet',
    ZoomMeetPremium = 'ZoomMeetPremium',
    MicrosoftTeams = 'MicrosoftTeams',
    Naukri = 'Naukri',
    WhatsApp = 'WhatsApp',
    HuggingFace = 'HuggingFace',
    OpenAI = 'OpenAI',
    JioCX = 'JioCX',
    AirtelIQ = 'AirtelIQ',
    TataTele = 'TataTele',
}

export enum CalendarType {
    GoogleMeet = 'GoogleMeet',
    ZoomMeet = 'ZoomMeet',   
    ZoomMeetPremium = 'ZoomMeetPremium',      
    MicrosoftTeams = 'MicrosoftTeams',
    Other = 'Other',
}

export enum StausIntegrationType {
    Active = 'Active',
    InActive = 'InActive'
}

export enum CountryStatus {
    Approved = 'Active',
    Pending = 'Pending',
    Suspended = 'Suspended',
    Deleted = 'Deleted'
}

export enum ScreeningType {
    PHONE_SCREENING = 'phone-screening',
    VIDEO_SCREENING = 'video-screening',
    IN_PERSON_SCREENING = 'in-person-screening',
    AI_SCREENING = 'ai-screening'
}

export enum Platform {
    GoogleMeet = 'Google-meet',
    ZoomMeet = 'Zoom-meet',
    MicrosoftTeams = 'Microsoft-teams'
}

export enum StatusConfigType {
    REGION = 'region',
    COMPANY = 'company',
    USER = 'user',
    AGENCY_ORG = 'agency-org',
    CUSTOMER_ORG = 'customer-org',
    ADMIN_CUSTOMER_ORG = 'admin-customer-org',
    PREMIUM_CUSTOMER_ORG = 'premium-customer-org',
    ACCOUNT_ORG = 'account-org',
    ROOT_ORG = 'root-org',
    ADMIN_ORG = 'admin-org',
    VENDOR_ORG = 'vendor-org'
}

export enum StatusFunctionality {
    CREATE = 'CREATE',
    READ = 'READ',
    UPDATE = 'UPDATE',
    DELETE = 'DELETE',
    APPROVE = 'APPROVE',
    REJECT = 'REJECT',
    SUBMIT = 'SUBMIT',
    ACTIVATE = 'ACTIVATE',
    SUSPEND = 'SUSPEND',
    ARCHIVE = 'ARCHIVE',
    ONHOLD = 'ONHOLD',
    MISCELLANEOUS = 'MISCELLANEOUS',
    REGISTER = 'REGISTER',
    PENDING = 'PENDING',
}

export enum SourceType {
    LANDING_PAGE = 'landingPage',
    ADMIN_PORTAL = 'adminPortal',
    MOBILE_APP = 'mobileApp',
    API = 'api',
    OTHER = 'other'
}

export enum InviteStatus {
    SEND = 'send',
    COMPLETED = 'completed'
}


export enum EmailTemplateEvent {
    WORKFLOW_SOURCING_PRE_CONSENT = 'workflow.sourcing.pre_consent',
    WORKFLOW_SCREENING = 'workflow.screening',
    WORKFLOW_ASSESSMENT = 'workflow.assessment',
    WORKFLOW_INTERVIEW_TELEPHONIC = 'workflow.interview.telephonic',
    WORKFLOW_INTERVIEW_AI = 'workflow.interview.ai',
    WORKFLOW_INTERVIEW_VIDEO = 'workflow.interview.video',
    WORKFLOW_INTERVIEW_IN_PERSON = 'workflow.interview.inperson',
    WORKFLOW_INTERVIEW_CANCELLATION = 'workflow.interview.cancellation',
    WORKFLOW_INTERVIEW_RESCHEDULING = 'workflow.interview.rescheduling',
    WORKFLOW_INTERVIEW_REMINDER = 'workflow.interview.reminder',
    WORKFLOW_DOCUMENTATION_COLLECTION_REQUEST = 'workflow.documentation.collection.request',
    WORKFLOW_OFFER = 'workflow.offer',
    WORKFLOW_OFFER_ACCEPTANCE_REMINDER = 'workflow.offer.acceptance.reminder',
    WORKFLOW_REJECTION = 'workflow.rejection',
    NEW_JOB_OPPORTUNITY = 'new.job.opportunity',
    WORKFLOW_TRACKER = 'workflow.tracker',
    SIGNATURE = 'signature',
    MEETING_RESCHEDULE = 'meeting.reschedule',
    TECHNICAL_PANEL_INVITE = 'technical.panel.invite',
    RAISE_INVOICE = 'raise.invoice'
}

export enum BusinessUnitType {
    UNSPECIFIED = 'unspecified',
    DEPARTMENT = 'department',
    EXECUTIVE = 'executive',
    RECRUITMENT = 'recruitment',
    HUMAN_RESOURCES = 'human-resources',
    FINANCE = 'finance',
    ACCOUNTING = 'accounting',
    OPERATIONS = 'operations',
    ENGINEERING = 'engineering',
    MARKETING = 'marketing',
    SALES = 'sales',
    PRODUCT_DEVELOPMENT = 'product-development',
    LEGAL = 'legal',
    ADMINISTRATION = 'administration',
    FAVOURITE = 'favourite',
    TECHNICAL = 'Technical'
}

export type EventPayload = {
    user: BasicUser;
    data: JobApplication;
    body?: string;
};

export type MeetingPayload = {
    user: BasicUser;
    data: Meeting;
};

export type CalendarPayload = {
    user: BasicUser;
    data: Calendar;
};

export type TechnicalPanelInvite = {
    user: BasicUser;
    data: Interview;
};

export enum FieldType {
    TEXT = 'text',
    NUMBER = 'number',
    DROPDOWN = 'dropdown',
    MULTISELECT = 'multiselect',
    CHECKBOX = 'checkbox',
    DATE = 'date',
    SLIDER = 'slider',
    CHIPSTEXTBOX = 'chips-textbox',
    RICHTEXT = 'rich-text',
}

export enum MeetingType {
    REGULAR = 'regular',
    URGENT = 'urgent',
    ONE_ON_ONE = 'one-on-one',
}

export enum MeetingStatus {
    SCHEDULED = 'scheduled',
    ONGOING = 'ongoing',
    COMPLETED = 'completed',
    CANCELLED = 'cancelled'
}

export enum BlogType {
    FREELANCERS = 'Freelancers',
    COMPANIES = 'Companies',
    AGENCIES = 'Agencies'
}

export enum BlogStatus {
    ACTIVE = 'Active',
    INACTIVE = 'Inactive'
}

export enum Salutaion {
    MR = 'Mr',
    MRS = 'Mrs',
    MS = 'Ms'
}

export enum Source {
    VENDOR = 'Vendor',
    FREELANCER = 'Freelancer',
  }
export enum AllowedControllersForAdmin {
    ORGS = "Orgs",
    CONTACTS = "Contacts",
    JOBS_GET = "Jobs:GET",
    // JOBS = "Jobs",
    JOB_APPLICATION_FORMS_GET = "Job-Application-Forms:GET",
    // JOB_APPLICATION_FORMS = "Job-Application-Forms",
    BUSINESS_UNITS = "Business Units",
    USERS = "Users",
    VENDOR_INVITE = "vendor-invite",
    ACTIVITIES = "Activities",
    FILE_UPLOADS = "File-Uploads",
    STAGES = "Stages",
    WORKFLOW = "Workflow",
    EMAIL_TEMPLATE_BUILDER = "Email-Template-builder",
    EMAIL_CONFIG = "email-config",
    USER_INBOX_CONFIG = "User-inbox-config",
    DELETE_USER = "delete-user",
    INTEGRATIONS = "Integrations",
    TASKS = "Tasks",
    // MEETINGS = "Meetings",
    MEETINGS_GET = "Meetings:GET",
    RECRUITER_TARGET = "recruiter-target",
    INDUSTRIES_GET = "Industries:GET",
    // INDUSTRIES = "Industries",
    ACCOUNT_TYPES_GET = "Account Types:GET",
    // ACCOUNT_TYPES = "Account Types",
    NOTES = "Notes",
    MESSAGES = "Messages",
    // BLOG = "blog",
    RECRUITMENT_TEAM = "Recruitement Team",
    COUNTRIES = "Countries",
    IDENTIFIERS = "Identifiers",
    // EVALUATION_FORMS = "Evaluation-Forms",
    // EDUCATION_QUALIFICATIONS = "Education-Qualifications",
    JOB_LOCATIONS = "Job-Locations",
    // JOB_ALLOCATIONS = "Job Allocations"
    ONBOARDINGS_GET = "Onboardings:GET",
    DYNAMIC_FIELDS = "dynamic-fields",
    CUSTOM_FIELDS = "custom-fields",
    STATUS_GET = "Status:GET",
    STATUS_PATCH = "Status:PATCH",
    CALENDARS = "Calendar",
}

export enum AllowedControllersForDeafultRoles {
    STATES = "States",
    BUSINESS_UNITS = "Business Units",
    ACTIVITIES = "Activities",
    FILE_UPLOADS = "File-Uploads",
    EDUCATION_QUALIFICATIONS = "Education-Qualifications",
    ORGS = "Orgs",
    CONTACTS = "Contacts",
    USER_INBOX_CONFIG = "User-inbox-config",
    JOB_LOCATIONS = "Job-Locations",
    ONBOARDINGS = "Onboardings",
    DELETE_USER = "delete-user",
    INDUSTRIES = "Industries",
    EVALUATION_FORMS = "Evaluation-Forms",
    WORK_EXPERIENCES = "Work-Experiences",
    ACCOUNT_TYPES = "Account Types",
    USERS = "Users",
    DYNAMIC_FIELDS = "dynamic-fields",
    TASKS = "Tasks",
    MESSAGES = "Messages",
    NOTES = "Notes",

}

export enum ExcludedControllersForGroupBy {
    INTEGRATIONS = "Integrations",
    HOME = "Home",
    STATES = "States",
    BUSINESS_UNITS = "Business Units",
    RESUMES = "Resumes",
    ACTIVITIES = "Activities",
    STATUS = "Status",
    FILE_UPLOADS = "File-Uploads",
    EDUCATION_QUALIFICATIONS = "Education-Qualifications",
    ORGS = "Orgs",
    REGIONS = "Regions",
    VENDOR_INVITE = "vendor-invite",
    USER_INBOX_CONFIG = "User-inbox-config",
    JOB_LOCATIONS = "Job-Locations",
    ONBOARDINGS = "Onboardings",
    DELETE_USER = "delete-user",
    BLOG = "blog",
    RECRUITMENT_TEAM = "Recruitement Team",
    INDUSTRIES = "Industries",
    EVALUATION_FORMS = "Evaluation-Forms",
    COUNTRIES = "Countries",
    WORK_EXPERIENCES = "Work-Experiences",
    DYNAMIC_FIELDS = "dynamic-fields",
    IDENTIFIERS = "Identifiers",
    AUTH = "Auth",
    ACCOUNT_TYPES = "Account Types",
    RECRUITER_TARGET = "recruiter-target",
    TASKS = "Tasks",
    EMAIL_TEMPLATE_BUILDER = "Email-Template-builder",
    EMAIL_CONFIG = "email-config",
    PREFERENCES = "Preferences",
    CALENDARS = "Calendars",
    MESSAGES = "Messages",
    NOTES = "Notes",
    USERS = "Users",
    CONTACTS = "Contacts",
    STAGES = "Stages",
    ASSESSMENT = "Assessment",
    OFFER = "Offer",
    INTERVIEWS = "Interviews",
    MEETINGS = "Meetings",
    CUSTOM_FIELDS="custom-fields",
    CALENDARS_1 = "Calendar",
}

export enum ExcludedControllersForGroupByNoOrg {
    INTEGRATIONS = "Integrations",
    HOME = "Home",
    STATES = "States",
    BUSINESS_UNITS = "Business Units",
    RESUMES = "Resumes",
    ACTIVITIES = "Activities",
    STATUS = "Status",
    FILE_UPLOADS = "File-Uploads",
    EDUCATION_QUALIFICATIONS = "Education-Qualifications",
    ORGS = "Orgs",
    REGIONS = "Regions",
    VENDOR_INVITE = "vendor-invite",
    USER_INBOX_CONFIG = "User-inbox-config",
    JOB_LOCATIONS = "Job-Locations",
    ONBOARDINGS = "Onboardings",
    DELETE_USER = "delete-user",
    BLOG = "blog",
    RECRUITMENT_TEAM = "Recruitement Team",
    INDUSTRIES = "Industries",
    EVALUATION_FORMS = "Evaluation-Forms",
    COUNTRIES = "Countries",
    WORK_EXPERIENCES = "Work-Experiences",
    DYNAMIC_FIELDS = "dynamic-fields",
    IDENTIFIERS = "Identifiers",
    AUTH = "Auth",
    ACCOUNT_TYPES = "Account Types",
    RECRUITER_TARGET = "recruiter-target",
    TASKS = "Tasks",
    EMAIL_TEMPLATE_BUILDER = "Email-Template-builder",
    EMAIL_CONFIG = "email-config",
    // PREFERENCES = "Preferences",
    CALENDARS = "Calendars",
    MESSAGES = "Messages"
}
export enum AllowedControllersForDeafultFreeLancerRole {
    STATES = "States",
    ACTIVITIES = "Activities",
    FILE_UPLOADS = "File-Uploads",
    EDUCATION_QUALIFICATIONS = "Education-Qualifications",
    JOB_LOCATIONS = "Job-Locations:GET",
    INDUSTRIES = "Industries",
    EVALUATION_FORMS = "Evaluation-Forms",
    WORK_EXPERIENCES = "Work-Experiences",
    // TASKS = "Tasks",
    // MESSAGES = "Messages",
    JOBS_GET = "Jobs:GET",
    JOB_APPLICATION_FORMS_GET = "Job-Application-Forms:GET",
    JOB_APPLICATION_FORMS_POST = "Job-Application-Forms:POST",
    JOB_APPLICATION_FORMS_PATCH = "Job-Application-Forms:PATCH",
    JOB_APPLICATION_FORMS_DELETE = "Job-Application-Forms:DELETE",
    WORKFLOWS_GET = "Workflow:GET",
    DYNAMIC_FIELDS = "dynamic-fields:GET",
}

export enum AllowedControllersForDeafultJobseekerRoles {
    STATES = "States",
    ACTIVITIES = "Activities",
    FILE_UPLOADS = "File-Uploads",
    EDUCATION_QUALIFICATIONS = "Education-Qualifications",
    JOB_LOCATIONS = "Job-Locations:GET",
    INDUSTRIES = "Industries",
    EVALUATION_FORMS = "Evaluation-Forms",
    WORK_EXPERIENCES = "Work-Experiences",
    // TASKS = "Tasks",
    // MESSAGES = "Messages",
    JOBS_GET = "Jobs:GET",
    JOB_APPLICATION_FORMS_GET = "Job-Application-Forms:GET",
    JOB_APPLICATION_FORMS_POST = "Job-Application-Forms:POST",
    JOB_APPLICATION_FORMS_PATCH = "Job-Application-Forms:PATCH",
    WORKFLOWS_GET = "Workflow:GET",
    PREFERENCES = "Preferences",
    RESUMES = "Resumes",
    // DYNAMIC_FIELDS = "dynamic-fields:GET",
    // JOBALLOCATIONS = "Job Allocations:GET"
    
}

export enum GroupByType {
    SOURCE = 'source',
    DATE = 'date',
}

export enum CandidateFields {
    JOB = "jobId.title",
    FIRST_NAME = "firstName",
    LAST_NAME = "lastName",
    CONTACT_EMAIL = "contactDetails.contactEmail",
    CONTACT_NUMBER = "contactDetails.contactNumber",
    STREET = "contactAddress.street",
    POSTAL_CODE = "contactAddress.postalCode",
    COUNTRY_NAME = "country.countryName",
    STATE_NAME = "state.stateName",
    CITY_NAME = "city.name",
    GENDER = "gender",
    CURRENT_CTC = "currentCTC",
    EXPECTED_CTC = "expectedCTC",
    CTC_PERCENTAGE = "ctcPercentage",
    CREATED_BY_NAME = "createdBy.firstName",
    CREATED_BY_ROLES = "createdBy.roles",
    IS_REJECTED = "isRejected",
    STAGE_NAME = "stage.name",
    CREATED_AT = "createdAt",
    SKILLS = "evaluationForm.skill",
    CURRENT_LOCATION = "currentLocation",
    COMPANY_NAME = "jobId.endClientOrg.title",
  }

  export const ORG_ROLE_PERMISSIONS_PAYLOAD = {
    rolesMapping: {
      bu_head: [
        "Workflow:POST",
        "Workflow:GET",
        "Workflow:PATCH",
        "Workflow:DELETE",
        "Jobs:POST",
        "Jobs:GET",
        "Jobs:PATCH",
        "Jobs:DELETE",
        "Job-Application-Forms:POST",
        "Job-Application-Forms:GET",
        "Job-Application-Forms:PATCH",
        "Job-Application-Forms:DELETE",
        //
        "Job Allocations:POST",
        "Job Allocations:GET",
        "Job Allocations:PATCH",
        "Job Allocations:DELETE",
      ],
      resource_manager: [
        "Workflow:POST",
        "Workflow:GET",
        "Workflow:PATCH",
        "Workflow:DELETE",
        "Jobs:POST",
        "Jobs:GET",
        "Jobs:PATCH",
        "Jobs:DELETE",
        "Job-Application-Forms:GET",
        // "Job-Application-Forms:PATCH",
        "Job Allocations:GET",
      ],
      delivery_manager: [
        "Workflow:POST",
        "Workflow:GET",
        "Workflow:PATCH",
        "Workflow:DELETE",
        "Jobs:GET",
        "Job-Application-Forms:POST",
        "Job-Application-Forms:GET",
        "Job-Application-Forms:PATCH",
        "Job-Application-Forms:DELETE",
        "Job Allocations:POST",
        "Job Allocations:GET",
        "Job Allocations:PATCH",
        "Job Allocations:DELETE",
      ],
      team_lead: [
        "Workflow:POST",
        "Workflow:GET",
        "Workflow:PATCH",
        "Workflow:DELETE",
        "Jobs:GET",
        "Job-Application-Forms:POST",
        "Job-Application-Forms:GET",
        "Job-Application-Forms:PATCH",
        "Job-Application-Forms:DELETE",
         //
         "Job Allocations:POST",
         "Job Allocations:GET",
         "Job Allocations:PATCH",
         "Job Allocations:DELETE",
      ],
      account_manager: [
        "Workflow:POST",
        "Workflow:GET",
        "Workflow:PATCH",
        "Workflow:DELETE",
        "Jobs:POST",
        "Jobs:GET",
        "Jobs:PATCH",
        "Jobs:DELETE",
        "Job-Application-Forms:GET",
        // "Job-Application-Forms:PATCH",
        "Job Allocations:GET",
      ],
      admin: [
        "Workflow:POST",
        "Workflow:GET",
        "Workflow:PATCH",
        "Workflow:DELETE",
        "Jobs:GET",
        "Job-Application-Forms:GET",
      ],
      recruiter: [
        "Workflow:GET",
        "Jobs:GET",
        "Job-Application-Forms:POST",
        "Job-Application-Forms:PATCH",
        "Job-Application-Forms:GET",
        "Job-Application-Forms:DELETE",
         //
         "Job Allocations:POST",
         "Job Allocations:GET",
         "Job Allocations:PATCH",
         "Job Allocations:DELETE",
      ],
      team_member: [
        "Workflow:GET",
        "Jobs:GET",
        "Job-Application-Forms:POST",
        "Job-Application-Forms:GET",
        "Job-Application-Forms:DELETE",
         //
         "Job Allocations:POST",
         "Job Allocations:GET",
         "Job Allocations:PATCH",
         "Job Allocations:DELETE",
      ],
      vendor: [
        "Workflow:GET",
        "Jobs:GET",
        "Job-Application-Forms:POST",
        "Job-Application-Forms:PATCH",
        "Job-Application-Forms:GET",
        "Job-Application-Forms:DELETE",
        "Bench:POST",
        "Bench:GET",
        "Bench:PATCH",
        "Bench:DELETE",
      ],
    },
  };
  
  export enum NotificationType {
    PROFILE_UPDATE = 'profile-update',
    WORKFLOW_UPDATE = 'workflow-update',
    JOB_ALLOCATION = 'job-allocation',
    JOB_CREATED = 'job-created',
}
  export enum WhatsAppTemplateType {
    INTERVIEW = 'interview',
    OFFER = 'offer',
    REJECTION = 'rejection',
    REMINDER = 'reminder',
  }
  
  export enum OnboardingStatus {
    DOCUMENTATION_PENDING = 'Documentation Pending',
    BGV_PENDING = 'BGV Pending',
    BGV_MAIL_SENT = 'BGV Mail Sent',
    AWAITING_BGV = 'Awaiting BGV',
    AWAITING_DOCUMENTS='Awaiting Documents',
    BGV_APPROVED = 'BGV Approved',
    BGV_REJECTED = 'BGV Rejected',
    INTERIM_BGV_UPLOADED = 'BGV Report Uploaded',
    CLIENT_BGV_APPROVAL_PENDING='BGV Pending Client Approval',
    CLIENT_BGV_APPROVAL_DONE='BGV Approved By Client',
    OFFER_RELEASED = 'Offer Released',
    OFFER_ACCEPTED = 'Offer Accepted',
    OFFER_REJECTED = 'Offer Rejected',
    FINAL_SELECT = "Final Select",
    INVOICE_GENERATED = "Invoice Generated",
    EMPLOYEE_ONBOARDED = "Employee Onboarded",
    OFFER_APPROVAL_PENDING='Offer Approval Pending',
    OFFER_APPROVED='Offer Approved',
    }

    export enum InvoiceStatus {
        NEW = 'New',
        PAID = 'Paid',
        PENDING = 'Pending'
    }

  export enum DurationUnit {
    Days = 'days',
    Weeks = 'weeks',
    Months = 'months',
    Years = 'years',
  }

  export const REQUIRED_BGV_DOCUMENTS = [
    {
      key: 'qualification_certificates',
      label: 'Highest Qualification Certificate(s)',
      description: 'Original and one photocopy of your highest qualification certificate(s), including any certifications or diplomas',
    },
    {
      key: 'passport_photo',
      label: 'Passport-size Photograph',
      description: 'One recent passport-size colour photograph',
    },
    {
      key: 'experience_letters',
      label: 'Experience & Relieving Letters',
      description: 'Copies of experience and relieving letters from all previous employments/internships',
    },
    {
      key: 'passport_copy',
      label: 'Passport Copy',
      description: 'Copy of Passport',
    },
    {
      key: 'pan_card',
      label: 'PAN Card',
      description: 'Copy of PAN Card',
    },
    {
      key: 'aadhar_card',
      label: 'AADHAR Card',
      description: 'Copy of AADHAR Card',
    },
    {
      key: 'dob_proof',
      label: 'Date of Birth Proof',
      description: 'Proof of Date of Birth',
    },
    {
      key: 'address_proof',
      label: 'Address Proof',
      description: 'Proof of current and permanent address (e.g., Telephone Bill, Electricity Bill, Driving License, etc.)',
    },
    {
      key: 'resume',
      label: 'Updated Resume',
      description: 'Updated Resume',
    },
  ];
  
  
// src/mediasoup/types/room.types.ts
// export interface Room {
//     id: string;
//     router: Router;
//     peers: Map<string, Peer>;
//   }
  
//   export interface Peer {
//     id: string;
//     transports: Map<string, Transport>;
//     producers: Map<string, Producer>;
//     consumers: Map<string, Consumer>;
//   }