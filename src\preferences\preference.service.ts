// preference.service.ts
import { BadRequestException, Injectable, InternalServerErrorException, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Preference } from './schemas/preference.schema';
import { CreatePreferenceDto } from './dto/create-preference.dto';
import { UpdatePreferenceDto } from './dto/update.preference.dto';
import { QueryPreferenceDto } from './dto/query.preference.dto';

@Injectable()
export class PreferenceService {
  constructor(
    @InjectModel(Preference.name) private preferenceModel: Model<Preference>,
  ) { }

  async create(createPreferenceDto: CreatePreferenceDto, user: any) {
    const createdBy = user._id;
  
    try {
      let { skills = [], locations = [], clients = [] } = createPreferenceDto;
  
      // Step 1: Normalize Data - Convert to lowercase & remove duplicates
      skills = [...new Set(skills.map(skill => skill.toLowerCase().trim()))];
      locations = [...new Set(locations.map(location => location.toLowerCase().trim()))];
      clients = [...new Set(clients.map(clientId => clientId.toString().trim()))]; // Ensure clients are unique as strings
  
      // Step 2: Fetch existing preference for the user
      const existingPreference = await this.preferenceModel.findOne({ createdBy }).lean();
  
      // Extract existing values and normalize them to lowercase for comparison
      const existingSkills = existingPreference?.skills?.map(s => s.skill.toLowerCase().trim()) || [];
      const existingLocations = existingPreference?.locations?.map(l => l.location.toLowerCase().trim()) || [];
      const existingClients = existingPreference?.clients?.map(c => c.clientId.toString().trim()) || []; // Convert ObjectId to string
  
      // Step 3: Filter out already existing values
      const newSkills = skills
        .filter(skill => !existingSkills.includes(skill))
        .map(skill => ({ skill }));
  
      const newLocations = locations
        .filter(location => !existingLocations.includes(location))
        .map(location => ({ location }));
  
      const newClients = clients
        .filter(clientId => !existingClients.includes(clientId))
        .map(clientId => ({ clientId }));
  
      // Step 4: Only update if there are new values
      const updateQuery: any = {};
      if (newSkills.length) updateQuery.skills = { $each: newSkills };
      if (newLocations.length) updateQuery.locations = { $each: newLocations };
      if (newClients.length) updateQuery.clients = { $each: newClients };
  
      if (Object.keys(updateQuery).length === 0) {
        throw new BadRequestException(`Already exists`);
      }
  
      // Step 5: Perform Database Update
      const updatedPreference = await this.preferenceModel.findOneAndUpdate(
        { createdBy },
        { $addToSet: updateQuery },
        { new: true, upsert: true, setDefaultsOnInsert: true }
      ).exec();
  
      return updatedPreference;
    } catch (error) {
      throw new BadRequestException(`Failed to create/update preference: ${error.message}`);
    }
  }  

  async findAll(userId: string) {
    return this.preferenceModel.findOne({ createdBy: userId })
      .populate({
        path: 'clients.clientId',
        select: '_id title orgType',
        model: 'Org'
      })
      .exec();
  }

  async deleteSkill(userId: string, skillId: string): Promise<{ message: string }> {
    const updatedPreference = await this.preferenceModel.findOneAndUpdate(
      { createdBy: userId, 'skills._id': skillId }, // Find user preference containing this skill
      { $pull: { skills: { _id: skillId } } }, // Remove skill from array
      { new: true }
    );

    if (!updatedPreference) {
      throw new NotFoundException('Skill not found or does not belong to user');
    }

    return { message: 'Skill removed successfully' };
  }

  async deleteLocation(userId: string, locationId: string): Promise<{ message: string }> {
    const updatedPreference = await this.preferenceModel.findOneAndUpdate(
      { createdBy: userId, 'locations._id': locationId },
      { $pull: { locations: { _id: locationId } } },
      { new: true }
    );

    if (!updatedPreference) {
      throw new NotFoundException('Location not found or does not belong to user');
    }

    return { message: 'Location removed successfully' };
  }

  async deleteClient(userId: string, clientId: string): Promise<{ message: string }> {
    const updatedPreference = await this.preferenceModel.findOneAndUpdate(
      { createdBy: userId, 'clients._id': clientId },
      { $pull: { clients: { _id: clientId } } },
      { new: true }
    );

    if (!updatedPreference) {
      throw new NotFoundException('Client not found or does not belong to user');
    }

    return { message: 'Client removed successfully' };
  }

}