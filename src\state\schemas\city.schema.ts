import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Types } from 'mongoose';
import { State } from 'src/state/schemas/state.schema';  // Assuming this is your State schema
import { Country } from 'src/country/schemas/country.schema';  // Assuming this is your Country schema

export type CityDocument = HydratedDocument<City>;

@Schema({
    timestamps: true,
})
export class City {

    // Id of the city
    @Prop({
        required: true,
        type: Number,
    })
    id: number;

    @Prop({
        type: String,
        required: true,
        trim: true,
    })
    name: string;

    @Prop({
        required: true,
        type: Number
    })
    stateId: number;

    @Prop({
        required: true,
        type: Number
    })
    countryId: number;
}

export const CitySchema = SchemaFactory.createForClass(City);

// Index for faster querying by stateId and countryId
CitySchema.index({ stateId: 1, countryId: 1 });
