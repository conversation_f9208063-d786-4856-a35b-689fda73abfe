import { Controller, Get, Post, Body, Patch, Param, Delete, Req, UseGuards, Query } from '@nestjs/common';

import { CreateLeaveManagmentDto, CreateLeavePolicyDto } from './dto/create-leave-managment.dto';
import { UpdateLeaveManagmentDto, UpdateLeavePolicyDto } from './dto/update-leave-managment.dto';
import { LeavePolicyService } from './leave-managment.service';
import { ApiBearerAuth, ApiOperation, ApiParam, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { AuthJwtGuard } from 'src/auth/guards/auth-jwt/auth-jwt.guard';
import { CreateLeaveRequestDto } from './dto/create-leave-request.dto';

@Controller()
@ApiTags('leave-managment')
export class LeaveManagmentController {
  constructor(private readonly leaveManagmentService: LeavePolicyService) { }

  @Post()
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  @ApiOperation({ summary: 'Create a new Leave Policy ' })
  @ApiResponse({ status: 201, description: 'Leave Policy.' })
  async create(@Req() req: any, @Body() dto: CreateLeavePolicyDto) {
    const orgId = req.user?.org?._id; // or however your auth structure is
    return this.leaveManagmentService.create(orgId, dto);
  }

  @Get()
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  @ApiOperation({ summary: 'Get Leave Policy ' })
  @ApiResponse({ status: 200, description: 'Leave Policy List.' })
  @ApiQuery({ name: 'leaveType', required: false, type: String, description: 'Filter by leave type (e.g., Paid, Sick)' })
  @ApiQuery({ name: 'planeName', required: false, type: String, description: 'Filter by plane name' })
  async findAll(@Req() req: any, @Query() filter: { leaveType?: string; planeName?: string }) {
    const orgId = req.user?.org?._id; // Get the orgId from the user's session
    return this.leaveManagmentService.findAll(orgId, filter);
  }


  @Get(':id')
  async findOne(@Param('id') id: string) {
    return this.leaveManagmentService.findOne(id);
  }

  @Post(':id/leave-request')
  async applyLeave(
    @Param('id') policyId: string,
    @Body() dto: CreateLeaveRequestDto,
  ) {
    return this.leaveManagmentService.addLeaveRequest(policyId, dto);
  }


  @Patch(':id')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  @ApiParam({ name: 'id', description: 'The ID of the Leave Managment  to update' })
  @ApiResponse({ status: 200, description: 'Leave Managment  updated successfully.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Invalid Data.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden. Only specific roles can access this endpoint.' })
  @ApiResponse({ status: 404, description: 'Leave Managment  not found.' })
  @ApiOperation({ summary: 'Update a Leave Managment  by ID', operationId: 'update', description: `This endpoint Attendance a Bench Candidate by ID. Only accessible by "BUHead", "TeamLead", "DeliveryManager", and "Recruiter".` })
  update(@Param('id') id: string, @Body() updateLeaveManagmentDto: UpdateLeavePolicyDto) {
    return this.leaveManagmentService.update(id, updateLeaveManagmentDto);
  }


  @Patch(':policyId/leave-request/:requestId/status')
  async updateLeaveRequestStatus(
    @Param('policyId') policyId: string,
    @Param('requestId') requestId: string,
    @Body() body: CreateLeaveRequestDto,
  ) {
    return this.leaveManagmentService.updateLeaveRequestStatus(
      policyId,
      requestId,
      body,
    );
  }


  @Delete(':id')
  async delete(@Param('id') id: string) {
    return this.leaveManagmentService.delete(id);
  }
}