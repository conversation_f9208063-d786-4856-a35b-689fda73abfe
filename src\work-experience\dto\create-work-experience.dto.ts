import { ApiHideProperty, ApiProperty } from "@nestjs/swagger";
import { Transform, TransformFnParams } from "class-transformer";
import { IsBoolean, IsDate, IsISO8601, IsMongoId, isNotEmpty, IsNotEmpty, IsOptional, IsString, Length } from "class-validator";
import { sanitizeWithStyle } from "src/utils/sanitize-with-style";

export class CreateWorkExperienceDto {

    @ApiProperty({
        type: String,
        required: true,
    })
    @IsNotEmpty()
    @IsMongoId()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    jobId: string;
    
    @ApiProperty({
        type: String,
        required: true,
    })
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    @IsNotEmpty()
    @IsString()
    jobTitle: string;

    @ApiProperty({
        type: String,
        required: true,
    })
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    @IsNotEmpty()
    @IsString()
    companyName: string;

    @ApiProperty({
        type: Date,
        required: false,
        default: new Date(Date.UTC(new Date().getUTCFullYear(), new Date().getUTCMonth())),
    })
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    @IsOptional()
    @IsISO8601({strict: true})
    @Length(10,24)
    jobStartDate?: Date;

    @ApiProperty({
        type: Date,
        required: false,
        default: new Date(Date.UTC(new Date().getUTCFullYear(), new Date().getUTCMonth())),
    })
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    @IsOptional()
    @IsISO8601({strict: true})
    @Length(10,24)
    jobEndDate: Date;

    @ApiProperty({
        type: Boolean,
        required: false,
    })
    @IsOptional()
    @IsBoolean()
    currentlyWorking?: boolean;

    @ApiHideProperty()
    @IsOptional()
    createdBy?: string;
}

