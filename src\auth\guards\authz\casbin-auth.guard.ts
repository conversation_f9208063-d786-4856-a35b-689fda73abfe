import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { AuthzService } from '../../authz.service';

@Injectable()
export class CasbinAuthGuard implements CanActivate {
    constructor(private reflector: Reflector, private authzService: AuthzService) {}

    async canActivate(context: ExecutionContext): Promise<boolean> {
        const roles = this.reflector.get<string[]>('roles', context.getHandler());
        const request = context.switchToHttp().getRequest();
        const user = request.user;
        const { originalUrl: path, method } = request;
        const enforcer = await this.authzService.getEnforcer();

        return enforcer.enforce(user.role, path, method);
    }
}
