import { ApiProperty } from "@nestjs/swagger";
import { IsBoolean, IsNotEmpty, IsNumber, IsOptional } from "class-validator";
import { CreateJobAllocationBaseDto } from "./create-job-allocation.dto";

export class CreateJobAllocationToFreelancersDto extends CreateJobAllocationBaseDto {
    @ApiProperty({ type: Boolean, required: false, default: true, description: 'Is job available in freelancer pool' })
    @IsBoolean()
    @IsOptional()
    isAvailableInFreeLancerPool?: boolean;

    @ApiProperty({ type: Number, required: true, description: 'Reward amount for the job' })
    @IsNumber()
    @IsNotEmpty()
    reward: number;
}
