import { IsBoolean, IsDate, IsOptional, IsString, IsArray, IsMongoId, IsNotEmpty, IsNumber, IsISO8601 } from 'class-validator';
import { Type } from 'class-transformer';
import { InviteeDto } from './create-invitee.dto';
import { ApiHideProperty, ApiProperty } from '@nestjs/swagger';
import { Transform, TransformFnParams } from 'class-transformer';
import sanitizeWithStyle from 'sanitize-html'
import { MeetingStatus, MeetingType } from 'src/shared/constants';

export class CreateMeetingDto {

  @ApiProperty({
    type: String,
    required: true,
    description: 'Subject of the meeting'
  })
  @IsString()
  @IsNotEmpty()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value).toLowerCase())
  subject: string;

  @ApiProperty({
    type: String,
    description: 'Summary of the meeting',
    required: false,
  })
  @IsOptional()
  @IsString()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value).toLowerCase())
  summary?: string;

  @ApiProperty({
    type: String,
    description: 'Detailed description of the meeting',
    required: false,
  })
  @IsOptional()
  @IsString()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value).toLowerCase())
  description?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'ID of the organizer (BasicUser)'
  })
  @IsMongoId()
  @IsOptional()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value).toLowerCase())
  organizer?: string; // Organizer should be the ID of the BasicUser

  // @ApiProperty({
  //   description: 'List of invitees',
  //   type: [InviteeDto],
  //   required: true,
  // })
  // @IsArray()
  // @IsNotEmpty()
  // @Type(() => InviteeDto) // Assuming InviteeDto handles the invitee structure
  // invitees: InviteeDto[];

  @ApiProperty({
    description: 'List of invitees email ids',
    example: ['<EMAIL>'],
    type: [String],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  invitees?: string[];

  @ApiProperty({
    description: 'List of guests email ids',
    example: ['<EMAIL>'],
    type: [String],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  guests?: string[];

  @ApiProperty({
    type: Date,
    required: true,
    description: 'Scheduled time for the meeting',
    default: new Date(Date.UTC(new Date().getUTCFullYear(), new Date().getUTCMonth(), new Date().getUTCDate(), new Date().getUTCHours(), new Date().getUTCMinutes())),
  })
  // YYYY-MM-DDThh:mm:ssZ - example : 2024-05-27T00:00:00.000Z
  @IsISO8601({ strict: true })
  @IsNotEmpty()
  scheduledAt: Date;

  @ApiProperty({
    description: 'List of meeting accepted email ids',
    example: ['<EMAIL>'],
    type: [String],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  accept?: string[];

  @ApiProperty({
    description: 'List of meeting rejected email ids',
    example: ['<EMAIL>'],
    type: [String],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  reject?: string[];

  @ApiProperty({
    description: 'End time of the meeting',
    required: false,
    type: Date
  })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  endTime?: Date;

  @ApiProperty({
    description: 'Duration of the meeting in minutes',
    example: 60,
    required: false,
    type: Number,
  })
  @IsOptional()
  @IsNumber()
  duration?: number;

  @ApiProperty({
    description: 'Timezone of the meeting, default is UTC',
    example: 'UTC',
    required: false,
    type: String,
  })
  @IsOptional()
  @IsString()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value).toLowerCase())
  timezone?: string;

  @ApiProperty({
    description: 'Flag to indicate if the meeting is deleted',
    example: false,
    required: false,
    type: Boolean
  })
  @IsOptional()
  @IsBoolean()
  isDeleted?: boolean;

  @ApiProperty({
    description: 'Flag to allow recording of the meeting',
    default: false,
    required: false,
    type: Boolean
  })
  @IsOptional()
  @IsBoolean()
  allowRecording?: boolean;

  @ApiProperty({
    description: 'Flag to allow transcription of the meeting',
    default: false,
    required: false,
    type: Boolean
  })
  @IsOptional()
  @IsBoolean()
  allowTranscripts?: boolean;

  @ApiProperty({
    description: 'Type of the meeting (e.g., regular, conference)',
    example: 'regular',
    required: false,
    type: String,
    default: MeetingType.REGULAR,
    enum: MeetingType
  })
  @IsOptional()
  @IsString()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value).toLowerCase())
  type?: string;

  @ApiProperty({
    description: 'Status of the meeting (e.g., scheduled, completed)',
    example: 'scheduled',
    required: false,
    type: String,
    default: MeetingStatus.SCHEDULED,
    enum: MeetingStatus,
  })
  @IsOptional()
  @IsString()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value).toLowerCase())
  status?: string;

  @ApiProperty({
    description: 'List of tags associated with the meeting',
    example: ['team-sync', 'Q4-planning'],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @ApiProperty({
    description: 'ID of the associated task (optional)',
    required: false,
    type: String
  })
  @IsOptional()
  @IsMongoId()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value).toLowerCase())
  task?: string;

  @ApiProperty({
    description: 'ID of the organization associated with the meeting',
    required: false,
    type: String
  })
  @IsOptional()
  @IsMongoId()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value).toLowerCase())
  org?: string;

  @ApiProperty({
    description: 'ID of the contact associated with the meeting (optional)',
    required: false,
    type: String
  })
  @IsOptional()
  @IsMongoId()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value).toLowerCase())
  contact?: string;

  @ApiProperty({
    description: 'ID of the job associated with the meeting (optional)',
    required: false,
    type: String
  })
  @IsOptional()
  @IsMongoId()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value).toLowerCase())
  job?: string;

  @ApiProperty({
    description: 'Unique link for the meeting',
    required: true,
    type: String
  })
  @IsNotEmpty()
  @IsString()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value).toLowerCase())
  meetingLink: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Meeting code',
  })
  @IsOptional()
  @IsString()
  meetingCode?: string;

  @ApiProperty({
    description: 'ID of the business unit associated with the meeting (optional)',
    required: false,
    type: String
  })
  @IsOptional()
  @IsMongoId()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value).toLowerCase())
  businessUnit?: string;

  @ApiProperty({
    description: 'ID of the user who deleted the meeting (optional)',
    required: false,
    type: String
  })
  @IsOptional()
  @IsMongoId()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value).toLowerCase())
  deletedBy?: string;

  @ApiHideProperty()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value).toLowerCase())
  createdBy: string;

  @ApiProperty({
    description: 'ID of the user who last updated the meeting (optional)',
    required: false,
    type: String
  })
  @IsOptional()
  @IsMongoId()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value).toLowerCase())
  updatedBy?: string;


  @ApiProperty({
    description: 'Flag to indicate if a reminder should be sent',
    required: false,
    type: Boolean
  })
  @IsOptional()
  @IsBoolean()
  sendReminder?: boolean;

  @ApiProperty({
    description: 'Number of minutes before the meeting to send a reminder',
    required: false,
    type: Number
  })
  @IsOptional()
  @IsNumber()
  reminderMinutesBefore?: number;

  @ApiProperty({
    description: 'List of file IDs associated with the meeting',
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsMongoId({ each: true })
  files?: string[];
}
