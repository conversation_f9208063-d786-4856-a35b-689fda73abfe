import { ApiProperty } from "@nestjs/swagger";
import { Transform, TransformFnParams, Type } from "class-transformer";
import {  IsEmail,  IsEnum,  IsNotEmpty, IsOptional,  IsString } from "class-validator";
import { sanitizeWithStyle } from "src/utils/sanitize-with-style";
import { Salutaion } from "src/shared/constants";


export class CreateRecruitementTeamDto {

    @ApiProperty({
       type: String,
       required: true,
       description: 'Type of the salutation (e.g., Mr/Mrs/Ms)',
       default: Salutaion.MR,
       enum: Salutaion
    })
    @IsString()
    @IsNotEmpty()
    @IsEnum(Salutaion)
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    salutation: string;

    @ApiProperty({
        type: String,
        required: true,
    })
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    @IsString()
    @IsNotEmpty()
    firstName: string;

    @ApiProperty({
        type: String,
        required: true,
    })
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    @IsString()
    @IsNotEmpty()
    lastName: string;

    @ApiProperty({
        type: String,
        required: false,
    })
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    @IsString()
    @IsOptional()
    middleName?: string;

    @ApiProperty({
        type: String,
        required: true,
    })
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    @IsString()
    @IsNotEmpty()
    reference: string;

    @ApiProperty({
        type: String,
        required: true,
        description: 'Enter email',
        format: 'email',
    })
    @IsNotEmpty()
    @IsEmail()
    @IsString()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value).toLowerCase())
    email: string;

    @ApiProperty({
        type: String,
        required: false,
        description: 'Contact number'
      })
    @IsString()
    @IsOptional() 
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    phoneNumber: string;

}