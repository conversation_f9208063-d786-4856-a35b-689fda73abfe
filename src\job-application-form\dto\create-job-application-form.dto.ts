import { ApiHideProperty, ApiProperty } from "@nestjs/swagger";
import { IsAlphanumeric, IsArray, IsBoolean, IsEnum, IsISO8601, IsMongoId, IsNotEmpty, IsNumber, IsObject, IsOptional, IsString, IsUrl, Length, Max, Min, ValidateNested } from "class-validator";
import { Transform, TransformFnParams, Type } from 'class-transformer';
import { sanitizeWithStyle } from "src/utils/sanitize-with-style";
import { ContactInformationDto } from "src/common/dto/contact-information.dto";
import { Currency, Gender } from "src/shared/constants";
import { AddressInformationDto } from "src/common/dto/address-information.dto";

// export class DynamicFieldValueDto {

//     @ApiProperty({ type: String, description: 'Label for the dynamic field', required: false })
//     @IsOptional()
//     @IsString()
//     @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value)) // Sanitize the label
//     label?: string;

//     @ApiProperty({ description: 'Value for the dynamic field', required: false })
//     @IsOptional()
//     value?: string | number | boolean | string[] | null | undefined; // Define the value property

// }

export class CreateJobApplicationFormDto {
    @ApiProperty({
        type: String,
        required: true,
    })
    @IsNotEmpty()
    @IsMongoId()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    jobId: string;


    @ApiProperty({
        type: String,
        required: true,
    })
    @IsMongoId()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    workflow: string;

    @ApiProperty({
        type: String,
        required: false,
    })
    @IsOptional()
    @IsMongoId()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    stage?: string;


    @ApiProperty({
        type: String,
        required: false,
    })
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    @IsString()
    @IsOptional()
    @IsMongoId()
    resumeMetadata?: string;

    @ApiProperty({
        type: String,
        required: false,
    })
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    @IsString()
    @IsOptional()
    @IsMongoId()
    coverLetterMetadata?: string;

    @ApiProperty({
        type: String,
        required: true,
    })
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    @IsString()
    firstName: string;

    @ApiProperty({
        type: String,
        required: true,
    })
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    @IsString()
    lastName: string;

    @ApiProperty({
        type: String,
        required: false,
    })
    @IsAlphanumeric()
    @IsOptional()
    panNumber?: String;

    @ApiProperty({
        type: ContactInformationDto,
        required: false,
        description: 'Contact information of job applicant'
    })
    @IsOptional()
    @ValidateNested()
    @Type(() => ContactInformationDto)
    contactDetails?: ContactInformationDto;

    @ApiProperty({
        type: AddressInformationDto,
        required: false,
        description: 'Contact address'
    })
    @IsOptional()
    @ValidateNested()
    @Type(() => AddressInformationDto)
    contactAddress?: AddressInformationDto;

    @ApiProperty({
        type: String,
        required: false,
        description: "Enter country"
    })
    @IsMongoId()
    @IsOptional()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value).toLowerCase())
    country?: string;

    @ApiProperty({
        type: String,
        required: false,
        description: "Enter state"
    })
    @IsMongoId()
    @IsOptional()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value).toLowerCase())
    state?: string;

    @ApiProperty({
        type: String,
        required: false,
        description: "Enter city"
    })
    @IsMongoId()
    @IsOptional()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value).toLowerCase())
    city?: string;

    @ApiProperty({
        type: Date,
        required: false,
        default: new Date(Date.UTC(new Date().getUTCFullYear(), new Date().getUTCMonth()))
    })
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    @IsISO8601({ strict: true })
    @Length(10, 24)
    @IsOptional()
    dob?: string;

    @ApiProperty({
        type: String,
        required: false,
        enum: Gender,
    })
    @IsOptional()
    @IsEnum(Gender)
    gender?: Gender;

    @ApiProperty({
        type: Boolean,
        required: false,
        default: false
    })
    @IsOptional()
    @IsBoolean()
    disability?: boolean;

    @ApiProperty({
        type: String,
        required: false,
    })
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    @IsOptional()
    @IsUrl()
    linkedInUrl?: string;

    @ApiProperty({
        type: String,
        required: false,
    })
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    @IsOptional()
    @IsUrl()
    websiteOrBlogUrl?: string;

    @ApiProperty({
        type: Boolean,
        required: false,

    })
    @IsBoolean()
    @IsOptional()
    isExperienced?: boolean;

    @ApiProperty({
        type: Number,
        required: false,
        description: 'Number of years of experience',
    })
    @IsNumber()
    @IsOptional()
    @Min(0) // To ensure the number of years is non-negative
    yearsOfExperience?: number;

    @ApiProperty({
        type: [String],
        required: false,
        description: 'Work experience for expirienced professionals'
    })
    @IsMongoId({ each: true })
    @IsOptional()
    @Transform((params: TransformFnParams) => params.value.map((value: string) => sanitizeWithStyle(value)))
    @IsArray()
    workExperience?: string[];

    @ApiProperty({
        type: [String],
        required: false,
    })
    @IsArray()
    @IsMongoId({ each: true })
    @IsOptional()
    @Transform((params: TransformFnParams) => params.value.map((value: string) => sanitizeWithStyle(value)))
    educationQualification?: string[];

    @ApiProperty({
        type: [String],
        required: false,
    })
    @IsArray()
    @IsMongoId({ each: true })
    @IsOptional()
    @Transform((params: TransformFnParams) => params.value.map((value: string) => sanitizeWithStyle(value)))
    evaluationForm?: string[];


    @ApiProperty({
        type: Number,
        required: false,
    })
    @IsOptional()
    @IsNumber()
    @Min(0)
    @Max(90)
    noticePeriodDays?: number;

    @ApiProperty({
        type: Boolean,
        required: false,
    })
    @IsOptional()
    @IsBoolean()
    servingNoticePeriod?: boolean;

    @ApiProperty({
        type: Date,
        required: false,
        default: new Date(Date.UTC(new Date().getUTCFullYear(), new Date().getUTCMonth()))
    })
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    @IsISO8601({ strict: true })
    @Length(10, 24)
    @IsOptional()
    lastWorkingDate?: string;

    @ApiProperty({
        type: String,
        required: false,
    })
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    @IsOptional()
    @IsString()
    currentLocation?: string;

    @ApiProperty({
        type: Boolean,
        required: false,
    })
    @IsOptional()
    @IsBoolean()
    willingToRelocate?: boolean;

    @ApiProperty({
        type: [String],
        required: false,
    })
    @IsOptional()
    @IsMongoId({ each: true })
    @IsArray()
    @Transform((params: TransformFnParams) => params.value.map((value: string) => sanitizeWithStyle(value)))
    reLocation?: string[];

    // @ApiProperty({
    //     type: String,
    //     required: false,
    //     default: WorkMode.OnSite,
    //     enum: WorkMode,
    //     description: 'Work mode of the job',
    // })
    // @IsEnum(WorkMode)
    // @IsString()
    // @IsOptional()
    // @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    // workMode?: string;

    @ApiProperty({
        type: String,
        required: false,
    })
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    @IsOptional()
    @IsString()
    preferredLocation?: string;

    @ApiProperty({
        type: Number,
        required: false,
    })
    @IsOptional()
    @IsNumber()
    currentCTC?: number;

    @ApiProperty({
        type: Number,
        required: false,
    })
    @IsOptional()
    @IsNumber()
    expectedCTC?: number;

    @ApiProperty({
        type: Number,
        required: false,
    })
    @IsOptional()
    @IsNumber()
    @Max(100)
    ctcPercentage?: number;

    @ApiProperty({
        type: String,
        required: false,
        enum: Currency,
        default: Currency.INR,
    })
    @IsOptional()
    @IsEnum(Currency)
    currency?: Currency;

    @ApiProperty({
        type: Boolean,
        required: false,
    })
    @IsOptional()
    @IsBoolean()
    companyNorms?: boolean;

    @ApiProperty({
        type: String,
        required: false,
    })
    @IsOptional()
    @IsMongoId()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    org?: string;

    @ApiHideProperty()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    createdBy?: string;

    @ApiHideProperty()
    @IsOptional()
    jobApplicationCode?: number;

    @ApiProperty({
        type: Number,
        required: false,
        description: 'Job description matching score',
    })
    @IsOptional()
    @IsNumber()
    jdMatchingScore?: number;

    @ApiProperty({
        type: Boolean,
        required: false,
        default: false
    })
    @IsBoolean()
    @IsOptional()
    isSelected?: boolean;

    @ApiProperty({
        type: Boolean,
        required: false,
        default: false
    })
    @IsBoolean()
    @IsOptional()
    isRejected?: boolean;

    @ApiProperty({
        required: false,
        default: false
    })
    @IsBoolean()
    @IsOptional()
    isDraft?: boolean;

    @ApiProperty({
        description: 'Dynamic fields associated with the job application, represented as key-value pairs',
        type: Object,
        required: false,
        example: {
            aadhaar: '12345',
            pan: '12344',
        },
    })
    @IsOptional()
    @IsObject()
    dynamicFields?: Record<string, any>;

    @ApiHideProperty()
    @IsOptional()
    postingOrg?: string;

}








