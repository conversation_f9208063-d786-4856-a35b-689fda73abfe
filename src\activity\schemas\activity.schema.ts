import { <PERSON>p, Schem<PERSON>, SchemaFactory } from "@nestjs/mongoose";
import { HydratedDocument, Types } from 'mongoose';
import { Account } from 'src/account/schemas/account.schema';
import { AccountStatus, HeadCount } from 'src/shared/constants';
import { Comment } from "src/common/schemas/comment.schema";
import { Contact } from "src/contact/schemas/contact.schema";
import { Industry } from 'src/industry/schemas/industry.schema';
import { Job } from "src/job/schemas/job.schema";
import { Note } from "src/note/schemas/note.schema";
import { Task } from "src/task/schemas/task.schema";
import { BasicUser } from 'src/user/schemas/basic-user.schema';
import { Org } from "src/org/schemas/org.schema";
import { Region } from "src/region/schemas/region.schema";
import { BusinessUnit } from "src/business-unit/schemas/business-unit.schema";
import { RateCard } from "src/rate-card/schemas/rate-card.schema";


export type ActivityDocument = HydratedDocument<Activity>;

@Schema({
    timestamps: true
})
export class Activity {

    @Prop({
        type: String,
        required: false,
        trim: true,
    })
    title?: string;

    @Prop({
        type: String,
        required: false,
        trim: true,
    })
    titleWithPlaceholders?: string;

    // 1. change the status on account schema itself


    // 2nd step goes into activity schema
    // use back ticks and publish hyperlinks
    // {{createdByLink}} changed the status from. {{fromStatus}}  to. {{toStatus}} for the account {{accountLink}} with code {{accountCode}}

    @Prop({
        type: Date,
        required: false,
    })
    dueDate?: Date;


    @Prop({
        type: Types.ObjectId,
        ref: 'Activity',
        required: false
    })
    parentActivity?: Types.ObjectId;


    @Prop({ type: Types.ObjectId, ref: 'Org' })
    org?: Org;

    @Prop({
        type: Types.ObjectId,
        ref: 'Contact',
        required: false,
    })
    contact?: Contact;

    @Prop({
        type: Types.ObjectId,
        ref: 'Task',
        required: false
    })
    task?: Task;

    @Prop({
        type: Types.ObjectId,
        ref: 'Note',
        required: false
    })
    note?: Note;

    // @Prop({
    //     type: Types.ObjectId,
    //     ref: 'Job',
    //     required: false
    // })
    // job?: Job;

    // @Prop({
    //     type: Types.ObjectId,
    //     ref: 'Email',
    //     required: false
    // })
    // email?: Email;

    @Prop({ default: false })
    isDeleted?: boolean;


    @Prop({
        required: true,
        type: Types.ObjectId, ref: 'BasicUser'
    })
    actor: BasicUser;

    @Prop({
        required: false,
        type: Types.ObjectId, ref: 'BasicUser'
    })
    audience: BasicUser[];

    // @Prop({
    //     type: String,
    //     required: false,
    //     trim: true,
    // })
    // comment?: string;


    @Prop({
        required: false,
        type: Types.ObjectId, ref: 'Comment'
    })
    comment?: Comment;

    @Prop({
        required: false,
        type: Types.ObjectId, ref: 'Region'
    })
    region?: Region;

    @Prop({
        required: false,
        type: Types.ObjectId, ref: 'BusinessUnit'
    })
    businessUnit?: BusinessUnit;

    @Prop({
        required: false,
        type: Types.ObjectId, ref: 'BasicUser'
    })
    user?: BasicUser;

    // @Prop({
    //     type: [String],
    //     default: [],
    //     required: false,
    // })
    // attachments?:string[];

    @Prop({
        required: false,
        type: Types.ObjectId, ref: 'RateCard'
    })
    rateCard?: RateCard;

}

export const ActivitySchema = SchemaFactory.createForClass(Activity);
