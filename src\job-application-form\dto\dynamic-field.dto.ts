import { ApiProperty, ApiHideProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsOptional, IsEnum, IsMongoId, IsBoolean, IsArray } from 'class-validator';
import { FieldType } from 'src/shared/constants';

export class DynamicFieldDto {
    @ApiProperty({ description: 'Label or display name of the field' })
    @IsString()
    @IsNotEmpty()
    title: string;

    @ApiProperty({ description: 'Type of the field', enum: FieldType })
    @IsEnum(FieldType)
    @IsNotEmpty()
    type: FieldType;

    @ApiProperty({ description: 'Placeholder text for the field', required: false })
    @IsString()
    @IsOptional()
    placeholder?: string;

    @ApiProperty({ description: 'List of options for dropdown-type fields', type: [String], required: false, default: [] })
    @IsArray()
    @IsOptional()
    options?: string[];

    // Organization ID associated with the dynamic field
    @ApiHideProperty()
    @IsMongoId()
    @IsOptional()
    orgId?: string;

    @ApiProperty({ description: 'Indicates if the field is required', type: Boolean, required: false, default: false })
    @IsBoolean()
    @IsOptional()
    isRequired?: boolean;

    @ApiProperty({ description: 'Indicates if the field is related to job applications', type: Boolean, required: false, default: false })
    @IsBoolean()
    @IsOptional()
    isJobApplicationField?: boolean;
}
