import { Module } from '@nestjs/common';
import { AccountService } from './account.service';
// import { AccountController } from './account.controller';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { PipesModule } from 'src/pipes/pipes.module';
import { getConnectionToken, MongooseModule } from '@nestjs/mongoose';
import { Account, AccountSchema } from './schemas/account.schema';
import { JwtModule } from '@nestjs/jwt';
import { UserModule } from 'src/user/user.module';
import { CountryModule } from 'src/country/country.module';
import { EndpointsRolesModule } from 'src/endpoints-roles/endpoints-roles.module';


@Module({
  imports: [
    ConfigModule,
    PipesModule,
    JwtModule, EndpointsRolesModule,
    UserModule,
    CountryModule,
    // MongooseModule.forFeature([{ name: Account.name, schema: AccountSchema }])
    MongooseModule.forFeatureAsync([
      {
        name: Account.name,
        imports: [ConfigModule],
        useFactory: (configService: ConfigService) => {
          const autoIncrement = AutoIncrementFactory(configService);
          const schema = AccountSchema;
          schema.plugin(autoIncrement, {
            inc_field: 'accountCode',
            id: 'account_sequence',
            start_seq: 1,
            reference_fields: []
          });

          return schema;
        },
        inject: [getConnectionToken(), ConfigService],

      },
    ]),
  ],
  // controllers: [AccountController],
  providers: [AccountService],
})
export class AccountModule { }

const AutoIncrementFactory = require('mongoose-sequence');
