import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Schema as MongooseSchema, Types } from 'mongoose';
import { AddressInformation } from 'src/common/schemas/address-information.schema';
import { ContactInformation } from 'src/common/schemas/contact-information.schema';
import { Country } from 'src/country/schemas/country.schema';
import { EducationQualification } from 'src/education-qualification/schemas/education-qualifaction.schema';
import { EvaluationForm } from 'src/evaluation-form/schemas/evaluation-form.schema';
import { FileMetadata } from 'src/file-upload/schemas/file-metadata.schema';
import { JobLocation } from 'src/job-location/schemas/job-location.schema';
import { Job } from 'src/job/schemas/job.schema';
import { Org } from 'src/org/schemas/org.schema';
import { Currency, FieldType, Gender } from 'src/shared/constants';
import { City } from 'src/state/schemas/city.schema';
import { State } from 'src/state/schemas/state.schema';
import { BasicUser } from 'src/user/schemas/basic-user.schema';
import { WorkExperience } from 'src/work-experience/schemas/work-experience.schema';
import { Workflow } from 'src/workflow/schemas/workflow.schema';

export enum AiInterviewStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
}

@Schema({ _id: false })
export class AiInterview {
  @Prop({
    type: String,
    enum: AiInterviewStatus,
    default: AiInterviewStatus.PENDING,
  })
  aiInterviewStatus: AiInterviewStatus;

  @Prop({ type: Number, required: false })
  aiInterviewScore?: number;

  @Prop({ type: Number, required: false })
  aiInterviewDuration?: number;

  @Prop({ type: Date, required: false })
  aiInterviewDate?: Date;
}

@Schema({ _id: false }) // Disables automatic _id generation for options
export class OptionSchema {
  @Prop({ type: String, required: true }) // Make key required
  label: string;

  @Prop({ type: String, required: true }) // Make value required
  value: string;
}

@Schema({ _id: false })
export class DateFieldPropertiesSchema {
  @Prop({ type: String, required: false }) // ISO 8601 format
  minDate?: string;

  @Prop({ type: String, required: false }) // ISO 8601 format
  maxDate?: string;

  @Prop({ type: String, required: false }) // Date format (e.g., YYYY-MM-DD)
  format?: string;
}

@Schema({ timestamps: true })
export class DynamicField {
  // @Prop({ type: String, required: true})
  // title: string;  // Label or display name of the field

  @Prop({ type: String, required: true, trim: true, lowercase: false })
  title: string;

  @Prop({ required: true, enum: FieldType })
  type: FieldType;

  @Prop({ required: false })
  placeholder?: string; // Placeholder text for the field

  // @Prop({ type: [{ label: String, value: String }], default: [] })  // ✅ FIXED: Now options will be stored as an array of objects
  // options?: { label: string; value: string }[];

  @Prop({ type: [OptionSchema], _id: false }) // **This prevents ObjectId creation**
  options?: OptionSchema[];

  // @Prop({ type: [String], _id: false })  // **This prevents ObjectId creation**
  // options?: string[];

  @Prop({ required: false, ref: 'Org' })
  orgId?: Types.ObjectId;

  @Prop({ ref: 'Contact', required: false })
  contactId?: Types.ObjectId;

  @Prop({ default: false })
  isRequired?: boolean;

  @Prop({ default: false })
  isJobApplicationField?: boolean; // Indicates if this field is related to job applications

  @Prop({ default: false })
  isJobField?: boolean; // Indicates if this field is related to jobs

  @Prop({ default: false })
  isDefault?: boolean;

  @Prop({ default: false })
  isDeleted?: boolean; // Indicates if this field is default to org

  @Prop({ type: DateFieldPropertiesSchema, required: false, _id: false })
  dateProperties?: DateFieldPropertiesSchema; // ✅ Now supports date field properties

  @Prop({ default: false })
  canDelete?: boolean; // Indicates if this field can be deleted

  @Prop({ type: Number, default: 0 }) // Order field
  order: number;

  @Prop({ default: false })
  isVisible?: boolean; // Indicates if this field can be visible to user

  @Prop({ type: String })
  name: string; //indicates payload name of the field

  @Prop({ default: true })
  canEdit?: boolean; // Indicates if this field can be editable to user

  @Prop({ required: false, ref: 'BusinessUnit' })
  departmentId?: Types.ObjectId;
}
export const DynamicFieldSchema = SchemaFactory.createForClass(DynamicField);

// export type DynamicFieldValue = {
//     label: string;
//     value?: string | number | boolean | string[] | null | undefined;
// }

@Schema({
  timestamps: true,
})
export class JobApplication {
  @Prop({
    type: Types.ObjectId,
    required: true,
    ref: 'Job',
  })
  jobId: Job;

  @Prop({
    required: true,
    type: Types.ObjectId,
    ref: 'Workflow',
  })
  workflow: Workflow;

  @Prop({
    required: true,
    type: Types.ObjectId,
    ref: 'Stage',
  })
  stage: Types.ObjectId;

  @Prop({
    type: Types.ObjectId,
    required: false,
  })
  resumeMetadata?: FileMetadata;

  @Prop({
    type: Types.ObjectId,
    required: false,
  })
  coverLetterMetadata?: FileMetadata;

  @Prop({
    type: String,
    required: true,
    trim: true,
  })
  firstName: string;

  @Prop({
    type: String,
    required: true,
    trim: true,
  })
  lastName: string;

  @Prop({
    type: String,
    required: false,
    match: /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/, // Validate PAN Number format
    uppercase: true,
  })
  panNumber?: string;

  @Prop({
    type: ContactInformation,
    required: false,
    ref: 'ContactInformation',
  })
  contactDetails?: ContactInformation;

  @Prop({ type: AddressInformation, required: false })
  contactAddress?: AddressInformation;

  @Prop({
    type: Types.ObjectId,
    required: false,
    ref: 'Country',
  })
  country?: Country;

  @Prop({
    type: Types.ObjectId,
    required: false,
    ref: 'State',
  })
  state?: State;

  @Prop({
    type: Types.ObjectId,
    required: false,
    ref: 'City',
  })
  city?: City;

  @Prop({
    type: Date,
    required: false,
  })
  dob?: Date;

  @Prop({
    type: String,
    required: false,
    trim: true,
    enum: Object.values(Gender),
  })
  gender?: string;

  @Prop({
    type: Boolean,
    required: false,
  })
  disability?: boolean;

  @Prop({
    type: String,
    required: false,
    trim: true,
  })
  linkedInUrl?: string;

  @Prop({
    type: String,
    required: false,
    trim: true,
  })
  websiteOrBlogUrl?: string;

  @Prop({
    type: Boolean,
    required: false,
  })
  isExperienced?: boolean;

  @Prop({
    type: Number,
    required: false,
  })
  yearsOfExperience?: number;

  @Prop({
    type: [Types.ObjectId],
    required: false,
    ref: 'WorkExperience',
  })
  workExperience?: WorkExperience[];

  @Prop({
    required: false,
    type: [Types.ObjectId],
    ref: 'EducationQualification',
  })
  educationQualification?: EducationQualification[];

  @Prop({
    required: false,
    type: [Types.ObjectId],
    ref: 'EvaluationForm',
  })
  evaluationForm?: EvaluationForm[];

  @Prop({
    type: Number,
    required: false,
  })
  noticePeriodDays?: number;

  @Prop({
    type: Boolean,
    required: false,
  })
  servingNoticePeriod?: boolean;

  @Prop({
    type: Date,
    required: false,
  })
  lastWorkingDate?: Date;

  @Prop({
    type: String,
    required: false,
    trim: true,
  })
  currentLocation?: string;

  @Prop({
    type: Boolean,
    required: false,
  })
  willingToRelocate?: boolean;

  @Prop({
    required: false,
    type: [Types.ObjectId],
    ref: 'Re-location',
  })
  reLocation?: JobLocation[];

  // @Prop({
  //     type: String,
  //     required: false,
  //     trim: true,
  //     default: WorkMode.OnSite,
  //     enum: Object.values(WorkMode),
  // })
  // workMode?: string;

  @Prop({
    type: String,
    required: false,
    trim: true,
  })
  preferredLocation?: string;

  @Prop({
    type: Number,
    required: false,
  })
  currentCTC?: number;

  @Prop({
    type: Number,
    required: false,
  })
  expectedCTC?: number;

  @Prop({
    type: Number,
    required: false,
  })
  ctcPercentage?: number;

  @Prop({
    type: String,
    required: false,
    trim: true,
    default: Currency.INR,
    enum: Object.values(Currency),
  })
  currency?: string;

  @Prop({
    type: Boolean,
    required: false,
  })
  companyNorms?: boolean;

  @Prop({
    required: false,
    type: Types.ObjectId,
    ref: 'Org',
  })
  org?: Org;

  @Prop({
    required: true,
    type: Types.ObjectId,
    ref: 'BasicUser',
  })
  createdBy: BasicUser;

  @Prop({
    required: false,
  })
  jobApplicationCode?: number;

  @Prop({
    type: Boolean,
    required: false,
    default: false,
  })
  isScreenSelected?: boolean;

  @Prop({
    type: Boolean,
    required: false,
    default: false,
  })
  isRejected?: boolean;

  @Prop({
    type: Boolean,
    required: false,
    default: false,
  })
  bgvVerified?: boolean;

  @Prop({
    type: Number,
    required: false,
    default: false,
  })
  communicationSkillRating?: number;

  @Prop({
    required: false,
    type: Boolean,
    default: false,
  })
  isDraft?: boolean;

  @Prop({
    type: MongooseSchema.Types.Mixed, // Allows flexibility for dynamic key-value pairs
    required: false,
    default: {},
  })
  dynamicFields?: Record<string, any>; // or Record<string, string | number | boolean | null>;

  @Prop({
    type: String,
    required: false,
    trim: true,
  })
  middleName: string;

  @Prop({ type: String, required: false, trim: true, lowercase: true })
  email: string;

  @Prop({ type: String, required: false, trim: true })
  mobileNumber?: string;

  @Prop({
    type: Boolean,
    required: false,
    default: false,
  })
  isDeleted?: boolean;

  @Prop({
    type: Types.ObjectId,
    required: true, // This should always be present
    ref: 'Org',
  })
  postingOrg: Org;

  @Prop({ type: AiInterview, required: false })
  aiInterview?: AiInterview;
}
export const JobApplicationSchema =
  SchemaFactory.createForClass(JobApplication);

// dynamicFields: {
//     aadhaar: '12345',
//     pan: '12344',
// },
