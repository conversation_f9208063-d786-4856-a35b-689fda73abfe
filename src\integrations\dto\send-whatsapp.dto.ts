import { ApiProperty } from "@nestjs/swagger";
import { IsString, IsNotEmpty, IsEnum, IsOptional } from "class-validator";

export class SendWhatsAppDto {
    @ApiProperty({ description: 'Phone number to send the message to (with country code)', example: '918123456789' })
    @IsString()
    @IsNotEmpty()
    to: string;
  
    @ApiProperty({ description: 'Message type', enum: ['template', 'text'], default: 'text' })
    @IsString()
    @IsEnum(['template', 'text'])
    @IsOptional()
    type?: 'template' | 'text' = 'text';
  
    @ApiProperty({ description: 'Text message content', example: 'Hello from our platform!' })
    @IsString()
    @IsOptional()
    text?: string;
  
    @ApiProperty({ description: 'Template name (required if type is template)', example: 'appointment_reminder' })
    @IsString()
    @IsOptional()
    templateName?: string;
  
    @ApiProperty({ description: 'Template language code', example: 'en_US', default: 'en_US' })
    @IsString()
    @IsOptional()
    languageCode?: string = 'en_US';
  
    @ApiProperty({ 
      description: 'Template components (parameters)',
      example: [
        {
          type: 'body',
          parameters: [
            { type: 'text', text: 'John' },
            { type: 'text', text: 'May 10, 2025' }
          ]
        }
      ],
      required: false
    })
    @IsOptional()
    components?: any[];
  }