import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { BasicUser } from 'src/user/schemas/basic-user.schema';
import { Project } from './project.schema';
import { Org } from 'src/org/schemas/org.schema';

@Schema({ timestamps: true })
export class ProjectAllocation extends Document {
    @Prop({ type: Types.ObjectId, ref: 'Project', required: true })
    projectId: Project;

    @Prop({ type: Types.ObjectId, ref: 'BasicUser', required: false })
    userId: BasicUser;

    @Prop({ type: [String], required: false })
    roles: string[];

    @Prop({ type: Number, default: 1, required: false })
    allocation: number;

    @Prop({
        type: [{ type: Types.ObjectId, ref: 'BusinessUnit' }], // Array of ObjectId references to BusinessUnit
        required: false,
    })
    departments?: string[]; // This represents an array of BusinessUnit documents


    @Prop({ type: [Types.ObjectId], ref: 'BasicUser', default: [] })
    reportingTo: Types.ObjectId[];

    @Prop({ default: Date.now })
    allocatedAt: Date;

    @Prop({
        type: Boolean,
        required: false,
        default: false
    })
    isDeleted?: boolean;


}

export const ProjectAllocationSchema = SchemaFactory.createForClass(ProjectAllocation);