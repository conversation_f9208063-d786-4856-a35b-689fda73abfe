import { Modu<PERSON> } from '@nestjs/common';
import { WorkExperienceService } from './work-experience.service';
import { WorkExperienceController } from './work-experience.controller';
import { ConfigModule } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { UserModule } from 'src/user/user.module';
import { MongooseModule } from '@nestjs/mongoose';
import { WorkExperience, WorkExperienceSchema } from './schemas/work-experience.schema';
import { EndpointsRolesModule } from 'src/endpoints-roles/endpoints-roles.module';
@Module({
  imports: [
    ConfigModule,
    JwtModule, EndpointsRolesModule,
    UserModule,
    MongooseModule.forFeature([{ name:WorkExperience.name, schema: WorkExperienceSchema}])

  ],
  controllers: [WorkExperienceController],
  providers: [WorkExperienceService],
})
export class WorkExperienceModule {}
