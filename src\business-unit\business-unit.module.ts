import { forwardRef, Module } from '@nestjs/common';
import { BusinessUnitService } from './business-unit.service';
import { BusinessUnitController } from './business-unit.controller';
import { MongooseModule } from '@nestjs/mongoose';;
import { BusinessUnit, BusinessUnitSchema } from './schemas/business-unit.schema';
import { ConfigModule } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { BasicUser, BasicUserSchema } from 'src/user/schemas/basic-user.schema';
import { OrgModule } from 'src/org/org.module';
import { EndpointsRolesModule } from 'src/endpoints-roles/endpoints-roles.module';
@Module({
  controllers: [BusinessUnitController],
  providers: [BusinessUnitService],
  imports: [
    ConfigModule,
    JwtModule, EndpointsRolesModule,
    forwardRef(() => OrgModule),
    MongooseModule.forFeature([
      { name: BusinessUnit.name, schema: BusinessUnitSchema },
      { name: BasicUser.name, schema: BasicUserSchema }
    ])
  ],
  exports: [BusinessUnitService],
})
export class BusinessUnitModule { }
