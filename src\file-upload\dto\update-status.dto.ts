import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsIn, IsOptional } from 'class-validator';
import { Transform, TransformFnParams } from "class-transformer";
import { sanitizeWithStyle } from "src/utils/sanitize-with-style";

export class UpdateStatusDto {
  @ApiProperty({
    description: 'The new status of the file',
    example: 'approved',
    enum: ['pending', 'approved', 'rejected'],
  })
  @IsString()
  @IsIn(['pending', 'approved', 'rejected'])
  status: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Reference to the user associated with the file'
})
  @IsString()
  @IsOptional()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  user?: string; 

  @ApiProperty({
    type: String,
    required: false,
    description: 'Reference to the Organization associated with the file'
})
  @IsString()
  @IsOptional()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  org?: string; 
}
