import { Module } from '@nestjs/common';
import { PrivilegeService } from './privilege.service';
import { PrivilegeController } from './privilege.controller';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { CommonModule } from 'src/common/common.module';
import { getConnectionToken, MongooseModule } from '@nestjs/mongoose';
import { Privilege, PrivilegeSchema } from './schemas/privilege.schema';
import { EndpointsRolesModule } from 'src/endpoints-roles/endpoints-roles.module';
@Module({
  imports: [
    ConfigModule,
    JwtModule, EndpointsRolesModule,
    CommonModule,

    MongooseModule.forFeatureAsync([
      {
        name: Privilege.name,
        imports: [ConfigModule],
        useFactory: (configService: ConfigService) => {
          const schema = PrivilegeSchema;
          return schema;
        },
        inject: [getConnectionToken(), ConfigService],

      },
    ]),

  ],
  controllers: [PrivilegeController],
  providers: [PrivilegeService],
})
export class PrivilegeModule { }
