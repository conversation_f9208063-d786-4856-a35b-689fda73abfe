import { ApiHideProperty, ApiProperty } from "@nestjs/swagger";
import { Transform, TransformFnParams, Type } from "class-transformer";
import { IsBoolean, IsDate, IsEnum, IsISO8601, IsMongoId, IsNotEmpty, IsNumber, IsOptional, IsString, Length, Matches } from "class-validator";
import { sanitizeWithStyle } from "src/utils/sanitize-with-style";
import { Priority, RecurrenceInterval, Status } from "src/shared/constants";

export class MemberJobAssignDto {

    @ApiProperty({
      type: String,
      required: true,
      description: 'Job title',
    })
    @IsString()
    @IsNotEmpty()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    title: string;

    @ApiProperty({
      type: Date,
      required: true,
      description: 'Job assigned date and time',
      default: new Date(Date.UTC(new Date().getUTCFullYear(), new Date().getUTCMonth(), new Date().getUTCDate(), new Date().getUTCHours(), new Date().getUTCMinutes())),
    })
    @IsISO8601({strict: true})
    assignedDateAndTime: Date;
    
    @ApiProperty({
        type: Date,
        required: true,
        description: 'Due date and time for recruiter or team',
        default: new Date(Date.UTC(new Date().getUTCFullYear(), new Date().getUTCMonth(), new Date().getUTCDate(), new Date().getUTCHours(), new Date().getUTCMinutes())),
    })
    @IsISO8601({strict: true})
    dueDate: Date;

    @ApiProperty({
      type: Number,
      required: true,
    })
    @IsNumber()
    targetProfiles: number;
    
    
    @ApiProperty({
      type: String,
      required: true,
      description: 'Job assigned to candidate',
    })
    @IsString()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    job: string

    // @ApiProperty({
    //   type: Boolean,
    //   required: true,
    //   default: true
    // })
    // @IsBoolean()
    // isJobAssign: boolean;

    @ApiProperty({
      type: String,
      required: true,
      default: Priority.LOW,
      enum: Priority,
      description: 'Job priority',
    })
    @IsEnum(Priority)
    priority: Priority;

}
