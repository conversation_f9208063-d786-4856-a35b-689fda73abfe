import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsArray, IsBoolean, IsNotEmpty, IsNumber, IsObject, IsOptional, ValidateNested } from 'class-validator';
import { ClientAddressDto } from 'src/common/dto/client-address.dto';


export class ClientOnboardingDto {

    @ApiProperty({
        type: [String],
        required: false,
        description: 'Array of file metadata IDs associated',
    })
    @IsOptional()
    @IsArray()
    agreements?: string[];

    // @ApiProperty({
    //     type: Number,
    //     required: false,
    // })
    // @IsOptional()
    // @IsNumber()
    // fixedRate?: number;

    // @ApiProperty({
    //     type: Number,
    //     required: false,
    // })
    // @IsOptional()
    // @IsNumber()
    // percentageOfCTC?: number;

    // @ApiProperty({
    //     type: Object,
    //     required: false,
    //     example: {
    //         serviceType: 'Consulting',
    //         panNumber: '**********',
    //     },
    // })
    // @IsOptional()
    // @IsObject()
    // other?: Record<string, any>;

    // @ApiProperty({
    //     type: Boolean,
    //     required: false,
    // })
    // @IsOptional()
    // @IsBoolean()
    // agreeTerms?: boolean;

    // @ApiProperty({
    //     type: Number,
    //     required: true,
    //     description: 'Payment duration in days'
    // })
    // @IsNotEmpty()
    // @IsNumber()
    // paymentDuration: number;

    @ApiProperty({
        type: [ClientAddressDto],
        required: false,
    })
    @IsOptional()
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => ClientAddressDto)
    clientAddress?: ClientAddressDto[];

    // @ApiProperty({
    //     type: Number,
    //     required: true,
    //     description: 'Invoice duration in days'
    // })
    // @IsNotEmpty()
    // @IsNumber()
    // invoiceDuration: number;
}
