import { Modu<PERSON> } from '@nestjs/common';
import { IdentifierService } from './identifier.service';
import { IdentifierController } from './identifier.controller';
import { JwtModule } from '@nestjs/jwt';
import { MongooseModule } from '@nestjs/mongoose';
import { Identifier, IdentifierSchema } from './schemas/identifier.schema';
import { EndpointsRolesModule } from 'src/endpoints-roles/endpoints-roles.module';

@Module({
  controllers: [IdentifierController],
  imports: [JwtModule, EndpointsRolesModule, MongooseModule.forFeature([{ name: Identifier.name, schema: IdentifierSchema }])],
  providers: [IdentifierService],
})
export class IdentifierModule { }
