import { Lo<PERSON>, Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { MongooseModule } from '@nestjs/mongoose';
import { AppController } from './app.controller';
import { AppService } from './app.service';
('');
// import { Logger } from '@nestjs/common';
import { FastifyMulterModule } from '@nest-lab/fastify-multer';
import { MailerModule } from '@nestjs-modules/mailer';
import { HandlebarsAdapter } from '@nestjs-modules/mailer/dist/adapters/handlebars.adapter';
import { RouterModule } from '@nestjs/core';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { AccountTypeModule } from './account-type/account-type.module';
import { ActivityModule } from './activity/activity.module';
import { AlertModule } from './alert/alert.module';
import { AnnouncmentsModule } from './announcments/announcments.module';
import { routes } from './app.routes';
import { AssessmentModule } from './assessment/assessment.module';
import { AttendanceModule } from './attendance/attendance.module';
import { AuthModule } from './auth/auth.module';
import { BenchModule } from './bench/bench.module';
import { BgvHandlerModule } from './bgv-handler/bgv-handler.module';
import { BlogModule } from './blog/blog.module';
import { BusinessUnitModule } from './business-unit/business-unit.module';
import { CalendarModule } from './calendar/calendar.module';
import { CommonModule } from './common/common.module';
import { ContactModule } from './contact/contact.module';
import { CountryModule } from './country/country.module';
import { CustomFieldsModule } from './custom-fields/custom-fields.module';
import { DashboardModule } from './dashboard/dashboard.module';
import { DeleteUserModule } from './delete-user/delete-user.module';
import { DynamicFieldModule } from './dynamic-fields/dynamic-fields.module';
import { EducationQualificationModule } from './education-qualification/education-qualification.module';
import { EmailConfigModule } from './email-config/email-config.module';
import { EmailTemplateBuilderModule } from './email-template-builder/email-template-builder.module';
import { EmployeeModule } from './employee/employee.module';
import { EndpointPermissionsModule } from './endpoint-permissions/endpoint-permissions.module';
import { EndpointsRolesModule } from './endpoints-roles/endpoints-roles.module';
import { EvaluationFormModule } from './evaluation-form/evaluation-form.module';
import { FileUploadModule } from './file-upload/file-upload.module';
import { IdentifierModule } from './identifier/identifier.module';
import { IndustryModule } from './industry/industry.module';
import { IntegrationsModule } from './integrations/integrations.module';
import { InterviewModule } from './interview/interview.module';
import { InvoiceModule } from './invoices/invoice.module';
import { JobAllocationModule } from './job-allocation/job-allocation.module';
import { JobApplicationFormModule } from './job-application-form/job-application-form.module';
import { JobLocationModule } from './job-location/job-location.module';
import { JobModule } from './job/job.module';
import { LeaveManagmentModule } from './leave-managment/leave-managment.module';
import { MeetingModule } from './meeting/meeting.module';
import { MessageModule } from './message/message.module';
import { NoteModule } from './note/note.module';
import { NotificationsModule } from './notification/notifications.module';
import { OfferModule } from './offer/offer.module';
import { OnboardingModule } from './onboarding/onboarding.module';
import { OrgModule } from './org/org.module';
import { PipesModule } from './pipes/pipes.module';
import { PreferenceModule } from './preferences/preference.module';
import { PrivilegeModule } from './privilege/privilege.module';
import { ProjectsModule } from './projects/projects.module';
import { RateCardCategoryModule } from './rate-card-category/rate-card-category.module';
import { RateCardModule } from './rate-card/rate-card.module';
import { RecruitementTeamModule } from './recruitement-team/recruitement-team.module';
import { RecruiterTargetModule } from './recruiter-target/recruiter-target.module';
import { RegionModule } from './region/region.module';
import { ResumeModule } from './resume/resume.module';
import { RolePrivilegeModule } from './role-privilege/role-privilege.module';
import { RolesModule } from './roles/roles.module';
import { StageModule } from './stage/stage.module';
import { StateModule } from './state/state.module';
import { StatusModule } from './status/status.module';
import { TaskModule } from './task/task.module';
import { TimeSheetModule } from './time-sheet/time-sheet.module';
import { UserInboxConfigModule } from './user-inbox-config/user-inbox-config.module';
import { UserModule } from './user/user.module';
import { VendorInviteModule } from './vendor-invite/vendor-invite.module';
import { WorkExperienceModule } from './work-experience/work-experience.module';
import { WorkflowModule } from './workflow/workflow.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    RouterModule.register(routes),
    FastifyMulterModule,
    MailerModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        transport: {
          host: configService.get<string>('SMTP_HOST'),
          // host: 'us2.smtp.mailhostbox.com',
          port: configService.get<number>('SMTP_PORT'),
          secure: true,
          auth: {
            user: configService.get<string>('SMTP_USERNAME'),
            pass: configService.get<string>('SMTP_PASSWORD'),
          },
          tls: {
            rejectUnauthorized: false,
          },
        },
        defaults: {
          from: configService.get<string>('DEFAULT_EMAIL_SENDER'),
        },
        template: {
          dir: __dirname + '/templates',
          adapter: new HandlebarsAdapter(),
          options: {
            strict: true,
          },
        },
        // preview: true,
      }),
      inject: [ConfigService],
    }),
    EventEmitterModule.forRoot({
      // set this to `true` to use wildcards
      wildcard: false,
      // the delimiter used to segment namespaces
      delimiter: '.',
      // set this to `true` if you want to emit the newListener event
      newListener: false,
      // set this to `true` if you want to emit the removeListener event
      removeListener: false,
      // the maximum amount of listeners that can be assigned to an event
      // maxListeners: 10,
      // show event name in memory leak message when more than maximum amount of listeners is assigned
      verboseMemoryLeak: false,
      // disable throwing uncaughtException if an error event is emitted and it has no listeners
      ignoreErrors: false,
    }),
    // LoggerModule.forRoot(), // simple configuration - read https://www.npmjs.com/package/nestjs-pino#asynchronous-logging
    // LoggerModule.forRoot({
    //   pinoHttp: {
    //     stream: pino.destination({
    //       dest: './log.txt', // omit for stdout
    //       minLength: 4096, // Buffer before writing
    //       sync: false, // Asynchronous logging
    //     }),
    //   },
    // }),
    // MongooseModule.forRoot('mongodb://localhost/nest'),
    // JwtModule.registerAsync({
    //   imports: [ConfigModule],
    //   useFactory: async (configService: ConfigService) => ({
    //     secret: configService.get<string>('SECRET_KEY'),
    //     signOptions: {
    //       // expiresIn: '1h' ,
    //       expiresIn: configService.get<string>('TOKEN_EXPIRTY_PERIOD')
    //     },
    //   }),
    //   inject: [ConfigService],
    // }),
    MongooseModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => {
        const uri = configService.get<string>('MONGODB_URI');
        if (!uri) {
          throw new Error('MONGODB_URI environment variable is required');
        }
        return {
          uri,
          // Add connection options for better reliability
          connectTimeoutMS: 30000, // 30 seconds
          socketTimeoutMS: 30000, // 30 seconds
          serverSelectionTimeoutMS: 30000, // 30 seconds
          maxPoolSize: 10,
          retryWrites: true,
          retryReads: true,
        };
      },
      inject: [ConfigService],
    }),
    PipesModule,
    AuthModule,
    UserModule,
    JobModule,
    CountryModule,
    OrgModule,
    AlertModule,
    ContactModule,
    IndustryModule,
    AccountTypeModule,
    NoteModule,
    TaskModule,
    MessageModule,
    ActivityModule,
    StateModule,
    CommonModule,
    EducationQualificationModule,
    JobLocationModule,
    EvaluationFormModule,
    JobApplicationFormModule,
    FileUploadModule,
    WorkExperienceModule,
    ResumeModule,
    IdentifierModule,
    StageModule,
    WorkflowModule,
    OnboardingModule,
    InterviewModule,
    CalendarModule,
    EmailConfigModule,
    AssessmentModule,
    OfferModule,
    StatusModule,
    RegionModule,
    EmailTemplateBuilderModule,
    BusinessUnitModule,
    UserInboxConfigModule,
    // RealTimeMessagingModule,
    MeetingModule,
    JobAllocationModule,
    BlogModule,
    RecruitementTeamModule,
    VendorInviteModule,
    BenchModule,
    IntegrationsModule,
    PreferenceModule,
    RecruiterTargetModule,
    DeleteUserModule,
    DynamicFieldModule,
    RolesModule,
    PrivilegeModule,
    RolePrivilegeModule,
    EndpointPermissionsModule,
    EndpointsRolesModule,
    CustomFieldsModule,
    DashboardModule,
    RateCardCategoryModule,
    RateCardModule,
    NotificationsModule,
    AnnouncmentsModule,
    EmployeeModule,
    BgvHandlerModule,
    InvoiceModule,
    AttendanceModule,
    ProjectsModule,
    TimeSheetModule,
    LeaveManagmentModule,
  ],
  controllers: [AppController],
  providers: [AppService, Logger],
})
export class AppModule {}
