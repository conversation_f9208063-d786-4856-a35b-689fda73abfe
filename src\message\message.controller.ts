import { <PERSON>, Get, Post, Body, Patch, Param, Delete, Logger, UseGuards, Req, Query } from '@nestjs/common';
import { MessageService } from './message.service';
import { CreateMessageDto } from './dto/create-message.dto';
import { UpdateMessageDto } from './dto/update-message.dto';
import { ApiBearerAuth, ApiBody, ApiOperation, ApiParam, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { AuthJwtGuard } from 'src/auth/guards/auth-jwt/auth-jwt.guard';
import { validateArrayOfObjectIds, validateObjectId } from 'src/utils/validation.utils';
import { RolesGuard } from 'src/auth/guards/roles/roles.guard';
import { Roles } from 'src/auth/decorators/roles.decorator';
import { Role } from 'src/auth/enums/role.enum';

@Controller('')
@ApiTags('Messages')
export class MessageController {

  private readonly logger = new Logger(MessageController.name);

  constructor(private readonly messageService: MessageService) { }

  @Post()
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager)
  @Roles()
  @ApiOperation({ summary: 'Create a new message', description: `This endpoint allows you to create a new message. This is accessible for everyone.` })
  @ApiResponse({ status: 201, description: 'Message is saved. ' })
  @ApiResponse({ status: 400, description: 'Bad Request / Data. ' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  @ApiBody({ type: CreateMessageDto, description: 'The message details to create' })
  async create(@Req() req: any, @Body() createMessageDto: CreateMessageDto) {
    validateArrayOfObjectIds(createMessageDto.recipients)
    createMessageDto.sender = req.user._id;
    return await this.messageService.create(createMessageDto);
  }


  @Post(':messageId/star')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager)
  @Roles()
  @ApiOperation({ summary: 'Star a message by Id', description: `This endpoint stars a message by Id. This is accessible for everyone.` })
  @ApiResponse({ status: 200, description: 'Message is saved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  @ApiResponse({ status: 404, description: 'Message not found.' })
  @ApiParam({ name: 'messageId', description: 'id of the message' })
  starMessage(@Param('messageId') messageId: string, @Req() req: any) {
    const objId = validateObjectId(messageId);
    return this.messageService.star(objId, req.user._id);
  }

  @Get('sent')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager)
  @Roles()
  @ApiOperation({ summary: 'Retrieve all messages sent by the current user', description: `This endpoint allows you to retrieve all messages sent by the current user.This is accessible for everyone.` })
  @ApiResponse({ status: 200, description: 'Messages are retrieved.' })
  @ApiResponse({ status: 401, description: 'User not logged in.' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number', example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of items per page', example: 10 })
  findAllSentByUser(@Req() req: any, @Query('page') page: number = 1, @Query('limit') limit: number = 10) {
    return this.messageService.findAllSentByUser(req.user._id, page, limit);
  }

  @Get('inbox')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager)
  @Roles()
  @ApiOperation({ summary: 'Retrieve all messages received by the current user', description: `This endpoint allows you to retrieve all messages received by the current user.This is accessible for everyone.` })
  @ApiResponse({ status: 200, description: 'Messages are retrieved.' })
  @ApiResponse({ status: 401, description: 'User not logged in.' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number', example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of items per page', example: 10 })
  findAllReceivedByUser(@Req() req: any, @Query('page') page: number = 1, @Query('limit') limit: number = 10) {
    return this.messageService.findAllReceivedByUser(req.user._id, page, limit);
  }

  @Get('starred')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager)
  @Roles()
  @ApiOperation({ summary: 'Retrieve all messages starred by the current user', description: `This endpoint allows you to retrieve all messages starred by the current user.This is accessible for everyone.` })
  @ApiResponse({ status: 200, description: 'Messages are retrieved.' })
  @ApiResponse({ status: 401, description: 'User not logged in.' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number', example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of items per page', example: 10 })
  findAllStarredMessages(@Req() req: any, @Query('page') page: number = 1, @Query('limit') limit: number = 10) {
    return this.messageService.findAllStarredMessages(req.user._id, page, limit);
  }

  @Get('soft-delete')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager)
  @Roles()
  @ApiOperation({ summary: 'Retrieve all messages soft-deleted by the current user', description: `This endpoint allows you to retrieve all messages soft-deleted by the current user.This is accessible for everyone.` })
  @ApiResponse({ status: 200, description: 'Messages are retrieved.' })
  @ApiResponse({ status: 401, description: 'User not logged in.' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number', example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of items per page', example: 10 })
  findAllSoftDeletedMessages(@Req() req: any, @Query('page') page: number = 1, @Query('limit') limit: number = 10) {
    return this.messageService.findAllSoftDeletedMessages(req.user._id, page, limit);
  }

  @Get(':messageId')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager)
  @Roles()
  @ApiOperation({ summary: 'Retrieve a message by Id', description: `This endpoint allows you to create a new message.This is accessible for everyone.` })
  @ApiResponse({ status: 200, description: 'Message is retrieved.' })
  @ApiResponse({ status: 400, description: 'Bad Request: Invalid ID format.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  @ApiResponse({ status: 404, description: 'Message not found.' })
  @ApiParam({ name: 'messageId', description: 'id of the message' })
  findOne(@Param('messageId') messageId: string, @Req() req: any) {
    const objId = validateObjectId(messageId);
    return this.messageService.findOne(objId, req.user._id);
  }

  @Get('search')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager)
  @Roles()
  @ApiOperation({ summary: 'Search all messages', description: `This endpoint allows you to search for all the messages.This is accessible for everyone.` })
  @ApiResponse({ status: 200, description: 'Messages are retrieved.' })
  @ApiResponse({ status: 401, description: 'User not logged in.' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number', example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of items per page', example: 10 })
  async searchMessages(@Req() req: any, @Query('query') query: string, @Query('page') page: number = 1, @Query('limit') limit: number = 10) {
    return this.messageService.searchMessages(req.user._id, query, page, limit);
  }

  // @Patch(':messageId')
  // @ApiBearerAuth()
  // @UseGuards(AuthJwtGuard)
  // @ApiOperation({ summary: 'Edit a message by Id', description: `This endpoint updates a message by Id. This is accessible for all` })
  // @ApiResponse({ status: 200, description: 'Message is saved.' })
  // @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  // @ApiResponse({ status: 404, description: 'Message not found.' })
  // @ApiParam({ name: 'messageId', description: 'id of the message' })
  // update(@Param('messageId') messageId: string, @Body() updateMessageDto: UpdateMessageDto, @Req() req: any) {
  //   validateArrayOfObjectIds(updateMessageDto.recipients)
  //   const objId = validateObjectId(messageId);
  //   return this.messageService.update(objId, updateMessageDto, req.user._id);
  // }

  @Patch(':messageId/read')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager)
  @Roles()
  @ApiOperation({ summary: 'Read a  message by Id', description: `This endpoint reads a message by Id. This is accessible for everyone.` })
  @ApiResponse({ status: 200, description: 'Message is read.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  @ApiParam({ name: 'messageId', description: 'id of the message' })
  @ApiQuery({ name: 'recipientId', description: 'id of the recipient' })
  async markAsRead(
    @Param('messageId') messageId: string,
    @Query('recipientId') recipientId: string
  ) {
    const objMessageId = validateObjectId(messageId);
    const objRecipientId = validateObjectId(recipientId);
    return this.messageService.markAsRead(objMessageId, objRecipientId);
  }

  @Patch(':messageId/unread')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager)
  @Roles()
  @ApiOperation({ summary: 'Unread a  message by Id', description: `This endpoint unreads a message by Id. This is accessible for everyone.` })
  @ApiResponse({ status: 200, description: 'Message is unread.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  @ApiParam({ name: 'messageId', description: 'id of the message' })
  @ApiQuery({ name: 'recipientId', description: 'id of the recipient' })
  async markAsUnread(
    @Param('messageId') messageId: string,
    @Query('recipientId') recipientId: string
  ) {
    const objMessageId = validateObjectId(messageId);
    const objRecipientId = validateObjectId(recipientId);
    return this.messageService.markAsUnread(objMessageId, objRecipientId);
  }

  @Patch(':messageId/restore')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager)
  @Roles()
  @ApiOperation({ summary: 'Restore a soft-deleted message by Id', description: `This endpoint restores a soft-deleted message by Id. This is accessible for everyone.` })
  @ApiParam({ name: 'messageId', description: 'id of the message' })
  @ApiResponse({ status: 200, description: 'Message is restored.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  // @ApiResponse({ status: 403, description: `Forbidden. User with role "admin" can only use this end point.` })
  @ApiResponse({ status: 404, description: 'Message not found.' })
  restoreSoftDeletedmessages(@Param('messageId') messageId: string) {
    const objId = validateObjectId(messageId);
    return this.messageService.restoreSoftDeletedmessages(objId);
  }

  @Patch(':messageId/star')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager)
  @Roles()
  @ApiOperation({ summary: 'Unstar a  message by Id', description: `This endpoint unstars an message by Id. This is accessible for everyone.` })
  @ApiResponse({ status: 200, description: 'Message is unstarred.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  @ApiParam({ name: 'messageId', description: 'id of the message' })
  unstarMessage(@Param('messageId') messageId: string, @Req() req: any) {
    const objId = validateObjectId(messageId);
    return this.messageService.unstarMessage(objId, req.user._id);
  }

  @Delete(':messageId/soft-delete/all')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager)
  @Roles()
  @ApiOperation({ summary: 'Soft delete a message by Id for everyone', description: `This endpoint soft-deletes an message by Id for all members of the message. This is accessible for everyone.` })
  @ApiResponse({ status: 200, description: 'Message is deleted.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  @ApiParam({ name: 'messageId', description: 'id of the message' })
  deleteForAll(@Param('messageId') messageId: string, @Req() req: any) {
    const objId = validateObjectId(messageId);
    return this.messageService.deleteForAll(objId, req.user._id);
  }

  @Delete(':messageId/soft-delete/me')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager)
  @Roles()
  @ApiOperation({ summary: 'Soft delete a message by Id for currently logged-in user', description: `This endpoint soft-deletes a message by Id for the current logged in user. This is accessible for everyone.` })
  @ApiResponse({ status: 200, description: 'Message is deleted.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  @ApiParam({ name: 'messageId', description: 'id of the message' })
  deleteForMe(@Param('messageId') messageId: string, @Req() req: any) {
    const objId = validateObjectId(messageId);
    return this.messageService.deleteForMe(objId, req.user._id);
  }

  // This endpoint soft-deletes a message by Id for one of the recipients of currently logged in user. This is accessible for all
  // Soft delete a message by Id for currently logged-in user
  @Delete(':messageId/soft-delete/:userId')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager)
  @Roles()
  @ApiOperation({ summary: 'Soft delete a message by Id for one of the recipients of logged-in user', description: `This endpoint soft-deletes a message by Id for one of the recipients of currently logged in user. This is accessible for everyone.` })
  @ApiResponse({ status: 200, description: 'Message is deleted.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  @ApiParam({ name: 'messageId', description: 'id of the message' })
  @ApiParam({ name: 'userId', description: 'id of the user' })
  deleteForUser(@Param('messageId') messageId: string, @Param('userId') userId: string, @Req() req: any) {
    const messageObjId = validateObjectId(messageId);
    const userObjId = validateObjectId(userId);
    return this.messageService.deleteForUser(messageObjId, userObjId, req.user._id);
  }

  @Get('message-counts')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager)
  @Roles()
  @ApiOperation({
    summary: 'Retrieve the count of messages for the current user',
    description: `This endpoint provides the count of messages sent, inbox, starred, and soft-deleted (trash) by the current user.`,
  })
  @ApiResponse({ status: 200, description: 'Message counts retrieved successfully.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  async getMessageCounts(@Req() req: any) {
    const userId = req.user._id;
    const counts = await this.messageService.getMessageCounts(userId);
    return counts;
  }

}
