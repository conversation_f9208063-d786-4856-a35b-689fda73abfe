import { Controller, Get, Post, Body, Patch, Param, Delete, Logger, Query, Req, UseGuards, BadRequestException } from '@nestjs/common';
import { EmployeeService } from './employee.service';
import { ApiBearerAuth, ApiOperation, ApiParam, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Roles } from 'src/auth/decorators/roles.decorator';
import { Role } from 'src/auth/enums/role.enum';
import { AuthJwtGuard } from 'src/auth/guards/auth-jwt/auth-jwt.guard';
import { RolesGuard } from 'src/auth/guards/roles/roles.guard';
import { validateObjectId } from 'src/utils/validation.utils';
import { CreateBankDetailsDto } from './dto/create-bank-details.dto';
import { UpdateEmployeeAddressDto } from './dto/update-employee-address.dto';
import { EmergencyContactDto } from './dto/emergency-contact.dto';
import { EmployeesQueryDTO } from './dto/query.employees.dto';

@Controller('')
@ApiTags('employee')
export class EmployeeController {

    private readonly logger = new Logger(EmployeeController.name);

    constructor(private readonly employeeService: EmployeeService) { }

    @Get('all')
    @ApiOperation({
        summary: 'Retrieve All employees of the organization',
        description: 'This endpoint retrieves all employees of the organization. This is accessible only for everyone.'
    })
    @ApiBearerAuth()
    @UseGuards(AuthJwtGuard)
    // @Roles(Role.Admin, Role.Recruiter, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager)
    @Roles()
    @ApiResponse({ status: 200, description: 'No employees retrieved.' })
    @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
    // @ApiResponse({ status: 403, description: 'Forbidden' })
    @ApiResponse({ status: 404, description: 'No employees retrieved found.' })
    findAllEmployees(@Req() req: any,@Query() query: EmployeesQueryDTO) {
        return this.employeeService.findAllEmployees(req.user,query);
    }

    @Get(':employeeId')
    @ApiOperation({
        summary: 'Retrieve employee by Id',
        description: 'This endpoint retrieves an employee by its Id. This is accessible only for everyone.'
    })
    @ApiBearerAuth()
    @UseGuards(AuthJwtGuard)
    // @Roles(Role.Admin, Role.Recruiter, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager)
    @Roles()
    @ApiResponse({ status: 200, description: 'No employee retrieved.' })
    @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
    // @ApiResponse({ status: 403, description: 'Forbidden' })
    @ApiResponse({ status: 404, description: 'No employee retrieved found.' })
    @ApiParam({ name: 'employeeId', description: 'The ID of the employee to retrieve payment details for.' })
    findEmployeeById(@Req() req: any,@Param('employeeId') employeeId: string) {
        return this.employeeService.findEmployeeById(req.user,employeeId);
    }

    @Post('bank-details')
    @ApiOperation({
        summary: 'Create a new BankDetails',
        description: `This endpoint allows you to create a new offer. Accessible only to users with roles "${Role.BUHead}", "${Role.ResourceManager}", "${Role.DeliveryManager}"."${Role.TeamLead},and "${Role.AccountManager}"`
    })
    @ApiBearerAuth()
    @UseGuards(AuthJwtGuard)
    // @Roles(Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager)
    @Roles()
    @ApiResponse({ status: 201, description: 'The offer is created.' })
    @ApiResponse({ status: 400, description: 'Bad Request / Data.' })
    @ApiResponse({ status: 401, description: 'Unauthorized access. Authentication is required.' })
    @ApiResponse({ status: 403, description: `Forbidden. Only users with roles "${Role.BUHead}", "${Role.ResourceManager}", "${Role.DeliveryManager}"."${Role.TeamLead},and "${Role.AccountManager}" are permitted to use this endpoint.` })
    createBankDetails(@Req() req: any, @Body() createBankDetailsDto: CreateBankDetailsDto) {
        return this.employeeService.saveBankDetails(req.user, createBankDetailsDto);
    }

    @Get('bank-details')
    @ApiOperation({
        summary: 'Retrieve an paymentDetails by Application Id',
        description: 'This endpoint retrieves an paymentDetails by its Application Id. This is accessible only for everyone..'
    })
    @ApiBearerAuth()
    @UseGuards(AuthJwtGuard)
    // @Roles(Role.Admin, Role.Recruiter, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager)
    @Roles()
    @ApiResponse({ status: 200, description: 'Offer retrieved.' })
    @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
    // @ApiResponse({ status: 403, description: 'Forbidden' })
    @ApiResponse({ status: 404, description: 'Offer not found.' })
    findMyBankDetails(@Req() req: any) {
        return this.employeeService.findMyBankDetails(req.user);
    }

    @Get(':employeeId/bank-details')
    @ApiOperation({
        summary: 'Retrieve an paymentDetails by Application Id',
        description: 'This endpoint retrieves an paymentDetails by its Application Id. This is accessible only for everyone..'
    })
    @ApiBearerAuth()
    @UseGuards(AuthJwtGuard)
    // @Roles(Role.Admin, Role.Recruiter, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager)
    @Roles()
    @ApiResponse({ status: 200, description: 'Offer retrieved.' })
    @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
    // @ApiResponse({ status: 403, description: 'Forbidden' })
    @ApiResponse({ status: 404, description: 'Offer not found.' })
    @ApiParam({ name: 'employeeId', description: 'The ID of the employee to retrieve payment details for.' })
    findEmployeeBankDetails(@Req() req: any, @Param('employeeId') employeeId: string) {
        return this.employeeService.findEmployeeBankDetails(employeeId,req.user);
    }

    @Patch('employee-address')
    @ApiOperation({
        summary: 'Create a new BankDetails',
        description: `This endpoint allows you to create a new offer. Accessible only to users with roles "${Role.BUHead}", "${Role.ResourceManager}", "${Role.DeliveryManager}"."${Role.TeamLead},and "${Role.AccountManager}"`
    })
    @ApiBearerAuth()
    @UseGuards(AuthJwtGuard)
    // @Roles(Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager)
    @Roles()
    @ApiResponse({ status: 201, description: 'The offer is created.' })
    @ApiResponse({ status: 400, description: 'Bad Request / Data.' })
    @ApiResponse({ status: 401, description: 'Unauthorized access. Authentication is required.' })
    @ApiResponse({ status: 403, description: `Forbidden. Only users with roles "${Role.BUHead}", "${Role.ResourceManager}", "${Role.DeliveryManager}"."${Role.TeamLead},and "${Role.AccountManager}" are permitted to use this endpoint.` })
    updateEmployeeAddress(@Req() req: any, @Body() updateEmployeeAddressDto: UpdateEmployeeAddressDto) {
        return this.employeeService.updateEmployeeAddressDetails(req.user, updateEmployeeAddressDto);
    }

    @Get('address-details')
    @ApiOperation({
        summary: 'Retrieve an paymentDetails by Application Id',
        description: 'This endpoint retrieves an paymentDetails by its Application Id. This is accessible only for everyone..'
    })
    @ApiBearerAuth()
    @UseGuards(AuthJwtGuard)
    // @Roles(Role.Admin, Role.Recruiter, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager)
    @Roles()
    @ApiResponse({ status: 200, description: 'Offer retrieved.' })
    @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
    // @ApiResponse({ status: 403, description: 'Forbidden' })
    @ApiResponse({ status: 404, description: 'Offer not found.' })
    findMyAddressDetails(@Req() req: any) {
        return this.employeeService.findMyAddressDetails(req.user);
    }

    @Patch('emergency-contact')
    @ApiOperation({
        summary: 'Create a new BankDetails',
        description: `This endpoint allows you to create a new offer. Accessible only to users with roles "${Role.BUHead}", "${Role.ResourceManager}", "${Role.DeliveryManager}"."${Role.TeamLead},and "${Role.AccountManager}"`
    })
    @ApiBearerAuth()
    @UseGuards(AuthJwtGuard)
    // @Roles(Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager)
    @Roles()
    @ApiResponse({ status: 201, description: 'The offer is created.' })
    @ApiResponse({ status: 400, description: 'Bad Request / Data.' })
    @ApiResponse({ status: 401, description: 'Unauthorized access. Authentication is required.' })
    @ApiResponse({ status: 403, description: `Forbidden. Only users with roles "${Role.BUHead}", "${Role.ResourceManager}", "${Role.DeliveryManager}"."${Role.TeamLead},and "${Role.AccountManager}" are permitted to use this endpoint.` })
    updateEmergencyContact(@Req() req: any, @Body() emergencyContactDto: EmergencyContactDto) {
        return this.employeeService.updateEmergencyContact(req.user, emergencyContactDto);
    }

    @Get('emergency-contact')
    @ApiOperation({
        summary: 'Retrieve an paymentDetails by Application Id',
        description: 'This endpoint retrieves an paymentDetails by its Application Id. This is accessible only for everyone..'
    })
    @ApiBearerAuth()
    @UseGuards(AuthJwtGuard)
    // @Roles(Role.Admin, Role.Recruiter, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager)
    @Roles()
    @ApiResponse({ status: 200, description: 'Offer retrieved.' })
    @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
    // @ApiResponse({ status: 403, description: 'Forbidden' })
    @ApiResponse({ status: 404, description: 'Offer not found.' })
    findMyEmergencyContactDetails(@Req() req: any) {
        return this.employeeService.findMyEmergencyContactDetails(req.user);
    }

}

