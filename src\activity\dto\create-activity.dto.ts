import { ApiProperty, ApiHideProperty } from "@nestjs/swagger";
import { Transform, TransformFnParams } from "class-transformer";
import { IsNotEmpty, IsOptional, IsString, IsBoolean, Length, IsISO8601, IsMongoId } from "class-validator";
import { Types } from 'mongoose';
import { sanitizeWithStyle } from "src/utils/sanitize-with-style";

export class CreateActivityDto {
    @ApiProperty({ 
        type: String, 
        required: false, 
        description: 'Reference to the Contact associated with the activity' 
    })
    @IsOptional()
    @IsMongoId()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    contact?: string;

    @ApiProperty({
        type: String,
        required: false,
        description: 'Title of the activity'
    })
    @IsOptional()
    @IsString()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    title?: string;

    @ApiHideProperty()
    @IsOptional()
    @IsString()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    titleWithPlaceholders?: string;

    @ApiProperty({
        type: String,
        required: false,
        description: 'Reference to the Organization Account associated with the activity'
    })
    @IsOptional()
    @IsMongoId()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    org?: string;

    @ApiProperty({
        type: String,
        required: false,
        description: 'Reference to the Task associated with the activity'
    })
    @IsMongoId()
    @IsString()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    task?: string;

    @ApiProperty({
        type: String,
        required: false,
        description: 'Reference to the Region associated with the activity'
    })
    @IsOptional()
    @IsMongoId()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    region?: string;

    @ApiProperty({
        type: String,
        required: false,
        description: 'Reference to the BusinessUnit associated with the activity'
    })
    @IsOptional()
    @IsMongoId()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    businessUnit?: string;

    @ApiProperty({
        type: String,
        required: false,
        description: 'Reference to the User associated with the activity'
    })
    @IsOptional()
    @IsMongoId()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    user?: string;

    @ApiProperty({
        type: String,
        required: false,
        description: 'Reference to the Task associated with the activity'
    })
    @IsMongoId()
    @IsString()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    note?: string;

    @ApiProperty({
        type: Date,
        required: false,
        description: 'Task due date',
        default: new Date(Date.UTC(new Date().getUTCFullYear(), new Date().getUTCMonth(), new Date().getUTCDate(), new Date().getUTCHours(), new Date().getUTCMinutes())),
    })
    // YYYY-MM-DDThh:mm:ssZ - example : 2024-05-27T00:00:00.000Z
    @IsISO8601({ strict: true })
    @Length(10, 24)
    dueDate?: Date;



    // @ApiProperty({ 
    //     type: String, 
    //     required: false, 
    //     description: 'Reference to the Email associated with the activity' 
    // })
    // @IsOptional()
    // @IsString()
    // email?: string;

    // @ApiProperty({ 
    //     type: String, 
    //     required: false, 
    //     description: 'Reference to the Email associated with the activity' 
    // })
    // @IsOptional()
    // @IsString()
    // job?: string;


    @ApiProperty({
        type: Boolean,
        required: false,
        default: false,
        description: 'Indicates if the activity is deleted',
    })
    @IsOptional()
    @IsBoolean()
    isDeleted?: boolean;

    @ApiProperty({
        type: String,
        required: true,
        description: 'Reference to the User who created the activity'
    })
    @IsNotEmpty()
    @IsMongoId()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    actor: string;

    @ApiProperty({
        type: String,
        required: false,
        description: 'Reference to the parent Activity, if any'
    })
    @IsOptional()
    @IsString()
   // @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    parentActivity?: string;

    @ApiHideProperty()
    @IsOptional()
    @IsString()
    @Transform((params: TransformFnParams) => params.value.map((value: string) => sanitizeWithStyle(value)))
    childActivities?: string[];

    // @ApiProperty({
    //     type: String,
    //     required: false,
    //     description: 'Comment related to that activity'
    // })
    // @IsOptional()
    // @IsString()
    // comment?: string;

    @ApiProperty({
        type: Types.ObjectId,
        required: false,
        description: 'Comment Id related to that activity'
    })
    @IsOptional()
    @IsString()
    comment?: Types.ObjectId;


    // @ApiProperty({
    //     type: [String],
    //     required: false,
    //     description: 'Attachments related to that activity'
    // })
    // @IsOptional()
    // @IsString({ each: true })
    // @Transform((params: TransformFnParams) => params.value.map((value: string) => sanitizeWithStyle(value)))
    // attachments?: string[];

    @ApiProperty({
        type: String,
        required: false,
        description: 'Reference to the Rate card associated with the activity'
    })
    @IsOptional()
    @IsMongoId()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    rateCard?: string;
}