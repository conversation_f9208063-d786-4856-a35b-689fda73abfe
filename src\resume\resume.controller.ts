import { <PERSON>, Get, Post, Body, Patch, Param, Delete, UseGuards, Logger, Req } from '@nestjs/common';
import { UpdateResumeDto } from './dto/update-resume.dto';
import { ApiBearerAuth, ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { RolesGuard } from 'src/auth/guards/roles/roles.guard';
import { AuthJwtGuard } from 'src/auth/guards/auth-jwt/auth-jwt.guard';
import { Roles } from 'src/auth/decorators/roles.decorator';
import { Role } from 'src/auth/enums/role.enum';
import { validateObjectId } from 'src/utils/validation.utils';
import { ResumeService } from './resume.service';
import { CreateResumeDto } from './dto/create-resume.dto';

@Controller('')
@ApiTags('Resumes')
export class ResumeController {
  constructor(private readonly resumeService: ResumeService) { }

  private readonly logger = new Logger(ResumeController.name);

  @Post('')
  @ApiOperation({ summary: 'Creates a new candidate resume', description: `This endpoint allows you to create a new candidate resume. This is accessible only for "${Role.SuperAdmin}" , "${Role.Admin, Role.JobSeeker}", "${Role.SalesRep}".` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.SuperAdmin, Role.Admin, Role.JobSeeker, Role.SalesRep)
  @Roles()
  @ApiResponse({ status: 201, description: 'Candidate resume is saved.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Data.' })
  @ApiResponse({ status: 401, description: 'Unauthorized..' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role super-admin, admin and sales-rep can only use this end point.' })
  create(@Req() req: any, @Body() createCandidateDto: CreateResumeDto) {
    const userId = req.user._id;
    return this.resumeService.create(createCandidateDto, userId);
  }

  @Get()
  @ApiOperation({ summary: 'Retrieve all candidate resumes', description: `This endpoint returns a list of all candidate resumes. This is accessible only for "${Role.SuperAdmin}" , "${Role.Admin, Role.JobSeeker}", "${Role.SalesRep}".` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.SuperAdmin, Role.Admin, Role.JobSeeker, Role.SalesRep)
  @Roles()
  @ApiResponse({ status: 200, description: 'All candidate resumes are retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role super-admin, admin and sales-rep can only use this end point.' })
  findAll(@Req() req: any) {
    const userId = req.user._id;
    return this.resumeService.findAll(userId);
  }

  @Get(':resumeId')
  @ApiOperation({ summary: 'Retrieve a candidate resume by Id', description: `This endpoint returns a candidate resume by its Id. This is accessible only for "${Role.SuperAdmin}" , "${Role.Admin, Role.JobSeeker}", "${Role.SalesRep}".` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.SuperAdmin, Role.Admin, Role.JobSeeker, Role.SalesRep)
  @Roles()
  @ApiResponse({ status: 200, description: 'Candidate resume is retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role super-admin, admin and sales-rep can only use this end point.' })
  findOne(@Param('resumeId') resumeId: string) {
    const objId = validateObjectId(resumeId);
    return this.resumeService.findOne(objId);
  }

  @Patch(':resumeId')
  @ApiOperation({ summary: 'Update a candidate resume by Id', description: `This endpoint updates a candidate resume by Id. This is accessible only for "${Role.SuperAdmin}" , "${Role.Admin, Role.JobSeeker}", "${Role.SalesRep}".` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.SuperAdmin, Role.Admin, Role.JobSeeker)
  @Roles()
  @ApiResponse({ status: 200, description: 'Candidate resume is updated.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @ApiResponse({ status: 404, description: 'Candidate resume not found.' })
  @ApiParam({ name: 'resumeId', description: 'Id of the candidate resume' })
  update(@Param('resumeId') resumeId: string, @Body() updateResumeDto: UpdateResumeDto) {
    const objId = validateObjectId(resumeId);
    return this.resumeService.update(objId, updateResumeDto);
  }

  @Delete(':resumeId/hard-delete')
  @ApiOperation({ summary: 'Permannently delete a candidate resume by Id', description: `This endpoint permanently deletes a candidate resume by Id. This is accessible only for "${Role.SuperAdmin}" , "${Role.Admin, Role.JobSeeker}",  "${Role.SalesRep}".` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.SuperAdmin, Role.Admin, Role.JobSeeker)
  @Roles()
  @ApiResponse({ status: 200, description: 'Candidate resume deleted permanently.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @ApiResponse({ status: 404, description: 'Candidate resume not found.' })
  @ApiParam({ name: 'resumeId', description: 'Id of the candidate' })
  remove(@Param('resumeId') resumeId: string) {
    const objId = validateObjectId(resumeId);
    return this.resumeService.remove(objId);
  }
}
