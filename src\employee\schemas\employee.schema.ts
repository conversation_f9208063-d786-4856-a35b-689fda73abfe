import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Types, Document, HydratedDocument } from 'mongoose';
import { Country } from 'src/country/schemas/country.schema';
import { JobApplication } from 'src/job-application-form/schemas/job-application.schema';
import { Job } from 'src/job/schemas/job.schema';
import { Org } from 'src/org/schemas/org.schema';
import { EmploymentType } from 'src/shared/constants';
import { City } from 'src/state/schemas/city.schema';
import { State } from 'src/state/schemas/state.schema';
import { BasicUser } from 'src/user/schemas/basic-user.schema';
import { BankDetails } from './bank-details.schema';

export type TempEmployeeDocument = HydratedDocument<Employee>;

class Address {

  @Prop({
    type: String,
    required: false,
    trim: true,
  })
  addressLine1?: string;

  @Prop({
    type: String,
    required: false,
    trim: true,
  })
  landmark?: string;

  @Prop({
    type: Types.ObjectId,
    required: false,
    ref: 'Country'
  })
  country?: string;

  @Prop({
    type: Types.ObjectId,
    required: false,
    ref: 'State'
  })
  state?: string;

  @Prop({
    type: Types.ObjectId,
    required: false,
    ref: 'City'
  })
  city?: string;

  @Prop({
    type: String,
    required: false,
    trim: true,
  })
  pinCode: string;

  @Prop({
    type: String,
    ref: 'FileMetadata',
    required: false
  })
  addressProofDoc?: string;
}

class EmergencyContact {

  @Prop({
    type: String,
    required: false,
    trim: true,
  })
  name?: string;

  @Prop({
    type: String,
    required: false,
    trim: true,
  })
  relationship?: string;

  @Prop({
    type: String,
    required: false,
    trim: false,
  })
  contactNumber?: string;

  @Prop({
    type: String,
    required: false,
    trim: true,
  })
  alternateNumber?: string;
}


@Schema({ timestamps: true })
export class Employee  {
  @Prop({
    required: false,
    type: Types.ObjectId,
    ref: 'Org'
  })
  endClientOrg?: Org;

  @Prop({
    required: false,
    type: Types.ObjectId,
    ref: 'Org'
  })
  payRollOrg: Org;

  @Prop({
    type: Types.ObjectId,
    ref: 'Job',
    required: true
  })
  job: Job;

  @Prop({
    type: String,
    required: true,
    trim: true,
  })
  jobTitle: string;

  @Prop({
    type: Types.ObjectId,
    required: true,
    ref: 'JobApplication',
  })
  jobApplication: JobApplication;

  @Prop({
    type: String,
    required: true,
    trim: true,
  })
  firstName: string;

  @Prop({
    type: String,
    required: true,
    trim: true,
  })
  lastName: string;

  @Prop({
    type: String,
    required: true,
    // unique: true,
    // index:true
    lowercase: true,
    trim: true
  })
  email: string;

  @Prop({
    type: String,
    required: false,
    trim: true,
  })
  contactNumber?: string;

  @Prop({
    type: String,
    required: false,
    trim: true,
    //default: EmploymentType.FullTime,
    enum: Object.values(EmploymentType),
  })
  employmentType?: string;

  @Prop({
    type: Number,
    required: false,
    min: 0,
    default: 0,
  })
  ctcFromClient?: number;

  @Prop({
    type: Number,
    required: false,
    min: 0,
    default: 0,
  })
  ctcToCandidate?: number;

  @Prop({
    type: Number,
    required: false,
    min: 0,
    default: 0,
  })
  hourlyRateFromClient?: number;

  @Prop({ type: Number, required: false, min: 0, default: 0 })
  hourlyPayToCandidate?: number;

  @Prop({ type: Date, required: false, })
  effectiveStartDate?: Date;

  @Prop({
    required: true,
    type: Types.ObjectId, ref: 'BasicUser'
  })
  createdBy: BasicUser;

  @Prop({
    type: Types.ObjectId,
    required: false,
    ref: 'Bgv',
  })
  bgvId?: Types.ObjectId;

  @Prop({
    type: Address,
    required: false
  })
  currentAddress?: Address;

  @Prop({
    type: Address,
    required: false
  })
  permanentAddress?: Address;

  @Prop({
    type: Boolean,
    required: false,
    default: false
  })
  sameAsCurrentAddress?: boolean;

  @Prop({
    type: Types.ObjectId,
    required: false,
    ref: 'BankDetails',
  })
  bankDetails?: BankDetails;

  @Prop({
    type: EmergencyContact,
    required: false
  })
  emergencyContact?: EmergencyContact;

  @Prop({
    type: Boolean,
    required: false,
    default: false
  })
  isDeleted?: boolean;

}

export const EmployeeSchema = SchemaFactory.createForClass(Employee);
