import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Types, Schema as MongooseSchema } from 'mongoose';
import { HeadCount, SourceType, OrgStatus, OrgType, EmailTemplateEvent, DurationUnit, EmploymentType } from 'src/shared/constants';
import { Industry } from 'src/industry/schemas/industry.schema';
import { BasicUser } from 'src/user/schemas/basic-user.schema';
import { ContactInformation } from 'src/common/schemas/contact-information.schema';
import { AccountType } from 'src/account-type/schemas/account-type.schema';
import { SocialMediaLink } from 'src/common/schemas/social-media-link.schema';
import { AddressInformation } from 'src/common/schemas/address-information.schema';
import { State } from 'src/state/schemas/state.schema';
import { Country } from 'src/country/schemas/country.schema';
import { FileMetadata } from 'src/file-upload/schemas/file-metadata.schema';
import { City } from 'src/state/schemas/city.schema';
import { BusinessUnit } from 'src/business-unit/schemas/business-unit.schema';
import { ClientAddress, ClientAddressSchema } from 'src/common/schemas/client-address.schema';
import { BgvHandler } from 'src/bgv-handler/schemas/bgv-handler.schema';
import { CompanyAddress, CompanyAddressSchema } from 'src/common/schemas/company-address.dto';

export type OrgDocument = HydratedDocument<Org>;


// Prop decorator can accept more options - read here - https://mongoosejs.com/docs/schematypes.html#schematype-options
// and here - https://docs.nestjs.com/techniques/mongodb#model-injection


// Define the subdocument schema for Placeholder
@Schema({ timestamps: true })
export class Placeholder {
  @Prop({ required: true })
  name: string;

  @Prop({ required: false })
  description?: string;

  @Prop({ default: 'none' })
  defaultValue?: string;

  @Prop({ required: true })
  jsonPath: string;

  @Prop({ required: false })
  collectionName?: string;

  @Prop({ required: false })
  fieldName?: string;

  @Prop({ required: false, ref: 'Org' })
  orgId?: Types.ObjectId;

  @Prop({ default: false })
  isDefault?: boolean;

  // @Prop({
  //   type: String,
  //   required: false,
  //   trim: true,
  //   enum: Object.values(EmailTemplateEvent),
  // })
  // eventName?: string;

  @Prop({ required: false, ref: 'EmailTemplate' })
  emailTemplate?: string;
}

export const PlaceholderSchema = SchemaFactory.createForClass(Placeholder);
@Schema({
  timestamps: true
})
export class Org {

  // TODO: UNIQUE -> check all possibilities 
  // Not unique verified from salesforce
  @Prop({
    type: String,
    required: true,
    // unique: true,
    trim: true
  })
  title: string;

  @Prop({
    type: String,
    required: false,
    trim: true
  })
  description?: string;

  @Prop({
    type: Types.ObjectId,
    required: false
  })
  logo?: FileMetadata;

  @Prop({
    type: Types.ObjectId,
    required: false
  })
  thumbnail?: FileMetadata;

  @Prop({
    type: String,
    required: false,
    trim: true
  })
  socialDescription?: string;

  @Prop({
    type: String,
    required: false,
    trim: true
  })
  legalName?: string;

  @Prop({
    type: String,
    required: false,
    trim: true,
  })
  websiteUrl?: string;

  @Prop({
    type: [ContactInformation],
    required: false,
    ref: 'ContactInformation'
  })
  contactDetails?: ContactInformation[];

  @Prop({ type: [AddressInformation], required: false })
  contactAddress?: AddressInformation[];

  @Prop({
    type: Types.ObjectId,
    required: false,
    ref: 'Industry'
  })
  industryOrDomain?: Industry;

  @Prop({
    type: Types.ObjectId,
    required: false,
    ref: 'BusinessUnit'
  })
  businessUnit?: BusinessUnit;

  @Prop({
    type: String,
    required: false,
    trim: true
  })
  linkedInUrl?: string;

  @Prop({
    type: Boolean,
    required: false,
    default: false
  })
  isDeleted?: boolean;

  @Prop({
    type: Boolean,
    required: false,
    default: false
  })
  isRejected?: boolean;

  @Prop({
    type: Boolean,
    required: false,
    default: false
  })
  isSuspended?: boolean;

  @Prop({
    type: Boolean,
    required: false,
    default: false
  })
  isBlocked?: boolean;

  @Prop({
    type: Boolean,
    required: false,
    default: false
  })
  isApproved?: boolean;

  @Prop({
    type: Boolean,
    required: false,
    default: false
  })
  isAdminOrg?: boolean;

  @Prop({
    type: Boolean,
    required: false,
    default: false
  })
  isOnboarded?: boolean;

  @Prop({
    type: String,
    required: false,
    trim: true,
    default: HeadCount.NOT_SPECIFIED,
    enum: Object.values(HeadCount),
  })
  headCount?: string;

  @Prop({
    type: String,
    required: false,
    trim: true,
    default: SourceType.ADMIN_PORTAL,
    enum: Object.values(SourceType),
  })
  source?: string;

  @Prop({
    type: Types.ObjectId,
    required: false,
    ref: 'Org'
  })
  parentOrg?: Org;

  @Prop({
    type: Types.ObjectId,
    required: false,
    ref: 'Org'
  })
  companyId?: Org;


  @Prop({
    type: [Types.ObjectId],
    required: false,
    ref: 'Org'
  })
  vendors?: Types.ObjectId[];

  @Prop({
    type: [Types.ObjectId],
    required: false,
    ref: 'Org'
  })
  customers?: Types.ObjectId[];

  // @Prop({
  //   type: Types.ObjectId,
  //   required: false,
  //   ref: 'BasicUser'
  // })
  // reportingTo?: BasicUser;

  // @Prop({
  //   type: Types.ObjectId,
  //   required: false,
  //   ref: 'BasicUser'
  // })
  // assignTo?: BasicUser;

  @Prop({
    type: [Types.ObjectId],
    required: false,
    ref: 'BasicUser',
  })
  assignTo?: BasicUser[]; // or BasicUser[] if you plan to populate


  @Prop({
    type: Types.ObjectId,
    required: false,
    ref: 'Country'
  })
  country?: Country;

  @Prop({
    type: Types.ObjectId,
    required: false,
    ref: 'State'
  })
  state?: State;

  @Prop({
    type: Types.ObjectId,
    required: false,
    ref: 'City'
  })
  city?: City;

  // TODO: usecase
  @Prop({
    type: Boolean,
    required: false,
    default: false,
  })
  isDuplicate?: boolean;

  @Prop({
    type: String,
    required: false,
    trim: true,
    default: OrgStatus.PROSPECT,
    enum: Object.values(OrgStatus),
  })
  status?: string;

  @Prop({
    required: false,
    type: Types.ObjectId,
    ref: 'Status'
  })
  customStatus?: Types.ObjectId;

  @Prop({
    type: Types.ObjectId,
    required: false,
    ref: 'AccountType'
  })
  accountType?: AccountType;

  @Prop({
    type: String,
    required: true,
    trim: true,
    default: OrgType.NONE,
    enum: Object.values(OrgType)
  })
  orgType: string;

  @Prop({
    type: Types.ObjectId,
    required: false,
    ref: 'BasicUser'
  })
  createdBy?: BasicUser;

  @Prop({
    required: false,
    type: [{ type: Types.ObjectId, ref: 'SocialMediaLink' }]
  })
  socialMediaLinks?: SocialMediaLink[];

  @Prop({
    type: Boolean,
    required: false,
    default: true,
  })
  isOperational?: boolean;

  @Prop({
    type: Boolean,
    required: false,
    default: false,
  })
  isRootOrg?: boolean;

  @Prop({
    type: String,
    required: false,
    trim: true
  })
  subDomain?: string;

  @Prop({
    type: Types.ObjectId,
    required: false,
    ref: 'Org'
  })
  addedByOrg?: Org;

  @Prop({
    type: [Types.ObjectId],
    required: false,
    ref: 'BasicUser',
    default: []
  })
  favouritedByUsers?: Types.ObjectId[];

  @Prop({
    type: String,
    required: false,
    trim: true
  })
  companyProfileName?: string;

  @Prop({ default: false })
  showSocialLogins?: boolean;

  @Prop({
    type: String,
    required: false,
    trim: true
  })
  instagramUrl?: string;

  @Prop({
    type: String,
    required: false,
    trim: true
  })
  facebookUrl?: string;

  @Prop({
    type: String,
    required: false,
    trim: true
  })
  otpCode?: string;

  @Prop({ type: Boolean, required: false, default: false })
  isVerified?: boolean;

  @Prop({
    type: Date,
    required: false
  })
  verificationExpires?: Date;

  @Prop({
    type: Boolean,
    required: false,
    default: false
  })
  isUpdated?: boolean;

  //  Duplicate Job Application Check & Configurable Duration
  @Prop({
    type: Number,
    required: false,
    default: 90,  // Default value: 90 days
  })
  duplicateCheckDuration?: number;

  @Prop({
    type: Types.ObjectId,
    required: false,
    ref: 'Org'
  })
  createdByOrg?: Org;

  @Prop({
    type: [Types.ObjectId],
    default: [],
    required: false,
    ref: 'FileMetadata',
  })
  agreements?: FileMetadata[];

  // @Prop({
  //   type: Number,
  //   required: false
  // })
  // fixedRate?: number;

  // @Prop({
  //   type: Number,
  //   required: false
  // })
  // percentageOfCTC?: number;

  // @Prop({
  //   type: MongooseSchema.Types.Mixed,
  //   required: false,
  //   default: {},
  // })
  // other?: Record<string, any>;

  // @Prop({
  //   type: Boolean,
  //   required: false,
  //   default: false
  // })
  // agreeTerms?: boolean;

  @Prop({
    type: [ClientAddressSchema],
    required: false,
    default: []
  })
  clientAddress?: ClientAddress[];

  @Prop({
    type: Date,
    required: false,
    description: "The date the recurring invoice starts."
  })
  startDate?: Date;

  @Prop({
    type: Number,
    required: false,
    description: "The duration value for the recurrence."
  })
  invoiceDurationValue?: number;

  @Prop({
    type: String,
    required: false,
    enum: Object.values(DurationUnit), // Using the DurationUnit enum
  })
  invoiceDurationUnit?: string;

  @Prop({
    type: Number,
    required: false,
    description: "The duration in days after which payment is due (e.g., 30 days, 60 days)."
  })
  paymentDurationValue?: number;

  @Prop({
    type: Boolean,
    required: false,
    default: false,
    description: "Indicates if the recurring invoice never expires. If false, an `endDate` is required."
  })
  neverExpires?: boolean;

  @Prop({
    type: Date,
    required: false,
    description: "The end date of the recurring invoice (required if `neverExpires` is false)."
  })
  endDate?: Date;

  @Prop({
    type: Types.ObjectId,
    ref: 'BgvHandler',
    required: false
  })
  bgvHandlerId: string;


  // @Prop({ 
  //   type: {
  //     addressLine: String,
  //     city: { type: Types.ObjectId, ref: 'City' },      // ref to City collection
  //     state: { type: Types.ObjectId, ref: 'State' },    // ref to State collection
  //     country: { type: Types.ObjectId, ref: 'Country' },// ref to Country collection
  //     postalCode: String,
  //     contactNumber: String,
  //     email: String,
  //     lutNumber: String,
  //     lutDate: String,
  //   },
  //   required: false
  // })
  // addressDetails: {
  //   addressLine: string;
  //   city: Types.ObjectId;
  //   state: Types.ObjectId;
  //   country: Types.ObjectId;
  //   postalCode: string;
  //   contactNumber: string;
  //   email: string;
  //   lutNumber: string;
  //   lutDate: string;
  // };

  @Prop({
    type: CompanyAddressSchema,
    required: false,
    default: {}
  })
  addressDetails?: CompanyAddress;

  @Prop({ type: Object, required: false })
  bankDetails: {
    accountHolderName: string;
    ifscCode: string;
    bankName: string;
    accountNumber: string;
    branchName: string;
    accountType: string;
    gstin: string;
  };

  @Prop({
    type: Map,
    of: {
      minMarginPercentage: Number,
      maxHikePercentage: Number,
      requiresApproval: Boolean,
      approverRole: String,
    },
    default: {},
  })
  hikeSettings?: Record<EmploymentType, {
    minMarginPercentage: number;
    maxHikePercentage: number;
    requiresApproval: boolean;
    approverRole: string;
  }>;


}

export const OrgSchema = SchemaFactory.createForClass(Org);
