import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsEmail, IsNotEmpty, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>ength } from 'class-validator';
import { IsObjectId } from 'class-validator-mongo-object-id';
import { Transform, TransformFnParams } from 'class-transformer';
import { sanitizeWithStyle } from "src/utils/sanitize-with-style";


// Read about operators of swagger here - https://docs.nestjs.com/openapi/decorators
export class LoginDto {

    @ApiProperty({
        type: String,
        required: true,
        default: '<EMAIL>',
        description: 'Enter user email',
    })
    @IsNotEmpty()
    @IsEmail()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value).toLowerCase())
    email: string;


    @ApiProperty({
        type: String,
        required: true,
        description: ' Enter user password'
    })
    @IsNotEmpty()
    // @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    password: string;


}
