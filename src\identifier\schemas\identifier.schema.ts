import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Types } from 'mongoose';
import { IdentifierType } from 'src/shared/constants';
import { Region } from 'src/region/schemas/region.schema';

export type IdentifierDocument = HydratedDocument<Identifier>;

@Schema({
    timestamps: true
})
export class Identifier {

    @Prop({
        type: String,
        required: true,
        trim: true
    })
    name: string;

    @Prop({
        type: Boolean,
        required: false,
        default: true
    })
    isUpload?: boolean;

    @Prop({
        type: Boolean,
        required: false,
        default: false
    })
    isTaxIdentifier?: boolean;

    @Prop({
        type: Boolean,
        required: false,
        default: true
    })
    isEntityIdentifier?: boolean;

    @Prop({
        type: String,
        required: false,
        trim: true,
        enum: Object.values(IdentifierType),
    })
    isType?: string;

    @Prop({
        type: Types.ObjectId,
        required: true,
        ref: 'Region'
    })
    region: Region;

    @Prop({
        type: Boolean,
        required: false,
        default: true
    })
    isActive?: boolean;

    @Prop({
        type: Boolean,
        required: false,
        default: false
    })
    isSuspended?: boolean;

    @Prop({
        type: Boolean,
        required: false,
        default: false
    })
    isOptional?: boolean;

    @Prop({
        type: Boolean,
        required: false,
        default: false
    })
    isNewIdentifier?: boolean;

}

export const IdentifierSchema = SchemaFactory.createForClass(Identifier);