import { Prop, Schema } from '@nestjs/mongoose';
import validator from 'validator';
import { HydratedDocument } from 'mongoose';
import { SchemaFactory } from '@nestjs/mongoose';


export type ForgotPasswordDocument = HydratedDocument<ForgotPassword>;

@Schema({
    timestamps: true
})
export class ForgotPassword {

    @Prop({
        required: [true, 'EMAIL_CANNOT_BE_BLANK'],
        lowercase: true,
    })
    email: string;

    @Prop({
        required: false,
    })
    firstName?: string;

    @Prop({
        required: false
    })
    verification?: string;

    @Prop({
        default: false,
    })
    firstUsed?: boolean;

    @Prop({
        default: false,
    })
    finalUsed?: boolean;

    @Prop({
        type: String,
        required: false,
        trim: true
    })
    otpCode?: string;

    @Prop({
        type: Date,
        required: true,
    })
    expires: Date;

    @Prop({
        required: false,
    })
    ip?: string;

    @Prop({
        required: false,
    })
    browser?: string;

    @Prop({
        required: false,
    })
    country?: string;

    @Prop({
        required: false,
    })
    ipChanged?: string;

    @Prop({
        required: false,
    })
    browserChanged?: string;

    @Prop({
        required: false,
    })
    countryChanged?: string;
}

export const ForgotPasswordSchema = SchemaFactory.createForClass(ForgotPassword);

// export const ForgotPassworddSchema = new Schema ({
//     email: {
//         required: [true, 'EMAIL_IS_BLANK'],
//         type: String,
//     },
//     firstName: {
//         type: String
//     },
//     verification: {
//         type: String,
//         validate: validator.isUUID,
//         required: true,
//     },
//     firstUsed: {
//         type: Boolean,
//         default: false,
//     },
//     finalUsed: {
//         type: Boolean,
//         default: false,
//     },
//     expires: {
//         type: Date,
//         required: true,
//     },
//     ip: {
//         type: String,
//         required: true,
//     },
//     browser: {
//         type: String,
//         required: true,
//     },
//     country: {
//         type: String,
//         required: true,
//     },
//     ipChanged: {
//         type: String,
//     },
//     browserChanged: {
//         type: String,
//     },
//     countryChanged: {
//         type: String,
//     },
// },
// {
//     versionKey: false,
//     timestamps: true,
// });
