import { ApiHideProperty, ApiProperty } from "@nestjs/swagger";
import { Transform, TransformFnParams, Type } from "class-transformer";
import { IsDate, IsEnum, IsISO8601, IsMongoId, IsNotEmpty, IsOptional, IsString, Length, Matches } from "class-validator";
import { sanitizeWithStyle } from "src/utils/sanitize-with-style";
import { Priority, RecurrenceInterval, Status } from "src/shared/constants";

export class CreateTaskDto {

    @ApiProperty({
        type: String,
        required: true,
        description: 'Task title',
    })
    @IsString()
    @IsNotEmpty()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    title: string;

    @ApiProperty({
        type: String,
        required: false,
        description: 'Task summary',
    })
    @IsString()
    @IsOptional()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    summary?: string;

    @ApiProperty({
        type: Date,
        required: true,
        description: 'Task due date',
        // default: new Date().toLocaleDateString('en-GB').split('/').reverse().join('-')
        // default : new Date().toISOString() -it is in ISO format but not in UTC time zone
        // new Date(new Date().toISOString().split('T')[0] + 'T00:00:00.000Z'); - to convert ISO to UTC time zone
        default: new Date(Date.UTC(new Date().getUTCFullYear(), new Date().getUTCMonth(), new Date().getUTCDate(), new Date().getUTCHours(), new Date().getUTCMinutes())),
    })
    // YYYY-MM-DDThh:mm:ssZ - example : 2024-05-27T00:00:00.000Z
    @IsISO8601({ strict: true })
    @Length(10, 24)
    dueDate: Date;

    // @ApiProperty({
    //     type: Date,
    //     required: false,
    //     description: 'Task due time',
    //     default:'12:00'
    // })
    // @Matches(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, {
    //     message: 'time must be in the format HH:mm',
    // })
    // @IsOptional()
    // dueTime?: Date;


    @ApiHideProperty()
    @IsOptional()
    code?: string;

    @ApiProperty({
        type: String,
        required: false,
        description: 'Parent task id',
    })
    @IsString()
    @IsOptional()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    parentTask: string;

    @ApiProperty({
        type: String,
        required: false,
        description: 'Task location',
    })
    @IsString()
    @IsOptional()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    location?: string;

    // @ApiProperty({
    //     type: String,
    //     required: false,
    //     description: 'Account associated with the task',
    // })
    // @IsString()
    // @IsOptional()
    // @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    // account?: string

    @ApiProperty({
        type: String,
        required: false,
        description: 'Organization associated with the task',
    })
    @IsString()
    @IsOptional()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    org?: string

    @ApiProperty({
        type: String,
        required: false,
        description: 'Single point of contact for task',
    })
    @IsString()
    @IsOptional()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    spoc?: string;

    @ApiHideProperty()
    @IsOptional()
    createdBy?: string;

    @ApiHideProperty()
    @IsOptional()
    assignee?: string;

    @ApiProperty({
        type: [String],
        required: false,
        description: 'Task assigned to',
    })
    @IsOptional()
    @IsMongoId({ each: true })
    @Transform((params: TransformFnParams) => params.value.map((value: string) => sanitizeWithStyle(value)))
    assignees: string[];

    @ApiProperty({
        type: String,
        required: false,
        default: Priority.LOW,
        enum: Priority,
        description: 'Task priority',
    })
    @IsEnum(Priority)
    @IsString()
    @IsOptional()
    priority?: Priority;

    @ApiProperty({
        type: String,
        required: false,
        default: Status.TO_DO,
        enum: Status,
        description: 'Task status',
    })
    @IsEnum(Status)
    @IsString()
    @IsOptional()
    status?: Status;

    @ApiProperty({
        type: String,
        required: false,
        enum: RecurrenceInterval,
        description: 'Recurrence interval for the task',
    })
    @IsEnum(RecurrenceInterval)
    @IsOptional()
    recurrenceInterval?: string;

    @ApiProperty({
        type: String,
        required: false,
        description: 'Vendor associated with the task',
    })
    @IsString()
    @IsOptional()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    vendor?: string
}
