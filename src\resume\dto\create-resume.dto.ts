import {
    IsString, IsO<PERSON>al, IsNumber, IsArray, ValidateNested,
    IsObject, IsNotEmpty, IsBoolean, IsISO8601,
    IsEnum,
    Length
} from 'class-validator';
import { Transform, TransformFnParams, Type } from 'class-transformer';
import { ApiHideProperty, ApiProperty } from '@nestjs/swagger';
import { sanitizeWithStyle } from "src/utils/sanitize-with-style";
import { CreateWorkExperienceDto } from 'src/work-experience/dto/create-work-experience.dto';
import { CreateEducationQualificationDto } from 'src/education-qualification/dto/create-education-qualification.dto';
import { Currency } from 'src/shared/constants';
import { Types } from 'mongoose';

export class ContactDetailsDto {
    @ApiProperty({ type: String, required: false })
    @IsString()
    @IsOptional()
    @Transform(({ value }) => (value ? sanitizeWithStyle(value) : value))
    contactEmail?: string;

    @ApiProperty({ type: String, required: false })
    @IsOptional()
    @IsString()
    contactNumber?: string;
}

export class ContactAddressDto {
    @ApiProperty({ type: String, required: false })
    @IsString()
    @IsOptional()
    street?: string;

    @ApiProperty({ type: String, required: false })
    @IsString()
    @IsOptional()
    postalCode?: string;
}

export class CreateWorkExperiences {
    @ApiProperty({
        type: String,
        required: true,
    })
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    @IsNotEmpty()
    @IsString()
    jobTitle: string;

    @ApiProperty({
        type: String,
        required: true,
    })
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    @IsNotEmpty()
    @IsString()
    companyName: string;

    @ApiProperty({
        type: Date,
        required: false,
        default: new Date(Date.UTC(new Date().getUTCFullYear(), new Date().getUTCMonth())),
    })
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    @IsOptional()
    @IsISO8601({ strict: true })
    @Length(10, 24)
    jobStartDate?: Date;

    @ApiProperty({
        type: Date,
        required: false,
        default: new Date(Date.UTC(new Date().getUTCFullYear(), new Date().getUTCMonth())),
    })
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    @IsOptional()
    @IsISO8601({ strict: true })
    @Length(10, 24)
    jobEndDate: Date;

    @ApiProperty({
        type: Boolean,
        required: false,
    })
    @IsOptional()
    @IsBoolean()
    currentlyWorking?: boolean;

}

export class CreateEducationQualifications {
    @ApiProperty({
        type: String,
        required: true,
        description: 'The Highest education degree',
    })
    @IsNotEmpty()
    @IsString()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    courseName: string;

    @ApiProperty({
        type: String,
        required: true,
        description: 'In which university'
    })
    @IsString()
    @IsNotEmpty()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    university: string;

    @ApiProperty({
        type: Date,
        required: false,
        description: 'Start date of the university'
    })
    @IsString()
    @IsOptional()
    startDate?: Date;

    @ApiProperty({
        type: Date,
        required: false,
        description: 'End date of the university'
    })
    @IsString()
    @IsOptional()
    endDate?: Date;

}
export class EvaluationForm {
    @ApiProperty({
        type: String,
        required: false,
    })
    @IsNotEmpty()
    @IsString()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    skill: string;

    @ApiProperty({
        type: Number,
        required: false,
    })
    @IsString()
    @IsNotEmpty()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    years: Number;

    @ApiProperty({
        type: Number,
        required: false,
    })
    @IsString()
    @IsOptional()
    months?: Number;

    @ApiProperty({
        type: Date,
        required: false,
    })
    @IsString()
    @IsOptional()
    rating?: Number;

    @ApiProperty({
        type: Boolean,
        required: false,
    })
    @IsNotEmpty()
    @IsString()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    isPrimary: Boolean;

}

export class CreateResumeDto {
    @ApiProperty({ type: String })
    @IsString()
    @IsNotEmpty()
    firstName: string;

    @ApiProperty({ type: String, required: false })
    @IsOptional()
    @IsString()
    lastName?: string;

    @ApiProperty({ type: String, required: false })
    @IsOptional()
    @IsString()
    panNumber?: string;

    @ApiProperty({ type: String, required: false })
    @IsOptional()
    @IsString()
    resumeMetadata?: string;

    @ApiProperty({ type: String, required: false })
    @IsOptional()
    @IsString()
    coverLetterMetadata?: string;

    @ApiProperty({ type: ContactDetailsDto, required: false })
    @IsOptional()
    @ValidateNested()
    @Type(() => ContactDetailsDto)
    contactDetails?: ContactDetailsDto;

    @ApiProperty({ type: ContactAddressDto, required: false })
    @IsOptional()
    @ValidateNested()
    @Type(() => ContactAddressDto)
    contactAddress?: ContactAddressDto;

    @ApiProperty({ type: String, required: false })
    @IsOptional()
    @IsString()
    state?: string;

    @ApiProperty({ type: String, required: false })
    @IsOptional()
    @IsString()
    city?: string;

    @ApiProperty({ type: String, required: false })
    @IsOptional()
    @IsString()
    country?: string;

    @ApiProperty({
        type: Date,
        required: true,
        description: 'Date of Birth'
    })
    @IsString()
    @IsOptional()
    dob?: Date;

    @ApiProperty({ type: String, required: true })
    @IsOptional()
    @IsString()
    gender?: string;

    @ApiProperty({ type: Boolean, required: false })
    @IsOptional()
    @IsBoolean()
    disability?: boolean;

    @ApiProperty({ type: String, required: false })
    @IsOptional()
    @IsString()
    nationality?: string;

    @ApiProperty({ type: String, required: false })
    @IsOptional()
    @IsString()
    linkedInUrl?: string;

    @ApiProperty({ type: String, required: false })
    @IsOptional()
    @IsString()
    websiteOrBlogUrl?: string;

    @ApiProperty({ type: Boolean, required: false })
    @IsOptional()
    @IsBoolean()
    isExperienced?: boolean;

    @ApiProperty({ type: Number, required: false })
    @IsOptional()
    @IsNumber()
    yearsOfExperience?: number;

    @ApiProperty({ type: [EvaluationForm], required: false })
    @IsOptional()
    @IsArray()
    evaluationForm?: EvaluationForm[];

    @ApiProperty({ type: Number, required: false })
    @IsOptional()
    @IsNumber()
    noticePeriodDays?: number;

    @ApiProperty({ type: Boolean, required: false })
    @IsOptional()
    @IsBoolean()
    servingNoticePeriod?: boolean;

    @ApiProperty({
        type: Date,
        required: false,
    })
    @IsString()
    @IsOptional()
    lastWorkingDate?: Date;

    @ApiProperty({ type: String, required: false })
    @IsOptional()
    @IsString()
    currentLocation?: string;

    @ApiProperty({ type: Boolean, required: false })
    @IsOptional()
    @IsBoolean()
    willingToRelocate?: boolean;

    @ApiProperty({ type: [String], required: false })
    @IsOptional()
    @IsArray()
    reLocation?: string[];

    @ApiProperty({ type: String, required: false })
    @IsOptional()
    @IsString()
    preferredLocation?: string;

    @ApiProperty({ type: Number, required: false })
    @IsOptional()
    @IsNumber()
    currentCTC?: number;

    @ApiProperty({ type: Number, required: false })
    @IsOptional()
    @IsNumber()
    expectedCTC?: number;

    @ApiProperty({ type: Number, required: false })
    @IsOptional()
    @IsNumber()
    ctcPercentage?: number;

    @ApiProperty({ type: String, required: false, enum: Currency, default: Currency.INR })
    @IsOptional()
    @IsEnum(Currency)
    currency?: Currency;

    @ApiProperty({ type: Boolean, required: false })
    @IsOptional()
    @IsBoolean()
    companyNorms?: boolean;

    @ApiProperty({ type: String, required: false })
    @IsOptional()
    @IsString()
    org?: string;

    @ApiProperty({ type: Boolean, required: false })
    @IsOptional()
    @IsBoolean()
    isDraft?: boolean;

    @ApiProperty({ type: Object, required: false })
    @IsOptional()
    @IsObject()
    dynamicFields?: object;

    @ApiProperty({ type: String, required: false })
    @IsOptional()
    @IsString()
    resumeName?: string;

    @ApiProperty({ type: [CreateWorkExperiences], required: false })
    @IsOptional()
    @IsArray()
    workExperience?: CreateWorkExperiences[];

    @ApiProperty({ type: [CreateEducationQualifications], required: false })
    @IsOptional()
    @IsArray()
    educationQualification?: CreateEducationQualifications[];

    @ApiHideProperty() // Hide from Swagger documentation
    createdBy: Types.ObjectId;
}




