import { ApiHideProperty, ApiProperty } from "@nestjs/swagger";
import { Transform, TransformFnParams } from "class-transformer";
import { IsArray, IsBoolean, IsEnum, IsMongoId, IsNotEmpty, IsNumber, IsOptional, IsString, Matches, Min } from "class-validator";
import { sanitizeWithStyle } from "src/utils/sanitize-with-style";
export class CreateRecruiterTargetDto {

    @ApiProperty({ type: String, required: false, description: 'Reference to the Organization' })
    @IsMongoId()
    @IsOptional()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    org?: string;

    @ApiProperty({ type: String, required: false, description: 'Reference to the Recruiter' })
    @IsMongoId()
    @IsOptional()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    recruiterId?: string;
    
    @ApiProperty({ type: Number, required: true, description: "Target number (must be a positive integer)" })
    @IsNumber()
    @Min(1, { message: "targetNumber must be at least 1" }) // Ensures only positive numbers
    targetNumber: number;

    @ApiProperty({ type: String, required: true, description: "Target month in YYYY-MM format" })
    @IsString()
    @Matches(/^\d{4}-(0[1-9]|1[0-2])$/, { message: "targetMonth must be in YYYY-MM format" })
    targetMonth: string

    @ApiHideProperty()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    createdBy?: string;


}


