// src/invoice/invoice.service.ts
import { HttpException, HttpStatus, Injectable, InternalServerErrorException, Logger, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Invoice, InvoiceDocument } from './schemas/invoice.schema';
import { CreateInvoiceDto } from './dto/create-invoice.dto';
import { UpdateInvoiceDto } from './dto/update-invoice.dto';
import { Org, OrgDocument, Placeholder } from 'src/org/schemas/org.schema';
import { Offer } from 'src/offer/schemas/offer.schema';
import { BasicUser } from 'src/user/schemas/basic-user.schema';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Job } from 'src/job/schemas/job.schema';
import { JobApplication } from 'src/job-application-form/schemas/job-application.schema';
import { RateCard } from 'src/rate-card/schemas/rate-card.schema';
import { Role } from 'src/auth/enums/role.enum';
import { EmailTemplateEvent, EmploymentType, InvoiceStatus, JobType, OnboardingStatus, OrgType } from 'src/shared/constants';
import { OrgService } from 'src/org/org.service';
import { JSONPath } from 'jsonpath-plus';
import { EmailTemplate } from 'src/email-template-builder/schemas/email-template-builder.schema';
import { Bgv } from 'src/offer/schemas/bgv.schema';
import { ApproveRejectInterimBgvDto } from 'src/offer/dto/approve-reject-interim-bgv.dto';
import { GetInvoicesQueryDto } from './dto/get-invoices-query.dto';
import { HikeApproval } from 'src/offer/schemas/hike-approval.schema';
import { unescape } from 'lodash';
import { Employee } from 'src/employee/schemas/employee.schema';

@Injectable()
export class InvoiceService {
  private readonly logger = new Logger(InvoiceService.name);

  constructor(
    @InjectModel(EmailTemplate.name) private emailTemplateService: Model<EmailTemplate>,
    @InjectModel(Invoice.name) private invoiceModel: Model<InvoiceDocument>,
    @InjectModel(Org.name) private orgModel: Model<OrgDocument>,
    @InjectModel(Offer.name) private offerModel: Model<Offer>, private eventEmitter: EventEmitter2,
    @InjectModel(BasicUser.name) private basicUserModel: Model<BasicUser>,
    @InjectModel(Employee.name) private employeeModel: Model<Employee>,
    @InjectModel(Job.name) private jobModel: Model<Job>,
    @InjectModel(JobApplication.name) private jobApplicationModel: Model<JobApplication>,
    @InjectModel(RateCard.name) private rateCardModel: Model<RateCard>,
    @InjectModel(Placeholder.name) private placeholderService: Model<Placeholder>,
    @InjectModel(Bgv.name) private bgvModel: Model<Bgv>,
    @InjectModel(HikeApproval.name) private hikeApprovalModel: Model<HikeApproval>,
    private readonly orgService: OrgService

  ) { }

  async findAll(): Promise<Invoice[]> {
    return this.invoiceModel
      .find()
      .populate(this.populateOptions())
      .exec();
  }


  async findOne(id: string): Promise<Invoice> {
    const invoice = await this.invoiceModel
      .findById(id)
      .populate(this.populateOptions())
      .exec();

    if (!invoice) throw new NotFoundException('Invoice not found');
    return invoice;
  }

  async create(user: any, createInvoiceDto: CreateInvoiceDto): Promise<Invoice> {
    const invoice = new this.invoiceModel({
      ...createInvoiceDto,
      generatedBy: user._id.toString(),
      isDeleted: false,
      isTemp: true,
    });

    return invoice.save();
  }

  async update(id: string, updateInvoiceDto: UpdateInvoiceDto): Promise<Invoice> {
    const invoice = await this.invoiceModel.findByIdAndUpdate(id, updateInvoiceDto, { new: true }).exec();
    if (!invoice) throw new NotFoundException('Invoice not found');
    return invoice;
  }

  async delete(id: string): Promise<Invoice> {
    const invoice = await this.invoiceModel.findByIdAndDelete(id).exec();
    if (!invoice) throw new NotFoundException('Invoice not found');
    return invoice;
  }

  async findInvoceEligibilityApplicationId(user: any, applicationId: Types.ObjectId) {
    try {

      const jobApp = await this.jobApplicationModel.findById(applicationId)
        .populate({ path: 'createdBy', select: '_id firstName roles org' })
        .exec();
      console.log(jobApp)
      const job = await this.jobModel.findById(jobApp?.jobId).exec();
      console.log(job)

      let isEligibleForInvoice = false;
      let isEligibleForOnboading = false;
      if (job?.jobType === JobType.Internal) {
        if (user?.roles.includes(Role.Vendor)) {
          if (job?.employmentType === EmploymentType.FullTime) {
            if (jobApp?.createdBy.roles?.includes(Role.Vendor)) {
              isEligibleForInvoice = true;
              isEligibleForOnboading = false;
            }
          }
          if (job?.employmentType === EmploymentType.Contract) {
            if (jobApp?.createdBy.roles?.includes(Role.Vendor)) {
              isEligibleForInvoice = true;
              isEligibleForOnboading = true;
            }
          }
        }
        // else if (user.org._id.toString() === jobApp?.createdBy.org?.toString()) {
        else {
          if (job?.employmentType === EmploymentType.FullTime) {
            if (jobApp?.createdBy.roles?.includes(Role.Vendor)) {
              isEligibleForInvoice = false;
              isEligibleForOnboading = true;
            }
            else if (jobApp?.createdBy.org === job.endClientOrg) {
              isEligibleForInvoice = false;
              isEligibleForOnboading = true;
            }
          }
          if (job?.employmentType === EmploymentType.Contract) {
            if (jobApp?.createdBy.roles?.includes(Role.Vendor)) {
              isEligibleForInvoice = false;
              isEligibleForOnboading = false;
            }
            else if (jobApp?.createdBy.org === job.endClientOrg) {
              isEligibleForInvoice = false;
              isEligibleForOnboading = true;
            }
          }
        }


      }

      if (job?.jobType === JobType.External) {
        if (user?.roles.includes(Role.Vendor)) {
          if (job?.employmentType === EmploymentType.FullTime) {
            if (jobApp?.createdBy.roles?.includes(Role.Vendor)) {
              isEligibleForInvoice = true;
              isEligibleForOnboading = false;
            }
          }
          if (job?.employmentType === EmploymentType.Contract) {
            if (jobApp?.createdBy.roles?.includes(Role.Vendor)) {
              isEligibleForInvoice = true;
              isEligibleForOnboading = true;
            }
          }
        }
        // else if (user.org._id.toString() === jobApp?.createdBy.org?.toString()) {
        else {
          if (job?.employmentType === EmploymentType.FullTime) {
            if (jobApp?.createdBy.roles?.includes(Role.Vendor)) {
              isEligibleForInvoice = true;
              isEligibleForOnboading = false;
            }
            else if (jobApp?.createdBy.org !== job.endClientOrg) {
              isEligibleForInvoice = true;
              isEligibleForOnboading = false;
            }
          }
          if (job?.employmentType === EmploymentType.Contract) {
            if (jobApp?.createdBy.roles?.includes(Role.Vendor)) {
              isEligibleForInvoice = true;
              isEligibleForOnboading = false;
            }
            else if (jobApp?.createdBy.org !== job.endClientOrg) {
              isEligibleForInvoice = true;
              isEligibleForOnboading = true;
            }
          }
        }


      }

      if (!jobApp) {
        throw new NotFoundException(`The application with id: "${applicationId}" doesn't exist.`);
      }
      return { isEligibleForInvoice, isEligibleForOnboading };
      // return responseWithFlags;
    }
    catch (error) {
      // this.logger.error(`An error occurred in fetching offer by Id ${applicationId}. ${error?.message}`);
      throw error;

    }
  }

  async findRateCardByApplicationId(user: any, applicationId: Types.ObjectId) {
    try {

      const jobApp = await this.jobApplicationModel.findById(applicationId).exec();
      const offer = await this.offerModel.findOne({ jobApplication: applicationId.toString() })
        .sort({ createdAt: -1 }) // 👉 Ensures the latest offer is returned
        .exec();
      console.log(jobApp)
      const job = await this.jobModel.findById(jobApp?.jobId).exec();
      console.log(job)
      let rateCard: any;
      if (job?.rateCard) {
        console.log("job.rateCard", job?.rateCard)
        const rateCardId = job?.rateCard;
        const card = await this.rateCardModel
          .findById(rateCardId)
          .populate({ path: 'category', select: '_id label description client' })
          .exec();

        if (!card || card.isDeleted) {
          throw new NotFoundException('Rate card not found.');
        }
        console.log('card', card)
        rateCard = card;
        // return card;
      }

      if (!jobApp) {
        throw new NotFoundException(`The application with id: "${applicationId}" doesn't exist.`);
      }
      return { rateCard, offeredCtc: offer?.salaryPerAnnum };
      // return responseWithFlags;
    }
    catch (error) {
      // this.logger.error(`An error occurred in fetching offer by Id ${applicationId}. ${error?.message}`);
      throw error;

    }
  }

  async findAddressByApplicationId(user: any, applicationId: Types.ObjectId) {
    try {

      const jobApp = await this.jobApplicationModel.findById(applicationId)
        .populate({
          path: 'createdBy',
          model: 'BasicUser',
          select: '_id firstName roles lastName email'
        }).exec();
      const offer = await this.offerModel.findOne({ jobApplication: applicationId.toString() })
        .sort({ createdAt: -1 }) // 👉 Ensures the latest offer is returned
        .exec();

      const existingOfferApproval = await this.hikeApprovalModel.findOne({
        offerId: offer?._id.toString(),
        status: "PENDING"
      }).exec();

      if (existingOfferApproval) {
        throw new HttpException(
          'An offer has already been released and is awaiting approval. Please wait until the current offer is approved before genrating the Invoice',
          HttpStatus.BAD_REQUEST,
        );
      }

      console.log("job details in the application details",jobApp)
      const job = await this.jobModel.findById(jobApp?.jobId).exec();
      console.log("based on Id", job)
      let rateCard: any;
      let billToAddress: any;
      let billToOrg: any;
      //here we need to handle multiple vendors with same email
      let org = user?.org?._id;
      if (user?.roles.includes(Role.Vendor)) {
        const existingOrgs = await this.orgModel.find({ 'contactDetails.contactEmail': user.email, isDeleted: false, orgType: OrgType.VENDOR_ORG }).exec();
        const matchedVendorOrg = existingOrgs.find(vendorOrg =>
          vendorOrg?.companyId?.toString() === job?.postingOrg?.toString()
        );
        if (matchedVendorOrg) {
          org = matchedVendorOrg._id;
        }
      }

      console.log("org usinf in the billing org::", org)
      const billingorg = await this.orgService.findOne(org);

    
      // const billingAddress = billingorg.addressDetails;
      const billingAddress = {
        ...((billingorg?.addressDetails as any)?.toObject?.()),
        title: billingorg.title,
      };
      const billingBankDetails = billingorg?.bankDetails;

      if (job?.jobType === JobType.Internal) {
        if (user?.roles.includes(Role.Vendor)) {
          if (job?.employmentType === EmploymentType.FullTime) {
            if (jobApp?.createdBy.roles?.includes(Role.Vendor)) {
              // isEligibleForInvoice = true;
              const existingOrgs = await this.orgModel.find({ 'contactDetails.contactEmail': user.email, isDeleted: false, orgType: OrgType.VENDOR_ORG }).exec();

              // Step 2: Extract org _ids
              const vendorOrgIds = existingOrgs.map(org => org._id.toString());
              console.log("vendorOrgIds", vendorOrgIds);

              const matchedRateCardEntry = job.vendorRateCards?.find((entry) =>
                vendorOrgIds.includes(entry.vendor?.toString())
              );

              const rateCardId = matchedRateCardEntry?.rateCard.toString() || null;
              const card = await this.rateCardModel
                .findById(rateCardId)
                .populate({ path: 'category', select: '_id label description client' })
                .exec();

              if (!card || card.isDeleted) {
                throw new NotFoundException('Rate card not found.');
              }
              console.log('card', card)
              rateCard = card;

              const billToOrgId = rateCard?.org;
              const billToOrgDetails = await this.orgService.findOne(billToOrgId);
              // billToAddress = billToOrgDetails.addressDetails;
              billToAddress = {
                ...billToOrgDetails?.addressDetails,
                title: billToOrgDetails.title,
              };
              billToOrg = billToOrgDetails;
            }
          }
        }

      }

        if (job?.jobType === JobType.Internal) {
        if (user?.roles.includes(Role.Vendor)) {
          if (job?.employmentType === EmploymentType.Contract) {
            if (jobApp?.createdBy.roles?.includes(Role.Vendor)) {
              // isEligibleForInvoice = true;
              const existingOrgs = await this.orgModel.find({ 'contactDetails.contactEmail': user.email, isDeleted: false, orgType: OrgType.VENDOR_ORG }).exec();

              // Step 2: Extract org _ids
              const vendorOrgIds = existingOrgs.map(org => org._id.toString());
              console.log("vendorOrgIds", vendorOrgIds);

              const matchedRateCardEntry = job.vendorRateCards?.find((entry) =>
                vendorOrgIds.includes(entry.vendor?.toString())
              );

              const rateCardId = matchedRateCardEntry?.rateCard.toString() || null;
              const card = await this.rateCardModel
                .findById(rateCardId)
                .populate({ path: 'category', select: '_id label description client' })
                .exec();

              if (!card || card.isDeleted) {
                throw new NotFoundException('Rate card not found.');
              }
              console.log('card', card)
              rateCard = card;

              const billToOrgId = rateCard?.org;
              const billToOrgDetails = await this.orgService.findOne(billToOrgId);
              // billToAddress = billToOrgDetails.addressDetails;
              billToAddress = {
                ...billToOrgDetails?.addressDetails,
                title: billToOrgDetails.title,
              };
              billToOrg = billToOrgDetails;
            }
          }
        }

      }

        if (job?.jobType === JobType.External) {
        if (user?.roles.includes(Role.Vendor)) {
          if (job?.employmentType === EmploymentType.Contract) {

            if (jobApp?.createdBy.roles?.includes(Role.Vendor)) {

              // isEligibleForInvoice = true;
              const existingOrgs = await this.orgModel.find({ 'contactDetails.contactEmail': user.email, isDeleted: false, orgType: OrgType.VENDOR_ORG }).exec();

              // Step 2: Extract org _ids
              const vendorOrgIds = existingOrgs.map(org => org._id.toString());
              console.log("vendorOrgIds", vendorOrgIds);

              const matchedRateCardEntry = job.vendorRateCards?.find((entry) =>
                vendorOrgIds.includes(entry.vendor?.toString())
              );

              const rateCardId = matchedRateCardEntry?.rateCard.toString() || null;
              const card = await this.rateCardModel
                .findById(rateCardId)
                .populate({ path: 'category', select: '_id label description client' })
                .exec();

              if (!card || card.isDeleted) {
                throw new NotFoundException('Rate card not found');
              }
              console.log('card', card)
              rateCard = card;

              const billToOrgId = rateCard?.org;
              const billToOrgDetails = await this.orgService.findOne(billToOrgId);
              // billToAddress = billToOrgDetails.addressDetails;
              billToAddress = {
                ...billToOrgDetails?.addressDetails,
                title: billToOrgDetails.title,
              };
              billToOrg = billToOrgDetails;

            }
          }
        }
        else {
          if (job?.employmentType === EmploymentType.Contract) {
            // isEligibleForInvoice = true;
            if (job?.rateCard) {
              console.log("job.rateCard", job?.rateCard)
              const rateCardId = job?.rateCard;
              const card = await this.rateCardModel
                .findById(rateCardId)
                .populate({ path: 'category', select: '_id label description client' })
                .exec();

              if (!card || card.isDeleted) {
                throw new NotFoundException('Rate card not found.');
              }
              console.log('card', card)
              rateCard = card;
              // return card;
            }

            const billToOrgId = rateCard?.category?.client;
            const billToOrgDetails = await this.orgService.findOne(billToOrgId);
            // billToAddress = billToOrgDetails.clientAddress;
            billToAddress = {
              ...billToOrgDetails?.addressDetails,
              title: billToOrgDetails.title,
            };
            billToOrg = billToOrgDetails;

          }
        }


      }

      if (job?.jobType === JobType.External) {
        if (user?.roles.includes(Role.Vendor)) {
          if (job?.employmentType === EmploymentType.FullTime) {

            if (jobApp?.createdBy.roles?.includes(Role.Vendor)) {

              // isEligibleForInvoice = true;
              const existingOrgs = await this.orgModel.find({ 'contactDetails.contactEmail': user.email, isDeleted: false, orgType: OrgType.VENDOR_ORG }).exec();

              // Step 2: Extract org _ids
              const vendorOrgIds = existingOrgs.map(org => org._id.toString());
              console.log("vendorOrgIds", vendorOrgIds);

              const matchedRateCardEntry = job.vendorRateCards?.find((entry) =>
                vendorOrgIds.includes(entry.vendor?.toString())
              );

              const rateCardId = matchedRateCardEntry?.rateCard.toString() || null;
              const card = await this.rateCardModel
                .findById(rateCardId)
                .populate({ path: 'category', select: '_id label description client' })
                .exec();

              if (!card || card.isDeleted) {
                throw new NotFoundException('Rate card not found');
              }
              console.log('card', card)
              rateCard = card;

              const billToOrgId = rateCard?.org;
              const billToOrgDetails = await this.orgService.findOne(billToOrgId);
              // billToAddress = billToOrgDetails.addressDetails;
              billToAddress = {
                ...billToOrgDetails?.addressDetails,
                title: billToOrgDetails.title,
              };
              billToOrg = billToOrgDetails;

            }
          }
        }
        else {
          if (job?.employmentType === EmploymentType.FullTime) {
            // isEligibleForInvoice = true;
            if (job?.rateCard) {
              console.log("job.rateCard", job?.rateCard)
              const rateCardId = job?.rateCard;
              const card = await this.rateCardModel
                .findById(rateCardId)
                .populate({ path: 'category', select: '_id label description client' })
                .exec();

              if (!card || card.isDeleted) {
                throw new NotFoundException('Rate card not found.');
              }
              console.log('card', card)
              rateCard = card;
              // return card;
            }

            const billToOrgId = rateCard?.category?.client;
            const billToOrgDetails = await this.orgService.findOne(billToOrgId);
            // billToAddress = billToOrgDetails.clientAddress;
            billToAddress = {
              ...billToOrgDetails?.addressDetails,
              title: billToOrgDetails.title,
            };
            billToOrg = billToOrgDetails;

          }
        }


      }
      console.log("billingorg", billingorg.addressDetails)
      console.log("billToOrg", billToOrg?.addressDetails)
      if (!billingorg?.addressDetails?.addressLine && !billingorg?.addressDetails?.city && !billingorg?.addressDetails?.state && !billingorg?.addressDetails?.country) {
        throw new HttpException(
          `${billingorg?.title || 'Billing Organization'} does not have address details configured. Please complete the address setup before generating the invoice.`,
          HttpStatus.BAD_REQUEST,
        );
      }

      if (!billingorg?.bankDetails) {
        throw new HttpException(
          `${billingorg?.title || 'Billing Organization'} does not have bank details configured. Please complete the bank setup before generating the invoice.`,
          HttpStatus.BAD_REQUEST,
        );
      }

      if (!billToOrg?.addressDetails.addressLine && !billToOrg?.addressDetails?.city && !billToOrg?.addressDetails?.state && !billToOrg?.addressDetails?.country) {
        throw new HttpException(
          `${billToOrg?.title || 'Client Organization'} does not have address details configured. Please complete the address setup before generating the invoice.`,
          HttpStatus.BAD_REQUEST,
        );
      }

      const invoiceNumber = await this.generateTempInvoiceNumber(billingorg?._id?.toString(), billToOrg?._id?.toString());
      let invoiceAmount = 0;
      if (!rateCard) {
        throw new NotFoundException(`No rate card exists to generate invoice`);
      } else {
        if (rateCard.percentageOfCTC == null && rateCard.fixedRate != null) {
          invoiceAmount = rateCard.fixedRate;
        } else if (rateCard.percentageOfCTC != null && rateCard.fixedRate == null) {
          // invoiceAmount = (offer?.salaryPerAnnum ?? 0) * rateCard.percentageOfCTC;
          invoiceAmount = (offer?.salaryPerAnnum ?? 0) * ((rateCard?.percentageOfCTC ?? 0) / 100);

        }
      }
      if (!jobApp) {
        throw new NotFoundException(`The application with id: "${applicationId}" doesn't exist.`);
      }
      return {
        invoiceNumber, invoiceAmount, rateCard, billToAddress, billingAddress, billingBankDetails,
        offeredCtc: offer?.salaryPerAnnum,
        billingOrgId: billingorg?._id?.toString(),
        billToOrgId: billToOrg?._id?.toString(), jobApplication: jobApp
      };
      // return responseWithFlags;
    }
    catch (error) {
      // this.logger.error(`An error occurred in fetching offer by Id ${applicationId}. ${error?.message}`);
      throw error;

    }
  }

  async generateInvoiceNumber(billedByOrgId: string, billedToOrgId: string): Promise<string> {
    const getFY = () => {
      const today = new Date();
      const year = today.getFullYear();
      const month = today.getMonth() + 1;
      const startYear = month < 4 ? year - 1 : year;
      const endYear = startYear + 1;
      return `${startYear}-${endYear}`;
    };

    const currentFY = getFY();
    const billedByOrg = await this.orgModel.findById(billedByOrgId).exec();
    const billedToOrg = await this.orgModel.findById(billedToOrgId).exec();
    const billedByOrgTitle = billedByOrg?.title || 'CL';
    const billedToOrgTitle = billedToOrg?.title || 'PL';
    const prefix =
      billedByOrgTitle?.slice(0, 2).toUpperCase() +
      billedToOrgTitle?.slice(0, 2).toUpperCase();

    // Match invoices in this FY
    const fyStart = new Date(`${currentFY.split('-')[0]}-04-01T00:00:00.000Z`);
    const fyEnd = new Date(`${currentFY.split('-')[1]}-03-31T23:59:59.999Z`);

    const latestInvoice = await this.invoiceModel
      .findOne({
        billedByOrg: billedByOrgId,
        billedToOrg: billedToOrgId,
        createdAt: { $gte: fyStart, $lte: fyEnd },
        isTemp: false,
      })
      .sort({ createdAt: -1 })
      .lean();

    let latestNumber = 1;
    if (latestInvoice) {
      const parts = latestInvoice.invoiceNumber.split('/');
      if (parts.length >= 2) {
        const parsed = parseInt(parts[1]);
        if (!isNaN(parsed)) latestNumber = parsed + 1;
      }
    }

    return `${prefix}/${latestNumber}/${currentFY}`;
  }

  async generateTempInvoiceNumber(billedByOrgId: string, billedToOrgId: string): Promise<string> {
    const getFY = () => {
      const today = new Date();
      const year = today.getFullYear();
      const month = today.getMonth() + 1;
      const startYear = month < 4 ? year - 1 : year;
      const endYear = startYear + 1;
      return `${startYear}-${endYear}`;
    };

    const currentFY = getFY();
    const billedByOrg = await this.orgModel.findById(billedByOrgId).exec();
    const billedToOrg = await this.orgModel.findById(billedToOrgId).exec();
    const billedByOrgTitle = billedByOrg?.title || 'CL';
    const billedToOrgTitle = billedToOrg?.title || 'PL';
    const prefix =
      billedByOrgTitle?.slice(0, 2).toUpperCase() +
      billedToOrgTitle?.slice(0, 2).toUpperCase();

    // Match invoices in this FY
    const fyStart = new Date(`${currentFY.split('-')[0]}-04-01T00:00:00.000Z`);
    const fyEnd = new Date(`${currentFY.split('-')[1]}-03-31T23:59:59.999Z`);

    const latestInvoice = await this.invoiceModel
      .findOne({
        billedByOrg: billedByOrgId,
        billedToOrg: billedToOrgId,
        createdAt: { $gte: fyStart, $lte: fyEnd },
        isTemp: true, // Ensure we only consider temp invoices
      })
      .sort({ createdAt: -1 })
      .lean();

    let latestNumber = 1;
    if (latestInvoice) {
      const parts = latestInvoice.invoiceNumber.split('/');
      if (parts.length >= 2) {
        const parsed = parseInt(parts[1]);
        if (!isNaN(parsed)) latestNumber = parsed + 1;
      }
    }

    // return `${prefix}/${latestNumber}/${currentFY}`;
    return `T-${prefix}/${latestNumber}/${currentFY}`;
  }

  async raiseBgvMail(user: any, billedByOrgId: string, billedToOrgId: string, jobApplicationId: string): Promise<string> {
    try {
      const billedByOrg = await this.orgModel.findById(billedByOrgId).exec();
      const billedToOrg = await this.orgModel.findById(billedToOrgId).exec();
      const jobApp = await this.jobApplicationModel.findById(jobApplicationId).populate('jobId').exec();


      const eventName = EmailTemplateEvent.RAISE_INVOICE;
      const emailTemplate = await this.getEmailTemplate(eventName, user);
      const istTimeZone = 'Asia/Kolkata';

      let body = emailTemplate.templateHTMLContent ? unescape(emailTemplate.templateHTMLContent) : '';
      body = await this.replacePlaceholders(body, user, billedByOrg, billedToOrg, emailTemplate._id.toString(), jobApp);

      this.logger.log(`Triggered ${eventName} event for interview reminder.`);
      body = body.replace(/lt;\/span>/g, '</span>');
      console.log("body", body)
      const cleanedHtml = body.replace(/<span[^>]*class="TiptapEditor_mention__[^"]*"[^>]*>(.*?)<\/span>/g, '$1');
      console.log("ceaned html", cleanedHtml)
      return cleanedHtml;
    } catch (error) {
      this.logger.error(`Failed to send interview reminder. ${error.message}`);
      throw new InternalServerErrorException(`Error when sending interview reminder. ${error?.message}`);
    }
  }

  // Helper function to get email template
  async getEmailTemplate(eventName: EmailTemplateEvent, user: any) {
    let emailTemplate = await this.emailTemplateService.findOne({
      eventName,
      isDefault: true,
      isGlobal: true,
      // org: user.org._id,
      isDeleted: false
    }).exec();

    if (!emailTemplate) {
      emailTemplate = await this.emailTemplateService.findOne({
        eventName,
        canDelete: false,
        // org: user.org._id,
        isGlobal: true,
        isDeleted: false
      }).exec();
    }

    if (!emailTemplate) {
      throw new Error(`Email template not found for the event: ${eventName}.`);
    }

    return emailTemplate;
  }

  // Helper function to replace placeholders in the email body
  async replacePlaceholders(body: string, user: any, billedByOrg: any, billedToOrg: any, emailTemplateId: string, jobApplication: any) {
    const placeholders = await this.placeholderService.find({ emailTemplate: emailTemplateId }).exec();

    placeholders.forEach((placeholder) => {
      const path = placeholder.jsonPath;
      const cleanData = JSON.parse(JSON.stringify({ user, data: jobApplication, billedByOrg, billedToOrg }));

      const value = JSONPath({
        path,
        json: cleanData
        // json: { user, data : jobApplication,billedByOrg,billedToOrg },
      });
      const regex = new RegExp(`#${placeholder.name}`, 'g');
      if (value.length > 0) {
        body = body.replace(regex, `<b>${value[0]}</b>`);
      }
    });
    return body;
  }

  //this is only for the full-time jobs
  async raiseInvoiceOnBgvApproval(user: any, applicationId: Types.ObjectId, payload: ApproveRejectInterimBgvDto) {

    const offer = await this.offerModel.findOne({ jobApplication: applicationId.toString() })
      .sort({ createdAt: -1 }) // 👉 Ensures the latest offer is returned
      .exec();
    // Step 3: Update the offer with the bgvId
    if (!offer) {
      throw new NotFoundException('No offer found for the specified job application.');
    }

    const existingBgv = await this.bgvModel.findOne({ jobApplication: applicationId.toString() }).exec();
    if (!existingBgv) {
      throw new NotFoundException('No BGV found for the specified job application.');
    }

    if (payload.isApproved) {
      existingBgv.status = OnboardingStatus.BGV_APPROVED;
      offer.status = OnboardingStatus.BGV_APPROVED;
      existingBgv.rejectionReason = undefined; // Clear previous reason if any
      const invoiceDetails = await this.findAddressByApplicationId(user, applicationId);
      if (invoiceDetails.invoiceNumber && invoiceDetails.invoiceAmount > 0) {
        const dto = {
          invoiceNumber: invoiceDetails.invoiceNumber,
          jobApplication: invoiceDetails.jobApplication._id.toString(),
          amount: invoiceDetails.invoiceAmount,
          // dueDate: "2025-06-13T09:58:00.000Z",
          raisedDate: new Date(),
          // fileMetaDataId: [
          //   "665cdb6be21e7d396c3a2f1a",
          //   "665cdb6be21e7d396c3a2f2a"
          // ],
          billedByOrg: invoiceDetails.billingOrgId,
          billedToOrg: invoiceDetails.billToOrgId,
          status: InvoiceStatus.NEW
        }
        const createdInvoice = await this.create(user, dto);
        offer.status = OnboardingStatus.INVOICE_GENERATED; // Set status to AWAITING_BGV
        await offer.save();
        return createdInvoice;
      }
    } else {
      existingBgv.status = OnboardingStatus.BGV_REJECTED;
      offer.status = OnboardingStatus.BGV_REJECTED;
      existingBgv.rejectionReason = payload.rejectionReason;
    }


    // offer.status = OnboardingStatus.AWAITING_BGV; // Set status to AWAITING_BGV
    // offer.stepperKey = 2;
    await offer.save();

    return offer;
  }

  async findInvoicesByBilledOrg(user: any, query: GetInvoicesQueryDto) {
    const { page = 1, limit = 10, billedToOrg, employmentType } = query;
    const skip = (page - 1) * limit;

    const userOrgId = Types.ObjectId.isValid(user.org?._id)
      ? new Types.ObjectId(user.org._id)
      : user.org._id;

    // Step 1: Build base invoice query
    const invoiceFilter: any = {
      $and: [
        {
          $or: [
            { billedByOrg: userOrgId },
            { billedByOrg: userOrgId.toString() },
          ],
        },
        { isDeleted: false },
      ],
    };


    if (billedToOrg) {
      invoiceFilter.$and.push({
        $or: [
          { billedToOrg: billedToOrg },
          { billedToOrg: new Types.ObjectId(billedToOrg) },
        ],
      });
    }

    // Step 2: Find invoices with jobApplications populated
    const invoices = await this.invoiceModel
      .find(invoiceFilter)
      .populate(this.populateOptions())
      .sort({ createdAt: -1 }) // Sort latest invoices at the top
      .skip(skip)
      .limit(limit)
      .exec();

    // Step 3: Filter in memory by employmentType (since no aggregation)
    const filteredInvoices = employmentType
      ? invoices.filter(inv => inv?.jobApplication?.jobId?.employmentType === employmentType)
      : invoices;

    // Step 4: Count total matching documents (with same filters)
    const totalCount = await this.invoiceModel.countDocuments(invoiceFilter);

    return {
      data: filteredInvoices,
      totalCount,
      page,
      limit,
    };
  }

  async findInvoiceDetailsByApplicationId(user: any, applicationId: Types.ObjectId) {
    try {
      const existingInvoice = await this.invoiceModel.findOne({ jobApplication: applicationId.toString() }).exec();

      const jobApp = await this.jobApplicationModel.findById(applicationId).populate('jobId').exec();
      const offer = await this.offerModel.findOne({ jobApplication: applicationId.toString() })
        .sort({ createdAt: -1 }) // 👉 Ensures the latest offer is returned
        .exec();
      console.log(jobApp)
      const job = await this.jobModel.findById(jobApp?.jobId).exec();
      console.log(job)
      let rateCard: any;
      let billToAddress: any;
      let billToOrg: any;

      const billingorg = await this.orgService.findOne(user.org._id);
      // const billingAddress = billingorg.addressDetails;
      const billingAddress = {
        ...((billingorg.addressDetails as any).toObject?.()),
        title: billingorg.title,
      };
      const billingBankDetails = billingorg.bankDetails;

      if (job?.jobType === JobType.Internal) {
        if (user?.roles.includes(Role.Vendor)) {
          if (job?.employmentType === EmploymentType.FullTime) {
            if (jobApp?.createdBy.roles?.includes(Role.Vendor)) {
              // isEligibleForInvoice = true;
              const existingOrgs = await this.orgModel.find({ 'contactDetails.contactEmail': user.email, isDeleted: false, orgType: OrgType.VENDOR_ORG }).exec();

              // Step 2: Extract org _ids
              const vendorOrgIds = existingOrgs.map(org => org._id.toString());
              console.log("vendorOrgIds", vendorOrgIds);

              const matchedRateCardEntry = job.vendorRateCards?.find((entry) =>
                vendorOrgIds.includes(entry.vendor?.toString())
              );

              const rateCardId = matchedRateCardEntry?.rateCard.toString() || null;
              const card = await this.rateCardModel
                .findById(rateCardId)
                .populate({ path: 'category', select: '_id label description client' })
                .exec();

              if (!card || card.isDeleted) {
                throw new NotFoundException('Rate card not found.');
              }
              console.log('card', card)
              rateCard = card;

              const billToOrgId = rateCard?.category?.org;
              const billToOrgDetails = await this.orgService.findOne(billToOrgId);
              // billToAddress = billToOrgDetails.addressDetails;
              billToAddress = {
                // ...billToOrgDetails.addressDetails,
                ...((billToOrgDetails.addressDetails as any).toObject?.()),
                title: billToOrgDetails.title,
              };
              billToOrg = billToOrgDetails;
            }
          }
        }

      }

      if (job?.jobType === JobType.External) {
        if (user?.roles.includes(Role.Vendor)) {
          if (job?.employmentType === EmploymentType.FullTime) {
            if (jobApp?.createdBy.roles?.includes(Role.Vendor)) {
              // isEligibleForInvoice = true;
              const existingOrgs = await this.orgModel.find({ 'contactDetails.contactEmail': user.email, isDeleted: false, orgType: OrgType.VENDOR_ORG }).exec();

              // Step 2: Extract org _ids
              const vendorOrgIds = existingOrgs.map(org => org._id.toString());
              console.log("vendorOrgIds", vendorOrgIds);

              const matchedRateCardEntry = job.vendorRateCards?.find((entry) =>
                vendorOrgIds.includes(entry.vendor?.toString())
              );

              const rateCardId = matchedRateCardEntry?.rateCard.toString() || null;
              const card = await this.rateCardModel
                .findById(rateCardId)
                .populate({ path: 'category', select: '_id label description client' })
                .exec();

              if (!card || card.isDeleted) {
                throw new NotFoundException('Rate card not found');
              }
              console.log('card', card)
              rateCard = card;

              const billToOrgId = rateCard?.category?.org;
              const billToOrgDetails = await this.orgService.findOne(billToOrgId);
              // billToAddress = billToOrgDetails.addressDetails;
              billToAddress = {
                // ...billToOrgDetails.addressDetails,
                ...((billToOrgDetails.addressDetails as any).toObject?.()),
                title: billToOrgDetails.title,
              };
              billToOrg = billToOrgDetails;

            }
          }
        }
        else {
          if (job?.employmentType === EmploymentType.FullTime) {
            // isEligibleForInvoice = true;
            if (job?.rateCard) {
              console.log("job.rateCard", job?.rateCard)
              const rateCardId = job?.rateCard;
              const card = await this.rateCardModel
                .findById(rateCardId)
                .populate({ path: 'category', select: '_id label description client' })
                .exec();

              if (!card || card.isDeleted) {
                throw new NotFoundException('Rate card not found.');
              }
              console.log('card', card)
              rateCard = card;
              // return card;
            }

            const billToOrgId = rateCard?.category?.client;
            const billToOrgDetails = await this.orgService.findOne(billToOrgId);
            // billToAddress = billToOrgDetails.clientAddress;
            billToAddress = {
              // ...billToOrgDetails.addressDetails,
              ...((billToOrgDetails.addressDetails as any).toObject?.()),
              title: billToOrgDetails.title,
            };
            billToOrg = billToOrgDetails;

          }
        }


      }

      const invoiceNumber = await this.generateInvoiceNumber(billingorg?._id?.toString(), billToOrg?._id?.toString());
      // const invoiceNumber = existingInvoice?.invoiceNumber;
      // let invoiceAmount = 0;
      const invoiceAmount = existingInvoice?.amount;

      // if (!rateCard) {
      //   throw new NotFoundException(`No rate card exists to generate invoice`);
      // } else {
      //   if (rateCard.percentageOfCTC == null && rateCard.fixedRate != null) {
      //     invoiceAmount = rateCard.fixedRate;
      //   } else if (rateCard.percentageOfCTC != null && rateCard.fixedRate == null) {
      //     invoiceAmount = (offer?.salaryPerAnnum ?? 0) * rateCard.percentageOfCTC;
      //   }
      // }
      if (!jobApp) {
        throw new NotFoundException(`The application with id: "${applicationId}" doesn't exist.`);
      }
      return {
        invoiceNumber, invoiceAmount, rateCard, billToAddress, billingAddress, billingBankDetails,
        offeredCtc: offer?.salaryPerAnnum,
        billingOrgId: billingorg?._id?.toString(),
        billToOrgId: billToOrg?._id?.toString(), jobApplication: jobApp, raisedDate: existingInvoice?.raisedDate
      };
      // return responseWithFlags;
    }
    catch (error) {
      // this.logger.error(`An error occurred in fetching offer by Id ${applicationId}. ${error?.message}`);
      throw error;

    }
  }

  async findInvoiceDetailsByEmployeeAndTimesheetId(user: any, employeeId: Types.ObjectId, timesheetId: Types.ObjectId) {
    try {
      const existingInvoice = await this.invoiceModel.findOne({
        employee: employeeId.toString(),
        timesheet: new Types.ObjectId(timesheetId),
        isDeleted: false,
      }).exec();


      const employee = await this.employeeModel.findById(employeeId)
        .populate({ path: 'job', model: 'Job' })
        .populate({ path: 'jobApplication', model: 'JobApplication' })
        .exec();

      if (!employee) throw new NotFoundException('Employee not found');

      const job = employee.job;
      const jobApp = employee.jobApplication;

      let jobApplicationId: string | undefined;

      if (employee.jobApplication && typeof employee.jobApplication === 'object' && '_id' in employee.jobApplication) {
        jobApplicationId = (employee.jobApplication as any)._id?.toString();
      } else {
        jobApplicationId = (employee.jobApplication as any)?.toString(); // in case it's just an ObjectId or string
      }

      const offer = await this.offerModel.findOne({
        jobApplication: jobApplicationId
      });

      let rateCard: any;
      let billToAddress: any;
      let billToOrg: any;

      const billingorg = await this.orgService.findOne(user.org._id);
      const billingAddress = {
        ...((billingorg.addressDetails as any).toObject?.()),
        title: billingorg.title,
      };
      const billingBankDetails = billingorg.bankDetails;

      if (job?.jobType === JobType.Internal && user?.roles.includes(Role.Vendor)) {
        const vendorOrgs = await this.orgModel.find({
          'contactDetails.contactEmail': user.email,
          isDeleted: false,
          orgType: OrgType.VENDOR_ORG
        }).exec();

        const vendorOrgIds = vendorOrgs.map(org => org._id.toString());
        const matchedRateCardEntry = job.vendorRateCards?.find(entry =>
          vendorOrgIds.includes(entry.vendor?.toString())
        );

        const rateCardId = matchedRateCardEntry?.rateCard?.toString();
        const card = await this.rateCardModel
          .findById(rateCardId)
          .populate({ path: 'category', select: '_id label description client' })
          .exec();

        if (!card || card.isDeleted) throw new NotFoundException('Rate card not found.');
        rateCard = card;

        const billToOrgDetails = await this.orgService.findOne(
          typeof card.category?.org === 'object'
            ? (card.category?.org as any)._id
            : card.category?.org
        );
        billToAddress = {
          ...((billToOrgDetails.addressDetails as any).toObject?.()),
          title: billToOrgDetails.title,
        };
        billToOrg = billToOrgDetails;

      } else if (job?.jobType === JobType.External) {
        if (job?.employmentType === EmploymentType.FullTime ||
          job?.employmentType === EmploymentType.Contract) {
          if (job?.rateCard) {
            const card = await this.rateCardModel
              .findById(job.rateCard)
              .populate({ path: 'category', select: '_id label description client' })
              .exec();

            if (!card || card.isDeleted) throw new NotFoundException('Rate card not found.');
            rateCard = card;

            const billToOrgDetails = await this.orgService.findOne(
              typeof card.category?.client === 'object'
                ? (card.category?.client as any)._id
                : card.category?.client
            );
            billToAddress = {
              ...((billToOrgDetails.addressDetails as any).toObject?.()),
              title: billToOrgDetails.title,
            };
            billToOrg = billToOrgDetails;
          }
        }
      }

      const invoiceNumber = existingInvoice?.invoiceNumber ??
        await this.generateInvoiceNumber(
          billingorg?._id?.toString(),
          billToOrg?._id?.toString()
        );

      const invoiceAmount = existingInvoice?.amount;
      return {
        invoiceNumber,
        invoiceAmount,
        rateCard,
        billToAddress,
        billingAddress,
        billingBankDetails,
        offeredCtc: offer?.salaryPerAnnum,
        billingOrgId: billingorg?._id?.toString(),
        billToOrgId: billToOrg?._id?.toString(),
        billingOrg: billingorg,
        employee,
        jobApplication: jobApp,
        timesheetId,
        raisedDate: existingInvoice?.raisedDate,
      };

    } catch (error) {
      throw error;
    }
  }


  private populateOptions() {
    return [
      {
        path: 'employee',
        model: 'Employee',
        populate: [
          { path: 'payRollOrg', model: 'Org', select: '_id title' },
          { path: 'endClientOrg', model: 'Org', select: '_id title' },
          { path: 'job', model: 'Job', select: '_id title employmentType' },
          { path: 'jobApplication', model: 'JobApplication', select: '_id firstName lastName' },
          { path: 'bgvId', model: 'Bgv', select: '_id' },
          { path: 'bankDetails', model: 'BankDetails', select: '_id accountNumber ifscCode' },
          {
            path: 'createdBy',
            model: 'BasicUser',
            select: '_id email firstName lastName',
          },
        ]
      }, {
        path: 'jobApplication',
        populate: {
          path: 'jobId',
          populate: {
            path: 'endClientOrg',
            model: 'Org',
            select: '_id title'
          },
          model: 'Job',
          select: 'title endClientOrg employmentType', // Optional: only if needed
        },
      },
      {
        path: 'billedByOrg',
        model: 'Org',
        select: '_id title orgType'
      },
      {
        path: 'billedToOrg',
        model: 'Org',
        select: '_id title orgType'
      },
      {
        path: 'generatedBy',
        model: 'BasicUser',
        select: '_id email firstName lastName'
      },
      {
        path: 'forwardedBy',
        model: 'BasicUser',
        select: '_id email firstName lastName'
      },
      {
        path: 'fileMetaDataId',
        model: 'FileMetadata',
        select: '_id originalName fileSize fileType locationUrl'
      },
      // {
      //   path: 'timesheet',
      //   model: 'Timesheet',
      //   populate: [
      //     {
      //       path: 'projectId',
      //       model: 'Project',
      //       select: '_id name org client'
      //     }
      //   ]
      // }
    ];
  }


}
