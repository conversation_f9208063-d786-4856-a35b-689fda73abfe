import { FileInterceptor } from '@nest-lab/fastify-multer';
import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  FileTypeValidator,
  Get,
  HttpStatus,
  Logger,
  MaxFileSizeValidator,
  Param,
  ParseFilePipe,
  Patch,
  Post,
  Query,
  Req,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiConsumes,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import axios from 'axios';
import FormData from 'form-data';
import moment from 'moment';
import { Roles } from 'src/auth/decorators/roles.decorator';
import { AuthJwtGuard } from 'src/auth/guards/auth-jwt/auth-jwt.guard';
import { RolesGuard } from 'src/auth/guards/roles/roles.guard';
import { GroupByType } from 'src/shared/constants';
import { validateObjectId } from 'src/utils/validation.utils';
import { CreateJobApplicationFormDto } from './dto/create-job-application-form.dto';
import { CreatePublicJobApplicationFormDto } from './dto/create-public-job-application-dto';
import { CustomFilterDto } from './dto/customFilterDto.dto';
import { DynamicFieldDto } from './dto/dynamic-field.dto';
import { QueryJobApplicationDto } from './dto/query-job-application.dto';
import { UpdateDynamicFieldDto } from './dto/update-dynamic-field.dto';
import { UpdateJobApplicationFormDto } from './dto/update-job-application-form.dto';
import { UpdateJobApplicationStatusDto } from './dto/update-job-application-status.dto';
import { UpdateAiInterviewDto } from './dto/update-ai-interview.dto';
import { JobApplicationFormService } from './job-application-form.service';

@Controller('')
@ApiTags('Job-Application-Forms')
export class JobApplicationFormController {
  constructor(
    private readonly jobApplicationFormService: JobApplicationFormService,
  ) {}

  private readonly logger = new Logger(JobApplicationFormController.name);

  @Post()
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.BUHead, Role.TeamLead, Role.DeliveryManager, Role.Recruiter, Role.Vendor, Role.JobSeeker, Role.JobSeeker, Role.JobSeeker, Role.Freelancer, Role.ResourceManager, Role.TeamMember)
  @Roles()
  @ApiResponse({ status: 201, description: 'Job application is saved.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Data. ' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  @ApiResponse({
    status: 403,
    description:
      'Forbidden, user with role "BUHead", "TeamLead", "DeliveryManager" and "Recruiter" can only use this end point.',
  })
  @ApiOperation({
    summary: 'Create a job application',
    description: `This endpoint for creating a job application. This is only accessible for "BUHead", "TeamLead", "DeliveryManager" and "Recruiter".`,
  })
  create(
    @Req() req: any,
    @Body() createJobApplicationDto: CreateJobApplicationFormDto,
  ) {
    createJobApplicationDto.createdBy = req.user._id;
    return this.jobApplicationFormService.create(
      req.user,
      createJobApplicationDto,
    );
  }

  @Get('all')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager, Role.Vendor, Role.JobSeeker, Role.JobSeeker)
  @Roles()
  @ApiOperation({
    summary: 'Retrieve all job applications',
    description: `This endpoint returns a list of all job applications. This is accessible for everyone. `,
  })
  @ApiResponse({ status: 200, description: 'Job application retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  // @ApiResponse({ status: 403, description: 'Forbidden, user with role admin and salesrep  can only use this end point.' })
  findAll() {
    return this.jobApplicationFormService.findAll();
  }

  @Post('parse-resume')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  @Roles()
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @ApiResponse({ status: 201, description: 'Resume is parsed.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Data. ' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  // @ApiResponse({
  //   status: 403,
  //   description:
  //     'Forbidden, user with role "BUHead" and "ResourceManager" can use this end point.',
  // })
  @ApiOperation({
    summary: 'Parse Resume',
    description: `This endpoint for creating parsing a resume.`,
  })
  async createWithExternalProcessing(
    @Req() req: any,
    @UploadedFile(
      new ParseFilePipe({
        validators: [
          new MaxFileSizeValidator({ maxSize: 10000000 }), // 10MB limit
          new FileTypeValidator({
            fileType:
              'application/pdf|application/msword|application/vnd.openxmlformats-officedocument.wordprocessingml.document|text/plain',
          }),
        ],
        errorHttpStatusCode: HttpStatus.UNPROCESSABLE_ENTITY,
        fileIsRequired: true,
      }),
    )
    file: Express.Multer.File,
  ) {
    if (!file) {
      throw new BadRequestException('File is required');
    }

    try {
      // Create a readable stream from buffer
      const { Readable } = require('stream');
      const fileStream = Readable.from(file.buffer);
      // Send file to ai-service API
      const formData = new FormData();
      formData.append('file', fileStream, {
        filename: file.originalname,
        contentType: file.mimetype,
        knownLength: file.size,
      });
      const aiServiceUrl =
        process.env.AI_SERVICE_URL || 'http://localhost:6000';
      const url = `${aiServiceUrl}/api/v1/parser/resume`;
      const response = await axios.post(url, formData, {
        headers: formData.getHeaders(),
      });

      if (response.status === 200 || response.status === 201) {
        this.logger.log('Resume parsed successfully.');
        return response.data;
      }
      throw new Error('Failed to parse resume.');
    } catch (error) {
      if (error.response) {
        throw new BadRequestException(
          `External service error: ${error.response.data.message || error.message}`,
        );
      }
      throw new BadRequestException(`Failed to process file: ${error.message}`);
    }
  }

  @Get('user-applicants')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager, Role.Vendor, Role.JobSeeker, Role.Freelancer)
  @Roles()
  @ApiOperation({
    summary: 'Retrieve all job applications of logged-in user',
    description: `This endpoint returns a list of all job applications. This is accessible for everyone. `,
  })
  @ApiResponse({ status: 200, description: 'Job application retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  // @ApiResponse({ status: 403, description: 'Forbidden, user with role admin and salesrep  can only use this end point.' })
  findAllUser(@Req() req: any, @Query() query: QueryJobApplicationDto) {
    const userId = req.user._id;
    return this.jobApplicationFormService.findAllUser(userId, query);
  }

  @Get(':jobApplicationId')
  // @ApiBearerAuth()
  // @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager, Role.Vendor, Role.JobSeeker, Role.JobSeeker, Role.Freelancer, Role.JobSeeker)
  // @Roles()
  @ApiOperation({
    summary: 'Retrieve an job application by Id',
    description:
      'This endpoint returns an job application by its Id. This is accessible for everyone.',
  })
  @ApiResponse({ status: 200, description: 'Job application is retrieved.' })
  @ApiResponse({ status: 404, description: 'Job application not found.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  // @ApiResponse({ status: 403, description: 'Forbidden, user with role admin can only use this end point.' })
  @ApiParam({
    name: 'jobApplicationId',
    description: 'id of the jobapplication.',
  })
  findOne(@Param('jobApplicationId') jobApplicationId: string) {
    const objId = validateObjectId(jobApplicationId);
    return this.jobApplicationFormService.findOne(objId);
  }

  @Get('count')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager, Role.Vendor, Role.JobSeeker, Role.JobSeeker, Role.Freelancer, Role.TeamMember)
  @Roles()
  @ApiOperation({
    summary: 'Retrieve count of job applications by Job Id',
    description:
      'This endpoint returns an job application by its Id.  This is accessible for everyone.',
  })
  @ApiResponse({ status: 200, description: 'Job application is retrieved.' })
  @ApiResponse({ status: 404, description: 'Job application not found.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  // @ApiResponse({ status: 403, description: 'Forbidden, user with role admin can only use this end point.' })
  @ApiQuery({
    name: 'jobId',
    required: true,
    type: String,
    description: 'Id of Job',
  })
  getCountOfJobApplications(@Query('jobId') jobId: string) {
    const sanitizedJobId = validateObjectId(jobId);
    return this.jobApplicationFormService.getCountOfJobApplications(jobId);
  }

  @Get(':jobId/user-application')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager, Role.Vendor, Role.JobSeeker, Role.Freelancer, Role.TeamMember)
  @Roles()
  @ApiOperation({
    summary: `Retrieve a user's job application for a specific job`,
    description:
      'This endpoint returns the job application submitted by a user for a specific job, identified by job ID and user ID. This is accessible for everyone.',
  })
  @ApiResponse({
    status: 200,
    description: 'Job application retrieved successfully.',
  })
  @ApiResponse({ status: 404, description: 'Job application not found.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  // @ApiResponse({ status: 403, description: 'Forbidden, only users with appropriate roles can access this endpoint.' })
  @ApiParam({ name: 'jobId', description: 'ID of the job.' })
  findUserApplicationForJob(@Req() req: any, @Param('jobId') jobId: string) {
    const createdBy = req.user._id;
    // this.logger.log(createdBy)
    return this.jobApplicationFormService.findUserApplicationForJob(
      req.user,
      jobId,
      createdBy,
    );
  }

  // TODO: Role check
  @Get('recruiter-count')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager, Role.Vendor, Role.JobSeeker, Role.JobSeeker)
  @Roles()
  @ApiOperation({
    summary: 'Retrieve count of job applications by Job Id',
    description:
      'This endpoint returns an job application by its Id. This is accessible for everyone.',
  })
  @ApiResponse({ status: 200, description: 'Job application is retrieved.' })
  @ApiResponse({ status: 404, description: 'Job application not found.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  // @ApiResponse({ status: 403, description: 'Forbidden, user with role admin can only use this end point.' })
  @ApiQuery({
    name: 'jobId',
    required: true,
    type: String,
    description: 'Id of Job',
  })
  @ApiQuery({
    name: 'recruiterId',
    required: true,
    type: String,
    description: 'Id of Recruiter',
  })
  getCountOfJobApplicationsByCreatedBy(
    @Query('jobId') jobId: string,
    @Query('recruiterId') recruiterId: string,
  ) {
    const sanitizedJobId = validateObjectId(jobId);
    const sanitizedRecruiterId = validateObjectId(recruiterId);
    return this.jobApplicationFormService.getCountOfJobApplicationsByCreatedBy(
      jobId,
      recruiterId,
    );
  }

  @Patch(':jobApplicationId')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.BUHead, Role.TeamLead, Role.DeliveryManager, Role.Recruiter, Role.Vendor, Role.JobSeeker, Role.TeamMember)
  @Roles()
  @ApiOperation({
    summary: 'Update an job application by Id',
    description: `This endpoint updates an job application by Id. This is only accessible for "BUHead", "TeamLead", "DeliveryManager" and "Recruiter".`,
  })
  @ApiResponse({ status: 200, description: 'Job application is updated.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  @ApiResponse({
    status: 403,
    description:
      'Forbidden, user with role "BUHead", "TeamLead", "DeliveryManager" and "Recruiter" can only use this end point.',
  })
  @ApiParam({
    name: 'jobApplicationId',
    description: 'id of the job application.',
  })
  update(
    @Param('jobApplicationId') jobApplicationId: string,
    @Body() updatejobApplicationDto: UpdateJobApplicationFormDto,
  ) {
    const objId = validateObjectId(jobApplicationId);
    return this.jobApplicationFormService.update(
      objId,
      updatejobApplicationDto,
    );
  }

  @Patch(':jobApplicationId/ai-interview')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  @Roles()
  @ApiOperation({
    summary: 'Update AI interview details for a job application',
    description: 'Updates the AI interview object fields for the specified job application. This is accessible for authenticated users.',
  })
  @ApiResponse({ status: 200, description: 'AI interview details updated successfully.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 404, description: 'Job application not found.' })
  @ApiParam({
    name: 'jobApplicationId',
    description: 'ID of the job application to update AI interview details for.',
  })
  async updateAiInterview(
    @Param('jobApplicationId') jobApplicationId: string,
    @Body() updateAiInterviewDto: UpdateAiInterviewDto,
  ) {
    const objId = validateObjectId(jobApplicationId);
    return this.jobApplicationFormService.updateAiInterview(objId, updateAiInterviewDto);
  }

  @Patch(':jobApplicationId/update-stage')
  @ApiParam({
    name: 'jobApplicationId',
    description: `Id of the job application.`,
  })
  @ApiResponse({ status: 200, description: `Job application stage updated.` })
  @ApiResponse({ status: 401, description: `Unauthorized.` })
  @ApiResponse({
    status: 403,
    description: `Forbidden, user with role "BUHead", "TeamLead", "DeliveryManager" and "Recruiter" use this end point.`,
  })
  @ApiQuery({
    name: 'stageId',
    required: true,
    type: String,
    description: 'Stage of the job application.',
  })
  @ApiOperation({
    summary: `Update the stage of the job application`,
    description: `This endpoint update the stage of the job application. This is accessible only for "BUHead", "TeamLead", "DeliveryManager" and "Recruiter".`,
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.BUHead, Role.DeliveryManager, Role.ResourceManager, Role.AccountManager, Role.TeamLead)
  @Roles()
  updateStage(
    @Req() req: any,
    @Param('jobApplicationId') jobApplicationId: string,
    @Query('stageId') stageId: string,
  ) {
    // this.logger.log(stageId)
    // this.logger.log(jobApplicationId)
    const objId = validateObjectId(jobApplicationId);
    const sanitizedStageId = validateObjectId(stageId);
    return this.jobApplicationFormService.updateStage(
      objId,
      sanitizedStageId,
      req.user,
    );
  }

  @Delete(':jobApplicationId/hard-delete')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.BUHead, Role.TeamLead, Role.DeliveryManager, Role.Recruiter, Role.Vendor, Role.JobSeeker, Role.JobSeeker)
  @Roles()
  @ApiOperation({
    summary: 'Delete an job application by Id',
    description:
      'This endpoint deletes an job application by Id. This is only accessible for "BUHead", "TeamLead", "DeliveryManager" and "Recruiter".',
  })
  @ApiResponse({ status: 200, description: 'Job application is hard deleted.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({
    status: 403,
    description:
      'Forbidden, user with role "BUHead", "TeamLead", "DeliveryManager" and "Recruiter" can only use this end point.',
  })
  @ApiParam({
    name: 'jobApplicationId',
    description: 'ID of the job application',
  })
  remove(@Param('jobApplicationId') jobApplicationId: string) {
    const objId = validateObjectId(jobApplicationId);
    return this.jobApplicationFormService.remove(objId);
  }

  @Get('find-by-org-and-job-id')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager, Role.Vendor, Role.JobSeeker, Role.JobSeeker)
  @Roles()
  @ApiOperation({
    summary: 'Search job applications by org Id and/or job Id',
    description: `This endpoint returns a list of job applications filtered by org Id and/or job Id. This is accessible for everyone.`,
  })
  @ApiResponse({ status: 200, description: 'Job applications retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  // @ApiResponse({ status: 403, description: 'Forbidden, user with role admin, superAdmin, and sales rep can only use this endpoint.' })
  @ApiQuery({
    name: 'orgId',
    required: false,
    type: String,
    description: 'Organization Id',
  })
  @ApiQuery({
    name: 'jobId',
    required: false,
    type: String,
    description: 'Job Id',
  })
  async findByJobAndOrgId(
    @Query('orgId') orgId: string,
    @Query('jobId') jobId: string,
  ) {
    // Search job applications by org Id and/or job Id
    return this.jobApplicationFormService.findByJobAndOrgId(orgId, jobId);
  }

  @Get(':jobId/filter-by-stage')
  @ApiResponse({
    status: 200,
    description: `Filtered job applications are retrieved.`,
  })
  @ApiResponse({ status: 401, description: `Unauthorized.` })
  // @ApiResponse({ status: 403, description: `Forbidden, user with role "super-admin", "admin" and "sales-rep" can only use this end point.` })
  @ApiOperation({
    summary: `Filtering job applications`,
    description: `This endpoint filter job applications .  This is accessible for everyone.`,
  })
  @ApiParam({ name: 'jobId', type: String, description: 'Id of the job' })
  @ApiQuery({
    name: 'stageId',
    type: String,
    description: 'Stage Id for filtering job applications based on stage',
    required: true,
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager, Role.Vendor, Role.JobSeeker, Role.JobSeeker)
  @Roles()
  filterJobApplicationsByStage(
    @Param('jobId') jobId: string,
    @Query('stageId') stageId: string,
  ) {
    return this.jobApplicationFormService.filterJobApplicationsByStage(
      jobId,
      stageId,
    );
  }

  @Get()
  @ApiOperation({
    summary: `Get job applications by job id`,
    description: `This endpoint retrieves job applications by job id.  This is accessible for everyone.`,
  })
  @ApiResponse({
    status: 200,
    description: `Filtered job applications are retrieved.`,
  })
  @ApiResponse({ status: 401, description: `Unauthorized.` })
  // @ApiResponse({ status: 403, description: `Forbidden, user with role "super-admin", "admin" and "sales-rep" can only use this end point.` })
  @ApiQuery({
    name: 'jobId',
    type: String,
    description: 'Job Id for retrieving job applications',
    required: true,
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.AccountManager, Role.TeamLead, Role.TeamMember, Role.Recruiter, Role.Vendor, Role.JobSeeker, Role.Freelancer)
  @Roles()
  getJobApplicatinsByJobId(@Query('jobId') jobId: string) {
    const objId = validateObjectId(jobId);
    return this.jobApplicationFormService.getJobApplicatinsByJobId(jobId);
  }

  @Patch(':jobApplicationId/update-status')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.BUHead, Role.TeamLead, Role.DeliveryManager, Role.Recruiter, Role.Vendor, Role.JobSeeker, Role.JobSeeker)
  @Roles()
  @ApiOperation({
    summary: 'Update an job application status by Id',
    description: `This endpoint updates an job application status by Id. This is only accessible for "BUHead", "TeamLead", "DeliveryManager" and "Recruiter".`,
  })
  @ApiResponse({
    status: 200,
    description: 'Job application status is updated.',
  })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  @ApiResponse({
    status: 403,
    description:
      'Forbidden, user with role "BUHead", "TeamLead", "DeliveryManager" and "Recruiter" can only use this end point.',
  })
  @ApiParam({
    name: 'jobApplicationId',
    description: 'id of the job application.',
  })
  // @ApiParam({ name: 'jobId',type: String, description: 'Id of the job' })
  updateStatus(
    @Param('jobApplicationId') jobApplicationId: string,
    @Body() updateJobApplicationStatusDto: UpdateJobApplicationStatusDto,
  ) {
    const objId = validateObjectId(jobApplicationId);
    return this.jobApplicationFormService.updateStatus(
      objId,
      updateJobApplicationStatusDto,
    );
  }

  @Get(':jobId/filter-job-applications')
  @ApiResponse({
    status: 200,
    description: `Filtered job applications are retrieved.`,
  })
  @ApiResponse({ status: 401, description: `Unauthorized.` })
  // @ApiResponse({ status: 403, description: `Forbidden, user with role "super-admin", "admin" and "sales-rep" can only use this end point.` })
  @ApiOperation({
    summary: `Filtering job applications`,
    description: `This endpoint filter job applications . This is accessible for everyone.`,
  })
  @ApiParam({ name: 'jobId', type: String, description: 'Id of the job' })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager, Role.Vendor, Role.JobSeeker, Role.JobSeeker)
  @Roles()
  filterJobApplications(
    @Param('jobId') jobId: string,
    @Query() queryJobApplicationDto: QueryJobApplicationDto,
  ) {
    return this.jobApplicationFormService.filterJobApplications(
      jobId,
      queryJobApplicationDto,
    );
  }

  // Add a new dynamic field
  @Post('dynamic-fields')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.Vendor, Role.JobSeeker)
  @Roles()
  @ApiOperation({
    summary: 'Add a new dynamic field',
    description: 'This is accessible only for "Admin".',
  })
  @ApiResponse({
    status: 201,
    description: 'Dynamic field added successfully.',
  })
  @ApiResponse({ status: 400, description: 'Bad Request / Invalid data.' })
  @ApiResponse({
    status: 403,
    description: 'Forbidden, user with role admin can only use this end point.',
  })
  async addDynamicField(
    @Req() req: any,
    @Body() dynamicFieldDto: DynamicFieldDto,
  ) {
    if (req.user.org) {
      dynamicFieldDto.orgId = req.user.org._id;
    }
    return await this.jobApplicationFormService.addDynamicField(
      dynamicFieldDto,
    );
  }

  // Update a specific dynamic field by ID
  @Patch('dynamic-fields/:fieldId')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.Vendor, Role.JobSeeker, Role.JobSeeker)
  @Roles()
  @ApiOperation({
    summary: 'Update a specific dynamic field',
    description: 'This is accessible only for "Admin".',
  })
  @ApiParam({ name: 'fieldId', description: 'ID of the field to be updated' })
  @ApiResponse({
    status: 200,
    description: 'Dynamic field updated successfully.',
  })
  @ApiResponse({ status: 400, description: 'Bad Request / Invalid data.' })
  @ApiResponse({
    status: 403,
    description: 'Forbidden, user with role admin can only use this end point.',
  })
  @ApiResponse({ status: 404, description: 'Dynamic field not found.' })
  async updateDynamicField(
    @Param('fieldId') fieldId: string,
    @Body() dynamicFieldDto: UpdateDynamicFieldDto,
  ) {
    return await this.jobApplicationFormService.updateDynamicField(
      fieldId,
      dynamicFieldDto,
    );
  }

  // Delete a specific dynamic field by ID
  @Delete('dynamic-fields/:fieldId')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.Vendor, Role.JobSeeker, Role.JobSeeker)
  @Roles()
  @ApiOperation({
    summary: 'Delete a specific dynamic field',
    description: 'This is accessible only for "Admin".',
  })
  @ApiParam({ name: 'fieldId', description: 'ID of the field to be deleted' })
  @ApiResponse({
    status: 200,
    description: 'Dynamic field deleted successfully.',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden, user with role admin can only use this end point.',
  })
  @ApiResponse({ status: 404, description: 'Dynamic field not found.' })
  async deleteDynamicField(@Param('fieldId') fieldId: string) {
    return await this.jobApplicationFormService.deleteDynamicField(fieldId);
  }

  @Get('dynamic-fields')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.Vendor, Role.JobSeeker, Role.TeamMember, Role.Recruiter, Role.BUHead, Role.DeliveryManager, Role.TeamLead, Role.Freelancer)
  @Roles()
  @ApiOperation({
    summary: 'Get all dynamic fields for an organization',
    description: 'This is accessible only for "Admin".',
  })
  @ApiQuery({
    name: 'orgId',
    description: 'ID of the organization',
    required: false,
  })
  @ApiQuery({
    name: 'isJobApplicationField',
    description: 'Filter by whether the field is related to job applications',
    required: false,
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden, user with role admin can only use this end point.',
  })
  @ApiResponse({
    status: 200,
    description: 'Dynamic fields retrieved successfully.',
  })
  async getAllDynamicFields(
    @Req() req: any,
    @Query('orgId') orgId?: string,
    @Query('isJobApplicationField') isJobApplicationField?: boolean,
  ) {
    if (!orgId) {
      orgId = req.user.org._id;
    }
    return await this.jobApplicationFormService.getAllDynamicFields(
      orgId,
      isJobApplicationField,
    );
  }

  // Get a specific dynamic field by ID
  @Get('dynamic-fields/:fieldId')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin)
  @Roles()
  @ApiOperation({
    summary: 'Retrieve a specific dynamic field by ID',
    description: 'This is accessible only for "Admin".',
  })
  @ApiParam({ name: 'fieldId', description: 'ID of the field' })
  @ApiResponse({
    status: 200,
    description: 'Dynamic field retrieved successfully.',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden, user with role admin can only use this end point.',
  })
  @ApiResponse({ status: 404, description: 'Dynamic field not found.' })
  async getDynamicField(@Param('fieldId') fieldId: string) {
    return await this.jobApplicationFormService.getDynamicField(fieldId);
  }

  @Get('user-job-application-count')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager, Role.Vendor, Role.JobSeeker, Role.JobSeeker, Role.Freelancer, Role.JobSeeker)
  @Roles()
  @ApiOperation({
    summary: 'Retrieve job application counts for the logged-in user',
    description: `This endpoint returns the count of job applications created and active by the logged-in user, grouped by month and year. This is accessible for everyone`,
  })
  @ApiResponse({
    status: 200,
    description: 'Job application counts retrieved.',
  })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  // @ApiResponse({ status: 403, description: 'Forbidden, only users with appropriate roles can access this endpoint.' })
  async getUserJobApplicationCount(@Req() req: any) {
    const userId = req.user._id;
    return await this.jobApplicationFormService.getUserJobApplicationCount(
      userId,
    );
  }

  @Get('job-application-counts')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager, Role.Vendor, Role.JobSeeker, Role.JobSeeker)
  @Roles()
  @ApiOperation({
    summary: 'Get job application counts by status and date filters',
    description:
      'Retrieve counts of shortlisted, hired, and rejected job applications for an organization, filtered by date range.',
  })
  @ApiQuery({
    name: 'filter',
    required: false,
    description: 'Date filter: today (default), yesterday, thisWeek, thisMonth',
  })
  @ApiResponse({
    status: 200,
    description: 'Job application counts retrieved.',
  })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  async getJobApplicationCounts(
    @Req() req: any,
    @Query('filter') filter: string,
  ) {
    const userId = req.user._id;
    if (!req.user.org._id) {
      throw new BadRequestException('User does not have org id');
    }
    return await this.jobApplicationFormService.getJobApplicationCounts(
      userId,
      filter,
      req.user,
    );
  }

  @Get('job-application-internal-counts')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager, Role.Vendor, Role.JobSeeker, Role.JobSeeker)
  @Roles()
  @ApiOperation({
    summary: 'Get job application counts by status and date filters',
    description:
      'Retrieve counts of shortlisted, hired, and rejected job applications for an organization, filtered by date range.',
  })
  @ApiQuery({
    name: 'filter',
    required: false,
    description: 'Date filter: today (default), yesterday, thisWeek, thisMonth',
  })
  @ApiResponse({
    status: 200,
    description: 'Job application counts retrieved.',
  })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  async getInternalJobApplicationCounts(
    @Req() req: any,
    @Query('filter') filter: string,
  ) {
    const userId = req.user._id;
    return await this.jobApplicationFormService.getInternalJobApplicationCounts(
      userId,
      filter,
    );
  }

  // New endpoint to retrieve the count of job applications by stage for the current month and year
  @Get('count-by-stage-by-user')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager, Role.Vendor, Role.JobSeeker, Role.JobSeeker, Role.Freelancer, Role.JobSeeker)
  @Roles()
  @ApiOperation({
    summary:
      'Retrieve job application count by stage for the current month and year',
    description:
      'This endpoint returns the count of job applications in each stage for the current month and year. This is accessible for everyone',
  })
  @ApiQuery({
    name: 'filter',
    required: false,
    enum: ['today', 'yesterday', 'week', 'month'],
    description: 'Filter by time range (default: month)',
  })
  @ApiResponse({
    status: 200,
    description: 'Job application count by stage retrieved.',
  })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  // @ApiResponse({ status: 403, description: 'Forbidden.' })
  getJobApplicationsCountByStageForCurrentMonthAndYear(
    @Req() req: any,
    @Query('filter') filter?: string,
  ) {
    const userId = req.user._id;
    return this.jobApplicationFormService.getJobApplicationsCountByStageForCurrentMonthAndYear(
      userId,
      filter,
    );
  }

  @Get('count-by-stage')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager, Role.Vendor, Role.JobSeeker, Role.JobSeeker, Role.Freelancer, Role.JobSeeker)
  @Roles()
  @ApiOperation({
    summary:
      'Retrieve job application count by stage for the current month and year',
    description:
      'This endpoint returns the count of job applications in each stage for the current month and year. This is accessible for everyone',
  })
  @ApiResponse({
    status: 200,
    description: 'Job application count by stage retrieved.',
  })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  // @ApiResponse({ status: 403, description: 'Forbidden.' })
  getJobApplicationsCountByStageForCurrentMonthAndYearForOrg(@Req() req: any) {
    // const userId = req.user._id;
    const orgId = req.user.org?._id;
    if (!orgId) {
      throw new BadRequestException('Both userId and orgId are required.');
    }

    return this.jobApplicationFormService.getJobApplicationsCountByStageForCurrentMonthAndYearForOrg(
      orgId,
    );
  }

  @Get('past-months-job-stage-count')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager, Role.Vendor, Role.JobSeeker, Role.JobSeeker, Role.Freelancer, Role.JobSeeker)
  @Roles()
  @ApiOperation({
    summary:
      'Retrieve job application count by stage for the current month and year',
    description:
      'This endpoint returns the count of job applications in each stage for the current month and year. This is accessible for everyone',
  })
  @ApiResponse({
    status: 200,
    description: 'Job application count by stage retrieved.',
  })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  // @ApiResponse({ status: 403, description: 'Forbidden.' })
  getJobApplicationsCountByStageForPastSixMonths(@Req() req: any) {
    const userId = req.user._id;
    return this.jobApplicationFormService.getJobApplicationsCountByStageForPastSixMonths(
      userId,
    );
  }

  @Get('day-count-stage')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager, Role.Vendor, Role.JobSeeker, Role.JobSeeker, Role.Freelancer)
  @Roles()
  @ApiOperation({
    summary:
      'Retrieve job application count by stage for each day of the month',
    description:
      'This endpoint returns the count of job applications in each stage for each day of the selected month and year. This is accessible for everyone',
  })
  @ApiResponse({
    status: 200,
    description: 'Job application count by stage retrieved.',
  })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  // @ApiResponse({ status: 403, description: 'Forbidden.' })
  async getJobApplicationsCountByStageForEachDayInMonth(
    @Req() req: any,
    @Query('month') month: number = moment().month() + 1,
    @Query('year') year: number = moment().year(),
  ) {
    const userId = req.user._id;
    return this.jobApplicationFormService.getJobApplicationsCountByStageForMonthAndYear(
      userId,
      month,
      year,
    );
  }

  @Get('applicants-count-by-job')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager, Role.Vendor, Role.JobSeeker, Role.JobSeeker, Role.Freelancer)
  @Roles()
  @ApiOperation({
    summary: 'Get applicants count by job',
    description: `This endpoint returns the applicants count by job and fitler by (today, yesterday, thisWeek, thisMonth).  This is accessible for everyone.`,
  })
  @ApiResponse({ status: 200, description: 'Applicants count retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 404, description: 'Job application not found.' })
  @ApiQuery({
    name: 'filter',
    description:
      'Filter for applicants count by (today, yesterday, thisWeek, thisMonth)',
    required: true,
  })
  async findApplicantsCountByJob(
    @Req() req: any,
    @Query('filter') filter: string,
  ) {
    const userId = req.user._id;
    if (!req.user.org._id) {
      throw new BadRequestException('User does not have org id');
    }
    return this.jobApplicationFormService.getJobApplicantsCountByJob(
      filter,
      userId,
      req.user,
    );
  }

  @Get('applicants-count-by-internal-job')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager, Role.Vendor, Role.JobSeeker, Role.JobSeeker)
  @Roles()
  @ApiOperation({
    summary: 'Get applicants count by internal job',
    description: `This endpoint returns the applicants count by internal jobs and fitler by (today, yesterday, thisWeek, thisMonth).  This is accessible for everyone.`,
  })
  @ApiResponse({ status: 200, description: 'Applicants count retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 404, description: 'Job application not found.' })
  @ApiQuery({
    name: 'filter',
    description:
      'Filter for applicants count by (today, yesterday, thisWeek, thisMonth)',
    required: true,
  })
  async getInternalJobApplicantsCountByJob(@Query('filter') filter: string) {
    return this.jobApplicationFormService.getInternalJobApplicantsCountByJob(
      filter,
    );
  }

  @Post('public-route')
  @ApiResponse({ status: 201, description: 'Job application is saved.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Data. ' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  @ApiOperation({
    summary: 'Create a job application from public route',
    description: `This endpoint for creating a job application from job route. This is only accessible for everyone`,
  })
  async createJobApplication(
    @Body() createJobApplicationDto: CreatePublicJobApplicationFormDto,
  ) {
    return this.jobApplicationFormService.createJobApplication(
      createJobApplicationDto,
    );
  }

  @Get('monthly-stats')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager, Role.Vendor, Role.JobSeeker, Role.JobSeeker, Role.Freelancer)
  @Roles()
  @ApiOperation({
    summary: 'Get job applications progress',
    description: `This endpoint for getting job applications progress. This is accessible for everyone`,
  })
  async getJobApplicationsProgress(@Req() req: any) {
    const userId = req.user._id;
    return this.jobApplicationFormService.getJobApplicationsProgressAgainstTarget(
      userId,
    );
  }

  @Get('stages-summary')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.JobSeeker)
  @Roles()
  @ApiOperation({
    summary:
      'Retrieve job applications grouped by stages with endClientOrg titles',
    description:
      'Returns job applications grouped by their stages with corresponding end client organization names.',
  })
  @ApiResponse({
    status: 200,
    description: 'Stages summary retrieved successfully.',
  })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  async getJobApplicationsByStages(@Req() req: any) {
    return this.jobApplicationFormService.processJobApplications(req.user._id);
  }

  @Get('dashboard/performance-ratio')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Recruiter, Role.Freelancer, Role.Vendor)
  @Roles()
  @ApiOperation({
    summary: 'Retrieve performance ratio for recruiter and freelancer',
  })
  // @ApiQuery({ name: 'filter', enum: ['today', 'yesterday', 'week', 'month'], required: false, description: 'Filter data by time range (default: month)' })
  @ApiQuery({
    name: 'filter',
    required: false,
    description: 'Filter data for past months',
  })
  @ApiResponse({
    status: 200,
    description: 'Performance ratio retrieved successfully.',
  })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  // async getRecruiterPerformanceRatio(@Req() req: any, @Query('filter') filter?: string) {
  async getRecruiterPerformanceRatio(
    @Req() req: any,
    @Query('filter') filter?: number,
  ) {
    return this.jobApplicationFormService.getRecruiterPerformanceLast3Months(
      req.user,
      filter,
    );
  }

  @Get('dashboard/trend-analysis')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Recruiter, Role.Freelancer, Role.Vendor)
  @Roles()
  @ApiOperation({
    summary: 'Retrieve performance ratio for recruiter and freelancer',
  })
  @ApiQuery({
    name: 'filter',
    required: false,
    description: 'Filter data for past months',
  })
  @ApiResponse({
    status: 200,
    description: 'Performance ratio retrieved successfully.',
  })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  async getRecruitertrendAnalysis(
    @Req() req: any,
    @Query('filter') filter?: number,
  ) {
    return this.jobApplicationFormService.getTrendAnalysis(req.user, filter);
  }

  @Get('dashboard/performance-report')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Recruiter, Role.Freelancer, Role.Vendor)
  @Roles()
  @ApiOperation({
    summary: 'Retrieve performance ratio for recruiter and freelancer',
  })
  @ApiQuery({
    name: 'startDate',
    required: false,
    description: 'Start date for the performance report (ISO string)',
  })
  @ApiQuery({
    name: 'endDate',
    required: false,
    description: 'End date for the performance report (ISO string)',
  })
  @ApiResponse({
    status: 200,
    description: 'Performance ratio retrieved successfully.',
  })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  async getRecruiterPerformanceReport(
    @Req() req: any,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    return this.jobApplicationFormService.getRecruiterPerformanceReport(
      req.user,
      startDate,
      endDate,
    );
  }

  @Get('dashboard/monthly-stats')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Recruiter)
  @Roles()
  @ApiOperation({
    summary: 'Retrieve monthly submitted profiles vs target for recruiter',
  })
  @ApiResponse({
    status: 200,
    description: 'Monthly stats retrieved successfully.',
  })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  async getMonthlyStats(@Req() req: any) {
    //  Throw BadRequestException if orgId is missing
    if (!req.user.org._id) {
      throw new BadRequestException('Organization ID is required.');
    }
    const orgId = req.user?.org?._id;
    const userId = req.user?._id;
    // Fetch monthly submitted applications vs target
    return await this.jobApplicationFormService.getMonthlyStats(userId, orgId);
  }

  @Patch('bulk-soft-delete')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.BUHead, Role.AccountManager, Role.DeliveryManager, Role.ResourceManager, Role.Recruiter, Role.Vendor, Role.JobSeeker, Role.Freelancer)
  @Roles()
  @ApiOperation({
    summary: 'Bulk soft delete job applications',
    description:
      'Marks multiple job applications as deleted (soft delete) by setting isDeleted to true.',
  })
  @ApiResponse({
    status: 200,
    description: 'Job applications marked as deleted successfully.',
  })
  @ApiResponse({ status: 400, description: 'Invalid request parameters.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({
    status: 403,
    description:
      'Forbidden. Only allowed for BUHead, TeamLead, DeliveryManager, and Recruiter.',
  })
  @ApiQuery({
    name: 'jobApplicationIds',
    description: 'Array of job application IDs',
    required: true,
    type: [String],
  })
  async bulkSoftDelete(
    @Query('jobApplicationIds') jobApplicationIds: string[],
  ) {
    if (
      !jobApplicationIds ||
      !Array.isArray(jobApplicationIds) ||
      jobApplicationIds.length === 0
    ) {
      throw new BadRequestException(
        'jobApplicationIds must be a non-empty array.',
      );
    }

    return await this.jobApplicationFormService.softDeleteMany(
      jobApplicationIds,
    );
  }

  @Get('org-job-applications')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  @Roles()
  @ApiOperation({ summary: 'Retrieve all job applications of an organization' })
  @ApiResponse({ status: 200, description: 'List of job applications' })
  @ApiResponse({ status: 400, description: 'Invalid Organization ID' })
  @ApiQuery({
    name: 'groupBy',
    required: false,
    enum: GroupByType,
    description: 'Group by source or date',
  })
  async getJobApplications(
    @Req() req: any,
    @Query() filters: CustomFilterDto,
    @Query('groupBy') groupBy: string,
  ) {
    let orgId;
    if (req.user.org) {
      orgId = req.user.org._id;
    } else {
      throw new BadRequestException('User does not have org id');
    }
    return this.jobApplicationFormService.getJobApplicationsByOrg(
      orgId,
      filters,
      groupBy,
    );
  }

  @Get('group-by')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  @Roles()
  @ApiOperation({ summary: 'Group job applications by source or date' })
  @ApiResponse({ status: 200, description: 'Grouped job applications' })
  @ApiResponse({ status: 400, description: 'Invalid request parameters' })
  @ApiQuery({
    name: 'groupBy',
    required: true,
    enum: GroupByType,
    description: 'Group by source or date',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number for pagination (default: 1)',
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items per page (default: 10)',
    example: 10,
  })
  async getGroupedJobApplications(
    @Req() req: any,
    @Query('groupBy') groupBy: GroupByType,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
  ) {
    let orgId;
    if (req.user.org) {
      orgId = req.user.org._id;
    } else {
      throw new BadRequestException('User does not have org id');
    }

    if (!Object.values(GroupByType).includes(groupBy)) {
      throw new BadRequestException(
        "Invalid 'groupBy' value. Use 'source' or 'date'.",
      );
    }

    return this.jobApplicationFormService.groupJobApplications(
      orgId,
      groupBy,
      page,
      limit,
    );
  }
}
