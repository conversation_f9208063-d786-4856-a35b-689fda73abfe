import { ApiProperty } from "@nestjs/swagger";
import { Transform, TransformFnParams, Type } from 'class-transformer';
import { IsEnum, IsNotEmpty, IsOptional, IsString } from "class-validator";
import sanitizeWithStyle from 'sanitize-html'
import { StatusConfigType, StatusFunctionality } from "src/shared/constants";

export class CreateStatusDto {

    @ApiProperty({ type: String, description: 'Name of the status' })
    @IsNotEmpty()
    @IsString()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    name: string;

    @ApiProperty({ type: String, description: 'Description of the status' })
    @IsOptional()
    @IsString()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    description?: string;

    @ApiProperty({
        type: String,
        required: true,
        enum: StatusConfigType,
        description: 'Config status type',
    })
    @IsNotEmpty()
    @IsString()
    @IsEnum(StatusConfigType)
    statusType: StatusConfigType

    @ApiProperty({
        type: String,
        required: true,
        enum: StatusFunctionality,
        description: 'Status Functionality type',
    })
    @IsNotEmpty()
    @IsString()
    @IsEnum(StatusFunctionality)
    functionality: StatusFunctionality

}
