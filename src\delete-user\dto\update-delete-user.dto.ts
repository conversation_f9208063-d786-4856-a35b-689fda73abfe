import { ApiProperty, PartialType } from '@nestjs/swagger';
import { CreateDeleteUserDto } from './create-delete-user.dto';
import { IsEnum } from 'class-validator';
//export class UpdateDeleteUserDto extends PartialType(CreateDeleteUserDto) {

export class UpdateDeleteUserDto  {
    @ApiProperty({ enum: ['APPROVED', 'REJECTED', 'PENDING'] })
    @IsEnum(['APPROVED', 'REJECTED', 'PENDING'])
    status: 'APPROVED' | 'REJECTED' | 'PENDING';
}
