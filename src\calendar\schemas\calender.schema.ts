// src/calendar/schemas/calendar.schema.ts
import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { calendar } from 'googleapis/build/src/apis/calendar';
import { Document, HydratedDocument, Types } from 'mongoose';
import { JobApplication } from 'src/job-application-form/schemas/job-application.schema';
import { Org } from 'src/org/schemas/org.schema';
import { CalendarType } from 'src/shared/constants';
import { BasicUser } from 'src/user/schemas/basic-user.schema';

export type CalendarDocument = HydratedDocument<Calendar>;

@Schema({
    timestamps: true
})
export class Calendar {

    @Prop({
        type: Types.ObjectId,
        required: true,
        ref: 'JobApplication'
    })
    jobApplication: JobApplication;

    @Prop({ required: true, type: String })
    title: string;

    @Prop({ required: false, type: String })
    description: string;

    @Prop({ type: Date, required: true })
    startTime: Date;

    @Prop({ type: Date, required: false })
    endTime?: Date;

    @Prop({
        type: [String],
        required: false
    })
    attendees: string[]; // array of emails

    @Prop({
        type: String,
        required: true
    })
    meetLink: string;

    @Prop({
        type: String,
        required: false,
    })
    meetingCode?: string;

    @Prop({
        type: String,
        required: false,
    })
    zoomMeetingUUID ?: string;

    @Prop({ type: Types.ObjectId, ref: 'BasicUser', requried: true })
    createdBy: BasicUser;

    @Prop({
        type: String,
        required: false,
        trim: true,
        enum: Object.values(CalendarType),
    })
    calendarType?: string;

    @Prop({
        required: false,
        type: Types.ObjectId, ref: 'Org'
    })
    org?: Org;

    @Prop({
        type: String,
        required: false
    })
    recordingShareLink?: string;

    @Prop({
        type: String,
        required: false,
    })
    recordingPasscode?: string;

    @Prop({
        type: String,
        required: false,
    })
    videoUrl?: string;

    @Prop({
        type: String,
        required: false,
    })
    transcriptUrl?: string;

    @Prop({
        required: false,
        type: Boolean,
        default: false
    })
    isCancelled?: boolean;
}

export const CalendarSchema = SchemaFactory.createForClass(Calendar);