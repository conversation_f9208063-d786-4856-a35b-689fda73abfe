import { ApiHideProperty, ApiProperty } from "@nestjs/swagger";
import { IsISO8601, IsNotEmpty, IsOptional, IsString, Length, IsArray, IsEnum, IsMongoId } from "class-validator";
import { Transform, TransformFnParams } from "class-transformer";
import { sanitizeWithStyle } from "src/utils/sanitize-with-style";
import { CalendarType } from "src/shared/constants";

export class CreateCalendarDto {

    @ApiProperty({
        type: String,
        required: true,
    })
    @IsNotEmpty()
    @IsMongoId()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    jobApplication: string;

    @ApiProperty({ type: String, required: true, description: 'Meeting title' })
    @IsString()
    @IsNotEmpty()
    @Transform(({ value }: TransformFnParams) => sanitizeWithStyle(value))
    title: string;

    @ApiProperty({ type: String, required: false, description: 'Meeting description' })
    @IsString()
    @IsOptional()
    @Transform(({ value }: TransformFnParams) => sanitizeWithStyle(value))
    description?: string;

    @ApiProperty({ type: Date, required: true, description: 'Start time in UTC', example: '2025-04-10T09:00:00.000Z' })
    @IsISO8601()
    startTime: string;

    @ApiProperty({ type: Date, required: false, description: 'End time in UTC', example: '2025-04-10T10:00:00.000Z' })
    @IsOptional()
    @IsISO8601()
    endTime?: string;

    @ApiProperty({
        description: 'List of attendee emails',
        example: ['<EMAIL>', '<EMAIL>'],
        required: false,
        type: [String],
    })
    @IsArray()
    @IsString({ each: true })
    @IsOptional()
    attendees?: string[];

    @ApiHideProperty()
    @IsOptional()
    createdBy?: string;

    @ApiProperty({
        enum: CalendarType,
        description: 'Type of calendar integration',
        example: CalendarType.GoogleMeet,
    })
    @IsOptional()
    @IsEnum(CalendarType)
    calendarType?: CalendarType;

    @ApiHideProperty()
    @IsOptional()
    org?: string;

    @ApiProperty({
        description: 'The meeting link for virtual meetings',
        example: 'https://zoom.us/j/1234567890',
        required: false,
    })
    @IsString()
    @IsOptional()
    meetLink?: string;

    @ApiProperty({
        description: 'The meeting code for joining physical or virtual meetings',
        example: 'XYZ123',
        required: false,
    })
    @IsString()
    @IsOptional()
    meetingCode?: string;
}