import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsOptional, IsString, IsArray, IsNotEmpty, IsMongoId } from 'class-validator';
import { sanitizeWithStyle } from "src/utils/sanitize-with-style";
import { Transform, TransformFnParams } from 'class-transformer';
import { FileMetadata } from 'src/file-upload/schemas/file-metadata.schema';

export class ForwardEmailDto {

    @ApiProperty({
        type: String,
        required: false,
        description: 'Id for the email connection.',
    })
    @IsOptional()
    @IsMongoId()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    userInboxConfigId: string;  // ID of the user's inbox configuration

    @ApiProperty({
        type: String,
        required: true,
        description: 'UID of the email.',
    })
    @IsNotEmpty()
    @IsString()
    uid: string;  // UID of the email being replied to

    @ApiProperty({
        type: String,
        required: true,
        description: 'Folders.',
    })
    @IsNotEmpty()
    @IsString()
    folder: string;

    @ApiProperty({
        type: [String],
        required: true,
        description: 'Whom email is forwarding.',
    })
    @IsNotEmpty()
    @IsArray()
    @IsEmail({}, { each: true })
    @Transform((params: TransformFnParams) => params.value?.map((email: string) => sanitizeWithStyle(email).toLowerCase()))
    to: string[];  // List of recipients for forwarding


    @ApiProperty({
        type: String,
        required: true,
        description: 'Body of the email.',
    })
    @IsString()
    body: string;

    @ApiProperty({
        type: String,
        required: false,
        description: 'Subject of the reply email.',
      })
      @IsOptional()
      @IsString()
      subject: string;  // Optional custom subject for the reply email


    @ApiProperty({
        type:[String],
        required: false,
        description: 'Other reciptents.',
    })
    @IsOptional()
    @IsArray()
    @IsEmail({}, { each: true })
    @Transform((params: TransformFnParams) => params.value?.map((email: string) => sanitizeWithStyle(email).toLowerCase()))
    cc?: string[];

    @ApiProperty({
        type: [String],
        required: false,
        description: 'Other reciptents.',
    })
    @IsOptional()
    @IsArray()
    @IsEmail({}, { each: true })
    @Transform((params: TransformFnParams) => params.value?.map((email: string) => sanitizeWithStyle(email).toLowerCase()))
    bcc?: string[];

    @ApiProperty({
        description: 'Array of attachments',
        type: [FileMetadata],
        required: false,
        example: [
            { filename: 'doc.pdf', contentType: 'application/pdf', content: 'base64string...' },
            { originalName: 'image.png', locationUrl: 'http://server.com/image.png', fileType: 'image/png' },
        ]
    })
    @IsArray()
    @IsOptional()
    attachments?: (FileMetadata | { filename: string, content: string, contentType: string })[];
    
}
