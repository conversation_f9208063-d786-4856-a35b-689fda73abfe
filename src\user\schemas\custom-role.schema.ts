
import { Prop, Schema } from '@nestjs/mongoose';
import validator from 'validator';
import { HydratedDocument } from 'mongoose';
import { SchemaFactory } from '@nestjs/mongoose';

import { BasicUser } from 'src/user/schemas/basic-user.schema';

export type CustomRoleDocument = HydratedDocument<CustomRole>;
// export interface CustomRole {
//     name: string;
//     rules: Rule[];
//   }


export interface Permission {
    action: string;
    subject: string;
    effect: 'allow' | 'deny';
  }

@Schema({
    timestamps: true
})
export class CustomRole {

    @Prop({
        required: [true, 'NAME_CANNOT_BE_BLANK'],
        lowercase: true,
    })
    name: string;

    @Prop({
        required: true,
    })
    createdBy: BasicUser;

    @Prop({
        required: true,
        // type: [Permission]
    })
    permissions: Permission[];
}

export const CustomRoleSchema = SchemaFactory.createForClass(CustomRole);


// import { Schema, Document } from 'mongoose';
// import { Prop,  SchemaFactory } from '@nestjs/mongoose';
// import { HydratedDocument, Types } from 'mongoose';
// import { User } from '../entities/user.entity';
// export interface CustomRole {
//   name: string;
//   rules: Rule[];
// }

// export interface Rule {
//   action: string;
//   subject: string;
//   effect: 'allow' | 'deny';
// }

// // export interface User {
// //   name: string;
// //   roles: Role[];
// // }

// export const roleSchema = new Schema<CustomRole>({
//   name: { type: String, required: true },
//   rules: [{ type: Schema.Types.Mixed, required: true }],
// });

// // export const userSchema = new Schema<User>({
// //   name: { type: String, required: true },
// //   roles: [{ type: Schema.Types.ObjectId, ref: 'Role' }],
// // });
// export const CustomRoleSchema = SchemaFactory.createForClass(BasicUser);
