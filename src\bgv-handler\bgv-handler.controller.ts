import { Body, Controller, Delete, Get, Param, Patch, Post, Query, Req, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { BgvHandlerService } from './bgv-handler.service';
import { CreateBgvHandlerDto } from './dto/create-bgv-handler.dto';
import { UpdateBgvHandlerDto } from './dto/update-bgv-handler.dto';
import { Roles } from 'src/auth/decorators/roles.decorator';
import { AuthJwtGuard } from 'src/auth/guards/auth-jwt/auth-jwt.guard';
import { RolesGuard } from 'src/auth/guards/roles/roles.guard';
import { REQUIRED_BGV_DOCUMENTS } from 'src/shared/constants';
import { OffersQueryDTO } from './dto/query-offers.dto';
import { CreateBgvDto } from 'src/offer/dto/create-bgv.dto';
import { validateObjectId } from 'src/utils/validation.utils';
import { BgvHandlerQueryDto } from './dto/query-all.dto';

@Controller('')
@ApiTags('bgv-handler')
export class BgvHandlerController {
    constructor(private readonly bgvHandlerService: BgvHandlerService) { }

    //   @Post()
    //   create(@Body() dto: CreateBgvHandlerDto) {
    //     return this.service.create(dto);
    //   }

    @Post()
    @ApiBearerAuth()
    @UseGuards(AuthJwtGuard)
    // @Roles(Role.BUHead, Role.TeamLead, Role.DeliveryManager, Role.Recruiter, Role.Vendor)
    @Roles()
    @ApiOperation({ summary: 'Create a Bgv handler', description: `This endpoint for creating a job application. This is only accessible for "BUHead", "TeamLead", "DeliveryManager" and "Recruiter".` })
    create(@Req() req: any, @Body() createBgvHandlerDto: CreateBgvHandlerDto) {
        return this.bgvHandlerService.create(req.user, createBgvHandlerDto);
    }

    @Get('all')
    @ApiBearerAuth()
    @UseGuards(AuthJwtGuard)
    @Roles()
    findAll(@Req() req: any, @Query() query: BgvHandlerQueryDto) {
        return this.bgvHandlerService.findAll(req.user, query);
    }

    @Get(':id')
    @ApiBearerAuth()
    @UseGuards(AuthJwtGuard)
    @Roles()
    findOne(@Req() req: any, @Param('id') id: string) {
        return this.bgvHandlerService.findOne(req.user, id);
    }

    @Patch(':id')
    @ApiBearerAuth()
    @UseGuards(AuthJwtGuard)
    @Roles()
    update(@Req() req: any, @Param('id') id: string, @Body() dto: UpdateBgvHandlerDto) {
        return this.bgvHandlerService.update(req.user, id, dto);
    }

    @Delete(':id')
    @ApiBearerAuth()
    @UseGuards(AuthJwtGuard)
    @Roles()
    remove(@Req() req: any, @Param('id') id: string) {
        return this.bgvHandlerService.remove(req.user, id);
    }

    @Get('/document-keys')
    getBgvDocumentKeys() {
        return { keys: REQUIRED_BGV_DOCUMENTS };
    }

    @Get('pending-onboardings')
    @ApiOperation({
        summary: 'Retrieve all onboardings.',
        description: `This endpoint retrieves a list of all onboardings . This is accessible only for everyone.`
    })
    @ApiBearerAuth()
    @UseGuards(AuthJwtGuard)
    // @Roles(Role.Admin, Role.Recruiter, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager)
    @Roles()
    @ApiResponse({ status: 200, description: 'All offers are retrieved.' })
    @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
    // @ApiResponse({ status: 403, description: `Forbidden. Only users with roles "${Role.BUHead}", "${Role.ResourceManager}", "${Role.DeliveryManager}"."${Role.TeamLead}, and "${Role.AccountManager}" are permitted to use this endpoint.` })
    getAllOnboardings(@Req() req: any, @Query() query: OffersQueryDTO) {
        return this.bgvHandlerService.getAllPendingBgvs(req.user, query);
    }

    @Post('upload-interim-bgv')
    @ApiOperation({
        summary: 'Save BGV Documents',
        description: `This endpoint allows you to Save BGV Documents.`
    })
    @ApiBearerAuth()
    @UseGuards(AuthJwtGuard)
    // @Roles(Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager)
    @Roles()
    @ApiResponse({ status: 201, description: 'The offer is created.' })
    @ApiResponse({ status: 400, description: 'Bad Request / Data.' })
    @ApiResponse({ status: 401, description: 'Unauthorized access. Authentication is required.' })
    saveInterimBgv(@Req() req: any, @Body() createBgvDto: CreateBgvDto) {
        return this.bgvHandlerService.saveInterimBgv(req.user, createBgvDto);
    }

    @Patch(':applicationId/submit-interimBgv-Report')
    @ApiOperation({
        summary: 'Retrieve an bgv documents by Application Id',
        description: 'This endpoint retrieves an bgv documents by its Application Id. This is accessible only for everyone..'
    })
    @ApiBearerAuth()
    @UseGuards(AuthJwtGuard)
    // @Roles(Role.Admin, Role.Recruiter, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager)
    @Roles()
    @ApiResponse({ status: 200, description: 'Offer retrieved.' })
    @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
    @ApiResponse({ status: 404, description: 'Offer not found.' })
    submitInterimBgvReports(@Param('applicationId') applicationId: string) {
        const objId = validateObjectId(applicationId);
        return this.bgvHandlerService.submitIntrimBgv(objId);
    }

    @Get(':applicationId/interim-bgv-report')
    @ApiOperation({
        summary: 'Retrieve an bgv documents by Application Id',
        description: 'This endpoint retrieves an bgv documents by its Application Id. This is accessible only for everyone..'
    })
    @ApiBearerAuth()
    @UseGuards(AuthJwtGuard)
    // @Roles(Role.Admin, Role.Recruiter, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager)
    @Roles()
    @ApiResponse({ status: 200, description: 'Offer retrieved.' })
    @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
    @ApiResponse({ status: 404, description: 'Offer not found.' })
    findBgvDocumentsByCandidateId(@Param('applicationId') applicationId: string) {
        const objId = validateObjectId(applicationId);
        return this.bgvHandlerService.findBgvDocumentsByCandidateId(objId);
    }
}
