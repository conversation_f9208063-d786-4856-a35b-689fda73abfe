import { Modu<PERSON> } from '@nestjs/common';
import { EvaluationFormService } from './evaluation-form.service';
import { EvaluationFormController } from './evaluation-form.controller';
import { ConfigModule } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { MongooseModule } from '@nestjs/mongoose';
import { EvaluationForm, EvaluationFormSchema } from './schemas/evaluation-form.schema';
import { EndpointsRolesModule } from 'src/endpoints-roles/endpoints-roles.module';
@Module({
  imports: [
    ConfigModule,
    JwtModule, EndpointsRolesModule,
    MongooseModule.forFeature([{ name: EvaluationForm.name, schema: EvaluationFormSchema}])
  ],
  controllers: [EvaluationFormController],
  providers: [EvaluationFormService],
})
export class EvaluationFormModule {}
