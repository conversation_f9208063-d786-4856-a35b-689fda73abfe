import { ApiProperty } from "@nestjs/swagger";
import { Transform, TransformFnParams } from "class-transformer";
import { IsEnum, IsMongoId, IsOptional, IsString } from "class-validator";
import { sanitizeWithStyle } from "src/utils/sanitize-with-style";
import { Role } from "src/auth/enums/role.enum";

export class OrgUserDto {


    @ApiProperty({ 
      type: String, 
      required: true, 
      description: 'User ID' 
    })
    @IsMongoId()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    userId: string;

    
    @ApiProperty({
    type: String,
    required: false,
    // default: Role.User,
    enum: Role,
    description: "Role of user"
    })
    @IsEnum(Role)
    @IsOptional()
    role?: string;
    
}
