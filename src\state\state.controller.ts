import { Controller, Get, Post, Body, Patch, Param, Delete, Req, UseGuards, Query } from '@nestjs/common';
import { StateService } from './state.service';
import { ApiOperation, ApiBearerAuth, ApiResponse, ApiParam, ApiTags, ApiQuery } from '@nestjs/swagger';
import { Roles } from 'src/auth/decorators/roles.decorator';
import { Role } from 'src/auth/enums/role.enum';
import { AuthJwtGuard } from 'src/auth/guards/auth-jwt/auth-jwt.guard';
import { RolesGuard } from 'src/auth/guards/roles/roles.guard';
import { CreateStateDto } from 'src/state/dto/create-state.dto';
import { UpdateStateDto } from 'src/state/dto/update-state.dto';
import { validateObjectId } from 'src/utils/validation.utils';

@Controller('')
@ApiTags('States')
export class StateController {
  constructor(private readonly stateService: StateService) { }


  // @Post('seed')
  // @ApiOperation({ summary: 'Seed states from JSON file with country Id', description: 'Seeds states into the database from a predefined JSON file with country Id .' })
  // @ApiBearerAuth()
  // @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.SuperAdmin, Role.Admin)
  // @ApiResponse({ status: 201, description: 'States seeded successfully.' })
  // @ApiResponse({ status: 400, description: 'Bad Request / Data ' })
  // @ApiResponse({ status: 401, description: 'Unauthorized ' })
  // @ApiResponse({ status: 403, description: 'Forbidden, user with role super admin and admin can only use this end point.' })
  // @ApiParam({ name: 'countryId', description: `Id of the country.` })
  // seedStates(@Param('countryId') countryId: string) {
  //   const objId = validateObjectId(countryId);
  //   return this.stateService.seedStatesFromFile(objId);
  // }

  @Post()
  @ApiOperation({
    summary: 'Creates a new state',
    description: `This endpoint allows you to create a new state. This is accessible only for "${Role.SuperAdmin}", "${Role.Admin}".`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.SuperAdmin, Role.Admin)
  @Roles()
  @ApiResponse({ status: 201, description: 'State is created.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Data' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role super admin and admin can only use this endpoint.' })
  @ApiQuery({ name: 'countryId', description: 'ID of the country', required: true })
  create(@Body() createStateDto: CreateStateDto, @Query('countryId') countryId: string) {
    const objId = validateObjectId(countryId);
    return this.stateService.create(createStateDto, objId);
  }

  @Get()
  @ApiOperation({
    summary: 'Retrieve all states by country ID',
    description: 'This endpoint retrieves a list of all states associated with a specific country ID. This endpoint is accessible by everyone.'
  })
  @ApiQuery({ name: 'countryId', required: true, description: 'ID of the country' })
  @ApiResponse({ status: 200, description: 'List of states retrieved successfully.' })
  @ApiResponse({ status: 400, description: 'Bad Request. Invalid or missing country ID.' })
  @ApiResponse({ status: 404, description: 'Not Found. No states found for the given country ID.' })
  findAll(@Query('countryId') countryId: string) {
    const objId = validateObjectId(countryId);
    return this.stateService.findAll(objId);
  }

  // if admin wants to see list of records include deleting records 
  @Get('all')
  @ApiOperation({
    summary: 'Retrieve all states by country Id including deleted states',
    description: `This endpoint returns a list of all states, including deleted states. This is accessible only for admins.`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin)
  @Roles()
  @ApiResponse({ status: 200, description: 'All states, including deleted states, are retrieved successfully.' })
  @ApiResponse({ status: 400, description: 'Bad Request. Invalid or missing country ID.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. You must be logged in to access this endpoint.' })
  @ApiResponse({ status: 403, description: 'Forbidden. Only admins have access to this endpoint.' })
  @ApiResponse({ status: 404, description: 'Not Found. No states found for the given country ID.' })
  @ApiQuery({ name: 'countryId', required: true, description: 'ID of the country' })
  getAllStates(@Query('countryId') countryId: string) {
    const objId = validateObjectId(countryId);
    return this.stateService.getAllStates(objId);
  }

  @Get('find-all-soft-deleted')
  @ApiOperation({
    summary: 'Retrieve all soft-deleted states',
    description: `This endpoint returns a list of all soft-deleted states. This is accessible only for "${Role.SuperAdmin}" and "${Role.Admin}".`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.SuperAdmin, Role.Admin)
  @Roles()
  @ApiResponse({ status: 200, description: 'All soft-deleted states are retrieved successfully.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. You need to be logged in to access this endpoint.' })
  @ApiResponse({ status: 403, description: 'Forbidden. Only users with roles "${Role.SuperAdmin}" and "${Role.Admin}" can access this endpoint.' })
  @ApiResponse({ status: 404, description: 'Not Found. No soft-deleted states found for the given country ID.' })
  @ApiQuery({ name: 'countryId', required: true, description: 'ID of the country' })
  getAllSoftDeletedStates(@Query('countryId') countryId: string) {
    const objId = validateObjectId(countryId);
    return this.stateService.getAllSoftDeletedStates(objId);
  }

  @Get(':stateId')
  @ApiOperation({
    summary: 'Retrieve a state by Id',
    description: 'This endpoint returns a state by its Id. This endpoint is accessible by everyone.'
  })
  @ApiResponse({ status: 200, description: 'State is retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 404, description: 'State not found.' })
  @ApiParam({ name: 'stateId', description: 'ID of the state.' })
  findOne(@Param('stateId') stateId: string) {
    const objId = validateObjectId(stateId);
    return this.stateService.findById(objId);
  }

  @Post('search')
  @ApiOperation({
    summary: 'Search for state',
    description: `This endpoint allows you to search for a state using the countryId and stateName as query parameters. This is accessible by everyone.`
  })
  @ApiResponse({ status: 201, description: 'State found.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Invalid Data.' })
  @ApiQuery({ name: 'countryId', required: true, description: 'ID of the country' })
  @ApiQuery({ name: 'stateName', required: true, description: 'Name of the state' })
  search(@Query('stateName') stateName: string, @Query('countryId') countryId: string) {
    const objId = validateObjectId(countryId);
    return this.stateService.searchByName(stateName, objId);
  }

  @Patch(':stateId')
  @ApiOperation({
    summary: 'Update a state by ID',
    description: `This endpoint updates a state by ID. Accessible only for "${Role.SuperAdmin}" and "${Role.Admin}".`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.SuperAdmin, Role.Admin)
  @Roles()
  @ApiResponse({ status: 200, description: 'State is updated.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  @ApiResponse({ status: 404, description: 'State not found.' })
  @ApiParam({ name: 'stateId', description: 'ID of the state.' })
  async update(@Param('stateId') stateId: string, @Body() updateStateDto: UpdateStateDto) {
    const objId = validateObjectId(stateId);
    return this.stateService.update(objId, updateStateDto);
  }

  @Patch(':stateId/restore')
  @ApiOperation({
    summary: 'Restore soft-deleted state by ID.',
    description: `This endpoint restores a soft-deleted state by ID. This is accessible for "${Role.SuperAdmin}" and "${Role.Admin}".`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.SuperAdmin)
  @Roles()
  @ApiResponse({ status: 200, description: 'State updated (restored).' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role super admin or admin can use this endpoint.' })
  @ApiParam({ name: 'stateId', description: 'ID of the State.' })
  async restoreSoftDeletedState(@Param('stateId') stateId: string) {
    const objId = validateObjectId(stateId);
    return await this.stateService.restoreSoftDeletedState(objId);
  }

  // @Patch('restore')
  // @ApiOperation({ summary: 'Restore all soft deleted countries', description: `This endpoint restore all soft deleted countries. This is accessible only for "${Role.SuperAdmin}" and "${Role.Admin}"` })
  // @ApiBearerAuth()
  // @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.SuperAdmin, Role.Admin)
  // @ApiResponse({ status: 201, description: 'All countries are restored.' })
  // @ApiResponse({ status: 401, description: 'Unauthorized ' })
  // @ApiResponse({ status: 403, description: 'Forbidden' })
  // @ApiResponse({ status: 404, description: 'State not found.' })
  // @ApiParam({ name: 'countryId', description: `Id of the country.` })
  // restore(@Param('countryId') countryId: string) {
  //   const objId = validateObjectId(countryId);
  //   return this.stateService.restore(objId);
  // }

  // @Delete(':stateId/hard-delete')
  // @ApiOperation({ summary: 'Permannently delete a state by Id', description: `This endpoint permanently deletes a state by Id. This is accessible only for "${Role.SuperAdmin}" and "${Role.Admin}"`})
  // @ApiBearerAuth()	
  // @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles( Role.SuperAdmin, Role.Admin )
  // @ApiResponse({ status: 200, description: 'State deleted permanently.'})
  // @ApiResponse({ status: 401, description: 'Unauthorized '})
  // @ApiResponse({ status: 403, description: 'Forbidden' })
  // @ApiResponse({ status: 404, description: 'State not found.'})
  // @ApiParam({ name: 'stateId', description: 'ID of the state'})
  // remove(@Param('stateId') stateId: string) {
  //   const objId = validateObjectId(stateId);
  //   return this.stateService.hardDelete(objId);
  // }

  @Delete(':stateId/soft-delete')
  @ApiOperation({ summary: 'Soft delete a state by Id', description: `This endpoint soft deletes a state by Id. This is accessible only for "${Role.SuperAdmin}" and "${Role.Admin}"` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.SuperAdmin, Role.Admin)
  @Roles()
  @ApiResponse({ status: 200, description: 'The state record has been soft deleted.' })
  @ApiResponse({ status: 401, description: 'Unauthorized ' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @ApiResponse({ status: 404, description: 'State not found.' })
  @ApiParam({ name: 'stateId', description: 'ID of the state' })
  delete(@Param('stateId') stateId: string) {
    const objId = validateObjectId(stateId);
    return this.stateService.softDelete(objId);
  }

  // @Delete('delete-all')
  // @ApiOperation({ summary: 'Hard delete all countries', description: `This endpoint hard deletes all countries. This is accessible only for "${Role.SuperAdmin}" and "${Role.Admin}"` })
  // @ApiBearerAuth()	
  // @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles( Role.SuperAdmin, Role.Admin )
  // @ApiResponse({ status: 200, description: 'All countries are deleted.'})
  // @ApiResponse({ status: 401, description: 'Unauthorized '})
  // @ApiResponse({ status: 403, description: 'Forbidden' })
  // @ApiResponse({ status: 404, description: 'States not found.'})
  // deleteAll() {
  //   return this.stateService.hardDeleteAllStates();
  // }

  @Get(':stateId/cities')
  @ApiOperation({
    summary: 'Retrieve cities based on state ID',
    description: 'This endpoint retrieves a list of cities based on the given state ID. This is accessible to everyone.'
  })
  @ApiResponse({ status: 200, description: 'Cities retrieved successfully.' })
  @ApiResponse({ status: 400, description: 'Bad request.' })
  @ApiResponse({ status: 404, description: 'State not found.' })
  @ApiParam({ name: 'stateId', description: 'ID of the state to retrieve cities for.' })
  async getCitiesByState(@Param('stateId') stateId: string) {
    const objId = validateObjectId(stateId);
    return this.stateService.getCitiesByState(objId);
  }

}
