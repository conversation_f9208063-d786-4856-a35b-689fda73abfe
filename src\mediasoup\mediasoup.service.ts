// import { Injectable, OnModuleInit } from '@nestjs/common';
// import * as mediasoup from 'mediasoup';
// import { Worker, Router, Producer, Consumer, RtpCodecCapability, MediaKind, RtpParameters } from 'mediasoup/node/lib/types';
// import { DtlsParameters, WebRtcTransport } from 'mediasoup/node/lib/WebRtcTransportTypes';

// import { config } from './mediasoup.config';

// interface Room {
//     id: string;
//     router: Router;
//     peers: Map<string, Peer>;
// }

// interface Peer {
//     id: string;
//     transports: Map<string, WebRtcTransport>;
//     producers: Map<string, Producer>;
//     consumers: Map<string, Consumer>;
// }

// interface ProduceParams {
//     kind: MediaKind;  // Using the proper MediaKind type
//     rtpParameters: RtpParameters;
// }

// @Injectable()
// export class MediasoupService implements OnModuleInit {
//     private workers: mediasoup.types.Worker[] = [];
//     private nextWorkerIndex = 0;
//     private rooms: Map<string, Room> = new Map();

//     async onModuleInit() {
//         await this.createWorkers();
//     }

//     private async createWorkers() {
//         const { numWorkers } = config.worker;

//         for (let i = 0; i < numWorkers; i++) {
//             const worker = await mediasoup.createWorker({
//                 logLevel: config.worker.logLevel,
//                 logTags: config.worker.logTags,
//                 rtcMinPort: config.worker.rtcMinPort,
//                 rtcMaxPort: config.worker.rtcMaxPort,
//             });

//             worker.on('died', () => {
//                 console.error(`Worker ${worker.pid} died, exiting in 2 seconds... [pid:${process.pid}]`);
//                 setTimeout(() => process.exit(1), 2000);
//             });

//             this.workers.push(worker);
//         }
//     }

//     private getNextWorker(): mediasoup.types.Worker {
//         const worker = this.workers[this.nextWorkerIndex];
//         this.nextWorkerIndex = (this.nextWorkerIndex + 1) % this.workers.length;
//         return worker;
//     }

//     async createRoom(roomId: string): Promise<Room> {
//         const existingRoom = this.rooms.get(roomId);
//         if (existingRoom) {
//             return existingRoom;
//         }

//         const worker = this.getNextWorker();
//         const mediaCodecs: RtpCodecCapability[] = config.router.mediaCodecs;
//         const router = await worker.createRouter({ mediaCodecs });

//         const room: Room = {
//             id: roomId,
//             router,
//             peers: new Map()
//         };

//         this.rooms.set(roomId, room);
//         return room;
//     }

//     async createWebRtcTransport(roomId: string, peerId: string) {
//         const room = this.rooms.get(roomId);
//         if (!room) {
//             throw new Error(`Room ${roomId} does not exist`);
//         }

//         const transport = await room.router.createWebRtcTransport({
//             ...config.webRtcTransport,
//             enableUdp: true,
//             enableTcp: true,
//             preferUdp: true,
//         });

//         transport.on('dtlsstatechange', (dtlsState) => {
//             if (dtlsState === 'closed') {
//                 transport.close();
//             }
//         });

//         let peer = room.peers.get(peerId);
//         if (!peer) {
//             peer = {
//                 id: peerId,
//                 transports: new Map(),
//                 producers: new Map(),
//                 consumers: new Map()
//             };
//             room.peers.set(peerId, peer);
//         }
//         peer.transports.set(transport.id, transport);

//         return {
//             id: transport.id,
//             iceParameters: transport.iceParameters,
//             iceCandidates: transport.iceCandidates,
//             dtlsParameters: transport.dtlsParameters,
//             sctpParameters: transport.sctpParameters,
//         };
//     }

//     async connectTransport(roomId: string, transportId: string, dtlsParameters: DtlsParameters) {
//         const room = this.rooms.get(roomId);
//         if (!room) {
//             throw new Error(`Room ${roomId} does not exist`);
//         }

//         let foundTransport: WebRtcTransport | undefined;
//         for (const peer of room.peers.values()) {
//             const transport = peer.transports.get(transportId);
//             if (transport) {
//                 foundTransport = transport;
//                 break;
//             }
//         }

//         if (!foundTransport) {
//             throw new Error(`Transport ${transportId} does not exist`);
//         }

//         await foundTransport.connect({ dtlsParameters });
//     }

//     async produce(roomId: string, transportId: string, produceParams: ProduceParams) {
//         const room = this.rooms.get(roomId);
//         if (!room) {
//             throw new Error(`Room ${roomId} does not exist`);
//         }

//         let foundTransport: WebRtcTransport | undefined;
//         let foundPeer: Peer | undefined;
//         for (const peer of room.peers.values()) {
//             const transport = peer.transports.get(transportId);
//             if (transport) {
//                 foundTransport = transport;
//                 foundPeer = peer;
//                 break;
//             }
//         }

//         if (!foundTransport || !foundPeer) {
//             throw new Error(`Transport ${transportId} does not exist`);
//         }

//         const producer = await foundTransport.produce({
//             kind: produceParams.kind,
//             rtpParameters: produceParams.rtpParameters,
//         });

//         producer.on('transportclose', () => {
//             producer.close();
//         });

//         foundPeer.producers.set(producer.id, producer);

//         return producer;
//     }

//     async consume(roomId: string, transportId: string, producerId: string, rtpCapabilities: mediasoup.types.RtpCapabilities) {
//         const room = this.rooms.get(roomId);
//         if (!room) {
//             throw new Error(`Room ${roomId} does not exist`);
//         }

//         let foundTransport: WebRtcTransport | undefined;
//         let foundPeer: Peer | undefined;
//         for (const peer of room.peers.values()) {
//             const transport = peer.transports.get(transportId);
//             if (transport) {
//                 foundTransport = transport;
//                 foundPeer = peer;
//                 break;
//             }
//         }

//         if (!foundTransport || !foundPeer) {
//             throw new Error(`Transport ${transportId} does not exist`);
//         }

//         if (!room.router.canConsume({
//             producerId: producerId,
//             rtpCapabilities,
//         })) {
//             throw new Error('Cannot consume');
//         }

//         const consumer = await foundTransport.consume({
//             producerId: producerId,
//             rtpCapabilities,
//             paused: true,
//         });

//         consumer.on('transportclose', () => {
//             consumer.close();
//         });

//         foundPeer.consumers.set(consumer.id, consumer);

//         return {
//             id: consumer.id,
//             producerId: producerId,
//             kind: consumer.kind,
//             rtpParameters: consumer.rtpParameters,
//             type: consumer.type,
//         };
//     }
// }

// // Creates a Mediasoup Worker (manages media processing).
// // Defines a Router for handling WebRTC streams.
// // Configures media codecs (Opus for audio, VP8 for video).