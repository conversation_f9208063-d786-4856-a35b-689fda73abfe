import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsNotEmpty, ValidateNested, IsArray } from 'class-validator';
import { Type } from 'class-transformer';

export class RateCardOtherFieldDto {
//   @ApiProperty({ example: 'billingCurrency', description: 'Key for the dynamic field' })
//   @IsString()
//   @IsNotEmpty()
//   key: string;

  @ApiProperty({ example: 'Billing Currency', description: 'Label for the dynamic field' })
  @IsString()
  @IsNotEmpty()
  @IsOptional()
  label: string;

  @ApiProperty({ example: 'USD', description: 'Value for the dynamic field' })
  @IsNotEmpty()
  @IsOptional()
  value: any;

  @ApiProperty({ example: 'Currency used for billing', description: 'Description of the field' })
  @IsOptional()
  @IsString()
  description?: string;
}
