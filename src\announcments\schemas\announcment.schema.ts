
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Types } from 'mongoose';
import { FileMetadata } from 'src/file-upload/schemas/file-metadata.schema';
import { JobApplication } from 'src/job-application-form/schemas/job-application.schema';
import { Job } from 'src/job/schemas/job.schema';
import { Org } from 'src/org/schemas/org.schema';
import { BasicUser } from 'src/user/schemas/basic-user.schema';

export type AnnouncmentDocument = HydratedDocument<Announcments>;


@Schema({ timestamps: true })
export class Announcments {

    @Prop({ type: Types.ObjectId, ref: 'Org' })
    org?: Org;

    @Prop({ type: Types.ObjectId, ref: 'Job' })
    job?: Job;

    @Prop({
        type: String,
        required: false,
        trim: true,
    })
    title?: string;

    @Prop({
        type: String,
        required: false,
        trim: true,
    })
    description?: string;

    @Prop({
        required: true,
        type: Types.ObjectId, ref: 'BasicUser'
    })
    createdBy: BasicUser;

    @Prop({
        type: Boolean,
        required: false,
        trim: true,
        default: false,
    })
    isDeleted?: boolean;

    @Prop({
        type: [Types.ObjectId],
        required: false,
        ref: 'BasicUser',
      })
      assignTo?: BasicUser[];

}

export const AnnouncmentsSchema = SchemaFactory.createForClass(Announcments);