import { SchemaFactory, Prop, Schema } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';
import { BasicUser } from 'src/user/schemas/basic-user.schema';  // Import the BasicUser schema

export type UserTempDocument = HydratedDocument<UserTemp>;

@Schema({ collection: 'user_temp', timestamps: true }) // Specify a custom collection name
export class UserTemp extends BasicUser {
  @Prop({ default: true })
  isTemp?: boolean; // Additional property to differentiate temp users
}

export const UserTempSchema = SchemaFactory.createForClass(UserTemp);
