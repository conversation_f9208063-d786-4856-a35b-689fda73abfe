import { Optional } from '@nestjs/common';
import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsDate, IsOptional, IsString } from 'class-validator';

export class QueryDTO {
  @IsOptional()
  @Transform(({ value }) => {
    return value === undefined || value === '0' ? '' : value;
  })
  contactId?: string;

  @IsOptional()
  @Transform(({ value }) => {
    return value === undefined || value === '0' ? '' : value;
  })
  orgId?: string;

  @IsOptional()
  @Transform(({ value }) => {
    return value === undefined || value === '0' ? '' : value;
  })
  taskId?: string;

  @IsOptional()
  @Transform(({ value }) => {
    return value === undefined || value === '0' ? '' : value;
  })
  regionId?: string;

  @IsOptional()
  @Transform(({ value }) => {
    return value === undefined || value === '0' ? '' : value;
  })
  businessUnitId?: string;

  @IsOptional()
  @Transform(({ value }) => {
    return value === undefined || value === '0' ? '' : value;
  })
  userId?: string;

  @IsOptional()
  @IsString()
  @ApiProperty({ description: 'Filter for activities (today, upcoming, custom, lastWeek, twoWeeksAgo, threeWeeksAgo, lastMonth)', required: false, type: String })
  dateFilter?: string;

  @IsOptional()
  @IsDate()
  @Transform(({ value }) => new Date(value))
  @ApiProperty({ description: 'Custom date for filtering activities in yyyy-mm-dd format', required: false, type: String })
  customDate?: Date;

  // @IsOptional()
  // @IsDate()
  // @Transform(({ value }) => new Date(value))
  // @ApiProperty({ description: 'Custom date for filtering activities in yyyy-mm-dd format', required: false, type: String })
  // startDate?: Date;

  // @IsOptional()
  // @IsDate()
  // @Transform(({ value }) => new Date(value))
  // @ApiProperty({ description: 'Custom date for filtering activities in yyyy-mm-dd format', required: false, type: String })
  // endDate?: Date;


  @Transform(({ value }) => {
    return value > 0 ? value : 1;
  })
  page: number = 1;

  @Transform(({ value }) => {
    return value > 0 ? value : 10;
  })
  limit: number = 10;

  @IsOptional()
  @Transform(({ value }) => {
    return value === undefined || value === '0' ? '' : value;
  })
  rateCardId?: string;
}