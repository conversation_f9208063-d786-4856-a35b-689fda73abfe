import { Module } from '@nestjs/common';
import { AccountTypeService } from './account-type.service';
import { AccountTypeController } from './account-type.controller';
import { ConfigModule } from '@nestjs/config';
import { MongooseModule } from '@nestjs/mongoose';;
import { AccountType, AccountTypeSchema } from './schemas/account-type.schema';
import { JwtModule } from '@nestjs/jwt';
import { UserModule } from 'src/user/user.module';
import { EndpointsRolesModule } from 'src/endpoints-roles/endpoints-roles.module';

@Module({
  imports: [
    ConfigModule,
    JwtModule, EndpointsRolesModule,
    UserModule,
    MongooseModule.forFeature([{ name: AccountType.name, schema: AccountTypeSchema }])
  ],
  controllers: [AccountTypeController],
  providers: [AccountTypeService],
})
export class AccountTypeModule { }
