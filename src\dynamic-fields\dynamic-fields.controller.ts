import { <PERSON>, Get, Post, Body, Patch, Param, Delete, UseGuards, <PERSON>q, Query, Logger, BadRequestException, InternalServerErrorException } from '@nestjs/common';
import { DynamicFieldService } from './dynamic-fields.service';
import { ApiBearerAuth, ApiOperation, ApiParam, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Roles } from 'src/auth/decorators/roles.decorator';
import { AuthJwtGuard } from 'src/auth/guards/auth-jwt/auth-jwt.guard';
import { RolesGuard } from 'src/auth/guards/roles/roles.guard';
import { Role } from 'src/auth/enums/role.enum';
import { validateObjectId } from 'src/utils/validation.utils';
import { DynamicFieldDto } from './dto/dynamic-field.dto';
import { UpdateDynamicFieldDto } from './dto/update-dynamic-field.dto';
import moment from 'moment';
import { UpdateFieldOrderBulkDto } from './dto/update-order.dto';

@Controller('')
@ApiTags('dynamic-fields')
export class DynamicFieldsController {
  constructor(private readonly dynamicFieldService: DynamicFieldService) { }

  private readonly logger = new Logger(DynamicFieldService.name);

  // Add a new dynamic field
  @Post('dynamic-fields')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.Vendor, Role.JobSeeker, Role.TeamMember, Role.Recruiter, Role.BUHead, Role.DeliveryManager, Role.TeamLead, Role.Freelancer)
  @Roles()
  @ApiOperation({ summary: 'Add a new dynamic field', description: 'This is accessible only for "Admin".' })
  @ApiResponse({ status: 201, description: 'Dynamic field added successfully.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Invalid data.' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role admin can only use this end point.' })
  async addDynamicField(@Req() req: any, @Body() dynamicFieldDto: DynamicFieldDto) {
    // if (req.user.org) {
    //   dynamicFieldDto.orgId = req.user.org._id;
    // }
    return await this.dynamicFieldService.addDynamicField(dynamicFieldDto);
  }

  // Update a specific dynamic field by ID
  @Patch('dynamic-fields/:fieldId')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.Vendor, Role.JobSeeker, Role.TeamMember, Role.Recruiter, Role.BUHead, Role.DeliveryManager, Role.TeamLead, Role.Freelancer)
  @Roles()
  @ApiOperation({ summary: 'Update a specific dynamic field', description: 'This is accessible only for "Admin".' })
  @ApiParam({ name: 'fieldId', description: 'ID of the field to be updated' })
  @ApiResponse({ status: 200, description: 'Dynamic field updated successfully.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Invalid data.' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role admin can only use this end point.' })
  @ApiResponse({ status: 404, description: 'Dynamic field not found.' })
  async updateDynamicField(@Param('fieldId') fieldId: string, @Body() dynamicFieldDto: UpdateDynamicFieldDto) {
    return await this.dynamicFieldService.updateDynamicField(fieldId, dynamicFieldDto);
  }

  // Delete a specific dynamic field by ID
  @Delete('dynamic-fields/:fieldId')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.Vendor, Role.JobSeeker, Role.TeamMember, Role.Recruiter, Role.BUHead, Role.DeliveryManager, Role.TeamLead, Role.Freelancer)
  @Roles()
  @ApiOperation({ summary: 'Delete a specific dynamic field', description: 'This is accessible only for "Admin".' })
  @ApiParam({ name: 'fieldId', description: 'ID of the field to be deleted' })
  @ApiResponse({ status: 200, description: 'Dynamic field deleted successfully.' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role admin can only use this end point.' })
  @ApiResponse({ status: 404, description: 'Dynamic field not found.' })
  async deleteDynamicField(@Param('fieldId') fieldId: string) {
    return await this.dynamicFieldService.deleteDynamicField(fieldId);
  }

  @Get('dynamic-fields')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.Vendor, Role.JobSeeker, Role.TeamMember, Role.Recruiter, Role.BUHead, Role.DeliveryManager, Role.TeamLead, Role.Freelancer)
  @Roles()
  @ApiOperation({ summary: 'Get all dynamic fields for an organization', description: 'This is accessible only for "Admin".' })
  @ApiQuery({ name: 'orgId', description: 'ID of the organization', required: false })
  @ApiQuery({ name: 'contactId', description: 'ID of the contact', required: false })
  @ApiQuery({ name: 'isJobField', description: 'Filter by whether the field is related to job applications', required: false })
  @ApiQuery({ name: 'isJobApplicationField', description: 'Filter by whether the field is related to job applications', required: false })
  @ApiQuery({ name: 'departmentId', description: 'departmentId to fetch dynamic-fields', required: false })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role admin can only use this end point.' })
  @ApiResponse({ status: 200, description: 'Dynamic fields retrieved successfully.' })
  async getAllDynamicFields(@Req() req: any, @Query('orgId') orgId?: string,  @Query('contactId') contactId?: string, @Query('isJobField') isJobField?: boolean,
    @Query('isJobApplicationField') isJobApplicationField?: boolean, @Query('departmentId') departmentId?: string) {
    // if (!orgId) {
    //   throw new BadRequestException('orgId is Mandatory.');
    // }
    return await this.dynamicFieldService.getAllDynamicFields(orgId,contactId, isJobField, isJobApplicationField, departmentId);
  }

  // Get a specific dynamic field by ID
  @Get('dynamic-fields/:fieldId')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin)
  @Roles()
  @ApiOperation({ summary: 'Retrieve a specific dynamic field by ID', description: 'This is accessible only for "Admin".' })
  @ApiParam({ name: 'fieldId', description: 'ID of the field' })
  @ApiResponse({ status: 200, description: 'Dynamic field retrieved successfully.' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role admin can only use this end point.' })
  @ApiResponse({ status: 404, description: 'Dynamic field not found.' })
  async getDynamicField(@Param('fieldId') fieldId: string) {
    return await this.dynamicFieldService.getDynamicField(fieldId);
  }

  @Post('bulk-dynamic-fields')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  // @Roles()
  @ApiOperation({ summary: 'Add multiple dynamic fields', description: 'Bulk add dynamic fields for an organization.' })
  @ApiResponse({ status: 201, description: 'Dynamic fields added successfully.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Invalid data.' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role admin can only use this endpoint.' })
  async addBulkDynamicField(@Req() req: any, @Body() dynamicFieldDtos: DynamicFieldDto[]) {
    if (req.user.org) {
      dynamicFieldDtos.forEach(field => {
        field.orgId = req.user.org._id; // Set orgId for each field
      });
    }
    return await this.dynamicFieldService.addBulkDynamicFields(dynamicFieldDtos);
  }


  @Get('dynamicfieldsForJobApplication/:jobId')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin)
  @Roles()
  @ApiOperation({ summary: 'Get all dynamic fields for a Job' })
  @ApiParam({ name: 'jobId', description: 'ID of the Job' })
  @ApiResponse({ status: 200, description: 'Dynamic field retrieved successfully.' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role admin can only use this end point.' })
  @ApiResponse({ status: 404, description: 'Dynamic field not found.' })
  async getDynamicFieldsForJobApplication(@Req() req: any, @Param('jobId') jobId: string) {
    if (!jobId) {
      throw new BadRequestException('jobId is Mandatory.');
    }
    return await this.dynamicFieldService.getDynamicFieldsForJobApplication(jobId);
  }

  @Get('dynamicfieldsForPostJob')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin)
  @Roles()
  @ApiOperation({ summary: 'Get all dynamic fields for a Job' })
  @ApiQuery({ name: 'departmentId', required: false, description: 'search dynamic fields by department' })
  @ApiQuery({ name: 'orgId', required: false, description: 'search dynamic fields by orgId' })
   @ApiQuery({ name: 'contactId', required: false, description: 'search dynamic fields by department' })
  @ApiResponse({ status: 200, description: 'Dynamic field retrieved successfully.' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role admin can only use this end point.' })
  @ApiResponse({ status: 404, description: 'Dynamic field not found.' })
  async getDynamicFieldsForPostJob(@Req() req: any, @Query('contactId') contactId?: string,@Query('departmentId') departmentId?: string,@Query('orgId') orgId?: string) {
    let postingOrgId = null;
    if (req.user?.org) {
       postingOrgId = req.user.org._id.toString();
    }
    return await this.dynamicFieldService.getDynamicFieldsForPostJob(orgId, contactId,departmentId,postingOrgId);
  }

  @Patch('update-order')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  @Roles() // Apply role-based access control
  @ApiOperation({ summary: 'Bulk update dynamic field order', description: 'Updates the order of multiple dynamic fields.' })
  @ApiResponse({ status: 200, description: 'Field order updated successfully.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Invalid data.' })
  @ApiResponse({ status: 403, description: 'Forbidden, user does not have access.' })
  async updateFieldOrder(@Body() updateDto: UpdateFieldOrderBulkDto) {
    return await this.dynamicFieldService.updateFieldOrder(updateDto);
  }

}
