import { IsEnum, IsMongoId, IsNotEmpty, IsOptional, IsString, ValidateIf } from 'class-validator';
import { ApiHideProperty, ApiProperty } from '@nestjs/swagger';
import { Transform, TransformFnParams } from 'class-transformer';
import { sanitizeWithStyle } from "src/utils/sanitize-with-style";
import { AccountType } from 'src/shared/constants';

export class CreateBankDetailsDto {
    @ApiProperty({
        type: String,
        required: true,
    })
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    @IsString()
    accountHolderName: string;

    @ApiProperty({
        type: String,
        required: true,
    })
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    @IsString()
    accountNumber: string;

    @ApiProperty({
        type: String,
        required: true,
    })
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    @IsString()
    ifscCode: string;

    @ApiProperty({
        type: String,
        required: true,
    })
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    @IsString()
    bankName: string;

    @ApiProperty({
        type: String,
        required: false,
    })
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    @IsString()
    @IsOptional()
    branchName?: string;


    @ApiProperty({
        type: String,
        required: false,
        enum: AccountType,
        description: 'Account type',
    })
    @IsString()
    @IsOptional()
    @ValidateIf((obj) => obj.employmentType === '' || Object.values(AccountType).includes(obj.AccountType))
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    accountType?: string;

    @ApiProperty({
        type: String,
        required: false,
    })
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    @IsOptional()
    @IsString()
    upiId?: string;

    @ApiProperty({
        type: String,
        required: false,
    })
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    @IsMongoId()
    @IsNotEmpty()
    empId: string;

    @IsOptional()
    isDeleted?: boolean;

    @IsOptional()
    isActive?: boolean;

    @ApiHideProperty()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    createdBy?: string;
}
