import { IsEmail, IsNotEmpty, IsO<PERSON>al, IsString, IsUUID } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Transform, TransformFnParams } from 'class-transformer';
import sanitizeWithStyle from 'sanitize-html'

export class VerifyOtpDto {

    @ApiProperty({
        type: String,
        required: false,
        description: 'Enter email of your account',
        format: 'email',
    })
    @IsOptional()
    @IsEmail()
    @IsString()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value).toLowerCase())
    email?: string;

    @ApiProperty({
        type: String,
        required: true,
        description: 'OTP to verify user'
    })
    @IsNotEmpty()
    @IsString()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    verification: string;
}