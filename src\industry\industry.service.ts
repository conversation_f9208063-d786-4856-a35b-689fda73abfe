import { NotFoundException, Injectable, Logger, InternalServerErrorException, BadRequestException, } from '@nestjs/common';
import { CreateIndustryDto } from './dto/create-industry.dto';
import { UpdateIndustryDto } from './dto/update-industry.dto';
import { ConfigService } from '@nestjs/config';
import { InjectModel } from '@nestjs/mongoose';
import { Industry, IndustryDocument } from './schemas/industry.schema';
import { Model, Types } from 'mongoose';


@Injectable()
export class IndustryService {

  private readonly logger = new Logger(IndustryService.name);

  constructor(private configService: ConfigService, @InjectModel(Industry.name) private industryModel: Model<Industry>) {

  }

  async create(createIndustryDto: CreateIndustryDto) {
    try {
      const isThere = await this.industryModel.findOne({
        name: createIndustryDto.name.trim()
      });
      if (isThere) {
        throw new NotFoundException(`The Industry with name: "${createIndustryDto.name}" already exists.`);
      }

      const createdIndustry = new this.industryModel(createIndustryDto);
      return await createdIndustry.save();

    }
    catch (error) {
      this.logger.error(`Failed to create industry. ${error}`);
      throw new InternalServerErrorException(`Error when creating industry. ${error?.message}`);
    }
  }


  findAll(): Promise<IndustryDocument[]> {
    return this.industryModel.find({ isDeleted: false })
      .populate({ path: 'createdBy', select: '_id roles firstName' })
      .sort({ updatedAt: -1 })
      .exec();
  }

  findAllIncludingSoftDeleted(): Promise<IndustryDocument[]> {
    return this.industryModel.find({})
      .populate({ path: 'createdBy', select: '_id roles firstName' })
      .sort({ updatedAt: -1 })
      .exec();
  }


  findAllSoftDeleted(): Promise<IndustryDocument[]> {
    return this.industryModel.find({ isDeleted: true })
      .populate({ path: 'createdBy', select: '_id roles firstName' })
      .sort({ updatedAt: -1 })
      .exec();
  }


  async findOne(industryId: Types.ObjectId) {
    try {
      const industry = await this.industryModel.findById(industryId);
      if (!industry) {
        throw new NotFoundException(`The Industry with id: "${industryId}" doesn't exist.`);
      }
      if (industry.isDeleted) {
        throw new BadRequestException(`The Industry with id: "${industryId}" is soft deleted.`);
      }
      return industry;
    }
    catch (error) {
      this.logger.error(`An error occurred in fetching  Industry by Id ${industryId}. ${error?.message}`);
      throw error;

    }

  }

  async update(industryId: Types.ObjectId, updateIndustryDto: UpdateIndustryDto) {
    try {
      const industry = await this.industryModel.findById(industryId)
      if (!industry) {
        throw new NotFoundException(`The Industry with id: "${industryId}" doesn't exist.`);
      }
      return await this.industryModel.findByIdAndUpdate(industryId, updateIndustryDto, { new: true });
    }
    catch (error) {
      this.logger.error(`An error occurred while updating Industry by ID ${industryId}. ${error?.message}`);
      throw error;
    }
  }
  // to retrive soft delete accounts 
  async restoreSoftDeletedIndustry(industryId: Types.ObjectId) {
    try {
      const industry = await this.industryModel.findById(industryId);
      if (!industry) {
        throw new NotFoundException(`The Industry with id: "${industryId}" doesn't exsist.`);
      }
      if (!industry.isDeleted) {
        throw new BadRequestException(`The Industry with id: "${industryId}" is not soft deleted.`);
      }
      industry.isDeleted = false
      await industry.save();
      return industry
    }
    catch (error) {
      this.logger.error(`An error occurred while updating Industry by ID ${industryId}. ${error?.message}`);
      throw error;
    }
  }


  //soft delete
  async remove(industryId: Types.ObjectId) {
    try {
      const industry = await this.industryModel.findById(industryId);
      if (!industry) {
        throw new NotFoundException(`The Industry with id: "${industryId}" doesn't exist.`);
      }
      if (industry.isDeleted) {
        throw new NotFoundException(`The Industry with id: "${industryId}" is already soft deleted.`);
      }
      industry.isDeleted = true;
      await industry.save();
      return industry;
    }
    catch (error) {
      this.logger.error(`An error occurred while deleting Industry by ID ${industryId}. ${error?.message}`);
      throw error;
    }
  }



  async hardDelete(industryId: Types.ObjectId) {
    try {
      const industry = await this.industryModel.findById(industryId);
      if (!industry) {
        throw new NotFoundException(`The Industry with id: "${industryId}" doesn't exist.`);
      }

      await this.industryModel.findByIdAndDelete(industryId);
      return { message: `Industry deleted.` };
    }
    catch (error) {
      this.logger.error(`An error occurred while deleting Industry by ID ${industryId}. ${error?.message}`);
      throw error;
    }
  }

  async deleteAll() {
    await this.industryModel.deleteMany();
    return "All industries deleted"
  }

};
