import {
    WebSocketGateway,
    WebSocketServer,
    OnGatewayConnection,
    OnGatewayDisconnect,
  } from '@nestjs/websockets';
  import { Server, Socket } from 'socket.io';
  import { Injectable } from '@nestjs/common';
  import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
  
  @WebSocketGateway({
        namespace: 'notifications',
        cors: { origin: '*' }, // Allow all origins (for development)
    })
  @Injectable()
  export class NotificationsGateway implements OnGatewayConnection, OnGatewayDisconnect {
    @WebSocketServer()
    server: Server;
  
    private userSockets = new Map<string, string>();
  
    constructor(private readonly jwtService: JwtService,private configService: ConfigService) {}
  
    async handleConnection(client: Socket) {
      try {
        console.log('Client connected in Notifications:', client.id);
        const token = client.handshake.auth?.token || client.handshake.query?.token;
        // console.log('Token:', token);
        if (!token) return client.disconnect();
        console.log('Token ------');
  
        // const payload = this.jwtService.verify(token);
        const payload = await this.jwtService.verifyAsync(
            token,
            {
              secret: this.configService.get<string>('SECRET_KEY')
            }
          );
        // console.log('Payload:', payload);
        const userId = payload?._id;
        console.log('User ID:', userId);
        if (!userId) return client.disconnect();
  
        this.userSockets.set(userId, client.id);
        console.log(`🔌 User connected: ${userId}`);
      } catch {
        client.disconnect();
      }
    }
  
    handleDisconnect(client: Socket) {
      const userId = [...this.userSockets.entries()].find(([_, socketId]) => socketId === client.id)?.[0];
      if (userId) {
        this.userSockets.delete(userId);
        console.log(`❌ User disconnected: ${userId}`);
      }
    }
  
    sendToUser(userId: string, payload: any) {
      const socketId = this.userSockets.get(userId);
      console.log('User ID:', userId);
      console.log('Socket ID:', socketId);
      if (socketId) {
        this.server.to(socketId).emit('new-notification', payload);
      }
    }
  }
  