export enum Role {
  // Default User Role, Can be added to any or all user
  User = 'user',
  SalesRep = 'sales-rep',

  Candidate = 'candidate',
  FreelanceRecruiter = 'freelance-recruiter',

  Superuser = 'admin',
  SuperAdmin = 'super-admin',
  Terminator = 'terminator',
  // Note: The following are org level roles.
  AgencyOrgMember = 'agency-org-member',
  AgencyOrgAdmin = 'agency-org-admin',
  RootOrgAdmin = 'root-org-admin', // Is for super admin role (Shankar sir)
  RootOrgMember = 'root-org-member', // For admin org employees (Coding Limits employees) 

  CustomerOrgAdmin = 'customer-org-admin',
  CustomerOrgMember = 'customer-org-member',
  PremiumCustomerOrgAdmin = 'premium-customer-org-admin', // Customers who wants to use premium features like Internal
  PremiumCustomerOrgMember = 'premium-customer-org-member',

  //NOTE: Accounts are for internal usage they don't need role permissions
  // AccountAdmin = 'account-admin',
  // AccountMember = 'account-member', 
  // SuperOrgAdmin = 'super-org-admin',
  // SuperOrgMember = 'super-org-member',

  // SuperAdmin = 'super-admin',
  OrgAdmin = 'org-admin',
  OrgUser = 'org-user', // default

  
  BUHead = 'bu_head',
  AccountManager = 'account_manager',
  ResourceManager = 'resource_manager',
  DeliveryManager = 'delivery_manager',
  TeamLead = 'team_lead',
  TeamMember = 'team_member',
  Vendor = 'vendor',
  JobSeeker = 'job-seeker',
  TechPanel = 'tech-panel',
  Recruiter = 'recruiter',
  Admin = 'admin',
  Freelancer = 'freelancer',
  Employee = 'employee',
  BgvHandler = 'bgvHandler',
  HR = 'HR',
  FinanceOfficer = 'finance_officer',

}
