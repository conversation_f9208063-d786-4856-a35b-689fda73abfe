import { Injectable, InternalServerErrorException, Logger, NotFoundException } from '@nestjs/common';
import { CreateIdentifierDto } from './dto/create-identifier.dto';
import { UpdateIdentifierDto } from './dto/update-identifier.dto';
import { Identifier } from './schemas/identifier.schema';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { EventEmitter2 } from '@nestjs/event-emitter';

@Injectable()
export class IdentifierService {

  private readonly logger = new Logger(IdentifierService.name);

  constructor(
    @InjectModel(Identifier.name) private identifierModel: Model<Identifier>,
    private eventEmitter: EventEmitter2
  ) {
  }

  async create(createIdentifierDto: CreateIdentifierDto, user: Object) {
    try {
      const createdIdentifier = new this.identifierModel(createIdentifierDto);
      await createdIdentifier.save();

      const entityType = createIdentifierDto?.isType

      const existingIdentifier = await this.findById(createdIdentifier._id);
      if (createIdentifierDto?.isNewIdentifier)
        this.emitEvent('identifier.created',{existingIdentifier, user, entityType })

      return createdIdentifier;
    } catch (error) {
      this.logger.error('Failed to create identifier', error);
      throw new InternalServerErrorException(`Unknown error when creating identifier. ${error.message}`);
    }
  }

  async findAll() {
    return await this.identifierModel.find()
      .populate({ path: 'region', select: '_id countryName stateName' })
      .exec();
  }

  async findAllByRegion(regionId: string): Promise<Identifier[]> {
    try {
      this.logger.debug(`Finding identifiers for regionId: ${regionId}`);
      const identifiers = await this.identifierModel.find({ region: regionId })
        .populate({ path: 'region', select: '_id countryName stateName' })
        .exec();
      this.logger.debug(`Found identifiers: ${JSON.stringify(identifiers)}`);
      return identifiers;
    } catch (error) {
      this.logger.error(`Error finding identifiers for regionId ${regionId}: ${error.message}`);
      throw error; // Rethrow the error to propagate it up the call stack
    }
  }

  async findById(identifierId: Types.ObjectId) {
    try {
      const identifier = await this.identifierModel.findById(identifierId)
        .populate({ path: 'region', select: '_id countryName stateName' })
        .exec();
      if (!identifier) {
        throw new NotFoundException(`Identifier with ID ${identifierId} not found`)
      }
      return identifier;
    } catch (error) {
      this.logger.error(error);
      this.logger.error(`An error occurred in fetching identifier by Id ${identifierId}. ${error?.message}`);
      throw error;
    }
  }

  async update(identifierId: Types.ObjectId, updateIdentifierDto: UpdateIdentifierDto, user:Object) {
    try {
      const existingIdentifier  = await this.findById(identifierId);
      if (!existingIdentifier ) {
        throw new NotFoundException(`Identifier with ID ${identifierId} not found`)
      }

      const updatedIdentifier= await this.identifierModel.findByIdAndUpdate(identifierId, updateIdentifierDto, { new: true })
        .populate({ path: 'region', select: '_id countryName stateName' })
        .exec();
      if (!updatedIdentifier ) {
        throw new NotFoundException(`Identifier with ID ${identifierId} not found`)
      }

        const changes = this.getIdentifierChanges(existingIdentifier , updatedIdentifier );
        if (changes.length > 0) {
          changes.forEach(change => {
            this.emitEvent('identifier.updated', {
              region: existingIdentifier .region,
              title: change.description,
              user:user
            });
          });
        }
    
      
      return updatedIdentifier
    } catch (error) {
      this.logger.error(error);
      this.logger.error(`An error occurred in updating identifier by Id ${identifierId}. ${error?.message}`);
      throw error;
    }
  }

  async delete(identifierId: Types.ObjectId, user:Object) {
    try {
      const identifier = await this.findById(identifierId);
      if (!identifier) {
        throw new NotFoundException(`Identifier with ID ${identifierId} not found`)
      }
      console.log(identifier);
      this.emitEvent('identifier.deleted',{identifier,user});
      return await this.identifierModel.deleteOne({ _id: identifierId });
    } catch (error) {
      this.logger.error(`An error occurred while deleting identifier by ID ${identifierId}. ${error?.message}`);
      throw new InternalServerErrorException('An error occurred while hard deleting the Job');
    }
  }

  getIdentifierChanges(existingIdentifier: Identifier, updatedIdentifier: Identifier) {
    const changes = [];
  
    if (existingIdentifier.name !== updatedIdentifier.name) {
      const entityType = existingIdentifier.isType==='Company' ? 'Company' : 'Individual';

      const entityTypeLabel = entityType === 'Company' ? 'Company Identifier' : 'Individual Identifier';


      if (updatedIdentifier.isTaxIdentifier) {
        changes.push({
          description: `${entityType} Tax Identifier has been updated from ${existingIdentifier.name} to ${updatedIdentifier.name}`
        });
      }

      else if (updatedIdentifier.isEntityIdentifier) {
        changes.push({
          description: `${entityTypeLabel}  has been updated from ${existingIdentifier.name} to ${updatedIdentifier.name}`
        });
      }

     else {
        changes.push({
          description: `${entityType} Option Identifier has been updated from ${existingIdentifier.name} to ${updatedIdentifier.name}`
        });
      }
    }

    if (existingIdentifier.isUpload !== updatedIdentifier.isUpload) {
      changes.push({
        description: `Identifier ${updatedIdentifier.name} upload functionality has been updated from ${existingIdentifier.isUpload} to ${updatedIdentifier.isUpload}`
      });
    }
  
    return changes;
  }
  


  emitEvent(eventName: string, payload: any) {
    this.logger.debug(payload)
    this.eventEmitter.emit(eventName, payload);
  }

}
