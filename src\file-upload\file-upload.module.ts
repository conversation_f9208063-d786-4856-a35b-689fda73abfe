import { forwardRef, Module } from '@nestjs/common';
import { FileUploadService } from './file-upload.service';
import { FileUploadController } from './file-upload.controller';
import { JwtModule } from '@nestjs/jwt';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { getConnectionToken, MongooseModule } from '@nestjs/mongoose';
import { FileMetadata, FileMetadataSchema } from './schemas/file-metadata.schema';
import { OnboardingModule } from 'src/onboarding/onboarding.module';
import { OrgModule } from 'src/org/org.module';
import { Org, OrgSchema } from 'src/org/schemas/org.schema';
import { Onboarding, OnboardingSchema } from 'src/onboarding/schemas/onboarding.schema';
import { StatusModule } from 'src/status/status.module';
import { EndpointsRolesModule } from 'src/endpoints-roles/endpoints-roles.module';
@Module({
  imports: [
    JwtModule, EndpointsRolesModule,
    ConfigModule,
    OnboardingModule,
    StatusModule,
    forwardRef(() => OrgModule),
    MongooseModule.forFeature([{ name: Org.name, schema: OrgSchema },
    { name: FileMetadata.name, schema: FileMetadataSchema },
    { name: Onboarding.name, schema: OnboardingSchema }

    ]),
    MongooseModule.forFeatureAsync([
      {
        name: FileMetadata.name,
        imports: [ConfigModule],
        useFactory: (configService: ConfigService) => {
          const autoIncrement = AutoIncrementFactory(configService);
          const schema = FileMetadataSchema;
          schema.plugin(autoIncrement, {
            inc_field: 'fileMetaDataCode',
            id: 'file_meta_data_sequence',
            start_seq: 1,
            reference_fields: []
          });

          return schema;
        },
        inject: [getConnectionToken(), ConfigService],

      },
    ])
  ],
  controllers: [FileUploadController],
  providers: [FileUploadService],
  exports: [FileUploadService, MongooseModule],  // Export MongooseModule 

})
export class FileUploadModule { }

const AutoIncrementFactory = require('mongoose-sequence');
