import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsNotEmpty, IsOptional } from 'class-validator';

export class UpdateJobApplicationStatusDto {
  @ApiProperty({
    type: Boolean,
    description: 'Indicates if the job application is selected',
    required: false,
    default:false
  })
  @IsBoolean()
  @IsOptional()
  isSelected?: boolean;

  @ApiProperty({
    type: Boolean,
    description: 'Indicates if the job application is rejected',
    required: false,
    default:false
  })
  @IsBoolean()
  @IsOptional()
  isRejected?: boolean;
}
