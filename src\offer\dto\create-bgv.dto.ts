// dto/create-bgv.dto.ts
import { IsO<PERSON>, IsMongoId, IsString, IsISO8601, IsOptional, IsEmail, IsArray, IsNumber } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Transform, TransformFnParams } from 'class-transformer';
import { sanitizeWithStyle } from "src/utils/sanitize-with-style";

export class CreateBgvDto {


    @ApiProperty({
        type: String,
        required: true,
        description: ''
    })
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    @IsString()
    jobApplication: string;

    @ApiProperty({
        type: String,
        required: false,
        description: 'BGV handler name',
    })
    @IsString()
    @IsOptional()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    bgvHandlerName: string;

    @ApiProperty({
        type: String,
        required: false,
        description: 'Email address of the BGV handler',
        format: 'email',
        default: '<EMAIL>',
    })
    @IsEmail()
    @IsString()
    @IsOptional()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value).toLowerCase())
    bgvHandlerEmail: string;

    @ApiProperty({
        type: String,
        required: false,
        description: 'BGV handler Contact number'
    })
    @IsString()
    @IsOptional()
    // @IsPhoneNumber()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    bgvHandlerContactNo?: string;

    @ApiProperty({
        type: Date,
        required: false,
        description: 'Date of joining',
        default: new Date(Date.UTC(new Date().getUTCFullYear(), new Date().getUTCMonth(), new Date().getUTCDate(), new Date().getUTCHours(), new Date().getUTCMinutes())),
    })
    // YYYY-MM-DDThh:mm:ssZ - example : 2024-05-27T00:00:00.000Z
    @IsISO8601({ strict: true })
    @IsOptional()
    expectedFinalReportDate: Date;

    @ApiProperty({
        example: {
            qualification_certificates: ['665cdb6be21e7d396c3a2f1a', '665cdb6be21e7d396c3a2f2a'],
            pan_card: ['665cdb6be21e7d396c3a2f1a'],
        },
    })
    @IsOptional()
    @IsObject()
    documents: Record<string, string[]>; // key: fileMetadataId

    @ApiProperty({
        example: ['665cdb6be21e7d396c3a2f1a', '665cdb6be21e7d396c3a2f2a'],
        required: false,
    })
    @IsOptional()
    @IsArray()
    @IsMongoId({ each: true })
    interimBgvDocs?: string[];

    @ApiProperty({
        example: '665cdb6be21e7d396c3a2f1a',
        required: false,
    })
    @IsOptional()
    @IsMongoId()
    offerLetterDoc?: string[];

    @ApiProperty({ example: 800000, required: false })
    @IsOptional()
    @IsNumber()
    expectedCtc?: number;


    // @ApiProperty({
    //     example: {
    //         manual_bgv: ['665cdb6be21e7d396c3a2f1a'],
    //         adhar_with_stamp: ['665cdb6be21e7d396c3a2f1f'],
    //     },
    //     description: 'Custom BGV document fields with arrays of file metadata IDs',
    // })
    // @IsOptional()
    // @IsObject()
    // customBgvDocuments?: Record<string, string[]>;


}
