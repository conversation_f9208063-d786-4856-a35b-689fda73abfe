import { ApiProperty } from "@nestjs/swagger";
import { Transform, TransformFnParams } from "class-transformer";
import { IsArray, IsBoolean, IsMongoId, IsNotEmpty, IsOptional, IsString } from "class-validator";
import { sanitizeWithStyle } from "src/utils/sanitize-with-style";

export class CreateWorkflowDto {


  @ApiProperty({
    type: String,
    required: true,
    description: ''
  })
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    type: [String],
    required: true,
    description: ''
  })
  @IsMongoId({ each: true })
  @Transform((params: TransformFnParams) => params.value.map((value: string) => sanitizeWithStyle(value)))
  @IsArray()
  stages: string[];


  @ApiProperty({
    type: String,
    required: false,
    description: ''
  })
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  @IsString()
  @IsOptional()
  @IsMongoId()
  businessUnitId?: string;

  @ApiProperty({
    type: Boolean,
    required: false,
    default: false
  })
  @IsOptional()
  @IsBoolean()
  isDefault?: boolean;

  @ApiProperty({
    type: Boolean,
    required: false,
    default: false
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  // @ApiProperty({
  //   type: Boolean,
  //   required: false,
  //   default: false
  // })
  // @IsOptional()
  // @IsBoolean()
  // isJobSpecific?: boolean;

  // // Todo :workflow in jobs

  @ApiProperty({
    type: String,
    required: false,
    description: ''
  })
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  @IsOptional()
  @IsString()
  @IsMongoId()
  org?: string;

  @ApiProperty({
    type: Boolean,
    required: false,
    default: false
  })
  @IsOptional()
  @IsBoolean()
  isGlobal?: boolean;

  @ApiProperty()
  totalJobApplicationsCount: number; // Add this field

}

