import { ApiHideProperty, ApiProperty } from "@nestjs/swagger";
import { IsArray, IsBoolean, IsEnum, IsMongoId, IsNotEmpty, IsNumber, IsObject, IsOptional, IsString, Min, ValidateIf, ValidateNested } from "class-validator";
import { Types } from "mongoose";
import { Transform, TransformFnParams, Type } from 'class-transformer';
import sanitizeWithStyle from 'sanitize-html'
import { Currency, EmploymentType, HiringMode, JobType, WorkMode } from "src/shared/constants";
import { SocialMediaLinkDto } from "src/common/dto/social-media-link.dto";
import { VendorRateCardDto } from "./vendor-rate-card.dto";

export class CreateJobDto {

  @ApiProperty({
    type: String,
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  title: string;


  // TBD:: internal , external 
  // is this mandatory??
  //internal and external here we can say internal is for the companies requirement
  // and external is other companies requirement  we are going to fill
  @ApiProperty({
    type: String,
    required: false,
    // default: JobType.Internal,
    enum: JobType,
    description: 'Job type',
  })
  @IsEnum(JobType)
  @IsString()
  @IsOptional()
  jobType?: string;

  //TBD:: department is a select, list
  //should be a separate schema or simply an enum ??
  // is this mandatory??
  //by following workable it should be seperate schema
  @ApiProperty({
    type: String,
    required: false,
  })
  @IsOptional()
  // @IsMongoId()
  @IsString()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  department?: string;

  @ApiProperty({
    type: Number,
    required: false,
    default: 1
  })
  @IsNumber()
  @IsOptional()
  noOfVacancies?: number;


  // this is an url, which is saved, after uploading a file to some third party file storage service, local storage.
  @ApiProperty({
    type: String,
    required: false,
  })
  // @IsMongoId()
  @IsString()
  @IsOptional()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  jdUrl?: string


  // TBD:: is this mandatory?
  // this should be a list of values
  // an enum or a separate schema - TBD
  // what is the default value? //list Full-time,Part-time,Contract,Temporary,Other
  @ApiProperty({
    type: String,
    required: false,
    enum: EmploymentType,
    description: 'Employment type',
  })
  @IsString()
  @IsOptional()
  @ValidateIf((obj) => obj.employmentType === '' || Object.values(EmploymentType).includes(obj.employmentType))
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  employmentType?: string;

  // TBD:: is this mandatory?
  // free text in the ui design layout
  // what is the default value?
  @ApiProperty({
    type: String,
    required: false,
    enum: WorkMode,
    description: 'Work mode of the job',
  })
  @IsString()
  @IsOptional()
  @ValidateIf((obj) => obj.workMode === '' || Object.values(WorkMode).includes(obj.workMode))
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  workMode?: string;

  @ApiProperty({
    type: String,
    required: false,
    // default: '<h1>Job Description</h1>'
  })
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  @IsString()
  @IsOptional()
  description?: string;


  @ApiProperty({ description: 'Array of Primary skills', type: [String], required: false })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  @Transform((params: TransformFnParams) => params.value.map((value: string) => sanitizeWithStyle(value)))
  primarySkills?: string[];

  @ApiProperty({ description: 'Array of Secondary skills', type: [String], required: false })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  @Transform((params: TransformFnParams) => params.value.map((value: string) => sanitizeWithStyle(value)))
  secondarySkills?: string[];

  // @ApiProperty({
  //     required:false,
  //   })
  // workExperienceFrom: number;

  // @ApiProperty({
  //     required:false,
  //   })
  // workExperienceTo: number;

  @ApiProperty({
    type: 'object',
    required: false,
    properties: {
      minimum: {
        type: 'number',
        description: 'Minimum years of work experience required',
      },
      maximum: {
        type: 'number',
        description: 'Maximum years of work experience required',
      },
    },
  })
  @IsOptional()
  workExperience?: { minimum?: number; maximum?: number };

  @ApiProperty({
    type: [String],
    required: false,
  })
  @IsOptional()
  @IsMongoId({ each: true })
  @IsArray()
  @Transform((params: TransformFnParams) => params.value.map((value: string) => sanitizeWithStyle(value)))
  jobLocation?: string[];


  @ApiProperty({
    type: String,
    required: false,
    description: 'Industry',
  })
  @IsOptional()
  // @IsMongoId()
  @IsString()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  industryOrDomain?: string;

  @ApiProperty({
    type: [String],
    required: false,
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  @Transform((params: TransformFnParams) => params.value.map((value: string) => sanitizeWithStyle(value)))
  educationalQualification?: string[];


  @ApiProperty({
    type: Boolean,
    required: false,
    default: false
  })
  @IsBoolean()
  @IsOptional()
  videoProfileRequested?: boolean;


  // TBD:: is this mandatory?
  // this should be a list of values
  // an enum or a separate schema - TBD
  // what is the default value?
  // 1. walk-in
  // 2. scheduled-interview
  // 3. drive

  @ApiProperty({
    type: String,
    required: false,
    enum: HiringMode,
    description: 'Hiring mode of the job',
  })
  @IsString()
  @IsOptional()
  @ValidateIf((obj) => obj.hiringMode === '' || Object.values(HiringMode).includes(obj.hiringMode))
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  hiringMode?: string;


  // this is an url, which is saved, after uploading a file to some third party file storage service, local storage.
  @ApiProperty({
    type: String,
    required: false,
  })
  @IsString()
  @IsOptional()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  instructionFile?: string;

  // this is an url, which is saved, after uploading a file to some third party file storage service, local storage.
  @ApiProperty({
    type: String,
    required: false,
  })
  @IsString()
  @IsOptional()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  instructionsToRecruiter?: string;

  @ApiProperty({
    description: 'Array of questions and answers to be posted to the candidate by the recruiter',
    type: 'array',
    items: {
      type: 'object',
      properties: {
        question: { type: 'string', description: 'The question text' },
        answer: { type: 'string', description: 'The answer text' },
      },
    },
    required: false,
  })
  @IsArray()
  @IsOptional()
  @Transform((params: TransformFnParams) =>
    params.value.map((obj: any) => ({
      question: sanitizeWithStyle(obj.question),
      answer: sanitizeWithStyle(obj.answer),
    }))
  )
  questionAnswers?: {
    question?: string;
    answer?: string;
  }[];

  @ApiProperty({
    required: true,
    default: false
  })
  @IsBoolean()
  @IsOptional()
  shareWithVendors?: boolean;

  @ApiProperty({
    required: false,
    default: false
  })
  @IsBoolean()
  @IsOptional()
  shareWithFreelancers?: boolean;

  @ApiProperty({
    required: false,
    default: false
  })
  @IsBoolean()
  @IsOptional()
  isDeleted?: boolean;

  @ApiProperty({
    required: true,
    default: false
  })
  @IsBoolean()
  @IsOptional()
  shareOnSocialMedia?: boolean;

  @ApiProperty({
    type: [SocialMediaLinkDto],
    required: false,
  })
  @IsArray()
  @IsOptional()
  socialMediaLinks?: SocialMediaLinkDto[];

  @ApiHideProperty()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  createdBy?: string;

  //The organization which posted the job
  @ApiHideProperty()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  @IsOptional()
  postingOrg: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  @IsOptional()
  // @IsMongoId()
  @IsString()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  hiringOrg?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Contact of the hiring organization',
  })
  @IsOptional()
  // @IsMongoId()
  @IsString()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  spoc?: string;

  // Organization for which the job is posted
  @ApiProperty({
    type: String,
    required: true,
    description: 'Organization for which the job is posted',
  })
  // @IsOptional()
  @IsNotEmpty()
  @IsMongoId()
  @IsString()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  endClientOrg: string;

  @ApiHideProperty()
  @IsOptional()
  jobCode?: string;

  @ApiProperty({
    required: false,
    default: true
  })
  @IsBoolean()
  @IsOptional()
  isOpen?: boolean;

  @ApiProperty({
    required: false,
    default: false
  })
  @IsBoolean()
  @IsOptional()
  isDraft?: boolean;

  @ApiProperty({
    type: Number,
    required: false,
    description: 'Maximum CTC offered by the company for this job post',
  })
  @IsOptional()
  @IsNumber({}, { message: 'CTC must be a valid number' })
  @Min(0, { message: 'CTC must be a positive number' })
  @Transform(({ value }) => (value === '' ? null : Number(value)))
  maxCtcOffered?: number;

  @ApiProperty({
    type: String,
    required: false,
    enum: Currency,
    // default: Currency.INR,
  })
  @IsOptional()
  @IsEnum(Currency)
  currency?: Currency;

  @ApiProperty({
    type: Number,
    required: false,
  })
  @IsNumber()
  @IsOptional()
  targetApplications?: number;

  @ApiProperty({
    required: false,
    default: true
  })
  @IsBoolean()
  @IsOptional()
  isClientvisible?: boolean;


  // @ApiProperty({
  //   type: String,
  //   required: false,
  // })
  // @IsOptional()
  // @IsMongoId()
  // @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  // workflow?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Name of the end client',
  })
  @IsOptional()
  // @IsMongoId()
  @IsString()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  endClientName?: string;


  @ApiProperty({
    type: String,
    required: false,
  })
  @IsOptional()
  // @IsMongoId()
  @IsString()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  rateCard?: string;

  @ApiProperty({
    description: 'Dynamic fields associated with the job application, represented as key-value pairs',
    type: Object,
    required: false,
    example: {
      aadhaar: '12345',
      pan: '12344',
    },
  })
  @IsOptional()
  @IsObject()
  dynamicFields?: Record<string, any>;


  @ApiProperty({
    description: 'List of vendor rate card mappings',
    type: [VendorRateCardDto],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => VendorRateCardDto)
  vendorRateCards?: VendorRateCardDto[];

  @ApiProperty({
    type: Boolean,
    required: false,
    default: false
  })
  @IsBoolean()
  @IsOptional()
  requiredBgv?: boolean;

}
