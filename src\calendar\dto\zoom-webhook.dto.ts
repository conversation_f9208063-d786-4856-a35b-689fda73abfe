// // src/calendars/zoom/dto/zoom-webhook.dto.ts
// import { ApiProperty } from '@nestjs/swagger';

// export class ZoomWebhookDto {
//   @ApiProperty({ example: 'meeting.started', description: 'Event name from Zoom' })
//   event: string;

//   @ApiProperty({ example: 'v2', description: 'Zoom webhook payload version' })
//   payload_version: string;

//   @ApiProperty({
//     description: 'Payload object from Zoom',
//     type: 'object',
//   })
//   payload: any;
// }

import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsObject, IsOptional } from 'class-validator';

export class ZoomWebhookDto {
  @ApiProperty({ description: 'The Zoom event type' })
  @IsNotEmpty()
  @IsString()
  event: string;

  @ApiProperty({ description: 'The payload containing event details' })
  @IsNotEmpty()
  @IsObject()
  payload: {
    object: {
      id?: string;
      uuid?: string;
      host_id?: string;
      topic?: string;
      type?: number;
      start_time?: string;
      duration?: number;
      timezone?: string;
      recording?: {
        id?: string;
        meeting_id?: string;
        recording_files?: Array<{
          id?: string;
          meeting_id?: string;
          recording_start?: string;
          recording_end?: string;
          file_type?: string;
          file_size?: number;
          play_url?: string;
          download_url?: string;
          status?: string;
          recording_type?: string;
        }>;
        share_url?: string;
        password?: string;
      };
      share_url?: string;
      [key: string]: any;
    };
    [key: string]: any;
  };

  @ApiProperty({ description: 'The webhook verification token' })
  @IsOptional()
  @IsString()
  download_token?: string;
}
