{"$schema": "https://json.schemastore.org/nest-cli", "collection": "@nestjs/schematics", "sourceRoot": "src", "outDir": "./dist", "compilerOptions": {"assets": [{"include": "public/**/*", "exclude": "", "outDir": "./dist"}, {"include": "templates/action/**/*.hbs", "exclude": "", "outDir": "./dist"}], "deleteOutDir": true, "watchAssets": true, "plugins": [{"name": "@nestjs/swagger", "options": {"classValidatorShim": true, "introspectComments": true, "dtoFileNameSuffix": [".entity.ts", ".dto.ts"], "controllerFileNameSuffix": [".controller.ts"]}}]}}