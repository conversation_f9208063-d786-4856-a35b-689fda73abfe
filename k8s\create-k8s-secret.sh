#!/bin/bash

# Script to create Kubernetes secret from .env file values
# This script creates a Kubernetes secret with all environment variables

echo "🚀 Creating Kubernetes secret 'nestjs-secrets' from .env file..."

# Check if kubectl is available
if ! command -v kubectl &> /dev/null; then
    echo "❌ kubectl is not installed or not in PATH"
    exit 1
fi

# Delete existing secret if it exists (optional)
echo "🗑️  Removing existing secret if it exists..."
kubectl delete secret nestjs-secrets --ignore-not-found=true

# Create the secret with all environment variables from .env
echo "📝 Creating new secret..."
kubectl create secret generic nestjs-secrets \
  --from-literal=PRODUCTION="true" \
  --from-literal=APP_NAME="TALENCY" \
  --from-literal=COMPANY_NAME="Codinglimits Private Limited" \
  --from-literal=MONGODB_URI="mongodb+srv://talency:<EMAIL>/talency?retryWrites=true&w=majority&appName=Cluster0" \
  --from-literal=SECRET_KEY="MY_SECRET_KEY" \
  --from-literal=TOKEN_EXPIRY_PERIOD="24h" \
  --from-literal=DEFAULT_EMAIL_SENDER='"Alerts | Talsy" <<EMAIL>>' \
  --from-literal=SMTP_HOST_URL="smtp://<EMAIL>:KQOzu*<EMAIL>" \
  --from-literal=SMTP_HOST="us2.smtp.mailhostbox.com" \
  --from-literal=SMTP_PORT="587" \
  --from-literal=SMTP_USERNAME="<EMAIL>" \
  --from-literal=SMTP_PASSWORD="J*%yp^!2" \
  --from-literal=EMAIL_SETTINGS_IGNORE_TLS="false" \
  --from-literal=EMAIL_SETTINGS_SECURE="false" \
  --from-literal=EMAIL_SETTINGS_PREVIEW="true" \
  --from-literal=EMAIL_SETTINGS_TEMPLATES_FOLDER="templates" \
  --from-literal=HOURS_TO_VERIFY="24" \
  --from-literal=HOURS_TO_BLOCK="8" \
  --from-literal=LOGIN_ATTEMPTS_TO_BLOCK="5" \
  --from-literal=API_URL="https://portal.talsy.ai" \
  --from-literal=PRODUCT_NAME="Talsy" \
  --from-literal=PRODUCT_TAGLINE="Talent For All" \
  --from-literal=PRODUCT_WEBSITE_URL="https://codinglimits.com/" \
  --from-literal=COMPANY_WEBSITE_URL="https://codinglimits.com/" \
  --from-literal=COMPANY_CONTACT_EMAIL="<EMAIL>" \
  --from-literal=COMPANY_ADDRESS_L1="256 Chapman Road STE 105-4, Newark, New Castle" \
  --from-literal=COMPANY_ADDRESS_L2="Delaware - 19702, USA" \
  --from-literal=YEAR="2025" \
  --from-literal=DO_SPACES_ENDPOINT="https://blr1.digitaloceanspaces.com" \
  --from-literal=DO_SPACES_BUCKET="gana" \
  --from-literal=DO_SPACES_ACCESS_KEY="DO00ZZLBHMMHNGM3Q6NZ" \
  --from-literal=DO_SPACES_SECRET="XBDOQQmN7D/49GPezsvqS33xMLS0qULRTlHvoCbdsys" \
  --from-literal=WEBSOCKET_SERVER_URL="ws://127.0.0.1:3333" \
  --from-literal=RECONNECT_INTERVAL="2000" \
  --from-literal=REDIS_URL="redis://:TalencyRed001@*************:6379" \
  --from-literal=REDIS_HOST="*************" \
  --from-literal=REDIS_PORT="6379" \
  --from-literal=REDIS_USERNAME="" \
  --from-literal=REDIS_PASSWORD="TalencyRed001" \
  --from-literal=HEROKU="false" \
  --from-literal=ADMIN_EMAIL="<EMAIL>" \
  --from-literal=GOOGLE_CLIENT_ID="85145999566-hgsqtaqfi61p4i95tn4hime06pedjrdv.apps.googleusercontent.com" \
  --from-literal=GOOGLE_CLIENT_SECRET="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" \
  --from-literal=GOOGLE_REDIRECT_URI="https://portal.talsy.ai/settings/integrations" \
  --from-literal=ZOOM_CLIENT_ID="jaiiWJ8PTyeHT52QGJ7SrQ" \
  --from-literal=ZOOM_CLIENT_SECRET="kJqYhf7Ce4XyN8a8juMN3YhffX7pnFUs" \
  --from-literal=ZOOM_REDIRECT_URI="https://portal.talsy.ai/settings/integrations" \
  --from-literal=MS_TEAMS_REDIRECT_URI="https://portal.talsy.ai/settings/integrations" \
  --from-literal=GOOGLE_OAUTH_CLIENT_ID="352966771762-phmr5rkfupou72upte2ac7f98sm55q2o.apps.googleusercontent.com" \
  --from-literal=GOOGLE_OAUTH_CLIENT_SECRET="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" \
  --from-literal=GOOGLE_OAUTH_REDIRECT_URI="https://apis.gana.talency.in/api/auth/google/callback" \
  --from-literal=LINKEDIN_OAUTH_CLIENT_ID="86a4d0btrpqvar" \
  --from-literal=LINKEDIN_OAUTH_CLIENT_SECRET="WPL_AP1.XFaV4HIUAljXyyMD.w7cSTA==" \
  --from-literal=LINKEDIN_OAUTH_REDIRECT_URI="https://apis.gana.talency.in/api/auth/linkedin/callback" \
  --from-literal=JWT_KEY="qwertyuiopasdfghjklzxcvbnm123456" \
  --from-literal=JWT_SECRET="eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************.Pdo0xtdnal6kzE8u-VWjy0SC6sw-wW4doD7kCZk8eHA"

# Check if the secret was created successfully
if [ $? -eq 0 ]; then
    echo "✅ Kubernetes secret 'nestjs-secrets' created successfully!"
    echo ""
    echo "📋 Secret contains the following keys:"
    kubectl get secret nestjs-secrets -o jsonpath='{.data}' | jq -r 'keys[]' 2>/dev/null || echo "   (Install jq to see key names)"
    echo ""
    echo "🚀 You can now apply the deployment:"
    echo "   kubectl apply -f k8s/deployment.yaml"
    echo ""
    echo "📊 Monitor the deployment:"
    echo "   kubectl rollout status deployment/nestjs-app"
    echo "   kubectl get pods -l app=nestjs-app"
    echo "   kubectl logs -f deployment/nestjs-app"
else
    echo "❌ Failed to create Kubernetes secret"
    echo "Please check your kubectl configuration and cluster connectivity"
    exit 1
fi
