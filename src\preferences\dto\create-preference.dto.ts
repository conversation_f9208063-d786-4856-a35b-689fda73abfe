import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ongo<PERSON>d, <PERSON><PERSON>ptional, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreatePreferenceDto {
  @ApiProperty({ type: [String], example: ["c", "java"], description: "List of skills" })
  @IsArray()
  @IsOptional()
  @IsString({ each: true })
  skills: string[] = [];

  @ApiProperty({ type: [String], example: ["hyderabad", "bangalore"], description: "List of locations" })
  @IsArray()
  @IsOptional()
  @IsString({ each: true })
  locations: string[] = [];

  @ApiProperty({ type: [String], example: ["67aa155f7685ada756331fd7"], description: "List of client IDs" })
  @IsArray()
  @IsOptional()
  @IsMongoId({ each: true })
  clients: string[] = [];

}