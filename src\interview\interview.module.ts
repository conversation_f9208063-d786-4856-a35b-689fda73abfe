import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { MongooseModule } from '@nestjs/mongoose';
import { AuthModule } from 'src/auth/auth.module';
import { Calendar, CalendarSchema } from 'src/calendar/schemas/calender.schema';
import {
  EmailTemplate,
  EmailTemplateSchema,
} from 'src/email-template-builder/schemas/email-template-builder.schema';
import { EndpointsRolesModule } from 'src/endpoints-roles/endpoints-roles.module';
import {
  Integration,
  IntegrationSchema,
} from 'src/integrations/schemas/integrations.schema';
import {
  JobApplication,
  JobApplicationSchema,
} from 'src/job-application-form/schemas/job-application.schema';
import { Placeholder, PlaceholderSchema } from 'src/org/schemas/org.schema';
import { Stage, StageSchema } from 'src/stage/schemas/stage.schema';
import { InterviewController } from './interview.controller';
import { InterviewService } from './interview.service';
import { Interview, InterviewSchema } from './schemas/interview.schema';
@Module({
  imports: [
    ConfigModule,
    JwtModule,
    EndpointsRolesModule,
    AuthModule,
    MongooseModule.forFeature([
      { name: Interview.name, schema: InterviewSchema },
      { name: EmailTemplate.name, schema: EmailTemplateSchema },
      { name: Placeholder.name, schema: PlaceholderSchema },
      { name: JobApplication.name, schema: JobApplicationSchema },
      { name: Stage.name, schema: StageSchema },
      { name: Integration.name, schema: IntegrationSchema },
      { name: Calendar.name, schema: CalendarSchema },
    ]),
  ],
  controllers: [InterviewController],
  providers: [InterviewService],
  exports: [MongooseModule, InterviewService],
})
export class InterviewModule {}
