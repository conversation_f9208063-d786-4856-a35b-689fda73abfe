import { Controller, Get, Post, Patch, Param, Delete, Body, Req, UseGuards, Query, BadRequestException, ValidationPipe } from '@nestjs/common';
import { JobAllocationService } from './job-allocation.service';
import { CreateJobAllocationToVendorsDto } from './dto/create-job-allocation-to-vendors.dto';
import { UpdateJobAllocationToVendorsDto } from './dto/update-dto/update-job-allocation-to-vendors.dto';
import { CreateJobAllocationToFreelancersDto } from './dto/create-job-allocation-to-freelancers.dto';
import { UpdateJobAllocationToFreelancersDto } from './dto/update-dto/update-job-allocation-to-freelancers.dto';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery, ApiParam } from '@nestjs/swagger';
import { Roles } from 'src/auth/decorators/roles.decorator';
import { Role } from 'src/auth/enums/role.enum';
import { AuthJwtGuard } from 'src/auth/guards/auth-jwt/auth-jwt.guard';
import { RolesGuard } from 'src/auth/guards/roles/roles.guard';
import { FindAllAssigneeJobAllocationsDto } from './dto/find-all-assignee-job-allocations.dto';
import { FindAllFreelancerJobAllocationsDto } from './dto/find-all-freelancer-job-allocations.dto';
import { validateObjectId } from 'src/utils/validation.utils';
import { FilterByDueDateDto } from './dto/filter-by-due-date.dto';
import { CreateJobAllocationToAssigneesDto } from './dto/create-job-allocation-to-assignees.dto';
import { UpdateJobAllocationToAssigneesDto } from './dto/update-dto/update-job-allocation-to-assignees.dto';
import { UpdateJobAllocationBaseDto } from './dto/update-dto/update-job-allocation-base.dto';
import { CreateJobAllocationBaseDto } from './dto/create-job-allocation.dto';
import { JobService } from 'src/job/job.service';
import { CreateAIRecruiterQADto } from './dto/create-ai-recruiter-qa.dto';
import { CreateJobAllocationToAiRecruiterDto } from './dto/create-job-allocation-to-aiRecruiter.dto';
import { FindAllAiRecruiterJobAllocationsDto } from './dto/find-all-aiRecruiters-job-allocations.dto';
import { UpdateJobAllocationToAiRecruiterDto } from './dto/update-dto/update-job-allocation-to-aiRecruiter.dto';

@ApiTags('Job Allocations')
@Controller()
@UseGuards(AuthJwtGuard, RolesGuard)
@ApiBearerAuth()
@ApiResponse({ status: 401, description: 'Unauthorized - Invalid or missing JWT token' })
@ApiResponse({ status: 403, description: 'Forbidden - User does not have required role' })
@ApiResponse({ status: 500, description: 'Internal server error' })
export class JobAllocationController {
  constructor(private readonly jobAllocationService: JobAllocationService,private readonly jobService: JobService) { }

  // Assignee routes:

  @Post('')
  @ApiResponse({ status: 201, description: 'Job Allocation created successfully' })
  @ApiResponse({ status: 400, description: 'Bad Request - Invalid input data' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role "DeliverManager" can use this end point.' })
  @ApiOperation({ summary: 'Create Job Allocation', description: `Job Allocation not found, This is only accessible for "DeliveryManager".` })
  // @Roles(Role.BUHead, Role.ResourceManager, Role.TeamLead, Role.TeamMember,  Role.AccountManager, Role.DeliveryManager, Role.Vendor, Role.Admin)
  @Roles()
  createJoballocation(@Req() req: any, @Body() createDto: CreateJobAllocationBaseDto) {
    createDto.createdBy = req.user._id;
    return this.jobAllocationService.createJoballocation(createDto);
  }

  @Get(':id')
  @ApiResponse({ status: 200, description: 'Job Allocation retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Job Allocation not found' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role "Admin", "BUHead", "ResourceManager", "AccountManager" and "DeliveryManager" can use this end point.' })
  @ApiOperation({ summary: 'Get Job Allocation by ID', description: `Job Allocation not found, This is only accessible for "DeliveryManager", "Admin", "BUHead", "ResourceManager" and "AccountManager".` })
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.TeamLead, Role.TeamMember,  Role.AccountManager, Role.DeliveryManager, Role.Vendor)
  @Roles()
  async getJoballocation(@Param('id') id: string) {
    const jobAllocationId = validateObjectId(id);
    return this.jobAllocationService.findOneJobAllocation(id);
  }

  @Patch(':id')
  @ApiResponse({ status: 200, description: 'Job Allocation updated successfully' })
  @ApiResponse({ status: 400, description: 'Bad Request - Invalid input data' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role "DeliveryManager" can use this end point.' })
  @ApiResponse({ status: 404, description: 'No job allocations found' })
  @ApiOperation({ summary: 'Update Job Allocation', description: `Job Allocation not found, This is only accessible for "DeliveryManager".` })
  // @Roles(Role.BUHead, Role.ResourceManager, Role.TeamLead, Role.TeamMember,  Role.AccountManager, Role.DeliveryManager, Role.Vendor, Role.Admin)
  @Roles()
  async updateJobAllocation(
    @Param('id') id: string,
    @Body() updateDto: UpdateJobAllocationBaseDto
  ) {
    const jobAllocationId = validateObjectId(id);
    return this.jobAllocationService.updateJoballocation(jobAllocationId, updateDto);
  }

  @Get('jobs/:jobId')
  @ApiResponse({ status: 200, description: 'Successfully retrieved job allocations' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role "Admin", "BUHead", "ResourceManager", "AccountManager" and "DeliveryManager" can use this end point.' })
  @ApiResponse({ status: 404, description: 'No job allocations found' })

  @ApiOperation({ summary: 'Get all Job Allocations for a job', description: `Job Allocation not found, This is only accessible for "DeliveryManager", "Admin", "BUHead", "ResourceManager" and "AccountManager".` })
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.TeamLead, Role.TeamMember,  Role.AccountManager, Role.DeliveryManager,Role.Vendor)
  @Roles()
  async getJobAllocationsByJob(@Param('jobId') jobId: string) {
    const jobObjectId = validateObjectId(jobId);
    return this.jobAllocationService.getJobAllocationsByJob(jobId);
  }

  @Post('assignees')
  @ApiResponse({ status: 201, description: 'Job Allocation for Assignee created successfully' })
  @ApiResponse({ status: 400, description: 'Bad Request - Invalid input data' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role "DeliveryManager" can use this end point.' })
  @ApiOperation({ summary: 'Create Job Allocation for Assignee', description: `Job Allocation not found, This is only accessible for "DeliveryManager".` })
  // @Roles(Role.BUHead, Role.ResourceManager, Role.TeamLead, Role.TeamMember,  Role.AccountManager, Role.DeliveryManager, Role.Vendor, Role.Admin)
  @Roles()
  createAssignee(@Req() req: any, @Body() createDto: CreateJobAllocationToAssigneesDto) {
    createDto.createdBy = req.user._id;
    return this.jobAllocationService.createForAssignees(createDto);
  }

  @Get('assignees')
  @ApiResponse({ status: 200, description: 'Fetched all Job Allocations for Assignees.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role "Admin", "BUHead", "ResourceManager", "AccountManager" and "DeliveryManager" can use this end point.' })
  @ApiOperation({ summary: 'Get all Job Allocations for Assignees.', description: `Job Allocation not found, This is only accessible for "DeliveryManager", "Admin", "BUHead", "ResourceManager" and "AccountManager".` })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number', example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of items per page', example: 10 })
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.TeamLead, Role.TeamMember,  Role.AccountManager, Role.DeliveryManager, Role.Vendor)
  @Roles()
  async findAllAssignees(@Query() query: FindAllAssigneeJobAllocationsDto) {
    return this.jobAllocationService.findAllAssigneeJobAllocations(query);
  }

  @Get('assignees/:id/all')
  @ApiResponse({ status: 200, description: `Fetched all Job Allocations for Assignee.` })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role "Admin", "BUHead", "ResourceManager", "AccountManager" and "DeliveryManager" can use this end point.' })
  @ApiOperation({ summary: `Get all Job Allocations for Assignee.`, description: `Job Allocation not found, This is only accessible for "DeliveryManager", "Admin", "BUHead", "ResourceManager" and "AccountManager".` })
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.TeamLead, Role.TeamMember,  Role.AccountManager, Role.DeliveryManager, Role.Vendor)
  @Roles()
  findAllForAssignee(@Param('id') id: string) {
    const assignId = validateObjectId(id);
    return this.jobAllocationService.findOneAssigneeJobAllocation(id);
  }

  @Patch('assignees/:id')
  @ApiResponse({ status: 200, description: `Job Allocation for Assignee updated successfully.` })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role "DeliveryManager" can use this end point.' })
  @ApiOperation({ summary: `Update Job Allocation for Assignee.`, description: `Job Allocation not found, This is only accessible for "DeliveryManager".` })
  // @Roles(Role.BUHead, Role.ResourceManager, Role.TeamLead, Role.TeamMember,  Role.AccountManager, Role.DeliveryManager, Role.Vendor, Role.Admin)
  @Roles()
  updateAssignee(
    @Param('id') id: string,
    @Body() updateDto: UpdateJobAllocationToAssigneesDto,
  ) {
    const allocationId = validateObjectId(id);
    return this.jobAllocationService.updateAssigneeJobAllocation(allocationId, updateDto);
  }

  @Delete('assignees/:id')
  @ApiResponse({ status: 200, description: `Job Allocation for Assignee deleted successfully.` })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role "DeliveryManager" can use this end point.' })
  @ApiOperation({ summary: `Delete Job Allocation for Assignee.`, description: `Job Allocation not found, This is only accessible for "DeliveryManager".` })
  // @Roles(Role.BUHead, Role.ResourceManager, Role.TeamLead, Role.TeamMember,  Role.AccountManager, Role.DeliveryManager, Role.Vendor, Role.Admin)
  @Roles()
  removeAssignee(@Param('id') id: string) {
    const assignId = validateObjectId(id);
    return this.jobAllocationService.removeAssigneeJobAllocation(assignId);
  }

  @Delete(':id/soft-delete')
  @ApiResponse({ status: 200, description: `Job Allocation for deleted successfully.` })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role "DeliveryManager" can use this end point.' })
  @ApiOperation({ summary: `Delete Job Allocation.`, description: `Job Allocation not found, This is only accessible for "DeliveryManager".` })
  // @Roles(Role.BUHead, Role.ResourceManager, Role.TeamLead, Role.TeamMember,  Role.AccountManager, Role.DeliveryManager, Role.Vendor, Role.Admin)
  @Roles()
  removeJobAllocation(@Req() req: any,@Param('id') id: string) {
    const jobAllocationId = validateObjectId(id);
    return this.jobAllocationService.removeJobAllocation(jobAllocationId,req.user._id);
  }

  @Get('jobs/:jobId/assignee')
  @ApiResponse({ status: 200, description: 'Fetched all Job Allocations for the specified job.' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role "Admin", "BUHead", "ResourceManager", "AccountManager" and "DeliveryManager" can use this end point.' })
  @ApiOperation({ summary: 'Get all Job Allocations for a specific job.', description: `Job Allocation not found, This is only accessible for "DeliveryManager", "Admin", "BUHead", "ResourceManager" and "AccountManager".` })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number', example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of items per page', example: 10 })
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.TeamLead, Role.TeamMember,  Role.AccountManager, Role.DeliveryManager, Role.Vendor)
  @Roles()
  async findJobAssignees(
    @Param('jobId') jobId: string,
    @Query() query: FindAllAssigneeJobAllocationsDto
  ) {
    const jobObjectId = validateObjectId(jobId);
    return this.jobAllocationService.findJobAllocationsForJob(jobId, query);
  }


  @Patch('jobs/:jobId')
  @ApiResponse({ status: 200, description: 'Fetched all Job Allocations for the specified job.' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role "DeliveryManager" can use this end point.' })
  @ApiOperation({ summary: 'Update all Job Allocations for a specific job.', description: `Job Allocation not found, This is only accessible for "DeliveryManager".` })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number', example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of items per page', example: 10 })
  // @Roles(Role.BUHead, Role.ResourceManager, Role.TeamLead, Role.TeamMember,  Role.AccountManager, Role.DeliveryManager, Role.Vendor, Role.Admin)
  @Roles()
  async updateAllAllocations(
    @Req() req: any,
    @Param('jobId') jobId: string,
    @Body() updateDto: UpdateJobAllocationBaseDto
  ) {
    const jobObjectId = validateObjectId(jobId);
    return this.jobAllocationService.updateAllAllocations(jobId, updateDto, req.user._id);
  }



  @Get('assignees/by-date')
  @ApiResponse({ status: 200, description: 'Successfully retrieved assignee job allocations by date' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role "Admin", "BUHead", "ResourceManager", "AccountManager" and "DeliveryManager" can use this end point.' })
  @ApiOperation({ summary: 'Get assignee job allocations by date with navigation', description: `Job Allocation not found, This is only accessible for "DeliveryManager", "Admin", "BUHead", "ResourceManager" and "AccountManager".` })
  @ApiQuery({ name: 'dueDate', required: false, type: String, description: 'Target date in ISO 8601 format', example: '2023-12-01' })
  @ApiQuery({ name: 'dateDirection', required: false, enum: ['prev', 'next'], description: 'Direction for date navigation' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number', example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of items per page', example: 10 })
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.TeamLead, Role.TeamMember,  Role.AccountManager, Role.DeliveryManager, Role.Vendor)
  @Roles()
  async findByDate(@Query() query: FindAllAssigneeJobAllocationsDto) {
    return this.jobAllocationService.findJobAllocationsByDate(query);
  }

  @Get('assignees/:id/by-date-filter')
  @ApiResponse({ status: 200, description: 'Successfully retrieved assignee job allocations by date' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role "Admin", "BUHead", "ResourceManager", "AccountManager" and "DeliveryManager" can use this end point.' })
  @ApiOperation({ summary: 'Get assignee job allocations for a specific date', description: `Job Allocation not found, This is only accessible for "DeliveryManager", "Admin", "BUHead", "ResourceManager" and "AccountManager".` })
  @ApiQuery({ name: 'dueDate', required: true, type: String, description: 'Target date in ISO 8601 format' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number', example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of items per page', example: 10 })
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.TeamLead, Role.TeamMember,  Role.AccountManager, Role.DeliveryManager, Role.Vendor)
  @Roles()
  async findByDueDate(
    @Param('id') id: string,
    @Query() query: FilterByDueDateDto
  ) {
    return this.jobAllocationService.findAssigneeJobAllocationsOfByDueDate(id, query);
  }

  // Freelancer routes
  @Post('freelancer')
  @ApiResponse({ status: 201, description: 'Job made available in freelancer pool successfully' })
  @ApiResponse({ status: 400, description: 'Bad Request - Invalid input data' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role "DeliveryManager" can use this end point.' })
  @ApiOperation({ summary: 'Make job available in freelancer pool with reward', description: `Job Allocation not found, This is only accessible for "DeliveryManager".` })
  // @Roles(Role.BUHead, Role.ResourceManager, Role.TeamLead, Role.TeamMember,  Role.AccountManager, Role.DeliveryManager, Role.Vendor, Role.Admin)
  @Roles()
  createFreelancer(@Req() req: any, @Body() createDto: CreateJobAllocationToFreelancersDto) {
    createDto.createdBy = req.user._id;
    return this.jobAllocationService.createForFreelancers(createDto);
  }

  @Get('freelancer')
  @ApiResponse({ status: 200, description: 'Successfully retrieved jobs from freelancer pool' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role "Admin", "BUHead", "ResourceManager", "AccountManager" and "DeliveryManager" can use this end point.' })
  @ApiOperation({ summary: 'Get all jobs in freelancer pool', description: 'Returns a list of all jobs available in the freelancer pool with their rewards, This is only accessible for "DeliveryManager", "Admin", "BUHead", "ResourceManager" and "AccountManager".' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number (1-based)', example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page (max 100)', example: 10 })
  @ApiQuery({ name: 'isAvailableInPool', required: false, type: Boolean, description: 'Filter by pool availability' })
  @ApiQuery({ name: 'minReward', required: false, type: Number, description: 'Minimum reward amount' })
  @ApiQuery({ name: 'maxReward', required: false, type: Number, description: 'Maximum reward amount' })
  @ApiQuery({ name: 'dueDate', required: true, type: String, description: 'Target date in ISO 8601 format' })
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.TeamLead, Role.TeamMember,  Role.AccountManager, Role.DeliveryManager, Role.Vendor)
  @Roles()
  async findAllFreelancers(@Req() req: any,@Query() query: FindAllFreelancerJobAllocationsDto) {
    // Validate pagination
    const page = query.page || 1;
    const limit = Math.min(query.limit || 10, 100); // Cap at 100 items per page

    if (page < 1) throw new BadRequestException('Page must be greater than 0');
    if (limit < 1) throw new BadRequestException('Limit must be greater than 0');

    return this.jobAllocationService.findAllFreelancerJobAllocations({
      ...query,
      page,
      limit,
    },req.user);
  }

  @Get('freelancer/:id')
  @ApiResponse({ status: 200, description: 'Successfully retrieved job from freelancer pool' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role "Admin", "BUHead", "ResourceManager", "AccountManager" and "DeliveryManager" can use this end point.' })
  @ApiResponse({ status: 404, description: 'No job allocations found' })
  @ApiOperation({ summary: 'Get job details from freelancer pool by ID', description: `Job Allocation not found, This is only accessible for "DeliveryManager", "Admin", "BUHead", "ResourceManager" and "AccountManager".` })
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.TeamLead, Role.TeamMember,  Role.AccountManager, Role.DeliveryManager, Role.Vendor)
  @Roles()
  findOneFreelancer(@Param('id') id: string) {
    const freelancerId = validateObjectId(id);
    return this.jobAllocationService.findOneForFreelancers(freelancerId);
  }

  @Patch('freelancer/:id')
  @ApiResponse({ status: 200, description: 'Job in freelancer pool updated successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role "DeliveryManager" can use this end point.' })
  @ApiResponse({ status: 404, description: 'No job allocations found' })
  @ApiOperation({ summary: 'Update job in freelancer pool (e.g., reward or availability)', description: `Job Allocation not found, This is only accessible for "DeliveryManager".` })
  // @Roles(Role.BUHead, Role.ResourceManager, Role.TeamLead, Role.TeamMember,  Role.AccountManager, Role.DeliveryManager, Role.Vendor, Role.Admin)
  @Roles()
  updateFreelancer(
    @Param('id') id: string,
    @Body() updateDto: UpdateJobAllocationToFreelancersDto,
  ) {
    const freelancerId = validateObjectId(id);
    return this.jobAllocationService.updateFreelancerJobAllocation(freelancerId, updateDto);
  }

  @Delete('freelancer/:id')
  @ApiResponse({ status: 200, description: 'Job removed from freelancer pool successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role "DeliveryManager" can use this end point.' })
  @ApiResponse({ status: 404, description: 'No job allocations found' })
  @ApiOperation({ summary: 'Remove job from freelancer pool', description: `Job Allocation not found, This is only accessible for "DeliveryManager".` })
  // @Roles(Role.BUHead, Role.ResourceManager, Role.TeamLead, Role.TeamMember,  Role.AccountManager, Role.DeliveryManager, Role.Vendor, Role.Admin)
  @Roles()
  removeFreelancer(@Param('id') id: string) {
    const freelancerId = validateObjectId(id);
    return this.jobAllocationService.removeFreelancerJobAllocation(freelancerId);
  }

  @Get('job/:jobId/freelancer')
  @ApiResponse({ status: 200, description: 'Successfully checked job availability in freelancer pool' })
  @ApiResponse({ status: 400, description: 'Invalid job ID format' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role "Admin", "BUHead", "ResourceManager", "AccountManager" and "DeliveryManager" can use this end point.' })
  @ApiOperation({ summary: 'Check if job is available in freelancer pool and get reward details', description: `Job Allocation not found, This is only accessible for "DeliveryManager", "Admin", "BUHead", "ResourceManager" and "AccountManager".` })
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.TeamLead, Role.TeamMember,  Role.AccountManager, Role.DeliveryManager, Role.Vendor)
  @Roles()
  async findJobFreelancers(
    @Param('jobId') jobId: string
  ) {
    const jobObjectId = validateObjectId(jobId);
    return this.jobAllocationService.findJobAllocationsForFreelancers(jobObjectId);
  }

  // Vendor routes
  @Post('vendor')
  @ApiResponse({ status: 201, description: `Job Allocation for Vendor created successfully.` })
  @ApiResponse({ status: 400, description: 'Bad Request - Invalid input data' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role "DeliveryManager" can use this end point.' })
  @ApiOperation({ summary: `Create Job Allocation for Vendor.`, description: `Job Allocation not found, This is only accessible for "DeliveryManager".` })
  // @Roles(Role.BUHead, Role.ResourceManager, Role.TeamLead, Role.TeamMember,  Role.AccountManager, Role.DeliveryManager, Role.Vendor, Role.Admin)
  @Roles()
  createVendor(@Req() req: any, @Body() createDto: CreateJobAllocationToVendorsDto) {
    createDto.createdBy = req.user._id;
    return this.jobAllocationService.createForVendors(createDto);
  }

  @Get('vendor')
  @ApiResponse({ status: 200, description: 'Fetched all Job Allocations for Vendors.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role "Admin", "BUHead", "ResourceManager", "AccountManager" and "DeliveryManager" can use this end point.' })
  @ApiOperation({ summary: 'Get all Job Allocations for Vendors.', description: 'This endpoint returns a list of all Job Allocations for Vendors. This is only accessible for "DeliveryManager", "Admin", "BUHead", "ResourceManager" and "AccountManager".' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number', example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of items per page', example: 10 })
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.TeamLead, Role.TeamMember,  Role.AccountManager, Role.DeliveryManager, Role.Vendor)
  @Roles()
  async findAllVendors(@Query() query: FindAllAssigneeJobAllocationsDto) {
    return this.jobAllocationService.findAllVendorJobAllocations(query);
  }

  @Get('vendors/:id/by-date-filter')
  @ApiResponse({ status: 200, description: 'Successfully retrieved vendor job allocations by date' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role "Admin", "BUHead", "ResourceManager", "AccountManager" and "DeliveryManager" can use this end point.' })
  @ApiOperation({ summary: 'Get vendor job allocations for a specific date', description: `Job Allocation not found, This is only accessible for "DeliveryManager", "Admin", "BUHead", "ResourceManager" and "AccountManager".` })
  @ApiQuery({ name: 'dueDate', required: true, type: String, description: 'Target date in ISO 8601 format' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number', example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of items per page', example: 10 })
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.TeamLead, Role.TeamMember,  Role.AccountManager, Role.DeliveryManager, Role.Vendor)
  @Roles()
  async findByDueDateVendors(
    @Param('id') id: string,
    @Query() query: FilterByDueDateDto
  ) {
    return this.jobAllocationService.findVendorJobAllocationsOfByDueDate(id, query);
  }

  @Get('vendor/:id')
  @ApiResponse({ status: 200, description: `Fetched a Job Allocation for Vendor.` })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role "Admin", "BUHead", "ResourceManager", "AccountManager" and "DeliveryManager" can use this end point.' })
  @ApiOperation({ summary: `Get Job Allocation by ID for Vendor.`, description: `Job Allocation not found, This is only accessible for "DeliveryManager", "Admin", "BUHead", "ResourceManager" and "AccountManager".` })
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.TeamLead, Role.TeamMember,  Role.AccountManager, Role.DeliveryManager, Role.Vendor)
  @Roles()
  findOneVendor(@Param('id') id: string) {
    const vendorId = validateObjectId(id);
    return this.jobAllocationService.findOneForVendors(vendorId);
  }

  @Patch('vendor/:id')
  @ApiResponse({ status: 200, description: `Job Allocation for Vendor updated successfully.` })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role "DeliveryManager" can use this end point.' })
  @ApiOperation({ summary: `Update Job Allocation for Vendor.`, description: `Job Allocation not found, This is only accessible for "DeliveryManager".` })
  // @Roles(Role.BUHead, Role.ResourceManager, Role.TeamLead, Role.TeamMember,  Role.AccountManager, Role.DeliveryManager, Role.Vendor, Role.Admin)
  @Roles()
  updateVendor(
    @Param('id') id: string,
    @Body() updateDto: UpdateJobAllocationToVendorsDto,
  ) {
    const vendorId = validateObjectId(id);
    return this.jobAllocationService.updateVendorJobAllocation(vendorId, updateDto);
  }

  @Delete('vendor/:id')
  @ApiResponse({ status: 200, description: `Job Allocation for Vendor deleted successfully.` })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role "DeliveryManager" can use this end point.' })
  @ApiOperation({ summary: `Delete Job Allocation for Vendor.`, description: `Job Allocation not found, This is only accessible for "DeliveryManager".` })
  // @Roles(Role.BUHead, Role.ResourceManager, Role.TeamLead, Role.TeamMember,  Role.AccountManager, Role.DeliveryManager, Role.Vendor, Role.Admin)
  @Roles()
  removeVendor(@Param('id') id: string) {
    const vendorId = validateObjectId(id);
    return this.jobAllocationService.removeVendorJobAllocation(vendorId);
  }

  @Get('job/:jobId/vendor')
  @ApiResponse({ status: 200, description: 'Fetched all Job Allocations for the specified job.' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role "Admin", "BUHead", "ResourceManager", "AccountManager" and "DeliveryManager" can use this end point.' })
  @ApiOperation({ summary: 'Get all Vendor Job Allocations for a specific job.', description: `Job Allocation not found, This is only accessible for "DeliveryManager", "Admin", "BUHead", "ResourceManager" and "AccountManager".` })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number', example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of items per page', example: 10 })
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.TeamLead, Role.TeamMember,  Role.AccountManager, Role.DeliveryManager, Role.Vendor)
  @Roles()
  async findJobVendors(
    @Param('jobId') jobId: string,
    @Query() query: FindAllAssigneeJobAllocationsDto
  ) {
    const jobObjectId = validateObjectId(jobId);
    return this.jobAllocationService.findJobAllocationsForVendors(jobObjectId, query);
  }

  @Get('vendors/by-date')
  @ApiResponse({ status: 200, description: 'Successfully retrieved vendor job allocations by date' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role "Admin", "BUHead", "ResourceManager", "AccountManager" and "DeliveryManager" can use this end point.' })
  @ApiOperation({ summary: 'Get vendor job allocations by date with navigation', description: `Job Allocation not found, This is only accessible for "DeliveryManager", "Admin", "BUHead", "ResourceManager" and "AccountManager".` })
  @ApiQuery({ name: 'dueDate', required: false, type: String, description: 'Target date in ISO 8601 format', example: '2023-12-01' })
  @ApiQuery({ name: 'dateDirection', required: false, enum: ['prev', 'next'], description: 'Direction for date navigation' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number', example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of items per page', example: 10 })
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.TeamLead, Role.TeamMember,  Role.AccountManager, Role.DeliveryManager, Role.Vendor)
  @Roles()
  async findVendorsByDate(@Query() query: FindAllAssigneeJobAllocationsDto) {
    return this.jobAllocationService.findVendorJobAllocationsByDate(query);
  }

  @Get('analytics/:jobId')
  @ApiResponse({ status: 200, description: 'Successfully retrieved Analytics' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role "Admin", "BUHead", "ResourceManager", "AccountManager" and "DeliveryManager" can use this end point.' })
  @ApiResponse({ status: 404, description: 'No analytics found' })

  @ApiOperation({ summary: 'Get all Analytics for a job', description: `Job Analytics not found, This is only accessible for "DeliveryManager", "Admin", "BUHead", "ResourceManager" and "AccountManager".` })
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.TeamLead, Role.TeamMember,  Role.AccountManager, Role.DeliveryManager,Role.Vendor)
  @Roles()
  async getAnalytics(@Param('jobId') jobId: string) {
    return this.jobAllocationService.getAnalytics(jobId);
  }

  @Patch(':jobId/share-with-freelancers')
    @ApiBearerAuth()
    @UseGuards(AuthJwtGuard, RolesGuard)
    // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.TeamLead, Role.TeamMember, Role.Vendor, Role.JobSeeker, Role.DeliveryManager)
    @Roles()
    @ApiOperation({ summary: 'Toggle shareWithFreelancers', description: `This endpoint toggles the shareWithFreelancers field for a job.` })
    @ApiResponse({ status: 200, description: 'Job field updated successfully.' })
    @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
    @ApiResponse({ status: 403, description: 'Forbidden. Only authorized roles can update this field.' })
    @ApiResponse({ status: 404, description: 'Job not found.' })
    @ApiResponse({ status: 500, description: 'Internal server error.' })
    @ApiParam({ name: 'jobId', description: 'ID of the job.' })
    async toggleShareWithFreelancers(@Param('jobId') jobId: string) {
      const validatedJobId = validateObjectId(jobId);
      return this.jobService.updateShareWithFreelancers(validatedJobId);
    }
  
  
    @Patch(':jobId/share-with-vendors/:vendorId')
    @ApiBearerAuth()
    @UseGuards(AuthJwtGuard, RolesGuard)
    // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.TeamLead, Role.TeamMember, Role.Vendor, Role.JobSeeker, Role.DeliveryManager)
    @Roles()
    @ApiOperation({ summary: 'Toggle shareWithFreelancers', description: `This endpoint toggles the shareWithFreelancers field for a job.` })
    @ApiResponse({ status: 200, description: 'Job field updated successfully.' })
    @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
    @ApiResponse({ status: 403, description: 'Forbidden. Only authorized roles can update this field.' })
    @ApiResponse({ status: 404, description: 'Job not found.' })
    @ApiResponse({ status: 500, description: 'Internal server error.' })
    @ApiParam({ name: 'jobId', description: 'ID of the job.' })
    @ApiParam({ name: 'vendorId', description: 'ID of the vendor.' })
    async toggleShareWithVendors(@Param('jobId') jobId: string, @Param('vendorId') vendorId: string) {
      const validatedJobId = validateObjectId(jobId);
      const validatedVendorId = validateObjectId(vendorId);
      return this.jobService.updateShareWithVendors(validatedJobId, validatedVendorId);
    }
  
    @Post('recruiter-qa')
    @ApiBearerAuth()
    @UseGuards(AuthJwtGuard)
    @Roles()
    async create(@Req() req: any,@Body(ValidationPipe) dto: CreateAIRecruiterQADto) {
      dto.createdBy = req.user._id;
      return this.jobAllocationService.create(dto);
    }
      
  
    @Get(':jobId/recruiter-qa')
    @ApiBearerAuth()
    @UseGuards(AuthJwtGuard)
    @Roles()
    @ApiParam({ name: 'jobId', description: 'ID of the job.' })
    async getByJobId(@Param('jobId') jobId: string) {
      return this.jobAllocationService.getByJobId(jobId);
    }

    // AiRecruiter routes
  @Post('aiRecruiter')
  @ApiResponse({ status: 201, description: 'Job made available in freelancer pool successfully' })
  @ApiResponse({ status: 400, description: 'Bad Request - Invalid input data' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role "DeliveryManager" can use this end point.' })
  @ApiOperation({ summary: 'Make job available in freelancer pool with reward', description: `Job Allocation not found, This is only accessible for "DeliveryManager".` })
  // @Roles(Role.BUHead, Role.ResourceManager, Role.TeamLead, Role.TeamMember,  Role.AccountManager, Role.DeliveryManager, Role.Vendor, Role.Admin)
  @Roles()
  createAiRecruiter(@Req() req: any, @Body() createDto: CreateJobAllocationToAiRecruiterDto) {
    createDto.createdBy = req.user._id;
    return this.jobAllocationService.createForAiRecruiter(createDto);
  }

  @Get('aiRecruiter')
  @ApiResponse({ status: 200, description: 'Successfully retrieved jobs from freelancer pool' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role "Admin", "BUHead", "ResourceManager", "AccountManager" and "DeliveryManager" can use this end point.' })
  @ApiOperation({ summary: 'Get all jobs in freelancer pool', description: 'Returns a list of all jobs available in the freelancer pool with their rewards, This is only accessible for "DeliveryManager", "Admin", "BUHead", "ResourceManager" and "AccountManager".' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number (1-based)', example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page (max 100)', example: 10 })
  @ApiQuery({ name: 'isAvailableInPool', required: false, type: Boolean, description: 'Filter by pool availability' })
  @ApiQuery({ name: 'minReward', required: false, type: Number, description: 'Minimum reward amount' })
  @ApiQuery({ name: 'maxReward', required: false, type: Number, description: 'Maximum reward amount' })
  @ApiQuery({ name: 'dueDate', required: true, type: String, description: 'Target date in ISO 8601 format' })
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.TeamLead, Role.TeamMember,  Role.AccountManager, Role.DeliveryManager, Role.Vendor)
  @Roles()
  async findAllAiRecruiter(@Req() req: any,@Query() query: FindAllAiRecruiterJobAllocationsDto) {
    // Validate pagination
    const page = query.page || 1;
    const limit = Math.min(query.limit || 10, 100); // Cap at 100 items per page

    if (page < 1) throw new BadRequestException('Page must be greater than 0');
    if (limit < 1) throw new BadRequestException('Limit must be greater than 0');

    return this.jobAllocationService.findAllAiRecruiterJobAllocations({
      ...query,
      page,
      limit
    },req?.user);
  }

  @Get('aiRecruiter/:id')
  @ApiResponse({ status: 200, description: 'Successfully retrieved job from freelancer pool' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role "Admin", "BUHead", "ResourceManager", "AccountManager" and "DeliveryManager" can use this end point.' })
  @ApiResponse({ status: 404, description: 'No job allocations found' })
  @ApiOperation({ summary: 'Get job details from freelancer pool by ID', description: `Job Allocation not found, This is only accessible for "DeliveryManager", "Admin", "BUHead", "ResourceManager" and "AccountManager".` })
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.TeamLead, Role.TeamMember,  Role.AccountManager, Role.DeliveryManager, Role.Vendor)
  @Roles()
  findOneAiRecruiter(@Param('id') id: string) {
    const allocationId = validateObjectId(id);
    return this.jobAllocationService.findOneForAiRecruiter(allocationId);
  }

  @Patch('aiRecruiter/:id')
  @ApiResponse({ status: 200, description: 'Job in freelancer pool updated successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role "DeliveryManager" can use this end point.' })
  @ApiResponse({ status: 404, description: 'No job allocations found' })
  @ApiOperation({ summary: 'Update job in freelancer pool (e.g., reward or availability)', description: `Job Allocation not found, This is only accessible for "DeliveryManager".` })
  // @Roles(Role.BUHead, Role.ResourceManager, Role.TeamLead, Role.TeamMember,  Role.AccountManager, Role.DeliveryManager, Role.Vendor, Role.Admin)
  @Roles()
  updateAiRecruiter(
    @Param('id') id: string,
    @Body() updateDto: UpdateJobAllocationToAiRecruiterDto,
  ) {
    const allocationId = validateObjectId(id);
    return this.jobAllocationService.updateAiRecruiterJobAllocation(allocationId, updateDto);
  }

  @Delete('aiRecruiter/:id')
  @ApiResponse({ status: 200, description: 'Job removed from freelancer pool successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role "DeliveryManager" can use this end point.' })
  @ApiResponse({ status: 404, description: 'No job allocations found' })
  @ApiOperation({ summary: 'Remove job from freelancer pool', description: `Job Allocation not found, This is only accessible for "DeliveryManager".` })
  // @Roles(Role.BUHead, Role.ResourceManager, Role.TeamLead, Role.TeamMember,  Role.AccountManager, Role.DeliveryManager, Role.Vendor, Role.Admin)
  @Roles()
  removeAiRecruiter(@Param('id') id: string) {
    const allocationId = validateObjectId(id);
    return this.jobAllocationService.removeAiRecruiterJobAllocation(allocationId);
  }

  @Get('job/:jobId/aiRecruiter')
  @ApiResponse({ status: 200, description: 'Successfully checked job availability in freelancer pool' })
  @ApiResponse({ status: 400, description: 'Invalid job ID format' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role "Admin", "BUHead", "ResourceManager", "AccountManager" and "DeliveryManager" can use this end point.' })
  @ApiOperation({ summary: 'Check if job is available in freelancer pool and get reward details', description: `Job Allocation not found, This is only accessible for "DeliveryManager", "Admin", "BUHead", "ResourceManager" and "AccountManager".` })
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.TeamLead, Role.TeamMember,  Role.AccountManager, Role.DeliveryManager, Role.Vendor)
  @Roles()
  async findJobAiRecruiter(
    @Param('jobId') jobId: string
  ) {
    const jobObjectId = validateObjectId(jobId);
    return this.jobAllocationService.findJobAllocationsForAiRecruiter(jobObjectId);
  }

}
