import { Injectable, BadRequestException, NotFoundException, Logger } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model, Types } from "mongoose";

import { UpdateCustomFieldDto } from "./dto/update-custom-field.dto";
import { CreateCustomFieldDto } from "./dto/create-custom-field.dto";
import { CustomField, CustomFieldDocument } from "./schemas/custom-fields.schema";
import { Role } from "src/auth/enums/role.enum";
import { OnEvent } from "@nestjs/event-emitter";

@Injectable()
export class CustomFieldsService {
  private readonly logger = new Logger(CustomFieldsService.name);

  constructor(
    @InjectModel(CustomField.name) private readonly customFieldModel: Model<CustomFieldDocument>
  ) { }

  // Add a new custom field
  async addCustomFields(createCustomFieldDto: CreateCustomFieldDto, user: any) {
    try {
      this.logger.log(`Adding new custom fields: ${JSON.stringify(createCustomFieldDto)}`);

      // Extract names array
      const names = createCustomFieldDto.name;
      if (!Array.isArray(names) || names.length === 0) {
        throw new BadRequestException('Names must be a non-empty array');
      }

      let existingFields: CustomFieldDocument[] = [];
      let newFields: CustomFieldDocument[] = [];

      if (user.roles.includes(Role.SuperAdmin)) {
        // Retrieve all default fields
        existingFields = await this.customFieldModel.find({ isDefault: true }).exec();
        createCustomFieldDto.isDefault = true;
      } else if (user.org) {
        // Retrieve all fields for the organization
        existingFields = await this.customFieldModel.find({ org: user.org._id }).exec();
        createCustomFieldDto.org = user.org._id;
      }

      // Convert existing names to a Set for quick lookup
      const existingNames = new Set(existingFields.map(field => field.name));

      // Filter out duplicates and create new entries
      const uniqueNames = names.filter(name => !existingNames.has(name));

      if (uniqueNames.length === 0) {
        throw new BadRequestException('Fields already exist');
      }

      newFields = uniqueNames.map(name => new this.customFieldModel({
        ...createCustomFieldDto,
        name
      }));

      return await this.customFieldModel.insertMany(newFields);
    } catch (error) {
      throw new BadRequestException(`${error.message}`);
    }
  }


  // Update an existing custom field
  async updateCustomField(fieldId: string, updateCustomFieldDto: UpdateCustomFieldDto) {
    try {
      const updatedField = await this.customFieldModel.findByIdAndUpdate(
        fieldId,
        updateCustomFieldDto,
        { new: true }
      );

      if (!updatedField) {
        throw new NotFoundException("Custom field not found");
      }

      return updatedField;
    } catch (error) {
      throw new BadRequestException(`Error updating custom field: ${error.message}`);
    }
  }

  // Delete a custom field
  async deleteCustomField(fieldId: string) {
    try {
      const deletedField = await this.customFieldModel.findByIdAndDelete(fieldId);
      if (!deletedField) {
        throw new NotFoundException("Custom field not found");
      }
      return deletedField;
    } catch (error) {
      throw new BadRequestException(`Error deleting custom field: ${error.message}`);
    }
  }

  // Get all custom fields for an organization
  async getAllCustomFields(orgId?: string, isDefault?: boolean) {
    try {
      const filter: any = {};
      if (orgId) {
        filter.org = orgId;
      }

      if (isDefault !== undefined) {
        filter.isDefault = isDefault;
      }

      this.logger.log(JSON.stringify(filter));

      return await this.customFieldModel.find(filter).sort({ createdAt: -1 }).exec();
    } catch (error) {
      throw new BadRequestException(`Error retrieving custom fields: ${error.message}`);
    }
  }

  // Get a specific custom field by ID
  async getCustomField(fieldId: string) {
    try {
      const field = await this.customFieldModel.findById(fieldId).exec();
      if (!field) {
        throw new NotFoundException("Custom field not found");
      }
      return field;
    } catch (error) {
      throw new BadRequestException(`Error retrieving custom field: ${error.message}`);
    }
  }

  @OnEvent('org.cloneDefaultCustomFieldsForOrg')
  async cloneDefaultCustomFields(payload: any): Promise<void> {

    try {
      const { org } = payload
      // Check if the org already has custom fields
      const existingCustomFields = await this.customFieldModel.find({ org }).lean();

      if (existingCustomFields.length > 0) {
        console.log(`Org ${org} already has custom fields. No cloning needed.`);
        return;
      }

      // Fetch all default custom fields
      const defaultCustomFields = await this.customFieldModel.find({ isDefault: true }).lean();

      if (!defaultCustomFields.length) {
        console.log('No default custom fields found.');
        return;
      }

      // Prepare new custom fields by removing _id and setting orgId & isDefault: false
      const fieldsToClone = defaultCustomFields.map((field: any) => ({
        ...field,
        _id: undefined,
        org,
        isDefault: false,
        createdAt: new Date(),
        updatedAt: new Date(),
      }));

      // Insert cloned fields into the database
      await this.customFieldModel.insertMany(fieldsToClone);
      console.log(`Cloned ${fieldsToClone.length} custom fields for orgId: ${org}`);
    } catch (error) {
      console.error('Error cloning default custom fields:', error);
      throw error;
    }
  }

}
