import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { EndpointsRoles } from './schemas/endpoints-roles.schema';
import { CreateEndpointsRolesDto } from './dto/create-endpoints-role.dto';
import { UpdateEndpointsRolesDto } from './dto/update-endpoints-role.dto';
import { EndpointPermission } from 'src/endpoint-permissions/schemas/endpointpermissions.schema';
import { parse } from 'url';
import { EndpointsUsers } from './schemas/endpoints-users.schema';
import { BasicUser } from 'src/user/schemas/basic-user.schema';

@Injectable()
export class EndpointsRolesService {
  constructor(@InjectModel(EndpointsRoles.name) private readonly endpointsRolesModel: Model<EndpointsRoles>,
    @InjectModel(EndpointsUsers.name) private readonly endpointsUsersModel: Model<EndpointsUsers>,
    @InjectModel(BasicUser.name) private basicUserModel: Model<BasicUser>,
    @InjectModel(EndpointPermission.name) private readonly endpointsPermissionModel: Model<EndpointPermission>
  ) { }

  async create(createEndpointsRolesDto: CreateEndpointsRolesDto): Promise<EndpointsRoles> {
    try {

      const { endPoint, org, roles = [] } = createEndpointsRolesDto;

      // Ensure roles are unique
      const uniqueRoles = [...new Set(roles)];

      createEndpointsRolesDto = { ...createEndpointsRolesDto, roles: uniqueRoles };

      // Check if the endpoint and org combination already exists
      const existingRecord = await this.endpointsRolesModel.findOne({ endPoint: endPoint, org: org }).exec();
      if (existingRecord) throw new BadRequestException('Record already exists');
      const record = new this.endpointsRolesModel(createEndpointsRolesDto);
      return await record.save();
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }


  async findAll(page: number = 1, limit: number = 10): Promise<EndpointsRoles[]> {
    try {
      const skip = (page - 1) * limit;
      return await this.endpointsRolesModel.find()
        .populate({ path: 'org', select: '_id title description' })
        .populate({ path: 'endPoint', select: '_id controller path method isPublic' })
        .skip(skip).limit(limit).exec();
    } catch (error) {
      throw new BadRequestException(`Error retrieving records: ${error.message}`);
    }
  }

  async findOne(endpoinsRolesId: Types.ObjectId): Promise<EndpointsRoles> {
    try {
      const record = await this.endpointsRolesModel.findById(endpoinsRolesId)
        .populate({ path: 'org', select: '_id title description' })
        .populate({ path: 'endPoint', select: '_id controller path method isPublic' })
        .exec();
      if (!record) throw new NotFoundException('Record not found');
      return record;
    } catch (error) {
      throw new BadRequestException(`Error retrieving record: ${error.message}`);
    }
  }

  async update(endpoinsRolesId: Types.ObjectId, updateDto: UpdateEndpointsRolesDto): Promise<EndpointsRoles> {
    try {
      if (updateDto?.roles && Array.isArray(updateDto.roles)) {
        const { roles = [] } = updateDto;

        // Ensure roles are unique
        const uniqueRoles = [...new Set(roles)];
        updateDto.roles = uniqueRoles;
      }

      // Update record
      const updatedRecord = await this.endpointsRolesModel.findByIdAndUpdate(
        endpoinsRolesId,
        updateDto,
        { new: true }
      )
        .populate({ path: 'org', select: '_id title description' })
        .populate({ path: 'endPoint', select: '_id controller path method isPublic' })
        .exec();

      if (!updatedRecord) throw new NotFoundException('Record not found');
      return updatedRecord;
    } catch (error) {
      throw new BadRequestException(`Error updating record: ${error.message}`);
    }
  }

  async remove(endpoinsRolesId: Types.ObjectId) {
    try {
      const deletedRecord = await this.endpointsRolesModel.findByIdAndDelete(endpoinsRolesId).exec();
      if (!deletedRecord) throw new NotFoundException('Record not found');
      return { message: 'Record deleted successfully' };
    } catch (error) {
      throw new BadRequestException(`Error deleting record: ${error.message}`);
    }
  }

  async getEndpointRoles(method: string, path: string, orgId?: string): Promise<string[]> {
    const parsedUrl = parse(path, true); // Parse the URL
    const cleanPath = parsedUrl.pathname || path; // Extract the path without query params
    // console.log(method)
    // console.log(cleanPath)
    // console.log(orgId)

    // Fetch all permissions for the given method
    // function pathToRegex(storedPath: string): RegExp {
    //   return new RegExp(`^${storedPath.replace(/\{[^}]+\}/g, "([^/]+)")}$`);
    // }

    function pathToRegex(storedPath: string): RegExp {
      return new RegExp(`^${storedPath.replace(/\{[^}]+\}/g, "([a-fA-F0-9]{24})")}$`);
    }


    const allPermissions = await this.endpointsPermissionModel.find({ method: method }).lean();
    const regexPath = new RegExp(pathToRegex(cleanPath));
    // console.log(regexPath)

    const endPoint = allPermissions.find(ep => pathToRegex(ep.path).test(cleanPath));
    if (!endPoint) {
      console.log("No matching endpoint found.");
      return [];
    }

    // const endPoint = await this.endpointsPermissionModel.findOne({ method: method, path: cleanPath })
    // console.log(endPoint)
    let endpointRoles: any;
    if (orgId) {
      endpointRoles = await this.endpointsRolesModel
        .findOne({ endPoint: endPoint?._id.toString(), org: orgId })
        .lean();
    }
    else {
      endpointRoles = await this.endpointsRolesModel
        .findOne({ endPoint: endPoint?._id.toString(), org: { $exists: false } })
        .lean();
    }
    // console.log(endpointRoles)


    if (endpointRoles?.roles)
      return endpointRoles.roles;
    else
      return []
  }

  async getEndpointRolesUser(method: string, path: string, userId: string, orgId: string): Promise<boolean> {
    const parsedUrl = parse(path, true); // Parse the URL
    const cleanPath = parsedUrl.pathname || path; // Extract the path without query params
    // console.log(method)
    // console.log(cleanPath)
    // console.log(orgId)

    // Fetch all permissions for the given method
    // function pathToRegex(storedPath: string): RegExp {
    //   return new RegExp(`^${storedPath.replace(/\{[^}]+\}/g, "([^/]+)")}$`);
    // }

    function pathToRegex(storedPath: string): RegExp {
      return new RegExp(`^${storedPath.replace(/\{[^}]+\}/g, "([a-fA-F0-9]{24})")}$`);
    }


    const allPermissions = await this.endpointsPermissionModel.find({ method: method }).lean();
    const regexPath = new RegExp(pathToRegex(cleanPath));
    // console.log(regexPath)

    const endPoint = allPermissions.find(ep => pathToRegex(ep.path).test(cleanPath));
    if (!endPoint) {
      console.log("No matching endpoint found.");
      return false;
    }

    // const endPoint = await this.endpointsPermissionModel.findOne({ method: method, path: cleanPath })
    // console.log(endPoint)
    // let endpointRoles: any;
    // if (orgId) {
    //   endpointRoles = await this.endpointsRolesModel
    //     .findOne({ endPoint: endPoint?._id.toString(), org: orgId ,userId : { $exists: false }})
    //     .lean();
    // }
    // else {
    const endpointRoles = await this.endpointsUsersModel
      .findOne({ endPoint: endPoint?._id.toString(), userId: userId, org: orgId })
      .lean();
    // }
    // console.log(endpointRoles)


    if (endpointRoles)
      return true
    else
      return false
  }

  async getUserData(userId: string){
    const user = await this.basicUserModel.findById(userId).exec();
    // console.log(user)
    if (!user) {
      throw new NotFoundException('User not found');
    }
   return user;
  }


}

