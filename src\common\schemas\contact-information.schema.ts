
import { Prop, SchemaFactory, Schema } from "@nestjs/mongoose";
import { HydratedDocument } from "mongoose";
import { AddressInformation } from "./address-information.schema";

  
export type ContactInformationDocument = HydratedDocument<ContactInformation>;

@Schema({ _id: false, timestamps: true }) // _id:false is optional
export class ContactInformation {

  @Prop({ type: String, required: false, trim: true, lowercase: true })
  contactEmail?: string;

  @Prop({ type: String, required: false, trim: true })
  contactNumber?: string;

  @Prop({ type: Boolean, required: false, default: false })
  isPrimary?: boolean;

}

export const ContactInformationSchema = SchemaFactory.createForClass(ContactInformation);
