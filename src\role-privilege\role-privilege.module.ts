import { Module } from '@nestjs/common';
import { RolePrivilegeService } from './role-privilege.service';
import { RolePrivilegeController } from './role-privilege.controller';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { CommonModule } from 'src/common/common.module';
import { getConnectionToken, MongooseModule } from '@nestjs/mongoose';
import { RolePrivilege } from './entities/role-privilege.entity';
import { RolePrivilegeSchema } from './schemas/role-privilege.schema';
import { EndpointsRolesModule } from 'src/endpoints-roles/endpoints-roles.module';
@Module({
  imports: [
    ConfigModule,
    JwtModule, EndpointsRolesModule,
    CommonModule,

    MongooseModule.forFeatureAsync([
      {
        name: RolePrivilege.name,
        imports: [ConfigModule],
        useFactory: (configService: ConfigService) => {
          const schema = RolePrivilegeSchema;
          return schema;
        },
        inject: [getConnectionToken(), ConfigService],

      },
    ]),

  ],
  controllers: [RolePrivilegeController],
  providers: [RolePrivilegeService],
  exports: [RolePrivilegeService],
})
export class RolePrivilegeModule {}
