import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Req } from '@nestjs/common';
import { JobLocationService } from './job-location.service';
import { CreateJobLocationDto } from './dto/create-job-location.dto';
import { UpdateJobLocationDto } from './dto/update-job-location.dto';
import { ApiBearerAuth, ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { RolesGuard } from 'src/auth/guards/roles/roles.guard';
import { AuthJwtGuard } from 'src/auth/guards/auth-jwt/auth-jwt.guard';
import { Roles } from 'src/auth/decorators/roles.decorator';
import { Role } from 'src/auth/enums/role.enum';
import { validateObjectId } from 'src/utils/validation.utils';
import { CreateJobsLocationDto } from './dto/create-jobs-location.dto';
import { UpdateJobsLocationDto } from './dto/update-jobs-location.dto';

@Controller('')
@ApiTags('Job-Locations')
export class JobLocationController {
  constructor(private readonly jobLocationService: JobLocationService) { }


  @Post()
  @ApiOperation({ summary: 'Creates a new job location', description: `This endpoint allows you to create a new job location. This is accessible only for "${Role.Admin}".` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.AccountManager, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.BUHead, Role.Recruiter, Role.Vendor, Role.JobSeeker, Role.Freelancer, Role.TeamMember)
  @Roles()
  @ApiResponse({ status: 201, description: 'Job location is saved.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Data ' })
  @ApiResponse({ status: 401, description: 'Unauthorized ' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role admin can only use this end point.' })
  create(@Req() req: any, @Body() createJobLocationDto: CreateJobLocationDto) {
    createJobLocationDto.orgId = req.user.org._id;
    return this.jobLocationService.create(createJobLocationDto);
  }

  @Get()
  @ApiOperation({ summary: 'Retrieve all job locations', description: `This endpoint returns a list of all job locations. This is accessible for everyone.` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.AccountManager, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.BUHead, Role.Recruiter, Role.Vendor, Role.JobSeeker, Role.Freelancer, Role.TeamMember)
  @Roles()
  @ApiResponse({ status: 200, description: 'All job locations are retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized ' })
  // @ApiResponse({ status: 403, description: 'Forbidden, user with role super-admin, admin and sales-rep can only use this end point.' })
  findAll() {
    return this.jobLocationService.findAll();
  }

  @Get('byOrgId')
  @ApiOperation({ summary: 'Retrieve all job locations', description: `This endpoint returns a list of all job locations. This is accessible for everyone.` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.AccountManager, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.BUHead, Role.Recruiter, Role.Vendor, Role.JobSeeker, Role.Freelancer, Role.TeamMember)
  @Roles()
  @ApiResponse({ status: 200, description: 'All job locations are retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized ' })
  // @ApiResponse({ status: 403, description: 'Forbidden, user with role super-admin, admin and sales-rep can only use this end point.' })
  findJobLocationsByOrgId(@Req() req: any) {
    return this.jobLocationService.findLocationByOrgId(req.user.org._id);
  }

  @Get(':jobLocationId')
  @ApiOperation({ summary: 'Retrieve a job location by Id', description: `This endpoint returns a job location by its Id. This is accessible only for everyone.` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.AccountManager, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.BUHead, Role.Recruiter, Role.Vendor, Role.JobSeeker, Role.TeamMember)
  @Roles()
  @ApiResponse({ status: 200, description: 'job location is retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized ' })
  // @ApiResponse({ status: 403, description: 'Forbidden, user with role super-admin, admin and sales-rep can only use this end point.' })
  findOne(@Param('jobLocationId') jobLocationId: string) {
    const objId = validateObjectId(jobLocationId);
    return this.jobLocationService.findOne(objId);
  }

  @Patch(':jobLocationId')
  @ApiOperation({ summary: 'Update a job location by Id', description: `This endpoint updates a job location by Id. This is accessible only for "${Role.Admin}".` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.TeamMember)
  @Roles()
  @ApiResponse({ status: 200, description: 'Job location is updated.' })
  @ApiResponse({ status: 401, description: 'Unauthorized ' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role admin can only use this end point.' })
  @ApiResponse({ status: 404, description: 'Job location not found.' })
  @ApiParam({ name: 'jobLocationId', description: 'ID of the job location' })
  update(@Param('jobLocationId') jobLocationId: string, @Req() req: any, @Body() updateJobLocationDto: UpdateJobLocationDto) {
    const objId = validateObjectId(jobLocationId);
    updateJobLocationDto.orgId = req.user.org._id;
    return this.jobLocationService.update(objId, updateJobLocationDto);
  }

  @Delete(':jobLocationId/hard-delete')
  @ApiOperation({ summary: 'Permanently delete a job location by Id', description: `This endpoint permanently deletes a job location by Id. This is accessible only for "${Role.Admin}".` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.TeamMember)
  @Roles()
  @ApiResponse({ status: 200, description: 'Job location deleted permanently.' })
  @ApiResponse({ status: 401, description: 'Unauthorized ' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role admin can only use this end point.' })
  @ApiResponse({ status: 404, description: 'Job location not found.' })
  @ApiParam({ name: 'jobLocationId', description: 'ID of the Job location' })
  remove(@Param('jobLocationId') jobLocationId: string) {
    const objId = validateObjectId(jobLocationId);
    return this.jobLocationService.remove(objId);
  }

  // ✅ Create Job Location
  @Post('jobs-location')
  @ApiOperation({
    summary: 'Create a new job location',
    description: `This endpoint allows creating a new job location. Allowed roles: Admin, AccountManager, ResourceManager, DeliveryManager, TeamLead, BUHead, Recruiter, Vendor, JobSeeker, Freelancer, TeamMember.`
  })
  // @Roles(Role.Admin, Role.AccountManager, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.BUHead, Role.Recruiter, Role.Vendor, Role.JobSeeker, Role.Freelancer, Role.TeamMember)
  @Roles()
  @ApiResponse({ status: 201, description: 'Job location created successfully.' })
  @ApiResponse({ status: 400, description: 'Bad Request: Invalid data.' })
  @ApiResponse({ status: 401, description: 'Unauthorized: Invalid credentials.' })
  @ApiResponse({ status: 403, description: 'Forbidden: User does not have the required role.' })
  createJobsLocation(@Req() req: any, @Body() createJobLocationDto: CreateJobsLocationDto) {
    // createJobLocationDto.orgId = req.user.org._id;
    return this.jobLocationService.createJobsLocation(createJobLocationDto);
  }

  // ✅ Get all Job Locations
  @Get('jobs-location')
  @ApiOperation({ summary: 'Retrieve all job locations', description: 'Returns a list of all job locations.' })
  // @Roles(Role.Admin, Role.AccountManager, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.BUHead, Role.Recruiter, Role.Vendor, Role.JobSeeker, Role.Freelancer, Role.TeamMember)
  @Roles()
  @ApiResponse({ status: 200, description: 'Successfully retrieved job locations.' })
  findAllJobsLocation() {
    return this.jobLocationService.findAllJobsLocation();
  }

  // ✅ Get a Job Location by ID
  @Get(':id/jobs-location')
  @ApiOperation({ summary: 'Retrieve a job location by ID', description: 'Returns details of a specific job location.' })
  // @Roles(Role.Admin, Role.AccountManager, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.BUHead, Role.Recruiter, Role.Vendor, Role.JobSeeker, Role.Freelancer, Role.TeamMember)
  @Roles()
  @ApiResponse({ status: 200, description: 'Successfully retrieved job location.' })
  @ApiResponse({ status: 404, description: 'Job location not found.' })
  findOneJobsLocation(@Param('id') id: string) {
    return this.jobLocationService.findOneJobsLocation(id);
  }

  // ✅ Update Job Location
  @Patch(':id/jobs-location')
  @ApiOperation({ summary: 'Update a job location', description: 'Updates details of a specific job location.' })
  // @Roles(Role.Admin, Role.AccountManager, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.BUHead, Role.Recruiter, Role.Vendor, Role.JobSeeker, Role.Freelancer, Role.TeamMember)
  @Roles()
  @ApiResponse({ status: 200, description: 'Job location updated successfully.' })
  @ApiResponse({ status: 400, description: 'Bad Request: Invalid update data.' })
  @ApiResponse({ status: 404, description: 'Job location not found.' })
  updateJobsLocation(@Param('id') id: string, @Body() updateJobLocationDto: UpdateJobsLocationDto) {
    return this.jobLocationService.updateJobsLocation(id, updateJobLocationDto);
  }

  // ✅ Delete Job Location
  @Delete(':id/jobs-location')
  @ApiOperation({ summary: 'Delete a job location', description: 'Removes a specific job location from the database.' })
  // @Roles(Role.Admin, Role.AccountManager, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.BUHead, Role.Recruiter, Role.Vendor, Role.JobSeeker, Role.Freelancer, Role.TeamMember)
  @Roles()
  @ApiResponse({ status: 200, description: 'Job location deleted successfully.' })
  @ApiResponse({ status: 404, description: 'Job location not found.' })
  removeJobsLocation(@Param('id') id: string) {
    return this.jobLocationService.removeJobsLocation(id);
  }
}
