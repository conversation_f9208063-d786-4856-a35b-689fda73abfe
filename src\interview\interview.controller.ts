import { <PERSON>, Get, Post, Body, Patch, Param, Delete, Logger, UseGuards, Req, Query, BadRequestException } from '@nestjs/common';
import { InterviewService } from './interview.service';
import { CreateInterviewDto } from './dto/create-interview.dto';
import { UpdateInterviewDto } from './dto/update-interview.dto';
import { ApiBearerAuth, ApiOperation, ApiParam, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { AuthJwtGuard } from 'src/auth/guards/auth-jwt/auth-jwt.guard';
import { RolesGuard } from 'src/auth/guards/roles/roles.guard';
import { Roles } from 'src/auth/decorators/roles.decorator';
import { Role } from 'src/auth/enums/role.enum';
import { validateObjectId } from 'src/utils/validation.utils';

@Controller('')
@ApiTags('Interviews')
export class InterviewController {

  private readonly logger = new Logger(InterviewService.name);
  constructor(private readonly interviewService: InterviewService) { }

  @Post()
  @ApiResponse({ status: 201, description: 'Interview is schedule.' })
  @ApiResponse({ status: 401, description: 'Interview not logged in.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Data.' })
  @ApiResponse({ status: 403, description: 'Forbidden only user with "BUHead", "ResourceManager", "DeliveryManager", "TeamLead", "AccountManager", "Recuriter", and "TeamMember" can only use this end point.' })
  @ApiOperation({ summary: 'Schedule a new interview', description: `This endpoint allows you to schedule a interview.This is accessible only for "BUHead", "ResourceManager", "DeliveryManager", "TeamLead", and "AccountManager".` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager, Role.Recruiter, Role.TeamMember)
  @Roles()
  create(@Req() req: any, @Body() createInterviewDto: CreateInterviewDto) {
    return this.interviewService.create(req.user, createInterviewDto);
  }

  @Get()
  @ApiResponse({ status: 200, description: 'Interviews retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  // @ApiResponse({ status: 403, description: 'Forbidden, user with role admin and salesrep can only use this end point.' })
  @ApiOperation({ summary: 'Retrieve all interviews', description: `This endpoint returns a list of all interviews. This is Accessible for everyone` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager, Role.Recruiter, Role.TeamMember)
  @Roles()
  findAll() {
    return this.interviewService.findAll();
  }

  @Get(':interviewId')
  @ApiResponse({ status: 200, description: 'Interview is retrieved.' })
  @ApiResponse({ status: 404, description: 'Interview not found.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  @ApiOperation({ summary: 'Retrieve all interviews with their Id', description: `This endpoint returns a list of all interviews. This is Accessible for everyone` })
  // @ApiResponse({ status: 403, description: 'Forbidden user with role admin can only use this end point.' })
  @ApiParam({ name: 'interviewId', description: 'Id of the interview.' })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager, Role.Recruiter, Role.TeamMember)
  @Roles()
  findOne(@Param('interviewId') interviewId: string) {
    const objId = validateObjectId(interviewId);
    return this.interviewService.findOne(objId);
  }

  @Get('/reject/template')
  @ApiOperation({
    summary: 'Get rejection email template',
    description: 'Fetches the email template for interview rejection with dynamic placeholders replaced.',
  })
  @ApiResponse({ status: 200, description: 'Email template content fetched successfully.' })
  @ApiResponse({ status: 404, description: 'Job application not found.' })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager, Role.Recruiter, Role.TeamMember)
  @Roles()
  async getRejectionEmailTemplate( @Req() req: any, @Query('jobApplicationId') jobApplicationId: string) {
    const objId = validateObjectId(jobApplicationId);
    if (!objId) {
      throw new BadRequestException('Job ID is required to process this request.');
    }

    return this.interviewService.getRejectionEmailTemplate(req.user, jobApplicationId);
  }

  @Patch('/reject')
  @ApiResponse({ status: 200, description: 'Interview rejection processed successfully.' })
  @ApiResponse({ status: 404, description: 'Job application not found.' })
  @ApiResponse({ status: 401, description: 'Unauthorized access.' })
  @ApiResponse({ status: 403, description: 'Forbidden only user with "BUHead", "ResourceManager", "DeliveryManager", "TeamLead", "AccountManager", "Recuriter", and "TeamMember" can access this endpoint.' })
  @ApiOperation({
    summary: 'Reject an interview',
    description: 'This endpoint allows you to reject an interview for a given job application. This is Accessible only for  "BUHead", "ResourceManager", "DeliveryManager", "TeamLead", and "AccountManager" .'
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager, Role.Recruiter, Role.TeamMember)
  @Roles()
  rejectInterview(
    @Req() req: any,
    @Query('jobApplicationId') jobApplicationId: string,
  ) {
    const objId = validateObjectId(jobApplicationId);
    if (!objId) {
      throw new BadRequestException('Job ID is required to process this request.');
    }
    return this.interviewService.rejectInterview(req.user, jobApplicationId);
  }


  @Patch(':interviewId')
  @ApiParam({ name: 'interviewId', description: 'Id of the interview.' })
  @ApiResponse({ status: 200, description: 'Interview updated.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden only user with "BUHead", "ResourceManager", "DeliveryManager", "TeamLead", "AccountManager", "Recuriter", and "TeamMember" can access this endpoint.' })
  @ApiOperation({ summary: 'Update an interview by Id', description: `This endpoint updates an interview by Id. This is accessible only for  "BUHead", "ResourceManager", "DeliveryManager", "TeamLead", and "AccountManager" .` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager, Role.Recruiter, Role.TeamMember)
  @Roles()
  update(@Param('interviewId') interviewId: string, @Body() updateInterviewDto: UpdateInterviewDto) {
    const objId = validateObjectId(interviewId);
    return this.interviewService.update(objId, updateInterviewDto);
  }


  @Delete(':interviewId')
  @ApiResponse({ status: 200, description: 'Interview is deleted.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden only user with "BUHead", "ResourceManager", "DeliveryManager", "TeamLead", "AccountManager", "Recuriter", and "TeamMember" can access this endpoint.' })
  @ApiParam({ name: 'interviewId', description: 'id of the interview.' })
  @ApiOperation({ summary: 'Delete an interview by Id', description: `This endpoint deletes an interview by Id. This is accessible only for  "BUHead", "ResourceManager", "DeliveryManager", "TeamLead", and "AccountManager" .` })
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager, Role.Recruiter, Role.TeamMember)
  @Roles()
  @ApiBearerAuth()
  remove(@Param('interviewId') interviewId: string) {
    const objId = validateObjectId(interviewId);
    return this.interviewService.remove(objId);
  }

  @Get('/cancel')
  @ApiResponse({ status: 200, description: 'Interview cancellation processed successfully, email template content retrieved.' })
  @ApiResponse({ status: 404, description: 'Job application not found.' })
  @ApiResponse({ status: 401, description: 'Unauthorized access.' })
  @ApiResponse({ status: 403, description: 'Forbidden only user with "BUHead", "ResourceManager", "DeliveryManager", "TeamLead", "AccountManager", "Recuriter", and "TeamMember" can access this endpoint.' })
  @ApiOperation({
    summary: 'Cancel an interview',
    description: `This endpoint allows you to cancel an interview for a given job application and retrieves the corresponding email template content. This Accessible is for everyone .`,
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager, Role.Recruiter, Role.TeamMember)
  @Roles()
  cancelInterview(@Req() req: any, @Query('jobApplicationId') jobApplicationId: string) {
    const objId = validateObjectId(jobApplicationId);
    if (!objId) {
      throw new BadRequestException('Job application ID is required to process this request.');
    }
    return this.interviewService.cancelInterview(req.user, jobApplicationId);
  }

  @Get('/remind')
  @ApiResponse({ status: 200, description: 'Interview reminder processed successfully, email template content retrieved.' })
  @ApiResponse({ status: 404, description: 'Job application not found.' })
  @ApiResponse({ status: 401, description: 'Unauthorized access.' })
  @ApiResponse({ status: 403, description: 'Forbidden only user with   "BUHead", "ResourceManager", "DeliveryManager", "TeamLead", "AccountManager", "Recuriter", and "TeamMember" can access this endpoint.' })
  @ApiOperation({
    summary: 'Send an interview reminder',
    description: `This endpoint allows you to send a reminder for a scheduled interview for a given job application and retrieves the corresponding email template content. This Accessible is for everyone.`,
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager, Role.Recruiter, Role.TeamMember)
  @Roles()
  remindInterview(@Req() req: any, @Query('jobApplicationId') jobApplicationId: string) {
    this.logger.log(JSON.stringify(jobApplicationId))
    const objId = validateObjectId(jobApplicationId);
    if (!objId) {
      throw new BadRequestException('Job application ID is required to process this request.');
    }
    return this.interviewService.remindInterview(req.user, jobApplicationId);
  }

  @Get('/interview-ratio')
  @ApiResponse({ status: 200, description: 'Interview ratio calculated successfully.' })
  @ApiResponse({ status: 401, description: 'Unauthorized access.' })
  @ApiResponse({ status: 403, description: 'Forbidden only user with  "BUHead", "ResourceManager", "DeliveryManager", "TeamLead", "AccountManager", "Recuriter", and "TeamMember" can access this endpoint.' })
  @ApiOperation({
    summary: 'Get interview ratio for logged-in user',
    description: `This endpoint calculates and returns the interview ratio (show-up rate) for the logged-in user. This Accessible is for everyone.`,
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager, Role.Recruiter, Role.TeamMember)
  @Roles()
  async getInterviewRatio(@Req() req: any) {
    const userId = req.user._id; // Extract user ID from the request object
    return this.interviewService.calculateInterviewRatio(userId);
  }

  @Patch('/show-up')
  @ApiResponse({ status: 200, description: 'Interview Candidate show-up updated successfully.' })
  @ApiResponse({ status: 400, description: 'Invalid job application ID or no offers found.' })
  @ApiResponse({ status: 401, description: 'Unauthorized access.' })
  @ApiResponse({ status: 403, description: 'Forbidden only user with "BUHead", "ResourceManager", "DeliveryManager", "TeamLead", "AccountManager", "Recuriter", and "TeamMember" can access this endpoint.' })
  @ApiOperation({
    summary: 'Update the most recent interview as accepted for a specific job application',
    description: `This endpoint updates the most recent interview for the specified job application ID by setting its "isCandidateAccepted" field to true. and This is Accessible only for  "BUHead", "ResourceManager", "DeliveryManager", "TeamLead", and "AccountManager" .`,
  })
  @ApiQuery({ name: 'jobApplicationId', type: String, required: true, description: 'The ID of the job application' })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager, Role.Recruiter, Role.TeamMember)
  @Roles()
  async acceptMostRecentInterview(@Query('jobApplicationId') jobApplicationId: string): Promise<{ message: string }> {
    return this.interviewService.acceptMostRecentInterview(jobApplicationId);
  }

}
