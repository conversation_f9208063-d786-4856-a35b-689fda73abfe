import { ApiHideProperty, ApiProperty } from "@nestjs/swagger";
import { Transform, TransformFnParams } from "class-transformer";
import { IsArray, IsNotEmpty, IsOptional, IsString } from "class-validator";
import { sanitizeWithStyle } from "src/utils/sanitize-with-style";


export class CreateMessageDto {

    @ApiProperty({
        type: String,
        required: true,
        description: 'Message subject',
    })
    @IsNotEmpty()
    @IsString()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    subject: string;

    @ApiHideProperty()
    @IsOptional()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    sender?: string;

    @ApiProperty({ type: [String], required: true, description: 'References to the RecipientIds' })
    @IsNotEmpty()
    @IsArray()
    @IsString({ each: true })
    @Transform((params: TransformFnParams) => params.value.map((value: string) => sanitizeWithStyle(value)))
    recipients: string[];

    @ApiProperty({
        type: String,
        required: false,
        description: 'Message content',
    })
    @IsOptional()
    @IsString()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    contents?: string;

    @ApiProperty({ description: 'Array of attachment URLs', type: [String], required: false, example: ['http://example.com/file1.png', 'http://example.com/file2.pdf'] })
    @IsArray()
    @IsString({ each: true })
    @IsOptional()
    attachments?: string[];



}
