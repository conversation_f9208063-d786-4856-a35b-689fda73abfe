import {
  BadRequestException,
  ConflictException,
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';

import { RateCardCategory } from './schemas/rate-card-category.schema';
import { CreateRateCardCategoryDto } from './dto/create-rate-card-category.dto';
import { UpdateRateCardCategoryDto } from './dto/update-rate-card-category.dto';
import { FilterRateCardCategoryDto } from './dto/filter-rate-card-category.dto';
import { validateObjectId } from 'src/utils/validation.utils';
import { RateCard } from 'src/rate-card/schemas/rate-card.schema';

@Injectable()
export class RateCardCategoryService {
  private readonly logger = new Logger(RateCardCategoryService.name);

  constructor(
    @InjectModel(RateCardCategory.name)
    private rateCardCategoryModel: Model<RateCardCategory>,
    @InjectModel(RateCard.name)
    private rateCardModel: Model<RateCard>,
    private readonly configService: ConfigService,
    private readonly eventEmitter: EventEmitter2,
  ) { }

  async create(dto: CreateRateCardCategoryDto, user: any) {
    try {
      const existing = await this.rateCardCategoryModel.findOne({
        label: dto.label,
        client: dto.client,
        org: user.org._id,
        createdBy: user._id,
        isDeleted: false,
      });

      if (existing) {
        throw new BadRequestException(
          'Rate card category with the same name already exists for this client.',
        );
      }
      this.logger.log(JSON.stringify(dto));

      const newCategory = new this.rateCardCategoryModel(dto);

      const created = await newCategory.save();
      // if (dto.parentCategory) {
      //   const objId = validateObjectId(dto.parentCategory);
      //   await this.rateCardCategoryModel.findByIdAndUpdate(
      //     objId,
      //     { $push: { children: created._id } },
      //     { new: true }
      //   );
      // }
      // TO-DO
      // this.eventEmitter.emit('rate-card-category.created', { created, user });

      return created;
    } catch (error) {
      // this.logger.error('Error while creating rate card category', error.stack);
      throw new BadRequestException(
        `Error creating rate card category: ${error.message}`,
      );
    }
  }

  async findAll(query: FilterRateCardCategoryDto) {
    try {
      const { label, org, page = 1, limit = 10, client } = query;

      let conditions: any = { isDeleted: false };

      if (label) {
        const regex = new RegExp(label, 'i'); // 'i' for case-insensitive search
        conditions.label = regex;
      }

      if (org) {
        conditions.org = org;
      }

      if (client) {
        conditions.client = client;
      }

      // this.logger.log(JSON.stringify(conditions))
      return await this.rateCardCategoryModel
        .find(conditions)
        .populate({
          path: 'children',
          match: { isDeleted: false },
          select: '_id name description fixedRate percentageOfCTC',
          model: 'RateCard'
        })
        .sort({ createdAt: -1 })
        .skip((page - 1) * limit)
        .limit(limit)
        .exec();
    } catch (error) {
      this.logger.error('Error fetching rate card categories', error.stack);
      throw new error;
    }
  }

  async findOne(rateCardCategoryId: Types.ObjectId) {
    try {
      const category = await this.rateCardCategoryModel.findById(rateCardCategoryId)
        // .populate({ path: 'createdBy', select: '_id roles firstName' })
        // .populate({ path: 'org', select: '_id title' })
        .populate({ path: 'parentCategory', select: '_id label description', model: 'RateCardCategory' })
        .populate({ path: 'children', select: '_id label description', model: 'RateCardCategory' })
        .exec();
      if (!category || category.isDeleted) {
        throw new NotFoundException('Rate card category not found.');
      }
      return category;
    } catch (error) {
      throw new error;
    }
  }

  async update(rateCardCategoryId: Types.ObjectId, dto: UpdateRateCardCategoryDto, user: any,) {
    try {
      const updated = await this.rateCardCategoryModel
        .findByIdAndUpdate(rateCardCategoryId, dto, { new: true })
        .exec();

      if (!updated) {
        throw new NotFoundException('Rate card category not found.');
      }

      // TO-DO
      // this.eventEmitter.emit('rate-card-category.updated', { updated, user });

      return updated;
    } catch (error) {
      throw new error;
    }
  }

  async softDelete(rateCardCategoryId: Types.ObjectId, user: any) {
    try {
      const deleted = await this.rateCardCategoryModel.findByIdAndUpdate(
        rateCardCategoryId,
        { isDeleted: true, deletedAt: new Date(), deletedBy: user._id },
        { new: true },
      );

      if (!deleted) {
        throw new NotFoundException('Rate card category not found.');
      }

      // TO-DO
      // this.eventEmitter.emit('rate-card-category.deleted', { deleted, user });

      return deleted;
    } catch (error) {
      throw new error;
    }
  }

  async hardDelete(rateCardCategoryId: Types.ObjectId, user: any) {
    try {
      const rateCardCategory = await this.findOne(rateCardCategoryId);
      if (!rateCardCategory) {
        throw new NotFoundException('Rate card category not found.');
      }
      return await this.rateCardCategoryModel.findByIdAndDelete(rateCardCategoryId);
    } catch (error) {
      throw new error;
    }
  }

  async restore(rateCardCategoryId: Types.ObjectId) {
    try {
      const restored = await this.rateCardCategoryModel.findByIdAndUpdate(
        rateCardCategoryId,
        { isDeleted: false, deletedAt: null, deletedBy: null },
        { new: true },
      );

      if (!restored) {
        throw new NotFoundException('Rate card category not found.');
      }

      return restored;
    } catch (error) {
      throw new InternalServerErrorException(
        `Error restoring rate card category: ${error.message}`,
      );
    }
  }

  async countAll(query: FilterRateCardCategoryDto) {
    try {
      const { label, org } = query;

      let conditions: any = { isDeleted: false };

      if (label) {
        const regex = new RegExp(label, 'i'); // 'i' for case-insensitive search
        conditions.label = regex;
      }

      if (org) {
        conditions.org = org;
      }

      const count = await this.rateCardCategoryModel.countDocuments(conditions);
      return { count };
    } catch (error) {
      throw new InternalServerErrorException(
        `Error counting rate card categories: ${error.message}`,
      );
    }
  }

  async getRootCategories(query: FilterRateCardCategoryDto) {
    try {
      const { label, org, page = 1, limit = 10, client } = query;

      const conditions: any = {
        isDeleted: false,
        parentCategory: { $exists: false }
      };

      if (label) {
        const regex = new RegExp(label, 'i');
        conditions.label = regex;
      }

      if (org) {
        conditions.org = org;
      }

      if (client) {
        conditions.client = client;
      }

      return await this.rateCardCategoryModel
        .find(conditions)
        .populate({
          path: 'children',
          match: { isDeleted: false },
          select: '_id name description fixedRate percentageOfCTC',
          model: 'RateCard'
        })
        .sort({ createdAt: -1 })
        .skip((page - 1) * limit)
        .limit(limit)
        .exec();
    } catch (error) {
      this.logger.error('Error fetching root-level rate card categories', error.stack);
      throw error;
    }
  }

}
