import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Types } from 'mongoose';
import { EmploymentType } from 'src/shared/constants';
import { BasicUser } from 'src/user/schemas/basic-user.schema';

@Schema({ timestamps: true })
export class HikeApproval {
  @Prop({ type: Types.ObjectId, required: true, ref: 'JobApplication', index: true })
  applicationId: Types.ObjectId;

  @Prop({ type: Types.ObjectId, required: true, ref: 'Job', index: true })
  jobId: Types.ObjectId;

  @Prop({ type: Types.ObjectId, required: true, ref: 'Offer' })
  offerId: Types.ObjectId;

  @Prop({ type: Types.ObjectId, required: true, ref: 'Org' })
  orgId: Types.ObjectId;

  @Prop({ type: String, enum: Object.values(EmploymentType), required: true })
  employmentType: EmploymentType;

  @Prop({ type: String, required: true })
  reason: string;

  @Prop({ type: String, required: false })
  approverRole: string;

  @Prop({ type: String, enum: ['PENDING', 'APPROVED', 'REJECTED'], default: 'PENDING' })
  status: 'PENDING' | 'APPROVED' | 'REJECTED';

  @Prop({ type: Types.ObjectId, required: true, ref: 'BasicUser' })
  createdBy: Types.ObjectId;

  @Prop({ type: Date, default: Date.now })
  reviewedAt?: Date;

  @Prop({ type: Types.ObjectId, ref: 'BasicUser' })
  reviewedBy?: Types.ObjectId;

  @Prop()
  reviewComment?: string;

  @Prop({
    type: Number,
    required: false,
  })
  modifiedCtc: number;

  @Prop({
    type: Number,
    required: false,
  })
  maxAllowedByMargin: number;

  @Prop({
    type: Number,
    required: false,
  })
  maxAllowedByHike: number;

  @Prop({ type: Object, required: false }) // Save hike settings object as-is
  hikeSettings: any;

  @Prop({
    type: [Types.ObjectId],
    required: false,
    ref: 'BasicUser',
  })
  approvers?: BasicUser[]; // or BasicUser[] if you plan to populate
}

export type HikeApprovalDocument = HydratedDocument<HikeApproval>;
export const HikeApprovalSchema = SchemaFactory.createForClass(HikeApproval);
