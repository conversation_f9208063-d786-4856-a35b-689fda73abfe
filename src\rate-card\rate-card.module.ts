import { Module } from '@nestjs/common';
import { RateCardService } from './rate-card.service';
import { RateCardController } from './rate-card.controller';
import { ConfigModule } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { MongooseModule } from '@nestjs/mongoose';
import { EndpointsRolesModule } from 'src/endpoints-roles/endpoints-roles.module';
import { RateCard, RateCardSchema } from './schemas/rate-card.schema';
import { RateCardCategory, RateCardCategorySchema } from 'src/rate-card-category/schemas/rate-card-category.schema';

@Module({
  imports: [
    ConfigModule,
    JwtModule, EndpointsRolesModule,
    MongooseModule.forFeature([
      { name: RateCard.name, schema: RateCardSchema },
      { name: RateCardCategory.name, schema: RateCardCategorySchema },
    ])
  ],
  controllers: [RateCardController],
  providers: [RateCardService],
  exports: [RateCardService, MongooseModule],
})
export class RateCardModule { }
