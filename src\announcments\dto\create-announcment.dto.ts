import { ApiHideProperty, ApiProperty } from "@nestjs/swagger";
import { Transform, TransformFnParams } from "class-transformer";
import { IsArray, IsBoolean, IsMongoId, IsOptional, IsString } from "class-validator";
import { sanitizeWithStyle } from "src/utils/sanitize-with-style";


export class CreateAnnouncmentDto {
    @ApiProperty({
        type: String,
        required: false,
        description: 'The title.',
    })
    @IsString()
    @IsOptional()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    title?: string;

    @ApiProperty({
        type: String,
        required: false,
        description: 'The description.'
    })
    @IsString()
    @IsOptional()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    description?: string;


    @ApiProperty({ type: String, required: false, description: 'Reference to the Organization' })
    @IsMongoId()
    @IsOptional()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    org?: string;

    @ApiProperty({ type: String, required: false, description: 'Reference to the Job' })
    @IsMongoId()
    @IsOptional()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    job?: string;

    @ApiProperty({
        type: Boolean,
        required: false,
        default: false,
        description: 'Indicates if the note is private.'
    })
    @IsBoolean()
    @IsOptional()
    isDeleted?: boolean;

    @ApiHideProperty()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    createdBy?: string;

    @ApiProperty({
        type: [String],
        required: false,
        description: 'Array of user IDs to assign',
    })
    @IsArray()
    @IsOptional()
    @Transform(({ value }) => {
        if (Array.isArray(value)) {
            return value.map((v) => sanitizeWithStyle(v));
        }
        return [];
    })
    assignTo?: string[];


}
