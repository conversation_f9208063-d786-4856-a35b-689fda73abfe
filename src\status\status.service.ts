import { HttpException, HttpStatus, Injectable, InternalServerErrorException, Logger, NotFoundException } from '@nestjs/common';
import { CreateStatusDto } from './dto/create-status.dto';
import { UpdateStatusDto } from './dto/update-status.dto';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Status, StatusDocument } from './schemas/status.schema';

@Injectable()
export class StatusService {

  private readonly logger = new Logger(StatusService.name);

  constructor(@InjectModel(Status.name) private statusModel: Model<Status>) {
  }

  async create(createStatusDto: CreateStatusDto): Promise<StatusDocument> {
    try {

      const createdStatus = new this.statusModel(createStatusDto);
      return await createdStatus.save();
    } catch (error) {
      if (error.code === 11000) {
        throw new HttpException('Status with the provided name and status type already exists.', HttpStatus.CONFLICT);
      }
      this.logger.error('Failed to create status', error);
      throw new InternalServerErrorException(`Unknown error when creating status. ${error.message}`);
    }
  }

  async findAll(): Promise<StatusDocument[]> {
    return await this.statusModel
      .find()
      .exec();
  }

  async findAllStatusByType(statusType: string): Promise<StatusDocument[]> {
    try {
      // Validate the status
      // const validStatusTypes = ['Region', 'Company', 'Agency', 'Freelancer'];
      // if (!validStatusTypes.includes(statusType)) {
      //   throw new NotFoundException(`Invalid status type: ${statusType}`);
      // }

      const statuses = await this.statusModel
        .find({ statusType })
        .exec();
      return statuses;
    } catch (error) {
      throw new InternalServerErrorException(`Error while fetching statuses by type: ${error.message}`);
    }
  }

  async findById(statusId: Types.ObjectId): Promise<StatusDocument> {
    try {
      const status = await this.statusModel.findById(statusId)
        .exec();
      if (!status) {
        throw new NotFoundException(`status with ID ${status} not found`)
      }
      return status;
    } catch (error) {
      this.logger.error(error);
      this.logger.error(`An error occurred in fetching status by Id ${statusId}. ${error?.message}`);
      throw error;
    }
  }

  async update(statusId: Types.ObjectId, updateStatusDto: UpdateStatusDto) {
    try {
      const status = await this.statusModel.findById(statusId);
      if (!status) {
        throw new NotFoundException(`status with ID ${statusId} not found`)
      }
      return await this.statusModel.findByIdAndUpdate(statusId, updateStatusDto, { new: true })
        .exec();
    } catch (error) {
      if (error.code === 11000) {
        throw new HttpException('Status with the provided name and status type already exists.', HttpStatus.CONFLICT);
      }
      this.logger.error(error);
      this.logger.error(`An error occurred in updating status by Id ${statusId}. ${error?.message}`);
      throw error;
    }
  }

  async delete(statusId: Types.ObjectId) {
    try {
      const status = await this.statusModel.findById(statusId);
      if (!status) {
        throw new NotFoundException(`status with ID ${statusId} not found`)
      }
      status.isDeleted = true;
      await status.save();
      return status;
    } catch (error) {
      this.logger.error(`An error occurred while deleting status by ID ${statusId}. ${error?.message}`);
      throw new InternalServerErrorException('An error occurred while soft deleting the status');
    }
  }

  async restore(statusId: Types.ObjectId): Promise<StatusDocument> {
    try {
      const status = await this.statusModel.findById(statusId);
      if (!status) {
        throw new NotFoundException(`status with ID ${statusId} not found`)
      }
      status.isDeleted = false;
      await status.save();
      return status;
    } catch (error) {
      this.logger.error(`An error occurred while restoring status by ID ${statusId}. ${error?.message}`);
      throw new InternalServerErrorException('An error occurred while restoring the status');
    }
  }

}
