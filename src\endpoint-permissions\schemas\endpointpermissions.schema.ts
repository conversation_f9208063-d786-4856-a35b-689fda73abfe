// src/endpoint/schemas/endpoint-permission.schema.ts
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Types } from 'mongoose';
import { Privilege } from 'src/privilege/schemas/privilege.schema';
import { Org } from 'src/org/schemas/org.schema';

export type EndpointPermissionDocument = HydratedDocument<EndpointPermission>;

@Schema({ timestamps: true })
export class EndpointPermission {
    @Prop({ required: true })
    controller: string;

    @Prop({ required: true })
    method: string;

    @Prop({ required: true })
    path: string;

    @Prop({ type: Types.ObjectId, ref: 'Org', required: false })
    orgId?: Org;

    @Prop({ required: false, default: false })
    isPublic?: boolean;

    @Prop({ required: false, default: false })
    isGlobal?: boolean;

}

export const EndpointPermissionSchema = SchemaFactory.createForClass(EndpointPermission);
// Add a compound index to ensure uniqueness of endpoint permissions per organization
EndpointPermissionSchema.index({ controller: 1, method: 1, path: 1, orgId: 1 }, { unique: true });