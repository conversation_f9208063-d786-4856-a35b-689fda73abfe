import { Injectable, NotFoundException, BadRequestException, InternalServerErrorException, ForbiddenException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Privilege } from './schemas/privilege.schema';
import { CreatePrivilegeDto } from './dto/create-privilege.dto';
import { UpdatePrivilegeDto } from './dto/update-privilege.dto';

@Injectable()
export class PrivilegeService {
  constructor(@InjectModel(Privilege.name) private readonly privilegeModel: Model<Privilege>) { }

  async create(createPrivilegeDto: CreatePrivilegeDto): Promise<Privilege> {
    try {
      const exsistingPrivilege = await this.privilegeModel.findOne({ name: createPrivilegeDto.name }).exec();
      if (exsistingPrivilege) {
        throw new BadRequestException('Privilege already exists');
      }
      const privilege = new this.privilegeModel(createPrivilegeDto);
      return await privilege.save();
    } catch (error) {
      throw error;
    }
  }

  async findAll(page: number = 1, limit: number = 10): Promise<Privilege[]> {
    try {
      const skip = (page - 1) * limit;
      const data = await this.privilegeModel.find().skip(skip).limit(limit).exec();
      return data;
    } catch (error) {
      throw new InternalServerErrorException(`Error retrieving privileges. ${error?.message}`);
    }
  }

  async findOne(privilegeId: Types.ObjectId): Promise<Privilege> {
    try {
      const privilege = await this.privilegeModel.findById(privilegeId).exec();
      if (!privilege) {
        throw new NotFoundException('Privilege not found');
      }
      return privilege;
    } catch (error) {
      throw error;
    }
  }

  async update(privilegeId: Types.ObjectId, updatePrivilegeDto: UpdatePrivilegeDto): Promise<Privilege> {
    try {

      const exsistingPrivilege = await this.privilegeModel.findOne({ name: updatePrivilegeDto.name }).exec();
      if (exsistingPrivilege) {
        throw new BadRequestException('Privilege already exists');
      }

      const privilege = await this.privilegeModel.findByIdAndUpdate(privilegeId, updatePrivilegeDto, { new: true }).exec();
      if (!privilege) {
        throw new NotFoundException('Privilege not found');
      }
      return privilege;
    } catch (error) {
      throw error;
    }
  }

  async remove(privilegeId: Types.ObjectId) {
    try {
      const privilege = await this.privilegeModel.findByIdAndDelete(privilegeId).exec();
      if (!privilege) {
        throw new NotFoundException('Privilege not found');
      }
      return 'Privilege deleted successfully';
    } catch (error) {
      throw error;
    }
  }

  // async bulkCreate(createPrivilegesDto: CreatePrivilegeDto[]): Promise<any> {
  //   try {
  //     const insertedPrivileges = await this.privilegeModel.insertMany(createPrivilegesDto);
  //     return {
  //       message: 'Privileges added successfully',
  //       insertedCount: insertedPrivileges.length,
  //     };
  //   } catch (error) {
  //     console.error('Error bulk adding privileges:', error);
  //     throw new BadRequestException('Failed to add privileges');
  //   }
  // }
}
