import { <PERSON>p, Schema, SchemaFactory } from "@nestjs/mongoose";
import { Types } from "mongoose";
import { Document } from "mongoose";
import { BasicUser } from "src/user/schemas/basic-user.schema";

@Schema({ timestamps: true })
export class Attendance extends Document {
  @Prop({ type: Types.ObjectId, rref: 'BasicUser',  required: false })
  userId?: BasicUser;

  @Prop({ required: true })
  date: Date;

  @Prop({ type: [{ type: Types.ObjectId, ref: 'AttendanceSession' }] })
  sessions: Types.ObjectId[];

  @Prop()
  status: 'present' | 'absent' | 'leave';

  @Prop()
  workLocation: 'office' | 'remote';

  @Prop()
  totalWorkHours?: number;

  @Prop()
  totalBreakHours?: number;

  @Prop()
  effectiveWorkHours?: number;
}

export const AttendanceSchema = SchemaFactory.createForClass(Attendance);
