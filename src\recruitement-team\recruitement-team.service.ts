import { Injectable, InternalServerErrorException, Logger, NotFoundException } from "@nestjs/common";
import ical from "ical-generator";
import { Model, Types } from "mongoose";
import { ConfigService } from "@nestjs/config";
import { InjectModel } from "@nestjs/mongoose";
import { RecruitementTeam } from "./schemas/recruitement-team.schema";
import { CreateRecruitementTeamDto } from "./dto/create-recruitement-team.dto";
import { UpdateRecruitementTeamDto } from "./dto/update-recruitement-team.dto";

@Injectable()
export class RecruitementTeamService {

  private readonly logger = new Logger(RecruitementTeamService.name);

  constructor(private configService: ConfigService, @InjectModel(RecruitementTeam.name) private recruitementTeamModel: Model<RecruitementTeam>) { }

  async create(createRecruitementTeamDto: CreateRecruitementTeamDto) {
    try {
      const createRecruitementTeam = new this.recruitementTeamModel(createRecruitementTeamDto);

      const recruitementTeam = await createRecruitementTeam.save();
      return recruitementTeam.toString();
    }
    catch (error) {
      this.logger.error(`Failed to create an recruitement team. ${error}`);
      throw new InternalServerErrorException(`Error when creating recruitement team. ${error?.message}`);
    }
  }
}