import { ConflictException, forwardRef, HttpException, HttpStatus, Inject, Injectable, InternalServerErrorException } from '@nestjs/common';
import { NotFoundException, BadRequestException } from '@nestjs/common';
import { CreateOrgDto } from './dto/create-org.dto';
import { UpdateOrgDto } from './dto/update-org.dto';
import { ConfigService } from '@nestjs/config';
import { InjectModel } from '@nestjs/mongoose';
import { Org, OrgDocument, Placeholder } from './schemas/org.schema';
import { Model, Types } from 'mongoose';
import { Logger } from '@nestjs/common';
import { validateObjectId } from 'src/utils/validation.utils';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';
import { CommentDto } from 'src/common/dto/comment.dto';
import { BasicUser } from 'src/user/schemas/basic-user.schema';
import { UserService } from 'src/user/user.service';
import { QueryOrgDto } from './dto/query-org.dto';
import { Stage } from 'src/stage/schemas/stage.schema';
import { Workflow } from 'src/workflow/schemas/workflow.schema';
import { Comment, CommentDocument } from 'src/common/schemas/comment.schema';
import { create, omit } from 'lodash';
import { UpdateResult } from 'mongodb';
import { StatusService } from 'src/status/status.service';
import { ChangeCustomStatusDto } from './dto/change-status.dto';
import { Status, StatusDocument } from 'src/status/schemas/status.schema';
import { ChangeStatusOrgDto } from './dto/change-status-org.dto';
import { AccountStatus, EmailTemplateEvent, InviteStatus, ORG_ROLE_PERMISSIONS_PAYLOAD, OrgType, SourceType, StatusFunctionality } from 'src/shared/constants';
import { CountryDocument } from 'src/country/schemas/country.schema';
import { StateDocument } from 'src/state/schemas/state.schema';
import { RegionDocument } from 'src/region/schemas/region.schema';
import { CreateMemberDto } from './dto/create-member.dto';
import { UpdateMemberDto } from './dto/update-member.dto';
import { FileMetadata, FileMetadataDocument } from 'src/file-upload/schemas/file-metadata.schema'

import { PlaceholderDto } from './dto/placeholder.dto';
import { CreateRemoveVendorCustomerDto } from './dto/create-vendor-customer.dto';
import { UpdatePlaceholderDto } from './dto/update-placeholder.dto';
import { CreateUserDto } from 'src/user/dto/create-user.dto';
import { Role } from 'src/auth/enums/role.enum';
import { JobService } from 'src/job/job.service';
import { JobsQueryDTO } from 'src/job/dto/query-jobs.dto';
import { Job } from 'src/job/schemas/job.schema';
import { CreateJobDto } from 'src/job/dto/create-job.dto';
import { UpdateCompanyProfileDto } from './dto/update-company-profile-dto';
import { TempOrg, TempOrgDocument } from './schemas/temp_org.schema';
import { VendorInvite } from 'src/vendor-invite/schemas/vendor-invite.schmea';
import { addMinutes } from 'date-fns';
import { ClientAddressDto } from 'src/common/dto/client-address.dto';
import { ClientOnboardingDto } from './dto/client-onboarding.dto';
import { Onboarding } from 'src/onboarding/schemas/onboarding.schema';
import { UpdateInvoiceDetailsDto } from './dto/client-invoice.dto';
import { Contact } from 'src/contact/schemas/contact.schema';
import { UpdateAddressDetailsDto, UpdateBankDetailsDto } from './dto/update-org-details.dto';
import { UpdateHikeSettingsDto } from './dto/hike-settings.dto';
import { BusinessUnit } from 'src/business-unit/schemas/business-unit.schema';

@Injectable()
export class OrgService {

  private readonly logger = new Logger(OrgService.name);

  constructor(private configService: ConfigService, @InjectModel(Org.name) private orgModel: Model<Org>,
    @InjectModel(TempOrg.name) private readonly tempOrgModel: Model<TempOrgDocument>,
    @InjectModel(Comment.name) private commentModel: Model<Comment>,
    @InjectModel(Stage.name) private stageModel: Model<Stage>,
    @InjectModel(Workflow.name) private workflowModel: Model<Workflow>,
    @InjectModel(Placeholder.name) private placeholderModel: Model<Placeholder>,
    @InjectModel(VendorInvite.name) private vendorInviteModel: Model<VendorInvite>,
    @InjectModel(Job.name) private jobModel: Model<Job>,
    @InjectModel(Status.name) private statusModel: Model<Status>,
    @InjectModel(BasicUser.name) private basicUserModel: Model<BasicUser>,
    @InjectModel(FileMetadata.name) private readonly fileMetadataModel: Model<FileMetadataDocument>,
    @InjectModel(Onboarding.name) private onboardingModel: Model<Onboarding>,
    @InjectModel(Contact.name) private contactModel: Model<Contact>,
    @InjectModel(BusinessUnit.name) private businessUnitModel: Model<BusinessUnit>,
    private eventEmitter: EventEmitter2, private userService: UserService,
    private statusService: StatusService) { }

  generateOTP(length: number): string {
    const digits = '0123456789';
    let OTP = '';
    for (let i = 0; i < length; i++) {
      OTP += digits[Math.floor(Math.random() * 10)];
    }
    return OTP;
  }

  async createVendor(createOrgDto: CreateOrgDto, isTemporary: boolean = false): Promise<OrgDocument | TempOrgDocument> {
    let createdOrg;

    const model = isTemporary ? this.tempOrgModel : this.orgModel;
    try {
      // Check if a company with the same contact email already exists
      if (createOrgDto.contactDetails?.length) {
        const contactEmail = createOrgDto.contactDetails[0]?.contactEmail;

        if (contactEmail) {
          const existingOrg = await this.orgModel
            .findOne({ 'contactDetails.contactEmail': contactEmail, isDeleted: false, orgType: OrgType.VENDOR_ORG })
            .exec();

          const vendorInvite = await this.vendorInviteModel.findOne({
            email: contactEmail,
            vendorName: createOrgDto.title,
          }).exec();
          if (vendorInvite) {
            const companyId = typeof vendorInvite.companyId === 'object' && '_id' in vendorInvite.companyId
              ? vendorInvite.companyId.toString()
              : vendorInvite.companyId;
            const existingOrgs = await this.orgModel
              .find({ 'contactDetails.contactEmail': contactEmail, isDeleted: false, orgType: OrgType.VENDOR_ORG })
              .exec();
            const matchingOrgs = existingOrgs.filter((org) =>
              org.companyId?.toString() === companyId
            );
            if (matchingOrgs.length > 0) {
              throw new BadRequestException(`A Vendor with the email "${contactEmail}" already exists for this company.`);
            }
          }


          if (existingOrg) {
            delete createOrgDto.createUserDto;
            // throw new BadRequestException(`An organization with the email "${contactEmail}" already exists.`);
          }
        }
      }

      // Fetch default status based on org type
      const defaultStatus = await this.getDefaultVendorStatus(createOrgDto.orgType);

      const orgData: any = {
        ...createOrgDto,
        customStatus: defaultStatus,
        isTemporary,
      };

      // Find vendor invite before creating user
      const contactEmail = createOrgDto?.contactDetails?.[0]?.contactEmail ?? null;
      const vendorInvite = await this.vendorInviteModel.findOne({
        email: contactEmail,
        vendorName: createOrgDto.title,
      }).exec();
      if (vendorInvite) {
        const companyId = typeof vendorInvite.companyId === 'object' && '_id' in vendorInvite.companyId
          ? vendorInvite.companyId.toString()
          : vendorInvite.companyId;

        orgData.companyId = companyId;
      }


      // Handle user creation logic
      if (createOrgDto.createUserDto) {
        const userEmail = createOrgDto.createUserDto.email;

        // Check if the user already exists
        const existingUser = await this.userService.findUserByEmailExists(userEmail);

        if (existingUser) {
          throw new BadRequestException(`The user with email "${userEmail}" already exists.`);
        }

        // Find vendor invite before creating user
        const vendorInvite = await this.vendorInviteModel.findOne({
          email: userEmail,
          vendorName: createOrgDto.title,
        }).exec();

        console.log("with vendor invite model", vendorInvite)

        let userDtoWithCompanyId = createOrgDto.createUserDto;

        console.log("VendorInvite", vendorInvite)

        if (vendorInvite) {
          console.log('Found vendor invite:', vendorInvite);

          const companyId = typeof vendorInvite.companyId === 'object' && '_id' in vendorInvite.companyId
            ? vendorInvite.companyId.toString()
            : vendorInvite.companyId;

          // Add companyId and vendor role to the user DTO
          userDtoWithCompanyId = {
            ...createOrgDto.createUserDto,
            companyId: companyId as string,
            roles: [...(createOrgDto.createUserDto.roles || [])]
          };

          orgData.companyId = companyId;

          // Mark invite as used
          await this.vendorInviteModel.findByIdAndUpdate(vendorInvite._id, {
            isUsed: true,
            usedAt: new Date(),
          });
        }

        console.log("userdto company:", userDtoWithCompanyId)

        // Create the user with the updated DTO
        const createdUser = await this.userService.createVendor(userDtoWithCompanyId, isTemporary);

        // Ensure createdUser._id is used and is an ObjectId
        if (createdUser && createdUser._id) {
          orgData.assignTo = createdUser._id;
        } else {
          throw new InternalServerErrorException('Failed to create user');
        }
      }

      // Create and save the organization
      createdOrg = new model(orgData);
      await createdOrg.save();

      const deletedInvite = await this.vendorInviteModel.findByIdAndDelete(vendorInvite?._id).exec();

      // Update user's org only if user was created and org was saved
      if (createOrgDto.createUserDto && createdOrg._id && orgData.assignTo) {
        await this.userService.updateUserOrg(createdOrg._id, orgData.assignTo);
      }

      // Emit events only for non-temporary organizations
      if (!isTemporary) {
        this.emitEvent('org.created', {
          title: createOrgDto.title,
          email: createOrgDto?.contactDetails?.[0]?.contactEmail ?? null,
        });

        if (createOrgDto.createAdminUser) {
          this.emitEvent('org.createAdminUser', { createdOrg, createOrgDto });
        }
        this.emitEvent('org.workflow', { createdOrg });

        // Emit vendor registration event if this was from an invite
        // if (createOrgDto.createUserDto?.companyId) {
        //   this.emitEvent('vendor.registered', {
        //     vendorOrg: createdOrg,
        //     companyId: createOrgDto.createUserDto.companyId,
        //     email: createOrgDto.createUserDto.email
        //   });
        // }

        const updatedInvite = await this.vendorInviteModel.findOneAndUpdate(
          { email: createOrgDto?.createUserDto?.email },
          { status: InviteStatus.COMPLETED },
          { new: true }
        );

        if (!updatedInvite) {
          console.error("Failed to update VendorInvite. Check if email exists:", createOrgDto?.createUserDto?.email);
        } else {
          console.log("Successfully updated VendorInvite:", updatedInvite);
        }

        console.log("Updated VendorInvite:", updatedInvite);
      }

      console.log("createdOrg", createdOrg)

      return createdOrg;
    } catch (error) {
      console.error("Error caught in createVendor method:", error);

      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new InternalServerErrorException(`Error while creating org: ${error.message}`);
    }
  }


  async updateVendorStatus(orgId: Types.ObjectId, isApprove?: boolean, isReject?: boolean): Promise<Org> {
    console.log("orgId", orgId);

    const updateData: any = {};
    if (isApprove !== undefined) updateData.isApproved = isApprove;
    if (isReject !== undefined) updateData.isRejected = isReject;

    this.logger.log('Updating organization status: ' + JSON.stringify(updateData));

    const tempOrg = await this.tempOrgModel.findById(orgId).exec();

    console.log("tempOrg", tempOrg)
    if (!tempOrg) {
      this.logger.error(`Organization with ID ${orgId} not found in temp_org collection.`);
      throw new BadRequestException(`Organization with ID ${orgId} not found in temp_org collection.`);
    }

    let updatedOrg: Org | null = null;



    if (isApprove || (isApprove === false && isReject === false)) {
      // if (isApprove) {
      // Get the tempOrg data and set isApproved to true
      const tempOrgData = tempOrg.toObject();
      delete tempOrgData.assignTo;  // Remove assignTo field

      // Add required fields
      tempOrgData.isApproved = false;
      tempOrgData.companyId = tempOrg.companyId;

      // Move the organization from temp_org to org collection with approved status
      const createdOrg = new this.orgModel(tempOrgData);
      await createdOrg.populate('companyId', 'title _id');
      updatedOrg = await createdOrg.save();

      //     updatedOrg = await this.orgModel
      // .findById(updatedOrg._id)
      // .populate('companyId', 'title _id')
      // .exec();

      // Optionally, remove from temp_org collection if successfully moved
      await this.tempOrgModel.findByIdAndDelete(orgId).exec();

      // If the organization has an associated user (assignTo), handle user migration
      if ((tempOrg?.assignTo ?? []).length > 0) {
        const userObjectId = tempOrg.assignTo ? new Types.ObjectId(tempOrg.assignTo.toString()) : null;
        if (userObjectId) {
          await this.userService.moveUserToMainCollection(userObjectId); // Move user and update verification
        } else {
          // throw new BadRequestException('Invalid userObjectId: null value provided.');
        }
      }

      console.log("updated org", updatedOrg)

      // Ensure contactDetails exists and is an array before accessing it
      const primaryContact = Array.isArray(updatedOrg.contactDetails)
        ? updatedOrg.contactDetails.find((contact: any) => contact.isPrimary)
        : null;

      const vendorEmail = primaryContact ? primaryContact.contactEmail : null;

      if (!vendorEmail) {
        this.logger.error(`No primary email found for vendor: ${updatedOrg?.title || 'Unknown Vendor'}`);
      } else {
        // Emit event with email
        this.emitEvent('vendor.registered', {
          vendorName: updatedOrg.title,
          companyName: updatedOrg.companyId?.title || 'Unknown Company',
          email: vendorEmail
        });
      }






    }

    if (isReject) {
      this.emitEvent('org.rejected', tempOrg);
    }

    if (!isApprove) {
      updatedOrg = await this.orgModel
        .findByIdAndUpdate(orgId, updateData, { new: true })
        .exec();
    }

    if (!updatedOrg) {
      this.logger.error(`Failed to update organization with ID ${orgId}`);
      throw new BadRequestException(`Failed to update organization with ID ${orgId}`);
    }

    return updatedOrg;
  }


  async create(createOrgDto: CreateOrgDto): Promise<OrgDocument> {

    let createdOrg;
    try {

      // Check if a company with the same contact email already exists
      if (createOrgDto.contactDetails?.length) {
        const contactEmail = createOrgDto.contactDetails[0]?.contactEmail;

        if (contactEmail && createOrgDto.orgType === OrgType.ADMIN_CUSTOMER_ORG) {
          const existingOrg = await this.orgModel
            .findOne({ 'contactDetails.contactEmail': contactEmail, isDeleted: false, orgType: OrgType.ADMIN_CUSTOMER_ORG })
            .exec();

          const onboardingRecord = existingOrg
            ? await this.onboardingModel.findOne({ org: existingOrg._id.toString() }).exec()
            : null;

          if (existingOrg) {
            if (existingOrg.isVerified && onboardingRecord) {
              throw new BadRequestException(`An organization with the email "${contactEmail}" already exists.`);
            }
            else {
              // Update existing org with new data from createOrgDto
              const updatedOrg = await this.orgModel.findByIdAndUpdate(
                existingOrg._id,
                { $set: createOrgDto },
                { new: true }
              ).exec();

              // Add isFilesUploaded flag based on onboarding existence
              const orgWithFlag = updatedOrg?.toObject();
              (orgWithFlag as any).isFilesUploaded = !!onboardingRecord;

              return orgWithFlag as OrgDocument & { isFilesUploaded: boolean };
              return updatedOrg as OrgDocument;
            }
          }

        }
        if (contactEmail && createOrgDto.orgType !== OrgType.ADMIN_CUSTOMER_ORG) {
          const existingOrg = await this.orgModel
            .findOne({ 'contactDetails.contactEmail': contactEmail, isDeleted: false, createdByOrg: createOrgDto.createdByOrg })
            .exec();

          if (existingOrg) {
            throw new BadRequestException(`An organization with the email "${contactEmail}" already exists.`);
          }
        }
      }

      const defaultStatus = await this.getDefaultStatus(createOrgDto.orgType);

      let otpCode;
      if ((createOrgDto.orgType === OrgType.ADMIN_CUSTOMER_ORG || createOrgDto.orgType === OrgType.AGENCY_ORG) && createOrgDto.source === SourceType.LANDING_PAGE) {
        otpCode = this.generateOTP(6);
        this.emitEvent('customer.register.admin', {
          title: createOrgDto.title
        });
      }

      const orgData: any = {
        ...createOrgDto,
        customStatus: defaultStatus
      };

      if (otpCode) {
        orgData.otpCode = otpCode;
        orgData.verificationExpires = addMinutes(new Date(), 10);
      }

      //validate whether user with same email already registered or not 
      if (createOrgDto.createAdminUser && createOrgDto.createUserDto) {
        const isThere = await this.userService.findUserByEmailExists(createOrgDto.createUserDto.email);
        if (isThere) {
          throw new BadRequestException(`The user with email "${createOrgDto.createUserDto.email}" already exists.`);
        }
      }


      createdOrg = new this.orgModel(orgData);
      await createdOrg.save()

      const createdBy = await this.basicUserModel.findOne({ _id: createdOrg.createdBy }).exec();
      console.log(createdBy)
      if (otpCode) {
        this.emitEvent('org.created.verify', {
          title: createOrgDto.title,
          email: createOrgDto?.contactDetails?.[0]?.contactEmail ?? null,
          otpCode
        });
      }
      else if (createdOrg.orgType === OrgType.ACCOUNT_ORG) {
        this.emitEvent('accountOrg.created', {
          title: createOrgDto.title,
          email: createOrgDto?.contactDetails?.[0]?.contactEmail ?? null,
          createdBy: createdBy?.firstName
        });
        this.emitEvent('org.workflow', { createdOrg });
        const createdById = createdOrg.createdBy
          ? new Types.ObjectId((createdOrg.createdBy as any)._id || createdOrg.createdBy)
          : undefined;

        // if (!createdById) {
        //   throw new BadRequestException('Invalid createdBy ID');
        // }

        const user = await this.basicUserModel.findOne({ _id: createdById });
        this.emitEvent('org.CloneDefaultDynamicFieldsForOrg', {
          org: createdOrg._id.toString(),
          orgType: createdOrg.orgType,
          adminOrg: user?.org?.toString()
        });
        this.emitEvent('org.CloneDefaultJobsDynamicFieldsForOrg', {
          org: createdOrg._id.toString(),
          orgType: createdOrg.orgType,
          adminOrg: user?.org?.toString()
        });

        if (createOrgDto.assignTo && Array.isArray(createOrgDto.assignTo)) {
          const assignToUsers = await this.basicUserModel.find({ _id: { $in: createOrgDto.assignTo } }).exec();
          if (!assignToUsers || assignToUsers.length === 0) {
            this.logger.log(`No users found for the provided assignTo IDs.`);
          } else {
            const assignToEmails = assignToUsers.map(user => {
              this.emitEvent('accountOrg.assignTo', {
                title: createOrgDto.title,
                email: user.email,
                assignTo: user?.firstName,
                createdBy: createdBy?.firstName
              });
              return user.email;
            });
            this.logger.log('Assign To Emails:', assignToEmails);
          }
        }


      }
      else {
        this.emitEvent('org.created', {
          title: createOrgDto.title,
          email: createOrgDto?.contactDetails?.[0]?.contactEmail ?? null,
        });
        this.emitEvent('org.workflow', { createdOrg });
        const createdById = createdOrg.createdBy
          ? new Types.ObjectId((createdOrg.createdBy as any)._id || createdOrg.createdBy)
          : undefined;

        // if (!createdById) {
        //   throw new BadRequestException('Invalid createdBy ID');
        // }

        const user = await this.basicUserModel.findOne({ _id: createdById });
        if (createdOrg.orgType === OrgType.CUSTOMER_ORG) {
          this.emitEvent('org.CloneDefaultDynamicFieldsForOrg', {
            org: createdOrg._id.toString(),
            orgType: createdOrg.orgType,
            adminOrg: user?.org?.toString()
          });
          this.emitEvent('org.CloneDefaultJobsDynamicFieldsForOrg', {
            org: createdOrg._id.toString(),
            orgType: createdOrg.orgType,
            adminOrg: user?.org?.toString()
          });
          if (createOrgDto.assignTo && Array.isArray(createOrgDto.assignTo)) {
            const assignToUsers = await this.basicUserModel.find({ _id: { $in: createOrgDto.assignTo } }).exec();
            if (!assignToUsers || assignToUsers.length === 0) {
              this.logger.log(`No users found for the provided assignTo IDs.`);
            } else {
              const assignToEmails = assignToUsers.map(user => {
                this.emitEvent('clientOrg.assignTo', {
                  title: createOrgDto.title,
                  email: user.email,
                  assignTo: user?.firstName,
                  createdBy: createdBy?.firstName
                });
                return user.email;
              });
              this.logger.log('Assign To Emails:', assignToEmails);
            }
          }

        }
      }

      if (createOrgDto.createAdminUser) {
        this.emitEvent('org.createAdminUser', { createdOrg, createOrgDto });
      }


      return createdOrg;
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new InternalServerErrorException(`Error while creating org: ${error.message}`);
    }
  }

  async verifyOTP(email: string, otpCode: string): Promise<boolean> {
    try {
      let org = await this.orgModel.findOne({
        'contactDetails.contactEmail': email,
        otpCode,
        isDeleted: false,
        verificationExpires: { $gt: new Date() }
      }).exec();

      if (!org) {
        throw new BadRequestException('Invalid or expired OTP code');
      }

      org.isVerified = true;
      // this.emitEvent('org.created', {
      //   title: org.title,
      //   email: org?.contactDetails?.[0]?.contactEmail ?? null,
      // });
      this.emitEvent('org.workflow', { createdOrg: org });
      await org.save();

      return true;
    } catch (error) {
      this.logger.error(`Error verifying organization OTP: ${error.message}`);
      throw new BadRequestException(`Failed to verify OTP: ${error.message}`);
    }
  }

  async resendOrgOTP(orgEmail: string): Promise<void> {
    try {
      let org = await this.orgModel.findOne({ 'contactDetails.contactEmail': orgEmail, isDeleted: false }).exec();

      if (!org) {
        throw new NotFoundException('Organization not found or has been deleted.');
      }

      const newOtpCode = Math.floor(100000 + Math.random() * 900000).toString();
      const verificationExpires = new Date();
      verificationExpires.setMinutes(verificationExpires.getMinutes() + 10);

      org.otpCode = newOtpCode;
      org.verificationExpires = verificationExpires;
      await org.save();

      this.emitEvent('org.resend.otp', {
        title: org.title,
        email: org?.contactDetails?.[0]?.contactEmail ?? null,
        otpCode: newOtpCode
      });

      this.logger.debug(`Resent OTP to ${orgEmail}: ${newOtpCode}`);
      console.log(`Your OTP code is ${newOtpCode}. Please verify your email to complete the organization registration. It is valid for 10 minutes.`);
    } catch (error) {
      this.logger.error(`Error resending OTP: ${error.message}`);
      throw new BadRequestException(`Failed to resend OTP: ${error.message}`);
    }
  }

  async markAsOnboarded(orgId: Types.ObjectId): Promise<OrgDocument> {
    try {
      // Find the current status of the organization
      const org = await this.orgModel.findById(orgId).exec();

      if (!org) {
        throw new BadRequestException(`Organization with ID ${orgId} not found.`);
      }

      // Toggle the value of isOnboarded (if it's true, set it to false, otherwise set it to true)
      const newStatus = !org.isOnboarded;

      const updatedOrg = await this.orgModel
        .findByIdAndUpdate(
          orgId,
          { isOnboarded: newStatus },
          { new: true }
        )
        .exec();

      if (!updatedOrg) {
        throw new BadRequestException(`Organization with ID ${orgId} not found.`);
      }

      // Emit an event to log or handle the onboarding completion if needed
      this.emitEvent('org.onboarded', { orgId: updatedOrg._id, title: updatedOrg.title });

      return updatedOrg;
    } catch (error) {
      console.error(`Error marking organization as onboarded: ${error.message}`);
      throw new InternalServerErrorException(`Failed to update onboarding status: ${error.message}`);
    }
  }


  async getOnlyActiveOrgs(page: number, limit: number): Promise<OrgDocument[]> {
    const populateOptions = this.getPopulateOptions();
    try {
      return await this.orgModel.find({ isDeleted: false })
        .sort({ updatedAt: -1 })
        .populate(populateOptions)
        .skip((page - 1) * limit)
        .limit(limit)
        .exec();
    } catch (error) {
      throw new InternalServerErrorException(`Error while fetching active orgs: ${error?.message}`);
    }
  }

  async getOnlySoftDeletedOrgs(page: number, limit: number): Promise<OrgDocument[]> {
    const populateOptions = this.getPopulateOptions();
    try {
      return await this.orgModel.find({ isDeleted: true })
        .sort({ updatedAt: -1 })
        .populate(populateOptions)
        .skip((page - 1) * limit)
        .limit(limit)
        .exec();
    } catch (error) {
      throw new InternalServerErrorException(`Error while fetching only soft deleted orgs: ${error?.message}`);
    }

  }

  async getOrgsByCustomStatus(status: Types.ObjectId, page: number, limit: number) {
    try {
      const orgs = await this.orgModel
        .find({ customStatus: status })
        .sort({ updatedAt: -1 })
        .populate({ path: 'country', select: '_id countryName countryPhoneCode currencyCode isDeleted createdAt updatedAt __v', model: 'Country' })
        .populate({ path: 'state', select: '_id stateName country isDeleted createdAt updatedAt __v', model: 'State' })
        .populate({
          path: 'customStatus',
          select: '_id name'
        })
        .skip((page - 1) * limit)
        .limit(limit)
        .exec();
      return orgs;
    } catch (error) {
      throw new InternalServerErrorException(`Error while fetching orgs by status ID: ${error.message}`);
    }
  }


  async getOrgsCountByCustomStatus(orgType: string): Promise<Array<{ id: string; name: string; orgType: string; count: number }>> {
    try {
      // Fetch all statuses for 'Org' type (assuming you have different statuses for different orgTypes)
      const allStatuses = await this.statusService.findAllStatusByType(orgType);

      const statusMap: Record<string, { id: string; name: string; orgType: string; count: number }> = {};

      // const matchStage = orgType ? { orgType } : {};

      const result = await this.orgModel.aggregate([
        {
          $match: { orgType } // Match by orgType if provided
        },
        {
          $lookup: {
            from: 'status', // Join with Status collection
            localField: 'customStatus',
            foreignField: '_id',
            as: 'statusDetails',
          }
        },
        {
          $unwind: '$statusDetails' // Unwind the joined status details
        },
        {
          $group: {
            _id: {
              statusId: '$statusDetails._id', // Group by status id
              orgType: '$orgType' // Also group by orgType
            },
            statusName: { $first: '$statusDetails.name' },
            orgType: { $first: '$orgType' },
            count: { $sum: 1 } // Count the number of orgs for each status and orgType
          }
        },
        {
          $project: {
            _id: 0, // Exclude the _id field
            id: '$_id.statusId', // Include the status ID
            name: '$statusName', // Rename _id field to statusName
            orgType: '$orgType', // Include orgType in the output
            count: 1 // Include the count field
          }
        }
      ]);

      result.forEach(entry => {
        const key = `${entry.id}-${entry.orgType}`;
        statusMap[key] = {
          id: entry.id,
          name: entry.name,
          orgType: entry.orgType,
          count: (statusMap[key]?.count || 0) + entry.count
        };
      });

      allStatuses.forEach(status => {
        const key = `${status._id.toString()}-${orgType}`;
        if (!statusMap[key]) {
          statusMap[key] = {
            id: status._id.toString(),
            name: status.name,
            orgType: orgType, // Set orgType to the one provided
            count: 0, // Set count to 0 if no orgs exist for this status and orgType
          };
        }
      });

      // Convert the result to an array of objects
      const statusArray: Array<{ id: string; name: string; orgType: string; count: number }> = Object.values(statusMap);

      return statusArray;
    } catch (error) {
      throw new InternalServerErrorException(`Error while fetching orgs count by status and orgType: ${error.message}`);
    }
  }

  async changeCustomStatus(orgId: Types.ObjectId, changeStatusDto: ChangeCustomStatusDto, user: Object) {
    try {
      console.log("status", JSON.stringify(changeStatusDto))
      // 1. Find the existing Org by its ID
      const existingOrg = await this.orgModel.findById(orgId).populate<{ status: StatusDocument }>({
        path: 'customStatus',
        select: '_id name'
      });;

      if (!existingOrg) {
        throw new NotFoundException(`Org with ID "${orgId}" not found`);
      }

      const { customStatus, ...comment } = changeStatusDto;
      const statusObjId = new Types.ObjectId(customStatus); // Ensure status is an ObjectId

      // 2. Find the new status by its ID
      const newStatus = await this.statusService.findById(statusObjId);
      if (!newStatus) {
        throw new BadRequestException(`Status with ID "${newStatus}" not found`);
      }

      // 3. (Optional) Check if the new status is the same as the current status
      // if (existingOrg.status && existingOrg.status.equals(statusObjId)) {
      //   throw new ConflictException(`Org already has the status "${newStatus.name}"`);
      // }

      // 4. (Optional) Handle status change hierarchy (SCD - Slowly Changing Dimensions)
      // if (existingOrg.status) {
      //   newStatus.parentStatus = existingOrg.status._id; // Keep track of old status
      //   await newStatus.save();
      // }

      // 5. Log the status change as a comment (if needed)
      // const createdComment = new this.commentModel(commentDto);
      // createdComment.org = orgId;
      // await createdComment.save();

      // 6. Update the status of the Org and return the updated Org with the new status populated
      const updatedOrg = await this.orgModel.findOneAndUpdate(
        { _id: orgId },
        { customStatus: statusObjId },
        { new: true }
      )
        .populate<{ status: StatusDocument }>({
          path: 'customStatus',
          select: '_id name functionality'
        });

      if (updatedOrg?.orgType === OrgType.ADMIN_CUSTOMER_ORG) {
        const user = await this.basicUserModel.findOne({ email: updatedOrg?.contactDetails?.[0]?.contactEmail ?? '' });
        // this.logger.log(JSON.stringify(updatedOrg.customStatus))
        if (user && (updatedOrg.customStatus as any)?.functionality === StatusFunctionality.ACTIVATE) {
          // this.logger.log(JSON.stringify(user))
          user.isSuspended = false;
          await user.save();
          const isThere = await this.userService.findUserByEmailExists(user.email);
          if (isThere) {
            this.emitEvent('customer.reApproved', isThere);
          }
        }
        else if (user) {
          this.emitEvent('org.rejectOrgAdmin', {
            existingOrg: existingOrg,
            updatedOrg: updatedOrg,
            user: null,
          });
          user.isSuspended = true;
          await user.save();
        }
      }

      if (updatedOrg?.orgType === OrgType.VENDOR_ORG) {
        this.emitEvent('org.custom.status.changed', { existingOrg, updatedOrg, comment, user });
      }
      return updatedOrg;

    } catch (error) {
      this.logger.error(`An error occurred while changing the status of Org by ID ${orgId}. ${error?.message}`);
      // Re-throw specific exceptions
      if (error instanceof BadRequestException || error instanceof NotFoundException || error instanceof ConflictException) {
        throw error;
      }
      // General error handling
      throw new InternalServerErrorException;
    }
  }


  async getAllOrgs(query: QueryOrgDto): Promise<OrgDocument[]> {
    const populateOptions = this.getPopulateOptions();
    try {
      const { title, industryId, accountTypeId, orgType, page = 1, limit = 10 } = query;

      let conditions: any = { isDeleted: false };

      if (title) {
        const regex = new RegExp(title, 'i'); // 'i' for case-insensitive search
        conditions.title = regex;
      }

      if (industryId) {
        conditions.industryOrDomain = industryId;
      }

      if (accountTypeId) {
        conditions.accountType = accountTypeId;
      }

      if (orgType) {
        conditions.orgType = orgType;
      }

      const orgs = await this.orgModel.find(conditions)
        .sort({ updatedAt: -1 })
        .populate(populateOptions)
        .skip((page - 1) * limit)
        .limit(limit)
        .sort({ updatedAt: -1 })
        .exec();
      return orgs;
    } catch (error) {
      throw new InternalServerErrorException(`Error while fetching all orgs: ${error?.message}`);
    }
  }

  async findOne(orgId: Types.ObjectId) {
    const populateOptions = this.getPopulateOptions();
    try {
      const org = await this.orgModel.findById(orgId)
        .populate(populateOptions)
        .exec();

      if (!org || org.isDeleted) {
        throw new NotFoundException(`Org not found with ID ${orgId}`);
      }

      return org;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      } else {
        throw new InternalServerErrorException(`Error while fetching org with ID ${orgId}: ${error.message}`);
      }
    }
  }

  async getOrgCountByType(orgType: string, userId: Types.ObjectId): Promise<Object> {
    try {
      const orgCounts = await this.orgModel.countDocuments({ orgType, isDeleted: false, createdBy: userId }).exec();
      return {
        count: orgCounts
      };
    } catch (error) {
      throw new InternalServerErrorException(`Error while fetching org counts by type: ${error.message}`);
    }
  }

  async searchOrgs(title: string) {
    const populateOptions = this.getPopulateOptions();
    try {
      if (!title) {
        throw new HttpException('Name parameter is required', HttpStatus.BAD_REQUEST);
      }
      const regex = new RegExp(title, 'i'); // 'i' for case-insensitive
      return await this.orgModel.find({ title: { $regex: regex }, isDeleted: false })
        .sort({ updatedAt: -1 })
        .populate(populateOptions)
        .exec();
    } catch (error) {
      // this.logger.error(`Error while searching for orgs: ${error.message}`, error.stack);
      throw new InternalServerErrorException(`Error while searching for orgs: ${error?.message}`);
    }
  }

  async filterOrgs(queryDto: QueryOrgDto, userId: Types.ObjectId): Promise<OrgDocument[]> {
    const { title, industryId, countryId, accountTypeId, orgType, page, limit } = queryDto;
    const populateOptions = this.getPopulateOptions();
    try {
      const query: any = { isDeleted: false, createdBy: userId }; // Ensure we only fetch non-deleted records
      const page = Number(queryDto.page) || 1;
      const limit = Number(queryDto.limit) || 10;


      if (industryId) {
        query.industryOrDomain = industryId;
      }

      if (orgType) {
        query.orgType = orgType;
      }


      if (countryId) {
        query.country = countryId;
      }

      if (accountTypeId) {
        query.accountType = accountTypeId;
      }

      let searchQueryStage = [];
      if (title) {
        searchQueryStage.push({
          $match: {
            $or: [
              { title: { $regex: new RegExp(title, 'i') } },
              { 'country.countryName': { $regex: new RegExp(title, 'i') } },
              { 'state.stateName': { $regex: new RegExp(title, 'i') } },
              { 'city.name': { $regex: new RegExp(title, 'i') } }
            ]
          }
        });
      }
      // const orgs = await this.orgModel.find(query)
      //   .sort({ updatedAt: -1 })
      //   .populate(populateOptions)
      //   .skip((page - 1) * limit)
      //   .limit(limit)
      //   .exec();



      const filteredorgs = await this.orgModel.aggregate([
        { $match: query }, // Apply base filters
        { "$addFields": { "country": { "$toObjectId": "$country" } } },
        {
          $lookup: {
            from: 'countries',
            localField: 'country',
            foreignField: '_id',
            as: 'country'
          }
        },
        { $unwind: { path: '$country', preserveNullAndEmptyArrays: true } },
        { "$addFields": { "state": { "$toObjectId": "$state" } } },
        {
          $lookup: {
            from: 'states',
            localField: 'state',
            foreignField: '_id',
            as: 'state'
          }
        },
        { $unwind: { path: '$state', preserveNullAndEmptyArrays: true } },
        { "$addFields": { "city": { "$toObjectId": "$city" } } },
        {
          $lookup: {
            from: 'cities',
            localField: 'city',
            foreignField: '_id',
            as: 'city'
          }
        },
        { $unwind: { path: '$city', preserveNullAndEmptyArrays: true } },
        ...searchQueryStage, // This will add $match only if title is present
        { $sort: { updatedAt: -1 } },
        { $skip: (page - 1) * limit },
        { $limit: limit },
        { $project: { _id: 1 } }
      ]);

      const orgIdsList = filteredorgs.map(org => org._id); // Extract IDs

      const orgs = await this.orgModel.find({ _id: { $in: orgIdsList } })
        .sort({ updatedAt: -1 })
        .populate(populateOptions)
        .skip((page - 1) * limit)
        .limit(limit)
        .exec();


      return orgs;
    } catch (error) {
      throw new InternalServerErrorException(`An error occurred while fetching orgs: ${error?.message}.`);
    }
  }

  async filterOrgsByType(orgType: string, isDeleted: boolean, page: number, limit: number, userId: Types.ObjectId, assignTo?: string, orgId?: string, contactId?: string): Promise<OrgDocument[]> {
    const populateOptions = this.getPopulateOptions();
    try {
      const existingUser = await this.basicUserModel.findById(userId).exec();
      console.log(userId)
      console.log(assignTo)
      let conditions: any = { isDeleted: false };
      if (orgType) {
        conditions.orgType = orgType;
        // conditions.orgType = { $in: [orgType] }; // string must match any element in the array
      }
      if (assignTo && existingUser?.roles?.includes(Role.AccountManager)) {
        // conditions.assignTo = { $in: [assignTo] };
        // conditions.createdBy = { $in: [userId, adminUserId] };
        conditions.$or = [
          { createdBy: { $in: [userId] } },
          { assignTo: { $in: [assignTo] } }
        ];
      }
      else {
        // conditions.createdBy = userId;
        conditions.createdByOrg = orgId;
      }
      if (isDeleted !== undefined) {
        conditions.isDeleted = isDeleted;
      }
      console.log(conditions)
      // this.logger.log(JSON.stringify(conditions));
      const orgs = await this.orgModel.find(conditions)
        .sort({ updatedAt: -1 })
        .populate(populateOptions)
        .sort({ updatedAt: -1, createdAt: -1 })
        .skip((page - 1) * limit)
        .limit(limit)
        .exec();

      console.log(orgs)
      if (contactId) {
        console.log(contactId)
        const contact = await this.contactModel.findById(contactId).exec();

        if (contact?.accountOrg) {
          const accountOrg = await this.orgModel.findById(contact.accountOrg)
            .populate(populateOptions)
            .exec();

          if (accountOrg) {
            // Optionally avoid duplicates by checking if it's already in the list
            const exists = orgs.some(org => org._id.toString() === accountOrg._id.toString());
            if (!exists) {
              orgs.push(accountOrg);
            }
          }
        }
      }
      console.log(orgs)
      return orgs;
    } catch (error) {
      throw new InternalServerErrorException(`An error occurred while fetching orgs: ${error?.message}.`);
    }
  }

  async filterOrgsByTypeReq(user: any) {
    const populateOptions = this.getPopulateOptions();
    try {
      const userId = user._id.toString();
      console.log(userId)

      let conditions: any = { isDeleted: false };
      conditions.orgType = { $in: [OrgType.CUSTOMER_ORG, OrgType.ACCOUNT_ORG] };
      if (user.roles?.includes(Role.AccountManager)) {
        conditions.$or = [
          { createdBy: userId },
          { assignTo: { $in: [userId] } }
        ];
      } else {
        conditions.createdByOrg = user.org._id;
      }
      // this.logger.log(JSON.stringify(conditions));
      const orgs = await this.orgModel.find(conditions)
        // .select('_id title') // only fetch _id and title
        .sort({ updatedAt: -1 })
        .sort({ updatedAt: -1, createdAt: -1 })
        // .skip((page - 1) * limit)
        // .limit(limit)
        .exec();

      const allOrgs = [
        {
          label: 'Clients',
          code: 'customer_org',
          items: orgs.filter(org => org.orgType === OrgType.CUSTOMER_ORG)
        },
        {
          label: 'Accounts',
          code: 'account_org',
          items: orgs.filter(org => org.orgType === OrgType.ACCOUNT_ORG)
        }
      ];

      return { allOrgs };
    } catch (error) {
      throw new InternalServerErrorException(`An error occurred while fetching orgs: ${error?.message}.`);
    }
  }

  async filterOrgsByTypeVendor(orgType: string, page: number, limit: number, user?: any): Promise<OrgDocument[]> {
    const populateOptions = this.getPopulateOptions();

    try {
      let conditions: any = { isDeleted: false, companyId: user.org._id };

      if (orgType) {
        conditions.orgType = orgType;
      }

      // Fetch organizations with population
      const orgs = await this.orgModel.find(conditions)
        .populate(populateOptions) // Ensures `companyId` is populated with `createdBy`
        .sort({ updatedAt: -1, createdAt: -1 })
        .skip((page - 1) * limit)
        .limit(limit)
        .exec();

      // const filteredOrgs = userId 
      // ? orgs.filter(org => (org.companyId?.createdBy as any)?._id?.toString() === userId)
      // : orgs;


      return orgs;
    } catch (error) {
      throw new InternalServerErrorException(`An error occurred while fetching orgs: ${error?.message}.`);
    }
  }


  async filterOrgsAndVendorInvites(
    orgType: string,
    page: number,
    limit: number,
    user: any,
    searchName?: string
  ) {
    // const populateOptions = this.getPopulateOptions();

    try {
      let conditions: any = { isDeleted: false, companyId: user.org._id };

      if (orgType) {
        conditions.orgType = orgType;
      }

      if (searchName) {
        conditions.title = { $regex: new RegExp(searchName, 'i') }; // Case-insensitive search on title (or name)
      }

      // Fetch organizations
      const orgs = await this.orgModel
        .find(conditions)
        .populate({ path: 'country', select: '_id countryId countryName' })
        .populate({ path: 'state', select: '_id stateId stateName' })
        .populate({ path: 'city', select: '_id name' })
        .populate({ path: 'customStatus', select: '_id name' })
        .populate({
          path: 'companyId',
          select: '_id title orgType createdBy',
          populate: { path: 'createdBy', select: '_id firstName lastName email roles' },
        })
        .sort({ updatedAt: -1, createdAt: -1 })
        .exec();

      const orgIds = orgs.map(org => org._id);
      const userLogos = await this.basicUserModel
        .find({ org: { $in: orgIds } }, { logo: 1, org: 1 })
        .populate({ path: 'logo', select: '_id locationUrl originalName fileSize fileType', model: 'FileMetadata' })
        .exec();

      // Map orgId => logo
      const orgLogoMap = new Map(userLogos.map(user => [(user?.org ?? '').toString(), user.logo]));

      const vendorInviteQuery: any = {
        createdBy: user._id.toString(),
      };

      if (searchName) {
        vendorInviteQuery.vendorName = { $regex: new RegExp(searchName, 'i') };
      }


      // Fetch vendor invites
      const vendorInvites = await this.vendorInviteModel
        .find(vendorInviteQuery) // Assuming user._id is the current user
        .populate({ path: 'createdBy', select: '_id firstName lastName email roles' })
        .populate({
          path: 'companyId',
          select: '_id title orgType createdBy',
          populate: { path: 'createdBy', select: '_id firstName lastName email roles' },
        })
        .sort({ createdAt: -1 })
        .exec();

      const normalizedOrgs = orgs.map(org => {
        const orgObj = org.toObject() as { createdAt?: Date; updatedAt?: Date }; // Explicit type assertion

        return {
          ...orgObj,
          contactEmail: org.contactDetails?.[0]?.contactEmail || null,
          name: org.title,
          logo: orgLogoMap.get(org._id.toString()) || null, // Attach logo
          statues:
            typeof org.customStatus === "object" && org.customStatus !== null && "name" in org.customStatus
              ? (org.customStatus as { name: string }).name
              : null,
          _originalContactDetails: org.contactDetails,
          createdAt: orgObj.createdAt || new Date(0), // Ensure it's defined
          updatedAt: orgObj.updatedAt || new Date(0)
        };
      });

      const normalizedVendorInvites = vendorInvites.map(invite => {
        const inviteObj = invite.toObject() as { createdAt?: Date; updatedAt?: Date };

        return {
          ...inviteObj,
          contactEmail: invite.email,
          name: invite.vendorName,
          statues: invite.status || null,
          _originalEmail: invite.email,
          createdAt: inviteObj.createdAt || new Date(0),
          updatedAt: inviteObj.updatedAt || new Date(0)
        };
      });

      // Merge results
      const combinedResults = [...normalizedOrgs, ...normalizedVendorInvites].sort(
        (a, b) =>
          new Date(b.updatedAt || b.createdAt).getTime() -
          new Date(a.updatedAt || a.createdAt).getTime()
      );

      // combinedResults.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

      // Paginate results manually
      const startIndex = (page - 1) * limit;
      const paginatedResults = combinedResults.slice(startIndex, startIndex + limit);

      // After combining the results and applying pagination
      const totalCount = combinedResults.length;
      return {
        totalCount,  // This will now reflect the combined count of merged orgs and vendor invites
        data: paginatedResults,
      };

    } catch (error) {
      throw new InternalServerErrorException(`An error occurred: ${error?.message}.`);
    }
  }


  async changeStatus(orgId: Types.ObjectId, changeStatusDto: ChangeStatusOrgDto, user: BasicUser) {
    const populateOptions = this.getPopulateOptions();
    try {
      await this.findOne(orgId);
      const { status, comment } = changeStatusDto
      const updateData: any = { status };
      if (status === 'client') {
        updateData.orgType = OrgType.CUSTOMER_ORG;
      }
      const updatedOrg = await this.orgModel.findByIdAndUpdate(orgId, { $set: updateData }, { new: true })
        .populate(populateOptions)
        .exec();
      this.emitEvent('org.status.changed', { updatedOrg, comment, user });
      return updatedOrg;
    } catch (error) {
      // this.logger.error(error);
      // this.logger.error(`An error occurred in fetching org by Id ${orgId}. ${error?.message}`);
      throw new InternalServerErrorException(`An error occurred in fetching org by Id ${orgId}: ${error?.message}`);
    }
  }

  // async assignAdmin(orgId: Types.ObjectId, userId: Types.ObjectId) {
  //   try {
  //     const org = await this.findOne(orgId);
  //     return this.userService.assignOrg(org._id, userId, org.orgType);
  //   } catch (error) {
  //     // this.logger.error(error);
  //     // this.logger.error(`An error occurred in fetching org by Id ${orgId}. ${error?.message}`);
  //     throw new InternalServerErrorException(`An error occurred while fetching org  by Id ${orgId}. ${error?.message}.`);
  //   }
  // }


  // async getAllVendors(orgId: Types.ObjectId) {
  //   try {

  //     const org = await this.orgModel.findById(orgId);
  //     console.log(org);
  //     if (!org) {
  //       throw new NotFoundException(`Organization with ID ${orgId} not found.`);
  //     }

  //     const allVendorsForCompany = await this.orgModel.find({ 
  //       companyId: org._id.toString(), 
  //       orgType: OrgType.VENDOR_ORG
  //     }).exec();

  //     console.log("All possible vendors for this company:", allVendorsForCompany);


  //     const vendors = await this.orgModel.find(
  //       { companyId: org._id.toString(), orgType: OrgType.VENDOR_ORG,  isDeleted: false }).exec();


  //     return vendors;

  //   } catch (error) {
  //     throw new InternalServerErrorException(`Failed to fetch vendors for organization ${orgId}: ${error?.message}`);
  //   }
  // }

  async getAllVendors(orgId: Types.ObjectId) {
    try {
      const org = await this.orgModel.findById(orgId);
      console.log(org);
      if (!org) {
        throw new NotFoundException(`Organization with ID ${orgId} not found.`);
      }

      // First, find the Active status from your Status model
      const activeStatus = await this.statusModel.findOne({ name: 'Active', statusType: 'vendor-org' });
      if (!activeStatus) {
        console.log("Active status not found in the database");
        // Fallback - get all vendors without status filtering if needed
      }

      // Set up the query conditions
      const queryConditions = {
        companyId: org._id.toString(),
        orgType: OrgType.VENDOR_ORG,
        isDeleted: false,
        customStatus: org.customStatus
      };

      // Add the customStatus filter if we found the Active status
      if (activeStatus) {
        queryConditions.customStatus = activeStatus._id;
      }

      // Create the query with populate for customStatus
      let vendorQuery = this.orgModel.find(queryConditions);

      // Set up populate options
      const populateOptions = [];
      if (this.orgModel.schema.paths['customStatus']) {
        populateOptions.push({ path: 'customStatus', select: '_id name', model: 'Status' });
      }

      // Apply populate if there are options
      if (populateOptions.length > 0) {
        vendorQuery = vendorQuery.populate(populateOptions);
      }

      // Execute the query
      const vendors = await vendorQuery.exec();
      return vendors;
    } catch (error) {
      throw new InternalServerErrorException(`Failed to fetch vendors for organization ${orgId}: ${error?.message}`);
    }
  }


  // TODO: Check org from dto whether it is org or not,

  async addVendor(orgId: Types.ObjectId, createRemoveVendorCustomerDto: CreateRemoveVendorCustomerDto) {
    try {
      const { id } = createRemoveVendorCustomerDto;

      const validId = validateObjectId(id);
      if (!validId) {
        throw new BadRequestException('Invalid vendor ID provided.');
      }

      const org = await this.orgModel.findById(orgId);
      if (!org) {
        throw new NotFoundException(`Organization with ID ${orgId} not found.`);
      }

      const vendor = await this.orgModel.findById(validId);
      if (!vendor) {
        throw new NotFoundException(`Vendor with ID ${validId} not found.`);
      }

      if (!org.vendors) {
        org.vendors = [];
      }

      if (!org.vendors.includes(validId)) {
        org.vendors.push(validId);
        await org.save();
      } else {
        throw new BadRequestException(`Vendor with ID ${validId} is already added.`);
      }

      return org;
    } catch (error) {
      throw new InternalServerErrorException(`An error occurred while adding vendor with ID ${createRemoveVendorCustomerDto.id} to organization ${orgId}. ${error?.message}`);
    }
  }

  async removeVendor(orgId: Types.ObjectId, createRemoveVendorCustomerDto: CreateRemoveVendorCustomerDto) {
    try {
      const { id } = createRemoveVendorCustomerDto;

      const validId = validateObjectId(id);
      if (!validId) {
        throw new BadRequestException('Invalid vendor ID provided. Please check the format of the ID.');
      }

      const org = await this.orgModel.findById(orgId);
      if (!org) {
        throw new NotFoundException(`Organization with ID ${orgId} not found.`);
      }

      if (!org.vendors || org.vendors.length === 0) {
        throw new BadRequestException(`No vendors found for organization with ID ${orgId}.`);
      }

      if (!org.vendors.includes(validId)) {
        throw new BadRequestException(`Vendor with ID ${validId} not found in the organization's vendor list.`);
      }

      org.vendors = org.vendors.filter(vendorId => !vendorId.equals(validId));

      await org.save();
      return org;
    } catch (error) {

      throw new InternalServerErrorException(
        `An error occurred while removing vendor from organization ${orgId}. Error: ${error?.message || 'Unknown error'}.`
      );
    }
  }


  async getAllCustomers(orgId: Types.ObjectId) {
    try {

      const org = await this.findOne(orgId);
      if (!org) {
        throw new NotFoundException(`Organization with ID ${orgId} not found.`);
      }

      if (!org.customers || org.customers.length === 0) {
        return [];
      }

      const customerOrgs = await Promise.all(
        org.customers.map(async (customerId) => {
          const customer = await this.findOne(customerId);
          if (!customer) {
            console.warn(`Customer with ID ${customerId} not found. Skipping.`);
          }
          return customer;
        })
      );

      return customerOrgs.filter((customer) => customer !== undefined);
    } catch (error) {
      throw new InternalServerErrorException(`Failed to fetch vendors for organization ${orgId}.`);
    }
  }


  // TODO: Check org from dto whether it is org or not,

  async addCustomer(orgId: Types.ObjectId, createRemoveVendorCustomerDto: CreateRemoveVendorCustomerDto) {
    try {
      const { id } = createRemoveVendorCustomerDto;

      const validId = validateObjectId(id);
      if (!validId) {
        throw new BadRequestException('Invalid customer ID provided.');
      }

      const org = await this.orgModel.findById(orgId);
      if (!org) {
        throw new NotFoundException(`Organization with ID ${orgId} not found.`);
      }

      const customer = await this.orgModel.findById(validId);
      if (!customer) {
        throw new NotFoundException(`Vendor with ID ${validId} not found.`);
      }

      if (!org.customers) {
        org.customers = [];
      }

      if (!org.customers.includes(validId)) {
        org.customers.push(validId);
        await org.save();
      } else {
        throw new BadRequestException(`Vendor with ID ${validId} is already added.`);
      }

      return org;
    } catch (error) {
      throw new InternalServerErrorException(`An error occurred while adding customer with ID ${createRemoveVendorCustomerDto.id} to organization ${orgId}. ${error?.message}`);
    }
  }

  async removeCustomer(orgId: Types.ObjectId, createRemoveVendorCustomerDto: CreateRemoveVendorCustomerDto) {
    try {
      const { id } = createRemoveVendorCustomerDto;

      const validId = validateObjectId(id);
      if (!validId) {
        throw new BadRequestException('Invalid customer ID provided. Please check the format of the ID.');
      }

      const org = await this.orgModel.findById(orgId);
      if (!org) {
        throw new NotFoundException(`Organization with ID ${orgId} not found.`);
      }

      if (!org.customers || org.customers.length === 0) {
        throw new BadRequestException(`No customers found for organization with ID ${orgId}.`);
      }

      if (!org.customers.includes(validId)) {
        throw new BadRequestException(`customer with ID ${validId} not found in the organization's customer list.`);
      }

      org.customers = org.customers.filter(vendorId => !vendorId.equals(validId));

      await org.save();
      return org;
    } catch (error) {
      throw new InternalServerErrorException(
        `An error occurred while removing customer from organization ${orgId}. Error: ${error?.message || 'Unknown error'}.`
      );
    }
  }


  async addMember(orgId: string, createMemberDto: CreateMemberDto) {
    try {
      const orgObjId = validateObjectId(orgId);
      const org = await this.findOne(orgObjId);
      return this.userService.addMemberToOrg(orgId, createMemberDto);
    } catch (error) {
      throw new InternalServerErrorException(`An error occurred while fetching org  by Id ${orgId}. ${error?.message}.`);
    }
  }

  async getAllMembers(orgId: Types.ObjectId, businessUnitId: string, searchTerm?: string, roles?: string[], userId?: string) {
    try {
      await this.findOne(orgId);
      return this.userService.getAllMembers(orgId, businessUnitId, searchTerm, roles, userId);
    } catch (error) {
      throw new InternalServerErrorException(`An error occurred while fetching org  by Id ${orgId}. ${error?.message}.`);
    }
  }

  async getAllDepartmentMembers(orgId: Types.ObjectId, businessUnitId: string, userId: string) {
    try {
      await this.findOne(orgId);
      return this.userService.getAllDepartmentMembers(orgId, businessUnitId, userId);
    } catch (error) {
      throw new InternalServerErrorException(`An error occurred while fetching org  by Id ${orgId}. ${error?.message}.`);
    }
  }

  async getAllMembersOrg(orgId: Types.ObjectId, businessUnitId: string[], searchTerm?: string, roles?: string[], userId?: string) {
    try {
      await this.findOne(orgId);
      return this.userService.getAllMembersOrg(orgId, businessUnitId, searchTerm, roles, userId);
    } catch (error) {
      throw new InternalServerErrorException(`An error occurred while fetching org  by Id ${orgId}. ${error?.message}.`);
    }
  }

  async getTeamMembersOfLead(orgId: Types.ObjectId, businessUnitId: string, leadId: string) {
    try {
      await this.findOne(orgId);
      return this.userService.getTeamMembersOfLead(orgId, businessUnitId, leadId);
    } catch (error) {
      throw new InternalServerErrorException(`An error occurred while fetching org  by Id ${orgId}. ${error?.message}.`);
    }
  }

  async getAllMembersByOrg(orgId: Types.ObjectId, page: number, limit: number, searchTerm?: string, departmentId?: Types.ObjectId) {
    try {
      await this.findOne(orgId);
      return this.userService.getAllMembersByOrg(orgId, page, limit, searchTerm, departmentId);
    } catch (error) {
      throw new InternalServerErrorException(`An error occurred while fetching org  by Id ${orgId}. ${error?.message}.`);
    }
  }

  async getAllMembersByOrgAndRole(orgId: Types.ObjectId, page: number, limit: number, searchTerm?: string, departmentId?: Types.ObjectId) {
    try {
      await this.findOne(orgId);
      return this.userService.getAllMembersByOrgAndDepartment(orgId, page, limit, searchTerm, departmentId);
    } catch (error) {
      throw new InternalServerErrorException(`An error occurred while fetching org  by Id ${orgId}. ${error?.message}.`);
    }
  }

  async getMember(orgId: Types.ObjectId, memberId: Types.ObjectId) {
    try {
      await this.findOne(orgId);
      return this.userService.getMember(orgId, memberId);
    } catch (error) {
      throw new InternalServerErrorException(`An error occurred while fetching org  by Id ${orgId}. ${error?.message}.`);
    }
  }

  async updateMember(orgId: Types.ObjectId, memberId: Types.ObjectId, updateMemberDto: UpdateMemberDto, userId: string) {
    try {
      const org = await this.findOne(orgId);

      const updatePayload: any = { ...updateMemberDto };

      // if ('reportingTo' in updateMemberDto) {
      //   updatePayload.reportingTo = updateMemberDto.reportingTo === '' ? null : updateMemberDto.reportingTo;
      // }
      if ('reportingTo' in updateMemberDto) {
        updatePayload.reportingTo =
          Array.isArray(updateMemberDto.reportingTo) && updateMemberDto.reportingTo.length === 0
            ? null
            : updateMemberDto.reportingTo;
      }
      return this.userService.updateMemberOfOrg(orgId, memberId, updatePayload, userId);
    } catch (error) {
      throw new InternalServerErrorException(`An error occurred while updating the member. ${error?.message}`);
    }
  }

  async removeMember(orgId: Types.ObjectId, memberId: Types.ObjectId) {
    try {
      await this.findOne(orgId);
      return this.userService.removeMemberFromOrg(orgId, memberId);
    } catch (error) {
      throw new InternalServerErrorException(`An error occurred while fetching org  by Id ${orgId}. ${error?.message}.`);
    }
  }

  async bulkRemoveMembers(orgId: Types.ObjectId, memberIds: Types.ObjectId[]) {
    try {
      // Perform the update to soft delete members
      await this.findOne(orgId);
      return this.userService.bulkRemoveMembers(orgId, memberIds);
    } catch (error) {
      this.logger.error(`Error while bulk deleting members: ${error.message}`, error.stack);
      throw new InternalServerErrorException('Error while bulk deleting members.');
    }
  }


  async hardDeleteMember(orgId: Types.ObjectId, memberId: Types.ObjectId) {
    try {
      const org = await this.findOne(orgId);
      return this.userService.hardRemoveMemberFromOrg(orgId, memberId);
    } catch (error) {
      throw new InternalServerErrorException(`An error occurred while fetching org  by Id ${orgId}. ${error?.message}.`);
    }
  }

  async addUser(orgId: Types.ObjectId, userId: Types.ObjectId, role?: string) {
    try {
      const org = await this.findOne(orgId);
      return this.userService.addUserToOrg(org._id, userId, role);
    } catch (error) {
      // this.logger.error(error);
      // this.logger.error(`An error occurred in fetching org by Id ${orgId}. ${error?.message}`);
      throw new InternalServerErrorException(`An error occurred while fetching org  by Id ${orgId}. ${error?.message}.`);
    }
  }

  // async deleteUser(userId: Types.ObjectId) {
  //   try {
  //     const user = this.userService.removeUserFromOrg(userId);
  //     return user;
  //   } catch (error) {
  //     // this.logger.error(error);
  //     // this.logger.error(`An error occurred in deleting user by Id ${userId} from org. ${error?.message}`);
  //     throw new InternalServerErrorException(`An error occurred while deleting user  by Id ${userId}. ${error?.message}.`);
  //   }
  // }

  async assignTo(orgId: Types.ObjectId, newOwnerId: string[], commentDto: CommentDto, user: BasicUser) {
    const populateOptions = this.getPopulateOptions();
    try {
      const existingOrg = await this.findOne(orgId);
      const updatedOrg = await this.orgModel.findByIdAndUpdate(
        orgId,
        { assignTo: newOwnerId },
        { new: true }
      )
        .populate(populateOptions)
        .exec();
      // console.log("updatedOrg", updatedOrg?.assignTo);
      // console.log("existingOrg", existingOrg.assignTo);
      // this.logger.log(commentDto);
      // this.logger.log(user);
      // this.emitEvent('org.assign_to.changed', { updatedOrg, commentDto, user });

      if (existingOrg.orgType == OrgType.ACCOUNT_ORG) {
        const createdBy = await this.basicUserModel.findById(updatedOrg?.createdBy).exec();
        if (updatedOrg?.assignTo && Array.isArray(updatedOrg?.assignTo)) {
          const existingAssignToIds = (existingOrg?.assignTo as unknown as { _id: any }[]).map(user => user._id.toString());
          const updatedAssignToIds = (updatedOrg?.assignTo as unknown as { _id: any }[]).map(user => user._id.toString());

          // const existingAssignToIds = existingOrg?.assignTo?.map(user => user._id.toString());
          // console.log('Existing AssignTo:', existingAssignToIds);
          // console.log('Updated AssignTo:', updatedAssignToIds);
          const newAssignToIds = updatedAssignToIds.filter(
            id => !existingAssignToIds.some(existingId => existingId.toString() === id.toString())
          );
          console.log(newAssignToIds);

          if (newAssignToIds.length > 0) {
            const assignToUsers = await this.basicUserModel.find({ _id: { $in: newAssignToIds } }).exec();
            // const assignToUsers = await this.basicUserModel.find({ _id: { $in: updatedOrgDetails?.assignTo } }).exec();
            if (!assignToUsers || assignToUsers.length === 0) {
              this.logger.log(`No users found for the provided assignTo IDs.`);
            } else {
              const assignToEmails = assignToUsers.map(user => {
                this.emitEvent('accountOrg.assignTo', {
                  title: updatedOrg?.title,
                  email: user.email,
                  assignTo: user?.firstName,
                  createdBy: createdBy?.firstName
                });
                return user.email;
              });
              this.logger.log('Assign To Emails:', assignToEmails);
            }
          }
        }
      }
      if (existingOrg.orgType == OrgType.CUSTOMER_ORG) {
        const createdBy = await this.basicUserModel.findById(updatedOrg?.createdBy).exec();
        if (updatedOrg?.assignTo && Array.isArray(updatedOrg?.assignTo)) {
          // console.log('AssignTo:', updatedOrgDetails?.assignTo);
          const existingAssignToIds = (existingOrg?.assignTo as unknown as { _id: any }[]).map(user => user._id.toString());
          const updatedAssignToIds = (updatedOrg?.assignTo as unknown as { _id: any }[]).map(user => user._id.toString());

          // const existingAssignToIds = existingOrg?.assignTo?.map(user => user._id.toString());
          // console.log('Existing AssignTo:', existingAssignToIds);
          // console.log('Updated AssignTo:', updatedAssignToIds);
          const newAssignToIds = updatedAssignToIds.filter(
            id => !existingAssignToIds.some(existingId => existingId.toString() === id.toString())
          );
          console.log(newAssignToIds);
          if (newAssignToIds.length > 0) {
            const assignToUsers = await this.basicUserModel.find({ _id: { $in: newAssignToIds } }).exec();
            // const assignToUsers = await this.basicUserModel.find({ _id: { $in: updatedOrgDetails?.assignTo } }).exec();
            if (!assignToUsers || assignToUsers.length === 0) {
              this.logger.log(`No users found for the provided assignTo IDs.`);
            } else {
              const assignToEmails = assignToUsers.map(user => {
                this.emitEvent('clientOrg.assignTo', {
                  title: updatedOrg?.title,
                  email: user.email,
                  assignTo: user?.firstName,
                  createdBy: createdBy?.firstName
                });
                return user.email;
              });
              this.logger.log('Assign To Emails:', assignToEmails);
            }
          }
        }
      }


      return updatedOrg;
    } catch (error) {
      // this.logger.error(error);
      // this.logger.error(`An error occurred in fetching org by Id ${newOrgId}. ${error?.message}`);
      throw new InternalServerErrorException(`An error occurred in fetching org by Id ${orgId}: ${error?.message}`);
    }
  }

  async update(orgId: Types.ObjectId, updateOrgDto: UpdateOrgDto, user: BasicUser) {
    this.logger.log('Updating organization with DTO:', updateOrgDto);

    const unsetData: any = {};

    const fieldsToCheck: (keyof UpdateOrgDto)[] = ['logo', 'thumbnail', 'industryOrDomain', 'businessUnit', 'parentOrg', 'assignTo', 'country', 'state', 'city', 'accountType', 'addedByOrg', 'headCount', 'companyProfileName'];

    // Iterate through each field and handle empty strings
    fieldsToCheck.forEach((field) => {
      if (updateOrgDto[field] === '') {
        unsetData[field] = ''; // Mark for removal
        delete updateOrgDto[field]; // Remove from updateOrgDto
      }
    });

    try {
      // Fetch the existing org for comparison (if needed)
      const existingOrg = await this.findOne(orgId);

      if (!existingOrg) {
        this.logger.error(`Organization with ID ${orgId} not found`);
        throw new NotFoundException(`Organization with ID ${orgId} not found`);
      }

      if (updateOrgDto.companyProfileName) {
        const companies = await this.orgModel.find({ companyProfileName: updateOrgDto.companyProfileName, _id: { $ne: orgId } });
        if (companies.length) {
          this.logger.error('Company profile name with this name already exists');
          throw new BadRequestException('Company profile name with this name already exists');
        }
      }
      // Perform the update operation
      const updatedOrgDetails = await this.orgModel
        .findByIdAndUpdate(orgId,
          {
            $set: updateOrgDto,
            $unset: unsetData,
          },
          { new: true }
        ).exec();

      let updatedOrg: any;
      if (updatedOrgDetails?._id) {
        updatedOrg = await this.findOne(updatedOrgDetails._id);
      }
      if (!updatedOrg) {
        this.logger.error(`Failed to update organization with ID ${orgId}`);
        throw new InternalServerErrorException(`Failed to update organization with ID ${orgId}`);
      }

      // Emit event only if the accountType has changed
      if (existingOrg.accountType?.toString() !== updatedOrg.accountType?.toString()) {
        this.emitEvent('org.account_type.updated', { existingOrg, updatedOrg, user });
      }

      // Emit event only if the industryOrDomain has changed
      if (existingOrg.industryOrDomain?.toString() !== updatedOrg.industryOrDomain?.toString()) {
        this.emitEvent('org.industry.updated', { existingOrg, updatedOrg, user });
      }

      if (existingOrg.title !== updatedOrg.title) {
        this.emitEvent('org.title.updated', { existingOrg, updatedOrg, user });
      }

      if (updateOrgDto.legalName && existingOrg.legalName !== updatedOrg.legalName) {
        this.emitEvent('org.legalName.updated', { existingOrg, updatedOrg, user });
      }

      if ((existingOrg.country as CountryDocument)?._id?.toString() !== (updatedOrg.country as CountryDocument)?._id?.toString()) {
        this.emitEvent('org.country.updated', { existingOrg, updatedOrg, user });
      }

      if ((existingOrg.state as StateDocument)?._id?.toString() !== (updatedOrg.state as StateDocument)?._id?.toString()) {
        this.emitEvent('org.state.updated', { existingOrg, updatedOrg, user });
      }

      console.log(updatedOrg);

      if (existingOrg.orgType == OrgType.ACCOUNT_ORG) {
        const createdBy = await this.basicUserModel.findById(updatedOrgDetails?.createdBy).exec();
        if (updatedOrgDetails?.assignTo && Array.isArray(updatedOrgDetails?.assignTo)) {
          const existingAssignToIds = (existingOrg?.assignTo as unknown as { _id: any }[]).map(user => user._id.toString());

          // const existingAssignToIds = existingOrg?.assignTo?.map(user => user._id.toString());
          // console.log('Existing AssignTo:', existingAssignToIds);
          const newAssignToIds = updatedOrgDetails?.assignTo.filter(
            id => !existingAssignToIds.some(existingId => existingId.toString() === id.toString())
          );
          console.log(newAssignToIds);

          if (newAssignToIds.length > 0) {
            const assignToUsers = await this.basicUserModel.find({ _id: { $in: newAssignToIds } }).exec();
            // const assignToUsers = await this.basicUserModel.find({ _id: { $in: updatedOrgDetails?.assignTo } }).exec();
            if (!assignToUsers || assignToUsers.length === 0) {
              this.logger.log(`No users found for the provided assignTo IDs.`);
            } else {
              const assignToEmails = assignToUsers.map(user => {
                this.emitEvent('accountOrg.assignTo', {
                  title: updatedOrgDetails?.title,
                  email: user.email,
                  assignTo: user?.firstName,
                  createdBy: createdBy?.firstName
                });
                return user.email;
              });
              this.logger.log('Assign To Emails:', assignToEmails);
            }
          }
        }
      }
      if (existingOrg.orgType == OrgType.CUSTOMER_ORG) {
        const createdBy = await this.basicUserModel.findById(updatedOrgDetails?.createdBy).exec();
        if (updatedOrgDetails?.assignTo && Array.isArray(updatedOrgDetails?.assignTo)) {
          // console.log('AssignTo:', updatedOrgDetails?.assignTo);
          const existingAssignToIds = (existingOrg?.assignTo as unknown as { _id: any }[]).map(user => user._id.toString());

          // const existingAssignToIds = existingOrg?.assignTo?.map(user => user._id.toString());
          // console.log('Existing AssignTo:', existingAssignToIds);
          const newAssignToIds = updatedOrgDetails?.assignTo.filter(
            id => !existingAssignToIds.some(existingId => existingId.toString() === id.toString())
          );
          // console.log(newAssignToIds);
          if (newAssignToIds.length > 0) {
            const assignToUsers = await this.basicUserModel.find({ _id: { $in: newAssignToIds } }).exec();
            // const assignToUsers = await this.basicUserModel.find({ _id: { $in: updatedOrgDetails?.assignTo } }).exec();
            if (!assignToUsers || assignToUsers.length === 0) {
              this.logger.log(`No users found for the provided assignTo IDs.`);
            } else {
              const assignToEmails = assignToUsers.map(user => {
                this.emitEvent('clientOrg.assignTo', {
                  title: updatedOrgDetails?.title,
                  email: user.email,
                  assignTo: user?.firstName,
                  createdBy: createdBy?.firstName
                });
                return user.email;
              });
              this.logger.log('Assign To Emails:', assignToEmails);
            }
          }
        }
      }

      const contactDetails = existingOrg?.contactDetails ?? [];
      const updatedContactDetails = updatedOrg?.contactDetails ?? [];

      const contactAddress = existingOrg?.contactAddress ?? [];
      const updatedContactAddress = updatedOrg?.contactAddress ?? [];

      if (contactDetails.length > 0) {
        if (contactDetails[0]?.contactEmail !== updatedContactDetails[0]?.contactEmail)
          this.emitEvent('org.contactEmail.updated', { existingOrg, updatedOrg, user });
        if (contactDetails[0]?.contactNumber !== updatedContactDetails[0]?.contactNumber)
          this.emitEvent('org.contactNumber.updated', { existingOrg, updatedOrg, user });
      }

      if (contactAddress.length > 0) {
        if (contactAddress[0]?.apartment !== updatedContactAddress[0]?.apartment)
          this.emitEvent('org.contactAddress.updated', { existingOrg, updatedOrg, user });
      }


      return updatedOrg;
    } catch (error) {
      // Log the full error stack for debugging
      this.logger.error(`An error occurred while updating organization with ID ${orgId}:`, error.stack);

      // Rethrow the error with a user-friendly message
      throw new BadRequestException(`An error occurred while updating organization ${error.message}`);
    }
  }

  async suspendOrg(orgId: Types.ObjectId, commentDto: CommentDto) {
    try {
      const org = await this.findOne(orgId);
      const updatedOrg = await this.orgModel.findByIdAndUpdate(orgId, { isSuspended: true }, { new: true });
      // org.isSuspended = true;
      // await org.save();

      const createdComment = new this.commentModel(commentDto);
      createdComment.org = orgId;
      await createdComment.save();

      return updatedOrg;
    }
    catch (error) {
      // this.logger.error(error);
      // this.logger.error(`An error occurred while suspending org by ID ${orgId}: ${error?.message}`);
      throw new InternalServerErrorException(`An error occurred in fetching org by Id ${orgId}: ${error?.message}`);
    }
  }

  async approveOrg(orgId: Types.ObjectId) {
    try {
      const org = await this.findOne(orgId);
      org.isApproved = true;
      await org.save();
      return org;
    }
    catch (error) {
      // this.logger.error(error);
      // this.logger.error(`An error occurred while suspending org by ID ${orgId}. ${error?.message}`);
      throw new InternalServerErrorException(`An error occurred in fetching org by Id ${orgId}: ${error?.message}`);
    }
  }

  async rejectOrg(orgId: Types.ObjectId) {
    try {
      const org = await this.findOne(orgId);
      org.isRejected = true;
      await org.save();
      return org;
    }
    catch (error) {
      // this.logger.error(error);
      // this.logger.error(`An error occurred while suspending org by ID ${orgId}. ${error?.message}`);
      throw new InternalServerErrorException(`An error occurred in fetching org by Id ${orgId}: ${error?.message}`);
    }
  }


  async hardDelete(orgId: Types.ObjectId) {
    try {
      await this.findOne(orgId);
      return await this.orgModel.findByIdAndDelete(orgId);
    } catch (error) {
      // this.logger.error(error);
      // this.logger.error(`An error occurred while hard deleting org by ID ${orgId}. ${error?.message}`);
      throw new InternalServerErrorException(`An error occurred in fetching org by Id ${orgId}: ${error?.message}`);
    }
  }

  async softDelete(orgId: Types.ObjectId, commentDto: CommentDto) {
    try {
      const org = await this.findOne(orgId);
      org.isDeleted = true;
      await org.save();

      const createdComment = new this.commentModel(commentDto);
      createdComment.org = orgId;
      await createdComment.save();

      return org;
    }
    catch (error) {
      // this.logger.error(error);
      // this.logger.error(`An error occurred while soft deleting org by ID ${orgId}. ${error?.message}`);
      throw new InternalServerErrorException(`An error occurred in fetching org by Id ${orgId}: ${error?.message}`);
    }
  }

  async hardDeleteAllOrgs() {
    try {
      const orgsToDelete = await this.orgModel.find();
      if (!orgsToDelete.length) {
        this.logger.log('No orgs found to delete.');
        throw new NotFoundException('No orgs found to delete.');
      }
      return await this.orgModel.deleteMany();
    } catch (error) {
      this.logger.error('Error while hard deleting all orgs', error.stack);
      throw new InternalServerErrorException('Error while hard deleting all orgs');
    }
  }

  async restoreSoftDeletedOrg(orgId: Types.ObjectId) {
    try {
      const org = await this.orgModel.findById(orgId);
      if (!org) {
        throw new NotFoundException(`The org with ID: "${orgId}" doesn't exist.`);
      }
      if (!org.isDeleted) {
        throw new BadRequestException(`The org with ID: "${orgId}" is not soft deleted.`);
      }
      org.isDeleted = false
      await org.save();
      return org
    }
    catch (error) {
      // this.logger.error(`An error occurred while restoring Org by ID ${orgId}. ${error?.message}`);
      throw new InternalServerErrorException(`An error occurred in restoring org by Id ${orgId}: ${error?.message}`);
    }
  }

  async restore() {
    const populateOptions = this.getPopulateOptions();
    try {
      const softDeletedOrgs = await this.orgModel.find({ isDeleted: true });
      if (!softDeletedOrgs.length) {
        this.logger.log('No soft-deleted orgs found to restore.');
        return [];
      }
      for (const org of softDeletedOrgs) {
        org.isDeleted = false;
        await org.save();
      }
      // this.logger.log('All soft-deleted orgs have been restored.');
      return this.orgModel.find()
        .populate(populateOptions)
        .exec();
    } catch (error) {
      this.logger.error(`An error occurred while restoring soft-deleted orgs. ${error.message}`);
      throw new InternalServerErrorException(`An error occurred while restoring soft-deleted orgs. ${error.message}`);
    }
  }

  async bulkDelete(orgIds: Types.ObjectId[]): Promise<UpdateResult> {
    try {
      const result = await this.orgModel.updateMany(
        { _id: { $in: orgIds } },
        { $set: { isDeleted: true } }
      ).exec();

      if (result.modifiedCount > 0) {
        this.logger.log(`Successfully soft-deleted ${result.modifiedCount} org(s).`);
      } else {
        this.logger.error('No orgs were deleted.');
      }

      return result;
    } catch (error) {
      this.logger.error(`Error while bulk deleting orgs: ${error.message}`, error.stack);
      throw new InternalServerErrorException('Error while bulk deleting orgs.');
    }
  }

  async bulkChangeStatus(orgIds: Types.ObjectId[], changeStatusDto: ChangeStatusOrgDto, user: BasicUser): Promise<any[]> {
    const populateOptions = this.getPopulateOptions();
    try {
      const { status, comment } = changeStatusDto
      const updateData: any = { status };
      if (status === 'client') {
        updateData.orgType = OrgType.CUSTOMER_ORG;
      }
      const result = await this.orgModel.updateMany(
        { _id: { $in: orgIds } },
        { $set: { ...updateData } }
      ).exec();

      if (result.modifiedCount === 0) {
        throw new BadRequestException('No organizations were updated.');
      }
      const updatedOrgs = await this.orgModel.find({ _id: { $in: orgIds } })
        .select('title status _id createdBy')
        .populate(populateOptions)
        .exec();

      this.emitEvent('org.status.bulkChanged', { updatedOrgs, comment, user });

      return updatedOrgs

    } catch (error) {
      this.logger.error(`Error while bulk updating org status: ${error.message}`, error.stack);
      throw new InternalServerErrorException('Error while bulk updating org status.');
    }
  }


  async addComment(orgId: Types.ObjectId, commentDto: CommentDto): Promise<CommentDocument> {
    try {
      const createdComment = new this.commentModel(commentDto);
      createdComment.org = orgId;
      return await createdComment.save();
    }
    catch (error) {
      this.logger.error(`Failed to create comment. ${error}`);
      throw new InternalServerErrorException(`Error while creating a comment. ${error?.message}`);
    }
  }

  async getComments(orgId: Types.ObjectId): Promise<Comment[]> {
    try {
      return this.commentModel.find({ org: orgId })
        .populate({ path: 'user', select: '_id roles firstName' })
        .populate({ path: 'attachments', select: '_id originalName fileSize fileType locationUrl', model: 'FileMetadata' }).exec();
    } catch (error) {
      throw new InternalServerErrorException('Failed to retrieve comments')
    }
  }

  getPopulateOptions(): any[] {
    const populateOptions = [
      { path: 'createdBy', select: '_id roles firstName', model: 'BasicUser' },
      { path: 'assignTo', select: '_id roles firstName', model: 'BasicUser' },
    ];

    // Add industryOrDomain to populate options if it exists
    if (this.orgModel.schema.paths['industryOrDomain']) {
      populateOptions.push({ path: 'industryOrDomain', select: '_id name description', model: 'Industry' });
    }

    if (this.orgModel.schema.paths['businessUnit']) {
      populateOptions.push({ path: 'businessUnit', select: '_id label parentBusinessUnit  org  type key createdBy children breadcrumb', model: 'BusinessUnit' });
    }

    if (this.orgModel.schema.paths['accountType']) {
      populateOptions.push({ path: 'accountType', select: '_id name description', model: 'AccountType' });
    }

    // if (this.orgModel.schema.paths['businessUnit']) {
    //   populateOptions.push({ path: 'businessUnit', select: '_id label org level type', model: 'BusinessUnit' });
    // }

    if (this.orgModel.schema.paths['reportingTo']) {
      populateOptions.push({ path: 'reportingTo', select: '_id roles firstName', model: 'BasicUser' });
    }

    if (this.orgModel.schema.paths['parentOrg']) {
      populateOptions.push({ path: 'parentOrg', select: '_id title orgType description', model: 'Org' });
    }

    if (this.orgModel.schema.paths['country']) {
      populateOptions.push({ path: 'country', select: '_id countryId countryName countryPhoneCode currencyCode isDeleted createdAt updatedAt __v', model: 'Country' });
    }

    if (this.orgModel.schema.paths['state']) {
      populateOptions.push({ path: 'state', select: '_id stateId stateName country isDeleted createdAt updatedAt __v', model: 'State' });
    }

    if (this.orgModel.schema.paths['city']) {
      populateOptions.push({ path: 'city', select: '_id name', model: 'City' });
    }

    if (this.orgModel.schema.paths['customStatus']) {
      populateOptions.push({ path: 'customStatus', select: '_id name', model: 'Status' });
    }

    // if (this.orgModel.schema.paths['logo']) {
    //   populateOptions.push({ path: 'logo', select: '_id locationUrl originalName fileSize fileType', model: 'FileMetadata' });
    // }

    if (this.orgModel.schema.paths['logo'] && this.orgModel.schema.paths['logo'] !== null) {
      populateOptions.push({ path: 'logo', select: '_id locationUrl originalName fileSize fileType', model: 'FileMetadata' });
    }

    // if (this.orgModel.schema.paths['thumbnail']) {
    //   populateOptions.push({ path: 'thumbnail', select: '_id locationUrl originalName fileSize fileType', model: 'FileMetadata' });
    // }

    if (this.orgModel.schema.paths['vendors']) {
      populateOptions.push({ path: 'vendors', select: '_id title orgType', model: 'Org' });
    }

    if (this.orgModel.schema.paths['companyId']) {
      populateOptions.push({
        path: 'companyId',
        select: '_id title orgType createdBy',
        model: 'Org',
        populate: {
          path: 'createdBy',
          select: '_id firstName lastName email roles',
          model: 'BasicUser'
        }
      } as any);

      if (this.orgModel.schema.paths['clientAddress']) {
        populateOptions.push({
          path: 'clientAddress',
          populate: [
            {
              path: 'country',
              select: '_id countryId countryName countryPhoneCode currencyCode',
              model: 'Country',
            },
            {
              path: 'state',
              select: '_id stateId stateName',
              model: 'State',
            },
            {
              path: 'city',
              select: '_id name',
              model: 'City',
            }
          ]
        } as any);
      }
    }

    // Add customers to populate options if it exists
    if (this.orgModel.schema.paths['customers']) {
      populateOptions.push({ path: 'customers', select: '_id title orgType', model: 'Org' });
    }

    if (this.orgModel.schema.paths['thumbnail'] && this.orgModel.schema.paths['logo'] !== null) {
      populateOptions.push({ path: 'thumbnail', select: '_id locationUrl originalName fileSize fileType', model: 'FileMetadata' });
    }

    if (this.orgModel.schema.paths['addressDetails']) {
      populateOptions.push({
        path: 'addressDetails',
        populate: [
          {
            path: 'country',
            select: '_id countryId countryName countryPhoneCode currencyCode',
            model: 'Country',
          },
          {
            path: 'state',
            select: '_id stateId stateName',
            model: 'State',
          },
          {
            path: 'city',
            select: '_id name',
            model: 'City',
          }
        ]
      } as any);
    }
    return populateOptions;
  }

  async getDefaultStatus(orgType: string): Promise<Types.ObjectId> {
    const statuses = await this.statusService.findAllStatusByType(orgType); // Assuming you have an order field to sort
    if (!statuses || statuses.length === 0) {
      throw new BadRequestException('No status available to set as default');
    }
    // Look for the status with type NEW(CREATE)
    let status = statuses.find(status => status.functionality === StatusFunctionality.CREATE);

    if (!status) {
      status = statuses.find(status => status.functionality === StatusFunctionality.ONHOLD)
    }

    // Return the NEW or ONHOLD status if it exists, otherwise the first status
    return status ? status._id : statuses[0]._id;
  }

  async getDefaultVendorStatus(orgType: string): Promise<Types.ObjectId> {
    const statuses = await this.statusService.findAllStatusByType(orgType); // Assuming you have an order field to sort
    if (!statuses || statuses.length === 0) {
      throw new BadRequestException('No status available to set as default');
    }
    // Look for the status with type Register
    const onRegisterStatus = statuses.find(status => status.functionality === StatusFunctionality.REGISTER);

    // Return the ONHOLD status if it exists, otherwise the first status
    return onRegisterStatus ? onRegisterStatus._id : statuses[0]._id;
  }

  async addPlaceholder(placeholderDto: PlaceholderDto) {
    try {

      // Create a new placeholder document
      const newPlaceholder = new this.placeholderModel(placeholderDto);
      const savedPlaceholder = await newPlaceholder.save();

      const placeholder = this.getPlaceholder(savedPlaceholder._id)

      return placeholder;

    } catch (error: any) {
      throw new InternalServerErrorException(`An error occurred while adding the placeholder. ${error}`);
    }
  }

  async updatePlaceholder(placeholderId: string, placeholderDto: UpdatePlaceholderDto) {
    try {
      // Update the placeholder by ID
      const updatedPlaceholder = await this.placeholderModel.findByIdAndUpdate(placeholderId, placeholderDto, { new: true })
        .populate({ path: 'emailTemplate', select: '_id templateName' }).exec();

      if (!updatedPlaceholder) {
        throw new NotFoundException('Placeholder not found');
      }

      return updatedPlaceholder

    } catch (error) {
      throw new InternalServerErrorException(`An error occurred while updating the placeholder. ${error}`);
    }
  }

  // Delete a placeholder by ID in an email template
  async removePlaceholder(placeholderId: string) {
    try {
      // Delete the placeholder by ID
      const deletedPlaceholder = await this.placeholderModel.findByIdAndDelete(placeholderId);

      if (!deletedPlaceholder) {
        throw new NotFoundException('Placeholder not found');
      }

      return deletedPlaceholder;

    } catch (error) {
      throw new InternalServerErrorException(`An error occurred while deleting the placeholder. ${error}`);
    }
  }

  async getAllPlaceholders(orgId?: string, emailTemplate?: string, isDefault?: boolean) {
    try {
      const query: any = {};

      if (orgId) {
        query.org = orgId;
      }

      if (emailTemplate) {
        query.emailTemplate = emailTemplate;
      }

      if (isDefault !== undefined) {
        query.isDefault = isDefault;
      }

      const placeholders = await this.placeholderModel.find(query)
        .populate({ path: 'emailTemplate', select: '_id templateName' })
        .exec();

      return placeholders;
    } catch (error) {
      throw new InternalServerErrorException(`An error occurred while retrieving placeholders. ${error}`);
    }
  }

  async getPlaceholder(placeholderId: Types.ObjectId) {
    try {
      const placeholder = await this.placeholderModel.findById(placeholderId)
        .populate({ path: 'emailTemplate', select: '_id templateName' })
        .exec();
      if (!placeholder) {
        throw new NotFoundException('Placeholder not found');
      }
      return placeholder;
    } catch (error) {
      throw new InternalServerErrorException(`An error occurred while retrieving the placeholder: ${error.message}`);
    }
  }

  async toggleFavourite(userId: Types.ObjectId, orgIds: Types.ObjectId[]): Promise<OrgDocument[]> {
    const updatedOrgs: OrgDocument[] = [];

    for (const orgId of orgIds) {
      const org = await this.orgModel.findById(orgId);

      if (!org) {
        throw new NotFoundException(`Org with ID ${orgId} not found.`);
      }

      if (!Array.isArray(org.favouritedByUsers)) {
        org.favouritedByUsers = [];
      }

      if (!org.favouritedByUsers.includes(userId)) {
        org.favouritedByUsers.push(userId);
        await org.save();
        updatedOrgs.push(org);
      }
    }

    return this.orgModel.find({ _id: { $in: orgIds } }).populate('favouritedByUsers', 'firstName email roles').exec();

  }


  async getFavouriteOrgs(userId: Types.ObjectId): Promise<OrgDocument[]> {
    // Validate pagination parameters
    // if (page <= 0 || limit <= 0) {
    //   throw new BadRequestException('Page and limit must be positive integers.');
    // }

    // const skip = (page - 1) * limit;

    return this.orgModel
      .find({ favouritedByUsers: userId, isDeleted: false })
      // .skip(skip)
      // .limit(limit)
      .populate('favouritedByUsers', 'firstName email roles') // Populate the 'favourites' field
      .exec();
  }

  async removeFromFavourites(userId: Types.ObjectId, orgIds: Types.ObjectId[]): Promise<OrgDocument[]> {
    const updatedorgs: OrgDocument[] = [];

    for (const orgId of orgIds) {
      const org = await this.orgModel.findById(orgId);

      if (!org) {
        throw new NotFoundException(`org with ID ${orgId} not found.`);
      }

      if (Array.isArray(org.favouritedByUsers)) {
        // Convert userId to ObjectId if it's not already one
        const userObjectId = new Types.ObjectId(userId);

        // Remove the user from the favourites array
        org.favouritedByUsers = org.favouritedByUsers.filter(favouritedByUsers => favouritedByUsers.toString() !== userId.toString());

        await org.save();
        updatedorgs.push(org);
      }
    }

    // Populate user details in `favourites`
    return this.orgModel.find({ _id: { $in: orgIds } }).populate('favouritedByUsers', 'firstName email roles').exec();
  }

  async updateStatus(orgId: Types.ObjectId, isApprove?: boolean, isReject?: boolean): Promise<Org> {

    const updateData: any = {};
    if (isApprove !== undefined) updateData.isApproved = isApprove;
    if (isReject !== undefined) updateData.isRejected = isReject;

    this.logger.log(JSON.stringify(updateData))

    const updatedOrg = await this.orgModel
      .findByIdAndUpdate(orgId, updateData, { new: true })
      .populate(this.getPopulateOptions())
      .exec();

    // Handle the case where the organization is not found
    if (!updatedOrg) {
      this.logger.error(`Failed to update organization with ID ${orgId}`);
      throw new BadRequestException(`Failed to update organization with ID ${orgId}`);
    }


    if (isApprove) {

      // Retrieve "Active" status
      let activeStatus: any;
      if (updatedOrg?.orgType === OrgType.VENDOR_ORG) {
        activeStatus = await this.statusModel.findOne({ functionality: StatusFunctionality.ACTIVATE, statusType: "vendor-org" }).select('_id');
        if (!activeStatus) {
          this.logger.error(`Active status not found`);
          throw new InternalServerErrorException(`Active status not found`);
        }
      }
      if (updatedOrg?.orgType === OrgType.ADMIN_CUSTOMER_ORG) {
        activeStatus = await this.statusModel.findOne({ functionality: StatusFunctionality.ACTIVATE, statusType: "admin-customer-org" }).select('_id');
        if (!activeStatus) {
          this.logger.error(`Active status not found`);
          throw new InternalServerErrorException(`Active status not found`);
        }
      }


      // Update the customStatus of the Org
      const updatedOrgWithStatus = await this.orgModel.findByIdAndUpdate(
        orgId,
        { customStatus: activeStatus._id },
        { new: true }
      )
        .populate<{ status: StatusDocument }>({
          path: 'customStatus',
          select: '_id name'
        });

      if (updatedOrg?.orgType === OrgType.ADMIN_CUSTOMER_ORG) {
        const user = await this.basicUserModel.findOne({ email: updatedOrg?.contactDetails?.[0]?.contactEmail ?? '' });
        if (user) {
          user.isSuspended = false;
          await user.save();
        }
      }

      console.log("update status", updatedOrgWithStatus)

      // Emit event for custom status change
      if (updatedOrgWithStatus?.orgType === OrgType.VENDOR_ORG) {
        this.emitEvent('org.custom.status.changed', {
          existingOrg: updatedOrg,
          updatedOrg: updatedOrgWithStatus,
          user: null, // Adjust if you need to pass a user object
        });
      }

      const generateStrongPassword = () => {
        const uppercase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        const lowercase = "abcdefghijklmnopqrstuvwxyz";
        const numbers = "0123456789";
        const specialChars = "!@#$%^&*()_-+=<>?";
        const allChars = uppercase + lowercase + numbers + specialChars;

        let password = '';
        password += uppercase[Math.floor(Math.random() * uppercase.length)];
        password += numbers[Math.floor(Math.random() * numbers.length)];
        password += specialChars[Math.floor(Math.random() * specialChars.length)];

        for (let i = 3; i < 8; i++) {
          password += allChars[Math.floor(Math.random() * allChars.length)];
        }

        return password.split('').sort(() => 0.5 - Math.random()).join(''); // Shuffle the password
      };
      // Generate and hash the password
      const password = generateStrongPassword();

      this.emitEvent('org.createOrgAdmin', {
        email: updatedOrg?.contactDetails?.[0]?.contactEmail ?? '',
        firstName: 'Customer',
        lastName: 'Customer',
        password: password,
        org: updatedOrg._id,
        roles: [Role.Admin, Role.User]
      });

      if (updatedOrg?.orgType === OrgType.ADMIN_CUSTOMER_ORG) {
        this.emitEvent('org.CloneDefaultPermissionsAdmin', {
          org: updatedOrg._id,
          roles: Role.Admin
        });
        this.emitEvent('roles.clone', {
          orgId: updatedOrg._id.toString()
        });
        this.emitEvent('org.CloneDefaultPermissionsRoleCreated', {
          org: updatedOrg._id.toString()
        });
        this.emitEvent('org.CloneDefaultDynamicFieldsForOrg', {
          org: updatedOrg._id.toString(),
          orgType: updatedOrg.orgType
        });
        this.emitEvent('org.CloneDefaultJobsDynamicFieldsForOrg', {
          org: updatedOrg._id.toString(),
          orgType: updatedOrg.orgType
        });
        this.emitEvent('org.cloneDefaultCustomFieldsForOrg', {
          org: updatedOrg._id.toString()
        });
        this.emitEvent('org.CloneDefaultPermissionsForOrgDefaultRoles', {
          orgId: updatedOrg._id.toString(),
          payload: ORG_ROLE_PERMISSIONS_PAYLOAD
        });
        this.emitEvent('org.CloneDefaultEmailTemplates', {
          org: updatedOrg._id.toString()
        });

       const update = await this.orgModel.updateOne(
          { _id: updatedOrg._id },
          {
            $set: {
              hikeSettings: {
                'full-time': {
                  minMarginPercentage: 40,
                  maxHikePercentage: 30,
                  requiresApproval: false
                },
                'contract': {
                  minMarginPercentage: 40,
                  maxHikePercentage: 30,
                  requiresApproval: false
                }
              }
            }
          }
        );
        
      }

    }

    // Handle rejection logic
    if (isReject) {

      let rejectedStatus: any;
      if (updatedOrg?.orgType === OrgType.VENDOR_ORG) {
        rejectedStatus = await this.statusModel.findOne({ functionality: StatusFunctionality.REJECT, statusType: "vendor-org" }).select('_id');
        if (!rejectedStatus) {
          this.logger.error(`Reject status not found`);
          throw new InternalServerErrorException(`Reject status not found`);
        }
      }
      if (updatedOrg?.orgType === OrgType.ADMIN_CUSTOMER_ORG) {
        rejectedStatus = await this.statusModel.findOne({ functionality: StatusFunctionality.REJECT, statusType: "admin-customer-org" }).select('_id');
        if (!rejectedStatus) {
          this.logger.error(`Reject status not found`);
          throw new InternalServerErrorException(`Reject status not found`);
        }
      }
      if (!rejectedStatus) {
        this.logger.error(`Rejected status not found`);
        throw new InternalServerErrorException(`Rejected status not found`);
      }

      const updatedOrgWithStatus = await this.orgModel.findByIdAndUpdate(
        orgId,
        { customStatus: rejectedStatus._id },
        { new: true }
      )
        .populate<{ status: StatusDocument }>({
          path: 'customStatus',
          select: '_id name'
        });

      if (updatedOrg?.orgType === OrgType.ADMIN_CUSTOMER_ORG) {
        const user = await this.basicUserModel.findOne({ email: updatedOrg?.contactDetails?.[0]?.contactEmail ?? '' });
        if (user) {
          user.isSuspended = true;
          await user.save();
        }
      }


      if (updatedOrg?.orgType === OrgType.VENDOR_ORG) {
        this.emitEvent('org.custom.status.changed', {
          existingOrg: updatedOrg,
          updatedOrg: updatedOrgWithStatus,
          user: null,
        });
      }

      if (updatedOrg?.orgType === OrgType.ADMIN_CUSTOMER_ORG) {
        this.emitEvent('org.rejectOrgAdmin', {
          existingOrg: updatedOrg,
          updatedOrg: updatedOrgWithStatus,
          user: null,
        });
      }

    }

    return updatedOrg;
  }


  async acceptAdminTerms(user: any, isAdminTermsAccepted?: boolean) {

    if (isAdminTermsAccepted) {
      const updatedUser = await this.basicUserModel.findByIdAndUpdate(
        user._id,
        {
          $set: { isAdminTermsAccepted: true },
          $addToSet: { roles: 'admin' }, // avoids duplication
        },
        { new: true }
      ).exec();

      // Handle the case where the user is not found
      if (!updatedUser) {
        this.logger.error(`Failed to update Terms and Conditions with ID ${user._id}`);
        throw new BadRequestException(`Failed to update Terms and Conditions with ID ${user._id}`);
      }

      if (updatedUser?.org) {
        const oldOrg = await this.orgModel.findById(updatedUser.org).lean();

        // if (!oldOrg) {
        //   throw new BadRequestException(`Original organization not found for user`);
        // }

        // Clone org data
        const newOrgData = {
          ...oldOrg,
          orgType: 'admin-customer-org',
        };

        delete newOrgData._id; // Prevent MongoDB conflict
        delete newOrgData.companyId; // Remove companyId for the new org

        const newOrg = new this.orgModel(newOrgData);
        const savedNewOrg = await newOrg.save();

        if (savedNewOrg) {
          await this.updateStatus(savedNewOrg._id, true, false); // Approve the new org
          const updatedUser = await this.basicUserModel.findByIdAndUpdate(
            user._id,
            {
              $set: { org: savedNewOrg._id }
            },
            { new: true }
          ).exec();
        }

        return savedNewOrg;
      }

    }
  }



  // async assignJobToVendor(vendorId: string, vendorJobAssignDto: VendorJobAssignDto) {
  //   try {
  //     const vendorObjId = validateObjectId(vendorId);
  //     return this.taskService.assignJobToVendor(vendorObjId, vendorJobAssignDto);
  //   } catch (error) {
  //     throw new InternalServerErrorException(`Error while assigning job to vendor ID ${vendorId}: ${error.message}`);
  //   }
  // }

  // async getVendorAssignedJobs(vendorId: string, jobId?: string) {
  //   try {
  //     const vendorObjId = validateObjectId(vendorId);
  //     return this.taskService.getVendorAssignedJobs(vendorObjId, jobId);
  //   } catch (error) {
  //     throw new InternalServerErrorException(`Error retrieving assigned jobs for vendor ID ${vendorId}: ${error.message}`);
  //   }
  // }

  // async unassignVendorJob(vendorId: string, jobId: string) {
  //   try {
  //     const vendorObjId = validateObjectId(vendorId);
  //     const jobObjId = validateObjectId(jobId);
  //     return this.taskService.unassignVendorJob(vendorObjId, jobObjId);
  //   } catch (error) {
  //     throw new InternalServerErrorException(`Error unassigning job from vendor ID ${vendorId}: ${error.message}`);
  //   }
  // }

  @OnEvent('region.blocked', { async: true })
  async handleRegionBlockedForOrg(payload: any) {
    const { region } = payload;

    try {
      if (region.state.stateName === 'ALL') {
        await this.orgModel.updateMany(
          { country: region.country._id },
          { isOperational: false }
        );
      }
      else {
        await this.orgModel.updateMany(
          { state: region.state._id },
          { isOperational: false }
        );
      }

      console.log(`Organizations for region ${region._id} are now non-operational.`);

    } catch (error) {
      console.error(`Error while updating organizations for region ${region._id}:`, error);
    }
  }

  @OnEvent('region.activated', { async: true })
  async handleRegionActivatedForOrg(payload: any) {
    const { region } = payload;

    try {
      if (region.state.stateName === 'ALL') {
        await this.orgModel.updateMany(
          { country: region.country._id },
          { isOperational: true }
        );
      }
      else {
        await this.orgModel.updateMany(
          { state: region.state._id },
          { isOperational: true }
        );
      }

      this.logger.log(`Organizations for region ${region._id} are now operational.`);

    } catch (error) {
      this.logger.error(`Error while updating organizations for region ${region._id}:`, error);
    }
  }


  @OnEvent('onboarding.document.status.updated')
  async handleOnboardingStatusUpdate(payload: { orgId: string, newStatus: StatusFunctionality }) {
    try {
      const { orgId, newStatus } = payload;

      const orgObjectId = new Types.ObjectId(orgId);

      // Default user object (can be an admin or system user)
      const systemUser = { id: 'system', role: 'admin' };



      // Update customStatus in Org
      await this.changeCustomStatus(orgObjectId, { customStatus: newStatus }, systemUser);

      this.logger.log(`Org ${orgId} customStatus updated to ${newStatus}`);
    } catch (error) {
      this.logger.error(`Failed to update org status: ${error.message}`);
    }
  }



  emitEvent(eventName: string, payload: any) {
    // payload['event']= eventName;
    this.logger.debug(payload)
    this.eventEmitter.emit(eventName, payload);
  }


  async getCompanyProfileDetails() {
    try {
      const companies: { OrgId: String; OrgName: String; OrgType: String; Jobs: Job[] }[] = [];
      const orgs = await this.orgModel.find({ isDeleted: false }, { title: true, orgType: true }).exec();

      let conditions: any = { isDeleted: false }; // Default condition to exclude deleted jobs 

      for (const org of orgs) {

        conditions.endClientOrg = org._id.toString;
        const jobs = await this.jobModel.find(conditions).sort({ updatedAt: -1 }).exec();
        companies.push({ OrgId: org._id.toString(), OrgName: org.title, OrgType: org.orgType, Jobs: jobs, });
      }

      return companies;
    } catch (error) {
      throw new InternalServerErrorException(`Error while fetching all orgs: ${error?.message}`);
    }
  }

  async updateCompanyProfile(orgId: Types.ObjectId, updateCompanyProfileDto: UpdateCompanyProfileDto) {
    // this.logger.log('Updating company profile with DTO:', updateCompanyProfileDto);

    const unsetData: any = {};

    const fieldsToCheck: (keyof UpdateCompanyProfileDto)[] = ['logo', 'linkedInUrl', 'companyProfileName', 'showSocialLogins', 'instagramUrl', 'facebookUrl'];

    // const existingLogo = await this.fileMetadataModel.findById(updateCompanyProfileDto.logo).exec();
    // if (updateCompanyProfileDto.logo && !existingLogo) {
    //   throw new NotFoundException(`Logo with ID ${updateCompanyProfileDto.logo} not found`)
    // }

    // Iterate through each field and handle empty strings
    fieldsToCheck.forEach((field) => {
      if (updateCompanyProfileDto[field] === '') {
        unsetData[field] = ''; // Mark for removal
        delete updateCompanyProfileDto[field];
      }
    });

    // Fetch the existing org for comparison (if needed)
    const existingOrg = await this.findOne(orgId);

    if (!existingOrg) {
      this.logger.error(`Organization with ID ${orgId} not found`);
      throw new NotFoundException(`Organization with ID ${orgId} not found`);
    }

    //throw error if same company profile name exists for other orgs
    if (updateCompanyProfileDto.companyProfileName) {
      const companies = await this.orgModel.find({ companyProfileName: updateCompanyProfileDto.companyProfileName, _id: { $ne: orgId } });
      if (companies.length) {
        this.logger.error('Company profile name with this name already exists');
        throw new HttpException('Company profile name with this name already exists', HttpStatus.CONFLICT);
      }
    }

    // Perform the update operation
    const updatedOrg = await this.orgModel
      .findByIdAndUpdate(orgId,
        {
          $set: updateCompanyProfileDto,
          $unset: unsetData,
        },
        { new: true }
      ).exec();

    if (!updatedOrg) {
      this.logger.error(`Failed to update organization with ID ${orgId}`);
      throw new InternalServerErrorException(`Failed to update organization with ID ${orgId}`);
    }
    return updatedOrg;
  }

  async findByCompanyProfile(companyProfileName: string) {
    const populateOptions = this.getPopulateOptions();

    try {
      return await this.orgModel.findOne({ isDeleted: false, companyProfileName: companyProfileName })
        .populate(populateOptions).exec();
    } catch (error) {
      this.logger.error(`An error occurred while fetching org details by company profile name:`, error);
      throw new InternalServerErrorException(`Error while fetching org details by company profile name: ${error?.message}`);
    }

  }

  async getCustomerOrgs(): Promise<OrgDocument[]> {
    try {
      return await this.orgModel.find(
        { orgType: OrgType.CUSTOMER_ORG },
        { _id: 1, title: 1, orgType: 1 }
      ).exec();
    } catch (error) {
      throw new InternalServerErrorException(`Failed to fetch customer organizations: ${error.message}`);
    }
  }

  async findAllOrgsByOrgType(page: number, limit: number, org_Type: string): Promise<OrgDocument[]> {
    const populateOptions = this.getPopulateOptions();
    try {
      let conditions: any = { isDeleted: false };
      // const orgType = OrgType.ADMIN_CUSTOMER_ORG;
      const orgType = org_Type;

      if (orgType) {
        conditions.orgType = orgType;
      }

      // this.logger.log(JSON.stringify(conditions));
      const orgs = await this.orgModel.find(conditions)
        .sort({ updatedAt: -1 })
        .populate(populateOptions)
        .sort({ updatedAt: -1, createdAt: -1 })
        .skip((page - 1) * limit)
        .limit(limit)
        .exec();
      return orgs;
    } catch (error) {
      throw new InternalServerErrorException(`An error occurred while fetching orgs: ${error?.message}.`);
    }
  }

  async findOrgCountryState(orgId: Types.ObjectId) {
    const populateOptions = this.getPopulateOptions();
    try {
      const org = await this.orgModel.findById(orgId)
        // .populate(populateOptions)
        .select('country state')
        .exec();

      if (!org || org.isDeleted) {
        throw new NotFoundException(`Org not found with ID ${orgId}`);
      }

      return org;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      } else {
        throw new InternalServerErrorException(`Error while fetching org with ID ${orgId}: ${error.message}`);
      }
    }
  }

  async getAllInternalContactsByOrg(orgId: Types.ObjectId, page: number, limit: number, searchTerm?: string, departmentId?: Types.ObjectId) {
    try {
      await this.findOne(orgId);
      return this.userService.getAllInternalContactsByOrg(orgId, page, limit, searchTerm, departmentId);
    } catch (error) {
      throw new InternalServerErrorException(`An error occurred while fetching org  by Id ${orgId}. ${error?.message}.`);
    }
  }

  async filterOrgsByTypeForAcc_Manager(orgType: string, page: number, limit: number, userId: Types.ObjectId, assignTo: string): Promise<OrgDocument[]> {
    const populateOptions = this.getPopulateOptions();
    try {
      let conditions: any = { isDeleted: false, createdBy: userId };
      if (orgType) {
        conditions.orgType = orgType;
      }
      if (assignTo) {
        conditions.assignTo = { $in: [assignTo] };
      }

      // this.logger.log(JSON.stringify(conditions));
      const orgs = await this.orgModel.find(conditions)
        .sort({ updatedAt: -1 })
        .populate(populateOptions)
        .sort({ updatedAt: -1, createdAt: -1 })
        .skip((page - 1) * limit)
        .limit(limit)
        .exec();
      return orgs;
    } catch (error) {
      throw new InternalServerErrorException(`An error occurred while fetching orgs: ${error?.message}.`);
    }
  }

  async onboardClient(orgId: string, dto: ClientOnboardingDto, user: any) {
    try {
      const populateOptions = this.getPopulateOptions();
      const existingOrg = await this.orgModel.findById(orgId);
      if (!existingOrg) {
        throw new NotFoundException('Organization not found.');
      }

      // Validation: At least one agreement and one client address required
      // if (!dto.agreements || dto.agreements.length === 0) {
      //   throw new BadRequestException('At least one agreement must be provided.');
      // }

      if (!dto.clientAddress || dto.clientAddress.length === 0) {
        throw new BadRequestException('At least one client address must be provided.');
      }

      this.logger.log(`Org ${orgId} onboarded as client by ${user?.email}`);
      const addressDetails = {
        ...dto.clientAddress[0],
        addressLine: dto.clientAddress[0]?.apartment, // ✅ Map apartment to addressLine
      };
      const updatedOrg = await this.orgModel.findByIdAndUpdate(orgId,
        {
          $set: {
            ...dto,
            orgType: OrgType.CUSTOMER_ORG,
            addressDetails: addressDetails,
          }
        }, { new: true })
        .populate(populateOptions)
        .exec();

      return updatedOrg
    } catch (error) {
      this.logger.error('Error during client onboarding', error);
      throw new InternalServerErrorException('Failed to onboard organization as client.');
    }
  }

  async updateInvoiceDetails(orgId: string, dto: UpdateInvoiceDetailsDto, user: any) {
    try {
      const existingOrg = await this.orgModel.findById(orgId);
      if (!existingOrg) {
        throw new NotFoundException('Organization not found.');
      }

      this.logger.log(`Invoice details updated for org ${orgId} by ${user?.email}`);

      const updatedOrg = await this.orgModel.findByIdAndUpdate(
        orgId,
        { $set: dto },
        { new: true }
      ).exec();

      return updatedOrg;
    } catch (error) {
      this.logger.error('Error updating invoice details', error);
      throw new InternalServerErrorException('Failed to update invoice details.');
    }
  }

  async updateBgvhandler(orgId: Types.ObjectId, bgvHandlerId: string, userId: string) {
    try {

      const updatedOrg = await this.orgModel.findByIdAndUpdate(
        orgId,
        {
          bgvHandler: bgvHandlerId.toString(),
          updatedAt: new Date(),
        },
        { new: true },
      )
        .populate([
          { path: 'bgvHandlerId', select: 'title email contactNumber' },
          { path: 'createdBy', select: 'firstName lastName email' },
        ])
        .exec();


    } catch (error) {
      throw new InternalServerErrorException(`An error occurred while updating the member. ${error?.message}`);
    }
  }

  // org.service.ts

  async updateAddressDetails(orgId: string, dto: UpdateAddressDetailsDto) {
    return this.orgModel.findByIdAndUpdate(
      orgId,
      {
        addressDetails: {
          addressLine: dto.addressLine,
          city: dto.city,
          state: dto.state,
          country: dto.country,
          postalCode: dto.postalCode,
          contactNumber: dto.contactNumber,
          email: dto.email,
          lutNumber: dto.lutNumber,
          lutDate: dto.lutDate,
        },
      },
      { new: true }
    );
  }

  async updateBankDetails(orgId: string, dto: UpdateBankDetailsDto) {
    return this.orgModel.findByIdAndUpdate(
      orgId,
      {
        bankDetails: {
          accountHolderName: dto.accountHolderName,
          ifscCode: dto.ifscCode,
          bankName: dto.bankName,
          accountNumber: dto.accountNumber,
          branchName: dto.branchName,
          accountType: dto.accountType,
          gstin: dto.gstin,
        },
      },
      { new: true }
    );
  }

  async updateHikeSettings(dto: UpdateHikeSettingsDto) {
    const hikeKey = `hikeSettings.${dto.employmentType}`;
  
    const hikeSettingsData = {
      minMarginPercentage: dto.minMarginPercentage,
      maxHikePercentage: dto.maxHikePercentage,
      requiresApproval: dto.requiresApproval,
      approverRole: dto.approverRole,
    };
  
    if (dto.departmentId) {
      await this.businessUnitModel.findByIdAndUpdate(
        dto.departmentId,
        { $set: { [hikeKey]: hikeSettingsData } },
        { new: true }
      );
      return { message: `Department hike settings updated for ${dto.employmentType}` };
    }
  
    if (dto.orgId) {
      await this.orgModel.findByIdAndUpdate(
        dto.orgId,
        { $set: { [hikeKey]: hikeSettingsData } },
        { new: true }
      );
      return { message: `Org hike settings updated for ${dto.employmentType}` };
    }
  
    throw new BadRequestException('Either orgId or departmentId must be provided');
  }

  async getHikeSettings(orgId?: string, departmentId?: string) {
    // const { orgId, departmentId } = query;
  
    if (departmentId) {
      const department = await this.businessUnitModel.findById(departmentId).lean();
      return department?.hikeSettings || {};
    }
  
    if (orgId) {
      const org = await this.orgModel.findById(orgId).lean();
      return org?.hikeSettings || {};
    }
  
    throw new BadRequestException('orgId or departmentId is required');
  }
  
  


  // async updateCreatedByOrgFromUserModel() {
  //   const orgs = await this.orgModel.find({
  //     orgType: { $in: [OrgType.CUSTOMER_ORG, OrgType.ACCOUNT_ORG] },
  //     // createdByOrg: null
  //   }).exec();


  //   for (const org of orgs) {
  //     const user = await this.basicUserModel.findById(org.createdBy);
  //     if (user?.org) {
  //       await this.orgModel.updateOne(
  //         { _id: org._id },
  //         { $set: { createdByOrg: user.org.toString() } }
  //       );
  //     }
  //   }

  //   console.log(`✅ Done updating createdByOrg based on user orgId.`);
  // }

}
