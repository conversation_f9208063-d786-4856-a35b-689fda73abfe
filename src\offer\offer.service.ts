import { BadRequestException, HttpException, HttpStatus, Injectable, InternalServerErrorException, Logger, NotFoundException } from '@nestjs/common';
import { CreateOfferDto } from './dto/create-offer.dto';
import { UpdateOfferDto } from './dto/update-offer.dto';
import { Model, Types } from 'mongoose';
import { InjectModel } from '@nestjs/mongoose';
import { Offer, OfferDocument } from './schemas/offer.schema';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { EmailTemplateEvent, EmploymentType, JobType, OnboardingStatus, OrgType, REQUIRED_BGV_DOCUMENTS } from 'src/shared/constants';
import { EmailTemplate } from 'src/email-template-builder/schemas/email-template-builder.schema';
import { Org, Placeholder } from 'src/org/schemas/org.schema';
import { JSONPath } from 'jsonpath-plus';
import { omit, unescape } from 'lodash';
import { JobApplication } from 'src/job-application-form/schemas/job-application.schema';
import { format } from 'date-fns';
import { toZonedTime } from 'date-fns-tz';
import { Job } from 'src/job/schemas/job.schema';
import { Role } from 'src/auth/enums/role.enum';
import { OffersQueryDTO } from './dto/query-offers.dto';
import moment from 'moment';
import { JobAllocationBase } from 'src/job-allocation/schemas/job-allocation-base.schema';
import { CreateBgvDto } from './dto/create-bgv.dto';
import { Bgv } from './schemas/bgv.schema';
import { BudgetConfirmationDto } from './dto/budget-confirmation.dto';
import { ReviewInterimDocDto } from './dto/review-interim-doc.dto';
import { Employee } from 'src/employee/schemas/employee.schema';
import * as bcrypt from 'bcrypt';
import { BasicUser } from 'src/user/schemas/basic-user.schema';
import { SendUploadLinksDto } from './dto/send-upload-links.dto';
import { BgvHandler, BgvHandlerDocument } from 'src/bgv-handler/schemas/bgv-handler.schema';
import { BusinessUnit } from 'src/business-unit/schemas/business-unit.schema';
import { ApproveRejectInterimBgvDto } from './dto/approve-reject-interim-bgv.dto';
import { Invoice, InvoiceDocument } from 'src/invoices/schemas/invoice.schema';
import { FileMetadata } from 'src/file-upload/schemas/file-metadata.schema';
import { ReviewDocDto } from './dto/review-doc.dto';
import { HikeApproval } from './schemas/hike-approval.schema';
import { ReviewHikeApprovalDto } from './dto/review-hike-approval.dto';

@Injectable()
export class OfferService {

  private readonly logger = new Logger(OfferService.name);
  constructor(
    @InjectModel(Job.name) private jobModel: Model<Job>,
    @InjectModel(JobApplication.name) private jobApplicationModel: Model<JobApplication>,
    @InjectModel(Offer.name) private offerModel: Model<Offer>, private eventEmitter: EventEmitter2,
    @InjectModel(EmailTemplate.name) private emailTemplateService: Model<EmailTemplate>,
    @InjectModel(Placeholder.name) private placeholderService: Model<Placeholder>,
    @InjectModel(JobApplication.name) private jobApplicationService: Model<JobApplication>,
    @InjectModel(Org.name) private orgsModel: Model<Org>,
    @InjectModel(JobAllocationBase.name) private jobAllocationBasesModel: Model<JobAllocationBase>,
    @InjectModel(Bgv.name) private bgvModel: Model<Bgv>,
    @InjectModel(Employee.name) private employeeModel: Model<Employee>,
    @InjectModel(BasicUser.name) private basicUserModel: Model<BasicUser>,
    @InjectModel(BgvHandler.name) private readonly bgvHandlerModel: Model<BgvHandlerDocument>,
    @InjectModel(BusinessUnit.name) private businessUnitModel: Model<BusinessUnit>,
    @InjectModel(Invoice.name) private invoiceModel: Model<InvoiceDocument>,
    @InjectModel(FileMetadata.name) private fileMetadataModel: Model<FileMetadata>,
    @InjectModel(HikeApproval.name) private hikeApprovalModel: Model<HikeApproval>,
  ) { }

  async create(user: any, createOfferDto: CreateOfferDto) {
    try {
      createOfferDto.createdBy = user._id.toString();
      const createdOffer = new this.offerModel(createOfferDto);
      const jobApp = await this.jobApplicationModel.findById(createdOffer.jobApplication).exec();
      const job = await this.jobModel.findById(jobApp?.jobId).exec();
      const jobApplication = await this.jobApplicationModel.findById(createdOffer.jobApplication).populate('jobId');
      const jobDetails = await this.jobModel.findById(jobApplication?.jobId)
        .populate([
          {
            path: 'endClientOrg',
            model: 'Org',
            select: '_id title',
          },
          {
            path: 'postingOrg',
            model: 'Org',
            select: '_id title',
          },
        ])
        .exec();
      console.log(job)
      if (job?.employmentType === EmploymentType.FullTime) {
        if (job?.requiredBgv) {
          createdOffer.status = OnboardingStatus.FINAL_SELECT;
        }
        else {
          createdOffer.status = OnboardingStatus.FINAL_SELECT;
        }
      } else {
        createdOffer.status = OnboardingStatus.FINAL_SELECT; // Set initial status to DOCUMENTATION_PENDING
      }

      let hikeSettings: any;
      const approvers: any = [];
      if (job?.jobType === JobType.Internal) {
        const departmentId = job.department;
        if (departmentId) {
          const department = await this.businessUnitModel.findById(departmentId).lean();
          approvers.push(department?.departmentHead);
          if (department?.hikeSettings) {
            if (job.employmentType === EmploymentType.FullTime) {
              hikeSettings = department?.hikeSettings[job.employmentType];
            }
            if (job.employmentType === EmploymentType.Contract) {
              hikeSettings = department?.hikeSettings[job.employmentType];
            }
          }
          if (!hikeSettings) {
            const orgId = job.postingOrg;
            const org = await this.orgsModel.findById(orgId).lean();
            if (org?.hikeSettings) {
              if (job.employmentType === EmploymentType.FullTime) {
                hikeSettings = org?.hikeSettings[job.employmentType];
              }
              if (job.employmentType === EmploymentType.Contract) {
                hikeSettings = org?.hikeSettings[job.employmentType];
              }
            }
          }
        }
        else {
          const orgId = job.postingOrg;
          const org = await this.orgsModel.findById(orgId).lean();
          if (org?.hikeSettings) {
            if (job.employmentType === EmploymentType.FullTime) {
              hikeSettings = org?.hikeSettings[job.employmentType];
            }
            if (job.employmentType === EmploymentType.Contract) {
              hikeSettings = org?.hikeSettings[job.employmentType];
            }
          }
        }
      }
      if (job?.jobType === JobType.External) {
        const orgId = job.endClientOrg;
        const org = await this.orgsModel.findById(orgId).lean();
        const assignTo = org?.assignTo ?? [];
        for (const userId of assignTo) {
          const idStr = userId.toString();
          if (!approvers.find((u: { toString: () => string; }) => u.toString() === idStr)) {
            approvers.push(userId);
          }
        }
        if (org?.hikeSettings) {
          if (job.employmentType === EmploymentType.FullTime) {
            hikeSettings = org?.hikeSettings[job.employmentType];
          }
          if (job.employmentType === EmploymentType.Contract) {
            hikeSettings = org?.hikeSettings[job.employmentType];
          }
        }
        if (!hikeSettings) {
          const orgId = job.postingOrg;
          const org = await this.orgsModel.findById(orgId).lean();
          if (org?.hikeSettings) {
            if (job.employmentType === EmploymentType.FullTime) {
              hikeSettings = org?.hikeSettings[job.employmentType];
            }
            if (job.employmentType === EmploymentType.Contract) {
              hikeSettings = org?.hikeSettings[job.employmentType];
            }
          }
        }
      }
      createdOffer.isApproved = true;
      if (job?.maxCtcOffered && createdOffer?.salaryPerAnnum && hikeSettings) {
        const offeredCtc = createdOffer?.salaryPerAnnum;
        const currentCtc = jobApp?.currentCTC;

        const minMargin = hikeSettings.minMarginPercentage ?? 0;
        const maxHike = hikeSettings.maxHikePercentage ?? 0;

        const maxAllowedByMargin = Math.round(job.maxCtcOffered * (1 - minMargin / 100));
        const maxAllowedByHike = currentCtc ? Math.round(currentCtc * (1 + maxHike / 100)) : null;

        const violations: string[] = [];

        if (offeredCtc > maxAllowedByMargin) {
          violations.push(
            `Offered CTC (${offeredCtc}) exceeds max allowed by margin (${maxAllowedByMargin})`
          );
        }

        if (currentCtc && offeredCtc > maxAllowedByHike!) {
          violations.push(
            `Offered CTC (${offeredCtc}) exceeds max allowed hike over current CTC (${maxAllowedByHike})`
          );
        }

        if (violations.length > 0 && hikeSettings.requiresApproval) {
          // ✅ Create approval record
          createdOffer.status = OnboardingStatus.OFFER_APPROVAL_PENDING;
          createdOffer.isApproved = false;

          await this.hikeApprovalModel.create({
            applicationId: jobApp?._id.toString(),
            jobId: job._id.toString(),
            offerId: createdOffer._id.toString(),
            orgId: job.postingOrg,
            employmentType: job.employmentType,
            hikeSettings: hikeSettings,
            reason: violations.join('; '),
            maxAllowedByHike: maxAllowedByHike,
            maxAllowedByMargin: maxAllowedByMargin,
            approvers: approvers,
            approverRole: hikeSettings.approverRole,
            status: 'PENDING',
            createdAt: new Date(),
            createdBy: user?._id,
          });
        }
      }


      await createdOffer.save();

      const offer = await this.offerModel.findById(createdOffer._id)
        .populate({
          path: 'jobApplication',
          populate: [
            {
              path: 'jobId',
              select: '_id title'
            },
            {
              path: 'org',
              select: '_id title'
            }
          ],
          select: '_id jobId firstName lastName contactDetails contactAddress org',
          model: 'JobApplication'
        })
        .exec();

      if (!offer) {
        throw new Error('Failed to fetch offer details after creation.');
      }

      const existingJobSeeker = await this.basicUserModel.findOne({ email: jobApp?.contactDetails?.contactEmail }).lean().exec();

      if (!existingJobSeeker) {
        const generateStrongPassword = () => {
          const uppercase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
          const lowercase = "abcdefghijklmnopqrstuvwxyz";
          const numbers = "0123456789";
          const specialChars = "!@#$%^&*()_-+=<>?";
          const allChars = uppercase + lowercase + numbers + specialChars;

          let password = '';
          password += uppercase[Math.floor(Math.random() * uppercase.length)];
          password += numbers[Math.floor(Math.random() * numbers.length)];
          password += specialChars[Math.floor(Math.random() * specialChars.length)];

          for (let i = 3; i < 8; i++) {
            password += allChars[Math.floor(Math.random() * allChars.length)];
          }

          return password.split('').sort(() => 0.5 - Math.random()).join(''); // Shuffle the password
        };
        // Generate and hash the password
        const password = generateStrongPassword();
        const hashedPassword = await bcrypt.hash(password, 10);


        const createMember = new this.basicUserModel({
          email: jobApp?.contactDetails?.contactEmail,
          firstName: jobApp?.firstName,
          lastName: jobApp?.lastName,
          contactNumber: jobApp?.contactDetails?.contactNumber,
          roles: [Role.JobSeeker], // Assign the job-seeker role
          // ...createMemberDto,
          // org: createdEmployee.payRollOrg,
          password: hashedPassword,
          isVerified: true,
        });

        const createdMember = await createMember.save();
        // (offer as any).status = OnboardingStatus.EMPLOYEE_ONBOARDED;
        // await offer?.save();

        this.emitEvent('candidate.verify.email.confirm.password',
          {
            ...createMember.toObject(),
            password,
            application: jobApplication,
            jobDetails: jobDetails
          });
        const sanitizedUser = omit(createdMember.toObject(), ['password', '__v']);
      }


      // return sanitizedUser;

      // Fetch the email template for the offer event
      const emailTemplate = await this.getEmailTemplate(EmailTemplateEvent.WORKFLOW_OFFER, user);
      const istTimeZone = 'Asia/Kolkata';

      const body = await this.replacePlaceholders(unescape(emailTemplate.templateHTMLContent || ''), user, offer.jobApplication, emailTemplate._id.toString(), {
        ...offer.toJSON(),
        dateOfJoining: format(
          toZonedTime(new Date(offer.dateOfJoining), istTimeZone),
          'MMM dd yyyy, h:mm a'
        ),
        // dateOfJoining: format(new Date(offer.dateOfJoining), 'MMM dd yyyy, h:mm a')
      });

      // Log the event and clean up the HTML
      this.logger.log(`Triggered ${EmailTemplateEvent.WORKFLOW_OFFER} letter event.`);
      const cleanedHtml = body.replace(/<span[^>]*class="TiptapEditor_mention__[^"]*"[^>]*>(.*?)<\/span>/g, '$1');

      return cleanedHtml;

    } catch (error) {
      this.logger.error(`Failed to create and send offer. ${error.message}`);
      throw new InternalServerErrorException(`Error in creating and sending offer. ${error?.message}`);
    }
  }

  getAllOffers(): Promise<OfferDocument[]> {
    return this.offerModel.find()
      .populate({
        path: 'jobApplication',
        populate: [
          {
            path: 'jobId',
            select: '_id title'
          },
          {
            path: 'org',
            select: '_id title'
          }
        ],
        select: '_id jobId firstName lastName contactDetails contactAddress', model: 'JobApplication'
      })
      .exec();
  }

  async findOne(offerId: Types.ObjectId) {
    try {
      const offer = await this.offerModel.findById(offerId)
        .populate({ path: 'jobApplication', select: '_id jobId firstName lastName contactDetails contactAddress', model: 'JobApplication' })
        // .populate({ path: 'stages', select: '_id name type sequenceNumber' })
        // .populate({ path: 'org', select: '_id title orgType status legalName' })
        .exec();
      if (!offer) {
        throw new NotFoundException(`The offer with id: "${offerId}" doesn't exist.`);
      }
      return offer;
    }
    catch (error) {
      this.logger.error(`An error occurred in fetching offer by Id ${offerId}. ${error?.message}`);
      throw error;

    }
  }

  async findOfferByApplicationId(user: any, applicationId: Types.ObjectId) {
    try {
      // const offer = await this.offerModel.findById(offerId)
      const offer = await this.offerModel.findOne({ jobApplication: applicationId.toString() })
        .sort({ createdAt: -1 }) // 👉 Ensures the latest offer is returned
        // .populate({ path: 'jobApplication', select: '_id jobId firstName lastName contactDetails contactAddress', model: 'JobApplication' })
        .populate([
          {
            path: 'createdBy', // offer.createdBy
            model: 'BasicUser', // or your actual user model name
            select: '_id firstName lastName email'
          },
          {
            path: 'bgvHandlerId',
            model: 'BgvHandler',
            select: '_id email title contactNumber'
          },
          {
            path: 'jobApplication',
            populate: [
              {
                path: 'createdBy', // jobApplication.createdBy
                model: 'BasicUser',
                select: '_id firstName lastName email roles org'
              },
              {
                path: 'stage',
                model: 'Stage',
                select: 'type name'
              },
              {
                path: 'jobId',
                populate: {
                  path: 'endClientOrg',
                  model: 'Org',
                  select: '_id title'
                },
                model: 'Job',
                select: '_id title endClientOrg employmentType postingOrg requiredBgv'
              }
            ],
            model: 'JobApplication',
            select: '_id firstName lastName contactDetails contactAddress jobId stage org createdBy'
          }
        ])
        // .populate({ path: 'stages', select: '_id name type sequenceNumber' })
        // .populate({ path: 'org', select: '_id title orgType status legalName' })
        .exec();

      // ✅ Extract necessary IDs for comparison
      const postingOrgId = offer?.jobApplication?.jobId?.postingOrg?.toString();
      const userOrgId = user?.org?._id?.toString();
      const applicantOrgId = offer?.jobApplication?.createdBy?.org?.toString();
      const eligibilityStatus = await this.findInvoceEligibilityApplicationId(user, applicationId);
      // ✅ Add computed fields
      const responseWithFlags = {
        ...offer?.toObject(),
        isClient: postingOrgId === userOrgId,
        isInternalApplication: postingOrgId === applicantOrgId,
        ...eligibilityStatus,
      };

      if (!offer) {
        throw new NotFoundException(`The offer with id: "${applicationId}" doesn't exist.`);
      }
      return responseWithFlags;
    }
    catch (error) {
      this.logger.error(`An error occurred in fetching offer by Id ${applicationId}. ${error?.message}`);
      throw error;

    }
  }

  async update(offerId: Types.ObjectId, updateOfferDto: UpdateOfferDto, user: any) {
    try {
      const offer = await this.offerModel.findById(offerId).exec();
      if (!offer) {
        throw new NotFoundException(`The offer with id: "${offerId}" doesn't exist.`);
      }
      const updatedoffer = await this.offerModel.findByIdAndUpdate(offerId, updateOfferDto, { new: true })
        // .populate({ path: 'stages', select: '_id name type sequenceNumber' })
        // .populate({ path: 'org', select: '_id title orgType status legalName' })
        .exec();
      return updatedoffer;
    }
    catch (error) {
      this.logger.error(`An error occurred while updating offer by ID ${offerId}. ${error?.message}`)
      throw error;
    }
  }

  async hardDelete(offerId: Types.ObjectId) {
    try {
      const offer = await this.offerModel.findById(offerId).exec();
      if (!offer) {
        throw new NotFoundException(`The offer with id: "${offerId}" doesn't exist.`);
      }
      await this.offerModel.findByIdAndDelete(offerId);
      return `Offer deleted`;
    }
    catch (error) {
      this.logger.error(`An error occurred while deleting offer by ID ${offerId}. ${error?.message}`);
      throw error;
    }
  }
  // Helper function to retrieve job application
  async getJobApplication(jobApplicationId: string) {
    const jobApplication = await this.jobApplicationService.findById(jobApplicationId)
      .populate({ path: 'jobId', select: '_id title', model: 'Job' })
      .populate({ path: 'org', select: '_id title', model: 'Org' })
      .exec();

    if (!jobApplication) {
      throw new Error('Job application not found.');
    }

    return jobApplication;
  }

  // Helper function to get email template
  async getEmailTemplate(eventName: EmailTemplateEvent, user: any) {
    let emailTemplate = await this.emailTemplateService.findOne({
      eventName,
      isDefault: false,
      org: user.org._id,
      isDeleted: false
    }).exec();

    if (!emailTemplate) {
      emailTemplate = await this.emailTemplateService.findOne({
        eventName,
        isDefault: true,
        org: user.org._id,
        isDeleted: false
      }).exec();
    }

    if (!emailTemplate) {
      throw new Error(`Email template not found for the event: ${eventName}.`);
    }

    return emailTemplate;
  }

  // Helper function to replace placeholders in the email body
  async replacePlaceholders(body: string, user: any, jobApplication: any, emailTemplateId: string, offer: any) {
    const placeholders = await this.placeholderService.find({ emailTemplate: emailTemplateId }).exec();

    placeholders.forEach((placeholder) => {
      const path = placeholder.jsonPath;
      const value = JSONPath({
        path,
        json: { user, data: jobApplication.toJSON(), offer: offer },
      });

      const regex = new RegExp(`#${placeholder.name}`, 'g');
      if (value.length > 0) {
        body = body.replace(regex, `<b>${value[0]}</b>`);
      }
    });

    return body;
  }

  async sendAcceptanceReminder(user: any, jobApplicationId: string, dueDate: Date): Promise<string> {
    try {
      // Fetch the job application details
      const jobApplication = await this.getJobApplication(jobApplicationId);

      // Check if the job application has an associated offer
      const offer = await this.offerModel.findOne({
        jobApplication: jobApplicationId,
      }).sort({ createdAt: -1 }).exec();

      if (!offer) {
        throw new Error('No offer found for this job application.');
      }

      if (offer) {
        offer.dueDate = new Date(dueDate); // replace with actual date
        await offer.save();
      }

      // Fetch the email template for acceptance reminder
      const eventName = EmailTemplateEvent.WORKFLOW_OFFER_ACCEPTANCE_REMINDER;
      const emailTemplate = await this.getEmailTemplate(eventName, user);
      const istTimeZone = 'Asia/Kolkata';

      // Ensure the template content exists and replace placeholders
      let body = emailTemplate.templateHTMLContent ? unescape(emailTemplate.templateHTMLContent) : '';
      body = await this.replacePlaceholders(body, user, jobApplication, emailTemplate._id.toString(), {
        ...offer.toJSON(),
        dueDate: format(
          toZonedTime(new Date(offer.dueDate), istTimeZone),
          'MMM dd yyyy, h:mm a'
        ),
        // dateOfJoining: format(new Date(offer.dateOfJoining), 'MMM dd yyyy, h:mm a')
      });

      // Log the event and clean up the HTML for email
      this.logger.log(`Triggered ${eventName} event for offer acceptance reminder.`);
      const cleanedHtml = body.replace(/<span[^>]*class="TiptapEditor_mention__[^"]*"[^>]*>(.*?)<\/span>/g, '$1');
      return cleanedHtml;
    } catch (error) {
      this.logger.error(`Failed to send offer acceptance reminder. ${error.message}`);
      throw new InternalServerErrorException(`Error when sending offer acceptance reminder. ${error?.message}`);
    }
  }



  async sendDocumentReminder(user: any, jobApplicationId: string): Promise<string> {
    try {
      // Fetch the job application details
      const jobApplication = await this.getJobApplication(jobApplicationId);

      // Check if the job application has an associated offer
      const offer = await this.offerModel.findOne({
        jobApplication: jobApplicationId,
      }).exec();

      if (!offer) {
        throw new Error('No offer found for this job application.');
      }

      // Fetch the email template for acceptance reminder
      const eventName = EmailTemplateEvent.WORKFLOW_DOCUMENTATION_COLLECTION_REQUEST;
      const emailTemplate = await this.getEmailTemplate(eventName, user);

      // Ensure the template content exists and replace placeholders
      let body = emailTemplate.templateHTMLContent ? unescape(emailTemplate.templateHTMLContent) : '';
      body = await this.replacePlaceholders(body, user, jobApplication, emailTemplate._id.toString(), offer);

      // Log the event and clean up the HTML for email
      this.logger.log(`Triggered ${eventName} event for document reminder.`);
      const cleanedHtml = body.replace(/<span[^>]*class="TiptapEditor_mention__[^"]*"[^>]*>(.*?)<\/span>/g, '$1');
      return cleanedHtml;
    } catch (error) {
      this.logger.error(`Failed to send document reminder. ${error.message}`);
      throw new InternalServerErrorException(`Error when sending document reminder. ${error?.message}`);
    }
  }

  async calculateOfferRatio(userId: string): Promise<{ acceptanceRatio: number, accepted: number, totalOffers: number }> {
    try {
      // Fetch job applications created by the specific user
      const jobApplications = await this.jobApplicationService.find({ createdBy: userId });

      if (!jobApplications.length) {
        return { acceptanceRatio: 0, accepted: 0, totalOffers: 0 };
      }

      // Extract all related offers from job applications
      const jobApplicationIds = jobApplications.map((app) => app._id.toString());
      const offers = await this.offerModel.find({ jobApplication: { $in: jobApplicationIds } });

      // If no offers are found, return default values
      if (!offers?.length) {
        return { acceptanceRatio: 0, accepted: 0, totalOffers: 0 };
      }

      const totalOffers = offers.length;
      const accepted = offers.filter((offer) => offer.isAccepted).length;

      // Calculate the ratio
      const acceptanceRatio = totalOffers ? accepted / totalOffers : 0;

      return { acceptanceRatio, accepted, totalOffers };
    } catch (error) {
      this.logger.error(`Error calculating offer ratio: . ${error.message}`);
      throw new InternalServerErrorException(`Failed to calculate the offer ratio. ${error.message}`);
    }
  }

  async acceptMostRecentOffer(jobApplicationId: string): Promise<{ message: string }> {
    try {
      // Fetch the most recent offer for the specified job application
      const mostRecentOffer = await this.offerModel
        .findOne({ jobApplication: jobApplicationId })
        .sort({ updatedAt: -1 });

      if (!mostRecentOffer) {
        throw new NotFoundException('No offers found for the specified job application.');
      }

      // Update `isAccepted` to true for the most recent offer
      mostRecentOffer.isAccepted = true;
      await mostRecentOffer.save();

      return { message: 'The offer letter has been accepted successfully.' };
    } catch (error) {
      this.logger.error(`Error accepting the most recent offer:  ${error.message}`);
      throw new InternalServerErrorException(`Failed to accept the offer. ${error.message}`);
    }
  }

  async markAsOnBoarded(jobApplicationId: string): Promise<{ message: string }> {
    try {
      // Fetch the most recent offer for the specified job application
      const mostRecentOffer = await this.offerModel
        .findOne({ jobApplication: jobApplicationId })
        .sort({ updatedAt: -1 });

      if (!mostRecentOffer) {
        throw new NotFoundException('No offers found for the specified job application.');
      }

      // Ensure the offer is accepted before marking as on-boarded
      if (!mostRecentOffer.isAccepted) {
        throw new BadRequestException('The offer must be accepted before marking as on-boarded.');
      }

      // Update `isOnBoarded` to true for the most recent offer
      mostRecentOffer.isOnBoarded = true;
      await mostRecentOffer.save();

      return { message: 'The candidate has been marked as on-boarded successfully.' };
    } catch (error) {
      this.logger.error(`Error marking the candidate as on-boarded: ${error.message}`);
      throw new InternalServerErrorException(`Failed to mark as on-boarded. ${error.message}`);
    }
  }

  // async offerFilter(orgId: Types.ObjectId, isDefault?: boolean) {
  //   try {
  //     let query: any = {
  //       isDefault : false
  //     };
  //     if (orgId) {
  //       query.org = orgId;
  //     }
  //     if (isDefault) {
  //       query.isDefault = isDefault;
  //     }
  //     const offers = await this.offerModel.find(query)
  //     // .populate({ path: 'stages', select: '_id name type sequenceNumber' })
  //     // .populate({ path: 'org', select: '_id title orgType status legalName' })
  //     .exec();
  //     return offers;
  //   } catch (error) {
  //     this.logger.error(error)
  //     throw new InternalServerErrorException(`An error occurred while filtering offers. ${error?.message}`);
  //   }
  // }

  emitEvent(eventName: string, payload: any) {
    // payload['event']= eventName;
    // this.logger.debug(payload)
    this.eventEmitter.emit(eventName, payload);
  }

  // getAllOnboardings(user : any): Promise<OfferDocument[]> {
  //   return this.offerModel.find()
  //     .populate({
  //       path: 'jobApplication',
  //       populate: [
  //         {
  //           path: 'jobId',
  //           select: '_id title'
  //         },
  //         {
  //           path: 'org',
  //           select: '_id title'
  //         }
  //       ],
  //       select: '_id jobId firstName lastName contactDetails contactAddress', model: 'JobApplication'
  //     })
  //     .exec();
  // }

  async getAllOnboardings(user: any, query: OffersQueryDTO) {
    let conditions: any = {};
    const { postingOrg, endClientOrg, name, page = 1, limit = 10, jobType, employmentType, fromDate, toDate } = query;
    let jobs = [];
    let appIds = [];
    if (user.roles?.includes(Role.Vendor)) {
      const existingOrgs = await this.orgsModel.find({ 'contactDetails.contactEmail': user.email, isDeleted: false, orgType: OrgType.VENDOR_ORG }).exec();

      // Step 2: Extract org _ids
      const vendorOrgIds = existingOrgs.map(org => org._id.toString());
      console.log("vendorOrgIds", vendorOrgIds);
      // Step 3: Prepare allocation query
      const queryConditions: any = {
        kind: 'JobAllocationToVendors',
        vendor: { $in: vendorOrgIds } // Match any of the vendor orgs
      };
      // Step 4: Query allocations
      const jobAllocations = await this.jobAllocationBasesModel.find(queryConditions).exec();
      const allocatedjobIds = jobAllocations.map(allocation => new Types.ObjectId(allocation.job.toString()));

      jobs = await this.jobModel.find({ _id: { $in: allocatedjobIds } }, { _id: 1 }).lean();
      const jobIds = jobs.map(job => job._id.toString());

      const applications = await this.jobApplicationModel.find({
        jobId: { $in: jobIds },
        createdBy: user._id.toString(),
        isRejected: false,
        isDeleted: false,
      }, { _id: 1 }).lean();
      appIds = applications.map(app => app._id.toString());
    }
    else {
      jobs = await this.jobModel.find({ postingOrg: user.org._id.toString() }, { _id: 1 }).lean();
      const jobIds = jobs.map(job => job._id.toString());

      const applications = await this.jobApplicationModel.find({
        jobId: { $in: jobIds },
        isRejected: false,
        isDeleted: false,
      }, { _id: 1 }).lean();
      appIds = applications.map(app => app._id.toString());
    }

    console.log("appIds", appIds)

    // Step 1: Get latest offers using aggregation
    const latestOffersRaw = await this.offerModel.aggregate([
      {
        $match:
        {
          // isAccepted: true,
          isApproved: true,
          jobApplication: { $in: appIds },
          status: {
            $nin: [
              OnboardingStatus.INVOICE_GENERATED,
              OnboardingStatus.EMPLOYEE_ONBOARDED
            ]
          }
        }
      },
      { $sort: { createdAt: -1 } },
      {
        $group: {
          _id: "$jobApplication",
          latestOffer: { $first: "$$ROOT" }
        }
      },
      {
        $replaceRoot: { newRoot: "$latestOffer" }
      }
    ]);

    // Step 2: Populate manually
    const offerIds = latestOffersRaw.map(offer => offer._id);

    console.log("offerIds", offerIds)
    const offers = await this.offerModel.find({ _id: { $in: offerIds } })
      .sort({ createdAt: -1 }) // Sort latest offers at the top
      .populate([
        {
          path: 'createdBy',
          model: 'BasicUser',
          select: '_id firstName lastName email'
        },
        {
          path: 'jobApplication',
          populate: [
            {
              path: 'createdBy',
              model: 'BasicUser',
              select: '_id firstName lastName email roles'
            },
            {
              path: 'stage',
              model: 'Stage',
              select: 'type name'
            },
            {
              path: 'jobId',
              populate: {
                path: 'endClientOrg',
                model: 'Org',
                select: '_id title'
              },
              model: 'Job',
              select: '_id title endClientOrg employmentType requiredBgv'
            }
          ],
          model: 'JobApplication',
        }
      ]);

    // const offers = await this.offerModel.find({
    //   isAccepted: true,
    //   jobApplication: { $in: appIds }
    // }).populate([
    //   {
    //     path: 'createdBy', // offer.createdBy
    //     model: 'BasicUser', // or your actual user model name
    //     select: '_id firstName lastName email'
    //   },
    //   {
    //     path: 'jobApplication',
    //     populate: [
    //       {
    //         path: 'createdBy', // jobApplication.createdBy
    //         model: 'BasicUser',
    //         select: '_id firstName lastName email roles'
    //       },
    //       {
    //         path: 'stage',
    //         model: 'Stage',
    //         select: 'type name'
    //       },
    //       {
    //         path: 'jobId',
    //         populate: {
    //           path: 'endClientOrg',
    //           model: 'Org',
    //           select: '_id title'
    //         },
    //         model: 'Job',
    //         select: '_id title endClientOrg employmentType requiredBgv'
    //       }
    //     ],
    //     model: 'JobApplication',
    //     // select: '_id firstName lastName contactDetails contactAddress jobId stage org createdBy'
    //   }
    // ]).exec();

    const offerids = offers.map(app => app._id.toString());

    console.log(offerids)
    // const filteredOffers = offers.filter(
    //   offer => (offer.jobApplication?.stage as any)?.type === 'workflow.offer'
    // );

    const filteredOffers = offers.filter((offer: any) => {
      const stageType = (offer.jobApplication?.stage as any)?.type;
      const job = offer.jobApplication?.jobId;
      const candidate = offer.jobApplication;

      // Stage type must be workflow.offer
      if (stageType !== 'workflow.offer') return false;

      // Filter by endClientOrg
      if (endClientOrg && job?.endClientOrg?._id?.toString() !== endClientOrg) return false;

      // Filter by employmentType
      if (employmentType && job?.employmentType !== employmentType) return false;

      // Filter by date range on offer createdAt
      if (fromDate) {
        const from = new Date(fromDate);
        if (new Date(offer.createdAt) < from) return false;
      }
      if (toDate) {
        const to = new Date(toDate);
        if (new Date(offer.createdAt) > to) return false;
      }

      // Filter by candidate name
      if (name) {
        const regex = new RegExp(name, 'i');
        const candidateFullName = `${candidate?.firstName ?? ''} ${candidate?.lastName ?? ''}`;
        const candidateCreatedFullName = `${candidate?.createdBy?.firstName ?? ''} ${candidate?.createdBy?.lastName ?? ''}`;
        // if (!regex.test(candidateFullName)) return false;
        const searchTarget = [
          job?.title,
          job?.endClientOrg?.title,
          job?.employmentType,
          candidateFullName,
          candidateCreatedFullName,
          candidate?.contactDetails?.contactNumber,
        ]
          .filter(Boolean)
          .join(' ');

        if (!regex.test(searchTarget)) return false;
      }

      return true;
    });

    const offersWithBgvCheck = await Promise.all(
      filteredOffers.map(async (offer: any) => {
        const applicationId = offer?.jobApplication?._id?.toString();
        let isBgvRaised: boolean = false;
        if (offer?.bgvId || offer?.bgvHandlerId) {
          isBgvRaised = true;
        }
        const isBgvHandlerExists = await this.findBgvHandler(applicationId); // You can update this to return just the boolean if needed
        const eligibilityStatus = await this.findInvoceEligibilityApplicationId(user, applicationId);
        return {
          ...offer.toObject?.() ?? offer,
          isBgvHandlerExists,
          isBgvRaised,
          ...eligibilityStatus,
        };
      })
    );


    // Pagination
    const total = offersWithBgvCheck.length;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedOffers = offersWithBgvCheck.slice(startIndex, endIndex);

    return {
      data: paginatedOffers,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };

    return filteredOffers;

  }

  async findInvoceEligibilityApplicationId(user: any, applicationId: Types.ObjectId) {
    try {

      const jobApp = await this.jobApplicationModel.findById(applicationId)
        .populate({ path: 'createdBy', select: '_id firstName roles org' })
        .exec();
      console.log(jobApp)
      const job = await this.jobModel.findById(jobApp?.jobId).exec();
      console.log(job)

      let isEligibleForInvoice = false;
      let isEligibleForOnboading = false;

      //here we need to handle multiple vendors with same email
      let billingOrgId = user?.org?._id;
      let billToOrgId: any;
      if (user?.roles.includes(Role.Vendor)) {
        const existingOrgs = await this.orgsModel.find({ 'contactDetails.contactEmail': user.email, isDeleted: false, orgType: OrgType.VENDOR_ORG }).exec();
        const matchedVendorOrg = existingOrgs.find(vendorOrg =>
          vendorOrg?.companyId?.toString() === job?.postingOrg?.toString()
        );
        if (matchedVendorOrg) {
          billingOrgId = matchedVendorOrg._id;
          billToOrgId = matchedVendorOrg._id;
        }
      } else {
        billingOrgId = user?.org?._id;
        billToOrgId = job?.endClientOrg;
      }
      // const billingOrg = await this.orgService.findOne(billingOrgId);
      // const billToOrg = await this.orgService.findOne(billToOrgId);

      const existingInvoice = await this.invoiceModel.findOne({ jobApplication: applicationId.toString(), billedByOrg: billingOrgId.toString(), billedToOrg: billToOrgId.toString() }).exec();

      if (job?.jobType === JobType.Internal) {
        if (user?.roles.includes(Role.Vendor)) {
          if (job?.employmentType === EmploymentType.FullTime) {
            if (jobApp?.createdBy.roles?.includes(Role.Vendor)) {

              isEligibleForInvoice = true;
              isEligibleForOnboading = false;
            }
          }
          if (job?.employmentType === EmploymentType.Contract) {
            if (jobApp?.createdBy.roles?.includes(Role.Vendor)) {
              isEligibleForInvoice = true;
              isEligibleForOnboading = true;
            }
          }
        }
        // else if (user.org._id.toString() === jobApp?.createdBy.org?.toString()) {
        else {
          if (job?.employmentType === EmploymentType.FullTime) {
            if (jobApp?.createdBy.roles?.includes(Role.Vendor)) {
              isEligibleForInvoice = false;
              isEligibleForOnboading = true;
            }
            else if (jobApp?.createdBy.org === job.endClientOrg) {
              isEligibleForInvoice = false;
              isEligibleForOnboading = true;
            }
          }
          if (job?.employmentType === EmploymentType.Contract) {
            if (jobApp?.createdBy.roles?.includes(Role.Vendor)) {
              isEligibleForInvoice = false;
              isEligibleForOnboading = false;
            }
            else if (jobApp?.createdBy.org === job.endClientOrg) {
              isEligibleForInvoice = false;
              isEligibleForOnboading = true;
            }
          }
        }


      }

      if (job?.jobType === JobType.External) {
        if (user?.roles.includes(Role.Vendor)) {
          if (job?.employmentType === EmploymentType.FullTime) {
            if (jobApp?.createdBy.roles?.includes(Role.Vendor)) {
              isEligibleForInvoice = true;
              isEligibleForOnboading = false;
            }
          }
          if (job?.employmentType === EmploymentType.Contract) {
            if (jobApp?.createdBy.roles?.includes(Role.Vendor)) {
              isEligibleForInvoice = true;
              isEligibleForOnboading = true;
            }
          }
        }
        // else if (user.org._id.toString() === jobApp?.createdBy.org?.toString()) {
        else {
          if (job?.employmentType === EmploymentType.FullTime) {
            if (jobApp?.createdBy.roles?.includes(Role.Vendor)) {
              isEligibleForInvoice = true;
              isEligibleForOnboading = false;
            }
            else if (jobApp?.createdBy.org !== job.endClientOrg) {
              isEligibleForInvoice = true;
              isEligibleForOnboading = false;
            }
          }
          if (job?.employmentType === EmploymentType.Contract) {
            if (jobApp?.createdBy.roles?.includes(Role.Vendor)) {
              isEligibleForInvoice = true;
              isEligibleForOnboading = false;
            }
            else if (jobApp?.createdBy.org !== job.endClientOrg) {
              isEligibleForInvoice = true;
              isEligibleForOnboading = true;
            }
          }
        }


      }
      if (existingInvoice) {
        isEligibleForInvoice = false;
      }

      if (!jobApp) {
        throw new NotFoundException(`The application with id: "${applicationId}" doesn't exist.`);
      }
      return { isEligibleForInvoice, isEligibleForOnboading };
      // return responseWithFlags;
    }
    catch (error) {
      // this.logger.error(`An error occurred in fetching offer by Id ${applicationId}. ${error?.message}`);
      throw error;

    }
  }

  async findBgvHandler(applicationId: string) {

    const application = await this.jobApplicationModel.findById(applicationId).populate('jobId').exec(); // or createdBy depending on your schema

    console.log(application);
    // for (const application of jobApplications) {
    let bgvHandler: any;
    if (application?.jobId?.jobType === JobType.Internal) {
      const departmentId = application?.jobId?.department;

      if (departmentId) {
        const department = await this.businessUnitModel.findById(departmentId).select('bgvHandlerId').exec();
        if (department?.bgvHandlerId) {
          bgvHandler = await this.bgvHandlerModel.findOne({
            _id: new Types.ObjectId(department.bgvHandlerId),
            isDeleted: false,
          }).exec();
        }
      }
      if (!bgvHandler) {
        bgvHandler = await this.bgvHandlerModel.findOne({
          org: application?.jobId?.postingOrg,
          isDeleted: false,
          isDefault: true
        }).exec();
      }
      // if (!bgvHandler) {
      //   bgvHandler = await this.bgvHandlerModel.findOne({
      //     org: application?.jobId?.postingOrg,
      //     isDeleted: false,
      //   }).exec();
      // }
    }
    else if (application?.jobId?.jobType === JobType.External) {
      const endClientOrgId = application?.jobId?.endClientOrg;
      const endClientOrg = await this.orgsModel.findById(endClientOrgId).select('bgvHandlerId').exec();
      bgvHandler = await this.bgvHandlerModel.findOne({
        _id: new Types.ObjectId(endClientOrg?.bgvHandlerId),
        isDeleted: false,
      }).exec();
      if (!bgvHandler) {
        bgvHandler = await this.bgvHandlerModel.findOne({
          org: application?.jobId?.postingOrg,
          isDeleted: false,
          isDefault: true
        }).exec();
      }
      // if (!bgvHandler) {
      //   bgvHandler = await this.bgvHandlerModel.findOne({
      //     org: application.jobId?.postingOrg,
      //     isDeleted: false,
      //   }).exec();
      // }
    }

    console.log('bgvHandler', bgvHandler)
    return !!bgvHandler;
    // }
    // ✅ Move this outside the loop
    // return { message: 'Upload links sent to all valid candidates successfully.' };
  }

  async saveBgvDocuments(user: any, createBgvDto: CreateBgvDto) {
    const docs = Object.entries(createBgvDto.documents).map(([key, fileMetadataId]) => ({
      key,
      fileMetadataId,
    }));

    const offer = await this.offerModel.findOne({ jobApplication: createBgvDto.jobApplication.toString() })
      .sort({ createdAt: -1 }) // 👉 Ensures the latest offer is returned
      .exec();

    // You can either update if already exists or create new
    const createdBGV = await this.bgvModel.findOneAndUpdate(
      { jobApplication: createBgvDto.jobApplication },
      {
        jobApplication: createBgvDto.jobApplication,
        documents: docs,
      },
      { new: true, upsert: true, setDefaultsOnInsert: true }
    );


    // Step 3: Update the offer with the bgvId
    if (!offer) {
      throw new NotFoundException('No offer found for the specified job application.');
    }
    (offer as any).bgvId = createdBGV._id;
    await offer.save();

    return createdBGV;
  }

  async submitBgvDocuments(applicationId: Types.ObjectId) {

    const offer = await this.offerModel.findOne({ jobApplication: applicationId.toString() })
      .sort({ createdAt: -1 }) // 👉 Ensures the latest offer is returned
      .exec();
    // Step 3: Update the offer with the bgvId
    if (!offer) {
      throw new NotFoundException('No offer found for the specified job application.');
    }
    offer.status = OnboardingStatus.AWAITING_BGV; // Set status to AWAITING_BGV
    offer.stepperKey = 2;
    await offer.save();

    return offer;
  }

  // async findBgvDocumentsByApplicationId(applicationId: Types.ObjectId) {
  //   try {
  //     const existingBgv = await this.bgvModel.findOne({ jobApplication: applicationId.toString() })
  //       .populate({
  //         path: 'documents.fileMetadataId',
  //         model: 'FileMetadata',
  //         select: 'originalName uniqueName fileSize fileType uploadedBy createdAt locationUrl',
  //       }).lean();
  //     console.log("existingBgv", existingBgv);
  //     const response = REQUIRED_BGV_DOCUMENTS.map(doc => {
  //       const matched = existingBgv?.documents?.find((d: any) => d.key === doc.key);
  //       return {
  //         ...doc,
  //         docs: matched?.fileMetadataId?.length
  //           ? matched.fileMetadataId.map((file: any) => ({
  //             _id: file._id,
  //             uploadedBy: file.uploadedBy,
  //             originalName: file.originalName,
  //             uniqueName: file.uniqueName,
  //             fileSize: file.fileSize,
  //             fileType: file.fileType,
  //             locationUrl: file.locationUrl,
  //             createdAt: file.createdAt,
  //           }))
  //           : [],
  //       };
  //     });

  //     return response;
  //   }
  //   catch (error) {
  //     this.logger.error(`An error occurred in fetching offer by Id ${applicationId}. ${error?.message}`);
  //     throw error;

  //   }
  // }

  async saveBgvHandlerInfo(user: any, createBgvDto: CreateBgvDto) {

    // You can either update if already exists or create new

    const updatedBgv = await this.bgvModel.findOneAndUpdate(
      { jobApplication: createBgvDto.jobApplication },
      {
        $set: {
          bgvHandlerName: createBgvDto.bgvHandlerName,
          bgvHandlerEmail: createBgvDto.bgvHandlerEmail,
          bgvHandlerContactNo: createBgvDto.bgvHandlerContactNo,
          expectedFinalReportDate: createBgvDto.expectedFinalReportDate,
        },
      },
      { new: true, upsert: false }
    );

    return updatedBgv;
  }

  async findBgvInfoByApplicationId(applicationId: Types.ObjectId) {
    try {
      const existingBgv = await this.bgvModel.findOne({ jobApplication: applicationId.toString() })
        .populate([
          {
            path: 'documents.versions.fileId',
            model: 'FileMetadata',
            select: 'originalName uniqueName fileSize fileType uploadedBy createdAt locationUrl',
          },
          {
            path: 'interimBgvDocs.fileMetadataId',
            model: 'FileMetadata',
            select: 'originalName uniqueName fileSize fileType uploadedBy createdAt locationUrl',
          },
          {
            path: 'interimBgvDocsHistory.replacedFileMetadataId',
            model: 'FileMetadata',
            select: 'originalName uniqueName fileSize fileType uploadedBy createdAt locationUrl',
          },
          {
            path: 'interimBgvDocsHistory.replacedByFileMetadataId',
            model: 'FileMetadata',
            select: 'originalName uniqueName fileSize fileType uploadedBy createdAt locationUrl',
          },
          {
            path: 'offerLetterDoc.fileMetadataId',
            model: 'FileMetadata',
            select: 'originalName uniqueName fileSize fileType uploadedBy createdAt locationUrl',
          },
          {
            path: 'offerLetterHistory.fileMetadataId',
            model: 'FileMetadata',
            select: 'originalName uniqueName fileSize fileType uploadedBy createdAt locationUrl',
          },
          // {
          //   path: 'customBgvDocuments.fileMetadataId',
          //   model: 'FileMetadata',
          //   select: 'originalName uniqueName fileSize fileType uploadedBy createdAt locationUrl',
          // }
        ])
        .exec();

      // Fetch status from the Offer
      const offer = await this.offerModel.findOne({ jobApplication: applicationId.toString() }, { status: 1 }).lean();
      console.log("existingBgv", offer);


      // Merge the status into the BGV response
      const result = {
        ...existingBgv?.toObject?.(), // convert Mongoose document to plain object
        status: offer?.status || null,
      };

      return result;

    }
    catch (error) {
      this.logger.error(`An error occurred in fetching offer by Id ${applicationId}. ${error?.message}`);
      throw error;

    }
  }

  async saveInterimBgv(user: any, createBgvDto: CreateBgvDto) {

    // const updatePayload: any = {
    //   interimBgvDocs: createBgvDto.interimBgvDocs || [],
    // };

    // // if (createBgvDto.customBgvDocuments) {
    // //   updatePayload.customBgvDocuments = createBgvDto.customBgvDocuments;
    // // }

    // const updatedBGV = await this.bgvModel.findOneAndUpdate(
    //   { jobApplication: createBgvDto.jobApplication },
    //   // { interimBgvDocs : createBgvDto.interimBgvDocs || []},
    //   updatePayload,
    //   { new: true }
    // );

    const existingBgv = await this.bgvModel.findOne({ jobApplication: createBgvDto.jobApplication.toString() });

    if (!existingBgv) {
      throw new NotFoundException('No BGV record found');
    }

    const updatedDocs = createBgvDto.interimBgvDocs?.map(newDoc => {
      // const previous = existingBgv.interimBgvDocs?.find(
      //   doc => doc.fileMetadataId.toString() === newDoc.toString()
      // );

      return {
        fileMetadataId: newDoc.toString(),
        status: 'pending',
        uploadedAt: new Date(),
        // history: previous
        //   ? [
        //     ...(previous.history || []),
        //     {
        //       fileMetadataId: previous.fileMetadataId,
        //       status: previous.status,
        //       uploadedAt: previous.uploadedAt,
        //     },
        //   ]
        //   : [],
      };
    }) || [];

    existingBgv.interimBgvDocs = updatedDocs;

    return existingBgv.save();
  }

  async submitIntrimBgv(applicationId: Types.ObjectId) {

    const offer = await this.offerModel.findOne({ jobApplication: applicationId.toString() })
      .sort({ createdAt: -1 }) // 👉 Ensures the latest offer is returned
      .exec();
    // Step 3: Update the offer with the bgvId
    if (!offer) {
      throw new NotFoundException('No offer found for the specified job application.');
    }
    offer.status = OnboardingStatus.CLIENT_BGV_APPROVAL_PENDING; // Set status to CLIENT_BGV_APPROVAL_PENDING
    offer.stepperKey = 3;
    offer.clientOfferReleased = false;
    await offer.save();

    return offer;
  }

  async replaceRejectedDoc(applicationId: string, rejectedFileMetadataId: string, newFileMetadataId: string) {
    const existingBgv = await this.bgvModel.findOne({ jobApplication: applicationId });

    if (!existingBgv) {
      throw new NotFoundException('No BGV record found');
    }

    const docIndex = existingBgv.interimBgvDocs?.findIndex(doc => doc.fileMetadataId.toString() === rejectedFileMetadataId);

    if (docIndex === -1) {
      throw new NotFoundException('Rejected document not found in interimBgvDocs');
    }

    const doc = existingBgv.interimBgvDocs[docIndex];

    if (doc.status !== 'rejected') {
      throw new BadRequestException('Only rejected documents can be replaced');
    }

    // Add replaced doc info to BGV-level history
    existingBgv.interimBgvDocsHistory = existingBgv.interimBgvDocsHistory || [];
    existingBgv.interimBgvDocsHistory.push({
      replacedFileMetadataId: doc.fileMetadataId,
      replacedAt: doc.uploadedAt,
      replacedStatus: doc.status,
      replacedByFileMetadataId: newFileMetadataId,
      replacedByAt: new Date(),
      rejectionReason: doc.rejectionReason
    });

    // Replace with new file metadata id and reset status
    doc.fileMetadataId = newFileMetadataId;
    doc.status = 'pending';
    doc.uploadedAt = new Date();
    doc.reviewedAt = undefined;
    doc.reviewedBy = undefined;
    doc.rejectionReason = undefined;

    // delete doc.rejectionReason;
    // delete doc.reviewedAt;
    // delete doc.reviewedBy;

    existingBgv.interimBgvDocs[docIndex] = doc;

    await existingBgv.save();

    return doc;
  }

  async saveBudgetConfirmation(user: any, applicationId: Types.ObjectId, budgetConfirmationDto: BudgetConfirmationDto) {
    const offer = await this.offerModel.findOne({ jobApplication: applicationId.toString() })
      .sort({ createdAt: -1 }) // 👉 Ensures the latest offer is returned
      .exec();
    if (!offer) {
      throw new NotFoundException('Offer not found for the provided application ID');
    }

    offer.budgetConfirmation = {
      effectiveStartDate: new Date(budgetConfirmationDto.effectiveStartDate),
      ctc: budgetConfirmationDto.ctc,
      hourlyPay: budgetConfirmationDto.hourlyPay,
      employmentType: budgetConfirmationDto.employmentType,
      confirmedBy: user._id.toString(),
      confirmedAt: new Date(),
    };

    await offer.save();
    return offer;
  }

  async reviewSingleInterimDoc(
    user: any,
    applicationId: Types.ObjectId,
    reviewInterimDocDto: ReviewInterimDocDto,

  ) {
    const bgv = await this.bgvModel.findOne({ jobApplication: applicationId.toString() });

    if (!bgv) throw new NotFoundException('BGV record not found');

    const doc = bgv.interimBgvDocs.find(
      d => d.fileMetadataId.toString() === reviewInterimDocDto.fileMetadataId.toString(),
    );

    if (!doc) {
      throw new NotFoundException('Document not found in interimBgvDocs');
    }

    // if (doc.status !== 'pending') {
    //   throw new BadRequestException(`Document already ${doc.status}`);
    // }

    doc.status = reviewInterimDocDto.status;
    doc.reviewedAt = new Date();
    doc.reviewedBy = user._id.toString();
    if (reviewInterimDocDto.status === 'rejected') {
      doc.rejectionReason = reviewInterimDocDto.reason;
    }

    await bgv.save();

    return {
      message: `Document ${reviewInterimDocDto.status} successfully`,
      fileMetadataId: reviewInterimDocDto.fileMetadataId,
      status: reviewInterimDocDto.status,
    };
  }

  async clientOfferRelease(user: any, applicationId: Types.ObjectId) {

    const offer = await this.offerModel.findOne({ jobApplication: applicationId.toString() })
      .sort({ createdAt: -1 }) // 👉 Ensures the latest offer is returned
      .exec();
    // Step 3: Update the offer with the bgvId
    if (!offer) {
      throw new NotFoundException('No offer found for the specified job application.');
    }
    offer.status = OnboardingStatus.CLIENT_BGV_APPROVAL_DONE; // Set status to CLIENT_BGV_APPROVAL_PENDING
    offer.clientOfferReleased = true; // Set clientBgvApproved to true
    // offer.stepperKey = 3;
    await offer.save();

    return offer;
  }

  async saveOfferLetters(user: any, createBgvDto: CreateBgvDto) {

    const existingBgv = await this.bgvModel.findOne({ jobApplication: createBgvDto.jobApplication.toString() });

    const jobApp = await this.jobApplicationModel.findById(createBgvDto.jobApplication).exec();
    const job = await this.jobModel.findById(jobApp?.jobId).exec();

    const offer = await this.offerModel.findOne({ jobApplication: createBgvDto.jobApplication })
      .sort({ createdAt: -1 }) // 👉 Ensures the latest offer is returned
      .exec();

    if (!offer) {
      throw new NotFoundException('No offer found for the specified job application.');
    }

    if (!existingBgv) {
      throw new NotFoundException('No BGV record found');
    }

    const existingOfferApproval = await this.hikeApprovalModel.findOne({
      offerId: offer._id.toString(),
      status: "PENDING"
    }).exec();

    if (existingOfferApproval) {
      throw new HttpException(
        'An offer has already been released and is awaiting approval. Please wait until the current offer is approved before releasing a new one.',
        HttpStatus.BAD_REQUEST,
      );
    }

    existingBgv.offerLetterDoc = {
      fileMetadataId: createBgvDto.offerLetterDoc?.toString(),
      expectedCtc: createBgvDto.expectedCtc,
      uploadedBy: user._id.toString(),
      uploadedAt: new Date(),
      isAccepted: false,
      isRejected: false,
    };

    let hikeSettings: any;
    const approvers: any = [];
    if (job?.jobType === JobType.Internal) {
      const departmentId = job.department;
      if (departmentId) {
        const department = await this.businessUnitModel.findById(departmentId).lean();
        approvers.push(department?.departmentHead);
        if (department?.hikeSettings) {
          if (job.employmentType === EmploymentType.FullTime) {
            hikeSettings = department?.hikeSettings[job.employmentType];
          }
          if (job.employmentType === EmploymentType.Contract) {
            hikeSettings = department?.hikeSettings[job.employmentType];
          }
        }
        if (!hikeSettings) {
          const orgId = job.postingOrg;
          const org = await this.orgsModel.findById(orgId).lean();
          if (org?.hikeSettings) {
            if (job.employmentType === EmploymentType.FullTime) {
              hikeSettings = org?.hikeSettings[job.employmentType];
            }
            if (job.employmentType === EmploymentType.Contract) {
              hikeSettings = org?.hikeSettings[job.employmentType];
            }
          }
        }
      }
      else {
        const orgId = job.postingOrg;
        const org = await this.orgsModel.findById(orgId).lean();
        if (org?.hikeSettings) {
          if (job.employmentType === EmploymentType.FullTime) {
            hikeSettings = org?.hikeSettings[job.employmentType];
          }
          if (job.employmentType === EmploymentType.Contract) {
            hikeSettings = org?.hikeSettings[job.employmentType];
          }
        }
      }
    }
    if (job?.jobType === JobType.External) {
      const orgId = job.endClientOrg;
      const org = await this.orgsModel.findById(orgId).lean();
      const assignTo = org?.assignTo ?? [];
      for (const userId of assignTo) {
        const idStr = userId.toString();
        if (!approvers.find((u: { toString: () => string; }) => u.toString() === idStr)) {
          approvers.push(userId);
        }
      }
      if (org?.hikeSettings) {
        if (job.employmentType === EmploymentType.FullTime) {
          hikeSettings = org?.hikeSettings[job.employmentType];
        }
        if (job.employmentType === EmploymentType.Contract) {
          hikeSettings = org?.hikeSettings[job.employmentType];
        }
      }
      if (!hikeSettings) {
        const orgId = job.postingOrg;
        const org = await this.orgsModel.findById(orgId).lean();
        if (org?.hikeSettings) {
          if (job.employmentType === EmploymentType.FullTime) {
            hikeSettings = org?.hikeSettings[job.employmentType];
          }
          if (job.employmentType === EmploymentType.Contract) {
            hikeSettings = org?.hikeSettings[job.employmentType];
          }
        }
      }
    }

    if (job?.maxCtcOffered && createBgvDto.expectedCtc && hikeSettings) {
      const offeredCtc = createBgvDto.expectedCtc;
      const currentCtc = jobApp?.currentCTC;

      const minMargin = hikeSettings.minMarginPercentage ?? 0;
      const maxHike = hikeSettings.maxHikePercentage ?? 0;

      const maxAllowedByMargin = Math.round(job.maxCtcOffered * (1 - minMargin / 100));
      const maxAllowedByHike = currentCtc ? Math.round(currentCtc * (1 + maxHike / 100)) : null;

      const violations: string[] = [];

      if (offeredCtc > maxAllowedByMargin) {
        violations.push(
          `Offered CTC (${offeredCtc}) exceeds max allowed by margin (${maxAllowedByMargin})`
        );
      }

      if (currentCtc && offeredCtc > maxAllowedByHike!) {
        violations.push(
          `Offered CTC (${offeredCtc}) exceeds max allowed hike over current CTC (${maxAllowedByHike})`
        );
      }

      if (violations.length > 0 && hikeSettings.requiresApproval) {
        const existingApproved = await this.hikeApprovalModel.findOne({
          applicationId: jobApp?._id.toString(),
          offerId: offer._id.toString(),
          status: 'APPROVED',
          modifiedCtc: { $gte: offeredCtc },
        }).lean();

        if (!existingApproved) {
          // ✅ Create approval record
          offer.status = OnboardingStatus.OFFER_APPROVAL_PENDING;

          await this.hikeApprovalModel.create({
            applicationId: jobApp?._id.toString(),
            jobId: job._id.toString(),
            offerId: offer._id.toString(),
            orgId: job.postingOrg,
            employmentType: job.employmentType,
            hikeSettings: hikeSettings,
            reason: violations.join('; '),
            maxAllowedByHike: maxAllowedByHike,
            maxAllowedByMargin: maxAllowedByMargin,
            approvers: approvers,
            approverRole: hikeSettings.approverRole,
            status: 'PENDING',
            createdAt: new Date(),
            createdBy: user?._id,
          });
        }
      }
    }
    offer.salaryPerAnnum = createBgvDto.expectedCtc ? createBgvDto.expectedCtc : offer.salaryPerAnnum;
    await offer.save();

    return existingBgv.save();
  }


  async reviseOfferLetter(
    user: any,
    createBgvDto: CreateBgvDto,
  ) {
    const existingBgv = await this.bgvModel.findOne({ jobApplication: createBgvDto.jobApplication.toString() });
    if (!existingBgv) {
      throw new NotFoundException('No BGV record found');
    }

    const jobApp = await this.jobApplicationModel.findById(createBgvDto.jobApplication).exec();
    const job = await this.jobModel.findById(jobApp?.jobId).exec();

    const offer = await this.offerModel.findOne({ jobApplication: createBgvDto.jobApplication })
      .sort({ createdAt: -1 }) // 👉 Ensures the latest offer is returned
      .exec();

    if (!offer) {
      throw new NotFoundException('No offer found for the specified job application.');
    }

    const existingOfferApproval = await this.hikeApprovalModel.findOne({
      offerId: offer._id.toString(),
      status: "PENDING"
    }).exec();

    if (existingOfferApproval) {
      throw new HttpException(
        'An offer has already been released and is awaiting approval. Please wait until the current offer is approved before revising the offer',
        HttpStatus.BAD_REQUEST,
      );
    }

    // Save previous offer to history
    if (existingBgv.offerLetterDoc) {
      existingBgv.offerLetterHistory.push({
        fileMetadataId: existingBgv.offerLetterDoc.fileMetadataId,
        uploadedBy: existingBgv.offerLetterDoc.uploadedBy,
        uploadedAt: existingBgv.offerLetterDoc.uploadedAt,
        expectedCtc: existingBgv.offerLetterDoc.expectedCtc
      });
    }
    // Replace with new offer
    existingBgv.offerLetterDoc = {
      fileMetadataId: createBgvDto.offerLetterDoc?.toString(),
      expectedCtc: createBgvDto.expectedCtc,
      uploadedBy: user._id.toString(),
      uploadedAt: new Date(),
      isAccepted: false,
      isRejected: false,
    };

    let hikeSettings: any;
    const approvers: any = [];
    if (job?.jobType === JobType.Internal) {
      const departmentId = job.department;
      if (departmentId) {
        const department = await this.businessUnitModel.findById(departmentId).lean();
        approvers.push(department?.departmentHead);
        if (department?.hikeSettings) {
          if (job.employmentType === EmploymentType.FullTime) {
            hikeSettings = department?.hikeSettings[job.employmentType];
          }
          if (job.employmentType === EmploymentType.Contract) {
            hikeSettings = department?.hikeSettings[job.employmentType];
          }
        }
        if (!hikeSettings) {
          const orgId = job.postingOrg;
          const org = await this.orgsModel.findById(orgId).lean();
          if (org?.hikeSettings) {
            if (job.employmentType === EmploymentType.FullTime) {
              hikeSettings = org?.hikeSettings[job.employmentType];
            }
            if (job.employmentType === EmploymentType.Contract) {
              hikeSettings = org?.hikeSettings[job.employmentType];
            }
          }
        }
      }
      else {
        const orgId = job.postingOrg;
        const org = await this.orgsModel.findById(orgId).lean();
        if (org?.hikeSettings) {
          if (job.employmentType === EmploymentType.FullTime) {
            hikeSettings = org?.hikeSettings[job.employmentType];
          }
          if (job.employmentType === EmploymentType.Contract) {
            hikeSettings = org?.hikeSettings[job.employmentType];
          }
        }
      }
    }
    if (job?.jobType === JobType.External) {
      const orgId = job.endClientOrg;
      const org = await this.orgsModel.findById(orgId).lean();
      const assignTo = org?.assignTo ?? [];
      for (const userId of assignTo) {
        const idStr = userId.toString();
        if (!approvers.find((u: { toString: () => string; }) => u.toString() === idStr)) {
          approvers.push(userId);
        }
      }
      if (org?.hikeSettings) {
        if (job.employmentType === EmploymentType.FullTime) {
          hikeSettings = org?.hikeSettings[job.employmentType];
        }
        if (job.employmentType === EmploymentType.Contract) {
          hikeSettings = org?.hikeSettings[job.employmentType];
        }
      }
      if (!hikeSettings) {
        const orgId = job.postingOrg;
        const org = await this.orgsModel.findById(orgId).lean();
        if (org?.hikeSettings) {
          if (job.employmentType === EmploymentType.FullTime) {
            hikeSettings = org?.hikeSettings[job.employmentType];
          }
          if (job.employmentType === EmploymentType.Contract) {
            hikeSettings = org?.hikeSettings[job.employmentType];
          }
        }
      }
    }

    if (job?.maxCtcOffered && createBgvDto.expectedCtc && hikeSettings) {
      const offeredCtc = createBgvDto.expectedCtc;
      const currentCtc = jobApp?.currentCTC;
      console.log(hikeSettings)

      const minMargin = hikeSettings.minMarginPercentage ?? 0;
      const maxHike = hikeSettings.maxHikePercentage ?? 0;

      const maxAllowedByMargin = Math.round(job.maxCtcOffered * (1 - minMargin / 100));
      const maxAllowedByHike = currentCtc ? Math.round(currentCtc * (1 + maxHike / 100)) : null;

      const violations: string[] = [];

      if (offeredCtc > maxAllowedByMargin) {
        violations.push(
          `Offered CTC (${offeredCtc}) exceeds max allowed by margin (${maxAllowedByMargin})`
        );
      }

      if (currentCtc && offeredCtc > maxAllowedByHike!) {
        violations.push(
          `Offered CTC (${offeredCtc}) exceeds max allowed hike over current CTC (${maxAllowedByHike})`
        );
      }

      if (violations.length > 0 && hikeSettings.requiresApproval) {

        const existingApproved = await this.hikeApprovalModel.findOne({
          applicationId: jobApp?._id.toString(),
          offerId: offer._id.toString(),
          status: 'APPROVED',
          modifiedCtc: { $gte: offeredCtc },
        }).lean();

        if (!existingApproved) {
          // ✅ Create approval record
          offer.status = OnboardingStatus.OFFER_APPROVAL_PENDING;

          await this.hikeApprovalModel.create({
            applicationId: jobApp?._id.toString(),
            jobId: job._id.toString(),
            offerId: offer._id.toString(),
            orgId: job.postingOrg,
            employmentType: job.employmentType,
            hikeSettings: hikeSettings,
            reason: violations.join('; '),
            maxAllowedByHike: maxAllowedByHike,
            maxAllowedByMargin: maxAllowedByMargin,
            approvers: approvers,
            approverRole: hikeSettings.approverRole,
            status: 'PENDING',
            createdAt: new Date(),
            createdBy: user?._id,
          });
        }
      }
    }

    offer.salaryPerAnnum = createBgvDto.expectedCtc ? createBgvDto.expectedCtc : offer.salaryPerAnnum;
    await offer.save();

    return await existingBgv.save();
  }

  async acceptOffer(applicationId: Types.ObjectId) {
    const bgv = await this.bgvModel.findOne({ jobApplication: applicationId.toString() });

    if (!bgv || !bgv.offerLetterDoc) {
      throw new NotFoundException('Offer letter not found to accept');
    }

    bgv.offerLetterDoc.isAccepted = true;
    bgv.offerLetterDoc.offerAcceptedAt = new Date();
    bgv.offerLetterDoc.isRejected = false;

    await bgv.save();

    const offer = await this.offerModel.findOne({ jobApplication: applicationId.toString() })
      .sort({ createdAt: -1 }) // 👉 Ensures the latest offer is returned
      .exec();
    if (!offer) {
      throw new NotFoundException('No offer found for the specified job application.');
    }
    offer.status = OnboardingStatus.OFFER_ACCEPTED; // Set status to CLIENT_BGV_APPROVAL_PENDING
    offer.stepperKey = 4;
    return await offer.save();
  }

  async revokeOffer(applicationId: Types.ObjectId) {
    const bgv = await this.bgvModel.findOne({ jobApplication: applicationId.toString() });

    if (!bgv || !bgv.offerLetterDoc) {
      throw new NotFoundException('Offer letter not found to revoke');
    }

    bgv.offerLetterDoc.isAccepted = false;
    bgv.offerLetterDoc.isRejected = true;
    bgv.offerLetterDoc.offerRejectedAt = new Date();

    await bgv.save();

    const offer = await this.offerModel.findOne({ jobApplication: applicationId.toString() })
      .sort({ createdAt: -1 }) // 👉 Ensures the latest offer is returned
      .exec();
    if (!offer) {
      throw new NotFoundException('No offer found for the specified job application.');
    }
    offer.status = OnboardingStatus.OFFER_REJECTED; // Set status to CLIENT_BGV_APPROVAL_PENDING
    // offer.stepperKey = 4;
    return await offer.save();
  }

  async onboardPermCandidateToEmployee(applicationId: Types.ObjectId, user: any) {
    const jobApp = await this.jobApplicationModel.findById(applicationId)
      .populate('jobId postingOrg')
      .exec();

    console.log("jobApp", jobApp)

    if (!jobApp) throw new NotFoundException('Job application not found');

    const bgv = await this.bgvModel.findOne({ jobApplication: applicationId.toString() }).exec();
    // if (!bgv || !bgv.offerLetterDoc?.isAccepted) {
    //   throw new BadRequestException('Offer not accepted yet');
    // }
    const offer = await this.offerModel.findOne({ jobApplication: applicationId.toString() })
      .sort({ createdAt: -1 }) // 👉 Ensures the latest offer is returned
      .exec();

    // Check if already converted
    const existingEmployee = await this.employeeModel.findOne({ jobApplication: applicationId.toString() });
    // ✅ Check for existing user and update role if needed
    const existingUser = await this.basicUserModel.findOne({ email: jobApp.contactDetails?.contactEmail });


    if (existingEmployee) {
      const existingUser = await this.basicUserModel.findOne({ email: jobApp.contactDetails?.contactEmail });

      if (
        existingUser &&
        Array.isArray(existingUser.roles) &&
        existingUser.roles.includes(Role.JobSeeker)
      ) {
        existingUser.roles = existingUser.roles.filter((r: string) => r !== Role.JobSeeker);

        if (!existingUser.roles.includes(Role.Employee)) {
          existingUser.roles.push(Role.Employee);
        }


        if (jobApp.jobId?.postingOrg) {
          existingUser.org = jobApp.jobId?.postingOrg;
        }




        await existingUser.save();

        this.emitEvent('user.promoted.to.employee', {
          ...existingUser.toObject(),
        });

      }


      return existingEmployee;
    }

    const newEmployee = new this.employeeModel({
      payRollOrg: user.org._id.toString(),
      endClientOrg: jobApp.jobId?.endClientOrg?.toString(),
      job: jobApp.jobId,
      jobTitle: jobApp.jobId?.title,
      jobApplication: jobApp._id.toString(),
      bgvId: bgv?._id?.toString(),
      firstName: jobApp.firstName,
      lastName: jobApp.lastName,
      email: jobApp.contactDetails?.contactEmail,
      contactNumber: jobApp.contactDetails?.contactNumber,
      employmentType: jobApp.jobId?.employmentType,
      // ctcFromClient: offer?.budgetConfirmation?.ctc,
      // hourlyRateFromClient: offer?.budgetConfirmation?.hourlyPay,
      ctcToCandidate: offer?.salaryPerAnnum,
      // effectiveStartDate: offer?.budgetConfirmation?.effectiveStartDate,
      createdBy: user._id.toString(),
    });

    const createdEmployee = await newEmployee.save();


    const generateStrongPassword = () => {
      const uppercase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
      const lowercase = "abcdefghijklmnopqrstuvwxyz";
      const numbers = "0123456789";
      const specialChars = "!@#$%^&*()_-+=<>?";
      const allChars = uppercase + lowercase + numbers + specialChars;

      let password = '';
      password += uppercase[Math.floor(Math.random() * uppercase.length)];
      password += numbers[Math.floor(Math.random() * numbers.length)];
      password += specialChars[Math.floor(Math.random() * specialChars.length)];

      for (let i = 3; i < 8; i++) {
        password += allChars[Math.floor(Math.random() * allChars.length)];
      }

      return password.split('').sort(() => 0.5 - Math.random()).join(''); // Shuffle the password
    };
    // Generate and hash the password
    const password = generateStrongPassword();
    const hashedPassword = await bcrypt.hash(password, 10);


    // const createMember = new this.basicUserModel({
    //   email: createdEmployee.email,
    //   firstName: createdEmployee.firstName,
    //   lastName: createdEmployee.lastName,
    //   contactNumber: createdEmployee.contactNumber,
    //   roles: [Role.Employee], // Assign the Employee role
    //   employeeId: createdEmployee._id?.toString(),
    //   // ...createMemberDto,
    //   org: createdEmployee.payRollOrg,
    //   password: hashedPassword,
    //   isVerified: true,
    // });

    // const createdMember = await createMember.save();
    // (offer as any).status = OnboardingStatus.EMPLOYEE_ONBOARDED;
    // await offer?.save();


    let createdMember = existingUser || null;

    if (!existingUser) {
      // Create new BasicUser
      const createMember = new this.basicUserModel({
        email: createdEmployee.email,
        firstName: createdEmployee.firstName,
        lastName: createdEmployee.lastName,
        contactNumber: createdEmployee.contactNumber,
        roles: [Role.Employee],
        employeeId: createdEmployee._id.toString(),
        org: createdEmployee.payRollOrg,
        password: hashedPassword,
        isVerified: true,
      });

      createdMember = await createMember.save();
      // 🔥 Emit only for newly created user
      this.emitEvent('user.verify.email.confirm.password', {
        ...createdMember.toObject(),
        password,
      });

    } else {
      // Update existing user to promote to employee
      if (!existingUser?.roles?.includes(Role.Employee)) {
        existingUser?.roles?.push(Role.Employee);
      }

      if (existingUser?.roles?.includes(Role.JobSeeker)) {
        existingUser.roles = existingUser?.roles?.filter(r => r !== Role.JobSeeker);
      }

      if (!existingUser.employeeId) {
        existingUser.employeeId = createdEmployee._id;
      }

      await existingUser.save();
    }

    // Update offer status
    if (offer) {
      (offer as any).status = OnboardingStatus.EMPLOYEE_ONBOARDED;
      await offer.save();
    }


    if (createdMember) {
      // this.emitEvent('user.verify.email.confirm.password', {
      //   ...createdMember.toObject(),
      //   password,
      // });

      return omit(createdMember.toObject(), ['password', '__v']);
    } else {
      throw new InternalServerErrorException('Failed to create or update user');
    }
    //   this.emitEvent('user.verify.email.confirm.password', { ...createMember.toObject(), password });
    // const sanitizedUser = omit(createdMember.toObject(), ['password', '__v']);
    // return sanitizedUser;

  }

  async convertCandidateToEmployee(applicationId: Types.ObjectId, user: any) {
    const jobApp = await this.jobApplicationModel.findById(applicationId)
      .populate('jobId')
      .exec();

    if (!jobApp) throw new NotFoundException('Job application not found');

    const bgv = await this.bgvModel.findOne({ jobApplication: applicationId.toString() }).exec();
    if (!bgv || !bgv.offerLetterDoc?.isAccepted) {
      throw new BadRequestException('Offer not accepted yet');
    }
    const offer = await this.offerModel.findOne({ jobApplication: applicationId.toString() })
      .sort({ createdAt: -1 }) // 👉 Ensures the latest offer is returned
      .exec();

    // Check if already converted
    const existingEmployee = await this.employeeModel.findOne({ jobApplication: applicationId });
    if (existingEmployee) return existingEmployee;

    const newEmployee = new this.employeeModel({
      payRollOrg: user.org._id.toString(),
      endClientOrg: jobApp.jobId?.endClientOrg?.toString(),
      job: jobApp.jobId,
      jobTitle: jobApp.jobId?.title,
      jobApplication: jobApp._id.toString(),
      bgvId: bgv._id?.toString(),
      firstName: jobApp.firstName,
      lastName: jobApp.lastName,
      email: jobApp.contactDetails?.contactEmail,
      contactNumber: jobApp.contactDetails?.contactNumber,
      employmentType: jobApp.jobId?.employmentType,
      ctcFromClient: offer?.budgetConfirmation?.ctc,
      hourlyRateFromClient: offer?.budgetConfirmation?.hourlyPay,
      ctcToCandidate: offer?.salaryPerAnnum,
      effectiveStartDate: offer?.budgetConfirmation?.effectiveStartDate,
      createdBy: user._id.toString(),
    });

    const createdEmployee = await newEmployee.save();

    const generateStrongPassword = () => {
      const uppercase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
      const lowercase = "abcdefghijklmnopqrstuvwxyz";
      const numbers = "0123456789";
      const specialChars = "!@#$%^&*()_-+=<>?";
      const allChars = uppercase + lowercase + numbers + specialChars;

      let password = '';
      password += uppercase[Math.floor(Math.random() * uppercase.length)];
      password += numbers[Math.floor(Math.random() * numbers.length)];
      password += specialChars[Math.floor(Math.random() * specialChars.length)];

      for (let i = 3; i < 8; i++) {
        password += allChars[Math.floor(Math.random() * allChars.length)];
      }

      return password.split('').sort(() => 0.5 - Math.random()).join(''); // Shuffle the password
    };
    // Generate and hash the password
    const password = generateStrongPassword();
    const hashedPassword = await bcrypt.hash(password, 10);


    const createMember = new this.basicUserModel({
      email: createdEmployee.email,
      firstName: createdEmployee.firstName,
      lastName: createdEmployee.lastName,
      contactNumber: createdEmployee.contactNumber,
      roles: [Role.Employee], // Assign the Employee role
      employeeId: createdEmployee._id?.toString(),
      // ...createMemberDto,
      org: createdEmployee.payRollOrg,
      password: hashedPassword,
      isVerified: true,
    });

    const createdMember = await createMember.save();

    this.emitEvent('user.verify.email.confirm.password', { ...createMember.toObject(), password });
    const sanitizedUser = omit(createdMember.toObject(), ['password', '__v']);
    return sanitizedUser;

  }

  async sendUploadLinksToCandidates(dto: SendUploadLinksDto) {
    console.log(dto.jobApplicationIds)
    const jobApplicationIds = dto.jobApplicationIds.map(id => new Types.ObjectId(id));
    console.log(jobApplicationIds)
    const jobApplications = await this.jobApplicationModel.find({
      _id: { $in: jobApplicationIds },
    }).populate('jobId'); // or createdBy depending on your schema

    console.log(jobApplications);
    for (const application of jobApplications) {
      const jobDetails = await this.jobModel.findById(application.jobId)
        .populate([
          {
            path: 'endClientOrg',
            model: 'Org',
            select: '_id title',
          },
          {
            path: 'postingOrg',
            model: 'Org',
            select: '_id title',
          },
        ])
        .exec();

      let bgvHandler: any;
      if (application.jobId?.jobType === JobType.Internal) {
        const departmentId = application?.jobId?.department;

        if (departmentId) {
          const department = await this.businessUnitModel.findById(departmentId).select('bgvHandlerId').exec();
          if (department?.bgvHandlerId) {
            bgvHandler = await this.bgvHandlerModel.findOne({
              _id: new Types.ObjectId(department.bgvHandlerId),
              isDeleted: false,
            }).exec();
          }
        }
        if (!bgvHandler) {
          bgvHandler = await this.bgvHandlerModel.findOne({
            org: application.jobId?.postingOrg,
            isDeleted: false,
            isDefault: true
          }).exec();
        }
        // if (!bgvHandler) {
        //   bgvHandler = await this.bgvHandlerModel.findOne({
        //     org: application.jobId?.postingOrg,
        //     isDeleted: false,
        //   }).exec();
        // }
      }
      else if (application.jobId?.jobType === JobType.External) {
        const endClientOrgId = application?.jobId?.endClientOrg;
        const endClientOrg = await this.orgsModel.findById(endClientOrgId).select('bgvHandlerId').exec();
        bgvHandler = await this.bgvHandlerModel.findOne({
          _id: new Types.ObjectId(endClientOrg?.bgvHandlerId),
          isDeleted: false,
        }).exec();
        if (!bgvHandler) {
          bgvHandler = await this.bgvHandlerModel.findOne({
            org: application.jobId?.postingOrg,
            isDeleted: false,
            isDefault: true
          }).exec();
        }
        // if (!bgvHandler) {
        //   bgvHandler = await this.bgvHandlerModel.findOne({
        //     org: application.jobId?.postingOrg,
        //     isDeleted: false,
        //   }).exec();
        // }
      }

      console.log('bgvHandler', bgvHandler)

      // You can either update if already exists or create new
      const createdBGV = await this.bgvModel.findOneAndUpdate(
        { jobApplication: application._id.toString() },
        {
          jobApplication: application._id.toString(),
        },
        { new: true, upsert: true, setDefaultsOnInsert: true }
      );

      const offer = await this.offerModel.findOne({ jobApplication: application._id.toString() })
        .sort({ createdAt: -1 }) // 👉 Ensures the latest offer is returned
        .exec();
      if (!offer) {
        throw new NotFoundException('No offer found for the specified job application.');
      }
      offer.status = OnboardingStatus.AWAITING_DOCUMENTS;
      (offer as any).bgvId = createdBGV._id;
      offer.bgvHandlerId = bgvHandler._id.toString();
      await offer.save();



      this.emitEvent('bgv.document.upload', {
        application: application,
        jobDetails: jobDetails,
        applicationId: application._id
      });


    }
    // ✅ Move this outside the loop
    return { message: 'Upload links sent to all valid candidates successfully.' };
  }

  async initiateBgv(applicationId: string, user: any) {
    const jobApplicationIds = [new Types.ObjectId(applicationId)];
    console.log(jobApplicationIds)
    const jobApplications = await this.jobApplicationModel.find({
      _id: { $in: jobApplicationIds },
    }).populate('jobId'); // or createdBy depending on your schema

    console.log(jobApplications);
    for (const application of jobApplications) {

      const offer = await this.offerModel.findOne({ jobApplication: application._id.toString() })
        .sort({ createdAt: -1 }) // 👉 Ensures the latest offer is returned
        .exec();
      if (!offer) {
        throw new NotFoundException('No offer found for the specified job application.');
      }

      if (offer.bgvHandlerId) {
        throw new HttpException(
          'A background verification (BGV) process has already been initiated for this candidate. Kindly proceed with the next steps.',
          HttpStatus.BAD_REQUEST,
        );
      }

      const jobDetails = await this.jobModel.findById(application.jobId)
        .populate([
          {
            path: 'endClientOrg',
            model: 'Org',
            select: '_id title',
          },
          {
            path: 'postingOrg',
            model: 'Org',
            select: '_id title',
          },
        ])
        .exec();

      let bgvHandler: any;
      if (application.jobId?.jobType === JobType.Internal) {
        const departmentId = application?.jobId?.department;

        if (departmentId) {
          const department = await this.businessUnitModel.findById(departmentId).select('bgvHandlerId').exec();
          if (department?.bgvHandlerId) {
            bgvHandler = await this.bgvHandlerModel.findOne({
              _id: new Types.ObjectId(department.bgvHandlerId),
              isDeleted: false,
            }).exec();
          }
        }
        if (!bgvHandler) {
          bgvHandler = await this.bgvHandlerModel.findOne({
            org: application.jobId?.postingOrg,
            isDeleted: false,
            isDefault: true
          }).exec();
        }
        // if (!bgvHandler) {
        //   bgvHandler = await this.bgvHandlerModel.findOne({
        //     org: application.jobId?.postingOrg,
        //     isDeleted: false,
        //   }).exec();
        // }
      }
      else if (application.jobId?.jobType === JobType.External) {
        const endClientOrgId = application?.jobId?.endClientOrg;
        const endClientOrg = await this.orgsModel.findById(endClientOrgId).select('bgvHandlerId').exec();
        bgvHandler = await this.bgvHandlerModel.findOne({
          _id: new Types.ObjectId(endClientOrg?.bgvHandlerId),
          isDeleted: false,
        }).exec();
        if (!bgvHandler) {
          bgvHandler = await this.bgvHandlerModel.findOne({
            org: application.jobId?.postingOrg,
            isDeleted: false,
            isDefault: true
          }).exec();
        }
        // if (!bgvHandler) {
        //   bgvHandler = await this.bgvHandlerModel.findOne({
        //     org: application.jobId?.postingOrg,
        //     isDeleted: false,
        //   }).exec();
        // }
      }

      console.log('bgvHandler', bgvHandler)

      if (!bgvHandler) {
        throw new HttpException(
          'No BGV handler has been assigned for the organization or department. Please configure a BGV handler before proceeding.',
          HttpStatus.BAD_REQUEST,
        );
      }
      // You can either update if already exists or create new
      const createdBGV = await this.bgvModel.findOneAndUpdate(
        { jobApplication: application._id.toString() },
        {
          jobApplication: application._id.toString(),
        },
        { new: true, upsert: true, setDefaultsOnInsert: true }
      );


      offer.status = OnboardingStatus.AWAITING_DOCUMENTS;
      (offer as any).bgvId = createdBGV._id;
      offer.bgvHandlerId = bgvHandler._id.toString();
      await offer.save();


      this.emitEvent('bgv.initaited', {
        application: application,
        jobDetails: jobDetails,
        bgvHandler: bgvHandler,
        applicationId: application._id
      });


    }
    // ✅ Move this outside the loop
    return { message: 'Upload links sent to all valid candidates successfully.' };
  }

  async findBgvDocumentsForCandidateByApplicationId(applicationId: Types.ObjectId) {
    try {
      const jobApp = await this.jobApplicationModel.findById(applicationId)
        .populate('jobId')
        .exec();
      console.log(jobApp)
      if (!jobApp) throw new NotFoundException('Job application not found');

      let bgvHandler: any;
      if (jobApp.jobId?.jobType === JobType.Internal) {
        const departmentId = jobApp?.jobId?.department;
        console.log(departmentId)
        if (departmentId) {
          const department = await this.businessUnitModel.findById(departmentId).select('bgvHandlerId').exec();
          if (department?.bgvHandlerId) {
            bgvHandler = await this.bgvHandlerModel.findOne({
              _id: new Types.ObjectId(department.bgvHandlerId),
              isDeleted: false,
            }).exec();
          }
        }
        if (!bgvHandler) {
          bgvHandler = await this.bgvHandlerModel.findOne({
            org: jobApp.jobId?.postingOrg,
            isDeleted: false,
            isDefault: true
          }).exec();
        }
      }
      else if (jobApp.jobId?.jobType === JobType.External) {
        const endClientOrgId = jobApp?.jobId?.endClientOrg;
        const endClientOrg = await this.orgsModel.findById(endClientOrgId).select('bgvHandlerId').exec();
        bgvHandler = await this.bgvHandlerModel.findOne({
          _id: new Types.ObjectId(endClientOrg?.bgvHandlerId),
          isDeleted: false,
        }).exec();
        if (!bgvHandler) {
          bgvHandler = await this.bgvHandlerModel.findOne({
            org: jobApp.jobId?.postingOrg,
            isDeleted: false,
            isDefault: true
          }).exec();
        }
      }

      console.log('bgvHandler', bgvHandler)
      const docs = bgvHandler?.documentKeys || [];

      if (docs.length < 1) {
        REQUIRED_BGV_DOCUMENTS.forEach(doc => {
          if (doc?.key && !docs.includes(doc.key)) {
            docs.push(doc.key);
          }
        });
      }

      return { org: jobApp.jobId.endClientName, name: `${jobApp.firstName} ${jobApp.lastName}`, docs };
    }
    catch (error) {
      this.logger.error(`An error occurred in fetching offer by Id ${applicationId}. ${error?.message}`);
      throw error;

    }
  }

  // async saveCandidateBgvDocuments(createBgvDto: CreateBgvDto) {
  //   const docs = Object.entries(createBgvDto.documents).map(([key, fileMetadataId]) => ({
  //     key,
  //     fileMetadataId,
  //   }));

  //   const offer = await this.offerModel.findOne({ jobApplication: createBgvDto.jobApplication.toString() })
  //     .sort({ createdAt: -1 }) // 👉 Ensures the latest offer is returned
  //     .exec();

  //   // You can either update if already exists or create new
  //   const createdBGV = await this.bgvModel.findOneAndUpdate(
  //     { jobApplication: createBgvDto.jobApplication },
  //     {
  //       jobApplication: createBgvDto.jobApplication,
  //       documents: docs,
  //     },
  //     { new: true, upsert: true, setDefaultsOnInsert: true }
  //   );


  //   // Step 3: Update the offer with the bgvId
  //   if (!offer) {
  //     throw new NotFoundException('No offer found for the specified job application.');
  //   }
  //   (offer as any).bgvId = createdBGV._id;
  //   offer.status = OnboardingStatus.BGV_MAIL_SENT;
  //   await offer.save();

  //   return createdBGV;
  // }

  async saveCandidateBgvDocuments(createBgvDto: CreateBgvDto) {
    const bgv = await this.bgvModel.findOne({
      jobApplication: createBgvDto.jobApplication,
    });

    const updatedDocs = Object.entries(createBgvDto.documents);

    if (!bgv) {
      // First-time upload
      const docs = updatedDocs.map(([key, fileIds]) => ({
        key,
        versions: fileIds.map((fileId) => ({
          fileId,
          status: 'PENDING',
        })),
      }));
      return this.bgvModel.create({
        jobApplication: createBgvDto.jobApplication,
        documents: docs,
      });
    }

    // Append to versions (maintaining history)
    for (const [key, fileIds] of updatedDocs) {
      let docEntry = bgv.documents.find((doc) => doc.key === key);
      if (!docEntry) {
        // New document type
        bgv.documents.push({
          key,
          versions: fileIds.map((fileId) => ({
            fileId: new Types.ObjectId(fileId),
            status: 'PENDING',
            uploadedAt: new Date(), // ✅ Add this line
          })),
        });
      } else {
        // Append new versions
        for (const fileId of fileIds) {
          docEntry.versions.push({
            fileId: new Types.ObjectId(fileId),
            status: 'PENDING',
            uploadedAt: new Date(), // ✅ Add this line
          });
        }
      }
    }

    await bgv.save();
    return bgv;
  }


  async submitCandidateBgvDocuments(applicationId: Types.ObjectId) {

    const offer = await this.offerModel.findOne({ jobApplication: applicationId.toString() })
      .sort({ createdAt: -1 }) // 👉 Ensures the latest offer is returned
      .exec();
    // Step 3: Update the offer with the bgvId
    if (!offer) {
      throw new NotFoundException('No offer found for the specified job application.');
    }
    offer.status = OnboardingStatus.BGV_MAIL_SENT; // Set status to AWAITING_BGV
    // offer.stepperKey = 2;
    await offer.save();

    return offer;
  }

  async findBgvDocumentsByCandidateId(applicationId: Types.ObjectId) {
    try {
      const offer = await this.offerModel.findOne({ jobApplication: applicationId.toString() })
        .sort({ createdAt: -1 }) // 👉 Ensures the latest offer is returned
        .exec();
      // Step 3: Update the offer with the bgvId
      if (!offer) {
        throw new NotFoundException('No offer found for the specified job application.');
      }

      let isDocumentsSubmitted: boolean = true;
      if (offer.status === OnboardingStatus.AWAITING_DOCUMENTS) {
        isDocumentsSubmitted = false;
      }

      const existingBgv = await this.bgvModel.findOne({ jobApplication: applicationId.toString() })
        .populate({
          path: 'documents.fileMetadataId',
          model: 'FileMetadata',
          select: 'originalName uniqueName fileSize fileType uploadedBy createdAt locationUrl',
        })
        .populate({
          path: 'interimBgvDocs.fileMetadataId',
          model: 'FileMetadata',
          select: 'originalName uniqueName fileSize fileType uploadedBy createdAt locationUrl',
        }).exec();
      console.log("existingBgv", existingBgv);

      let responsePayload: any = null;

      if (existingBgv) {
        responsePayload = existingBgv.toObject(); // make plain JS object
        responsePayload.isDocumentsSubmitted = isDocumentsSubmitted;
      }

      return { existingBgv: responsePayload };
    }
    catch (error) {
      this.logger.error(`An error occurred in fetching offer by Id ${applicationId}. ${error?.message}`);
      throw error;

    }
  }

  // async findBgvHandlerForCandidateByApplicationId(applicationId: Types.ObjectId) {
  //   try {
  //     const jobApp = await this.jobApplicationModel.findById(applicationId)
  //       .populate('jobId')
  //       .exec();


  //     if (!jobApp) throw new NotFoundException('Job application not found');

  //     const offer = await this.offerModel.findOne({ jobApplication: applicationId.toString() })
  //       .sort({ createdAt: -1 }) // 👉 Ensures the latest offer is returned
  //       .exec();

  //     let bgvHandler: any;
  //     let isBgvRaised: boolean = false;
  //     let isBgvHandlerExists: boolean = false;

  //     if (offer?.bgvHandlerId) {
  //       bgvHandler = await this.bgvHandlerModel.findOne({
  //         _id: new Types.ObjectId(offer?.bgvHandlerId),
  //         isDeleted: false,
  //       }).exec();

  //       isBgvRaised = true;
  //     } else {
  //       if (jobApp.jobId?.jobType === JobType.Internal) {
  //         const departmentId = jobApp?.jobId?.department;

  //         if (departmentId) {
  //           const department = await this.businessUnitModel.findById(departmentId).select('bgvHandlerId').exec();
  //           if (department?.bgvHandlerId) {
  //             bgvHandler = await this.bgvHandlerModel.findOne({
  //               _id: new Types.ObjectId(department.bgvHandlerId),
  //               isDeleted: false,
  //             }).exec();
  //           }
  //         }
  //         if (!bgvHandler) {
  //           bgvHandler = await this.bgvHandlerModel.findOne({
  //             org: jobApp.jobId?.postingOrg,
  //             isDeleted: false,
  //             isDefault: true
  //           }).exec();
  //         }
  //       }
  //       else if (jobApp.jobId?.jobType === JobType.External) {
  //         const endClientOrgId = jobApp?.jobId?.endClientOrg;
  //         const endClientOrg = await this.orgsModel.findById(endClientOrgId).select('bgvHandlerId').exec();
  //         bgvHandler = await this.bgvHandlerModel.findOne({
  //           _id: new Types.ObjectId(endClientOrg?.bgvHandlerId),
  //           isDeleted: false,
  //         }).exec();
  //         if (!bgvHandler) {
  //           bgvHandler = await this.bgvHandlerModel.findOne({
  //             org: jobApp.jobId?.postingOrg,
  //             isDeleted: false,
  //             isDefault: true
  //           }).exec();
  //         }
  //       }
  //     }
  //     console.log('bgvHandler', bgvHandler)


  //     const handlerDocs: string[] = bgvHandler?.documentKeys?.length
  //       ? bgvHandler.documentKeys
  //       : REQUIRED_BGV_DOCUMENTS.map(doc => doc.key).filter(Boolean);

  //     const existingBgv = await this.bgvModel.findOne({ jobApplication: applicationId.toString() })
  //       .populate({
  //         path: 'documents.fileMetadataId',
  //         model: 'FileMetadata',
  //         select: 'originalName uniqueName fileSize fileType uploadedBy createdAt locationUrl',
  //       })
  //       .populate({
  //         path: 'interimBgvDocs.fileMetadataId',
  //         model: 'FileMetadata',
  //         select: 'originalName uniqueName fileSize fileType uploadedBy createdAt locationUrl',
  //       }).exec();
  //     console.log("existingBgv", existingBgv);

  //     const uploadedDocs = existingBgv?.documents || [];

  //     // Step 1: All keys from handler
  //     const handlerKeys = new Set(handlerDocs);

  //     // Step 2: Add custom keys from existingBgv
  //     uploadedDocs.forEach(doc => {
  //       if (doc?.key && !handlerKeys.has(doc.key)) {
  //         handlerKeys.add(doc.key);
  //       }
  //     });

  //     // Step 3: Build final merged document status
  //     const documentStatus = Array.from(handlerKeys).map(key => {
  //       const uploaded = uploadedDocs.find(doc => doc.key === key);
  //       return {
  //         key,
  //         uploaded: !!uploaded,
  //         fileMetadata: uploaded?.fileMetadataId || null
  //       };
  //     });
  //     if (bgvHandler) {
  //       isBgvHandlerExists = true
  //     }

  //     return {
  //       candidateName: `${jobApp.firstName} ${jobApp.lastName}`,
  //       org: jobApp.jobId?.endClientName,
  //       documents: documentStatus,
  //       bgvHandler,
  //       isBgvHandlerExists,
  //       isBgvRaised
  //     };
  //   }
  //   catch (error) {
  //     this.logger.error(`An error occurred in fetching offer by Id ${applicationId}. ${error?.message}`);
  //     throw error;

  //   }
  // }

  async getCandidateBgvStatus(applicationId: string) {
    // const requiredKeys = ['pan_card', 'aadhar_card', 'qualification_certificates', 'experience_letters']; // customize as needed

    const jobApp = await this.jobApplicationModel.findById(applicationId)
      .populate('jobId')
      .exec();


    if (!jobApp) throw new NotFoundException('Job application not found');

    const offer = await this.offerModel.findOne({ jobApplication: applicationId.toString() })
      .sort({ createdAt: -1 }) // 👉 Ensures the latest offer is returned
      .exec();

    let bgvHandler: any;
    let isBgvRaised: boolean = false;
    let isBgvHandlerExists: boolean = false;

    // if (offer?.bgvHandlerId) {
    //   bgvHandler = await this.bgvHandlerModel.findOne({
    //     _id: new Types.ObjectId(offer?.bgvHandlerId),
    //     isDeleted: false,
    //   }).exec();

    //   isBgvRaised = true;
    // } else {
    if (jobApp.jobId?.jobType === JobType.Internal) {
      const departmentId = jobApp?.jobId?.department;

      if (departmentId) {
        const department = await this.businessUnitModel.findById(departmentId).select('bgvHandlerId').exec();
        if (department?.bgvHandlerId) {
          bgvHandler = await this.bgvHandlerModel.findOne({
            _id: new Types.ObjectId(department.bgvHandlerId),
            isDeleted: false,
          }).exec();
        }
      }
      if (!bgvHandler) {
        bgvHandler = await this.bgvHandlerModel.findOne({
          org: jobApp.jobId?.postingOrg,
          isDeleted: false,
          isDefault: true
        }).exec();
      }
    }
    else if (jobApp.jobId?.jobType === JobType.External) {
      const endClientOrgId = jobApp?.jobId?.endClientOrg;
      const endClientOrg = await this.orgsModel.findById(endClientOrgId).select('bgvHandlerId').exec();
      bgvHandler = await this.bgvHandlerModel.findOne({
        _id: new Types.ObjectId(endClientOrg?.bgvHandlerId),
        isDeleted: false,
      }).exec();
      if (!bgvHandler) {
        bgvHandler = await this.bgvHandlerModel.findOne({
          org: jobApp.jobId?.postingOrg,
          isDeleted: false,
          isDefault: true
        }).exec();
      }
    }
    // }
    console.log('bgvHandler', bgvHandler)


    const handlerDocs: string[] = bgvHandler?.documentKeys?.length
      ? bgvHandler.documentKeys
      : REQUIRED_BGV_DOCUMENTS.map(doc => doc.key).filter(Boolean);

    const bgv = await this.bgvModel.findOne({ jobApplication: applicationId }).lean().exec();

    const result = [];

    const allFileIds = new Set<string>();

    bgv?.documents?.forEach((doc) => {
      doc.versions.forEach((version) => {
        allFileIds.add(version.fileId.toString());
      });
    });

    const fileMetadataList = await this.fileMetadataModel
      .find({ _id: { $in: Array.from(allFileIds) } })
      .select('originalName uniqueName fileSize fileType uploadedBy createdAt locationUrl')
      .exec();

    const fileMap = new Map(fileMetadataList.map((f) => [f._id.toString(), f]));

    for (const key of handlerDocs) {
      const doc = bgv?.documents?.find((d) => d.key === key);
      const versions = doc?.versions || [];

      const enrichedVersions = versions.map((v) => ({
        ...v,
        file: fileMap.get(v.fileId.toString()), // ✅ populated info
      }));

      if (enrichedVersions.length === 0) {
        result.push({
          key,
          status: 'NOT_UPLOADED',
          action: 'UPLOAD',
          versions: [],
        });
        continue;
      }

      const latest = enrichedVersions[enrichedVersions.length - 1];
      const status = latest.status;

      // All previous versions (excluding latest)
      const previousVersions = enrichedVersions
        .slice(0, -1)
        .map((v) => ({
          ...v,
          file: fileMap.get(v.fileId.toString()),
        }));
      if (offer?.bgvHandlerId) {
        isBgvRaised = true;
      }
      result.push({
        key,
        status,
        action: status === 'REJECTED' ? 'REUPLOAD' : 'VIEW',
        latestVersion: latest,
        versions: previousVersions,
        isBgvRaised
      });
    }


    return result;
  }


  async approveCandidateInterimBgv(applicationId: Types.ObjectId, payload: ApproveRejectInterimBgvDto) {

    const offer = await this.offerModel.findOne({ jobApplication: applicationId.toString() })
      .sort({ createdAt: -1 }) // 👉 Ensures the latest offer is returned
      .exec();
    // Step 3: Update the offer with the bgvId
    if (!offer) {
      throw new NotFoundException('No offer found for the specified job application.');
    }

    const existingBgv = await this.bgvModel.findOne({ jobApplication: applicationId.toString() }).exec();
    if (!existingBgv) {
      throw new NotFoundException('No BGV found for the specified job application.');
    }

    if (payload.isApproved) {
      existingBgv.status = OnboardingStatus.BGV_APPROVED;
      offer.status = OnboardingStatus.BGV_APPROVED;
      existingBgv.rejectionReason = undefined; // Clear previous reason if any
    } else {
      existingBgv.status = OnboardingStatus.BGV_REJECTED;
      existingBgv.rejectionReason = payload.rejectionReason;
    }


    offer.status = OnboardingStatus.AWAITING_BGV; // Set status to AWAITING_BGV
    // offer.stepperKey = 2;
    await offer.save();

    return offer;
  }

  async approveCandidateDocuments(user: any, applicationId: Types.ObjectId, payload: ReviewDocDto) {

    const offer = await this.offerModel.findOne({ jobApplication: applicationId.toString() })
      .sort({ createdAt: -1 }) // 👉 Ensures the latest offer is returned
      .exec();
    // Step 3: Update the offer with the bgvId
    if (!offer) {
      throw new NotFoundException('No offer found for the specified job application.');
    }

    const existingBgv = await this.bgvModel.findOne({ jobApplication: applicationId.toString() }).exec();
    if (!existingBgv) {
      throw new NotFoundException('No BGV found for the specified job application.');
    }

    const jobApp = await this.jobApplicationModel.findById(applicationId)
      .populate('jobId')
      .exec();
    let found = false;

    for (const doc of existingBgv.documents) {
      for (const version of doc.versions) {
        if (version.fileId.toString() === payload.fileId) {
          version.status = payload.status as 'APPROVED' | 'REJECTED';

          if (payload.status === 'APPROVED') {
            version.approvedAt = new Date();
            version.approvedBy = user._id;
            version.rejectedAt = undefined;
            version.rejectedBy = undefined;
            version.rejectionReason = undefined;
          } else {
            version.rejectedAt = new Date();
            version.rejectedBy = user._id;
            version.rejectionReason = payload.rejectionReason || 'No reason provided';
            version.approvedAt = undefined;
            version.approvedBy = undefined;

            const updatedFile = await this.fileMetadataModel.findById(version.fileId).lean().exec();
            this.eventEmitter.emit('candidate.file.status', {
              updatedFile,
              status: 'rejected',
              key: doc.key,
              jobApp: jobApp,
            });
          }
          found = true;
          break;
        }
      }
    }

    if (!found) throw new NotFoundException('File version not found');
    await existingBgv.save();
    return { message: `Document ${payload.status?.toLowerCase()} successfully.` };
  }

  async sendDocumentReminderToCandidate(applicationId: string) {
    const documentStatusList = await this.getCandidateBgvStatus(applicationId);
    const jobApp = await this.jobApplicationModel.findById(applicationId)
      .populate('jobId')
      .exec();

    if (!jobApp) throw new NotFoundException('Job application not found');

    const pendingDocs = documentStatusList.filter(
      (doc) => doc.status === 'NOT_UPLOADED' || doc.status === 'REJECTED'
    );

    if (!pendingDocs.length) {
      this.logger.log(`No pending documents for application ${applicationId}`);
      return;
    }

    function formatKeyLabel(key: string): string {
      return key
        .split('_')
        .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');
    }

    // Build document status message
    const docListHtml = pendingDocs
      .map((doc) => {
        const key = formatKeyLabel(doc.key);
        let line = `• <b>${key}</b> – ${doc.status.replace('_', ' ')}`;

        if (doc.status === 'REJECTED' && doc.latestVersion?.rejectionReason) {
          line += ` <span style="color: red;">(Reason: ${doc.latestVersion.rejectionReason})</span>`;
        }

        return line;
      })
      .join('<br>');

    this.eventEmitter.emit('candidate.file.remainder', {
      docNames: docListHtml,
      jobApp: jobApp,
    });

    this.logger.log(`Bulk reminder emitted for application ${applicationId}`);
  }


  async getCandidateDetails(user: any) {

    const existingUser = await this.basicUserModel.findById(user?._id).exec();
    const jobApp = await this.jobApplicationModel.find({ 'contactDetails.contactEmail': existingUser?.email }).populate('jobId').exec();

    const jobAppIds = jobApp.map(app => app._id.toString());

    // 1. Get latest offer per jobApplication
    const offers = await this.offerModel.aggregate([
      { $match: { jobApplication: { $in: jobAppIds } } },
      { $sort: { createdAt: -1 } },
      {
        $group: {
          _id: '$jobApplication',
          latestOffer: { $first: '$$ROOT' }
        }
      }
    ]);

    const offerMap = new Map(offers.map(o => [o._id.toString(), o.latestOffer]));


    // 2. Fetch all BGVs for these applications
    const bgvs = await this.bgvModel.find({
      jobApplication: { $in: jobAppIds }
    }).exec();

    const bgvMap = new Map(bgvs.map(b => [b.jobApplication.toString(), b]));

    // Step 4: Combine only applications that have BOTH offer and BGV
    // Step 4: Return only jobApps that have an offer; attach BGV if available
    const result = jobApp
      .filter(app => offerMap.has(app._id.toString())) // ✅ Only apps with an offer
      .map(app => {
        const id = app._id.toString();
        return {
          ...app.toObject(),
          latestOffer: offerMap.get(id),
          bgv: bgvMap.get(id) || null, // ✅ May be null
        };
      });

    return result;
  }

  async reviewHikeApproval(user: any, dto: ReviewHikeApprovalDto) {
    const approval = await this.hikeApprovalModel.findById(dto.approvalId);

    if (!approval) throw new NotFoundException('Approval record not found');
    if (approval.status !== 'PENDING') throw new BadRequestException('Approval already reviewed');

    approval.status = dto.status;
    approval.reviewedBy = user._id;
    approval.reviewedAt = new Date();
    approval.reviewComment = dto.reviewComment;

    const offer = await this.offerModel.findOne({ jobApplication: approval.applicationId.toString() })
      .sort({ createdAt: -1 }) // 👉 Ensures the latest offer is returned
      .exec();

    if (dto.status === 'APPROVED') {
      if (dto.modifiedCtc) {
        approval.modifiedCtc = dto.modifiedCtc;
        (offer as any).salaryPerAnnum = dto.modifiedCtc;
        (offer as any).isApproved = true;
        (offer as any).status = OnboardingStatus.OFFER_APPROVED;
      }
    }
    await offer?.save();
    await approval.save();

    return { message: `Approval ${dto.status.toLowerCase()} successfully.` };
  }

  async getPendingOfferApprovals(user: any, query: OffersQueryDTO) {
    const isHrOrAdmin = user.roles?.includes(Role.Admin) || user.roles?.includes(Role.HR);
    const filter: any = { status: { $in: ['PENDING'] }, orgId: user.org._id };
    if (!isHrOrAdmin) {
      filter.approvers = user._id.toString(); // Mongoose will match if user._id exists in the array
    }
    const approvals = await this.hikeApprovalModel
      .find(filter)
      .populate([
        {
          path: 'jobId',
          populate: {
            path: 'endClientOrg',
            model: 'Org',
            select: '_id title'
          },
          model: 'Job',
          select: '_id title endClientOrg employmentType requiredBgv maxCtcOffered'
        },
        {
          path: 'applicationId',
          populate: [
            {
              path: 'createdBy',
              model: 'BasicUser',
              select: '_id firstName lastName roles',
            },
            { path: 'country', model: 'Country' },
            { path: 'state', model: 'State' },
            { path: 'city', model: 'City' },
          ],
        },
        { path: 'offerId' },
        {
          path: 'createdBy',
          model: 'BasicUser',
          select: '_id firstName lastName email roles'
        },
      ])
      .sort({ createdAt: -1 })
      .exec();

    const {
      endClientOrg,
      employmentType,
      name,
      fromDate,
      toDate,
      page = 1, limit = 10,

    } = query;


    const filteredApprovals = approvals.filter((approval: any) => {

      const job = approval.jobId;
      const candidate = approval.applicationId;
      const createdAt = new Date(approval.createdAt);

      // endClientOrg filter
      if (endClientOrg && job?.endClientOrg?._id?.toString() !== endClientOrg) return false;

      // employmentType filter
      if (employmentType && job?.employmentType !== employmentType) return false;

      // fromDate filter
      if (fromDate && createdAt < new Date(fromDate)) return false;

      // toDate filter
      if (toDate && createdAt > new Date(toDate)) return false;

      // name filter (on candidate full name and job title)
      if (name) {
        const regex = new RegExp(name, 'i');
        const candidateFullName = `${candidate?.firstName ?? ''} ${candidate?.lastName ?? ''}`;
        const jobTitle = job?.title ?? '';
        const email = candidate?.email ?? '';
        const searchTarget = [candidateFullName, jobTitle, email].join(' ');
        if (!regex.test(searchTarget)) return false;
      }
      return true;


    });

    // Step 2: Pagination
    const total = filteredApprovals.length;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedData = filteredApprovals.slice(startIndex, endIndex);

    // Step 3: Return paginated result
    return {
      data: paginatedData,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };

    // console.log("final filtered", filteredApprovals)

    // return filteredApprovals




  }

  async getApprovalsByOffer(applicationId: string) {
    const offer = await this.offerModel.findOne({ jobApplication: applicationId.toString() })
      .sort({ createdAt: -1 }) // 👉 Ensures the latest offer is returned
      .exec();
    return this.hikeApprovalModel
      .find({ applicationId: applicationId, offerId: offer?._id.toString() })
      .populate([
        {
          path: 'jobId',
          populate: {
            path: 'endClientOrg',
            model: 'Org',
            select: '_id title'
          },
          model: 'Job',
          select: '_id title endClientOrg employmentType requiredBgv maxCtcOffered'
        },
        {
          path: 'applicationId',
          populate: [
            {
              path: 'createdBy',
              model: 'BasicUser',
              select: '_id firstName lastName roles',
            },
            { path: 'country', model: 'Country' },
            { path: 'state', model: 'State' },
            { path: 'city', model: 'City' },
          ],
        },
        { path: 'offerId' },
        {
          path: 'createdBy',
          model: 'BasicUser',
          select: '_id firstName lastName email roles'
        },
        {
          path: 'reviewedBy',
          model: 'BasicUser',
          select: '_id firstName lastName email roles'
        },
      ])
      .sort({ createdAt: -1 })
      .exec();
  }

  async getApprovalById(approvalId: Types.ObjectId) {
    return this.hikeApprovalModel
      .findById(approvalId)
      .populate([
        {
          path: 'jobId',
          populate: {
            path: 'endClientOrg',
            model: 'Org',
            select: '_id title'
          },
          model: 'Job',
          select: '_id title endClientOrg employmentType requiredBgv maxCtcOffered'
        },
        {
          path: 'applicationId',
          populate: [
            {
              path: 'createdBy',
              model: 'BasicUser',
              select: '_id firstName lastName roles',
            },
            { path: 'country', model: 'Country' },
            { path: 'state', model: 'State' },
            { path: 'city', model: 'City' },
          ],
        },
        { path: 'offerId' },
        {
          path: 'createdBy',
          model: 'BasicUser',
          select: '_id firstName lastName email roles'
        },
      ])
      .exec();
  }


}
