import { BadRequestException, HttpException, HttpStatus, Injectable, InternalServerErrorException, Logger, NotFoundException, ServiceUnavailableException } from '@nestjs/common';
import { CreateContactDto } from './dto/create-contact.dto';
import { UpdateContactDto } from './dto/update-contact.dto';
import { ConfigService } from '@nestjs/config';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Contact, ContactDocument } from 'src/contact/schemas/contact.schema';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { CommentDto } from 'src/common/dto/comment.dto';
import { BasicUser } from 'src/user/schemas/basic-user.schema';
import { validateObjectId } from 'src/utils/validation.utils';
import { QueryContactDto } from './dto/query-contact.dto';
import { Comment, CommentDocument } from 'src/common/schemas/comment.schema';
import { ChangeStatusContactDto } from 'src/common/dto/contact-change-status.dto';
import { QueryContactCountDto } from './dto/query-contact-count.dto';
import { Role } from 'src/auth/enums/role.enum';

@Injectable()
export class ContactService {


  private readonly logger = new Logger(ContactService.name);

  constructor(private configService: ConfigService, @InjectModel(Contact.name) private contactModel: Model<Contact>, private eventEmitter: EventEmitter2,
    @InjectModel(Comment.name) private commentModel: Model<Comment>,
    @InjectModel(BasicUser.name) private basicUserModel: Model<BasicUser>,

  ) { }

  // this.eventEmitter.emit('contact.CloneOrgDynamicFieldsForContact', {
  //     contactId: createdContact._id.toString(),
  //     accountOrgId: createdContact.accountOrg?.toString(),
  //     createdOrgId: createdContact.createdByOrg?.toString(),
  //     user,
  //   });

  //   console.log('[EMIT] contact.CloneOrgDynamicFieldsForContact', {
  //     contactId: createdContact._id.toString(),
  //     accountOrgId: createdContact.accountOrg?.toString(),
  //     createdOrgId: createdContact.createdByOrg?.toString(),
  //   });


  async create(createContactDto: CreateContactDto, user: any) {

    createContactDto.createdBy = user._id;
    createContactDto.createdByOrg = user.org._id.toString();
    const { accountOrg, industry, reportingTo } = createContactDto;

    if (user?.org) {
      createContactDto.salesRepOrg = user.org;
    }

    // if (!createContactDto.assignTo)
    //   createContactDto.assignTo = user._id;
    // else {
    //   validateObjectId(createContactDto.assignTo);
    // }

    if (accountOrg) { validateObjectId(accountOrg); }
    if (industry) { validateObjectId(industry); }
    if (reportingTo) { validateObjectId(reportingTo); }

    createContactDto.industry = industry || undefined;
    createContactDto.accountOrg = accountOrg || undefined;
    createContactDto.reportingTo = reportingTo || undefined;

    try {
      const createContact = new this.contactModel(createContactDto);
      // this.logger.log(createContact);
      const createdContact = await createContact.save();
       this.emitEvent('contact.created', { createdContact, user });

       console.log("created user", createdContact)
      // Emit dynamic field cloning event (ORG → Contact)
      this.eventEmitter.emit('contact.CloneJobFieldsForContact', {
        contact: createdContact._id.toString(),
        accountOrg: createdContact.accountOrg?.toString(),
        createdOrg: createdContact.createdByOrg?.toString(),
      
      });

      console.log("[EMIT] contact.CloneJobFieldsForContact", {
        contact: createdContact._id.toString(),
        accountOrg: createdContact.accountOrg?.toString(),
        createdOrg: createdContact.createdByOrg?.toString(),
      });

     
      const createdBy = await this.basicUserModel.findById(createdContact.createdBy).exec();
      if (createdContact.assignTo && Array.isArray(createdContact.assignTo)) {
        const assignToUsers = await this.basicUserModel.find({ _id: { $in: createdContact.assignTo } }).exec();
        if (!assignToUsers || assignToUsers.length === 0) {
          this.logger.log(`No users found for the provided assignTo IDs.`);
        } else {
          const assignToEmails = assignToUsers.map(user => {
            this.emitEvent('contact.assignTo', {
              title: createdContact.firstName,
              email: user.email,
              assignTo: user?.firstName,
              createdBy: createdBy?.firstName
            });
            return user.email;
          });
          this.logger.log('Assign To Emails:', assignToEmails);
        }
      }

      return createdContact;
    } catch (error) {
      throw new InternalServerErrorException(`Error while creating contact: ${error?.message}`);
    }
  }

  async getOnlyActiveContacts(query: QueryContactDto, userId: Types.ObjectId, assignToUser: string, orgId: string): Promise<ContactDocument[]> {
    const populateOptions = this.getPopulateOptions();

    try {
      const existingUser = await this.basicUserModel.findById(userId).exec();
      const { name, industryId, accountId, designation, referredBy, page, limit, isInternal, assignTo, isDeleted, vendorId, contactId } = query;
      // Validate page and limit to be positive integers
      if (page <= 0 || limit <= 0) {
        throw new BadRequestException('Page and limit must be positive integers.');
      }
      let conditions: any = { isDeleted: false };

      if (name) {
        const regex = new RegExp(name, 'i'); // 'i' for case-insensitive search
        // conditions.firstName = regex;
        conditions.$or = [
          { firstName: regex },         // Match first name
          { lastName: regex },          // Match last name
          {
            $expr: {
              $regexMatch: {
                input: { $concat: ["$firstName", " ", "$lastName"] },
                regex: name,
                options: "i"
              }
            }
          }  // Match full name dynamically
        ];
      }

      if (accountId) {
        conditions.accountOrg = accountId;
      }

      if (industryId) {
        conditions.industry = industryId;
      }

      if (referredBy) {
        conditions.referredBy = referredBy;
      }

      if (designation) {
        conditions.designation = { $regex: designation, $options: 'i' };
      }

      if (isInternal !== undefined) {
        conditions.isInternal = isInternal;
      }
      if (assignTo) {
        conditions.assignTo = { $in: [assignTo] };
      }
      if (assignToUser && existingUser?.roles?.includes(Role.AccountManager)) {
        // conditions.assignTo = { $in: [assignToUser] };
        conditions.$or = [
          { createdBy: { $in: [userId] } },
          { assignTo: { $in: [assignToUser] } }
        ];
      }
      else {
        conditions.createdByOrg = orgId;
      }
      if (isDeleted !== undefined) {
        conditions.isDeleted = isDeleted;
      }

      if (vendorId) {
        conditions.vendor = vendorId;
      }

      // console.log("conditions", conditions);
      const contacts = await this.contactModel.find(conditions)
        .sort({ updatedAt: -1 })
        .populate(populateOptions)
        .skip((page - 1) * limit)
        .limit(limit)
        .exec();

      if (contactId) {
        const contact = await this.contactModel.findById(contactId).exec();

        if (contact?.reportingTo) {
          const reportingToContact = await this.contactModel.findById(contact.reportingTo)
            .populate(populateOptions)
            .exec();

          if (reportingToContact) {
            contacts.push(reportingToContact);
          }
        }
      }

      return contacts;
    } catch (error) {
      // Handling specific Mongoose validation errors
      if (error.name === 'ValidationError') {
        this.logger.error(`Validation Error: ${error.message}`);
        throw new BadRequestException(`Validation Error: ${error.message}`);
      }

      // Handling errors related to querying database (like syntax or structure issues)
      if (error.name === 'MongoError' && error.code === 2) {
        this.logger.error(`Query Error: ${error.message}`);
        throw new BadRequestException(`Invalid query structure: ${error.message}`);
      }

      // Handling MongoDB connection issues
      if (error.name === 'MongoNetworkError') {
        this.logger.error(`Database Connection Error: ${error.message}`);
        throw new ServiceUnavailableException(`Database connection error: ${error.message}`);
      }

      // Handling timeout errors
      if (error.name === 'MongooseTimeoutError') {
        this.logger.error(`Query Timeout: ${error.message}`);
        throw new ServiceUnavailableException(`Request timed out: ${error.message}`);
      }

      // Generic fallback error handling
      this.logger.error(error);
      this.logger.error(error?.message);
      throw new InternalServerErrorException(`Error while fetching active contacts: ${error?.message}`);
    }
  }



  async getOnlySoftDeletedContacts(page: number, limit: number): Promise<ContactDocument[]> {
    const populateOptions = this.getPopulateOptions();
    try {
      return await this.contactModel.find({ isDeleted: true })
        .sort({ updatedAt: -1 })
        .populate(populateOptions)
        .skip((page - 1) * limit)
        .limit(limit)
        .exec();
    } catch (error) {
      throw new InternalServerErrorException(`Error while fetching only soft deleted contacts: ${error?.message}`);
    }
  }

  // if admin wants to see list of records include deleting records we need one more function
  async getAllContacts(page: number, limit: number): Promise<ContactDocument[]> {
    const populateOptions = this.getPopulateOptions();
    try {
      return await this.contactModel.find()
        .sort({ updatedAt: -1 })
        .populate(populateOptions)
        .skip((page - 1) * limit)
        .limit(limit)
        .sort({ updatedAt: -1 })
        .exec();
    } catch (error) {
      throw new InternalServerErrorException(`Error while fetching all contacts: ${error?.message}`);
    }
  }

  async findOne(contactId: Types.ObjectId) {
    const populateOptions = this.getPopulateOptions();
    try {
      const contact = await this.contactModel.findById(contactId)
        .populate(populateOptions)
        .exec();
      if (!contact || contact.isDeleted) {
        throw new NotFoundException(`Contact not found with ID: ${contactId}`);
      }
      return contact;
    } catch (error) {
      throw new InternalServerErrorException(`Error while fetching contact by ID ${contactId}: ${error?.message}`);
    }
  }

  async getContactCounts(query: QueryContactCountDto, userId: Types.ObjectId) {
    try {
      const { accountId, vendorId, isInternal } = query;
      let conditions: any = { isDeleted: false };

      if (accountId) {
        conditions.accountOrg = accountId;
      }

      if (vendorId) {
        conditions.vendor = vendorId;
      }

      else {
        conditions.createdBy = userId;
      }
      if (isInternal !== undefined) {
        conditions.isInternal = isInternal;
      }
      const count = await this.contactModel.countDocuments(conditions).exec();
      return {
        count
      };
    } catch (error) {
      throw new InternalServerErrorException(`Error while fetching contact counts: ${error.message}`);
    }
  }

  async getInternalContactCounts(userId: Types.ObjectId) {
    try {
      const count = await this.contactModel
        .countDocuments({
          isDeleted: false, // Exclude deleted contacts
          createdBy: userId, // Filter by the user who created the contacts
          isInternal: true, // Only include internal contacts
        })
        .exec();
      return {
        count,
      };
    } catch (error) {
      throw new InternalServerErrorException(
        `Error while fetching internal contact counts: ${error.message}`
      );
    }
  }




  async searchContacts(name: string) {
    const populateOptions = this.getPopulateOptions();
    try {
      if (!name) {
        throw new HttpException('Name parameter is required', HttpStatus.BAD_REQUEST);
      }
      const regex = new RegExp(name, 'i'); // 'i' for case-insensitive
      return await this.contactModel.find({ $or: [{ firstName: { $regex: regex } }, { lastName: { $regex: regex } }], isDeleted: false })
        .populate(populateOptions)
        .sort({ updatedAt: -1 })
        .exec();
    } catch (error) {
      this.logger.error(`Error while searching for contacts: ${error.message}`, error.stack);
      throw new InternalServerErrorException(`Error while searching for contacts: ${error?.message}`);
    }
  }

  // async filterContacts(queryDto: QueryContactDto): Promise<ContactDocument[]> {
  //   const { accountId, industryId, designation, page, limit } = queryDto;
  //   const populateOptions = this.getPopulateOptions();
  //   try {
  //     const query: any = {};

  //     if (accountId) {
  //       query.accountOrg = accountId;
  //     }

  //     if (industryId) {
  //       query.industry = industryId;
  //     }

  //     if (location) {
  //       query.location = { $regex: location, $options: 'i' };
  //     }

  //     if (designation) {
  //       query.designation = { $regex: designation, $options: 'i' };
  //     }

  //     const contacts = await this.contactModel.find(query)
  //       .populate(populateOptions)
  //       .skip((page - 1) * limit)
  //       .limit(limit)
  //       .exec();

  //     return contacts;
  //   } catch (error) {
  //     throw new InternalServerErrorException(`An error occurred while fetching contacts: ${error?.message}`);
  //   }
  // }


  async update(contactId: Types.ObjectId, updateContactDto: UpdateContactDto, user: BasicUser) {
    const populateOptions = this.getPopulateOptions();

    const unsetData: any = {};

    // List of fields to check
    const fieldsToCheck: (keyof UpdateContactDto)[] = ['industry', 'accountOrg', 'salesRepOrg', 'businessUnit', 'reportingTo', 'assignTo', 'logo'];

    // Iterate through each field, check for empty string, remove it from updateContactDto and mark for unset
    fieldsToCheck.forEach((field) => {
      if (updateContactDto[field] === '') {
        unsetData[field] = ''; // Mark for removal
        delete updateContactDto[field]; // Remove from updateContactDto
      }
    });

    try {
      // Fetch the current contact to compare values with the new ones
      const existingContact = await this.findOne(contactId);

      // console.log(JSON.stringify(updateContactDto))
      // console.log(JSON.stringify(unsetData))

      // Perform the update operation
      const updatedContact = await this.contactModel
        .findByIdAndUpdate(
          contactId,
          {
            $set: updateContactDto,  // Apply the updated fields
            $unset: unsetData,  // Remove the fields marked for deletion
          },
          { new: true }  // Return the updated document
        )
        .populate(populateOptions)
        .exec();

      if (!updatedContact) {
        throw new InternalServerErrorException(`Failed to update contact with ID ${contactId}`);
      }

      // Emit event only if accountOrg has changed
      if (existingContact.accountOrg?.toString() !== updatedContact.accountOrg?.toString()) {
        this.emitEvent('contact.account_org.updated', { updatedContact, user });
      }

      // Emit event only if reportingTo has changed
      if (existingContact.reportingTo?.toString() !== updatedContact.reportingTo?.toString()) {
        this.emitEvent('contact.reporting_to.updated', { updatedContact, user });
      }

      // Emit event only if industry has changed
      if (existingContact.industry?.toString() !== updatedContact.industry?.toString()) {
        this.emitEvent('contact.industry.updated', { updatedContact, user });
      }

      const createdBy = await this.basicUserModel.findById(updatedContact.createdBy).exec();
      if (updatedContact.assignTo && Array.isArray(updatedContact.assignTo)) {
        console.log('updatedContact.assignTo:', updatedContact.assignTo);
        console.log('existingContact.assignTo:', existingContact.assignTo);
        const newAssignToIds = updatedContact.assignTo.filter(
          id => !existingContact.assignTo?.some(existingId => existingId.toString() === id.toString())
        );
        console.log('newAssignToIds:', newAssignToIds);
        // Emit event only if assignTo has changed

        if (newAssignToIds.length > 0) {
          const assignToUsers = await this.basicUserModel.find({ _id: { $in: newAssignToIds } }).exec();
          if (!assignToUsers || assignToUsers.length === 0) {
            this.logger.log(`No users found for the provided new assignTo IDs.`);
          } else {
            const assignToEmails = assignToUsers.map(user => {
              this.emitEvent('contact.assignTo', {
                title: updatedContact.firstName,
                email: user.email,
                assignTo: user?.firstName,
                createdBy: createdBy?.firstName
              });
              return user.email;
            });
            this.logger.log('Assign To Emails:', assignToEmails);
          }
        }
      }

      return updatedContact;
    } catch (error) {
      this.logger.error(`An error occurred while updating contact with ID ${contactId}:`, error.stack);
      throw new InternalServerErrorException(`An error occurred while updating contact with ID ${contactId}: ${error.message}`);
    }
  }


  async changeStatus(contactId: Types.ObjectId, status: string, commentDto: CommentDto, user: BasicUser) {
    const populateOptions = this.getPopulateOptions();
    try {
      await this.findOne(contactId);
      const updatedContact = await this.contactModel.findByIdAndUpdate(contactId, { status }, { new: true })
        .populate(populateOptions)
        .exec();
      this.emitEvent('contact.status.changed', { updatedContact, commentDto, user });
      return updatedContact;
    } catch (error) {
      // this.logger.error(error);
      // this.logger.error(`An error occurred in fetching contact by Id ${contactId}: ${error?.message}`);
      throw new InternalServerErrorException(`An error occurred in fetching contact by Id ${contactId}: ${error?.message}`);
    }
  }

  async assignTo(contactId: Types.ObjectId, newAssigneeId: string[], commentDto: CommentDto, user: BasicUser) {
    const populateOptions = this.getPopulateOptions();
    try {
      const existingContact = await this.findOne(contactId);
      const updatedContact = await this.contactModel.findByIdAndUpdate(
        contactId,
        { assignTo: newAssigneeId },
        { new: true }
      )
        .populate(populateOptions)
        .exec();
      // this.emitEvent('contact.assign_to.changed', { updatedContact, commentDto, user });
      // console.log("existingContact.assignTo", existingContact.assignTo);
      // console.log("updatedContact.assignTo", updatedContact?.assignTo);

      const createdBy = await this.basicUserModel.findById(updatedContact?.createdBy).exec();
      if (updatedContact?.assignTo && Array.isArray(updatedContact?.assignTo)) {
        const existingAssignToIds = (existingContact?.assignTo as unknown as { _id: any }[]).map(user => user._id.toString());
        const updatedAssignToIds = (updatedContact?.assignTo as unknown as { _id: any }[]).map(user => user._id.toString());

        // const existingAssignToIds = existingOrg?.assignTo?.map(user => user._id.toString());
        // console.log('Existing AssignTo:', existingAssignToIds);
        // console.log('Updated AssignTo:', updatedAssignToIds);
        const newAssignToIds = updatedAssignToIds.filter(
          id => !existingAssignToIds.some(existingId => existingId.toString() === id.toString())
        );
        console.log(newAssignToIds);

        if (newAssignToIds.length > 0) {
          const assignToUsers = await this.basicUserModel.find({ _id: { $in: newAssignToIds } }).exec();
          // const assignToUsers = await this.basicUserModel.find({ _id: { $in: updatedOrgDetails?.assignTo } }).exec();
          if (!assignToUsers || assignToUsers.length === 0) {
            this.logger.log(`No users found for the provided assignTo IDs.`);
          } else {
            const assignToEmails = assignToUsers.map(user => {
              this.emitEvent('contact.assignTo', {
                title: updatedContact?.firstName,
                email: user.email,
                assignTo: user?.firstName,
                createdBy: createdBy?.firstName
              });
              return user.email;
            });
            this.logger.log('Assign To Emails:', assignToEmails);
          }
        }
      }
      return updatedContact;
    } catch (error) {
      // this.logger.error(error);
      // this.logger.error(`An error occurred in fetching contact by Id ${newContactId}. ${error?.message}`);
      throw new InternalServerErrorException(`An error occurred in fetching contact by Id ${contactId}: ${error?.message}`);
    }
  }


  async favourite(contactId: Types.ObjectId) {
    try {
      const contact = await this.findOne(contactId);
      contact.favourite = !contact.favourite; // default: false
      await contact.save();
      return contact;
    }
    catch (error) {
      // this.logger.error(error);
      // this.logger.error(`An error occurred while soft deleting contact by ID ${contactId}. ${error?.message}`);
      throw new InternalServerErrorException(`An error occurred in fetching contact by Id ${contactId}: ${error?.message}`);
    }
  }

  async hardDelete(contactId: Types.ObjectId) {
    try {
      await this.findOne(contactId);
      return await this.contactModel.findByIdAndDelete(contactId);
    } catch (error) {
      // this.logger.error(error);
      // this.logger.error(`An error occurred while hard deleting contact by ID ${contactId}. ${error?.message}`);
      throw new InternalServerErrorException(`An error occurred in fetching contact by Id ${contactId}: ${error?.message}`);
    }
  }

  async softDelete(contactId: Types.ObjectId) {
    try {
      const contact = await this.findOne(contactId);
      contact.isDeleted = true;
      await contact.save();
      return contact;
    }
    catch (error) {
      // this.logger.error(error);
      // this.logger.error(`An error occurred while soft deleting contact by ID ${contactId}. ${error?.message}`);
      throw new InternalServerErrorException(`An error occurred in fetching contact by Id ${contactId}: ${error?.message}`);
    }
  }

  async hardDeleteAllContacts() {
    try {
      const contactsToDelete = await this.contactModel.find();
      if (!contactsToDelete.length) {
        // this.logger.log('No contacts found to delete.');
        throw new NotFoundException('No contacts found to delete.');
      }
      return await this.contactModel.deleteMany();
    } catch (error) {
      // this.logger.error('Error while hard deleting all contacts', error.stack);
      throw new InternalServerErrorException('Error while hard deleting all contacts');
    }
  }

  async restoreSoftDeletedContact(contactId: Types.ObjectId) {
    try {
      const contact = await this.contactModel.findById(contactId);
      if (!contact) {
        throw new NotFoundException(`The contact with ID: "${contactId}" doesn't exist.`);
      }
      if (!contact.isDeleted) {
        throw new BadRequestException(`The contact with ID: "${contactId}" is not soft deleted.`);
      }
      contact.isDeleted = false
      await contact.save();
      return contact
    }
    catch (error) {
      // this.logger.error(`An error occurred while restoring Contact by ID ${contactId}. ${error?.message}`);
      throw new InternalServerErrorException(`An error occurred in fetching contact by Id ${contactId}: ${error?.message}`);
    }
  }

  async restore() {
    const populateOptions = this.getPopulateOptions();
    try {
      const softDeletedContacts = await this.contactModel.find({ isDeleted: true });
      if (!softDeletedContacts.length) {
        this.logger.log('No soft-deleted contacts found to restore.');
        return [];
      }
      for (const contact of softDeletedContacts) {
        contact.isDeleted = false;
        await contact.save();
      }
      // this.logger.log('All soft-deleted contacts have been restored.');
      return this.contactModel.find()
        .populate(populateOptions)
        .exec();
    } catch (error) {
      this.logger.error(`An error occurred while restoring soft-deleted contacts. ${error.message}`);
      throw new InternalServerErrorException(`An error occurred while restoring soft-deleted contacts. ${error.message}`);
    }
  }

  async addComment(contactId: Types.ObjectId, commentDto: CommentDto): Promise<CommentDocument> {
    try {
      const createdComment = new this.commentModel(commentDto);
      createdComment.contact = contactId;
      return await createdComment.save();
    }
    catch (error) {
      this.logger.error(`Failed to create comment. ${error}`);
      throw new InternalServerErrorException(`Error while creating a comment. ${error?.message}`);
    }
  }

  async getComments(contactId: Types.ObjectId): Promise<Comment[]> {
    try {
      return this.commentModel.find({ contact: contactId })
        .populate({ path: 'user', select: '_id roles firstName' })
        .populate({ path: 'attachments', select: '_id originalName fileSize fileType locationUrl', model: 'FileMetadata' }).exec();
    } catch (error) {
      throw new InternalServerErrorException('Failed to retrieve comments')
    }
  }


  async bulkDelete(orgIds: Types.ObjectId[]) {
    try {
      const result = await this.contactModel.updateMany(
        { _id: { $in: orgIds } },
        { $set: { isDeleted: true } }
      ).exec();

      if (result.modifiedCount > 0) {
        this.logger.log(`Successfully soft-deleted ${result.modifiedCount} org(s).`);
      } else {
        this.logger.error('No orgs were deleted.');
      }

      return result;
    } catch (error) {
      this.logger.error(`Error while bulk deleting orgs: ${error.message}`, error.stack);
      throw new InternalServerErrorException('Error while bulk deleting orgs.');
    }
  }

  async bulkChangeStatus(orgIds: Types.ObjectId[], changeStatusDto: ChangeStatusContactDto, user: BasicUser): Promise<any[]> {
    const populateOptions = this.getPopulateOptions();
    try {
      const { status, comment } = changeStatusDto
      const updateData: any = { status };
      const result = await this.contactModel.updateMany(
        { _id: { $in: orgIds } },
        { $set: { ...updateData } }
      ).exec();

      if (result.modifiedCount === 0) {
        throw new BadRequestException('No organizations were updated.');
      }
      const updatedOrgs = await this.contactModel.find({ _id: { $in: orgIds } })
        .select('title status _id createdBy')
        .populate(populateOptions)
        .exec();

      this.emitEvent('org.status.bulkChanged', { updatedOrgs, comment, user });

      return updatedOrgs

    } catch (error) {
      this.logger.error(`Error while bulk updating org status: ${error.message}`, error.stack);
      throw new InternalServerErrorException('Error while bulk updating org status.');
    }
  }
  async addToFavourites(userId: Types.ObjectId, contactIds: Types.ObjectId[]): Promise<ContactDocument[]> {
    const updatedContacts: ContactDocument[] = [];

    for (const contactId of contactIds) {
      const contact = await this.contactModel.findById(contactId);

      if (!contact) {
        throw new NotFoundException(`Contact with ID ${contactId} not found.`);
      }

      if (!Array.isArray(contact.favourites)) {
        contact.favourites = [];
      }

      if (!contact.favourites.includes(userId)) {
        contact.favourites.push(userId);
        await contact.save();
        updatedContacts.push(contact);
      }
    }

    // Populate user details in `favourites`
    return this.contactModel.find({ _id: { $in: contactIds } }).populate('favourites', 'firstName email roles').exec();
  }

  async getFavouriteContacts(userId: Types.ObjectId): Promise<ContactDocument[]> {
    // Validate pagination parameters
    // if (page <= 0 || limit <= 0) {
    //   throw new BadRequestException('Page and limit must be positive integers.');
    // }

    // const skip = (page - 1) * limit;

    return this.contactModel
      .find({ favourites: userId, isDeleted: false })
      .sort({ updatedAt: -1 })
      // .skip(skip)
      // .limit(limit)
      .populate('favourites', 'firstName email roles') // Populate the 'favourites' field
      .exec();
  }


  async removeFromFavourites(userId: Types.ObjectId, contactIds: Types.ObjectId[]): Promise<ContactDocument[]> {
    const updatedContacts: ContactDocument[] = [];

    for (const contactId of contactIds) {
      const contact = await this.contactModel.findById(contactId);

      if (!contact) {
        throw new NotFoundException(`Contact with ID ${contactId} not found.`);
      }

      if (Array.isArray(contact.favourites)) {
        // Convert userId to ObjectId if it's not already one
        const userObjectId = new Types.ObjectId(userId);

        // Remove the user from the favourites array
        contact.favourites = contact.favourites.filter(favourite => favourite.toString() !== userId.toString());

        await contact.save();
        updatedContacts.push(contact);
      }
    }

    // Populate user details in `favourites`
    return this.contactModel.find({ _id: { $in: contactIds } }).populate('favourites', 'firstName email roles').exec();
  }



  getPopulateOptions(): any[] {
    const populateOptions = [
      { path: 'createdBy', select: '_id roles firstName email', model: 'BasicUser' },
      { path: 'assignTo', select: '_id roles firstName email', model: 'BasicUser' },
    ];

    if (this.contactModel.schema.paths['industry']) {
      populateOptions.push({ path: 'industry', select: '_id name description', model: 'Industry' });
    }

    if (this.contactModel.schema.paths['accountOrg']) {
      populateOptions.push({ path: 'accountOrg', select: '_id title description', model: 'Org' });
    }

    if (this.contactModel.schema.paths['reportingTo']) {
      populateOptions.push({ path: 'reportingTo', select: '_id firstName lastName', model: 'Contact' });
    }

    // if (this.contactModel.schema.paths['referredBy']) {
    //   populateOptions.push({ path: 'referredBy', select: '_id firstName lastName', model: 'Contact' });
    // }

    if (this.contactModel.schema.paths['country']) {
      populateOptions.push({ path: 'country', select: '_id countryName countryPhoneCode currencyCode', model: 'Country' });
    }

    if (this.contactModel.schema.paths['state']) {
      populateOptions.push({ path: 'state', select: '_id stateName statePhoneCode', model: 'State' });
    }
    if (this.contactModel.schema.paths['city']) {
      populateOptions.push({ path: 'city', select: '_id name', model: 'City' });
    }
    if (this.contactModel.schema.paths['logo']) {
      populateOptions.push({
        path: 'logo',
        select: '_id locationUrl originalName fileSize fileType',
        model: 'FileMetadata',
      });
    }

    return populateOptions;
  }

  emitEvent(eventName: string, payload: any) {
    // payload['event']= eventName;
    this.logger.debug(payload)
    this.eventEmitter.emit(eventName, payload);
  }

  // async updateCreatedByOrgFromUserModel() {
  //   const orgs = await this.contactModel.find({}).exec();

  //   for (const org of orgs) {
  //     const user = await this.basicUserModel.findById(org.createdBy);
  //     if (user?.org) {
  //       await this.contactModel.updateOne(
  //         { _id: org._id },
  //         { $set: { createdByOrg: user.org.toString() } }
  //       );
  //     }
  //   }

  //   console.log(`✅ Done updating createdByOrg based on user orgId.`);
  // }

}
