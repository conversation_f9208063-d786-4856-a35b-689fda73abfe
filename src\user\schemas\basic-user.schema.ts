import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Types } from 'mongoose';
import { Role } from 'src/auth/enums/role.enum';
import { BusinessUnit } from 'src/business-unit/schemas/business-unit.schema';
import { AddressInformation } from 'src/common/schemas/address-information.schema';
import { ContactInformation } from 'src/common/schemas/contact-information.schema';
import { Country } from 'src/country/schemas/country.schema';
import { Employee } from 'src/employee/schemas/employee.schema';
import { FileMetadata } from 'src/file-upload/schemas/file-metadata.schema';
import { Org } from 'src/org/schemas/org.schema';
import { Roles } from 'src/roles/schemas/roles.schema';
import { SourceType } from 'src/shared/constants';
import { City } from 'src/state/schemas/city.schema';
import { State } from 'src/state/schemas/state.schema';
import { UserInboxConfig } from 'src/user-inbox-config/schemas/user-inbox-config.schema';

export type BasicUserDocument = HydratedDocument<BasicUser>;


// Prop decorator can accept more options - read here - https://mongoosejs.com/docs/schematypes.html#schematype-options
// and here - https://docs.nestjs.com/techniques/mongodb#model-injection
@Schema({
  timestamps: true
})
export class BasicUser {

  @Prop({
    type: String,
    required: true,
    // unique: true,
    // index:true
    lowercase: true,
    trim: true
  })
  email: string;

  //   @Prop()
  //   age: number;

  // @Prop({
  //   type: String,
  //   required: false,
  //   trim: true,
  // })
  // fullName?: string;

  @Prop({
    type: String,
    required: false,
    trim: true,
  })
  firstName?: string;

  @Prop({
    type: String,
    required: false,
    trim: true,
  })
  lastName?: string;

  @Prop({
    type: String,
    required: false,
    trim: true,
  })
  contactNumber?: string;

  @Prop({
    type: String,
    required: false,
    trim: true,
  })
  designation?: string;

  @Prop({
    type: Boolean,
    required: false,
    default: false
  })
  isVerified?: boolean;

  @Prop({
    type: Boolean,
    required: false,
    default: false
  })
  isApproved?: boolean;

  @Prop({
    type: String,
    required: true,
    trim: true
  })
  password: string;

  @Prop({
    required: false,
    default: [Role.User]
  })
  roles?: Role[];

  @Prop({
    type: Boolean,
    required: false,
    default: false
  })
  isDeleted?: boolean;

  @Prop({
    type: Boolean,
    required: false,
    default: false
  })
  isPending?: boolean;


  @Prop({
    type: Boolean,
    required: false,
    default: false
  })
  isRequested?: boolean;


  @Prop({
    type: Boolean,
    required: false,
    default: false
  })
  isSuspended?: boolean;

  @Prop({
    type: Boolean,
    required: false,
    default: false
  })
  isRejected?: boolean;


  @Prop({
    type: Boolean,
    required: false,
    default: false
  })
  isBlocked?: boolean;

  @Prop({
    type: Types.ObjectId,
    required: false,
    ref: 'Country'
  })
  country?: Country;

  @Prop({
    type: Types.ObjectId,
    required: false,
    ref: 'State'
  })
  state?: State;

  @Prop({
    type: Types.ObjectId,
    required: false,
    ref: 'City'
  })
  city?: City;

  // @Prop({
  //   type: String,
  //   required: false,
  //   trim: true,
  //   default: UserStatus.PENDING,
  //   enum: Object.values(UserStatus),
  // })
  // status?: string;

  @Prop({
    required: false,
    type: Types.ObjectId,
    ref: 'Status'
  })
  customStatus?: Types.ObjectId;

  @Prop({
    type: String,
    required: false,
    trim: true
  })
  verification?: string;

  @Prop({
    type: String,
    required: false,
    trim: true
  })
  apikey?: string;

  @Prop({
    type: String,
    required: false,
    trim: true
  })
  otpCode?: string;

  @Prop({ type: Boolean, required: false, default: false })
  verified?: boolean;

  @Prop({
    type: Date,
    required: false
  })
  verificationExpires?: Date;

  @Prop({ type: Number, default: 0 })
  loginAttempts: number;

  @Prop({
    type: Date,
    required: false
  })
  blockExpires?: Date;

  @Prop({
    type: Types.ObjectId,
    ref: 'Org',
    required: false
  })
  org?: Org;

  @Prop({
    type: Types.ObjectId,
    ref: 'Org',
    required: false
  })
  companyId?: Org;

  @Prop({
    type: Types.ObjectId,
    ref: 'UserInboxConfig',
    required: false
  })
  userInboxConfig?: Types.ObjectId;

  // @Prop({
  //   type: Types.ObjectId,
  //   ref: 'BusinessUnit',
  //   required: false
  // })
  // businessUnit?: BusinessUnit;

  @Prop({
    type: [{ type: Types.ObjectId, ref: 'BusinessUnit' }], // Array of ObjectId references to BusinessUnit
    required: false,
  })
  businessUnit?: string[]; // This represents an array of BusinessUnit documents


  @Prop({
    type: [ContactInformation],
    required: false,
    // ref: 'ContactInformation'
  })
  contactDetails?: ContactInformation[];

  @Prop({ type: [AddressInformation], required: false })
  contactAddress?: AddressInformation[];

  @Prop({
    type: [String],
    required: false
  })
  interestedCountries?: string[];

  @Prop({
    type: [String],
    required: false
  })
  interestedDomains?: string[];

  @Prop({
    type: [String],
    required: false
  })
  interestedSkills?: string[];

  @Prop({
    type: String,
    required: false,
    trim: true,
    default: SourceType.ADMIN_PORTAL,
    enum: Object.values(SourceType),
  })
  source?: string;

  // @Prop({
  //   type: Types.ObjectId,
  //   required: false,
  //   ref: 'BasicUser'
  // })
  // reportingTo?: BasicUser;

  @Prop({
    type: [{ type: Types.ObjectId, ref: 'BasicUser' }], // Array of ObjectIds that reference BasicUser
    required: false,
  })
  reportingTo?: string[]; // This represents an array of ObjectIds


  @Prop({
    type: Boolean,
    required: false,
    default: true,
  })
  isOperational?: boolean;

  @Prop({
    type: Types.ObjectId,
    required: false
  })
  logo?: FileMetadata;

  @Prop({
    required: false,
    type: [Types.ObjectId],
  })
  roleIds?: Roles[];

  @Prop({
    type: Boolean,
    required: true,
    default: false,
  })
  isCustom?: boolean; //this is set to true when the admin upadtes user permissions

  @Prop()
  createdAt?: Date;

  @Prop()
  updatedAt?: Date;

  @Prop({
    type: Types.ObjectId,
    ref: 'Employee',
    required: false
  })
  employeeId?: Employee | Types.ObjectId;
  @Prop({
    type: Types.ObjectId,
    ref: 'BgvHandler',
    required: false
  })
  bgvHandlerId: string;

  @Prop({
    type: Boolean,
    required: false,
    default: false
  })
  isAdminTermsAccepted?: boolean;

  @Prop({
    type: [{ type: Types.ObjectId, ref: 'Project' }], // Array of ObjectId references to BusinessUnit
    required: false,
  })
  projectIds?: string[];

}

export const BasicUserSchema = SchemaFactory.createForClass(BasicUser);

BasicUserSchema.index(
  { email: 1 },
  {
    unique: true,
    partialFilterExpression: { isDeleted: false }, // 👈 only unique when not deleted
  }
);