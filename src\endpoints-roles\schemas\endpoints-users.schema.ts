import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Types } from 'mongoose';
import { EndpointPermission } from 'src/endpoint-permissions/schemas/endpointpermissions.schema';
import { Org } from 'src/org/schemas/org.schema';
import { BasicUser } from 'src/user/schemas/basic-user.schema';


export type EndpointsUsersDocument = HydratedDocument<EndpointsUsers>;
@Schema({ timestamps: true })
export class EndpointsUsers {
    @Prop({ type: Types.ObjectId, ref: 'EndpointPermission', required: true })
    endPoint: EndpointPermission;

    // @Prop({ type: [String],lowercase: true, required: false })
    // roles?: string[];

    @Prop({
        type: Types.ObjectId,
        required: true,
        ref: 'Org'
    })
    org: Org;

    @Prop({
        type: Types.ObjectId,
        required: true,
        ref: 'BasicUser'
    })
    userId: BasicUser;
}

export const EndpointsUsersSchema = SchemaFactory.createForClass(EndpointsUsers);
EndpointsUsersSchema.index({ endPoint: 1, org: 1, userId: 1 }, { unique: true });
