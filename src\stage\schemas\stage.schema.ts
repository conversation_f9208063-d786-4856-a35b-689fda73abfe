import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Types } from 'mongoose';
import { Org } from 'src/org/schemas/org.schema';
import { StageType } from 'src/shared/constants';

export type StageDocument = HydratedDocument<Stage>;

@Schema({
  timestamps:true
})
export class Stage {
   
  @Prop({
    type: String,
    required: true,
    trim: true
  })
  name: string;
 
  @Prop({
    type: String,
    required: true,
    trim: true,
    default: StageType.NONE,
    enum: Object.values(StageType),
  })
  type: string;

  @Prop({
    type: Number,
    required: false,
  })
  sequenceNumber?: number;

  @Prop({
    type: Number,
    required: false,
  })
  jobApplicationsCount?: number;

  @Prop({
    type: String,
    required: false,
    trim: true
  })
  iconUrl?: string; 
  
  // @Prop({
  //   type: Types.ObjectId,
  //   ref: 'Org',
  //   required: true
  // })
  // org: Org;
  


}

export const StageSchema = SchemaFactory.createForClass(Stage);

