import { BadRequestException } from '@nestjs/common';
import { Types } from 'mongoose';

export function validateObjectId(id: string) {
    if (!Types.ObjectId.isValid(id)) {
        throw new BadRequestException(`Not a valid Mongodb Object ID.`);
    }
    return new Types.ObjectId(id);
}

export function validateArrayOfObjectIds(array: string[] = []) {
    array.forEach((element) => {
        validateObjectId(element);
    })
}