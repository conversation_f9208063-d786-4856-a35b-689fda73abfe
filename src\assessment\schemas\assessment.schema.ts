
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Types } from 'mongoose';
import { FileMetadata } from 'src/file-upload/schemas/file-metadata.schema';
import { JobApplication } from 'src/job-application-form/schemas/job-application.schema';

export type AssessmentDocument = HydratedDocument<Assessment>;


@Schema({ timestamps: true })
export class Assessment {

  @Prop({
    type: Types.ObjectId,
    required: true,
    ref: 'JobApplication',
  })
  jobApplication: JobApplication;

  @Prop({
    type: Date,
    required: false,
  })
  dueDate?: Date;

  @Prop({
    type: String,
    required: false,
    trim: true,
  })
  assessmentLink?: string;

  @Prop({
    type: [FileMetadata],
    required: false,
  })
  assessmentFiles?: FileMetadata[];

  @Prop({
    type: Boolean,
    required: false,
  })
  isAssessmentCompleted?: boolean;

}

export const AssessmentSchema = SchemaFactory.createForClass(Assessment);