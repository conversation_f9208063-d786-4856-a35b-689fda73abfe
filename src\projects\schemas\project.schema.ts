import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { Org } from 'src/org/schemas/org.schema';
import { BasicUser } from 'src/user/schemas/basic-user.schema';

@Schema({ timestamps: true })
export class Project extends Document {
    @Prop({ required: true })
    name: string;

    @Prop({ required: false })
    description: string;

    @Prop({
        type: Date,
        required: false,
    })
    startDate?: Date;

    @Prop({
        type: Date,
        required: false,
    })
    endDate?: Date;

    @Prop({
        type: Number,
        required: false,
        min: 0,
        default: 0,
    })
    budget: number;

    @Prop({ required: false })
    currency: string;

    @Prop({ enum: ['Billable', 'Non-Billable'], required: false })
    projectType: string;

    @Prop({
        type: [{ type: Types.ObjectId, ref: 'BusinessUnit' }], // Array of ObjectId references to BusinessUnit
        required: false,
    })
    departments?: string[]; // This represents an array of BusinessUnit documents


    @Prop({
        type: Types.ObjectId,
        ref: 'Org',
        required: false
    })
    client?: Org;

    @Prop({
        type: Types.ObjectId,
        ref: 'Org',
        required: false
    })
    org?: Org;

    @Prop({
        required: false,
        type: Types.ObjectId, ref: 'BasicUser'
    })
    projectManagerId?: BasicUser;

    @Prop({
        type: Boolean,
        required: false,
        default: false
    })
    isDeleted?: boolean;

    @Prop({
        type: Boolean,
        required: false,
        default: true
    })
    isInternal?: boolean;

}

export const ProjectSchema = SchemaFactory.createForClass(Project);
