import { ApiProperty } from "@nestjs/swagger";
import { Transform, TransformFnParams, Type } from "class-transformer";
import { IsEnum, IsMongoId, IsNotEmpty, IsOptional, IsString } from "class-validator";
import { sanitizeWithStyle } from "src/utils/sanitize-with-style";
import { AddressType } from "src/shared/constants";

export class AddressInformationDto {
  @ApiProperty({
    type: String,
    required: false,
    description: "Enter Door, suite, apartment information"
  })
  @IsOptional()
  @IsString()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value).toLowerCase())
  apartment?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: "Enter street"
  })
  @IsString()
  @IsOptional()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value).toLowerCase())
  street?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: "Enter city"
  })
  @IsString()
  @IsOptional()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value).toLowerCase())
  city?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: "Enter postal code"
  })
  @IsString()
  @IsOptional()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value).toLowerCase())
  postalCode?: string;

  // @ApiProperty({
  //   type: String,
  //   required: false,
  //   description: "Enter state"
  // })
  // @IsMongoId()
  // @IsOptional()
  // @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value).toLowerCase())
  // state?: string;

  // @ApiProperty({
  //   type: String,
  //   required: false,
  //   description: "Enter country"
  // })
  // @IsMongoId()
  // @IsOptional()
  // @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value).toLowerCase())
  // country?: string;

  // @ApiProperty({
  //   type: String,
  //   required: false,
  //   description: "Enter region"
  // })
  // @IsMongoId()
  // @IsOptional()
  // @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value).toLowerCase())
  // country?: string;

  @ApiProperty({
    type: String,
    required: false,
    default: AddressType.HOME,
    enum: AddressType,
    description: 'Enter address type',
  })
  @IsOptional()
  @IsEnum(AddressType)
  addressType?: string;
}
