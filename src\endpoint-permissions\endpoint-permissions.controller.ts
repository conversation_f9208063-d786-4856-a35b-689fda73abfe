import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Req, BadRequestException, Query } from '@nestjs/common';
import { EndpointPermissionsService } from './endpoint-permissions.service';
import { CreateEndpointPermissionDto, UpdateUserPermissionsDto } from './dto/create-endpoint-permission.dto';
import { UpdateEndpointPermissionDto } from './dto/update-endpoint-permission.dto';
import { ApiBearerAuth, ApiOperation, ApiParam, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { AuthJwtGuard } from 'src/auth/guards/auth-jwt/auth-jwt.guard';
import { validateObjectId } from 'src/utils/validation.utils';
import { Role } from 'src/auth/enums/role.enum';

@Controller('')
@ApiTags('endpoint-permissions')
export class EndpointPermissionsController {
  constructor(private readonly endpointPermissionsService: EndpointPermissionsService) { }

  // @Post()
  // create(@Body() createEndpointPermissionDto: CreateEndpointPermissionDto) {
  //   return this.endpointPermissionsService.create(createEndpointPermissionDto);
  // }

  // @Get()
  // findAll() {
  //   return this.endpointPermissionsService.findAll();
  // }

  // @Get(':id')
  // findOne(@Param('id') id: string) {
  //   return this.endpointPermissionsService.findOne(+id);
  // }

  // @Patch(':id')
  // update(@Param('id') id: string, @Body() updateEndpointPermissionDto: UpdateEndpointPermissionDto) {
  //   return this.endpointPermissionsService.update(+id, updateEndpointPermissionDto);
  // }

  // @Delete(':id')
  // remove(@Param('id') id: string) {
  //   return this.endpointPermissionsService.remove(+id);
  // }

  @Get('groupByModules')
  @ApiOperation({ summary: 'Retrieve all Endpoints Grouping By Controller and Method' })
  // @ApiParam({ name: 'endpoinsRolesId', description: 'ID of the EndpointsRoles document' })
  @ApiResponse({ status: 200, description: 'Record found.' })
  @ApiResponse({ status: 404, description: 'Record not found.' })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  async groupByModules() {
    // const objId = validateObjectId(endpoinsRolesId);
    return this.endpointPermissionsService.groupByModules();
  }

  @Get('rolesMapping')
  @ApiOperation({ summary: 'Retrieve all Endpoints Grouping By Controller and Method' })
  // @ApiParam({ name: 'endpoinsRolesId', description: 'ID of the EndpointsRoles document' })
  @ApiResponse({ status: 200, description: 'Record found.' })
  @ApiResponse({ status: 404, description: 'Record not found.' })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  async getRolesWithPermissions(@Req() req: any) {
    if (!req.user.org) {
      throw new BadRequestException('orgId is not found for this user');
    }
    const orgId = req.user.org._id;
    // const objId = validateObjectId(endpoinsRolesId);
    return this.endpointPermissionsService.getControllerMethodRoles(orgId);
  }

  @Patch('/update')
  @ApiOperation({ summary: 'Update roles with permissions based on controller and method' })
  @ApiResponse({ status: 200, description: 'Roles updated successfully.' })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  @ApiResponse({ status: 404, description: 'Record not found.' })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  async updateRolesWithPermissions(@Req() req: any, @Body() payload: CreateEndpointPermissionDto) {
    if (!req.user.org) {
      throw new BadRequestException('orgId is not found for this user');
    }
    const orgId = req.user.org._id;
    return this.endpointPermissionsService.updateOrgRolePermissions(orgId, payload);
  }

  @Patch('/updateUserPermissions')
  @ApiOperation({ summary: 'Update user-specific permissions based on controller and method' })
  @ApiResponse({ status: 200, description: 'User permissions updated successfully.' })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  @ApiResponse({ status: 404, description: 'Record not found.' })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  async updateUserPermissions(@Req() req: any, @Body() payload: UpdateUserPermissionsDto) {
    if (!req.user.org) {
      throw new BadRequestException('orgId is not found for this user');
    }
    const orgId = req.user.org._id;

    return this.endpointPermissionsService.updateUserPermissions(orgId,payload);
  }

  // @Get('groupByModulesWithoutOrg')
  // @ApiOperation({ summary: 'Retrieve all Endpoints Grouping By Controller and Method' })
  // // @ApiParam({ name: 'endpoinsRolesId', description: 'ID of the EndpointsRoles document' })
  // @ApiResponse({ status: 200, description: 'Record found.' })
  // @ApiResponse({ status: 404, description: 'Record not found.' })
  // @ApiBearerAuth()
  // @UseGuards(AuthJwtGuard)
  // async groupByModulesForNoOrg() {
  //   // const objId = validateObjectId(endpoinsRolesId);
  //   return this.endpointPermissionsService.groupByModulesForNoOrg();
  // }

  // @Get('rolesMappingWithoutOrg')
  // @ApiOperation({ summary: 'Retrieve all Endpoints Grouping By Controller and Method' })
  // // @ApiParam({ name: 'endpoinsRolesId', description: 'ID of the EndpointsRoles document' })
  // @ApiResponse({ status: 200, description: 'Record found.' })
  // @ApiResponse({ status: 404, description: 'Record not found.' })
  // @ApiBearerAuth()
  // @UseGuards(AuthJwtGuard)
  // async getControllerMethodRolesForNoOrg(@Req() req: any) {
  //   // if (!req.user.org) {
  //   //   throw new BadRequestException('orgId is not found for this user');
  //   // }
  //   // const orgId = req.user.org._id;
  //   // const objId = validateObjectId(endpoinsRolesId);
  //   return this.endpointPermissionsService.getControllerMethodRolesForNoOrg();
  // }

  // @Patch('/updateRolesWithoutOrg')
  // @ApiOperation({ summary: 'Update roles with permissions based on controller and method' })
  // @ApiResponse({ status: 200, description: 'Roles updated successfully.' })
  // @ApiResponse({ status: 400, description: 'Bad Request.' })
  // @ApiResponse({ status: 404, description: 'Record not found.' })
  // @ApiBearerAuth()
  // @UseGuards(AuthJwtGuard)
  // async updateRolePermissions(@Req() req: any, @Body() payload: CreateEndpointPermissionDto) {
  //     // if (!req.user.org) {
  //     //     throw new BadRequestException('orgId is not found for this user');
  //     // }
  //     // const orgId = req.user.org._id;
  //     return this.endpointPermissionsService.updateRolePermissionsForNoOrg(payload);
  // }

  @Get('resourcesActionsMapping')
  @ApiOperation({ summary: 'Retrieve all Endpoints Grouping By Controller and Method' })
  // @ApiParam({ name: 'endpoinsRolesId', description: 'ID of the EndpointsRoles document' })
  @ApiResponse({ status: 200, description: 'Record found.' })
  @ApiResponse({ status: 404, description: 'Record not found.' })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  async getResourcesWithActions(@Req() req: any) {
    // if (!req.user.org) {
    //   throw new BadRequestException('orgId is not found for this user');
    // }
    let orgId = req.user?.org?._id;
    const roles = req.user?.roles;
    if (req.user?.roles.includes(Role.Vendor)) {
      orgId = req.user?.companyId?._id;
    }
    const userId = req.user?._id;
    // const objId = validateObjectId(endpoinsRolesId);
    return this.endpointPermissionsService.getResourceWiseActionsForRole(orgId, roles,userId);
  }
  @Patch('/updateRolesWithoutOrg/:role')
  @ApiOperation({ summary: 'Update roles with permissions based on controller and method' })
  @ApiResponse({ status: 200, description: 'Roles updated successfully.' })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  @ApiResponse({ status: 404, description: 'Record not found.' })
  @ApiParam({ name: 'role', description: 'role to process' })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  async updateRolePermissions(@Req() req: any, @Param('role') role: string,) {
    // if (!req.user.org) {
    //     throw new BadRequestException('orgId is not found for this user');
    // }
    // const orgId = req.user.org._id;
    const allowedRoles = [Role.Freelancer, Role.JobSeeker];
    if (!allowedRoles.includes(role as Role)) {
      throw new BadRequestException(`Invalid role: ${role}. Allowed roles: ${allowedRoles.join(', ')}`);
    }
    return this.endpointPermissionsService.cloneDefaultEndpointsForNoOrgRoles(role);
  }

  @Get('getRolesByOrg')
  @ApiOperation({ summary: 'Get list of Roles by orgId', description: `This endpoint gives the list of Roles by type.` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  @ApiQuery({ name: 'isInternal', required: false, type: Boolean, description: 'to fetch internal or delivery roles' })
  @ApiResponse({ status: 200, description: 'All Roles are retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized ' })
  async getRolesByTheirStatus(@Req() req: any, @Query('isInternal') isInternal: boolean) {
    const orgId = req.user.org._id;
    if (!orgId) {
      throw new BadRequestException('orgId query parameter is required');
    }
    return await this.endpointPermissionsService.findRolesByStatus(orgId, isInternal);
  }

  @Get('usersresourcesActionsMapping/:userId')
  @ApiOperation({ summary: 'Retrieve all Endpoints Grouping By Controller and Method' })
  // @ApiParam({ name: 'endpoinsRolesId', description: 'ID of the EndpointsRoles document' })
  @ApiResponse({ status: 200, description: 'Record found.' })
  @ApiResponse({ status: 404, description: 'Record not found.' })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  async getUserResourcesWithActions(@Req() req: any,@Param('userId') userId: string) {
    // if (!req.user.org) {
    //   throw new BadRequestException('orgId is not found for this user');
    // }
    // let orgId = req.user?.org?._id;
    // const roles = req.user?.roles;
    // if (req.user?.roles.includes(Role.Vendor)) {
    //   orgId = req.user?.companyId?._id;
    // }
    // const objId = validateObjectId(endpoinsRolesId);
    return this.endpointPermissionsService.getResourceWiseActionsForUser(userId);
  }

  @Patch('/updateIsCustom/:userId')
  @ApiOperation({ summary: 'Update user-specific permissions based on controller and method' })
  @ApiQuery({ name: 'isCustom', required: true, type: Boolean, description: 'Set to true to activate custom permissions for the user' })
  @ApiResponse({ status: 200, description: 'User permissions updated successfully.' })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  @ApiResponse({ status: 404, description: 'Record not found.' })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  async updateIsCustomFlag(@Req() req: any,@Param('userId') userId: string,@Query('isCustom') isCustom?: boolean) {
  const objId = validateObjectId(userId);
    return this.endpointPermissionsService.updateIsCustomFlag(userId.toString(),isCustom);
  }

}
