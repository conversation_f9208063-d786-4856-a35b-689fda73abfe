import { Routes } from '@nestjs/core';
import { AuthModule } from './auth/auth.module';
import { UserModule } from './user/user.module';
import { JobModule } from './job/job.module';
import { OrgModule } from './org/org.module';
import { ContactModule } from './contact/contact.module';
import { AccountTypeModule } from './account-type/account-type.module';
import { IndustryModule } from './industry/industry.module';
import { NoteModule } from './note/note.module';
import { TaskModule } from './task/task.module';
import { CountryModule } from './country/country.module';
import { ActivityModule } from './activity/activity.module';
import { StateModule } from './state/state.module';
import { MessageModule } from './message/message.module';
import { JobLocationModule } from './job-location/job-location.module';
import { EvaluationFormModule } from './evaluation-form/evaluation-form.module';
import { EducationQualificationModule } from './education-qualification/education-qualification.module';
import { JobApplicationFormModule } from './job-application-form/job-application-form.module';
import { FileUploadModule } from './file-upload/file-upload.module';
import { WorkExperienceModule } from './work-experience/work-experience.module';
import { ResumeModule } from './resume/resume.module';
import { IdentifierModule } from './identifier/identifier.module';
import { StageModule } from './stage/stage.module';
import { WorkflowModule } from './workflow/workflow.module';
import { InterviewModule } from './interview/interview.module';
import { CalendarModule } from './calendar/calendar.module';
import { OnboardingModule } from './onboarding/onboarding.module';
import { AssessmentModule } from './assessment/assessment.module';
import { OfferModule } from './offer/offer.module';
import { StatusModule } from './status/status.module';
import { RegionModule } from './region/region.module';
import { UserInboxConfigModule } from './user-inbox-config/user-inbox-config.module';
import { BusinessUnitModule } from './business-unit/business-unit.module';
import { EmailTemplateBuilderModule } from './email-template-builder/email-template-builder.module';
import { MeetingModule } from './meeting/meeting.module';
import { JobAllocationModule } from './job-allocation/job-allocation.module';
import { BlogModule } from './blog/blog.module';
import { RecruitementTeamModule } from './recruitement-team/recruitement-team.module';
import { VendorInviteModule } from './vendor-invite/vendor-invite.module';
import { BenchModule } from './bench/bench.module';
import { IntegrationsModule } from './integrations/integrations.module';
import { PreferenceModule } from './preferences/preference.module';
import { RecruiterTargetModule } from './recruiter-target/recruiter-target.module';
import { DeleteUserModule } from './delete-user/delete-user.module';
import { RolesModule } from './roles/roles.module';
import { PrivilegeModule } from './privilege/privilege.module';
import { RolePrivilegeModule } from './role-privilege/role-privilege.module';
import { EndpointPermissionsModule } from './endpoint-permissions/endpoint-permissions.module';
import { EndpointsRolesModule } from './endpoints-roles/endpoints-roles.module';
import { CustomFieldsModule } from './custom-fields/custom-fields.module';
import { DashboardModule } from './dashboard/dashboard.module';
import { RateCardCategoryModule } from './rate-card-category/rate-card-category.module';
import { RateCardModule } from './rate-card/rate-card.module';
import { NotificationsModule } from './notification/notifications.module';
import { AnnouncmentsModule } from './announcments/announcments.module';
import { EmployeeModule } from './employee/employee.module';
import { BgvHandlerModule } from './bgv-handler/bgv-handler.module';
import { InvoiceModule } from './invoices/invoice.module';
import { AttendanceModule } from './attendance/attendance.module';
import { ProjectsModule } from './projects/projects.module';
import { TimeSheetModule } from './time-sheet/time-sheet.module';
import { LeaveManagmentModule } from './leave-managment/leave-managment.module';



export const routes: Routes = [
  {
    path: 'auth',
    module: AuthModule,
  },
  {
    path: 'users',
    module: UserModule,
  },
  {
    path: 'jobs',
    module: JobModule,
  },
  {
    path: 'job-allocations',
    module: JobAllocationModule,
  },
  {
    path: 'job-locations',
    module: JobLocationModule,
  },
  {
    path: 'job-application-forms',
    module: JobApplicationFormModule,
  },
  {
    path: 'work-experiences',
    module: WorkExperienceModule,
  },
  {
    path: 'evaluation-forms',
    module: EvaluationFormModule,
  },
  {
    path: 'education-qualifications',
    module: EducationQualificationModule,
  },

  {
    path: 'contacts',
    module: ContactModule,
  },
  {
    path: 'countries',
    module: CountryModule,
    // children: [
    //   {
    //     path: '/:countryId/states',
    //     module: StateModule,
    //   },
    // ]
  },
  {
    path: 'states',
    module: StateModule,
  },
  {
    path: 'orgs',
    module: OrgModule,
  },
  {
    path: 'account-types',
    module: AccountTypeModule,
  },
  {
    path: 'industries',
    module: IndustryModule,
  },
  {
    path: 'notes',
    module: NoteModule,
  },
  {
    path: 'contacts',
    module: ContactModule,
  },
  {
    path: 'business-units',
    module: BusinessUnitModule,
  },
  {
    path: 'tasks',
    module: TaskModule,
  },
  {
    path: 'activities',
    module: ActivityModule,
  },
  // {
  //   path: 'clients',
  //   module: ClientModule,
  // },
  {
    path: 'messages',
    module: MessageModule,
  },
  {
    path: 'file-uploads',
    module: FileUploadModule,
  },
  {
    path: 'resumes',
    module: ResumeModule,
  },
  {
    path: 'identifiers',
    module: IdentifierModule,
  },
  {
    path: 'onboardings',
    module: OnboardingModule,
  },
  {
    path: 'stages',
    module: StageModule,
  },
  {
    path: 'workflows',
    module: WorkflowModule,
  },
  {
    path: 'interviews',
    module: InterviewModule,
  },
  {
    path: 'calendars',
    module: CalendarModule,
  },
  {
    path: 'assessments',
    module: AssessmentModule,
  },
  {
    path: 'offers',
    module: OfferModule,
  },
  {
    path: 'status',
    module: StatusModule,
  },
  {
    path: 'region',
    module: RegionModule,
  },
  {
    path: 'user-inbox-config',
    module: UserInboxConfigModule,
  },
  {
    path: 'email-template',
    module: EmailTemplateBuilderModule,
  },
  {
    path: 'meeting',
    module: MeetingModule,
  },
  {
    path: 'blog',
    module: BlogModule,
  },
  {
    path: 'recruitement-team',
    module: RecruitementTeamModule,
  },
  {
    path: 'vendor-invite',
    module: VendorInviteModule,
  },
  {
    path: 'Bench',
    module: BenchModule,
  },
  {
    path: 'integrations',
    module: IntegrationsModule,
  },
  {
    path: 'preferences',
    module: PreferenceModule,
  },
  {
    path: 'roles',
    module: RolesModule,
  },
  {
    path: 'recruiter-target',
    module: RecruiterTargetModule,
  },
  {
    path: 'delete-user',
    module: DeleteUserModule,
  },
  {
    path: 'privileges',
    module: PrivilegeModule,
  },
  {
    path: 'role-privilages',
    module: RolePrivilegeModule,
  },
  {
    path: 'endpoints-roles',
    module: EndpointsRolesModule,
  },
  {
    path: 'custom-fields',
    module: CustomFieldsModule,
  },
  {
    path: 'dashboard',
    module: DashboardModule,
  },
  {
    path: 'rate-card-category',
    module: RateCardCategoryModule,
  },
  {
    path: 'rate-cards',
    module: RateCardModule,
  },
  {
    path: 'notifications',
    module: NotificationsModule,
  },
  {
    path: 'announcments',
    module: AnnouncmentsModule,
  },
  {
    path: 'employee',
    module: EmployeeModule,
  },
  {
    path: 'bgv-handler',
    module: BgvHandlerModule,
  },
  {
    path: 'invoices',
    module: InvoiceModule,
  },
  {
    path:'attendance',
    module: AttendanceModule
  },
  {
    path: 'projects',
    module: ProjectsModule,
  },
  {
    path: 'time-sheet',
    module: TimeSheetModule
  },
  {
    path: 'leave-managment',
    module: LeaveManagmentModule
  }


];