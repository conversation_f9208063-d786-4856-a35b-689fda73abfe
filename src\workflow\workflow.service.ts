import { BadRequestException, Injectable, InternalServerErrorException, Logger, NotFoundException } from '@nestjs/common';
import { CreateWorkflowDto } from './dto/create-workflow.dto';
import { UpdateWorkflowDto } from './dto/update-workflow.dto';
import { Model, Types } from 'mongoose';
import { ConfigService } from '@nestjs/config';
import { InjectModel } from '@nestjs/mongoose';
import { Workflow, WorkflowDocument } from './schemas/workflow.schema';
import { Stage } from '../stage/schemas/stage.schema';
import { CreateStageDto } from 'src/stage/dto/create-stage.dto';
import { StageService } from 'src/stage/stage.service';
import { OnEvent } from '@nestjs/event-emitter';
import { EmailTemplate } from 'src/email-template-builder/schemas/email-template-builder.schema';
import { EmailTemplateEvent } from 'src/shared/constants';
import { JobApplication } from 'src/job-application-form/schemas/job-application.schema';
import { Placeholder } from 'src/org/schemas/org.schema';
import { unescape } from 'lodash';
import { JSONPath } from 'jsonpath-plus';
import { CreateJobDto } from 'src/job/dto/create-job.dto';


@Injectable()
export class WorkflowService {

  private readonly logger = new Logger(WorkflowService.name);
  constructor(private configService: ConfigService,
    private stageService: StageService,
    @InjectModel(Workflow.name) private workflowModel: Model<Workflow>,
    @InjectModel(EmailTemplate.name) private emailTemplateService: Model<EmailTemplate>,
    @InjectModel(JobApplication.name) private jobApplicationService: Model<JobApplication>,
    @InjectModel(Placeholder.name) private placeholderService: Model<Placeholder>,
    @InjectModel(Stage.name) private stageModel: Model<Stage>,
    // @InjectModel(Org.name) private orgModel: Model<Org>,
  ) { }

  async create(createWorkflowDto: CreateWorkflowDto): Promise<WorkflowDocument> {
    try {
      const transformedStages = createWorkflowDto.stages.map((id) => new Types.ObjectId(id));

      // Only transform org and businessUnitId if they exist
      const transformedOrg = createWorkflowDto.org ? new Types.ObjectId(createWorkflowDto.org) : null;
      const transformedBusinessUnitId = createWorkflowDto.businessUnitId || null;

      // If isDefault is true, unset isDefault from other workflows of the same org or business unit
      if (createWorkflowDto.isDefault) {
        const filter: any = {};

        const orConditions = [];
        if (transformedOrg) {
          orConditions.push({ org: transformedOrg });
        }
        if (transformedBusinessUnitId) {
          orConditions.push({ businessUnitId: transformedBusinessUnitId });
        }

        if (orConditions.length > 0) {
          filter.$or = orConditions;
        }

        await this.workflowModel.updateMany(filter, { isDefault: false });
      }

      const createdWorkflow = new this.workflowModel({
        ...createWorkflowDto,
        stages: transformedStages,
        org: transformedOrg,
      });

      await createdWorkflow.save();
      return createdWorkflow;
    }
    catch (error) {
      if (error.name === 'ValidationError') {
        this.logger.error(`Validation failed when creating a workflow. ${error.message}`);
        throw new BadRequestException(`Invalid data: ${error.message}`);
      }
      this.logger.error(`Failed to create a workflow. ${error}`);
      throw new InternalServerErrorException(`Error when creating a workflow. ${error?.message}`);
    }
  }

  async cloneDefaultWorkflow(createJobDto: CreateJobDto, user: any): Promise<WorkflowDocument> {
    this.logger.log(JSON.stringify(createJobDto));

    let defaultWorkflow = null
    let orgId = undefined
    if (createJobDto.department) {
      defaultWorkflow = await this.workflowModel.findOne({
        businessUnitId: createJobDto.department,
        isDefault: true,
      });
      orgId = createJobDto.endClientOrg
    }
    if (createJobDto.endClientOrg && !defaultWorkflow) {
      defaultWorkflow = await this.workflowModel.findOne({
        org: new Types.ObjectId(createJobDto.endClientOrg),
        isDefault: true,
      });
      orgId = createJobDto.endClientOrg
    }
    if (createJobDto.hiringOrg && !defaultWorkflow) {
      defaultWorkflow = await this.workflowModel.findOne({
        org: new Types.ObjectId(createJobDto.hiringOrg),
        isDefault: true,
      });
      orgId = createJobDto.hiringOrg
    }

    if (user.org && !defaultWorkflow) {
      defaultWorkflow = await this.workflowModel.findOne({
        org: new Types.ObjectId(user.org._id),
        isDefault: true,
      });
      orgId = user.org._id
    }
    this.logger.log(defaultWorkflow)
    if (!defaultWorkflow) {
      defaultWorkflow = await this.workflowModel.findOne({ isGlobal: true });
    }
    if (!defaultWorkflow) {
      throw new InternalServerErrorException(`No default workflow found for organization ${orgId}`);
    }

    const clonedStageIds: Types.ObjectId[] = [];
    for (const stageId of defaultWorkflow.stages) {
      const originalStage = await this.stageModel.findById(stageId).exec();
      if (!originalStage) {
        throw new InternalServerErrorException(`Stage not found: ${stageId}`);
      }

      const originalStageObject = originalStage.toObject();
      const { _id, ...stageData } = originalStageObject;
      stageData.jobApplicationsCount = 0;
      const clonedStage = new this.stageModel({
        ...stageData,
        _id: new Types.ObjectId(),
      });
      await clonedStage.save();
      clonedStageIds.push(clonedStage._id);
    }

    const clonedWorkflow = new this.workflowModel({
      name: defaultWorkflow.name,
      stages: clonedStageIds,
      // department: defaultWorkflow.department,
      isDefault: false,
      isActive: false,
      org: new Types.ObjectId(orgId)
    });
    await clonedWorkflow.save();

    return clonedWorkflow;
  }

  async updateJobIdInWorkFlow(workflowId: Types.ObjectId, jobId: string): Promise<WorkflowDocument> {
    try {
      // this.logger.log(`Workflow ID: ${workflowId}`);
      // this.logger.log(`Job ID: ${jobId}`);

      // Find the workflow by ID
      const workflow = await this.workflowModel.findById(workflowId);

      if (!workflow) {
        throw new NotFoundException(`Workflow with ID ${workflowId} not found.`);
      }

      // Update the job field
      workflow.job = new Types.ObjectId(jobId); // Ensure jobId is stored as ObjectId

      await workflow.save(); // Save the updated workflow

      // this.logger.log(`Updated Workflow: ${workflow}`);
      return workflow;
    } catch (error) {
      this.logger.error('Error updating workflow:', error);
      throw new InternalServerErrorException('Failed to update workflow.');
    }
  }


  getAllWorkflows(): Promise<WorkflowDocument[]> {
    return this.workflowModel.find()
      .populate({ path: 'stages', select: '_id name type sequenceNumber jobApplicationsCount' })
      // .populate({ path: 'org', select: '_id title orgType status legalName' })
      .exec();
  }

  async findOne(workflowId: Types.ObjectId) {
    try {
      const workflow = await this.workflowModel.findById(workflowId)
        .populate({ path: 'stages', select: '_id name type sequenceNumber jobApplicationsCount' })
        // .populate({ path: 'org', select: '_id title orgType status legalName' })
        .exec();
      if (!workflow) {
        throw new NotFoundException(`The workflow with id: "${workflowId}" doesn't exist.`);
      }
      return workflow;
    }
    catch (error) {
      this.logger.error(`An error occurred in fetching workflow by Id ${workflowId}. ${error?.message}`);
      throw error;

    }
  }

  @OnEvent('business-unit.created', { async: true })  // Use the async option
  async onBusinessUnitCreatedEvent(payload: any) {
    const { createdBusinessUnit, user } = payload;
    const defaultWorkflow = await this.workflowModel.findOne({
      org: createdBusinessUnit.org,
      isDefault: true,
    });

    if (!defaultWorkflow) {
      throw new InternalServerErrorException(`No default workflow found for organization ${createdBusinessUnit.org}`);
    }

    const clonedStageIds: Types.ObjectId[] = [];
    for (const stageId of defaultWorkflow.stages) {
      const originalStage = await this.stageModel.findById(stageId).exec();
      if (!originalStage) {
        throw new InternalServerErrorException(`Stage not found: ${stageId}`);
      }

      const originalStageObject = originalStage.toObject();
      const { _id, ...stageData } = originalStageObject;

      const clonedStage = new this.stageModel({
        ...stageData,
        _id: new Types.ObjectId(),
      });
      await clonedStage.save();
      clonedStageIds.push(clonedStage._id);
    }

    const clonedWorkflow = new this.workflowModel({
      name: createdBusinessUnit.label + ' Workflow',
      stages: clonedStageIds,
      businessUnit: createdBusinessUnit._id,
      isDefault: false,
      isActive: false,
      org: createdBusinessUnit.org
    });
    await clonedWorkflow.save();

    return clonedWorkflow;
  }

  @OnEvent('org.workflow', { async: true })
  async onOrgCreatedEvent(payload: any) {
    const { createdOrg } = payload;
    // this.logger.log(JSON.stringify(createdOrg))
    // this.logger.log(createdOrg)

    try {
      let defaultWorkflow = await this.workflowModel.findOne({ isGlobal: true });

      if (!defaultWorkflow) {
        this.logger.log("No default workflow is assigned to the created org");
        return;
      }

      const clonedStageIds: Types.ObjectId[] = [];

      for (const stageId of defaultWorkflow.stages) {
        try {
          const originalStage = await this.stageModel.findById(stageId).exec();
          if (!originalStage) {
            throw new InternalServerErrorException(`Stage not found: ${stageId}`);
          }

          const originalStageObject = originalStage.toObject();
          const { _id, ...stageData } = originalStageObject;

          const clonedStage = new this.stageModel({
            ...stageData,
            _id: new Types.ObjectId(),
          });

          await clonedStage.save();
          clonedStageIds.push(clonedStage._id);
        } catch (stageError) {
          this.logger.error(`Error cloning stage: ${stageId}`, stageError.stack);
          throw new InternalServerErrorException(`Failed to clone stage: ${stageId}`);
        }
      }

      const clonedWorkflow = new this.workflowModel({
        name: defaultWorkflow.name,
        stages: clonedStageIds,
        isDefault: true,
        isActive: true,
        org: createdOrg._id
      });

      await clonedWorkflow.save();

      return clonedWorkflow;
    } catch (error) {
      this.logger.error("Error during organization workflow creation", error);
      throw new InternalServerErrorException("Failed to create organization workflow");
    }
  }

  async update(workflowId: Types.ObjectId, updateWorkflowDto: UpdateWorkflowDto, user: any) {
    try {
      const workflow = await this.workflowModel.findById(workflowId).exec();
      if (!workflow) {
        throw new NotFoundException(`The workflow with id: "${workflowId}" doesn't exist.`);
      }

      // If isDefault is true, update other workflows in the same scope to isDefault: false
      if (updateWorkflowDto.isDefault) {
        const filter: any = {};

        const orConditions = [];
        if (workflow.org) {
          orConditions.push({ org: workflow.org });
        }
        if (workflow.businessUnitId) {
          orConditions.push({ businessUnitId: workflow.businessUnitId });
        }

        if (orConditions.length > 0) {
          filter.$or = orConditions;
        }

        await this.workflowModel.updateMany(filter, { isDefault: false });
      }

      const transformedStages = updateWorkflowDto?.stages?.map((id) => new Types.ObjectId(id));

      // Only transform org and businessUnitId if they exist
      const transformedOrg = updateWorkflowDto.org ? new Types.ObjectId(updateWorkflowDto.org) : null;

      const data = {
        ...updateWorkflowDto,
        stages: transformedStages,
        org: transformedOrg,
      };

      const updatedworkflow = await this.workflowModel.findByIdAndUpdate(workflowId, data, { new: true })
        .populate({ path: 'stages', select: '_id name type sequenceNumber jobApplicationsCount' })
        // .populate({ path: 'org', select: '_id title orgType status legalName' })
        .exec();
      return updatedworkflow;
    }
    catch (error) {
      this.logger.error(`An error occurred while updating workflow by ID ${workflowId}. ${error?.message}`)
      throw error;
    }
  }

  async createStageAndUpdateInWorkflow(workflowId: Types.ObjectId, createStageDto: CreateStageDto) {
    try {
      const workflow = await this.workflowModel.findById(workflowId);

      if (!workflow) {
        throw new NotFoundException(`The workflow with id: "${workflowId}" doesn't exist.`);
      }
      const createdStage = await this.stageService.create(createStageDto);
      workflow.stages.push(createdStage._id);
      await workflow.save();
      return workflow;
    } catch (error) {
      throw new InternalServerErrorException(`Error while updating stage in workflow. ${error?.message}`);
    }
  }

  async updateWorkflowStageOrder(workflowId: Types.ObjectId, stageOrder: string[]): Promise<WorkflowDocument> {
    try {
      const stageOrderObjectIds = stageOrder.map(id => new Types.ObjectId(id));
      const workflow = await this.workflowModel.findById(workflowId);
      if (!workflow) {
        throw new NotFoundException(`The workflow with id: "${workflowId}" doesn't exist.`);
      }
      const workflowStageIds = workflow.stages.map(stage => stage.toString());
      const providedStageIds = stageOrder.map(stage => stage.toString());

      if (workflowStageIds.length !== providedStageIds.length || !providedStageIds.every(id => workflowStageIds.includes(id))) {
        throw new InternalServerErrorException('Given stage order does not match existing stages in the workflow.');
      }

      for (let i = 0; i < stageOrderObjectIds.length; i++) {
        const stageId = stageOrderObjectIds[i];
        const stage = await this.stageModel.findById(stageId);
        if (stage) {
          stage.sequenceNumber = i + 1;
          await stage.save();
        }
      }

      workflow.stages = stageOrderObjectIds;
      await workflow.save();
      return workflow;
    } catch (error) {
      throw new InternalServerErrorException(`Error while updating workflow stage order: ${error.message}`);
    }
  }

  async hardDelete(workflowId: Types.ObjectId) {
    try {
      const workflow = await this.workflowModel.findById(workflowId).exec();
      if (!workflow) {
        throw new NotFoundException(`The workflow with id: "${workflowId}" doesn't exist.`);
      }
      await this.workflowModel.findByIdAndDelete(workflowId);
      return `Workflow deleted`;
    }
    catch (error) {
      this.logger.error(`An error occurred while deleting workflow by ID ${workflowId}. ${error?.message}`);
      throw error;
    }
  }

  async workflowFilter(orgId: string, businessUnitId?: string, isDefault?: boolean) {
    try {
      const filter: any = { org: new Types.ObjectId(orgId) };
      if (businessUnitId) {
        filter.businessUnitId = businessUnitId;
      }
      else {
        filter.businessUnitId = { $exists: false }; // OR: { $in: [null, undefined] }
      }
      if (isDefault !== undefined) {
        filter.isDefault = isDefault;
      }

      // Ensure workflows where the `job` field does not exist
      filter.$or = [{ job: { $exists: false } }, { job: null }];

      // Query the database with the filter
      const workflows = await this.workflowModel.find(filter)
        .populate({ path: 'stages', select: '_id name type sequenceNumber jobApplicationsCount' })
        .sort({ updatedAt: -1 })
        // .populate({ path: 'org', select: '_id title orgType status legalName' })
        .exec();

      for (const workflow of workflows) {
        if (workflow.job) {
          await this.workflowModel.populate(workflow, {
            path: 'job',
            select: '_id title' // Adjust fields as needed
          });
        }
      }

      return workflows;
    } catch (error) {
      this.logger.error(error);
      throw new InternalServerErrorException(`An error occurred while filtering workflows. ${error?.message}`);
    }
  }

  async generateTrackerEmailContent(user: any, jobApplicationIds: string[]) {
    try {
      // this.logger.log(JSON.stringify(jobApplicationIds));
      // Step 1: Fetch the email template for the tracker event
      let emailTemplate = await this.emailTemplateService.findOne({
        eventName: EmailTemplateEvent.WORKFLOW_TRACKER,
        isDefault: false,
        org: user.org._id,
      }).exec();

      if (!emailTemplate) {
        emailTemplate = await this.emailTemplateService.findOne({
          eventName: EmailTemplateEvent.WORKFLOW_TRACKER,
          isDefault: true,
        }).exec();
      }


      // Step 2: Handle error if no template is found

      // Step 2: Handle error if no template is found
      if (!emailTemplate) {
        throw new Error('Email template not found for the tracker event.');
      }


      // Step 3: Extract the email template body content

      // Step 3: Extract the email template body content
      let body = emailTemplate.templateHTMLContent ? unescape(emailTemplate.templateHTMLContent) : '';
      this.logger.log(body);

      // Step 2: Fetch placeholders and job applications
      const placeholders = await this.placeholderService.find({ emailTemplate: emailTemplate._id.toString() }).exec();
      const jobApplications = await this.jobApplicationService.find({
        _id: { $in: jobApplicationIds },
      }).exec();

      if (!jobApplications || !jobApplications.length) {
        throw new Error('No job applications found for the provided IDs.');
      }

      // Step 3: Extract all rows in the table
      const rowMatches = body.match(/<tr[^>]*>.*?<\/tr>/gs); // Matches all rows in the table
      if (!rowMatches || rowMatches.length < 2) {
        throw new Error('Table rows not found or insufficient rows in the email body.');
      }

      // Use the second row as the row template
      const rowTemplate = rowMatches[1]; // Assuming the second row is at index 1

      // Step 4: Generate rows for each job application
      let generatedRows = '';
      jobApplications.forEach((jobApplication) => {
        let row = rowTemplate;

        placeholders.forEach((placeholder) => {
          const path = placeholder.jsonPath;
          const value = JSONPath({ path, json: { data: jobApplication.toJSON() } });

          const dynamicPlaceholder = new RegExp(`#${placeholder.name}`);
          if (value.length > 0) {
            row = row.replace(dynamicPlaceholder, `<b>${value[0]}</b>`);
          }
        });

        generatedRows += row;
      });

      // Step 5: Replace the second row in the table with the generated rows
      body = body.replace(rowTemplate, generatedRows);

      // Step 6: Clean any mentions or placeholders that remain in the email body
      const cleanedHtml = body.replace(/<span[^>]*class="TiptapEditor_mention__[^"]*"[^>]*>(.*?)<\/span>/g, '$1');

      return cleanedHtml;
    } catch (error) {
      this.logger.error(`Error generating tracker email content: ${error.message}`);
      throw new Error(`Failed to generate tracker email content: ${error.message}`);
    }
  }
}
