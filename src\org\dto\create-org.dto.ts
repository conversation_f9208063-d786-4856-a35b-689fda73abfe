import { ApiHideProperty, ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsArray, IsBoolean, IsEnum, IsMongoId, IsNotEmpty, IsOptional, IsString, ValidateNested } from "class-validator";
import { HeadCount, SourceType, OrgStatus, OrgType } from "src/shared/constants";
import { Transform, TransformFnParams, Type } from "class-transformer";
import { sanitizeWithStyle } from "src/utils/sanitize-with-style";
import { ContactInformationDto } from "src/common/dto/contact-information.dto";
import { AddressInformationDto } from "src/common/dto/address-information.dto";
import { CreateUserDto } from "src/user/dto/create-user.dto";

export class CreateOrgDto {

  @ApiProperty({
    type: String,
    required: true,
    description: 'The title of your org',
  })
  @IsNotEmpty()
  @IsString()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  title: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'The description of your org',
  })
  @IsString()
  @IsOptional()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  description?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'The logo of your org',
  })
  @IsString()
  @IsOptional()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  logo?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'The thumbnail for social-sharing of the org',
  })
  @IsString()
  @IsOptional()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  thumbnail?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'The description for social-sharing of the org',
  })
  @IsString()
  @IsOptional()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  socialDescription?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'The legal name of your org',
  })
  @IsString()
  @IsOptional()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  legalName?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'The website URL of your org',
  })
  @IsString()
  @IsOptional()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  websiteUrl?: string;

  @ApiProperty({
    type: [ContactInformationDto],
    required: false,
    description: 'Contact information of org'
  })
  @IsOptional()
  contactDetails?: ContactInformationDto[];

  @ApiProperty({
    type: AddressInformationDto,
    required: false,
    description: 'Contact address'
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => AddressInformationDto)
  contactAddress?: AddressInformationDto[];

  @ApiProperty({
    type: String,
    required: false,
    description: 'The industry of your org',
  })
  @IsOptional()
  // @IsMongoId()
  @IsString()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  industryOrDomain?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'The business unit of your org',
  })
  @IsString()
  @IsOptional()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  businessUnit?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: "Enter city"
  })
  @IsMongoId()
  @IsOptional()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value).toLowerCase())
  city?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: "Enter state"
  })
  @IsMongoId()
  @IsOptional()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value).toLowerCase())
  state?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: "Enter country"
  })
  @IsMongoId()
  @IsOptional()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value).toLowerCase())
  country?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'The linkedin URL of your org',
  })
  @IsString()
  @IsOptional()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  linkedInUrl?: string;

  @ApiProperty({
    type: String,
    required: false,
    default: HeadCount.NOT_SPECIFIED,
    enum: HeadCount,
    description: 'Number of employees in the org',
  })
  @IsString()
  @IsOptional()
  @IsEnum(HeadCount)
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  headCount?: string;

  @ApiProperty({
    type: String,
    required: false,
    default: SourceType.ADMIN_PORTAL,
    enum: SourceType,
    description: 'Source where the organization was added',
  })
  @IsString()
  @IsOptional()
  @IsEnum(SourceType)
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  source?: string;

  @ApiPropertyOptional({
    type: String,
    required: false,
    description: 'The parent org of your org',
  })
  @IsOptional()
  // @IsMongoId()
  @IsString()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  parentOrg?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'The  org',
  })
  @IsString()
  // @IsMongoId({ message: 'org must be a valid MongoDB ObjectId.' })
  @IsOptional()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  companyId?: string;

  @ApiPropertyOptional({
    type: [String],
    required: false,
    description: 'The parent org of your org',
  })
  @IsOptional()
  @IsArray()
  @Transform((params: TransformFnParams) => params.value.map((value: string) => sanitizeWithStyle(value)))
  vendors?: string[];

  @ApiPropertyOptional({
    type: [String],
    required: false,
    description: 'The parent org of your org',
  })
  @IsOptional()
  @IsArray()
  @Transform((params: TransformFnParams) => params.value.map((value: string) => sanitizeWithStyle(value)))
  customers?: string[];

  // Not required here.

  // @ApiProperty({
  //   type: String,
  //   required: false,
  //   description: 'Reporting to User',
  // })
  // @IsMongoId()
  // @IsOptional()
  // @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  // reportingTo?: string;

  // @ApiProperty({
  //   type: String,
  //   required: false,
  //   description: 'Assigning to User',
  // })
  // // @IsMongoId()
  // @IsString()
  // @IsOptional()
  // @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  // assignTo?: string;

  @ApiProperty({
    type: [String],
    required: false,
    description: 'Array of user IDs to assign',
  })
  @IsArray()
  @IsOptional()
  @Transform(({ value }) => {
    if (Array.isArray(value)) {
      return value.map((v) => sanitizeWithStyle(v));
    }
    return [];
  })
  assignTo?: string[];
  

  @ApiProperty({
    type: String,
    required: false,
    default: OrgStatus.PROSPECT,
    enum: OrgStatus,
    description: 'Org status',
  })
  @IsOptional()
  @IsString()
  @IsEnum(OrgStatus)
  status?: OrgStatus

  @ApiProperty({ type: String, required: false, description: 'Reference to the Account type' })
  @IsOptional()
  // @IsMongoId()
  @IsString()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  accountType?: string;

  @ApiProperty({
    type: String,
    required: true,
    default: OrgType.NONE,
    enum: OrgType,
    description: "Org type"
  })
  @IsNotEmpty()
  @IsString()
  @IsEnum(OrgType)
  orgType: string;

  @ApiProperty({
    type: Boolean,
    required: false,
    description: "Tells which is admin org",
    default: false
  })
  @IsOptional()
  @IsBoolean()
  isAdminOrg?: Boolean;

  @ApiProperty({
    type: Boolean,
    required: false,
    description: "Tells which is onboarded org",
    default: false
  })
  @IsOptional()
  @IsBoolean()
  isOnboarded?: Boolean;


  @ApiProperty({
    type: Boolean,
    required: false,
    description: "Tells whether we need to create an admin user",
    default: false
  })
  @IsOptional()
  @IsBoolean()
  createAdminUser?: Boolean;

  @ApiProperty({
    description: 'Details of the admin user to be created along with the organization',
    type: CreateUserDto, // Reference to the nested DTO
    required: false,     // Indicates it's optional
  })
  @ValidateNested()
  @Type(() => CreateUserDto)
  @IsOptional()
  createUserDto?: CreateUserDto;

  @ApiHideProperty()
  @IsOptional()
  @IsString()
  createdBy?: string;

  @ApiHideProperty()
  @IsOptional()
  @IsString()
  createdByOrg?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'The subdomain of the org',
  })
  @IsString()
  @IsOptional()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  subDomain?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'The org which added the current org',
  })
  @IsOptional()
  @IsString()
  // @IsMongoId()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  addedByOrg?: string;

  @ApiProperty({
    type: Boolean,
    required: false,
    description: "Tells whether the org is updated or not",
    default: false
  })
  @IsOptional()
  @IsBoolean()
  isUpdated?: Boolean;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Comapny profile name',
  })
  @IsString()
  @IsOptional()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  companyProfileName?: string;

}
