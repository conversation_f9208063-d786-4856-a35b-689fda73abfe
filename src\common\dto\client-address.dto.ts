import { ApiProperty } from '@nestjs/swagger';
import { Transform, TransformFnParams } from 'class-transformer';
import { IsBoolean, IsMongoId, IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { sanitizeWithStyle } from "src/utils/sanitize-with-style";

export class ClientAddressDto {
  @ApiProperty({
    type: String,
    required: true,
    description: "Country ID (MongoDB ObjectId)",
  })
  @IsNotEmpty()
  @IsMongoId()
  country: string;

  @ApiProperty({
    type: String,
    required: true,
    description: "State ID (MongoDB ObjectId)",
  })
  @IsNotEmpty()
  @IsMongoId()
  state: string;

  @ApiProperty({
    type: String,
    required: true,
    description: "City ID (MongoDB ObjectId)",
  })
  @IsNotEmpty()
  @IsMongoId()
  city: string;

  @ApiProperty({
    type: String,
    required: true,
    description: "Postal code",
  })
  @IsNotEmpty()
  @IsString()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value).trim())
  postalCode: string;

  @ApiProperty({
    type: String,
    required: true,
    description: "Apartment or address details",
  })
  @IsNotEmpty()
  @IsString()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value).trim())
  apartment: string;

  // @ApiProperty({
  //   type: Boolean,
  //   required: false,
  //   description: "Whether the address is in a Special Economic Zone (SEZ)",
  //   default: false,
  // })
  // @IsOptional()
  // @IsBoolean()
  // sez?: boolean;

  // @ApiProperty({
  //   type: String,
  //   required: true,
  //   description: "GST Number (will be uppercased)",
  // })
  // @IsNotEmpty()
  // @IsString()
  // @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value).toUpperCase().trim())
  // gstNumber: string;

  @ApiProperty({
    type: Boolean,
    required: false,
    description: "Whether the address is registered or not",
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  registeredAddress?: boolean;

}
