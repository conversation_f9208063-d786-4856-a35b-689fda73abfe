import { ApiProperty } from "@nestjs/swagger";
import { IsEnum, IsNotEmpty, IsBoolean, IsMongoId, IsOptional, IsArray, ArrayMinSize } from "class-validator";
import { CandidateFields } from "src/shared/constants";

export class CreateCustomFieldDto {
    @ApiProperty({
        description: 'List of field names (Enum-based)',
        enum: CandidateFields,
        isArray: true
    })
    @IsArray()
    @ArrayMinSize(1)  // Ensure at least one name is provided
    @IsEnum(CandidateFields, { each: true })  // Validate each item in the array
    @IsNotEmpty({ each: true })  // Ensure no empty strings in the array
    name: CandidateFields[];


    @ApiProperty({
        description: 'Indicates if the field is a default field',
        type: Boolean,
        default: false,
        required: false
    })
    @IsBoolean()
    @IsOptional()
    isDefault?: boolean;

    @ApiProperty({
        description: 'Indicates if the field is visible in the UI',
        type: Boolean,
        default: true
    })
    @IsBoolean()
    @IsNotEmpty()
    isVisible: boolean;

    @ApiProperty({
        description: 'Organization ID (if applicable)',
        required: false,
        type: String
    })
    @IsMongoId()
    @IsOptional()
    org?: string;
}
