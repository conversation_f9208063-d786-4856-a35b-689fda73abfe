import { Injectable, OnModuleInit } from '@nestjs/common';
import { Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Redis } from 'ioredis';

@Injectable()
export class RedisStoreService implements OnModuleInit{
    private logger: Logger = new Logger(RedisStoreService.name);
    client!:any;

    constructor(private configService: ConfigService){

    }

    async onModuleInit() {
        try {
            // the following uses node-redis
            // this.client = createClient({ url: `${process.env.REDIS_URL}` });
            // await this.client.connect();

            // the following uses ioredis.
            // let isProd = `${process.env.PRODUCTION?.toLowerCase()}` ==='true';
            let isProd = this.configService.get<string>('PRODUCTION')==='true' ;
            let REDIS_URL = this.configService.get<string>('REDIS_URL');
            // let isHeroku = `${process.env.HEROKU?.toLowerCase()}`==='true';
            let isHeroku = this.configService.get<string>('HEROKU')==='true';
            this.logger.log(`REDIS_URL is set to ${REDIS_URL}. HEROKU is ${isHeroku} and PRODUCTION is ${isProd}`);

            // the following code throws etimed out error ON LOCAL. On Heroku - TLS settings is a must.
            // https://socket.io/docs/v4/server-options
            // Read Heroku Redis - https://devcenter.heroku.com/articles/connecting-heroku-redis#connecting-in-node-js
            if(isProd && isHeroku){
                this.logger.log(`Production and Heroku - found true. Using heroku specific settings for Redis`);
                this.client = new Redis(`${REDIS_URL}`,
                {
                    connectTimeout: 60000,
                    tls: {
                        rejectUnauthorized: false,
                        }
                });
            }else{
                this.logger.log(`Production and / or  Heroku - found false. Using local specific settings for Redis`);
                this.client = new Redis(`${REDIS_URL}`,{
                    connectTimeout: 60000,
                    // lazyConnect: true

                });

            }

 

            // the following is for heroku redis issue fix.
            // this.client = new Redis(`${process.env.REDIS_URL}`,
            //         {
            //             tls: {
            //                 rejectUnauthorized: false,
            //                 }
            //         });

            // // the following is only for local.
            // this.client = new Redis(`${process.env.REDIS_URL}`);

            // this deletes the whole shit. don't mess around 
            // await this.client.flushall();


            this.client.on('error', (err:any) => this.logger.error(`Error in Redis Store Service. ${err}`));
            this.ping();
            return;
        } catch (error) {
            this.logger.error(`Error in init - redis store service. ${error}`);
            return ;
        }
    }

    async ping(){
        await this.client.set('ping', 'PONG');
        const pinged =  await this.client.get('ping');
        this.logger.log(`Ping: ${pinged}`);
    }
    
    async store(key:string, value:any, expiryInSeconds?:number){
        if(expiryInSeconds){
            return await this.client.set(key, value, 'EX', expiryInSeconds);

        }else{
            return await this.client.set(key, value);

        }
    }

    async delete(key:string){
        return await this.client.del(key); 
    }

    async getValue(key:string){
        return await this.client.get(key);
    }

    async storeObject(key:string, value:any, expiryInSeconds?:number){
        if(expiryInSeconds){
            return await this.client?.set(key , JSON.stringify(value), 'EX', expiryInSeconds);
        }else{
            return await this.client?.set(key , JSON.stringify(value));
        }

    }

    async getObject(key:string){
        const value = await this.client.get(key);
        return JSON.parse(value);

    }

    async deleteObject(key:string){
        return await this.client.del(key);

    }
}
