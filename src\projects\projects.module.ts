import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ProjectsService } from './projects.service';
import { ProjectsController } from './projects.controller';
import { Project, ProjectSchema } from './schemas/project.schema';
import { ProjectAllocation, ProjectAllocationSchema } from './schemas/project-allocation.schema';
import { JwtModule } from '@nestjs/jwt';
import { EndpointsRolesModule } from 'src/endpoints-roles/endpoints-roles.module';
import { Org, OrgSchema } from 'src/org/schemas/org.schema';
import { Offer, OfferSchema } from 'src/offer/schemas/offer.schema';
import { BasicUser, BasicUserSchema } from 'src/user/schemas/basic-user.schema';
import { JobApplication, JobApplicationSchema } from 'src/job-application-form/schemas/job-application.schema';
import { Job, JobSchema } from 'src/job/schemas/job.schema';
import { RateCard, RateCardSchema } from 'src/rate-card/schemas/rate-card.schema';
import { OrgModule } from 'src/org/org.module';
import { EmailTemplate, EmailTemplateSchema } from 'src/email-template-builder/schemas/email-template-builder.schema';
import { Placeholder, PlaceholderSchema } from 'src/org/schemas/org.schema';
import { Bgv, BgvSchema } from 'src/offer/schemas/bgv.schema';
import { Invoice, InvoiceSchema } from 'src/invoices/schemas/invoice.schema';
import { Employee, EmployeeSchema } from 'src/employee/schemas/employee.schema';

@Module({
    imports: [JwtModule, EndpointsRolesModule,
        MongooseModule.forFeature([
            { name: Employee.name, schema: EmployeeSchema },
            { name: Invoice.name, schema: InvoiceSchema },
            { name: Org.name, schema: OrgSchema },
            { name: Offer.name, schema: OfferSchema },
            { name: BasicUser.name, schema: BasicUserSchema },
            { name: JobApplication.name, schema: JobApplicationSchema },
            { name: Job.name, schema: JobSchema },
            { name: RateCard.name, schema: RateCardSchema },
            { name: EmailTemplate.name, schema: EmailTemplateSchema },
            { name: Placeholder.name, schema: PlaceholderSchema },
            { name: Bgv.name, schema: BgvSchema },
            { name: Project.name, schema: ProjectSchema },
            { name: ProjectAllocation.name, schema: ProjectAllocationSchema },
        ]),
        MongooseModule.forFeature([{ name: 'User', schema: {} }])
    ],
    providers: [ProjectsService],
    controllers: [ProjectsController],
})
export class ProjectsModule { }
