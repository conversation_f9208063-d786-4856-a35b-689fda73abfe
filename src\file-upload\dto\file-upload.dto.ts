// import { ApiProperty } from '@nestjs/swagger';

// export class SingleFileUploadDto {
//     @ApiProperty({
//         type: 'string',
//         format: 'binary',
//         description: 'The file to upload',
//     })
//     file: any;
// }

// export class MultipleFileUploadDto {
//     @ApiProperty({
//         type: 'array',
//         items: {
//             type: 'string',
//             format: 'binary',
//         },
//         description: 'The files to upload',
//     })
//     files: any[];
// }
