import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Types } from 'mongoose';
import { BasicUser } from 'src/user/schemas/basic-user.schema';
// export type AccountDocument = HydratedDocument<Account>;


// Prop decorator can accept more options - read here - https://mongoosejs.com/docs/schematypes.html#schematype-options
// and here - https://docs.nestjs.com/techniques/mongodb#model-injection
@Schema({timestamps:true})
export class AccountType {

  @Prop({
    required: true,
    unique: true,
    trim: true,
  })
  name: string;

  @Prop({
    required: false,
    trim: true,
  })
  description?: string;

  @Prop({
    required: false,
    default: false,
    type: Boolean,
  })
  isDeleted?: boolean;
  
  @Prop({
    required: true,
    type: Types.ObjectId, ref: 'BasicUser'
  })
  createdBy: BasicUser;

}

export const AccountTypeSchema = SchemaFactory.createForClass(AccountType);
