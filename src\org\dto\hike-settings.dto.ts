import { IsBoolean, IsEnum, IsMongoId, IsNumber, IsOptional, IsString } from "class-validator";
import { EmploymentType } from "src/shared/constants";

export class UpdateHikeSettingsDto {
    @IsOptional()
    @IsMongoId()
    orgId?: string;

    @IsOptional()
    @IsMongoId()
    departmentId?: string;

    @IsEnum(EmploymentType)
    @IsOptional()
    employmentType: EmploymentType;

    @IsNumber()
    @IsOptional()
    minMarginPercentage: number;

    @IsOptional()
    @IsNumber()
    maxHikePercentage: number;

    @IsOptional()
    @IsBoolean()
    requiresApproval: boolean;

    @IsOptional()
    @IsString()
    approverRole: string;
}
