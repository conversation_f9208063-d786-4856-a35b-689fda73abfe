import { BadRequestException, Injectable, InternalServerErrorException, Logger, NotFoundException } from '@nestjs/common';
import { CreateAssessmentDto } from './dto/create-assessment.dto';
import { UpdateAssessmentDto } from './dto/update-assessment.dto';
import { Model, Types } from 'mongoose';
import { ConfigService } from '@nestjs/config';
import { InjectModel } from '@nestjs/mongoose';
import { Assessment, AssessmentDocument } from './schemas/assessment.schema';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { EmailTemplateEvent } from 'src/shared/constants';
import { JSONPath } from 'jsonpath-plus';
import { EmailTemplate } from 'src/email-template-builder/schemas/email-template-builder.schema';
import { Placeholder } from 'src/org/schemas/org.schema';
import { unescape } from 'lodash';
import { format } from 'date-fns';
import { toZonedTime } from 'date-fns-tz';

@Injectable()
export class AssessmentService {

  private readonly logger = new Logger(AssessmentService.name);
  constructor(
    @InjectModel(Assessment.name) private assessmentModel: Model<Assessment>, private eventEmitter: EventEmitter2,
    @InjectModel(EmailTemplate.name) private emailTemplateService: Model<EmailTemplate>,
    @InjectModel(Placeholder.name) private placeholderService: Model<Placeholder>,
  ) { }

  async create(user: any, createAssessmentDto: CreateAssessmentDto) {
    try {
      const createdAssessment = new this.assessmentModel(createAssessmentDto);
      await createdAssessment.save();
      const assessment = await this.assessmentModel.findById(createdAssessment._id)
        .populate({
          path: 'jobApplication',
          populate: [
            {
              path: 'jobId',
              select: '_id title'
            },
            {
              path: 'org',
              select: '_id title'
            }
          ],
          select: '_id jobId firstName lastName contactDetails contactAddress org',
          model: 'JobApplication'
        }).exec();

      if (!assessment) {
        throw new Error('Failed to fetch assessment details after creation.');
      }

      // Attempt to find a specific org-specific template, fallback to default if not found
      let emailTemplate = await this.emailTemplateService.findOne({
        eventName: EmailTemplateEvent.WORKFLOW_ASSESSMENT,
        isDefault: true,
        org: user.org._id,
        isDeleted: false
      }).exec();

      if (!emailTemplate) {
        emailTemplate = await this.emailTemplateService.findOne({
          eventName: EmailTemplateEvent.WORKFLOW_ASSESSMENT,
          canDelete: false,
          org: user.org._id,
          isDeleted: false
        }).exec();
      }

      // Error handling if no template is found
      if (!emailTemplate) {
        throw new Error('Email template not found for the assessment event.');
      }

      // Generate the email content with placeholder replacements
      let body = emailTemplate.templateHTMLContent ? unescape(emailTemplate.templateHTMLContent) : '';
      const placeholders = await this.placeholderService.find({ emailTemplate: emailTemplate._id.toString() }).exec();

      // this.logger.log(JSON.stringify(assessment.toJSON()));
      const istTimeZone = 'Asia/Kolkata';
      placeholders.forEach((placeholder) => {
        const path = placeholder.jsonPath;
        const value = JSONPath({
          path, json: {
            user, data: assessment.toJSON().jobApplication, assessment: {
              ...assessment.toJSON(),
              dueDate: format(
                              toZonedTime(new Date(assessment?.dueDate ?? new Date()), istTimeZone),
                              'MMM dd yyyy, h:mm a'
                            ),
              // dueDate: assessment.dueDate ? format(new Date(assessment.dueDate), 'MMM dd yyyy, h:mm a') : null
            }
          }
        });

        const regex = new RegExp(`#${placeholder.name}`, 'g');
        console.log(`Processing placeholder: ${placeholder.name}`);
        console.log(`JSONPath: ${path}`);
        if (value.length > 0) {
          console.log(`Replacing #${placeholder.name} with: ${value}`);
          body = body.replace(regex, `<b>${value[0]}</b>`);
        }
      });

      // this.logger.log(JSON.stringify(placeholders))

      this.logger.log(`Triggered ${EmailTemplateEvent.WORKFLOW_ASSESSMENT} event`);

      const cleanedHtml = body.replace(/<span[^>]*class="TiptapEditor_mention__[^"]*"[^>]*>(.*?)<\/span>/g, '$1');

      return cleanedHtml
    }
    catch (error) {
      if (error.name === 'ValidationError') {
        this.logger.error(`Validation failed when creating a assessment. ${error.message}`);
        throw new BadRequestException(`Invalid data: ${error.message}`);
      }
      this.logger.error(`Failed to create a assessment. ${error}`);
      throw new InternalServerErrorException(`Error when creating a assessment. ${error?.message}`);
    }
  }

  getAllAssessments(): Promise<AssessmentDocument[]> {
    return this.assessmentModel.find()
      // .populate({ path: 'stages', select: '_id name type sequenceNumber' })
      // .populate({ path: 'org', select: '_id title orgType status legalName' })
      .exec();
  }

  async findOne(assessmentId: Types.ObjectId) {
    try {
      const assessment = await this.assessmentModel.findById(assessmentId)
        // .populate({ path: 'stages', select: '_id name type sequenceNumber' })
        // .populate({ path: 'org', select: '_id title orgType status legalName' })
        .exec();
      if (!assessment) {
        throw new NotFoundException(`The assessment with id: "${assessmentId}" doesn't exist.`);
      }
      return assessment;
    }
    catch (error) {
      this.logger.error(`An error occurred in fetching assessment by Id ${assessmentId}. ${error?.message}`);
      throw error;

    }
  }

  async update(assessmentId: Types.ObjectId, updateAssessmentDto: UpdateAssessmentDto, user: any) {
    try {
      const assessment = await this.assessmentModel.findById(assessmentId).exec();
      if (!assessment) {
        throw new NotFoundException(`The assessment with id: "${assessmentId}" doesn't exist.`);
      }
      const updatedassessment = await this.assessmentModel.findByIdAndUpdate(assessmentId, updateAssessmentDto, { new: true })
        // .populate({ path: 'stages', select: '_id name type sequenceNumber' })
        // .populate({ path: 'org', select: '_id title orgType status legalName' })
        .exec();
      return updatedassessment;
    }
    catch (error) {
      this.logger.error(`An error occurred while updating assessment by ID ${assessmentId}. ${error?.message}`)
      throw error;
    }
  }

  async hardDelete(assessmentId: Types.ObjectId) {
    try {
      const assessment = await this.assessmentModel.findById(assessmentId).exec();
      if (!assessment) {
        throw new NotFoundException(`The assessment with id: "${assessmentId}" doesn't exist.`);
      }
      await this.assessmentModel.findByIdAndDelete(assessmentId);
      return `Assessment deleted`;
    }
    catch (error) {
      this.logger.error(`An error occurred while deleting assessment by ID ${assessmentId}. ${error?.message}`);
      throw error;
    }
  }

  // async assessmentFilter(orgId: Types.ObjectId, isDefault?: boolean) {
  //   try {
  //     let query: any = {
  //       isDefault : false
  //     };
  //     if (orgId) {
  //       query.org = orgId;
  //     }
  //     if (isDefault) {
  //       query.isDefault = isDefault;
  //     }
  //     const assessments = await this.assessmentModel.find(query)
  //     // .populate({ path: 'stages', select: '_id name type sequenceNumber' })
  //     // .populate({ path: 'org', select: '_id title orgType status legalName' })
  //     .exec();
  //     return assessments;
  //   } catch (error) {
  //     this.logger.error(error)
  //     throw new InternalServerErrorException(`An error occurred while filtering assessments. ${error?.message}`);
  //   }

  // }

  emitEvent(eventName: string, payload: any) {
    // payload['event']= eventName;
    this.logger.debug(payload)
    this.eventEmitter.emit(eventName, payload);
  }

}
