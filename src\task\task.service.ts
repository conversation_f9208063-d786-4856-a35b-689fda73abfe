import { BadRequestException, HttpException, HttpStatus, Injectable, InternalServerErrorException, Logger, NotFoundException, Type } from '@nestjs/common';
import { CreateTaskDto } from './dto/create-task.dto';
import { UpdateTaskDto } from './dto/update-task.dto';
import { Task, TaskDocument } from './schemas/task.schema';
import { ConfigService } from '@nestjs/config';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';
import { omit } from 'lodash';
import { CommentDto } from 'src/common/dto/comment.dto';
import moment from 'moment';
import { Comment, CommentDocument } from 'src/common/schemas/comment.schema';
import { ChangeStatusTaskDto } from './dto/change-status-task.dto';
import { MemberJobAssignDto } from 'src/task/dto/assign-job-to-member';
import { validateObjectId } from 'src/utils/validation.utils';
import { VendorJobAssignDto } from 'src/task/dto/assign-job-to-vendor.dto';
import { Role } from 'src/auth/enums/role.enum';

@Injectable()
export class TaskService {

  private readonly logger = new Logger(TaskService.name);

  constructor(private configService: ConfigService, @InjectModel(Task.name) private taskModel: Model<Task>,
    @InjectModel(Comment.name) private commentModel: Model<Comment>, private eventEmitter: EventEmitter2) { }

  async create(createTaskDto: CreateTaskDto): Promise<TaskDocument> {
    this.logger.log(createTaskDto)
    try {

      if (createTaskDto.assignees) {
        createTaskDto.assignees = createTaskDto.assignees.filter(assignee => assignee !== createTaskDto.createdBy);
      }

      const createdTask = new this.taskModel(createTaskDto);
      //assignee = createdBy ->if assignees length is 0 (empty array)
      if (!createdTask.assignees.length || createdTask.assignees.length >= 1) {
        createdTask.assignee = createdTask.createdBy;
        // this.logger.log(createdTask.assignee);
      }
      //assignee =asssigness[0] -> if assignees length is 1
      // if (createdTask.assignees.length == 1) {
      //   createdTask.assignee = createdTask.assignees[0];
      // }

      await createdTask.save();
      const task = await this.findOne(createdTask._id);
      this.emitEvent('task.created', task);
      return createdTask;
    }
    catch (error) {

      this.logger.error(`Failed to create Task. ${error}`);
      throw new InternalServerErrorException(`Error when creating Task. ${error?.message}`);
    }
  }

  async assignJobToMember(user: any, memberId: Types.ObjectId, memberJobAssignDto: MemberJobAssignDto) {
    try {

      const { job } = memberJobAssignDto;
      const jobId = validateObjectId(job);

      const jobToAssign = new this.taskModel({
        ...memberJobAssignDto,
        assignee: memberId,
        job: jobId,
        createdBy: user._id,
        isJobAssign: true
      });

      const assignedJob = await jobToAssign.save();

      this.emitEvent('member.job.assigned', { assignedJob, memberId });

      return assignedJob;
    } catch (error) {
      throw new InternalServerErrorException(`Error while assigning job to member: ${error?.message}`);
    }
  }

  async getAnalytics(jobId: Types.ObjectId) {
    try {
      const query = this.taskModel.find({ job: jobId, isJobAssign: true });
      const populateOptions = [
        { path: 'assignee', select: '_id roles firstName lastName email reportingTo', model: 'BasicUser' },
        { path: 'job', select: '_id title jobType employmentType workflow', model: 'Job' },
        { path: 'org', select: '_id title', model: 'Org' },
      ];

      const tasks = await query.populate(populateOptions);
      let teamCount = 0;
      let memberCount = 0;
      let vendorCount = 0;
      const teamLeads = new Set();

      tasks.forEach(task => {
        if (task.org) {
          vendorCount++;
        }

        if (task.assignee && task.assignee.roles) {
          console.log('Assignee roles:', task.assignee.roles);

          if (task.assignee.roles.includes(Role.TeamMember)) {
            memberCount++;
            teamLeads.add(task.assignee.reportingTo);
          }
          if (task.assignee.roles.includes(Role.TeamLead)) {
            teamCount++;
          }
        }
      });

      teamCount = teamLeads.size;
      return {
        vendorCount,
        teamCount,
        memberCount,
      };
    } catch (error) {
      console.error("Error fetching analytics:", error?.message);
      throw new Error(`Failed to retrieve analytics, ${error?.message}`);
    }
  }

  async getAllMemberAssignedJobs(memberId: Types.ObjectId): Promise<TaskDocument[]> {
    try {
      const assignedJobs = await this.taskModel.find({ assignee: memberId, isJobAssign: true })
        .populate({ path: 'assignee', select: '_id roles firstName lastName email reportingTo', model: 'BasicUser' })
        .populate({ path: 'job', select: '_id title jobType employmentType workflow', model: 'Job' })
        .exec();
      return assignedJobs;
    } catch (error) {
      throw new InternalServerErrorException(`Error while retrieving assigned jobs: ${error?.message}`);
    }
  }

  async getAssignedJobs(memberId: Types.ObjectId, jobId: Types.ObjectId): Promise<TaskDocument[]> {
    try {
      const assignedJobs = await this.taskModel.find({ assignee: memberId, job: jobId, isJobAssign: true })
        .populate({ path: 'assignee', select: '_id roles firstName lastName email reportingTo', model: 'BasicUser' })
        .populate({ path: 'job', select: '_id title jobType employmentType workflow', model: 'Job' })
        .exec();
      return assignedJobs;
    } catch (error) {
      throw new InternalServerErrorException(`Error while retrieving assigned jobs: ${error?.message}`);
    }
  }

  async unassignJob(memberId: Types.ObjectId, jobId: Types.ObjectId) {
    try {
      const result = await this.taskModel.updateOne(
        { job: jobId, assignee: memberId, isJobAssign: true }, // Use 'assignee'
        { $unset: { assignee: "" } } // Unset the assignee
      );

      if (result.modifiedCount === 0) { // Change to modifiedCount
        throw new NotFoundException(`No job found with ID ${jobId} assigned to member ID ${memberId}.`);
      }

      return { message: 'Job unassigned successfully.' };
    } catch (error) {
      throw new InternalServerErrorException(`Error while unassigning job: ${error?.message}`);
    }
  }

  async assignJobToVendor(user: any, vendorId: Types.ObjectId, vendorJobAssignDto: VendorJobAssignDto) {
    try {

      const { job } = vendorJobAssignDto;
      const jobId = validateObjectId(job);

      const taskToAssign = new this.taskModel({
        ...vendorJobAssignDto,
        org: vendorId,
        job: jobId,
        createdBy: user._id,
        isJobAssign: true
      });

      const assignedJob = await taskToAssign.save();
      return assignedJob;
    } catch (error) {
      throw new InternalServerErrorException(`Error while assigning job to vendor: ${error.message}`);
    }
  }

  async getAllVendorAssignedJobs(vendorId: Types.ObjectId) {
    try {

      const assignedJobs = await this.taskModel.find({ org: vendorId, isJobAssign: true })
        .populate({ path: 'assignee', select: '_id roles firstName lastName email reportingTo', model: 'BasicUser' })
        .populate({ path: 'org', select: '_id title', model: 'Org' })
        .exec();
      return assignedJobs;
    } catch (error) {
      throw new InternalServerErrorException(`Error while retrieving assigned jobs: ${error.message}`);
    }
  }

  async getVendorAssignedJobs(vendorId: Types.ObjectId, jobId: Types.ObjectId) {
    try {

      const assignedJobs = await this.taskModel.find({ org: vendorId, job: jobId, isJobAssign: true })
        .populate({ path: 'assignee', select: '_id roles firstName lastName email reportingTo', model: 'BasicUser' })
        .populate({ path: 'org', select: '_id title', model: 'Org' })
        .exec();
      return assignedJobs;
    } catch (error) {
      throw new InternalServerErrorException(`Error while retrieving assigned jobs: ${error.message}`);
    }
  }

  async unassignVendorJob(vendorId: Types.ObjectId, jobId: Types.ObjectId) {
    try {

      const result = await this.taskModel.updateOne(
        { job: jobId, org: vendorId },
        { $unset: { org: "" } }
      );

      if (result.modifiedCount === 0) {
        throw new NotFoundException(`No job found with ID ${jobId} assigned to vendor ID ${vendorId}.`);
      }

      return { message: 'Job unassigned successfully.' };
    } catch (error) {
      throw new InternalServerErrorException(`Error while unassigning job: ${error.message}`);
    }
  }



  async createSubTask(createTaskDto: CreateTaskDto, parentTaskId: Types.ObjectId): Promise<TaskDocument> {
    try {

      const createdTask = new this.taskModel(createTaskDto);
      createdTask.parentTask = parentTaskId;
      //assignee = createdBy ->if assignees length is 0 (empty array)
      if (!createdTask.assignees.length || createdTask.assignees.length > 1) {
        createdTask.assignee = createdTask.createdBy;
        // this.logger.log(createdTask.assignee);
      }
      //assignee =asssigness[0] -> if assignees length is 1
      if (createdTask.assignees.length == 1) {
        createdTask.assignee = createdTask.assignees[0];
      }

      await createdTask.save();
      const task = await this.taskModel.findById(createdTask._id)
        .populate({ path: 'createdBy', select: '_id roles firstName' })
        .populate({ path: 'org', select: '_id title' })
        .populate({ path: 'spoc', select: '_id firstName lastName ' })
        .populate({ path: 'assignees', select: '_id roles firstName email', model: 'BasicUser' })
        .populate({ path: 'assignee', select: '_id roles firstName email', model: 'BasicUser' })
        .exec();
      this.emitEvent('task.created', task);
      return createdTask;
    }
    catch (error) {
      this.logger.error(`Failed to create Task. ${error}`);
      throw new InternalServerErrorException(`Error while creating a sub-task. ${error?.message}`);
    }
  }

  findAll(page: number, limit: number): Promise<TaskDocument[]> {
    return this.taskModel.find()
      .populate({ path: 'createdBy', select: '_id roles firstName' })
      .populate({ path: 'assignee', select: '_id roles firstName email' })
      .populate({ path: 'org', select: '_id title' })
      .populate({ path: 'spoc', select: '_id firstName lastName ' })
      .populate({ path: 'assignees', select: '_id roles firstName email', model: 'BasicUser' })
      .skip((page - 1) * limit)
      .limit(limit)
      .sort({ createdAt: -1 })
      .exec();
  }

  async findOne(taskId: Types.ObjectId): Promise<TaskDocument> {
    try {
      const task = await this.taskModel.findById(taskId)
        .populate({ path: 'createdBy', select: '_id roles firstName' })
        .populate({ path: 'org', select: '_id title' })
        .populate({ path: 'spoc', select: '_id firstName lastName ' })
        .populate({ path: 'assignees', select: '_id roles firstName email', model: 'BasicUser' })
        .populate({ path: 'assignee', select: '_id roles firstName email', model: 'BasicUser' })
        .exec();

      if (!task) {
        throw new NotFoundException(`The task with id: "${taskId}" doesn't exist.`);
      }
      return task;
    }
    catch (error) {
      this.logger.error(`An error occurred in fetching task by Id ${taskId}. ${error?.message}`);
      throw error;

    }
  }

  async getUserTasksByDate(user: any, search?: string) {
    try {
      const now = new Date();

      // Start and end of today in UTC
      const startOfDay = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate(), 0, 0, 0, 0));
      const endOfDay = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate(), 23, 59, 59, 999));

      // // Base query to find tasks assigned to the user
      // const query: any = {
      //   $or: [
      //     { createdBy: user._id },
      //     { assignee: user._id },
      //     { assignees: { $in: [user._id] } }
      //   ]
      // };

      const userIdStr = user._id.toString();
      const userIdObj = new Types.ObjectId(userIdStr);

      const query: any = {
        $or: [
          { createdBy: userIdStr },
          { createdBy: userIdObj },
          { assignee: userIdStr },
          { assignee: userIdObj },

        ]
      };

      // Fetch all assigned tasks
      const tasks = await this.taskModel.find(query)
        .populate({ path: 'createdBy', select: '_id roles firstName lastName' })
        .populate({ path: 'assignee', select: '_id roles firstName lastName email' })
        .populate({ path: 'assignees', select: '_id roles firstName lastName email', model: 'BasicUser' })
        .populate({ path: 'org', select: '_id title' })
        .populate({ path: 'spoc', select: '_id firstName lastName' })
        .sort({ createdAt: -1 })
        .exec();

      // If search is provided, filter tasks based on the search keyword
      if (search) {
        const searchResults = tasks.filter(task =>
          task.title.toLowerCase().includes(search.toLowerCase())
        );
        return searchResults;
      }

      // Categorize tasks
      const todayTasks = tasks.filter(task => {
        const taskDueDate = new Date(task.dueDate);
        return taskDueDate >= startOfDay && taskDueDate <= endOfDay;
      });

      const upcomingTasks = tasks.filter(task => {
        const taskDueDate = new Date(task.dueDate);
        return taskDueDate > endOfDay;
      });

      const pastTasks = tasks.filter(task => {
        const taskDueDate = new Date(task.dueDate);
        return taskDueDate < startOfDay;
      });

      // Build notificationTasks (up to 10 tasks)
      let notificationTasks = [...todayTasks].slice(0, 10);
      if (notificationTasks.length < 10) {
        notificationTasks = notificationTasks.concat(upcomingTasks.slice(0, 10 - notificationTasks.length));
      }

      return {
        todayTasks,
        upcomingTasks,
        pastTasks,
        notificationTasks,
        totalToday: todayTasks.length,
        totalUpcoming: upcomingTasks.length,
        totalPast: pastTasks.length,
        totalNotifications: notificationTasks.length
      };

    } catch (error) {
      this.logger.error(`Error fetching user tasks: ${error.message}`, error.stack);
      throw new InternalServerErrorException('Failed to retrieve tasks.');
    }
  }

  async findCreatedTasks(userId: Types.ObjectId, page: number, limit: number): Promise<TaskDocument[]> {
    try {
      this.logger.debug('Finding tasks for user:', userId)
      return await this.taskModel.find({ createdBy: userId })
        .populate({ path: 'createdBy', select: '_id roles firstName lastName' })
        .populate({ path: 'org', select: '_id title' })
        .populate({ path: 'spoc', select: '_id firstName lastName ' })
        .populate({ path: 'assignees', select: '_id roles firstName lastName email', model: 'BasicUser' })
        .populate({ path: 'assignee', select: '_id roles firstName lastName email' })
        .skip((page - 1) * limit)
        .limit(limit)
        .exec();

    } catch (error) {
      throw new Error(`Error fetching Tasks with user id: ${userId}. ${error.message}`);
    }
  }


  async findByContactAndOrg(
    page: number,
    limit: number,
    orgId?: string,
    vendorId?: string,
    contactId?: string,
    filter?: string,
    specificDate?: string
  ): Promise<TaskDocument[]> {
    try {
      // Initialize the query object
      const query: any = {};

      if (contactId) {
        query.spoc = contactId;
      }

      if (orgId) {
        query.org = orgId;
      }
      
      if (vendorId) {
        query.vendor = vendorId;
      }

      // Retrieve all tasks with the specified filters
      const allTasks = await this.taskModel.find(query)
        .populate({ path: 'createdBy', select: '_id roles firstName lastName' })
        .populate({ path: 'org', select: '_id title' })
        .populate({ path: 'spoc', select: '_id firstName lastName' })
        .populate({ path: 'assignees', select: '_id roles firstName lastName email', model: 'BasicUser' })
        .populate({ path: 'assignee', select: '_id roles firstName lastName email' })
        .exec();

      // Initialize 'now' variable for date filtering
      const now = new Date();

      // Apply the date filter to the tasks
      let filteredTasks: TaskDocument[] = [];
      switch (filter) {
        case 'today':
          const startOfDay = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate(), 0, 0, 0, 0)).toISOString();
          const endOfDay = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate(), 23, 59, 59, 999)).toISOString();
          filteredTasks = allTasks.filter(task => {
            const taskDueDate = new Date(task.dueDate).toISOString();
            return taskDueDate >= startOfDay && taskDueDate <= endOfDay;
          });
          break;

        case 'specificDate':
          if (!specificDate) {
            throw new Error('specificDate is required for the specificDate filter');
          }
          const givenDate = new Date(specificDate);
          const startTime = new Date(Date.UTC(givenDate.getUTCFullYear(), givenDate.getUTCMonth(), givenDate.getUTCDate(), 0, 0, 0, 0)).toISOString();
          const endTime = new Date(Date.UTC(givenDate.getUTCFullYear(), givenDate.getUTCMonth(), givenDate.getUTCDate(), 23, 59, 59, 999)).toISOString();
          filteredTasks = allTasks.filter(task => {
            const taskDueDate = new Date(task.dueDate).toISOString();
            return taskDueDate >= startTime && taskDueDate <= endTime;
          });
          break;

        case 'upcoming':
          const endOfToday = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate(), 23, 59, 59, 999)).toISOString();
          filteredTasks = allTasks.filter(task => {
            const taskDueDate = new Date(task.dueDate).toISOString();
            return taskDueDate > endOfToday;
          });
          break;

        default:
          filteredTasks = allTasks; // No date filter applied
          break;
      }

      // Apply pagination
      const paginatedTasks = filteredTasks.slice((page - 1) * limit, page * limit);

      return paginatedTasks;
    } catch (error) {
      throw new InternalServerErrorException(`An error occurred while fetching tasks by contact and org. ${error?.message}`);
    }
  }



  async findChildTasks(parentTaskId: Types.ObjectId, page?: number, limit?: number): Promise<TaskDocument[]> {
    try {
      if (!limit) {
        limit = 0
      }
      if (!page) {
        page = 1
      }
      this.logger.debug('Finding child tasks for the parent task id:', parentTaskId)
      return await this.taskModel.find({ parentTask: parentTaskId })
        .populate({ path: 'createdBy', select: '_id roles firstName lastName' })
        .populate({ path: 'org', select: '_id title' })
        .populate({ path: 'spoc', select: '_id firstName lastName ' })
        .populate({ path: 'assignees', select: '_id roles firstName lastName email', model: 'BasicUser' })
        .populate({ path: 'assignee', select: '_id roles firstName lastName email' })
        .skip((page - 1) * limit)
        .limit(limit)
        .exec();


    } catch (error) {
      throw new Error(`Error fetching child Tasks with parent task id: ${parentTaskId}. ${error.message}`);
    }
  }

  async update(taskId: Types.ObjectId, updateTaskDto: UpdateTaskDto, user: Object) {

    const { dueDate, priority, title, spoc, org } = updateTaskDto;
    try {
      const task = await this.taskModel.findById(taskId);

      if (!task) {
        throw new NotFoundException(`The task with id: "${taskId}" doesn't exist.`);
      }

      const updatedTask = await this.taskModel.findByIdAndUpdate(taskId, updateTaskDto, { new: true })
        .populate({ path: 'createdBy', select: '_id roles firstName lastName' })
        .populate({ path: 'org', select: '_id title' })
        .populate({ path: 'spoc', select: '_id firstName lastName ' })
        .populate({ path: 'assignees', select: '_id roles firstName lastName email', model: 'BasicUser' })
        .populate({ path: 'assignee', select: '_id roles firstName lastName email', model: 'BasicUser' })
        .exec();
      if (title) {
        this.emitEvent('task.title.updated', { updatedTask, user });
      }
      if (priority) {
        this.emitEvent('task.priority.updated', { updatedTask, user });
      }
      if (dueDate) {
        this.emitEvent('task.duedate.updated', { updatedTask, user });
      }
      if (spoc) {
        this.emitEvent('task.spoc.updated', { updatedTask, user });
      }
      if (org) {
        this.emitEvent('task.org.updated', { updatedTask, user });
      }
      return updatedTask;
    }
    catch (error) {
      this.logger.error(`An error occurred while updating task by ID ${taskId}. ${error?.message}`);
      throw error;
    }
  }

  async assignTo(taskId: Types.ObjectId, assignees: string[], commentDto: CommentDto, user: any) {
    try {
      const task = await this.taskModel.findById(taskId);
      // this.logger.debug(assignee);
      if (!task) {
        throw new NotFoundException(`The task with id: "${taskId}" doesn't exist.`);
      }
      // this.logger.debug(comment);

      const updatedTask = await this.taskModel.findByIdAndUpdate(taskId, { assignees: assignees }, { new: true })
        .populate({ path: 'createdBy', select: '_id roles firstName lastName' })
        .populate({ path: 'org', select: '_id title' })
        .populate({ path: 'spoc', select: '_id firstName lastName ' })
        .populate({ path: 'assignees', select: '_id roles firstName lastName email', model: 'BasicUser' })
        .populate({ path: 'assignee', select: '_id roles firstName lastName email', model: 'BasicUser' })
        .exec();
      // this.logger.log(updatedTask)
      // this.emitEvent('task.assignee.changed', { updatedTask, commentDto, user });
      // return updatedTask;

      const lastTask = await this.taskModel
        .findOne({})
        .sort({ taskCode: -1 })
        .select('taskCode')
        .lean();

      let nextTaskCode = lastTask?.taskCode !== undefined ? lastTask.taskCode + 1 : (task.taskCode || 1);

      const newTasks = assignees.map((assigneeId) => {
        return {
          title: task.title,
          taskCode: nextTaskCode++,
          parentTask: task._id,
          dueDate: task.dueDate,
          spoc: task.spoc,
          createdBy: user._id,
          assignee: assigneeId,
          assignees: assignees,
          priority: task.priority,
          status: 'TO-DO', // or 'PENDING'
          isDeleted: false,
          isJobAssign: task.isJobAssign,
        };
      });
  
      const insertedTasks = await this.taskModel.insertMany(newTasks);
  
      // Optionally emit events for each task
      insertedTasks.forEach((task) => {
        this.emitEvent('task.assignee.changed', { task, commentDto, createdBy: user });
      });
  
      return insertedTasks;

    }
    catch (error) {
      this.logger.error(`An error occurred while assigning the task ${error?.message}`);
      throw error;
    }
  }

  async changeStatus(taskId: Types.ObjectId, changeStatusTaskDto: ChangeStatusTaskDto, user: object) {
    try {
      const task = await this.taskModel.findById(taskId);
      if (!task) {
        throw new NotFoundException(`The task with id: "${taskId}" doesn't exist.`);
      }

      const { status, comment } = changeStatusTaskDto;

      //If all subtasks are completed, you can proceed with completing the parent task
      if (status === 'COMPLETED') {
        let childTasks: any[] = [];
        childTasks = await this.findChildTasks(taskId);

        for (const task of childTasks) {
          if (task.status !== 'COMPLETED') {
            throw new BadRequestException(`Cannot complete the parent task. Subtask with ID ${task._id} is not completed.`);
          }
        }
      }

      const updatedTask = await this.taskModel.findByIdAndUpdate(taskId, { status }, { new: true })
        .populate({ path: 'createdBy', select: '_id roles firstName lastName' })
        .populate({ path: 'org', select: '_id title' })
        .populate({ path: 'spoc', select: '_id firstName lastName ' })
        .populate({ path: 'assignees', select: '_id roles firstName lastName email', model: 'BasicUser' })
        .populate({ path: 'assignee', select: '_id roles firstName lastName email', model: 'BasicUser' })
        .exec();

      this.emitEvent('task.status.changed', { updatedTask, comment, user });
      return updatedTask;
    } catch (error) {
      // this.logger.error(`An error occurred while updating the task status. ${error?.message}`);
      throw error;
    }
  }

  //TODO : ACTOR:req.user_id
  async remove(taskId: Types.ObjectId, user: object) {
    try {
      const task = await this.taskModel.findById(taskId)
        .populate({ path: 'createdBy', select: '_id roles firstName' })
        .populate({ path: 'org', select: '_id title' })
        .populate({ path: 'spoc', select: '_id firstName lastName ' })
        .populate({ path: 'assignees', select: '_id roles firstName email', model: 'BasicUser' })
        .populate({ path: 'assignee', select: '_id roles firstName email', model: 'BasicUser' })
        .exec();
      if (!task) {
        throw new NotFoundException(`The task with id: "${taskId}" doesn't exist.`);
      }
      await this.taskModel.findByIdAndDelete(taskId);

      this.emitEvent('task.removed', { task, user });
      return `task deleted`;
    }
    catch (error) {
      this.logger.error(`An error occurred while deleting task by ID ${taskId}. ${error?.message}`);
      throw error;
    }
  }

  async dateFilter(user: any, filter: string, specificDate: string, page: number, limit: number) {
    const now = new Date();
    // Set start and end of the day in UTC
    const startOfDay = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate(), 0, 0, 0, 0)).toISOString();
    const endOfDay = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate(), 23, 59, 59, 999)).toISOString();

    // this.logger.log(startOfDay, endOfDay)
    // Retrieve all tasks with populations
    const Tasks = await this.taskModel.find({
      $or: [
        { createdBy: user._id },
        { assignee: user._id },
        { assignees: { $in: [user._id] } }
      ]
    })
      .populate({ path: 'createdBy', select: '_id roles firstName' })
      .populate({ path: 'assignee', select: '_id roles firstName email' })
      .populate({ path: 'org', select: '_id title' })
      .populate({ path: 'spoc', select: '_id firstName lastName' })
      .populate({ path: 'assignees', select: '_id roles firstName email', model: 'BasicUser' })
      .exec();

    let filteredTasks: TaskDocument[] = [];
    switch (filter) {
      case 'today':
        filteredTasks = Tasks.filter(task => {
          const taskDueDate = new Date(task.dueDate).toISOString();
          return taskDueDate > startOfDay && taskDueDate < endOfDay;
        });
        this.logger.debug(`Today's filter conditions`);
        break;

      case 'specificDate':
        const givenDate = new Date(specificDate);

        // Set start and end of the given date in UTC
        const startTime = new Date(Date.UTC(givenDate.getUTCFullYear(), givenDate.getUTCMonth(), givenDate.getUTCDate(), 0, 0, 0, 0)).toISOString();
        const endTime = new Date(Date.UTC(givenDate.getUTCFullYear(), givenDate.getUTCMonth(), givenDate.getUTCDate(), 23, 59, 59, 999)).toISOString();

        filteredTasks = Tasks.filter(task => {
          const taskDueDate = new Date(task.dueDate).toISOString();
          return taskDueDate >= startTime && taskDueDate <= endTime;
        });
        // this.logger.debug(startOfDay,endOfDay);
        break;

      case 'upcoming':
        filteredTasks = Tasks.filter(task => {
          const taskDueDate = new Date(task.dueDate).toISOString();
          return taskDueDate > endOfDay;
        });
        break;

      default:
        throw new Error('Invalid filter specified');
    }
    // this.logger.debug(filteredTasks)
    return filteredTasks

  }

  async recurringTasks(page: number, limit: number) {
    const startTime = moment.utc(moment.utc().startOf('day').toISOString()); //(00:00:00)
    const endTime = moment.utc(moment.utc().endOf('day').toISOString()); //(23:59:59).
    const tasksArray = await this.taskModel.find({ recurrenceInterval: { $exists: true } }).exec()
    const todaysTasks = []
    for (const task of tasksArray) {

      let taskDueDate = moment.utc(task.dueDate);

      //If the task due date is passed this condition will update the task due date
      if (taskDueDate.isBefore(startTime)) {
        // this.logger.log(`task due date is passed  - ${task.title}`);
        switch (task.recurrenceInterval) {
          case 'DAILY':
            task.dueDate = moment.utc(task.dueDate).add(1, 'day').toDate();
            break;
          case 'MONTHLY':
            task.dueDate = moment.utc(task.dueDate).add(1, 'month').toDate();
            break;
          case 'YEARLY':
            task.dueDate = moment.utc(task.dueDate).add(1, 'year').toDate();
            break;
        }
        await task.save();
        taskDueDate = moment.utc(task.dueDate);
      }
      //If the task due date is today between 00:00:00 and 23:59:59
      if (taskDueDate.isBetween(startTime, endTime)) {
        this.logger.log(`Today is the due date - ${task.title}`)
        todaysTasks.push(task)
      }
      //If the task due date is in future 
      else if (taskDueDate.isAfter(endTime)) {
        this.logger.log(`task due date is in future - ${task.title}`);
      }

    };
    this.logger.log(todaysTasks)
  }


  async getTasksCountByStatus(status: string, userId: Types.ObjectId) {
    try {
      // assignees: { $in: [userId] }
      // const userTasks = await this.taskModel.find({ assignee: userId, status: status })

      const count = await this.taskModel.countDocuments({ assignee: userId, status: status })
      // const count = await this.taskModel.estimatedDocumentCount()

      return { count: count };

    } catch (error) {
      throw new Error(`Error fetching Task with ${userId}. ${error.message}`);
    }
  }

  async addComment(taskId: Types.ObjectId, commentDto: CommentDto): Promise<CommentDocument> {
    try {
      const createdComment = new this.commentModel(commentDto);
      createdComment.taskId = taskId;
      return await createdComment.save();
    }
    catch (error) {
      this.logger.error(`Failed to create comment. ${error}`);
      throw new InternalServerErrorException(`Error while creating a comment. ${error?.message}`);
    }
  }

  async getComments(taskId: Types.ObjectId): Promise<Comment[]> {
    return this.commentModel.find({ taskId: taskId })
      .populate({ path: 'user', select: '_id roles firstName' })
      .populate({ path: 'attachments', select: '_id originalName fileSize fileType locationUrl', model: 'FileMetadata' }).exec();
  }

  async searchTasks(name: string) {
    try {
      if (!name) {
        throw new HttpException('Name parameter is required', HttpStatus.BAD_REQUEST);
      }
      const regex = new RegExp(name, 'i'); // 'i' for case-insensitive
      return await this.taskModel.find({ title: regex })
        .populate({ path: 'createdBy', select: '_id roles firstName' })
        .populate({ path: 'org', select: '_id title' })
        .populate({ path: 'spoc', select: '_id firstName lastName ' })
        .populate({ path: 'assignees', select: '_id roles firstName email', model: 'BasicUser' })
        .populate({ path: 'assignee', select: '_id roles firstName email', model: 'BasicUser' })
        .exec();
    } catch (error) {
      this.logger.error(`Error while searching for tasks: ${error.message}`, error.stack);
      throw new InternalServerErrorException(`Error while searching for tasks: ${error?.message}`);
    }
  }

  @OnEvent('task.created', { async: true })
  async onTaskCreatedEvent(payload: any) {

    // https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/all
    // start

    // use a try catch
    try {
      let taskCreated: CreateTaskDto = payload;
      if (!taskCreated) {
        throw new NotFoundException(`An error occured while creating a sub-tasks with task Id ${payload._id}.`);
      }
      let assignees = taskCreated.assignees;
      if (assignees.length >= 1) {

        let promises: any[] = [];

        for (let idx = 0; idx < assignees.length; idx++) {
          const assignee = assignees[idx];
          let taskDto: any = omit(taskCreated, ['_id', '__v', 'code']); // read about omit here - https://lodash.com/docs/4.17.15#omit
          taskDto.assignee = assignee;
          taskDto.parentTask = payload._id;
          let prom = this.createChildTask(taskDto);
          promises.push(prom);
        }
        // use a try catch
        let results = await Promise.all(promises);
        if (!(results.length)) {
          throw new BadRequestException(`An error occurred while creating sub-tasks. promise is rejected.`);
        }
        this.logger.debug(results?.length);

      }

    }
    catch (error) {
      this.logger.error(`An error occurred while creating sub-tasks ${error.message}.`);
      throw error;
    }

    // end


    // TODO: handle post task creation events like - creating sub tasks or updating assignees or sending email alerts. 
    // when there are more things to be done, use separate handlers, with the event name in other services.
  }

  emitEvent(eventName: string, payload: any) {
    // payload['event']= eventName;
    // this.logger.debug(payload)
    this.eventEmitter.emit(eventName, payload);
  }

  async createChildTask(taskDto: CreateTaskDto) {
    try {
      const createdTask = new this.taskModel(taskDto);
      await createdTask.save();
      return createdTask;
    } catch (error) {
      throw error;
    }
  }

  async getRecruiterTaskCounts(user: any) {
    try {
      const now = new Date();

      // Define UTC start and end of today
      const startOfDay = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate(), 0, 0, 0, 0)).toISOString();
      const endOfDay = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate(), 23, 59, 59, 999)).toISOString();

      // Fetch all tasks assigned to the recruiter
      const recruiterTasks = await this.taskModel.find({
        $or: [
          { createdBy: user._id },
          { assignee: user._id },
          { assignees: { $in: [user._id] } }
        ],
        status: { $ne: "COMPLETED" }
      }).select('dueDate').exec();

      // Count tasks based on due dates
      let todayTasksCount = 0;
      let upcomingTasksCount = 0;

      recruiterTasks.forEach(task => {
        const taskDueDate = new Date(task.dueDate).toISOString();
        if (taskDueDate >= startOfDay && taskDueDate <= endOfDay) {
          todayTasksCount++;
        } else if (taskDueDate > endOfDay) {
          upcomingTasksCount++;
        }
      });

      return {
        todayTasksCount,
        upcomingTasksCount
      };

    } catch (error) {
      this.logger.error(`Error retrieving recruiter task counts: ${error.message}`, error.stack);
      throw new InternalServerErrorException(`Error retrieving recruiter task counts. ${error.message}`);
    }
  }
}
