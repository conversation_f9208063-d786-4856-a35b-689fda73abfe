import { ApiProperty } from "@nestjs/swagger";
import { IsBoolean, IsNotEmpty, IsNumber, IsOptional } from "class-validator";
import { CreateJobAllocationBaseDto } from "./create-job-allocation.dto";

export class CreateJobAllocationToAiRecruiterDto extends CreateJobAllocationBaseDto {
    @ApiProperty({ type: Boolean, required: false, default: true, description: 'Is job available in AiRecruiter pool' })
    @IsBoolean()
    @IsOptional()
    isAvailableInAiRecruiterPool?: boolean;

    @ApiProperty({ type: Boolean, required: false, default: true, description: 'Complete Access to AI' })
    @IsBoolean()
    @IsOptional()
    uptoAIRecruiter?: boolean;
}
