import { forwardRef, Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { JobAllocationService } from './job-allocation.service';
import { JobAllocationController } from './job-allocation.controller';
import { JobAllocationToAssignees, JobAllocationToAssigneesSchema } from './schemas/job-allocation-to-assignees.schema';
import { JobAllocationToFreelancers, JobAllocationToFreelancersSchema } from './schemas/job-allocation-to-freelancers.schema';
import { JobAllocationToVendors, JobAllocationToVendorsSchema } from './schemas/job-allocation-to-vendors.schema';
import { JobAllocationBase, JobAllocationBaseSchema } from './schemas/job-allocation-base.schema';
import { JwtModule } from '@nestjs/jwt';
import { AuthModule } from 'src/auth/auth.module';
import { CommonModule } from 'src/common/common.module';
import { EndpointsRolesModule } from 'src/endpoints-roles/endpoints-roles.module';
import { JobModule } from 'src/job/job.module';
import { NotificationsModule } from 'src/notification/notifications.module';
import { AIRecruiterQA, AIRecruiterQASchema } from './schemas/ai-recruiter-qa.schema';
import { JobAllocationToAiRecruiters, JobAllocationToAiRecruitersSchema } from './schemas/job-allocation-to-aiRecruiter.schema';


@Module({
  imports: [
    JwtModule.register({}),
    AuthModule,
    CommonModule,
    EndpointsRolesModule,
    forwardRef(() => JobModule),
    forwardRef(() => NotificationsModule),
    MongooseModule.forFeature([
      { name: JobAllocationBase.name, schema: JobAllocationBaseSchema },
      { name: JobAllocationToAssignees.name, schema: JobAllocationToAssigneesSchema },
      { name: JobAllocationToVendors.name, schema: JobAllocationToVendorsSchema },
      { name: JobAllocationToFreelancers.name, schema: JobAllocationToFreelancersSchema },
      { name: JobAllocationToAiRecruiters.name, schema: JobAllocationToAiRecruitersSchema },
      { name: AIRecruiterQA.name, schema: AIRecruiterQASchema },
    ]),
  ],
  controllers: [JobAllocationController],
  providers: [JobAllocationService],
  exports: [JobAllocationService,MongooseModule],

})
export class JobAllocationModule { }
