import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';

import { HydratedDocument, Types } from 'mongoose';
import { Country } from 'src/country/schemas/country.schema';
import { State } from 'src/state/schemas/state.schema';
import { City } from 'src/state/schemas/city.schema';
export type ClientAddressDocument = HydratedDocument<ClientAddress>;

@Schema({
    _id: false,
    timestamps: true
})
export class ClientAddress {

    @Prop({
        type: Types.ObjectId,
        required: true,
        ref: 'Country'
    })
    country: Types.ObjectId;

    @Prop({
        type: Types.ObjectId,
        required: true,
        ref: 'State'
    })
    state: Types.ObjectId;

    @Prop({
        type: Types.ObjectId,
        required: true,
        ref: 'City'
    })
    city: Types.ObjectId;

    @Prop({
        type: String,
        required: true,
        trim: true
    })
    postalCode: string;

    @Prop({
        type: String,
        required: true,
        trim: true
    })
    apartment: string;

    @Prop({
        type: Boolean,
        required: false,
        default: false
    })
    sez?: boolean; // Special Economic Zone - yes/no

    @Prop({
        type: String,
        required: false,
        trim: true
    })
    gstNumber: string; // GST Number for taxation

    @Prop({
        type: Boolean,
        required: false,
        default: false
    })
    registeredAddress?: boolean;
}

export const ClientAddressSchema = SchemaFactory.createForClass(ClientAddress);
