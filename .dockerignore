# Node modules (will be installed inside Docker)
node_modules
npm-debug.log
yarn-error.log
pnpm-lock.yaml

# Dependency lock files (optional if installing fresh)
package-lock.json

# Build output
dist
build

# OS and editor junk
.DS_Store
Thumbs.db

# Git
.git
.github
.gitignore

# Environment files (copy only if needed)
.env
.env.*

# Dockerfile and docker-compose files
docker-compose.*
Dockerfile.*

# Logs and temp files
*.log
tmp
*.tmp

# IDEs and editors
.vscode
.idea
*.swp