import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { Org } from 'src/org/schemas/org.schema';
import { BasicUser } from 'src/user/schemas/basic-user.schema';

export type BgvHandlerDocument = BgvHandler & Document;

@Schema({ timestamps: true })
export class BgvHandler {
    @Prop({
        type: String,
        required: true,
        lowercase: true,
        trim: true
    })
    email: string;

    @Prop({
        type: String,
        required: false,
        trim: true,
    })
    title: string;

    @Prop({
        type: String,
        required: false,
        trim: true,
    })
    contactNumber?: string;

    @Prop({
        type: Boolean,
        required: false,
        default: false
    })
    isDeleted?: boolean;

    @Prop({
        type: Boolean,
        required: false,
        default: false
    })
    isDefault?: boolean;

    @Prop({
        required: true,
        type: Types.ObjectId, ref: 'BasicUser'
    })
    createdBy: BasicUser;

    @Prop({
        type: Types.ObjectId,
        ref: 'Org',
        required: false
    })
    org?: Org;

    @Prop({
        type: [String], // keys like 'passport_photo', 'qualification_certificates'
        default: [],
    })
    documentKeys: string[];

    @Prop({
        type: [{ type: Types.ObjectId, ref: 'BusinessUnit' }],
        required: false,
    })
    assignToDepartment?: string[];

    @Prop({
        type: [{ type: Types.ObjectId, ref: 'Org' }],
        required: false,
    })
    assignToOrg?: string[];

}

export const BgvHandlerSchema = SchemaFactory.createForClass(BgvHandler);
