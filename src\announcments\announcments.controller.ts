import { Controller, Get, Post, Body, Patch, Param, Delete, Logger, UseGuards, Req, Query } from '@nestjs/common';
import { AnnouncmentsService } from './announcments.service';
import { CreateAnnouncmentDto } from './dto/create-announcment.dto';
import { UpdateAnnouncmentDto } from './dto/update-announcment.dto';
import { ApiBearerAuth, ApiQuery, ApiTags } from '@nestjs/swagger';
import { AuthJwtGuard } from 'src/auth/guards/auth-jwt/auth-jwt.guard';
import { validateObjectId } from 'src/utils/validation.utils';

@Controller()
@ApiTags('Announcments')
export class AnnouncmentsController {
  constructor(private readonly announcmentsService: AnnouncmentsService) { }

  private readonly logger = new Logger(AnnouncmentsController.name);

  @Post()
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  create(@Req() req: any, @Body() createAnnouncmentDto: CreateAnnouncmentDto) {
    createAnnouncmentDto.createdBy = req.user._id
    createAnnouncmentDto.org = req.user.org._id
    return this.announcmentsService.create(createAnnouncmentDto);
  }

  @Get('getAnnouncments')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  @ApiQuery({ name: 'jobId', required: false, type: String, description: 'ID of the job filter by' })
  findAll(@Req() req: any, @Query('page') page = '1', @Query('limit') limit = '10',@Query('jobId') jobId?: string,) {
    return this.announcmentsService.findAll(
      req.user._id,
      req.user.org._id,
      parseInt(page, 10),
      parseInt(limit, 10),
      jobId

    );
  }

  @Get(':announcmentId')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  findOne(@Param('announcmentId') announcmentId: string) {
    return this.announcmentsService.findOne(announcmentId);
  }

  @Get('/assigned')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  getForAssignee(@Req() req: any) {
    return this.announcmentsService.findForAssignee(req.user._id);
  }



  @Patch(':announcmentId')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  update(@Param('announcmentId') announcmentId: string, @Body() updateAnnouncmentDto: UpdateAnnouncmentDto) {
    return this.announcmentsService.update(announcmentId, updateAnnouncmentDto);
  }



  @Delete(':announcmentId')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  remove(@Param('announcmentId') announcmentId: string) {
    const objId = validateObjectId(announcmentId);
    return this.announcmentsService.remove(objId);
  }
}
