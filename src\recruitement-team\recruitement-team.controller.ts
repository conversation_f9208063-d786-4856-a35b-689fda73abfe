import { Controller, Get, Post, Body, Patch, Param, Delete, Logger, UseGuards, BadRequestException, Req } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { AuthJwtGuard } from 'src/auth/guards/auth-jwt/auth-jwt.guard';
import { RolesGuard } from 'src/auth/guards/roles/roles.guard';
import { Roles } from 'src/auth/decorators/roles.decorator';
import { Role } from 'src/auth/enums/role.enum';
import { validateObjectId } from 'src/utils/validation.utils';
import { RecruitementTeamService } from './recruitement-team.service';
import { CreateRecruitementTeamDto } from "./dto/create-recruitement-team.dto";
import { UpdateRecruitementTeamDto } from "./dto/update-recruitement-team.dto";

@Controller('')
@ApiTags('Recruitement Team')
export class RecruitementTeamController {

    private readonly logger = new Logger(RecruitementTeamController.name);

    constructor(private readonly recruitementTeamService: RecruitementTeamService) { }

    @Post()
    @ApiResponse({ status: 201, description: 'Recruitement team member is saved.' })
    @ApiResponse({ status: 401, description: 'Unauthorized.' })
    @ApiResponse({ status: 400, description: 'Bad Request / Data.' })
    @ApiResponse({ status: 403, description: 'Forbidden, User with role admin can only use this end point.' })
    @ApiOperation({ summary: 'Create a new Recruitement team member', description: `This endpoint allows you to create a new Blog. This is accessible only for "admin".` })
    @ApiBearerAuth()
    @UseGuards(AuthJwtGuard, RolesGuard)
    // @Roles(Role.Admin, Role.SuperAdmin)
    @Roles()
    create(@Req() req: any,@Body() createRecruitementTeamDto: CreateRecruitementTeamDto) {
        return this.recruitementTeamService.create(createRecruitementTeamDto);
    }
}
