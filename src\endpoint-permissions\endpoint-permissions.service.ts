import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import { CreateEndpointPermissionDto, UpdateUserPermissionsDto } from './dto/create-endpoint-permission.dto';
import { UpdateEndpointPermissionDto } from './dto/update-endpoint-permission.dto';
import { EndpointPermission } from './schemas/endpointpermissions.schema';
import { Model } from 'mongoose';
import { InjectModel } from '@nestjs/mongoose';
import { EndpointsRoles } from 'src/endpoints-roles/schemas/endpoints-roles.schema';
import { Roles } from 'src/roles/schemas/roles.schema';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';
import { Role } from 'src/auth/enums/role.enum';
import { AllowedControllersForAdmin, AllowedControllersForDeafultFreeLancerRole, AllowedControllersForDeafultJobseekerRoles, AllowedControllersForDeafultRoles, ExcludedControllersForGroupBy, ExcludedControllersForGroupByNoOrg } from 'src/shared/constants';
import { BasicUser } from 'src/user/schemas/basic-user.schema';
import { EndpointsUsers } from 'src/endpoints-roles/schemas/endpoints-users.schema';

@Injectable()
export class EndpointPermissionsService {

  private readonly logger = new Logger(EndpointPermissionsService.name);

  constructor(@InjectModel(EndpointPermission.name) private endpointPermissionModel: Model<EndpointPermission>,
    @InjectModel(EndpointsRoles.name) private endpointsRolesModel: Model<EndpointsRoles>,
    @InjectModel(EndpointsUsers.name) private endpointsUsersModel: Model<EndpointsUsers>,

    @InjectModel(Roles.name) private rolesModel: Model<Roles>,
    @InjectModel(BasicUser.name) private basicUserModel: Model<BasicUser>,
    private eventEmitter: EventEmitter2
  ) { }
  create(createEndpointPermissionDto: CreateEndpointPermissionDto) {
    return 'This action adds a new endpointPermission';
  }

  findAll() {
    return `This action returns all endpointPermissions`;
  }

  findOne(id: number) {
    return `This action returns a #${id} endpointPermission`;
  }

  update(id: number, updateEndpointPermissionDto: UpdateEndpointPermissionDto) {
    return `This action updates a #${id} endpointPermission`;
  }

  remove(id: number) {
    return `This action removes a #${id} endpointPermission`;
  }

  async updateAllRecords() {
    try {
      const result = await this.endpointPermissionModel.updateMany(
        {}, // Empty filter updates all documents
        { $set: { isGlobal: true } } // Set requiredPrivileges to empty array
      );

      this.logger.log(`Updated ${result.modifiedCount} records.`);
      return { message: `${result.modifiedCount} records updated successfully.` };
    } catch (error) {
      this.logger.error('Error updating records:', error);
      throw error;
    }
  }


  async removeRequiredPrivilegesField() {
    try {
      const result = await this.endpointPermissionModel.updateMany(
        { requiredPrivileges: { $exists: true, $size: 0 } }, // Match documents where requiredPrivileges is an empty array
        { $unset: { requiredPrivileges: "" } } // Remove the field
      );

      this.logger.log(`Removed requiredPrivileges from ${result.modifiedCount} records.`);
      return { message: `${result.modifiedCount} records updated successfully.` };
    } catch (error) {
      this.logger.error('Error removing requiredPrivileges field:', error);
      throw error;
    }
  }

  async groupByModules() {
    try {

      // const excludedControllers = [
      //   "Integrations", "Home", "States", "Business Units", "Resumes", "Activities", "Status",
      //   "File-Uploads", "Education-Qualifications", "Orgs", "Regions", "vendor-invite",
      //   "User-inbox-config", "Job-Locations", "Onboardings", "delete-user", "blog",
      //   "Recruitement Team", "Industries", "Evaluation-Forms", "Countries", "Work-Experiences",
      //   "dynamic-fields", "Identifiers", "Auth", "Account Types", "recruiter-target", "Tasks",
      //   "Email-Template-builder", "email-config", "Preferences", "Calendars", "Messages"
      // ];
      // const excludedControllers = new Set<string>(Object.values(ExcludedControllersForGroupBy));
      const excludedControllers: string[] = Object.values(ExcludedControllersForGroupBy);


      const result = await this.endpointPermissionModel.aggregate([
        {
          $match: { controller: { $nin: excludedControllers } } // Exclude specific controllers
        },
        {
          $group: {
            _id: { controller: "$controller", method: "$method" }
          }
        },
        {
          $group: {
            _id: "$_id.controller",
            methods: { $addToSet: "$_id.method" } // Collect unique method names
          }
        },
        {
          $project: {
            _id: 0,
            controller: "$_id",
            methods: 1
          }
        }
      ]);

      // Function to format controller names dynamically
      const formatControllerName = (controller: string) => {
        return controller
          .replace(/[-_]/g, " ")  // Replace hyphens/underscores with spaces
          .replace(/\b\w/g, char => char.toLowerCase()) // Lowercase first letters
          .trim();
      };

      const pluralize = (word: string, isGet: boolean) => {
        if (isGet) {
          return word.endsWith("s") ? word : `${word}s`; // Ensure plural for GET
        } else {
          return word.endsWith("s") ? word.slice(0, -1) : word; // Ensure singular for others
        }
      };

      // Function to generate method descriptions dynamically
      const getMethodDescription = (controller: string, method: string) => {
        const baseName = formatControllerName(controller) || "resource";
        // const pluralName = pluralize(baseName);
        const formattedName = pluralize(baseName, method === "GET");
        const descriptions: Record<string, string> = {
          "GET": `View ${formattedName}`,
          "POST": `Create a new ${formattedName}`,
          "PATCH": `Update an existing ${formattedName}`,
          "DELETE": `Delete a ${formattedName}`
        };
        return descriptions[method] || "No description available";
      };

      // Format result dynamically
      const formattedResult = result.reduce((acc, item) => {
        acc[item.controller] = item.methods.map((method: string) => ({
          method,
          description: getMethodDescription(item.controller, method)
        }));
        return acc;
      }, {});

      const sortedResult = Object.keys(formattedResult)
        .sort()
        .reduce((acc, key) => {
          acc[key] = formattedResult[key];
          return acc;
        }, {} as Record<string, any>);
      return sortedResult;
    } catch (error) {
      this.logger.error('Error grouping modules:', error);
      throw error;
    }
  }

  async getControllerMethodRoles(orgId: string) {
    try {
      // Fetch all endpoint permissions
      const endpointPermissions = await this.endpointPermissionModel.find().lean();
      const endpointRoles = await this.endpointsRolesModel.find({ org: orgId }).lean();
      const rolesData = await this.rolesModel.find().lean(); // Fetch actual roles

      // Create a map of lowercase roles to actual role names
      const roleMap = new Map<string, string>();
      rolesData.forEach(role => {
        roleMap.set(role.role.toLowerCase(), role.role); // Map lowercase to actual case
      });

      // List of controllers to exclude
      // const excludedControllers = new Set([
      //   "Integrations", "Home", "States", "Business Units", "Resumes", "Activities",
      //   "Status", "File-Uploads", "Education-Qualifications", "Orgs", "Regions",
      //   "vendor-invite", "User-inbox-config", "Job-Locations", "Onboardings",
      //   "delete-user", "blog", "Recruitement Team", "Industries", "Evaluation-Forms",
      //   "Countries", "Work-Experiences", "dynamic-fields", "Identifiers", "Auth",
      //   "Account Types", "recruiter-target", "Tasks", "Email-Template-builder",
      //   "email-config", "Preferences", "Calendars", "Messages"
      // ]);

      const excludedControllers = new Set<string>(Object.values(ExcludedControllersForGroupBy));

      // Function to format controller names dynamically
      const formatControllerName = (controller: string) => {
        return controller
          .replace(/[-_]/g, " ")  // Replace hyphens/underscores with spaces
          .toLowerCase()
          .trim();
      };

      // Function to handle pluralization
      const pluralize = (word: string, isGet: boolean) => {
        if (isGet) {
          return word.endsWith("s") ? word : `${word}s`; // Ensure plural for GET
        } else {
          return word.endsWith("s") ? word.slice(0, -1) : word; // Ensure singular for others
        }
      };

      // Function to dynamically generate descriptions
      const getMethodDescription = (controller: string, method: string) => {
        const baseName = formatControllerName(controller);
        // const pluralName = pluralize(baseName);
        const formattedName = pluralize(baseName, method === "GET");


        const descriptions: Record<string, string> = {
          "GET": `View ${formattedName}`,
          "POST": `Create a new ${formattedName}`,
          "PATCH": `Update an existing ${formattedName}`,
          "DELETE": `Delete a ${formattedName}`
        };

        return descriptions[method] || "No description available";
      };

      // Create a mapping of endpoint IDs to their controllers and methods
      const endpointMap: Record<string, { controller: string; method: string }> = {};
      endpointPermissions.forEach(ep => {
        endpointMap[ep._id.toString()] = { controller: ep.controller, method: ep.method };
      });

      // Initialize a structure to hold the results
      const controllerMethodMap: Record<string, Record<string, { roles: string[], description: string }>> = {};

      // Populate existing controllers and methods with default empty roles and descriptions
      endpointPermissions.forEach(ep => {
        if (excludedControllers.has(ep.controller)) return; // 🚀 Skip excluded controllers
        const description = getMethodDescription(ep.controller, ep.method);

        if (!controllerMethodMap[ep.controller]) controllerMethodMap[ep.controller] = {};
        if (!controllerMethodMap[ep.controller][ep.method]) {
          controllerMethodMap[ep.controller][ep.method] = { roles: [], description };
        }
      });

      // Map roles to the correct controller and method
      endpointRoles.forEach(er => {
        const endpointDetails = endpointMap[er.endPoint.toString()];
        if (endpointDetails) {
          const { controller, method } = endpointDetails;

          if (excludedControllers.has(controller)) return; // 🚀 Skip excluded controllers

          // Ensure controller and method exist
          if (!controllerMethodMap[controller]) controllerMethodMap[controller] = {};
          if (!controllerMethodMap[controller][method]) {
            const description = getMethodDescription(controller, method);
            controllerMethodMap[controller][method] = { roles: [], description };
          }

          // ✅ Push only unique roles
          er.roles?.forEach(role => {
            const actualRole = roleMap.get(role) || role;
            if (!controllerMethodMap[controller][method].roles.includes(actualRole)) {
              controllerMethodMap[controller][method].roles.push(actualRole);
            }
          });
        }
      });

      return controllerMethodMap;
    } catch (error) {
      this.logger.error('Error fetching controller method roles:', error);
      throw error;
    }
  }





  async getRolesWithPermissions(org: string) {
    try {
      // Fetch all roles from roles model
      const allRoles = await this.rolesModel.find({ isDeleted: false, orgId: org }).lean();

      // Fetch all endpoint permissions (controllers & methods)
      const endpointPermissions = await this.endpointPermissionModel.find().lean();

      // Create a map of controller -> all methods available
      const controllerMethodsMap: Record<string, Set<string>> = {};
      for (const ep of endpointPermissions) {
        if (!controllerMethodsMap[ep.controller]) {
          controllerMethodsMap[ep.controller] = new Set();
        }
        controllerMethodsMap[ep.controller].add(ep.method);
      }

      // Fetch all role-based permissions
      const endpointsRoles = await this.endpointsRolesModel.find().lean();

      // Create a map of endpoint ID -> { controller, method }
      const endpointMap = endpointPermissions.reduce<Record<string, { controller: string; method: string }>>(
        (acc, ep) => {
          acc[ep._id.toString()] = { controller: ep.controller, method: ep.method };
          return acc;
        },
        {}
      );

      // Initialize rolesMap with all controllers and methods set to false
      const rolesMap: Record<string, Record<string, Record<string, boolean>>> = {};

      for (const role of allRoles) {
        const roleName = role.role; // Assuming `name` field exists in roles model
        rolesMap[roleName] = {};

        // Ensure every role has all controllers
        for (const controller in controllerMethodsMap) {
          rolesMap[roleName][controller] = {};
          for (const method of controllerMethodsMap[controller]) {
            rolesMap[roleName][controller][method] = false;
          }
        }
      }

      // Assign true to methods that roles have access to
      for (const endpointRole of endpointsRoles) {
        const { roles, endPoint } = endpointRole;
        if (!roles || !Array.isArray(roles)) continue;

        const endpointDetails = endpointMap[endPoint.toString()];
        if (!endpointDetails) continue;

        const { controller, method } = endpointDetails;

        for (const role of roles) {
          if (!rolesMap[role]) rolesMap[role] = {};

          if (!rolesMap[role][controller]) {
            rolesMap[role][controller] = {};
          }

          rolesMap[role][controller][method] = true;
        }
      }

      return rolesMap;
    } catch (error) {
      this.logger.error("Error fetching role permissions:", error);
      throw error;
    }
  }

  // async updateRolePermissions(orgId: string, payload: CreateEndpointPermissionDto) {
  //   try {
  //     const roleUpdates = [];
  //     const rolesToRemove = [];
  //     const AdminRole = "Admin";
  //     const controllerMethodMap: Record<string, string[]> = {};

  //     for (const [role, permissions] of Object.entries(payload.rolesMapping)) {
  //       if (permissions.length === 0) {
  //         rolesToRemove.push(role);
  //       } else {
  //         permissions.forEach(permission => {
  //           const [controller, method] = permission.split(':');
  //           if (!controllerMethodMap[`${controller}:${method}`]) {
  //             controllerMethodMap[`${controller}:${method}`] = [];
  //           }
  //           controllerMethodMap[`${controller}:${method}`].push(role);
  //         });
  //       }
  //     }

  //     // **1. Remove roles that have no assigned permissions**
  //     if (rolesToRemove.length > 0) {
  //       await this.endpointsRolesModel.updateMany(
  //         { org: orgId },
  //         { $pull: { roles: { $in: rolesToRemove } } }
  //       );
  //     }

  //     // **2. Fetch endpoints related to the given controllers & methods**
  //     const endpointsToUpdate = await this.endpointPermissionModel.find({
  //       $or: Object.keys(controllerMethodMap).map(key => {
  //         const [controller, method] = key.split(':');
  //         return { controller, method };
  //       })
  //     });

  //     const updatedEndpointIds = new Set(endpointsToUpdate.map(e => e._id.toString()));

  //     for (const endpoint of endpointsToUpdate) {
  //       const endpointKey = `${endpoint.controller}:${endpoint.method}`;
  //       const newRoles = controllerMethodMap[endpointKey] || [];

  //       // Fetch the existing roles for this endpoint
  //       const existingRoleDoc = await this.endpointsRolesModel.findOne({
  //         org: orgId,
  //         endPoint: endpoint._id.toString(),
  //       });

  //       // Check if "Admin" role already exists
  //       const existingRoles = existingRoleDoc?.roles || [];
  //       const shouldIncludeAdmin = existingRoles.some(role => role === AdminRole);

  //       // Prepare the final roles list
  //       const updatedRoles = shouldIncludeAdmin ? [...new Set([AdminRole, ...newRoles])] : [...new Set(newRoles)];

  //       // **Update roles only if there is a change**
  //       roleUpdates.push({
  //         updateOne: {
  //           filter: { org: orgId, endPoint: endpoint._id.toString() },
  //           update: { $set: { roles: updatedRoles } },
  //           upsert: true,
  //         }
  //       });
  //     }


  //     // **4. Remove roles from endpoints that were NOT updated**
  //     // Fetch all endpoint roles that are not in updatedEndpointIds
  //     const endpointsToModify = await this.endpointsRolesModel.find({
  //       org: orgId,
  //       endPoint: { $nin: Array.from(updatedEndpointIds) }
  //     });

  //     for (const endpoint of endpointsToModify) {
  //       const existingRoles = endpoint.roles || [];
  //       const hasAdminRole = existingRoles.some(role => role === AdminRole);

  //       // Only remove roles if Admin was already present
  //       const updateQuery: any = { $pull: { roles: { $nin: hasAdminRole ? [AdminRole] : [] } } };

  //       await this.endpointsRolesModel.updateOne(
  //         { org: orgId, endPoint: endpoint.endPoint },
  //         updateQuery
  //       );
  //     }


  //     // **5. Perform bulk update on `endpointRoles` collection**
  //     if (roleUpdates.length > 0) {
  //       await this.endpointsRolesModel.bulkWrite(roleUpdates);
  //     }

  //     this.emitEvent('org.CloneDefaultPermissionsAdmin', {
  //       org: orgId,
  //       roles: [Role.Admin]
  //     });
  //     this.emitEvent('org.CloneDefaultPermissionsRoleCreated', {
  //       org: orgId.toString()
  //     });

  //     return { message: 'Roles updated successfully' };
  //   } catch (error) {
  //     console.error('Error updating roles:', error);
  //     throw error;
  //   }
  // }

  async updateOrgRolePermissions(orgId: string, payload: CreateEndpointPermissionDto) {
    try {
      const roleUpdates = [];
      const rolesToRemove = [];
      const controllerMethodMap = new Map<string, string[]>();
      const additionalPermissionsToRemove = [
        "Stages:POST",
        "Stages:GET",
        "Stages:PATCH",
        "Stages:DELETE",

        "Assessment:POST",
        "Assessment:GET",
        "Assessment:PATCH",
        "Assessment:DELETE",

        "Meetings:POST",
        "Meetings:GET",
        "Meetings:PATCH",
        "Meetings:DELETE",

        "Calendar:POST",
        "Calendar:GET",
        "Calendar:PATCH",
        "Calendar:DELETE",

        "Offer:POST",
        "Offer:GET",
        "Offer:PATCH",
        "Offer:DELETE",

        "Interviews:POST",
        "Interviews:GET",
        "Interviews:PATCH",
        "Interviews:DELETE",

        // "Job-Application-Forms:PATCH",
        // "Job-Application-Forms:DELETE"
      ];

      for (const [role, permissions] of Object.entries(payload.rolesMapping)) {
        const normalizedRole = role.toLowerCase(); // Normalize to lowercase
        if (normalizedRole === "admin") {
          continue; // 🔥 Skip admin role
        }
        if (permissions.length === 0) {
          rolesToRemove.push(normalizedRole);
        } else {
          const additionalPermissions: Set<string> = new Set(); // Explicitly define type
          const hasWorkflowPatch = permissions.includes("Workflow:PATCH"); // Check existence
          const hasWorkflowGET = permissions.includes("Workflow:GET");
          const hasJobApplicationFormsPOST = permissions.includes("Job-Application-Forms:POST");

          permissions.forEach(permission => {
            const [controller, method] = permission.split(':');
            if (controller === "Workflow" && method === "GET") {
              additionalPermissions.add("Job-Application-Forms:GET");
            }
            if (hasJobApplicationFormsPOST) {
              additionalPermissions.add("dynamic-fields:GET")
            }
            if (hasWorkflowPatch) {
              if (controller === "Workflow" && method === "PATCH") {
                additionalPermissions.add("Stages:POST");
                additionalPermissions.add("Stages:GET");
                additionalPermissions.add("Stages:PATCH");
                additionalPermissions.add("Stages:DELETE");


                additionalPermissions.add("Assessment:POST");
                additionalPermissions.add("Assessment:GET");
                additionalPermissions.add("Assessment:PATCH");
                additionalPermissions.add("Assessment:DELETE");

                additionalPermissions.add("Meetings:POST");
                additionalPermissions.add("Meetings:GET");
                additionalPermissions.add("Meetings:PATCH");
                additionalPermissions.add("Meetings:DELETE");

                additionalPermissions.add("Calendar:POST");
                additionalPermissions.add("Calendar:GET");
                additionalPermissions.add("Calendar:PATCH");
                additionalPermissions.add("Calendar:DELETE");

                additionalPermissions.add("Offer:POST");
                additionalPermissions.add("Offer:GET");
                additionalPermissions.add("Offer:PATCH");
                additionalPermissions.add("Offer:DELETE");

                additionalPermissions.add("Interviews:POST");
                additionalPermissions.add("Interviews:GET");
                additionalPermissions.add("Interviews:PATCH");
                additionalPermissions.add("Interviews:DELETE");

                // additionalPermissions.add("Job-Application-Forms:PATCH");
                // additionalPermissions.add("Job-Application-Forms:DELETE");

              }
            }
            const key = `${controller}:${method}`;
            if (!controllerMethodMap.has(key)) {
              controllerMethodMap.set(key, []);
            }
            controllerMethodMap.get(key)!.push(normalizedRole);
          });

          // console.log(additionalPermissions)
          // Append additional permissions to the same role
          additionalPermissions.forEach((extraPermission: string) => {
            const [extraController, extraMethod] = extraPermission.split(':');
            const key = `${extraController}:${extraMethod}`;
            if (!controllerMethodMap.has(key)) {
              controllerMethodMap.set(key, []);
            }
            controllerMethodMap.get(key)!.push(normalizedRole);
          });
          // console.log(controllerMethodMap)
          // console.log(hasWorkflowGET)
          // console.log(hasWorkflowPatch)
          // console.log(hasWorkflowPatch)
          // console.log(role)
          // ❌ If Workflow:PATCH is missing, remove extra permissions
          if (!hasWorkflowPatch) {
            additionalPermissionsToRemove.forEach(permission => {
              // if (permission === "Job-Application-Forms:PATCH" && !permissions.includes("Job-Application-Forms:PATCH")) {
              //   controllerMethodMap.delete(permission);
              // }
              // if (permission === "Job-Application-Forms:DELETE" && !permissions.includes("Job-Application-Forms:DELETE")) {
              //   controllerMethodMap.delete(permission);
              // }
              // else{
              //   controllerMethodMap.delete(permission);
              // }

              // controllerMethodMap.delete(permission);

              const roles = controllerMethodMap.get(permission);
              if (roles) {
                // Remove 'bu_head' or any other role you want to remove
                const updatedRoles = roles.filter(role => role !== normalizedRole);

                if (updatedRoles.length > 0) {
                  controllerMethodMap.set(permission, updatedRoles);
                } else {
                  controllerMethodMap.delete(permission); // Optional: remove key if no roles left
                }
              }


            });
          }
          if (!hasJobApplicationFormsPOST) {
            // controllerMethodMap.delete("dynamic-fields:GET");
            const permissionKey = "dynamic-fields:GET";
            const roles = controllerMethodMap.get(permissionKey);
            if (roles) {
              const updatedRoles = roles.filter(role => role !== normalizedRole);
              if (updatedRoles.length > 0) {
                controllerMethodMap.set(permissionKey, updatedRoles);
              } else {
                controllerMethodMap.delete(permissionKey); // optional
              }
            }
          }
          if (!hasWorkflowGET && !permissions.includes("Job-Application-Forms:GET")) {
            // controllerMethodMap.delete("Job-Application-Forms:GET");
            const permissionKey = "Job-Application-Forms:GET";
            const roles = controllerMethodMap.get(permissionKey);
            if (roles) {
              const updatedRoles = roles.filter(role => role !== normalizedRole);
              if (updatedRoles.length > 0) {
                controllerMethodMap.set(permissionKey, updatedRoles);
              } else {
                controllerMethodMap.delete(permissionKey); // optional
              }
            }
          }

        }
      }
      // console.log(controllerMethodMap)
      // console.log(rolesToRemove)
      // **1. Remove roles that have no assigned permissions in a single query**
      if (rolesToRemove.length > 0) {
        await this.endpointsRolesModel.updateMany(
          { org: orgId },
          { $pull: { roles: { $in: rolesToRemove.map(role => role.toLowerCase()) } } }
        );
      }

      // **2. Fetch endpoints related to the given controllers & methods**
      const endpointsToUpdate = await this.endpointPermissionModel.find({
        $or: Array.from(controllerMethodMap.keys()).map(key => {
          const [controller, method] = key.split(':');
          return { controller, method };
        })
      });
      // console.log(endpointsToUpdate)

      const updatedEndpointIds = new Set(endpointsToUpdate.map(e => e._id.toString()));

      // **3. Fetch existing endpoint roles in bulk**
      const existingRolesDocs = await this.endpointsRolesModel.find({
        org: orgId,
        endPoint: { $in: Array.from(updatedEndpointIds) }
      });

      const existingRolesMap = new Map<string, string[]>();

      for (const doc of existingRolesDocs) {
        existingRolesMap.set(doc.endPoint.toString(), doc.roles || []);
      }


      for (const endpoint of endpointsToUpdate) {
        const endpointKey = `${endpoint.controller}:${endpoint.method}`;
        const newRoles = controllerMethodMap.get(endpointKey) || [];

        // Prepare the final roles list
        // const updatedRoles = [...new Set(newRoles)];
        const updatedRoles = [...new Set(newRoles.map(role => role.toLowerCase()))];


        roleUpdates.push({
          updateOne: {
            filter: { org: orgId, endPoint: endpoint._id.toString() },
            update: { $set: { roles: updatedRoles } },
            upsert: true,
          }
        });
      }

      // **4. Bulk remove roles from endpoints that were NOT updated**
      // await this.endpointsRolesModel.updateMany(
      //   {
      //     org: orgId,
      //     endPoint: { $nin: Array.from(updatedEndpointIds) }
      //   },
      //   { $pullAll: { roles: rolesToRemove.map(role => role.toLowerCase()) } }
      // );

      await this.endpointsRolesModel.updateMany(
        {
          org: orgId,
          endPoint: { $nin: Array.from(updatedEndpointIds) }
        },
        { $set: { roles: [] } } // ✅ Ensures roles is an empty array instead of being removed
      );

      // **5. Perform bulk update on `endpointRoles` collection**
      if (roleUpdates.length > 0) {
        await this.endpointsRolesModel.bulkWrite(roleUpdates);
      }
      this.emitEvent('org.CloneDefaultPermissionsAdmin', {
        org: orgId,
        roles: Role.Admin
      });
      this.emitEvent('org.CloneDefaultPermissionsRoleCreated', {
        org: orgId.toString()
      });

      return { message: 'Roles updated successfully' };
    } catch (error) {
      console.error('Error updating roles:', error);
      throw error;
    }
  }

  async updateUserPermissions(orgId: string, updateUserPermissionsDto: UpdateUserPermissionsDto) {
    try {
      const userId = updateUserPermissionsDto.userId;
      const permissions = new Set(updateUserPermissionsDto.permissions); // Store unique permissions

      // 🛑 If no permissions, remove all endpoint roles for this user and return

      if (permissions.size === 0) {
        await this.endpointsUsersModel.deleteMany({ userId: userId, org: orgId });
        // const user = await this.basicUserModel.findByIdAndUpdate(
        //   userId,
        //   { $set: { isCustom: false } },
        //   { new: true } // Returns the updated document
        // );
        // if (!user) throw new BadRequestException("User not found");
        return { message: "All user permissions removed successfully" };
      }

      const user = await this.basicUserModel.findByIdAndUpdate(
        userId,
        { $set: { isCustom: true } },
        { new: true } // Returns the updated document
      );
      if (!user) throw new BadRequestException("User not found");

      this.emitEvent('org.CloneDefaultPermissionsCustomUserCreated', {
        org: orgId,
        user: userId
      });

      let bulkOps = [];
      let newEndpointIds = new Set<string>();

      // 🔹 Define additional permissions logic
      const additionalPermissions: Set<string> = new Set();

      if (permissions.has("Workflow:GET")) {
        additionalPermissions.add("Job-Application-Forms:GET");
      }
      if (permissions.has("Job-Application-Forms:POST")) {
        additionalPermissions.add("dynamic-fields:GET");
      }
      if (permissions.has("Workflow:PATCH")) {
        additionalPermissions.add("Stages:POST");
        additionalPermissions.add("Stages:GET");
        additionalPermissions.add("Stages:PATCH");
        additionalPermissions.add("Stages:DELETE");

        additionalPermissions.add("Assessment:POST");
        additionalPermissions.add("Assessment:GET");
        additionalPermissions.add("Assessment:PATCH");
        additionalPermissions.add("Assessment:DELETE");

        additionalPermissions.add("Meetings:POST");
        additionalPermissions.add("Meetings:GET");
        additionalPermissions.add("Meetings:PATCH");
        additionalPermissions.add("Meetings:DELETE");

        additionalPermissions.add("Offer:POST");
        additionalPermissions.add("Offer:GET");
        additionalPermissions.add("Offer:PATCH");
        additionalPermissions.add("Offer:DELETE");

        additionalPermissions.add("Calendar:POST");
        additionalPermissions.add("Calendar:GET");
        additionalPermissions.add("Calendar:PATCH");
        additionalPermissions.add("Calendar:DELETE");

        additionalPermissions.add("Interviews:POST");
        additionalPermissions.add("Interviews:GET");
        additionalPermissions.add("Interviews:PATCH");
        additionalPermissions.add("Interviews:DELETE");
      }

      // 🔹 Merge additional permissions without duplicates
      additionalPermissions.forEach((perm) => permissions.add(perm));

      // 🔹 Fetch all endpoints in a single query
      const endpointFilters = Array.from(permissions).map((perm) => {
        const [controller, method] = perm.split(":");
        return { controller, method };
      });

      const endpoints = await this.endpointPermissionModel.find({ $or: endpointFilters });

      for (const endpoint of endpoints) {
        const endpointId = endpoint._id.toString();
        newEndpointIds.add(endpointId);

        // ✅ Add or update the entry in `endpointRoles`
        bulkOps.push({
          updateOne: {
            filter: { userId: userId, endPoint: endpointId, org: orgId },
            // update: { $set: { userId : userId, endPoint: endpointId } }, // No permissions field
            update: { $setOnInsert: { userId: userId, endPoint: endpointId } },
            upsert: true,
          },
        });
      }

      // 🔹 Remove old user-specific permissions that are no longer in the new payload
      await this.endpointsUsersModel.deleteMany({
        userId: userId,
        org: orgId,
        endPoint: { $nin: Array.from(newEndpointIds) }, // Keep only current endpointIds
      });

      // 🔹 Perform bulk update for new permissions
      if (bulkOps.length > 0) {
        await this.endpointsUsersModel.bulkWrite(bulkOps);
      }

      return { message: "User permissions updated successfully" };
    } catch (error) {
      console.error("Error updating user permissions:", error);
      throw error;
    }
  }


  // async updateRolePermissions(orgId: string, payload: CreateEndpointPermissionDto) {
  //   try {
  //     const roleUpdates = [];
  //     const rolesToRemove = [];
  //     const controllerMethodMap: Record<string, string[]> = {};

  //     for (const [role, permissions] of Object.entries(payload.rolesMapping)) {
  //       if (permissions.length === 0) {
  //         rolesToRemove.push(role); // Collect roles to be removed
  //       } else {
  //         // Group permissions by controller and method
  //         permissions.forEach(permission => {
  //           const [controller, method] = permission.split(':');
  //           if (!controllerMethodMap[`${controller}:${method}`]) {
  //             controllerMethodMap[`${controller}:${method}`] = [];
  //           }
  //           controllerMethodMap[`${controller}:${method}`].push(role);
  //         });
  //       }
  //     }

  //     // **1. Remove roles that have no assigned permissions**
  //     if (rolesToRemove.length > 0) {
  //       await this.endpointsRolesModel.updateMany(
  //         { org: orgId },
  //         { $pull: { roles: { $in: rolesToRemove } } }
  //       );
  //     }
  //     console.log(rolesToRemove)
  //     // **2. Update roles that have permissions**
  //     if (Object.keys(controllerMethodMap).length > 0) {
  //       // Fetch all matching endpoints based on controller & method
  //       const endpointsToUpdate = await this.endpointPermissionModel.find({
  //         $or: Object.keys(controllerMethodMap).map(key => {
  //           const [controller, method] = key.split(':');
  //           return { controller, method };
  //         })
  //       });
  //       const updatedEndpointIds = new Set(endpointsToUpdate.map(e => e._id.toString()));
  //       console.log(endpointsToUpdate)
  //       for (const endpoint of endpointsToUpdate) {
  //         roleUpdates.push({
  //           updateOne: {
  //             filter: { org: orgId, endPoint: endpoint._id.toString() },
  //             update: { $set: { roles: { $each: controllerMethodMap[`${endpoint.controller}:${endpoint.method}`] || [] } } },
  //             upsert: true
  //           }
  //         });
  //       }
  //       // **4. Remove roles from endpoints that were NOT updated**
  //       await this.endpointsRolesModel.updateMany(
  //         { org: orgId, endPoint: { $nin: Array.from(updatedEndpointIds) } },
  //         { $set: { roles: [] } }
  //       );

  //     }
  //     console.log(JSON.stringify(roleUpdates))

  //     this.emitEvent('org.CloneDefaultPermissionsAdmin', {
  //       org: orgId,
  //       roles: [Role.Admin]
  //     });


  //     // **5. Perform bulk update on `endpointRoles` collection**
  //     if (roleUpdates.length > 0) {
  //       await this.endpointsRolesModel.bulkWrite(roleUpdates);
  //     }

  //     return { message: 'Roles updated successfully' };
  //   } catch (error) {
  //     console.error('Error updating roles:', error);
  //     throw error;
  //   }
  // }

  @OnEvent('org.CloneDefaultPermissionsAdmin') // Also listen for activation event
  async cloneDefaultEndpoints(payload: any) {
    const { org, roles } = payload;
    const orgId = org.toString();
    console.log(orgId)
    try {
      // Fetch all endpoint permissions
      // const defaultEndpoints = await this.endpointPermissionModel.find().lean();

      // List of allowed controllers
      // const allowedControllers = [
      //   "Orgs",
      //   "Contacts",
      //   "Jobs",
      //   "Job-Application-Forms",
      //   "Business Units",
      //   "Users",
      //   "vendor-invite",
      //   "Activities",
      //   "File-Uploads",
      //   "Stages",
      //   "Workflow",
      //   "Email-Template-builder",
      //   "email-config",
      //   "User-inbox-config",
      //   "delete-user",
      //   "Integrations",
      //   "Tasks",
      //   "Meetings",
      //   "recruiter-target",
      //   "Industries",
      //   "Account Types",
      //   "Notes",
      //   "Messages",
      //   "blog",
      //   "Recruitement Team",
      //   "Countries",
      //   "Identifiers",
      //   "Evaluation-Forms",//
      //   "Education-Qualifications",//
      //   "Job-Locations",
      //   "Job Allocations"

      // ];

      const allowedControllers: string[] = Object.values(AllowedControllersForAdmin);


      // Fetch all default endpoints and filter based on allowed controllers
      const defaultEndpoints = await this.endpointPermissionModel.find().lean();

      const allowedPermissions = allowedControllers.flatMap((perm) => {
        const [controller, method] = perm.split(":");

        if (method) {
          // If method is already provided, keep it as is
          return [{ controller, method }];
        } else {
          // If no method is provided, assign all methods
          return ["POST", "GET", "PATCH", "DELETE"].map(m => ({ controller, method: m }));
        }
      });


      // console.log(allowedPermissions)
      // Filter endpoints based on allowed controllers
      // const filteredEndpoints = defaultEndpoints.filter(ep =>
      //   allowedControllers.includes(ep.controller)
      // );

      const filteredEndpoints = defaultEndpoints.filter(ep =>
        allowedPermissions.some(
          (perm) => perm.controller === ep.controller && perm.method === ep.method
        )
      );

      // Fetch existing endpoint roles for the organization
      const existingRoles = await this.endpointsRolesModel.find({ org: orgId }).lean();

      // Create a map for quick lookup of existing roles
      const existingRolesMap = new Map(
        existingRoles.map(er => [
          `${er.endPoint.toString()}:${orgId}`,
          (er.roles || []).map(role => role.toLowerCase()) // Ensure roles are lowercase
        ])
      );
      // Prepare bulk operations
      const roleUpdates = [];

      for (const endpoint of filteredEndpoints) {
        const endpointKey = `${endpoint._id.toString()}:${orgId}`;
        const existingRolesLower = existingRolesMap.get(endpointKey) || [];

        // Ensure the role list is unique and case-insensitive
        const newRoles = [...new Set([...existingRolesLower, roles])];
        roleUpdates.push({
          updateOne: {
            filter: { org: orgId, endPoint: endpoint._id.toString() },
            update: { $addToSet: { roles: { $each: newRoles.map(role => role.toLowerCase()) } } },
            upsert: true
          }
        });
      }

      // Perform bulk write operations
      if (roleUpdates.length > 0) {
        await this.endpointsRolesModel.bulkWrite(roleUpdates);
      }

      this.logger.log(`Processed ${roleUpdates.length} endpoint role updates for org ${orgId}`);
    } catch (error) {
      this.logger.error("Error cloning/updating default endpoints:", error);
    }
  }

  @OnEvent('org.CloneDefaultPermissionsRoleCreated') // Also listen for activation event
  async cloneDefaultEndpointsForRoles(payload: any) {
    const { org } = payload;
    const orgId = org.toString();

    try {
      // const allowedControllers = new Set([
      //   "States", "Business Units", "Activities", "File-Uploads",
      //   "Education-Qualifications", "Orgs", "User-inbox-config",
      //   "Job-Locations", "Onboardings", "delete-user", "Industries",
      //   "Evaluation-Forms", "Work-Experiences", "Account Types",
      //   "Tasks", "Messages",
      // ]);

      const allowedControllers = new Set<string>(Object.values(AllowedControllersForDeafultRoles));

      // Fetch all existing roles in the organization
      const orgRoles = await this.rolesModel.find({ orgId: orgId, isDeleted: false }).lean();
      const allRoles = orgRoles.map(role => role.role.toLowerCase()); // Convert to lowercase for consistency

      // Fetch all default endpoints and filter based on allowed controllers
      const defaultEndpoints = await this.endpointPermissionModel.find().lean();
      const filteredEndpoints = defaultEndpoints.filter(ep =>
        allowedControllers.has(ep.controller)
      );
      // Fetch existing endpoint roles for the organization
      const existingRoles = await this.endpointsRolesModel.find({ org: orgId }).lean();

      // Create a map for quick lookup of existing roles per endpoint
      const existingRolesMap = new Map(
        existingRoles.map(er => [
          `${er.endPoint.toString()}:${orgId}`,
          (er.roles || []).map(role => role.toLowerCase())
        ])
      );

      const roleUpdates = [];

      for (const endpoint of filteredEndpoints) {
        const endpointKey = `${endpoint._id.toString()}:${orgId}`;
        const existingRolesLower = existingRolesMap.get(endpointKey) || [];

        // Ensure all roles are assigned uniquely and case-insensitively
        const updatedRoles = [...new Set([...existingRolesLower, ...allRoles])];

        roleUpdates.push({
          updateOne: {
            filter: { org: orgId, endPoint: endpoint._id.toString() },
            update: { $addToSet: { roles: { $each: updatedRoles.map(role => role.toLowerCase()) } } },
            upsert: true
          }
        });
      }

      // Perform bulk write operations
      if (roleUpdates.length > 0) {
        await this.endpointsRolesModel.bulkWrite(roleUpdates);
      }

      this.logger.log(`Assigned all roles to ${roleUpdates.length} endpoint roles for org ${orgId}`);
    } catch (error) {
      this.logger.error("Error assigning roles to default endpoints:", error);
    }
  }


  @OnEvent('org.CloneDefaultPermissionsCustomUserCreated') // Also listen for activation event
  async cloneDefaultEndpointsForCustomUser(payload: any) {
    const { org, user } = payload;
    const orgId = org.toString();
    const userId = user.toString();

    try {
      const allowedControllers = new Set<string>(Object.values(AllowedControllersForDeafultRoles));

      // Fetch all default endpoints and filter based on allowed controllers
      const defaultEndpoints = await this.endpointPermissionModel.find().lean();
      const filteredEndpoints = defaultEndpoints.filter(ep =>
        allowedControllers.has(ep.controller)
      );

      // Fetch all existing endpoints already assigned to the user
      const existingUserEndpoints = await this.endpointsUsersModel.find({
        org: orgId,
        userId: userId,
      }).lean();

      const existingEndpointIds = new Set(
        existingUserEndpoints.map(entry => entry.endPoint.toString())
      );

      const newEndpointAssignments = [];

      for (const endpoint of filteredEndpoints) {
        const endpointId = endpoint._id.toString();

        if (!existingEndpointIds.has(endpointId)) {
          newEndpointAssignments.push({
            insertOne: {
              document: {
                userId : userId,
                endPoint: endpointId,
                org: orgId,
                createdAt: new Date(),
                updatedAt: new Date()
              }
            }
          });
        }
      }

      if (newEndpointAssignments.length > 0) {
        await this.endpointsUsersModel.bulkWrite(newEndpointAssignments);
      }

      this.logger.log(`Assigned ${newEndpointAssignments.length} new endpoint permissions to user ${userId} in org ${orgId}`);
    } catch (error) {
      this.logger.error("Error assigning endpoints to user:", error);
    }
  }

  


  emitEvent(eventName: string, payload: any) {
    // payload['event']= eventName;
    this.logger.debug(payload)
    this.eventEmitter.emit(eventName, payload);
  }

  async groupByModulesForNoOrg() {
    try {

      // const excludedControllers = [
      //   "Integrations", "Home", "States", "Business Units", "Resumes", "Activities", "Status",
      //   "File-Uploads", "Education-Qualifications", "Orgs", "Regions", "vendor-invite",
      //   "User-inbox-config", "Job-Locations", "Onboardings", "delete-user", "blog",
      //   "Recruitement Team", "Industries", "Evaluation-Forms", "Countries", "Work-Experiences",
      //   "dynamic-fields", "Identifiers", "Auth", "Account Types", "recruiter-target", "Tasks",
      //   "Email-Template-builder", "email-config", "Preferences", "Calendars", "Messages"
      // ];
      // const excludedControllers = new Set<string>(Object.values(ExcludedControllersForGroupBy));
      const excludedControllers: string[] = Object.values(ExcludedControllersForGroupByNoOrg);


      const result = await this.endpointPermissionModel.aggregate([
        {
          $match: { controller: { $nin: excludedControllers } } // Exclude specific controllers
        },
        {
          $group: {
            _id: { controller: "$controller", method: "$method" }
          }
        },
        {
          $group: {
            _id: "$_id.controller",
            methods: { $addToSet: "$_id.method" } // Collect unique method names
          }
        },
        {
          $project: {
            _id: 0,
            controller: "$_id",
            methods: 1
          }
        }
      ]);

      // Function to format controller names dynamically
      const formatControllerName = (controller: string) => {
        return controller
          .replace(/[-_]/g, " ")  // Replace hyphens/underscores with spaces
          .replace(/\b\w/g, char => char.toLowerCase()) // Lowercase first letters
          .trim();
      };

      const pluralize = (word: string, isGet: boolean) => {
        if (isGet) {
          return word.endsWith("s") ? word : `${word}s`; // Ensure plural for GET
        } else {
          return word.endsWith("s") ? word.slice(0, -1) : word; // Ensure singular for others
        }
      };

      // Function to generate method descriptions dynamically
      const getMethodDescription = (controller: string, method: string) => {
        const baseName = formatControllerName(controller) || "resource";
        // const pluralName = pluralize(baseName);
        const formattedName = pluralize(baseName, method === "GET");
        const descriptions: Record<string, string> = {
          "GET": `View ${formattedName}`,
          "POST": `Create a new ${formattedName}`,
          "PATCH": `Update an existing ${formattedName}`,
          "DELETE": `Delete a ${formattedName}`
        };
        return descriptions[method] || "No description available";
      };

      // Format result dynamically
      const formattedResult = result.reduce((acc, item) => {
        acc[item.controller] = item.methods.map((method: string) => ({
          method,
          description: getMethodDescription(item.controller, method)
        }));
        return acc;
      }, {});

      const sortedResult = Object.keys(formattedResult)
        .sort()
        .reduce((acc, key) => {
          acc[key] = formattedResult[key];
          return acc;
        }, {} as Record<string, any>);
      return sortedResult;
    } catch (error) {
      this.logger.error('Error grouping modules:', error);
      throw error;
    }
  }

  async getControllerMethodRolesForNoOrg() {
    try {
      // Fetch all endpoint permissions
      const endpointPermissions = await this.endpointPermissionModel.find().lean();
      const endpointRoles = await this.endpointsRolesModel.find({ org: { $exists: false } }).lean();

      // List of controllers to exclude
      // const excludedControllers = new Set([
      //   "Integrations", "Home", "States", "Business Units", "Resumes", "Activities",
      //   "Status", "File-Uploads", "Education-Qualifications", "Orgs", "Regions",
      //   "vendor-invite", "User-inbox-config", "Job-Locations", "Onboardings",
      //   "delete-user", "blog", "Recruitement Team", "Industries", "Evaluation-Forms",
      //   "Countries", "Work-Experiences", "dynamic-fields", "Identifiers", "Auth",
      //   "Account Types", "recruiter-target", "Tasks", "Email-Template-builder",
      //   "email-config", "Preferences", "Calendars", "Messages"
      // ]);

      const excludedControllers = new Set<string>(Object.values(ExcludedControllersForGroupByNoOrg));

      // Function to format controller names dynamically
      const formatControllerName = (controller: string) => {
        return controller
          .replace(/[-_]/g, " ")  // Replace hyphens/underscores with spaces
          .toLowerCase()
          .trim();
      };

      // Function to handle pluralization
      const pluralize = (word: string, isGet: boolean) => {
        if (isGet) {
          return word.endsWith("s") ? word : `${word}s`; // Ensure plural for GET
        } else {
          return word.endsWith("s") ? word.slice(0, -1) : word; // Ensure singular for others
        }
      };

      // Function to dynamically generate descriptions
      const getMethodDescription = (controller: string, method: string) => {
        const baseName = formatControllerName(controller);
        // const pluralName = pluralize(baseName);
        const formattedName = pluralize(baseName, method === "GET");


        const descriptions: Record<string, string> = {
          "GET": `View ${formattedName}`,
          "POST": `Create a new ${formattedName}`,
          "PATCH": `Update an existing ${formattedName}`,
          "DELETE": `Delete a ${formattedName}`
        };

        return descriptions[method] || "No description available";
      };

      // Create a mapping of endpoint IDs to their controllers and methods
      const endpointMap: Record<string, { controller: string; method: string }> = {};
      endpointPermissions.forEach(ep => {
        endpointMap[ep._id.toString()] = { controller: ep.controller, method: ep.method };
      });

      // Initialize a structure to hold the results
      const controllerMethodMap: Record<string, Record<string, { roles: string[], description: string }>> = {};

      // Populate existing controllers and methods with default empty roles and descriptions
      endpointPermissions.forEach(ep => {
        if (excludedControllers.has(ep.controller)) return; // 🚀 Skip excluded controllers
        const description = getMethodDescription(ep.controller, ep.method);

        if (!controllerMethodMap[ep.controller]) controllerMethodMap[ep.controller] = {};
        if (!controllerMethodMap[ep.controller][ep.method]) {
          controllerMethodMap[ep.controller][ep.method] = { roles: [], description };
        }
      });

      // Map roles to the correct controller and method
      endpointRoles.forEach(er => {
        const endpointDetails = endpointMap[er.endPoint.toString()];
        if (endpointDetails) {
          const { controller, method } = endpointDetails;

          if (excludedControllers.has(controller)) return; // 🚀 Skip excluded controllers

          // Ensure controller and method exist
          if (!controllerMethodMap[controller]) controllerMethodMap[controller] = {};
          if (!controllerMethodMap[controller][method]) {
            const description = getMethodDescription(controller, method);
            controllerMethodMap[controller][method] = { roles: [], description };
          }

          // ✅ Push only unique roles
          er.roles?.forEach(role => {
            if (!controllerMethodMap[controller][method].roles.includes(role)) {
              controllerMethodMap[controller][method].roles.push(role);
            }
          });
        }
      });

      return controllerMethodMap;
    } catch (error) {
      this.logger.error('Error fetching controller method roles:', error);
      throw error;
    }
  }


  async updateRolePermissionsForNoOrg(payload: CreateEndpointPermissionDto) {
    try {
      const roleUpdates = [];
      const rolesToRemove = [];
      const controllerMethodMap = new Map<string, string[]>();

      for (const [role, permissions] of Object.entries(payload.rolesMapping)) {
        const normalizedRole = role.toLowerCase(); // Normalize to lowercase
        if (permissions.length === 0) {
          rolesToRemove.push(normalizedRole);
        } else {
          permissions.forEach(permission => {
            const [controller, method] = permission.split(':');
            const key = `${controller}:${method}`;
            if (!controllerMethodMap.has(key)) {
              controllerMethodMap.set(key, []);
            }
            controllerMethodMap.get(key)!.push(normalizedRole);
          });
        }
      }

      console.log(rolesToRemove);
      // **1. Remove roles that have no assigned permissions**
      if (rolesToRemove.length > 0) {
        await this.endpointsRolesModel.updateMany(
          { org: { $exists: false } }, // Only update endpoints without orgId
          { $pull: { roles: { $in: rolesToRemove.map(role => role.toLowerCase()) } } }
        );
      }


      // **2. Fetch endpoints related to the given controllers & methods**
      const endpointsToUpdate = await this.endpointPermissionModel.find({
        $or: Array.from(controllerMethodMap.keys()).map(key => {
          const [controller, method] = key.split(':');
          return { controller, method };
        })
      });

      console.log(endpointsToUpdate);

      const updatedEndpointIds = new Set(endpointsToUpdate.map(e => e._id.toString()));

      // **3. Fetch existing endpoint roles in bulk**
      const existingRolesDocs = await this.endpointsRolesModel.find({
        endPoint: { $in: Array.from(updatedEndpointIds) },
        org: { $exists: false } // Only fetch roles without orgId
      });

      const existingRolesMap = new Map<string, string[]>();

      for (const doc of existingRolesDocs) {
        existingRolesMap.set(doc.endPoint.toString(), doc.roles || []);
      }

      for (const endpoint of endpointsToUpdate) {
        const endpointKey = `${endpoint.controller}:${endpoint.method}`;
        const newRoles = controllerMethodMap.get(endpointKey) || [];

        // Prepare the final roles list
        const updatedRoles = [...new Set(newRoles.map(role => role.toLowerCase()))];

        roleUpdates.push({
          updateOne: {
            filter: { endPoint: endpoint._id.toString(), org: { $exists: false } },
            update: { $set: { roles: updatedRoles } },
            upsert: true,
          }
        });
      }

      // **4. Bulk remove roles from endpoints that were NOT updated**
      await this.endpointsRolesModel.updateMany(
        {
          endPoint: { $nin: Array.from(updatedEndpointIds) },
          org: { $exists: false } // Only update global roles
        },
        { $set: { roles: [] } } // ✅ Ensures roles is an empty array instead of being removed
      );

      // **5. Perform bulk update on `endpointRoles` collection**
      if (roleUpdates.length > 0) {
        await this.endpointsRolesModel.bulkWrite(roleUpdates);
      }

      return { message: 'Roles updated successfully' };
    } catch (error) {
      console.error('Error updating roles:', error);
      throw error;
    }
  }

  // async getResourceWiseActionsForRole(orgId: string, userRoles: string[]) {
  //   try {
  //     // Fetch all endpoint permissions
  //     const endpointPermissions = await this.endpointPermissionModel.find().lean();
  //     let endpointRoles: any;
  //     if (orgId) {
  //       endpointRoles = await this.endpointsRolesModel.find({ org: orgId }).lean();
  //     }
  //     else {
  //       endpointRoles = await this.endpointsRolesModel.find({ org: { $exists: false }, userId: { $exists: false } })
  //         .lean();
  //     }

  //     const rolesData = await this.rolesModel.find().lean(); // Fetch actual roles

  //     // Create a map of lowercase roles to actual role names
  //     const roleMap = new Map<string, string>();
  //     rolesData.forEach(role => {
  //       roleMap.set(role.role.toLowerCase(), role.role); // Map lowercase to actual case
  //     });
  //     // List of controllers to exclude
  //     const excludedControllers = new Set<string>(Object.values(ExcludedControllersForGroupBy));

  //     // Create a mapping of endpoint IDs to their controllers and methods
  //     const endpointMap: Record<string, { controller: string; method: string }> = {};
  //     endpointPermissions.forEach(ep => {
  //       endpointMap[ep._id.toString()] = { controller: ep.controller, method: ep.method };
  //     });

  //     // Initialize a structure to hold the results
  //     const controllerMethodMap: Record<string, Record<string, boolean>> = {};

  //     // Populate existing controllers and methods with default access as false
  //     endpointPermissions.forEach(ep => {
  //       if (excludedControllers.has(ep.controller)) return; // 🚀 Skip excluded controllers

  //       if (!controllerMethodMap[ep.controller]) controllerMethodMap[ep.controller] = {};
  //       if (!controllerMethodMap[ep.controller][ep.method]) {
  //         controllerMethodMap[ep.controller][ep.method] = false;
  //       }
  //     });

  //     // Map roles to the correct controller and method
  //     endpointRoles.forEach((er: { endPoint: { toString: () => string | number; }; roles: any[]; }) => {
  //       const endpointDetails = endpointMap[er.endPoint.toString()];
  //       if (endpointDetails) {
  //         const { controller, method } = endpointDetails;

  //         if (excludedControllers.has(controller)) return; // 🚀 Skip excluded controllers

  //         // Ensure controller and method exist
  //         if (!controllerMethodMap[controller]) controllerMethodMap[controller] = {};
  //         if (!controllerMethodMap[controller][method]) {
  //           controllerMethodMap[controller][method] = false;
  //         }

  //         if (userRoles.includes('admin')) {
  //           if (er.roles?.some(role => userRoles.includes(role))) {
  //             controllerMethodMap[controller][method] = true;
  //           }
  //         }
  //         else {
  //           if (er.roles?.some(role => {
  //             const actualRole = roleMap.get(role); // Get the properly-cased role
  //             return actualRole && userRoles.includes(actualRole);
  //           })) {
  //             controllerMethodMap[controller][method] = true;
  //           }

  //         }
  //       }
  //       // ✅ Check if the logged-in user has access
  //       // if (er.roles?.some(role => userRoles.includes(role))) {
  //       //   controllerMethodMap[controller][method] = true;
  //       // }

  //       //   if (er.roles?.some(role => {
  //       //     const actualRole = roleMap.get(role); // Get the properly-cased role
  //       //     return actualRole && userRoles.includes(actualRole);
  //       //   })) {
  //       //     controllerMethodMap[controller][method] = true;
  //       //   }

  //       // }
  //     });

  //     return controllerMethodMap;
  //   } catch (error) {
  //     this.logger.error('Error fetching controller method roles:', error);
  //     throw error;
  //   }
  // }

  async getResourceWiseActionsForRole(orgId: string, userRoles: string[], userId: string) {
    try {
      const user = await this.basicUserModel.findOne({ _id: userId }).lean();
      if (!user) throw new BadRequestException("User not found");
      // Fetch all endpoint permissions
      const endpointPermissions = await this.endpointPermissionModel.find().lean();
      // let endpointRoles: any;

      if (user.isCustom) {
        // 🚀 Step 2: Fetch endpoint permissions & roles in parallel
        const [endpointPermissions] = await Promise.all([
          this.endpointPermissionModel.find().lean(),
          // this.rolesModel.find().lean(),
        ]);

        // 🚀 Step 4: Fetch **user-specific permissions**
        const userSpecificPermissions = await this.endpointsUsersModel.find({ userId: userId, org: orgId }).lean();

        // 🚀 Step 5: Create Maps for efficient lookup
        const endpointMap = new Map(endpointPermissions.map(ep => [ep._id.toString(), { controller: ep.controller, method: ep.method }]));
        // const roleMap = new Map(rolesData.map(role => [role.role.toLowerCase(), role.role]));

        const excludedControllers = new Set<string>(Object.values(ExcludedControllersForGroupBy));
        const controllerMethodMap: Record<string, Record<string, boolean>> = {};
        const allowedEndpoints = new Set<string>();

        // 🚀 Step 7: Process **user-specific permissions**

        userSpecificPermissions.forEach(({ endPoint }) => {
          const endpointDetails = endpointMap.get(endPoint.toString());
          if (!endpointDetails || excludedControllers.has(endpointDetails.controller)) return;

          const { controller, method } = endpointDetails;
          controllerMethodMap[controller] ??= {};
          controllerMethodMap[controller][method] = true;
          allowedEndpoints.add(`${controller}:${method}`);
        });

        // 🚀 Step 2: Ensure all controllers and methods exist, setting defaults
        endpointMap.forEach(({ controller, method }) => {
          if (excludedControllers.has(controller)) return; // 🚀 Skip excluded controllers
          controllerMethodMap[controller] ??= {};
          controllerMethodMap[controller][method] = allowedEndpoints.has(`${controller}:${method}`);
        });

        return controllerMethodMap;
      }
      else {
        let endpointRoles: any;
        if (orgId) {
          endpointRoles = await this.endpointsRolesModel.find({ org: orgId }).lean();
        }
        else {
          endpointRoles = await this.endpointsRolesModel.find({ org: { $exists: false } })
            .lean();
        }

        const rolesData = await this.rolesModel.find().lean(); // Fetch actual roles

        // Create a map of lowercase roles to actual role names
        const roleMap = new Map<string, string>();
        rolesData.forEach(role => {
          roleMap.set(role.role.toLowerCase(), role.role); // Map lowercase to actual case
        });

        // 👇 Manually add special roles that are not in DB
        roleMap.set('freelancer', 'freelancer');
        roleMap.set('job-seeker', 'job-seeker');

        // List of controllers to exclude
        const excludedControllers = new Set<string>(Object.values(ExcludedControllersForGroupBy));

        // Create a mapping of endpoint IDs to their controllers and methods
        const endpointMap: Record<string, { controller: string; method: string }> = {};
        endpointPermissions.forEach(ep => {
          endpointMap[ep._id.toString()] = { controller: ep.controller, method: ep.method };
        });

        // Initialize a structure to hold the results
        const controllerMethodMap: Record<string, Record<string, boolean>> = {};

        // Populate existing controllers and methods with default access as false
        endpointPermissions.forEach(ep => {
          if (excludedControllers.has(ep.controller)) return; // 🚀 Skip excluded controllers

          if (!controllerMethodMap[ep.controller]) controllerMethodMap[ep.controller] = {};
          if (!controllerMethodMap[ep.controller][ep.method]) {
            controllerMethodMap[ep.controller][ep.method] = false;
          }
        });

        // Map roles to the correct controller and method
        endpointRoles.forEach((er: { endPoint: { toString: () => string | number; }; roles: any[]; }) => {
          const endpointDetails = endpointMap[er.endPoint.toString()];
          if (endpointDetails) {
            const { controller, method } = endpointDetails;

            if (excludedControllers.has(controller)) return; // 🚀 Skip excluded controllers

            // Ensure controller and method exist
            if (!controllerMethodMap[controller]) controllerMethodMap[controller] = {};
            if (!controllerMethodMap[controller][method]) {
              controllerMethodMap[controller][method] = false;
            }

            if (userRoles.includes('admin')) {
              if (er.roles?.some(role => userRoles.includes(role))) {
                controllerMethodMap[controller][method] = true;
              }
            }
            else {
              if (er.roles?.some(role => {
                const actualRole = roleMap.get(role); // Get the properly-cased role
                return actualRole && userRoles.includes(actualRole);
              })) {
                controllerMethodMap[controller][method] = true;
              }

            }
          }
          // ✅ Check if the logged-in user has access
          // if (er.roles?.some(role => userRoles.includes(role))) {
          //   controllerMethodMap[controller][method] = true;
          // }

          //   if (er.roles?.some(role => {
          //     const actualRole = roleMap.get(role); // Get the properly-cased role
          //     return actualRole && userRoles.includes(actualRole);
          //   })) {
          //     controllerMethodMap[controller][method] = true;
          //   }

          // }
        });

        return controllerMethodMap;
      }

    } catch (error) {
      this.logger.error('Error fetching controller method roles:', error);
      throw error;
    }
  }

  async getResourceWiseActionsForUser(userId: string) {
    try {
      console.log(userId);

      // 🚀 Step 1: Fetch user details (orgId & roles)
      const user = await this.basicUserModel.findOne({ _id: userId }).lean();
      if (!user) throw new BadRequestException("User not found");

      const orgId = user.org;
      const userRoles = user.roles ?? [];

      // 🚀 Step 2: Fetch endpoint permissions & roles in parallel
      const [endpointPermissions] = await Promise.all([
        this.endpointPermissionModel.find().lean(),
        // this.rolesModel.find().lean(),
      ]);

      // 🚀 Step 3: Fetch **role-based permissions** first
      // let roleBasedPermissions: any[] = [];
      // if (orgId) {
      //   roleBasedPermissions = await this.endpointsRolesModel.find({ org: orgId, userId: { $exists: false } }).lean();
      // }

      // 🚀 Step 4: Fetch **user-specific permissions**
      const userSpecificPermissions = await this.endpointsUsersModel.find({ userId: userId, org: orgId }).lean();

      // 🚀 Step 5: Create Maps for efficient lookup
      const endpointMap = new Map(endpointPermissions.map(ep => [ep._id.toString(), { controller: ep.controller, method: ep.method }]));
      // const roleMap = new Map(rolesData.map(role => [role.role.toLowerCase(), role.role]));

      const excludedControllers = new Set<string>(Object.values(ExcludedControllersForGroupBy));
      const controllerMethodMap: Record<string, Record<string, boolean>> = {};
      const allowedEndpoints = new Set<string>();

      // 🚀 Step 6: Process **role-based permissions**
      // roleBasedPermissions.forEach(({ endPoint, roles }) => {
      //   const endpointDetails = endpointMap.get(endPoint.toString());
      //   if (!endpointDetails || excludedControllers.has(endpointDetails.controller)) return;

      //   const { controller, method } = endpointDetails;
      //   controllerMethodMap[controller] ??= {};
      //   controllerMethodMap[controller][method] = false;

      //   // if (
      //   //   userRoles.includes(Role.Admin) ||
      //   //   roles?.some((role: string) => userRoles.includes(roleMap.get(role.toLowerCase()) ?? role))
      //   // ) {
      //   //   allowedEndpoints.add(`${controller}:${method}`);
      //   // }

      //   if (
      //     userRoles.includes(Role.Admin) || // Admin has all permissions
      //     (Array.isArray(roles) && roles.some(role => {
      //       const actualRole = roleMap.get(role.toLowerCase()) ?? role; // Ensure role mapping
      //       return userRoles.includes(actualRole); // Check if user has the role
      //     }))
      //   ) {
      //     allowedEndpoints.add(`${controller}:${method}`);
      //   }

      // });

      // 🚀 Step 7: Process **user-specific permissions**

      userSpecificPermissions.forEach(({ endPoint }) => {
        const endpointDetails = endpointMap.get(endPoint.toString());
        if (!endpointDetails || excludedControllers.has(endpointDetails.controller)) return;

        const { controller, method } = endpointDetails;
        controllerMethodMap[controller] ??= {};
        controllerMethodMap[controller][method] = true;
        allowedEndpoints.add(`${controller}:${method}`);
      });

      // 🚀 Step 2: Ensure all controllers and methods exist, setting defaults
      endpointMap.forEach(({ controller, method }) => {
        if (excludedControllers.has(controller)) return; // 🚀 Skip excluded controllers
        controllerMethodMap[controller] ??= {};
        controllerMethodMap[controller][method] = allowedEndpoints.has(`${controller}:${method}`);
      });

      // 🚀 Step 8: Apply collected permissions to response
      // allowedEndpoints.forEach(endpoint => {
      //   const [controller, method] = endpoint.split(":");
      //   controllerMethodMap[controller] ??= {};
      //   controllerMethodMap[controller][method] = true;
      // });

      return controllerMethodMap;
    } catch (error) {
      this.logger.error("Error fetching user permissions:", error);
      throw error;
    }
  }




  // @OnEvent('org.CloneDefaultPermissionsNoOrgRoleCreated') // Also listen for activation event
  async cloneDefaultEndpointsForNoOrgRoles(role: any) {
    // const { role } = payload; // Single role from payload
    const normalizedRole = role.toLowerCase(); // Normalize for consistency

    try {
      let allowedControllers: any;
      // Convert enum values to a set for quick lookup
      if (role === Role.Freelancer) {
        allowedControllers = new Set<string>(Object.values(AllowedControllersForDeafultFreeLancerRole));

      } else if (role === Role.JobSeeker) {
        allowedControllers = new Set<string>(Object.values(AllowedControllersForDeafultJobseekerRoles));
      }

      // Fetch all default endpoints
      const defaultEndpoints = await this.endpointPermissionModel.find().lean();


      const filteredEndpoints = defaultEndpoints.filter(ep => {
        const fullAccess = allowedControllers.has(ep.controller);
        const methodRestricted = allowedControllers.has(`${ep.controller}:${ep.method}`);

        return fullAccess || methodRestricted;
      });

      // Fetch existing endpoint roles where `org` does NOT exist
      const existingRoles = await this.endpointsRolesModel.find({ org: { $exists: false } }).lean();

      // Create a map of existing roles per endpoint
      const existingRolesMap = new Map<string, Set<string>>();
      for (const er of existingRoles) {
        existingRolesMap.set(er.endPoint.toString(), new Set((er.roles || []).map(r => r.toLowerCase())));
      }

      // Prepare bulk updates
      const roleUpdates = filteredEndpoints.map(endpoint => {
        const endpointKey = endpoint._id.toString();
        const existingRolesLower = existingRolesMap.get(endpointKey) || new Set();

        if (!existingRolesLower.has(normalizedRole)) {
          existingRolesLower.add(normalizedRole);
        }

        return {
          updateOne: {
            filter: { endPoint: endpoint._id.toString(), org: { $exists: false } }, // Ensure `org` does not exist
            update: { $addToSet: { roles: normalizedRole } },
            upsert: true
          }
        };
      });

      // Perform bulk write operations
      if (roleUpdates.length > 0) {
        await this.endpointsRolesModel.bulkWrite(roleUpdates);
      }
      this.logger.log(`Assigned role "${role}" to ${roleUpdates.length} endpoint roles (without org field)`);
      return (`Assigned role "${role}" to ${roleUpdates.length} endpoint roles (without org field)`);
    } catch (error) {
      this.logger.error(`Error assigning role "${role}" to default endpoints: ${error.message}`, error);
    }
  }

  async findRolesByStatus(orgId: string, isInternal: boolean) {
    try {
      let conditions: any = { isDeleted: false, isDefault: false };
      if (orgId) {
        conditions.orgId = orgId;
      }

      // Fetch all endpoint roles for the given organization
      const endpointRoles = await this.endpointsRolesModel.find({ org: orgId }).lean();

      // Fetch only the endpoint permissions related to the "Jobs" controller with POST and PATCH methods
      const jobEndpoints = await this.endpointPermissionModel.find({
        controller: "Jobs",
        method: { $in: ["POST", "PATCH"] }
      }).lean();

      // Create a mapping of endpoint IDs to their methods
      const jobEndpointMap: Record<string, string> = {};
      jobEndpoints.forEach(ep => {
        jobEndpointMap[ep._id.toString()] = ep.method;
      });

      // Create a set to store unique roles
      const jobRoles = new Set<string>();

      // Create a mapping of endpoint IDs to their methods
      const jobEndpointIds = new Set(jobEndpoints.map(ep => ep._id.toString()));

      // Extract roles for matching endpoints
      endpointRoles.forEach(er => {
        if (jobEndpointIds.has(er.endPoint.toString())) {
          er.roles?.forEach(role => jobRoles.add(role));
        }
      });


      // return jobRolesMap;
      // if (isInternal) {
      //   conditions.role = { $in: Array.from(jobRoles) };
      // }
      // else {
      //   conditions.role = { $nin: Array.from(jobRoles) };
      // }
      console.log(conditions);
      // const total = await this.rolesModel.countDocuments(conditions); // Get total count
      const roles = await this.rolesModel
        .find(conditions)
        .select('role roleAlias') // Fetch only the role name
        .sort({ updatedAt: -1 })
        .exec();

      const filteredRoles = roles.filter(r => r.role.toLowerCase() !== 'vendor');

      return filteredRoles;
    } catch (error) {
      this.logger.error("Error fetching job controller roles:", error);
      throw error;
    }
  }

  async updateIsCustomFlag(user: string,isCustom?: boolean ) {
    try {
      const userId = user; // Extract userId from the payload

      const existingUser = await this.basicUserModel.findByIdAndUpdate(
        userId,
        { $set: { isCustom: isCustom } },
        { new: true } // Returns the updated document
      );
      if (!user) throw new BadRequestException("User not found");

      return { message: "User permissions updated successfully" };
    } catch (error) {
      console.error("Error updating user permissions:", error);
      throw error;
    }
  }

  @OnEvent('org.CloneDefaultPermissionsForOrgDefaultRoles')
  async handleOrgRolePermissionsUpdateEvent(event: { orgId: string; payload: CreateEndpointPermissionDto }) {
    await this.updateOrgRolePermissions(event.orgId, event.payload);
  }

}
