import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Query } from '@nestjs/common';
import { EvaluationFormService } from './evaluation-form.service';
import { CreateEvaluationFormDto } from './dto/create-evaluation-form.dto';
import { UpdateEvaluationFormDto } from './dto/update-evaluation-form.dto';
import { ApiBearerAuth, ApiOperation, ApiParam, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { RolesGuard } from 'src/auth/guards/roles/roles.guard';
import { AuthJwtGuard } from 'src/auth/guards/auth-jwt/auth-jwt.guard';
import { Roles } from 'src/auth/decorators/roles.decorator';
import { Role } from 'src/auth/enums/role.enum';
import { validateObjectId } from 'src/utils/validation.utils';
@Controller('')
@ApiTags('Evaluation-Forms')
export class EvaluationFormController {
  constructor(private readonly evaluationFormService: EvaluationFormService) { }

  @Post()
  @ApiOperation({ summary: 'Add a new skill', description: `This endpoint allows you to add a new skill. This is accessible for "BUHead", "TeamLead", "DeliveryManager" and "Recruiter".` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.AccountManager, Role.TeamLead, Role.TeamMember, Role.Recruiter, Role.Vendor, Role.JobSeeker, Role.Freelancer)
  @Roles()
  @ApiResponse({ status: 201, description: 'Added a new skill.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Data ' })
  @ApiResponse({ status: 401, description: 'Unauthorized ' })
  @ApiResponse({ status: 403, description: 'Forbidden, logged in user "BUHead", "TeamLead", "DeliveryManager" and "Recruiter" can only access this end point.' })
  create(@Body() createEvaluationFormDto: CreateEvaluationFormDto) {
    return this.evaluationFormService.create(createEvaluationFormDto);
  }

  // @Get()
  // findAll() {
  //   return this.evaluationFormService.findAll();
  // }

  // @Get(':id')
  // findOne(@Param('id') id: string) {
  //   return this.evaluationFormService.findOne(+id);
  // }

  @Patch(':skillId')
  @ApiOperation({ summary: 'Update a skill by its Id', description: `This endpoint updates a skill by Id. This is accessible for "BUHead", "TeamLead", "DeliveryManager" and "Recruiter".` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.AccountManager, Role.TeamLead, Role.TeamMember, Role.Recruiter, Role.Vendor, Role.JobSeeker, Role.Freelancer)
  @Roles()
  @ApiResponse({ status: 200, description: 'Skill is updated.' })
  @ApiResponse({ status: 401, description: 'Unauthorized ' })
  @ApiResponse({ status: 403, description: 'Forbidden, logged in user "BUHead", "TeamLead", "DeliveryManager" and "Recruiter" can only access this end point.' })
  @ApiResponse({ status: 404, description: 'Skill not found.' })
  @ApiParam({ name: 'skillId', description: 'ID of the Skill' })
  update(@Param('skillId') skillId: string, @Body() updateEvaluationFormDto: UpdateEvaluationFormDto) {
    const objId = validateObjectId(skillId);
    return this.evaluationFormService.update(objId, updateEvaluationFormDto);
  }

  @Delete(':skillId/hard-delete')
  @ApiOperation({ summary: 'Permannently delete a skill by Id', description: `This endpoint permanently deletes a skill by Id. This is accessible for "BUHead", "TeamLead", "DeliveryManager" and "Recruiter".` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.AccountManager, Role.TeamLead, Role.TeamMember, Role.Recruiter, Role.Vendor, Role.JobSeeker, Role.Freelancer)
  @Roles()
  @ApiResponse({ status: 200, description: 'Skill deleted permanently.' })
  @ApiResponse({ status: 401, description: 'Unauthorized ' })
  @ApiResponse({ status: 403, description: 'Forbidden, logged in user "BUHead", "TeamLead", "DeliveryManager" and "Recruiter" can only access this end point.' })
  @ApiResponse({ status: 404, description: 'Skill not found.' })
  @ApiParam({ name: 'skillId', description: 'ID of the Skill' })
  remove(@Param('skillId') skillId: string) {
    const objId = validateObjectId(skillId);
    return this.evaluationFormService.remove(objId);
  }

  @Get('filter-skill-by-primary-and-secondary')
  @ApiResponse({ status: 200, description: `Skills are retrieved.` })
  @ApiResponse({ status: 404, description: `Unable to retrieve skills based on filtering.` })
  @ApiResponse({ status: 401, description: `Unauthorized. ` })
  // @ApiResponse({ status: 403, description: `Forbidden, logged in user can only access this end point.` })
  @ApiOperation({ summary: `Retrieve skills based on primary/secondary skill`, description: `This endpoint returns Skill based on primary/secondary skill. This is accessible for everyone.` })
  @ApiQuery({ name: 'status', description: 'Status of the skill (primary or secondary)', required: true })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.AccountManager, Role.TeamLead, Role.TeamMember, Role.Recruiter, Role.Vendor, Role.JobSeeker, Role.Freelancer)
  @Roles()
  filterSkill(@Query('status') status: string) {
    return this.evaluationFormService.filterSkill(status);
  }
}
