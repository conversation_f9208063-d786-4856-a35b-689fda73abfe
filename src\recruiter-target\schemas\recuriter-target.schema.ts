
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Types } from 'mongoose';
import { Account } from 'src/account/schemas/account.schema';
import { Client } from 'src/client/schemas/client.schema';
import { Contact } from 'src/contact/schemas/contact.schema';
import { Org } from 'src/org/schemas/org.schema';
import { BasicUser } from 'src/user/schemas/basic-user.schema';

export type NoteDocument = HydratedDocument<RecruiterTarget>;


@Schema({ timestamps: true })
export class RecruiterTarget {

    @Prop({ type: Types.ObjectId, ref: 'Org' })
    org?: Org;

    @Prop({ type: Types.ObjectId, ref: 'BasicUser' })
    recruiterId?: BasicUser;
    
    // Only allows numbers (integer or string representation of a number)
    @Prop({
        type: Number,
        required: true,
        min: 0 // Ensures only positive numbers
    })
    targetNumber: number;

    // Enforces only year-month format (YYYY-MM)
    @Prop({
        type: String,
        required: true,
        match: [/^\d{4}-(0[1-9]|1[0-2])$/, 'Invalid month format. Use YYYY-MM']
    })
    targetMonth: string;


    @Prop({
        required: true,
        type: Types.ObjectId, ref: 'BasicUser'
    })
    createdBy: BasicUser;

  

}

export const RecruiterTargetSchema = SchemaFactory.createForClass(RecruiterTarget);