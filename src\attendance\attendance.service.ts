import { ForbiddenException, Injectable, InternalServerErrorException, Logger, NotFoundException } from '@nestjs/common';
import { CreateAttendanceDto } from './dto/create-attendance.dto';
import { UpdateAttendanceDto } from './dto/update-attendance.dto';
import { CreateAttendanceSessionDto } from './dto/create-attendance-session.dto';
import { UpdateAttendanceSessionDto } from './dto/update-attendance-session.dto';
import { AttendanceSession } from './schemas/attendance-session.schema';
import { Attendance } from './schemas/attendance.schema';
import { Model, Types } from 'mongoose';
import { InjectModel } from '@nestjs/mongoose';
import { Project } from 'src/projects/schemas/project.schema';

@Injectable()
export class AttendanceService {

  private readonly logger = new Logger(AttendanceService.name);

  constructor(
    @InjectModel(Attendance.name) private readonly attendanceModel: Model<Attendance>,
    @InjectModel(AttendanceSession.name) private readonly sessionModel: Model<AttendanceSession>,
    @InjectModel(Project.name) private readonly projectModel: Model<Project>,
  ) { }

  async findOrCreateTodayAttendance(userId: string): Promise<Attendance> {
    const todayStart = new Date();
    todayStart.setHours(0, 0, 0, 0);

    let attendance = await this.attendanceModel.findOne({
      userId,
      date: { $gte: todayStart }
    });

    if (!attendance) {
      attendance = await this.attendanceModel.create({
        userId,
        date: new Date(),
        sessions: [],
        status: 'present'
      });
    }

    return attendance;
  }

  async findTodayAttendance(userId: string): Promise<Attendance | null> {
    const todayStart = new Date();
    todayStart.setHours(0, 0, 0, 0);

    return this.attendanceModel.findOne({
      userId,
      date: { $gte: todayStart }
    });
  }

  async findActiveSession(attendanceId: string): Promise<AttendanceSession | null> {
    return this.sessionModel.findOne({
      attendanceId: new Types.ObjectId(attendanceId),
      checkOut: null,
    });
  }

  async findActiveSessionForProject(attendanceId: string, projectId: string): Promise<AttendanceSession | null> {
    return this.sessionModel.findOne({
      attendanceId: new Types.ObjectId(attendanceId),
      projectId: new Types.ObjectId(projectId),
      checkOut: null,
    });
  }

  async getProjectIfValid(projectId: string, orgId: string): Promise<any> {
    const project = await this.projectModel.findById(projectId);

    // First check if the project exists
    if (!project) throw new NotFoundException('Project not found');

    // Then check orgId match, safely
    if (project.org?.toString() !== orgId.toString()) {
      throw new ForbiddenException('User does not belong to this organization');
    }

    return project;
  }



  async create(createAttendanceDto: CreateAttendanceDto) {
    const attendanceForm = new this.attendanceModel(createAttendanceDto);
    const attendance = await attendanceForm.save();
    this.logger.log(`Attendance created: ${JSON.stringify(attendance)}`);
    return attendance;
  }

  async findAll(userId: string) {
    const populateOptions = this.getPopulateOptions();
    try {
      const attendances = await this.attendanceModel.find({ userId })
        .populate(populateOptions)
        .sort({ createdAt: -1 })
        .exec();
      return attendances;
    } catch (error) {
      this.logger.error(`Failed to fetch Attendance. ${error}`);
      throw new InternalServerErrorException(
        `Failed to fetch Attendance. ${error.message}`,
      );
    }
  }

  async findOne(id: string) {
    const populateOptions = this.getPopulateOptions();
    try{
    const attendance = await this.attendanceModel.findById(id)
      .populate(populateOptions)
      .exec();
    return attendance;
    }
    catch (error) {
      this.logger.error(`Failed to fetch Attendance. ${error}`);
      throw new InternalServerErrorException(
        `Failed to fetch Attendance. ${error.message}`,
      );
    }
  }

  async update(id: string, updateAttendanceDto: UpdateAttendanceDto) {
    const attendance = await this.attendanceModel.findByIdAndUpdate(id, updateAttendanceDto, { new: true });
    if (!attendance) throw new NotFoundException('Attendance not found');
    return attendance;
  }

  remove(id: string) {
    return this.attendanceModel.findByIdAndDelete(id);
  }

  async createSession(attendanceId: string, dto: CreateAttendanceSessionDto): Promise<AttendanceSession> {
    const session = await this.sessionModel.create({
      ...dto,
      attendanceId: new Types.ObjectId(attendanceId),
      projectId: dto.projectId ? new Types.ObjectId(dto.projectId) : undefined, // convert if provided
    });

    await this.attendanceModel.findByIdAndUpdate(attendanceId, {
      $push: { sessions: session._id },
    });

    return session;
  }

  async updateSession(sessionId: string, dto: UpdateAttendanceSessionDto) {
    return this.sessionModel.findByIdAndUpdate(
      new Types.ObjectId(sessionId),
      { checkOut: dto.checkOut },
      { new: true },
    );
  }

  async removeSession(sessionId: string) {
    const session = await this.sessionModel.findById(sessionId);
    if (!session) throw new NotFoundException('Session not found');

    await this.attendanceModel.findByIdAndUpdate(session.attendanceId, {
      $pull: { sessions: session._id },
    });
    return this.sessionModel.findByIdAndDelete(sessionId);
  }

  

  getPopulateOptions(): any[] {
    const populateOptions = [
      { path: 'userId', select: '_id roles firstName org', model: 'BasicUser' , populate:{
        path: 'org',
        select: '_id title', // adjust fields as needed
        model: 'Org',
      }},

      {
        path: 'sessions',
        select: '_id projectId checkIn checkOut',
        model: 'AttendanceSession',
        populate:{
          path: 'projectId',
          select: '_id name  org client startDate endDate department ',
          model: 'Project',
          populate: [
          {
            path: 'org',
            select: '_id title', // adjust fields as needed
            model: 'Org',
          },
          {
            path: 'client',
            select: '_id title', // adjust fields as needed
            model: 'Org',
          },
          {
            path: 'departments',
            select: '_id label', // adjust fields as needed
            model: 'BusinessUnit',
          },
        ],
        }
      },

    ]
    return populateOptions;

  }
}
