import { Prop, Schem<PERSON>, SchemaFactory } from "@nestjs/mongoose";
import { Types } from "mongoose";
import { JobApplication } from "src/job-application-form/schemas/job-application.schema";
import { Platform, ScreeningType } from "src/shared/constants";
import { BasicUser } from "src/user/schemas/basic-user.schema";

@Schema({ timestamps: true })
export class Interview {

    @Prop({
        type: Types.ObjectId,
        required: true,
        ref: 'JobApplication'
    })
    jobApplication: JobApplication;

    @Prop({
        type: Date,
        required: true,
    })
    interviewDate: Date;

    //technical panel member email ids who is going to take interview
    @Prop({
        required: false,
        type: [String],
        default: []
    })
    technicalPanel?: string[];

    //other email Id's to which we need to send invites through email
    @Prop({ type: String, required: false })
    others?: string[];

    @Prop({
        type: String,
        required: true,
        trim: true,
        default: ScreeningType.PHONE_SCREENING,
        enum: Object.values(ScreeningType),
    })
    screeningType: string;

    @Prop({
        type: String,
        required: false,
        trim: true,
        enum: Object.values(Platform),
    })
    platform?: string;

    @Prop({
        type: String,
        required: false,
        trim: true,
    })
    meetingUrl?: string;

    @Prop({
        type: String,
        required: false
    })
    meetingId?: string;

    @Prop({
        type: String,
        required: false,
    })
    meetingCode?: string;

    @Prop({
        type: String,
        required: false,
        trim: true,
    })
    spoc?: string;

    @Prop({
        type: String,
        required: false,
        trim: true,
    })
    spocPhoneNumber?: string;

    @Prop({
        type: String,
        required: false,
    })
    companyAddress?: string;

    @Prop({
        type: Boolean,
        required: false,
        default: false,
    })
    isRescheduled?: boolean;

    @Prop({
        type: Boolean,
        required: false,
        default: false,
    })
    isCandidateAttended: boolean;

    //TODO: Need to modify schema to schedule multiple interviews
    //with multiple candidates in different timings in different pannels

}

export const InterviewSchema = SchemaFactory.createForClass(Interview);