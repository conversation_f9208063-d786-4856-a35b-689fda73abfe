import { BadRequestException, Injectable, InternalServerErrorException, Logger } from '@nestjs/common';
import { CreateVendorInviteDto } from './dto/create-vendor-invite.dto';
import { UpdateVendorInviteDto } from './dto/update-vendor-invite.dto';
import { ConfigService } from '@nestjs/config';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { VendorInvite, VendorInviteDocument } from './schemas/vendor-invite.schmea';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { randomBytes } from 'crypto';
import { BasicUser } from 'src/user/schemas/basic-user.schema';
import { Org } from 'src/org/schemas/org.schema';
import { UserTemp } from 'src/user/schemas/user-temp.schema';
import { OrgType } from 'src/shared/constants';


@Injectable()
export class VendorInviteService {
  private readonly logger = new Logger(VendorInviteService.name);

  constructor(private configService: ConfigService,
    @InjectModel(VendorInvite.name) private VendorInviteModel: Model<VendorInvite>,
    @InjectModel(BasicUser.name) private basicUserModel: Model<BasicUser>,
    @InjectModel(UserTemp.name) private userTempModel: Model<UserTemp>,
    @InjectModel(Org.name) private orgModel: Model<Org>,
    private eventEmitter: EventEmitter2) { }


  async create(createVendorInviteDto: CreateVendorInviteDto,user : any) {
    try {
      console.log("createVendordto", createVendorInviteDto);
      const { email } = createVendorInviteDto;

      // 1️⃣ Check if the email already exists in BasicUser
      // const existingUser = await this.basicUserModel.findOne({ email }).exec();
      // if (existingUser) {
      //   throw new BadRequestException('This user is already registered in the system.');
      // }

      const existingInvite = await this.VendorInviteModel.findOne({
        email : email,
        companyId: new Types.ObjectId(user?.org?._id)  // Ensure the org matches the user's org
      }).exec();

      if (existingInvite) {
        throw new BadRequestException('The Vendor with this email is already invited with this organization.');
      }

      // 2️⃣ Check if the email exists in any Org's contactDetails array
      const existingOrg = await this.orgModel.findOne({
        contactDetails: { $elemMatch: { contactEmail: email } }, // Check inside array
        orgType: OrgType.VENDOR_ORG,
        companyId: user?.org?._id.toString() // Ensure the org matches the user's org
      }).exec();

      if (existingOrg) {
        throw new BadRequestException('This email is already associated with this organization as a Vendor.');
      }


      // Save the invitation in the database
      const vendorInvite = new this.VendorInviteModel(createVendorInviteDto);
      await vendorInvite.save();

      // Populate createdBy field to get the associated org
      const populatedInvite = await this.VendorInviteModel.findById(vendorInvite._id)
        .populate({
          path: 'createdBy',
          select: 'firstName lastName email org', // Selecting necessary fields from BasicUser
          populate: {
            path: 'org',
            select: '_id title description', // Selecting specific fields from Org
            model: 'Org'
          }
        })
        .exec();

      if (!populatedInvite) {
        throw new Error('Failed to retrieve created VendorInvite');
      }

      if (populatedInvite.createdBy && populatedInvite.createdBy.org) {
        const orgId = (populatedInvite.createdBy.org as any)._id;
        const orgTitle = (populatedInvite.createdBy.org as any).title;

        // Update vendor invite with companyId
        await this.VendorInviteModel.findByIdAndUpdate(vendorInvite._id, {
          companyId: orgId
        });

        // Emit event with additional company details
        this.eventEmitter.emit('vendor.invite.sent', {
          ...createVendorInviteDto,
          companyId: orgId,
          companyName: orgTitle,  // Include company name in the payload
        });
      }
      return {
        success: true,
        message: 'Vendor invite created successfully',
        data: vendorInvite,
      };

    } catch (error) {
      this.logger.error(`Failed to create vendor invite: ${error.message}`);
      throw new BadRequestException(error.message);
    }
  }

  async findAll(userId: string) {
    const populateOptions = this.getPopulateOptions();
    try {
      // Find all bench candidates created by the specified user
      const benchCandidates = await this.VendorInviteModel
        .find({ createdBy: userId })
        .populate(populateOptions)
        .sort({ createdAt: -1 })
        .exec();
      return benchCandidates;
    } catch (error) {
      this.logger.error(`Failed to fetch Bench Candidates. ${error}`);
      throw new InternalServerErrorException(
        `Failed to fetch Bench Candidates. ${error.message}`,
      );
    }
  }

  async findById(inviteId: string) {
    const populateOptions = this.getPopulateOptions();
    try {
      // Find the vendor invite by ID and populate relevant fields
      const vendorInvite = await this.VendorInviteModel.findById(inviteId)
        .populate(populateOptions)
        .exec();

      if (!vendorInvite) {
        throw new BadRequestException('Vendor invite not found.');
      }

      return {
        success: true,
        message: 'Vendor invite retrieved successfully',
        data: vendorInvite,
      };
    } catch (error) {
      this.logger.error(`Failed to fetch Vendor Invite by ID: ${error.message}`);
      throw new InternalServerErrorException(
        `Failed to fetch Vendor Invite. ${error.message}`,
      );
    }
  }

  async removeInviteIfUserRegistered(email: string, isTemp: boolean,companyId?: string) {
    try {
      // Check if the email exists in the BasicUser collection
      const existingUser = isTemp
        ? await this.userTempModel.findOne({ email, isDeleted: false }).exec()
        : await this.basicUserModel.findOne({ email, isDeleted: false }).exec();

      if (!existingUser) {
        this.logger.log(`No registered user found with email: ${email} in ${isTemp ? 'temp_user' : 'basicUser'} collection.`);
        return;
      }

      // Delete the invite if the user is registered
      const deletedInvite = await this.VendorInviteModel.findOneAndDelete({ email,companyId : new Types.ObjectId(companyId) }).exec();

      if (deletedInvite) {
        this.logger.log(`Deleted invite for email: ${email} as the user is now registered.`);
      } else {
        this.logger.log(`No invite found for email: ${email}`);
      }
    } catch (error) {
      this.logger.error(`Failed to remove vendor invite for ${email}: ${error.message}`);
      throw new InternalServerErrorException(
        `Failed to remove vendor invite. ${error.message}`,
      );
    }
  }


  async sendReminderToInvitee(email: string) {
    try {
      // 1️⃣ Find the existing invite by email
      const existingInvite = await this.VendorInviteModel.findOne({ email }).exec();

      console.log("existingInvite", existingInvite)

      if (!existingInvite) {
        throw new BadRequestException(`No invite found with email: ${email}`);
      }

      await existingInvite.save();

      // Fetch related organization info (e.g., company name)
      const populatedInvite = await this.VendorInviteModel.findById(existingInvite._id)
        .populate({
          path: 'createdBy', // Assuming 'createdBy' is the user who created the invite
          select: 'firstName lastName email org',
          populate: {
            path: 'org', // Assuming 'org' is the organization associated with the user
            select: '_id title', // Ensure you select the organization details
          },
        })
        .exec();

      if (!populatedInvite || !populatedInvite.createdBy || !populatedInvite.createdBy.org) {
        throw new BadRequestException('Organization details not found.');
      }

      // Retrieve the organization name and vendor name
      const orgTitle = populatedInvite.createdBy.org.title || 'Unknown Organization';
      const vendorName = `${populatedInvite.createdBy.firstName} ${populatedInvite.createdBy.lastName}` || 'Unknown Vendor';


      // 3️⃣ Emit an event if needed
      this.eventEmitter.emit('vendor.invite.reminder.sent', {
        email,
        vendorName: existingInvite.vendorName, // Use vendorName from existingInvite
        userName: existingInvite.userName, // Use userName from existingInvite
        companyName: orgTitle,  // Ensure companyName is included
        message: 'Reminder sent successfully for vendor invite.',
      });

      return {
        success: true,
        message: 'Reminder sent successfully for vendor invite.',
        data: existingInvite,
      };

    } catch (error) {
      this.logger.error(`Failed to send reminder for vendor invite: ${error.message}`);
      throw new InternalServerErrorException(
        `Failed to send reminder for vendor invite. ${error.message}`,
      );
    }
  }




  getPopulateOptions(): any[] {
    const populateOptions = [
      { path: 'createdBy', select: '_id roles firstName', model: 'BasicUser' },
      { path: 'companyId', select: '_id title orgType description', model: 'Org' }
    ];

    return populateOptions;
  }

  emitEvent(eventName: string, payload: any) {
    this.eventEmitter.emit(eventName, payload);
  }

}
