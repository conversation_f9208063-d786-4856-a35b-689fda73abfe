import { Controller, Get, Post, Body, Patch, Param, Delete, Req, UseGuards, Query } from '@nestjs/common';
import { NotificationsService } from './notifications.service';
import { ApiBearerAuth, ApiQuery, ApiTags } from '@nestjs/swagger';
import { AuthJwtGuard } from 'src/auth/guards/auth-jwt/auth-jwt.guard';
import { RolesGuard } from 'src/auth/guards/roles/roles.guard';
import { Roles } from 'src/auth/decorators/roles.decorator';
import { validateObjectId } from 'src/utils/validation.utils';

@Controller('')
@ApiTags('notifications')
export class NotificationsController {
  constructor(private readonly notificationsService: NotificationsService) {}

  @Get()
  @ApiBearerAuth()
  @ApiQuery({ name: 'jobId', required: false, type: String, description: 'Id of the Job.' })
  @UseGuards(AuthJwtGuard)
  async getMyNotifications(@Req() req: any,@Query('jobId') jobId?: string) {
      const sanitizedJobId = jobId ? validateObjectId(jobId) : null;
    return this.notificationsService.getUserNotifications(req.user._id.toString(), sanitizedJobId?.toString());
  }

  @Get('all')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  async getMyAllNotifications(@Req() req: any) {
    return this.notificationsService.getAllUserNotifications(req.user._id.toString());
  }

  @Patch(':id/read')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  async markAsRead(@Param('id') id: string) {
    return this.notificationsService.markAsRead(id);
  }

  @Patch('read-all')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  async markAllAsRead(@Req() req: any) {
    return this.notificationsService.markAllAsRead(req.user.userId);
  }

  @Delete(':id')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  async deleteNotification(@Param('id') id: string) {
    return this.notificationsService.deleteNotification(id);
  }
}
