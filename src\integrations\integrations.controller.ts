import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Logger, Req, Query } from '@nestjs/common';

import { CreateIntegrationDto } from './dto/create-integration.dto';
import { UpdateIntegrationDto } from './dto/update-integration.dto';
import { ApiBearerAuth, ApiOperation, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Role } from 'src/auth/enums/role.enum';
import { RolesGuard } from 'src/auth/guards/roles/roles.guard';
import { AuthJwtGuard } from 'src/auth/guards/auth-jwt/auth-jwt.guard';
import { Roles } from 'src/auth/decorators/roles.decorator';
import { validateObjectId } from 'src/utils/validation.utils';
import { IntegrationService } from './integrations.service';
import { GoogleOAuthCallbackDto } from './dto/google-oauth-callback.dto';
import { ZoomOAuthCallbackDto } from './dto/zoom-oauth-callback.dto';
import { MicrosoftOAuthCallbackDto } from './dto/microsoft-oauth-callback.dto';
import { ValidateWhatsAppTokenDto } from './dto/validate-whatsapp-token.dto';
import { SendWhatsAppDto } from './dto/send-whatsapp.dto';


@Controller('')
@ApiTags('Integrations')
export class IntegrationController {
  constructor(private readonly IntegrationService: IntegrationService) { }

  @Post()
  @ApiOperation({ summary: 'Create integration', description: `Allows creation of a new integration.` })
  @ApiResponse({ status: 201, description: 'Integration created.' })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @ApiResponse({ status: 401, description: `Unauthorized.` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  @Roles()
  create(@Req() req: any, @Body() createIntegrationDto: CreateIntegrationDto) {
    createIntegrationDto.createdBy = req.user._id;
    if (req.user.org) {
      createIntegrationDto.org = req.user.org._id;
    }
    return this.IntegrationService.create(createIntegrationDto, req.user);
  }

  @Post('google/callback')
  @ApiOperation({
    summary: 'Handle Google OAuth callback',
    description: 'Receives code and state from Google and exchanges it for access token.',
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  @ApiResponse({ status: 200, description: 'Integration updated with tokens.' })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  callback(@Body() callbackDto: GoogleOAuthCallbackDto) {
    return this.IntegrationService.handleGoogleCallback(callbackDto);
  }

  @Get('google/regenerate-consent')
  @ApiOperation({
    summary: 'Regenerate Google OAuth consent URL',
    description: 'Regenerates the Google OAuth consent URL for a specific integration.',
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  @ApiResponse({ status: 200, description: 'Google OAuth consent URL regenerated.' })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @ApiQuery({ name: 'integrationId', required: true, type: String, description: 'Integration ID' })
  async regenerateGoogleConsentUrl(@Query('integrationId') integrationId: string) {
    validateObjectId(integrationId);
    return this.IntegrationService.regenerateConsentUrl(integrationId);
  }

  @Post('zoom/callback')
  @ApiOperation({
    summary: 'Handle Zoom OAuth callback',
    description: 'Receives code and state from Zoom and exchanges it for access token.',
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  @ApiResponse({ status: 200, description: 'Integration updated with tokens.' })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  callbackZoom(@Body() callbackDto: ZoomOAuthCallbackDto) {
    return this.IntegrationService.handleZoomCallback(callbackDto);
  }

  @Get('zoom/regenerate-consent')
  @ApiOperation({
    summary: 'Regenerate Zoom OAuth consent URL',
    description: 'Regenerates the Zoom OAuth consent URL for a specific integration.',
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  @ApiResponse({ status: 200, description: 'Zoom OAuth consent URL regenerated.' })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @ApiQuery({ name: 'integrationId', required: true, type: String, description: 'Integration ID' })
  async regenerateZoomConsentUrl(@Query('integrationId') integrationId: string) {
    validateObjectId(integrationId);
    return this.IntegrationService.regenerateZoomConsentUrl(integrationId);
  }

  @Post('microsoft/callback')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  @ApiOperation({
    summary: 'Handle Microsoft Teams OAuth callback',
    description: 'Receives code and state from Microsoft and exchanges it for access tokens.',
  })
  @ApiResponse({ status: 200, description: 'Integration updated with tokens.' })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  async callbackMicrosoft(@Body() callbackDto: MicrosoftOAuthCallbackDto) {
    return this.IntegrationService.handleMicrosoftCallback(callbackDto);
  }

  @Get('microsoft/regenerate-consent')
  @ApiOperation({
    summary: 'Regenerate Microsoft Teams OAuth consent URL',
    description: 'Regenerates the Microsoft Teams OAuth consent URL for a specific integration.',
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  @ApiResponse({ status: 200, description: 'Microsoft Teams OAuth consent URL regenerated.' })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @ApiQuery({ name: 'integrationId', required: true, type: String, description: 'Integration ID' })
  async regenerateMicrosoftConsentUrl(@Query('integrationId') integrationId: string) {
    validateObjectId(integrationId);
    return this.IntegrationService.regenerateMicrosoftConsentUrl(integrationId);
  }

  @Get('all')
  @ApiOperation({ summary: 'Get all integrations', description: `Returns all integrations.` })
  @ApiResponse({ status: 200, description: 'Integrations retrieved.' })
  @ApiResponse({ status: 401, description: `Unauthorized.` })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number', example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of items per page', example: 10 })
  @ApiQuery({ name: 'org', required: false, type: String, example: '6613c488a24b7aeb232fa83d' })
  @ApiQuery({ name: 'type', required: false, type: String, example: 'meeting' })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  findAll(@Req() req: any, @Query('page') page: number = 1, @Query('limit') limit: number = 10, @Query('org') org?: string,@Query('type') type?: string) {
    if (!org && req.user.org) {
      org = req.user.org._id;
    }
    return this.IntegrationService.findAll(page, limit, org,type);
  }

  @Get('user')
  @ApiOperation({ summary: 'Get all integrations for current user', description: `Returns all integrations for current logged-in user.` })
  @ApiResponse({ status: 200, description: 'Integrations retrieved.' })
  @ApiResponse({ status: 401, description: `Unauthorized.` })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number', example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of items per page', example: 10 })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  @Roles()
  findByUser(@Req() req: any, @Query('page') page: number = 1, @Query('limit') limit: number = 10) {
    return this.IntegrationService.findByUser(page, limit, req.user);
  }

  @Get(':integrationId')
  @ApiOperation({ summary: 'Get integration by ID', description: `Fetch a specific integration by its ID.` })
  @ApiResponse({ status: 200, description: 'Integration retrieved.' })
  @ApiResponse({ status: 404, description: 'Integration not found.' })
  @ApiResponse({ status: 401, description: `Unauthorized. ` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  @Roles()
  findOne(@Param('integrationId') integrationId: string) {
    const objId = validateObjectId(integrationId);
    return this.IntegrationService.findOne(objId);
  }

  @Patch(':integrationId')
  @ApiOperation({ summary: 'Update integration', description: `Update a specific integration.` })
  @ApiResponse({ status: 200, description: 'Integration updated.' })
  @ApiResponse({ status: 404, description: 'Integration not found.' })
  @ApiResponse({ status: 401, description: `Unauthorized. ` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  @Roles()
  update(@Param('integrationId') integrationId: string, @Body() updateIntegrationDto: UpdateIntegrationDto) {
    const objId = validateObjectId(integrationId);
    return this.IntegrationService.update(objId, updateIntegrationDto);
  }

  @Delete(':integrationId')
  @ApiOperation({ summary: 'Delete integration', description: `Delete a specific integration.` })
  @ApiResponse({ status: 200, description: 'Integration deleted.' })
  @ApiResponse({ status: 404, description: 'Integration not found.' })
  @ApiResponse({ status: 401, description: `Unauthorized. ` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  @Roles()
  remove(@Param('integrationId') integrationId: string) {
    const objId = validateObjectId(integrationId);
    return this.IntegrationService.remove(objId);
  }

  @Post('/validate-whatsapp-token')
  @ApiOperation({
    summary: 'Validate WhatsApp access token',
    description: 'Validates the provided WhatsApp access token by making an API request.',
  })
  @ApiResponse({ status: 200, description: 'Token is valid.' })
  @ApiResponse({ status: 400, description: 'Invalid token or error during validation.' })
  async validateWhatsAppToken(@Body() validateWhatsAppTokenDto: ValidateWhatsAppTokenDto) {
    const { whatsappAccessToken } = validateWhatsAppTokenDto;
    return this.IntegrationService.validateWhatsAppToken(whatsappAccessToken);
  }

  @Post('send-whatsapp')
  @ApiOperation({ summary: 'Send WhatsApp message', description: 'Sends a WhatsApp message using stored integration details' })
  @ApiResponse({ status: 200, description: 'Message sent successfully' })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'WhatsApp integration not found' })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  sendWhatsAppMessage(@Req() req: any, @Body() sendWhatsAppDto: SendWhatsAppDto) {
    return this.IntegrationService.sendWhatsAppMessage(sendWhatsAppDto, req.user);
  }

}
