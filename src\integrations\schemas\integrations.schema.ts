import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Types } from 'mongoose';
import { Org } from 'src/org/schemas/org.schema';
import { IntegrationType, StausIntegrationType } from 'src/shared/constants';
import { BasicUser } from 'src/user/schemas/basic-user.schema';

export type IntegrationDocument = HydratedDocument<Integration>;

@Schema({
    timestamps: true
})
export class Integration {
    @Prop({
        type: String,
        required: false,
        trim: true
    })
    clientId?: string;

    @Prop({
        type: String,
        required: false,
        trim: true
    })
    clientSecret?: string;

    @Prop({
        type: String,
        required: false,
        trim: true
    })
    webhookSecretToken?: string;

    @Prop({
        type: String,
        required: false,
        trim: true
    })
    tenantId?: string;

    @Prop({
        type: String,
        required: true,
        trim: true,
        enum: Object.values(IntegrationType),
        default: IntegrationType.GoogleMeet,
    })
    integrationType: string;

    @Prop({
        type: String,
        required: false,
        trim: true,
    })
    type: string;

    @Prop({
        required: false,
        default: false,
        type: Boolean,
    })
    isDeleted?: boolean;

    // @Prop({
    //     type: String,
    //     required: false,
    //     trim: true
    // })
    // accessToken?: string;

    //Required to generate new access tokens.
    //Needed to generate access tokens without re-authentication.
    @Prop({
        type: String,
        required: false,
        trim: true
    })
    refreshToken?: string;

    // @Prop({ type: Date, required: false })
    // accessTokenExpiresAt?: Date;

    @Prop({
        required: true,
        type: Types.ObjectId, ref: 'BasicUser'
    })
    createdBy: BasicUser;

    @Prop({
        required: false,
        type: [String],
    })
    scope?: string[];

    @Prop({
        required: false,
        type: Types.ObjectId, ref: 'Org'
    })
    org?: Org;

    // @Prop({ default: false, type: Boolean })
    // defaultCredentials: boolean;

    // WhatsApp-specific fields
    @Prop({ type: String, trim: true })
    whatsappPhoneNumber?: string;

    @Prop({ type: String, trim: true })
    whatsappAccessToken?: string;

    @Prop({
        type: Object,
        required: false,
        default: {},
    })
    whatsappTemplates?: Record<string, string>; // safer and clearer    

    @Prop({ type: String, trim: true })
    whatsappBusinessAccountId?: string;

    @Prop({ type: String, trim: true })
    huggingFaceApiKey?: string;

    @Prop({ type: String, trim: true })
    openAiApiKey?: string;

    @Prop({ type: String, trim: true })
    smsSenderId?: string;

    @Prop({ type: String, trim: true })
    smsApiKey?: string;

    @Prop({ type: String, trim: true })
    smsApiSecret?: string;

    @Prop({ type: String, trim: true })
    smsBaseUrl?: string; // For example: https://api.jiocx.in/sendsms


}

export const IntegrationSchema = SchemaFactory.createForClass(Integration);