import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Logger, Query, BadRequestException, Req } from '@nestjs/common';
import { UserService } from './user.service';
import { UpdateUserDto } from './dto/update-user.dto';
import { ApiBearerAuth, ApiBody, ApiOperation, ApiParam, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Types } from 'mongoose';
import { AuthJwtGuard } from 'src/auth/guards/auth-jwt/auth-jwt.guard';
import { Roles } from 'src/auth/decorators/roles.decorator';
import { Role } from 'src/auth/enums/role.enum';
import { RolesDto } from './dto/roles.dto';
import { RolesGuard } from 'src/auth/guards/roles/roles.guard';
import { validateObjectId } from 'src/utils/validation.utils';
import { CommentDto } from 'src/common/dto/comment.dto';
import { OrgTypeFilterDto } from 'src/org/dto/org-type-filter.dto';
import { CreateFreelancerDto } from './dto/create-freelancer-dto';
import { ChangeCustomStatusDto } from './dto/change-status.dto';
import { CreateUserDto } from './dto/create-user.dto';
import { FilterUsersDto } from './dto/filter-user-by-org-and-bu.dto';
import { MemberJobAssignDto } from 'src/task/dto/assign-job-to-member';
import { UserProfileDto } from 'src/auth/dto/user-profile.dto';
import { VerifyVendorOtpDto } from './dto/verify-vendor-otp';
import { ResendOtpDto } from './dto/resend-otp.dto';
import { CreateDeleteUserDto } from 'src/delete-user/dto/create-delete-user.dto';

@Controller('')
// @UseGuards(AuthJwtGuard)
@ApiTags('Users')
export class UserController {
  private readonly logger = new Logger(UserController.name);


  constructor(private readonly userService: UserService) { }


  @Post()
  @ApiOperation({ summary: 'Creates a new user', description: `This endpoint allows you to create a new user. This is accessible only by admin and bu-head.` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead)
  @Roles()
  @ApiResponse({ status: 201, description: 'User is saved.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Data ' })
  @ApiResponse({ status: 403, description: 'Forbidden - user with role "Admin" and "BUHead" can only use this end point.' })
  create(@Body() createUserDto: CreateUserDto) {
    return this.userService.create(createUserDto);
  }

  @Post('vendor-user')
  @ApiOperation({
    summary: 'Creates a new user',
    description: `This endpoint allows you to create a new user. This is accessible only by admin and bu-head.`,
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead)
  @Roles()
  @ApiResponse({ status: 201, description: 'User is saved.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Data ' })
  @ApiResponse({ status: 403, description: 'Forbidden - user with role "Admin" and "BUHead" can only use this endpoint.' })
  async createVendor(
    @Body() createUserDto: CreateUserDto,
    @Query('isTemporary') isTemp: boolean = true, // Optional query parameter to specify if the user is temporary
  ) {
    if (isTemp !== undefined && typeof isTemp !== 'boolean') {
      throw new BadRequestException('The "isTemporary" flag must be a boolean.');
    }

    return this.userService.createVendor(createUserDto, isTemp); // Pass the flag to the service
  }


  @Post('verify-otp')
  @ApiOperation({
    summary: 'Verify OTP for user',
    description: 'This endpoint verifies the OTP sent to the user email. This is for both temporary and main users.',
  })
  @ApiResponse({ status: 200, description: 'OTP verified successfully.' })
  @ApiResponse({ status: 400, description: 'Invalid or expired OTP code.' })
  @ApiBody({
    description: 'The request body for verifying OTP',
    required: true,
    type: VerifyVendorOtpDto,
  })
  async verifyOTP(
    @Body() verifyVendorOtpDto: VerifyVendorOtpDto
  ) {
    const { email, otpCode } = verifyVendorOtpDto;
    return this.userService.verifyOTP(email, otpCode);
  }

  @Post('resend-otp')
  @ApiOperation({
    summary: 'Resend OTP to user email',
    description: 'This endpoint generates and resends a new OTP to the user email. Works for both temporary and main users.',
  })
  @ApiResponse({ status: 200, description: 'OTP resent successfully.' })
  @ApiResponse({ status: 404, description: 'User not found or has been deleted.' })
  @ApiBody({
    description: 'The request body for resending OTP',
    required: true,
    type: ResendOtpDto,
  })
  async resendOTP(
    @Body() resendOtpDto: ResendOtpDto
  ): Promise<void> {
    const { email } = resendOtpDto;
    await this.userService.resendOTP(email);
  }


  @Get('verification-status')
  @ApiOperation({
    summary: 'Check user verification status',
    description: 'This endpoint checks if a user has verified their email through OTP.',
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead)
  @Roles()
  @ApiResponse({ status: 200, description: 'Returns verification status.' })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - only users with role "Admin" and "BUHead" can use this endpoint.'
  })
  async checkVerificationStatus(@Query('email') email: string) {
    if (!email) {
      throw new BadRequestException('Email is required');
    }
    return { isVerified: await this.userService.isUserVerified(email) };
  }

  @Post('freelancer')
  @ApiOperation({ summary: 'Creates a new freelancer', description: `This endpoint allows you to create a new freelancer. This is accessible only by admin.` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin)
  @Roles()
  @ApiResponse({ status: 201, description: 'User is saved.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Data ' })
  @ApiResponse({ status: 403, description: 'Forbidden - user with role "Admin" can only use this end point.' })
  createFreelancer(@Body() createUserDto: CreateFreelancerDto) {
    return this.userService.createFreelancer(createUserDto);
  }

  @Patch(':userId/profile')
  @ApiOperation({
    summary: 'Update a user profile by Id',
    description: `This endpoint updates the user profile by their ID. Accessible by everyone.`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.AccountManager, Role.TeamLead, Role.TeamMember, Role.Recruiter, Role.Vendor, Role.JobSeeker, Role.Freelancer, Role.TechPanel)
  // @Roles()
  @ApiResponse({ status: 200, description: 'User profile successfully updated.' })
  @ApiResponse({ status: 401, description: 'Unauthorized - Missing or invalid JWT token.' })
  // @ApiResponse({ status: 403, description: 'Forbidden - User does not have access rights.' })
  @ApiResponse({ status: 404, description: 'User not found.' })
  @ApiParam({ name: 'userId', description: 'ID of the user to update' })
/*************  ✨ Codeium Command ⭐  *************/
/**
 * Updates the user profile for the specified user ID.
 * 
 * @param {string} userId - The ID of the user whose profile is to be updated.
 * @param {UserProfileDto} userProfileDto - The data transfer object containing the updated profile information for the user.
 * @returns {Promise<any>} - A promise that resolves to the updated user profile.
 * 
 * @throws {BadRequestException} - If the user ID format is invalid or if an error occurs during the update process.
 */

/******  a07faaec-6286-4064-ab81-2957cbc1ae30  *******/  async updateProfile(
    @Param('userId') userId: string,
    @Body() userProfileDto: UserProfileDto,
  ) {
    // Validate the ObjectId format (you can use Mongoose's ObjectId validation)
    const objId = validateObjectId(userId);

    // Call the service method to update the user profile
    return await this.userService.updateProfile(objId, userProfileDto);
  }


  @Get()
  @ApiOperation({ summary: 'Retrieve all users', description: `This endpoint returns a list of all users. This is accessible only for "${Role.Admin}, ${Role.BUHead}, ${Role.DeliveryManager}, ${Role.ResourceManager}, ${Role.TeamLead}, ${Role.TeamMember} and ${Role.Recruiter}"` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.DeliveryManager, Role.ResourceManager, Role.TeamLead, Role.TeamMember, Role.Recruiter)
  @Roles()
  @ApiResponse({ status: 200, description: 'All users are retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized ' })
  @ApiResponse({ status: 403, description: `Forbidden, user with role "${Role.Admin}, ${Role.BUHead}, ${Role.DeliveryManager}, ${Role.ResourceManager}, ${Role.TeamLead}, ${Role.TeamMember} and ${Role.Recruiter}" can only use this end point.` })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number', example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of items per page', example: 25 })
  @ApiQuery({ name: 'role', required: false, enum: Role, enumName: 'Role', description: 'Role of the user' })
  @ApiQuery({ name: 'name', required: false, type: String, description: 'Name to search users (optional)', example: 'John Doe' })
  @ApiQuery({ name: 'orgType', required: false, type: String, description: 'Organization type to filter users (optional)', example: 'customer-org' })
  findAll(@Query('page') page: number = 1, @Query('limit') limit: number = 25, @Query('role') role?: Role, @Query('name') name?: string, @Query('orgType') orgType?: string) {

    return this.userService.findAll(page, limit, role, name, orgType);
  }

  @Get('org-users')
  @ApiOperation({ summary: 'Retrieve all users', description: `This endpoint returns a list of all users. This is accessible only for "${Role.Admin}"` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.Vendor, Role.BUHead, Role.AccountManager, Role.DeliveryManager)
  @Roles()
  @ApiResponse({ status: 200, description: 'All users are retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized ' })
  @ApiResponse({ status: 403, description: `Forbidden, user with role "${Role.Admin}" can only use this end point.` })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number', example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of items per page', example: 25 })
  @ApiQuery({ name: 'role', required: false, enum: Role, enumName: 'Role', description: 'Role of the user' })
  @ApiQuery({ name: 'name', required: false, type: String, description: 'Name to search users (optional)', example: 'John Doe' })
  @ApiQuery({ name: 'orgType', required: false, type: String, description: 'Organization type to filter users (optional)', example: 'customer-org' })
  findAllOrgUser(@Req() req: any, @Query('page') page: number = 1, @Query('limit') limit: number = 25, @Query('role') role?: Role, @Query('name') name?: string, @Query('orgType') orgType?: string) {
    const orgId = req.user.org._id
    return this.userService.findAllOrgUser(orgId, page, limit, role, name, orgType);
  }

  @Get('active')
  @ApiOperation({ summary: 'Retrieve all active  users', description: `This endpoint returns a list of all active users. This is accessible only for "${Role.Admin}"` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin)
  @Roles()
  @ApiResponse({ status: 200, description: 'All active users are retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized ' })
  @ApiResponse({ status: 403, description: `Forbidden, user with role "${Role.Admin}" can only use this end point.` })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number', example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of items per page', example: 25 })
  @ApiQuery({ name: 'role', required: false, enum: Role, enumName: 'Role', description: 'Role of the user' })
  findAllActiveUsers(@Query('page') page: number = 1, @Query('limit') limit: number = 25, @Query('role') role: Role) {
    return this.userService.findAllActiveUsers(page, limit, role);
  }

  @Get('orgUsers')
  @ApiOperation({ summary: 'Retrieve all users', description: `Returns a list of all users. Only accessible to "${Role.Admin}"` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager, Role.Vendor, Role.TeamMember)
  @Roles()
  @ApiResponse({ status: 200, description: 'Users retrieved successfully.' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: `Forbidden, only "${Role.Admin}" can access this endpoint.` })
  @ApiQuery({ name: 'page', required: false, type: Number, example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, example: 25 })
  @ApiQuery({ name: 'role', required: false, enum: Role, enumName: 'Role' })
  @ApiQuery({ name: 'name', required: false, type: String, example: 'John Doe' })
  @ApiQuery({ name: 'orgType', required: false, type: String, example: 'customer-org' })
  findAllOrg(@Req() req: any,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 25,
    @Query('role') role?: Role,
    @Query('name') name?: string,
    @Query('orgType') orgType?: string,
  ) {
    return this.userService.findAllOrg(page, limit, req.user, role, name, orgType);
  }


  @Get('suspended')
  @ApiOperation({ summary: 'Retrieve all suspended users', description: `This endpoint returns a list of all suspended users. This is accessible only for "${Role.Admin}"` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin)
  @Roles()
  @ApiResponse({ status: 200, description: 'All suspended users are retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized ' })
  @ApiResponse({ status: 403, description: `Forbidden, user with role "${Role.Admin}" can only use this end point.` })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number', example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of items per page', example: 25 })
  findAllSuspendedUsers(@Query('page') page: number = 1, @Query('limit') limit: number = 25) {
    return this.userService.findAllSuspendedUsers(page, limit);
  }

  @Get('find-user')
  @ApiOperation({ summary: 'Retrieve a single user by email', description: `This endpoint returns a single user by email. This endpoint accessible for "${Role.Admin}".` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin)
  @Roles()
  @ApiResponse({ status: 200, description: 'User retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized ' })
  @ApiResponse({ status: 403, description: `Forbidden, user with role "${Role.Admin}" can only use this end point.` })
  @ApiQuery({ name: 'email', description: 'email of the user' })
  findByEmail(@Query('email') email: string) {
    return this.userService.findUserByEmail(email);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Retrieve a single user by ID', description: `This endpoint returns a single user by ID. This endpoint accessible for "${Role.Admin}".` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin)
  @Roles()
  @ApiResponse({ status: 200, description: 'User retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized ' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role admin can only use this end point.' })
  @ApiParam({ name: 'id', description: 'ID of the user' })
  findOne(@Param('id') id: string) {
    const objId = validateObjectId(id);
    return this.userService.findById(objId);
  }

  @Get('search')
  @ApiOperation({ summary: 'Search for user', description: `This endpoint allows you to search for user. This is accessible only for "${Role.Admin}"` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin)
  @Roles()
  @ApiResponse({ status: 201, description: 'User found.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Data ' })
  @ApiResponse({ status: 401, description: 'Unauthorized ' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role admin can only use this end point.' })
  @ApiQuery({ name: 'name', required: true, type: String, description: 'Name of user', example: "John" })
  search(@Query('name') name: string, @Query('page') page: number = 1, @Query('limit') limit: number = 10) {
    return this.userService.searchUsers(name, page, limit);
  }


  @Get('filter/org-type')
  @ApiOperation({
    summary: 'Filter users by org type and return users',
    description: `This endpoint returns a list of users by organization type. Accessible only to "${Role.BUHead}" and "${Role.Admin}".`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead)
  @Roles()
  @ApiResponse({ status: 200, description: 'Users retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 403, description: `Forbidden. Only users with roles "${Role.BUHead}" and "${Role.Admin}" can access this endpoint.` })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number', example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of items per page', example: 10 })
  findAllUsersByOrgType(@Query() query: OrgTypeFilterDto, @Query('page') page: number = 1, @Query('limit') limit: number = 10) {
    const { orgType } = query;
    return this.userService.findAllUsersByOrgType(orgType, page, limit);
  }


  @Get('filter/user')
  @ApiOperation({
    summary: 'Filter users by org  and business unit and return users',
    description: `This endpoint returns a list of users by org  type and business unit. Accessible only to "${Role.BUHead}" and "${Role.Admin}".`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.BUHead, Role.Admin)
  @Roles()
  @ApiResponse({ status: 200, description: 'Users retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 403, description: `Forbidden. Only users with roles "${Role.BUHead}" and "${Role.Admin}" can access this endpoint.` })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number', example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of items per page', example: 10 })
  findUserByOrgAndBusinessUnit(@Query() query: FilterUsersDto, @Query('page') page: number = 1, @Query('limit') limit: number = 10) {
    return this.userService.findUserByOrgAndBusinessUnit(query);
  }


  @Patch(':id')
  @ApiOperation({ summary: 'Update a user by Id', description: `This endpoint updates a user by Id. This is accessible by everyone.` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.AccountManager, Role.TeamLead, Role.TeamMember, Role.Recruiter, Role.Vendor, Role.JobSeeker, Role.Freelancer, Role.TechPanel)
  // @Roles()
  @ApiResponse({ status: 200, description: 'User is updated.' })
  @ApiResponse({ status: 401, description: 'Unauthorized ' })
  // @ApiResponse({ status: 403, description: 'Forbidden' })
  @ApiResponse({ status: 404, description: 'User not found.' })
  @ApiParam({ name: 'id', description: 'id of the user' })
  async update(@Param('id') id: string, @Body() updateUserDto: UpdateUserDto, @Req() req: any) {
    const objId = validateObjectId(id);
    return await this.userService.update(objId, updateUserDto, req.user);
  }

  @Patch(':id/role')
  @ApiOperation({ summary: 'Updates roles of user by Id', description: `This endpoint updates roles of a user by Id. This is accessible only for "${Role.Admin}".` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin)
  @Roles()
  @ApiResponse({ status: 200, description: 'User roles are updated.' })
  @ApiResponse({ status: 401, description: 'Unauthorized ' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role "Admin" can only use this end point.' })
  @ApiResponse({ status: 404, description: 'User not found.' })
  @ApiParam({ name: 'id', description: 'id of the user' })
  async updateRole(@Param('id') id: string, @Body() rolesDto: RolesDto) {
    const objId = validateObjectId(id);
    return await this.userService.updateRoles(objId, rolesDto);
  }

  @Patch(':id/suspend')
  @ApiOperation({ summary: 'Suspends a user by Id', description: `This endpoint suspends a user by Id. This is accessible only for "${Role.Admin}".` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin)
  @Roles()
  @ApiResponse({ status: 200, description: 'User is suspended.' })
  @ApiResponse({ status: 401, description: 'Unauthorized ' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role "Admin" can only use this end point.' })
  @ApiResponse({ status: 404, description: 'User not found.' })
  @ApiParam({ name: 'id', description: 'id of the user' })
  async suspendUser(@Param('id') id: string, @Body() commentDto: CommentDto) {
    const objId = validateObjectId(id);
    return await this.userService.suspendUser(objId, commentDto);
  }

  @Patch(':id/reject')
  @ApiOperation({ summary: 'Rejects a user by Id', description: `This endpoint rejects a user by Id. This is accessible only for "${Role.Admin}".` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin)
  @Roles()
  @ApiResponse({ status: 201, description: 'User is rejected.' })
  @ApiResponse({ status: 401, description: 'Unauthorized ' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role "Admin" can only use this end point.' })
  @ApiResponse({ status: 404, description: 'User not found.' })
  @ApiParam({ name: 'id', description: 'id of the user' })
  async rejectUser(@Param('id') id: string) {
    const objId = validateObjectId(id);
    return await this.userService.rejectUser(objId);
  }

  @Patch(':id/approve')
  @ApiOperation({ summary: 'Approves a user by Id', description: `This endpoint approves a user by Id. This is accessible only for "${Role.Admin}".` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin)
  @Roles()
  @ApiResponse({ status: 201, description: 'User is suspended.' })
  @ApiResponse({ status: 401, description: 'Unauthorized ' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role "Admin" can only use this end point.' })
  @ApiResponse({ status: 404, description: 'User not found.' })
  @ApiParam({ name: 'id', description: 'id of the user' })
  async approveUser(@Param('id') id: string) {
    const objId = validateObjectId(id);
    return await this.userService.approveUser(objId);
  }

  @Patch(':id/restore')
  @ApiOperation({ summary: 'Restore soft deleted user by ID.', description: `This endpoint restores a soft-deleted user by ID. This is accessible for "${Role.Admin}".` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin)
  @Roles()
  @ApiResponse({ status: 200, description: 'User restored.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role "Admin" can only use this end point.' })
  @ApiParam({ name: 'id', description: 'ID of the User.' })
  async restoreSoftDeletedUser(@Param('id') userId: string) {
    const objId = validateObjectId(userId);
    return await this.userService.restoreSoftDeletedUser(objId);
  }


  @Delete(':id/soft-delete')
  @ApiOperation({ summary: 'Soft delete a user by Id', description: `This endpoint soft deletes a user by Id. This is accessible only for "${Role.Admin}".` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin)
  @Roles()
  @ApiResponse({ status: 200, description: 'User is soft deleted.' })
  @ApiResponse({ status: 401, description: 'Unauthorized ' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role "Admin" can only use this end point.' })
  @ApiResponse({ status: 404, description: 'User not found.' })
  @ApiParam({ name: 'id', description: 'id of the user' })
  remove(@Param('id') id: string, @Body() commentDto: CommentDto) {
    const objId = validateObjectId(id);
    return this.userService.softDelete(objId, commentDto);
  }

  @Delete(':id/soft-delete-recruiter')
  @ApiOperation({ summary: 'Soft delete a user by Id', description: `This endpoint request deletes a user by Id. This is accessible only for "${Role.Admin}".` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager, Role.TeamMember, Role.JobSeeker, Role.Freelancer, Role.TechPanel, Role.Vendor)
  @Roles()
  @ApiResponse({ status: 200, description: 'User is soft deleted.' })
  @ApiResponse({ status: 401, description: 'Unauthorized ' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role "Admin" can only use this end point.' })
  @ApiResponse({ status: 404, description: 'User not found.' })
  @ApiParam({ name: 'id', description: 'id of the user' })
  removeRecruiter(@Param('id') id: string, @Body() createDeleteUserDto: CreateDeleteUserDto) {
    const objId = validateObjectId(id);
    return this.userService.softDeleteRecruiter(objId, createDeleteUserDto);
  }

  @Delete(':id/hard-delete')
  @ApiOperation({ summary: 'Hard delete a user by Id', description: `This endpoint hard deletes a user by Id. This is accessible only for "${Role.Admin}".` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin)
  @Roles()
  @ApiResponse({ status: 200, description: 'User is hard deleted.' })
  @ApiResponse({ status: 401, description: 'Unauthorized ' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role "Admin" can only use this end point.' })
  @ApiResponse({ status: 404, description: 'User not found.' })
  @ApiParam({ name: 'id', description: 'id of the user' })
  hardDelete(@Param('id') id: string) {
    const objId = validateObjectId(id);
    return this.userService.hardDelete(objId);
  }

  @Patch(':id/custom-status')
  @ApiOperation({ summary: 'Update a User Custom Status by Id', description: `This endpoint updates a user by Id. This is accessible only for "${Role.Admin}".` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin)
  @Roles()
  @ApiResponse({ status: 200, description: 'User Custom Status is updated.' })
  @ApiResponse({ status: 401, description: 'Unauthorized ' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role "Admin" can only use this end point.' })
  @ApiResponse({ status: 404, description: 'User not found.' })
  @ApiParam({ name: 'id', description: 'ID of the user' })
  async changeCustomStatus(
    @Param('id') id: string,
    @Body() changeStatusDto: ChangeCustomStatusDto,
    @Req() req: any
  ) {
    const objId = validateObjectId(id);
    return await this.userService.changeUserCustomStatus(objId, changeStatusDto, req.user);
  }

  @Get('counts-by-custom-status')
  @ApiOperation({ summary: 'Get counts by custom status', description: `This endpoint gives the count by User status. This is accessible by everyone.` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager, Role.TeamMember, Role.JobSeeker, Role.Freelancer)
  @Roles()
  @ApiResponse({ status: 200, description: 'All counts are retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized ' })
  // @ApiResponse({ status: 403, description: 'Forbidden user with role super admin and admin can only use this end point.' })
  async getCountsByStatus() {
    return await this.userService.getFreelancersCountByCustomStatus();
  }

  @Get('find')
  @ApiOperation({ summary: 'Get list of Users by status', description: `This endpoint gives the list of users by status. This is accessible only for "${Role.Admin}"` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin)
  @Roles()
  @ApiResponse({ status: 200, description: 'All Users are retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized ' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role "Admin" can only use this end point.' })
  @ApiQuery({ name: 'status', required: true, type: String, description: 'Status Id of Users' })
  @ApiQuery({ name: 'role', required: false, type: String, description: 'Role of Users (optional)' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number for pagination' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of records per page' })
  async getUsersByStatus(@Query('status') status: string, @Query('page') page: number = 1, @Query('limit') limit: number = 10, @Query('role') role: string = '',) {
    if (!status) {
      throw new BadRequestException('Status query parameter is required');
    }
    const objId = validateObjectId(status);
    return await this.userService.getUsersByCustomStatus(objId, page, limit, role);
  }

  @Delete('delete-all')
  @ApiOperation({ summary: 'Hard delete all users', description: `This endpoint hard deletes all users. This is accessible only for "${Role.Admin}".` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin)
  @Roles()
  @ApiResponse({ status: 200, description: 'All users are deleted.' })
  @ApiResponse({ status: 401, description: 'Unauthorized ' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role "Admin" can only use this end point.' })
  @ApiResponse({ status: 404, description: 'Users not found.' })
  removeAll() {
    return this.userService.removeAll();
  }

  @Get('org-recruiters')
  @ApiOperation({
    summary: 'Retrieve all recruiters in an organization',
    description: `This endpoint returns a list of all users with the role "recruiter" within the authenticated user's organization. This is accessible only for "${Role.Admin}".`,
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin)
  @Roles()
  @ApiResponse({ status: 200, description: 'List of recruiters retrieved successfully.' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden, only authorized roles can access this endpoint.' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number', example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of items per page', example: 0 })
  @ApiQuery({ name: 'name', required: false, type: String, description: 'Search recruiters by name (optional)' })
  async getRecruiters(@Req() req: any, @Query('page') page: number = 1, @Query('limit') limit: number = 0, @Query('name') name?: string) {
    if (!req.user?.org?._id) {
      throw new BadRequestException('Organization ID is required for the user');
    }

    const orgId = req.user.org._id;
    return this.userService.getRecruitersByOrg(orgId, page, limit, name);
  }


  @Get('org-recruiters/count')
  @ApiOperation({ summary: 'Get recruiter count', description: `Fetch the total count of recruiters in an organization with optional filters.  This is accessible only for "${Role.Admin}".` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin)
  @Roles()
  @ApiResponse({ status: 200, description: 'Recruiter count fetched successfully' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: `Forbidden. Only users with specific roles can access this endpoint.` })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async countRecruiters(@Req() req: any) {
    if (!req.user?.org?._id) {
      throw new BadRequestException('Organization ID is required for the user');
    }
    const orgId = req.user.org._id; // Default to the logged-in user's organization

    return await this.userService.countRecruiters(orgId);
  }

  // @Post('verify-email')
  // @ApiOperation({ summary: 'Verify Email', description: 'This endpoint allows you to verify email. This is accessible by individual user.' })
  // @ApiResponse({ status: 201, description: 'Confirm user account.' })
  // @ApiResponse({ status: 400, description: 'Bad Request / Data ' })
  // async verifyEmail(@Req() req: any, @Body() verifyUuidDto: VerifyUuidDto) {
  //   return await this.userService.verifyEmail(req, verifyUuidDto);
  // }

  // @Get(':id/confirm/:token')
  // @ApiOperation({ summary: 'Verify Email', description: 'This endpoint allows you to verify email. This is accessible by everyone.' })
  // @ApiResponse({ status: 200, description: 'Confirm user account.' })
  // @ApiResponse({ status: 400, description: 'Bad Request / Data ' })
  // @ApiParam({ name: 'id', description: 'user id' })
  // @ApiParam({ name: 'token', description: 'verification token' })
  // async verifyEmailAddress(@Req() req: any, @Param() params: any) {
  //   let verifyUuidDto: VerifyUuidDto = {
  //     verification: params.token,
  //   };
  //   return await this.userService.verifyEmail(req, verifyUuidDto);
  // }

  // @Post('forgot-password')
  // @ApiOperation({ summary: 'Forgot Password', description: 'This endpoint allows you to reset a forgotten password. This is accessible by everyone.' })
  // @ApiResponse({ status: 200, description: 'Instructions sent to email.' })
  // @ApiResponse({ status: 400, description: 'Bad Request / Data' })
  // submitForgotPassword(@Req() req: any, @Body() forgotPasswordDto: ForgotPasswordDto) {
  //   return this.userService.handleForgotPasswordRequest(req, forgotPasswordDto);
  // }

  // @Post('forgot-password-verify')
  // // @HttpCode(HttpStatus.OK)
  // @ApiOperation({ summary: 'Verify forget password code', description: 'This endpoint allows you to verify forget password code. This is accessible by everyone.' })
  // @ApiResponse({ status: 200, description: 'Password code verified.' })
  // @ApiResponse({ status: 400, description: 'Bad Request / Data' })
  // async forgotPasswordVerify(@Req() req: any, @Body() verifyUuidDto: VerifyUuidDto) {
  //   return await this.userService.forgotPasswordVerify(req, verifyUuidDto);
  // }

  // @Patch('reset-password')
  // @ApiOperation({ summary: 'Reset the password', description: 'This endpoint allows you to reset the password. This is accessible by everyone.' })
  // @ApiResponse({ status: 200, description: 'Password is reset.' })
  // @ApiResponse({ status: 400, description: 'Bad Request / Data' })
  // async resetPassword(@Body() resetPasswordDto: ResetPasswordDto) {
  //   return await this.userService.resetPassword(resetPasswordDto);
  // }


  // @Get(':id')
  // @ApiParam({ name: 'id', description: 'id of the user'})
  // findOne(@Param('id', ParseMongoObjectIdPipe) id: Types.ObjectId) {
  //   return this.userService.findOne(id);
  // }


  // @Get('me')
  // @ApiBearerAuth()
  // @UseGuards(AuthJwtGuard) // loading the auth guard to protect the route
  // findCurrentUserLoggedIn(@Req() req: any) {
  //   return this.userService.findUserByEmail(req.user['email']);
  // }

  // @Patch('change-password')
  // @ApiOperation({ summary: 'Change password of the user', description: `This endpoint updates password of the user by Id. This is accessible by logged in user.` })
  // @ApiBearerAuth()
  // @UseGuards(AuthJwtGuard)
  // @ApiResponse({ status: 200, description: 'Password updated.' })
  // @ApiResponse({ status: 401, description: 'Unauthorized ' })
  // @ApiResponse({ status: 404, description: 'User not found.' })
  // updatePassword(@Req() req: any, @Body() passwordDto: PasswordDto) {
  //   return this.userService.updatePassword(req.user['_id'], passwordDto);
  // }
}
