import { Controller, Get, Post, Body, Patch, Param, UseGuards, Query, BadRequestException, Logger, HttpException, HttpStatus, Req } from '@nestjs/common';
import { RegionService } from './region.service';
import { CreateRegionDto } from './dto/create-region.dto';
import { UpdateRegionDto } from './dto/update-region.dto';
import { ApiOperation, ApiBearerAuth, ApiResponse, ApiTags, ApiParam, ApiQuery } from '@nestjs/swagger';
import { Roles } from 'src/auth/decorators/roles.decorator';
import { Role } from 'src/auth/enums/role.enum';
import { AuthJwtGuard } from 'src/auth/guards/auth-jwt/auth-jwt.guard';
import { RolesGuard } from 'src/auth/guards/roles/roles.guard';
import { validateObjectId } from 'src/utils/validation.utils';
import { ChangeStatusDto } from './dto/change-status.dto';

@Controller('')
@ApiTags('Regions')
export class RegionController {

  private readonly logger = new Logger(RegionController.name);

  constructor(private readonly regionService: RegionService) { }

  @Post()
  @ApiOperation({ summary: 'Creates a new Region', description: `This endpoint allows you to create a new Region. This is accessible only for "${Role.Admin}".` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin)
  @Roles()
  @ApiResponse({ status: 201, description: 'Region is created.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Data ' })
  @ApiResponse({ status: 401, description: 'Unauthorized ' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role admin can only use this end point.' })
  create(@Body() createRegionDto: CreateRegionDto,@Req() req: any) {

    if (!createRegionDto) {
      throw new BadRequestException('Request body is missing or invalid');
    }
    const convertedRegionDto = {
      country: validateObjectId(createRegionDto.country),
      state: validateObjectId(createRegionDto.state)
    }
    try {
      return this.regionService.create(convertedRegionDto, req.user);
    } catch (error) {
      if ( error.code === 11000) {
        throw new HttpException(
          'Region with the provided country and state already exists.',
          HttpStatus.CONFLICT
        );
      }

      throw new HttpException(
        `Error while creating Region: ${error.message || 'Internal Server Error'}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('all')
  @ApiOperation({ summary: 'Retrieve all Regions ', description: `This endpoint returns a list of all Regions. This is accessible only for "${Role.Admin}"` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin)
  @Roles()
  @ApiResponse({ status: 200, description: 'All Regions are retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized ' })
  @ApiResponse({ status: 403, description: 'Forbidden user with role admin can only use this end point.' })
  getAllRegions() {
    return this.regionService.getAllRegions();
  }

  @Get(':regionId')
  @ApiOperation({ summary: 'Retrieve a Region by Id', description: 'This endpoint returns a Region by its Id. This is accessible only for Admin".' })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard) 
  // @Roles(Role.Admin)
  @Roles()
  @ApiResponse({ status: 200, description: 'Region is retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized ' })
  @ApiResponse({ status: 403, description: 'Forbidden user with role admin can only use this end point' })
  @ApiResponse({ status: 404, description: 'Regions not found.' })
  findOne(@Param('regionId') regionId: string) {
    const objId = validateObjectId(regionId);
    return this.regionService.findById(objId);
  }

  @Get(':regionId/comments')
  @ApiResponse({ status: 200, description: 'Comments retrieved successfully.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden user with role admin can only use this end point.' })
  @ApiOperation({ summary: 'Get comments of the region.', description: 'This endpoint allows you to retrieve comments on the region. This is accessible only for "admin".' })
  @ApiParam({ name: 'regionId', description: 'Id of the region.' })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin)
  @Roles()
  getComments(@Param('regionId') regionId: string) {
    const objId = validateObjectId(regionId);
    return this.regionService.getComments(objId);
  }

  @Post('search')
  @ApiOperation({ summary: 'Search for Region', description: `This endpoint allows you to search for Region. This is accessible only for admin.` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin)
  @Roles()
  @ApiResponse({ status: 201, description: 'Region found.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Data ' })
  @ApiResponse({ status: 401, description: 'Unauthorized ' })
  @ApiResponse({ status: 403, description: 'Forbidden user with role admin can only use this end point' })
  @ApiQuery({ name: 'name', required: true, type: String, description: 'Name of Region', example: "India" })
  search(@Query('name') name: string) {
    return this.regionService.searchByName(name);
  }

  @Patch(':regionId')
  @ApiOperation({ summary: 'Update a Region by Id', description: `This endpoint updates a Region by Id. This is accessible only for "${Role.Admin}".` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin)
  @Roles()
  @ApiResponse({ status: 200, description: 'Region is updated.' })
  @ApiResponse({ status: 401, description: 'Unauthorized ' })
  @ApiResponse({ status: 403, description: 'Forbidden user with role admin can only use this end point' })
  @ApiResponse({ status: 404, description: 'Region not found.' })
  @ApiParam({ name: 'regionId', description: 'ID of the Region' })
  async update(@Param('regionId') regionId: string, @Body() updateRegionDto: UpdateRegionDto,@Req() req: any) {
    const objId = validateObjectId(regionId);
    if (!updateRegionDto.country||!updateRegionDto.state) {
      throw new BadRequestException('Request body is missing or invalid');
    }
    const convertedRegionDto = {
      country: validateObjectId(updateRegionDto.country),
      state: validateObjectId(updateRegionDto.state)
    }
    return await this.regionService.update(objId, convertedRegionDto, req.user);
  }

  @Patch(':regionId/status')
  @ApiOperation({ summary: 'Update a Region Status by Id', description: `This endpoint updates a Region by Id. This is accessible only for "${Role.Admin}".` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin)
  @Roles()
  @ApiResponse({ status: 200, description: 'Region Status is updated.' })
  @ApiResponse({ status: 401, description: 'Unauthorized ' })
  @ApiResponse({ status: 403, description: 'Forbidden user with role admin can only use this end point' })
  @ApiResponse({ status: 404, description: 'Region not found.' })
  @ApiParam({ name: 'regionId', description: 'ID of the Region' })
  async changeStatus(
    @Param('regionId') regionId: string,
    @Body() changeStatusDto: ChangeStatusDto,
    @Req() req: any
  ) {
    const objId = validateObjectId(regionId);
    return await this.regionService.changeStatus(objId, changeStatusDto, req.user);
  }

  @Get('counts-by-status')
  @ApiOperation({ summary: 'Get counts by status', description: `This endpoint gives the count by Region status. This is accessible only for "${Role.Admin}"` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin)
  @Roles()
  @ApiResponse({ status: 200, description: 'All counts are retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized ' })
  @ApiResponse({ status: 403, description: 'Forbidden user with role admin can only use this end point.' })
  async getCountsByStatus() {
    return await this.regionService.getRegionsCountByStatus();
  }

  @Get()
  @ApiOperation({ summary: 'Get list of Regions by status', description: `This endpoint gives the list of Regions by status. This is accessible only for "${Role.Admin}"` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin)
  @Roles()
  @ApiResponse({ status: 200, description: 'All Regions are retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized ' })
  @ApiResponse({ status: 403, description: 'Forbidden user with role admin can only use this end point.' })
  @ApiQuery({ name: 'status', required: true, type: String, description: 'Status Id of Regions' })
  async getRegionsByStatus(@Query('status') status: string) {
    if (!status) {
      throw new BadRequestException('Status query parameter is required');
    }
    const objId = validateObjectId(status);
    return await this.regionService.getRegionsByStatus(objId);
  }

  @Get('country/:countryId/state/:stateId')
  @ApiOperation({ summary: 'Get a Region by Country Id and State Id', description: `This endpoint gets a Region by Country Id and State Id. This is accessible  for "Everyone".` })
  // @ApiBearerAuth()
  // @UseGuards(AuthJwtGuard)
  // @Roles(Role.SuperAdmin, Role.Admin)
  @ApiResponse({ status: 200, description: 'Region is retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized ' })
  // @ApiResponse({ status: 403, description: 'Forbidden' })
  @ApiResponse({ status: 404, description: 'Region not found.' })
  @ApiParam({ name: 'countryId', description: 'ID of the Country' })
  @ApiParam({ name: 'stateId', description: 'ID of the State' })
  async getRegionByCountryAndState(@Param('countryId') countryId: string, @Param('stateId') stateId: string) {
    const countryObjId = validateObjectId(countryId);
    const stateObjId = validateObjectId(stateId);
    return await this.regionService.findRegionByCountryAndState(countryObjId, stateObjId)
  }

}
