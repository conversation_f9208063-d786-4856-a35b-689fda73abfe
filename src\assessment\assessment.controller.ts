import { Controller, Get, Post, Body, Patch, Param, Delete, Logger, Query, Req, UseGuards } from '@nestjs/common';
import { AssessmentService } from './assessment.service';
import { ApiBearerAuth, ApiOperation, ApiParam, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Roles } from 'src/auth/decorators/roles.decorator';
import { Role } from 'src/auth/enums/role.enum';
import { AuthJwtGuard } from 'src/auth/guards/auth-jwt/auth-jwt.guard';
import { RolesGuard } from 'src/auth/guards/roles/roles.guard';
import { validateObjectId } from 'src/utils/validation.utils';
import { CreateAssessmentDto } from './dto/create-assessment.dto';
import { UpdateAssessmentDto } from './dto/update-assessment.dto';

@Controller('')
@ApiTags('Assessment')
export class AssessmentController {

  private readonly logger = new Logger(AssessmentController.name);

  constructor(private readonly assessmentService: AssessmentService) { }


  @Post()
  @ApiOperation({
    summary: 'Create a new assessment',
    description: `This endpoint allows you to create a new assessment. Accessible only to users with roles "BUHead", "ResourceManager", "DeliveryManager", "TeamLead", "AccountManager", "Recuriter", and "TeamMember".`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager, Role.Recruiter, Role.TeamMember)
  @Roles()  
  @ApiResponse({ status: 201, description: 'The assessment is created.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Data.' })
  @ApiResponse({ status: 401, description: 'Unauthorized access. Authentication is required.' })
  @ApiResponse({ status: 403, description: `Forbidden. Only users with roles "BUHead", "ResourceManager", "DeliveryManager", "TeamLead", "AccountManager", "Recuriter", and "TeamMember" are permitted to use this endpoint.` })
  create(@Req() req: any, @Body() createAssessmentDto: CreateAssessmentDto) {
    return this.assessmentService.create(req.user, createAssessmentDto);
  }


  @Get('all')
  @ApiOperation({
    summary: 'Retrieve all assessments.',
    description: `This endpoint retrieves a list of all assessments including soft deleted assessments. Accessible only to users with roles "BUHead", "ResourceManager", "DeliveryManager", "TeamLead", "AccountManager", "Recuriter", and "TeamMember".`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager, Role.Recruiter, Role.TeamMember)
  @Roles()
  @ApiResponse({ status: 200, description: 'All assessments are retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  // @ApiResponse({ status: 403, description: `Forbidden. Only users with roles "${Role.SuperAdmin}" and "${Role.Admin}" are permitted to use this endpoint.` })
  getAllAssessments() {
    return this.assessmentService.getAllAssessments();
  }

  @Get(':id')
  @ApiOperation({
    summary: 'Retrieve an assessment by Id',
    description: 'This endpoint retrieves an assessment by its Id. Accessible only to authenticated users.'
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager, Role.Recruiter, Role.TeamMember)
  @Roles()
  @ApiResponse({ status: 200, description: 'Assessment retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  // @ApiResponse({ status: 403, description: 'Forbidden' })
  @ApiResponse({ status: 404, description: 'Assessment not found.' })
  findOne(@Param('id') id: string) {
    const objId = validateObjectId(id);
    return this.assessmentService.findOne(objId);
  }

  @Patch(':id')
  @ApiOperation({
    summary: 'Update an assessment by Id',
    description: `This endpoint updates an assessment by Id. Accessible only to users with roles "BUHead", "ResourceManager", "DeliveryManager", "TeamLead", "AccountManager", "Recuriter", and "TeamMember".`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager, Role.Recruiter, Role.TeamMember)
  @Roles()
  @ApiResponse({ status: 200, description: 'Assessment is updated.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 403, description: `Forbidden. Only users with roles "BUHead", "ResourceManager", "DeliveryManager", "TeamLead", "AccountManager", "Recuriter", and "TeamMember" are permitted to use this endpoint.` })
  @ApiResponse({ status: 404, description: 'Assessment not found.' })
  @ApiParam({ name: 'id', description: 'ID of the assessment' })
  update(@Req() req: any, @Param('id') id: string, @Body() updateAssessmentDto: UpdateAssessmentDto) {
    const objId = validateObjectId(id);
    return this.assessmentService.update(objId, updateAssessmentDto, req.user);
  }

  @Delete(':id/hard-delete')
  @ApiOperation({
    summary: 'Hard delete an assessment by Id',
    description: `Deletes an assessment permanently by its Id. This is accessible only for "BUHead", "ResourceManager", "DeliveryManager", "TeamLead", "AccountManager", "Recuriter", and "TeamMember".`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager, Role.Recruiter, Role.TeamMember)
  @Roles()
  @ApiResponse({ status: 200, description: 'Assessment is hard deleted.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 403, description: `Forbidden. Only users with roles "BUHead", "ResourceManager", "DeliveryManager", "TeamLead", "AccountManager", "Recuriter", and "TeamMember" are permitted to use this endpoint.` })
  @ApiResponse({ status: 404, description: 'Assessment not found.' })
  @ApiParam({ name: 'id', description: 'ID of the assessment' })
  remove(@Param('id') id: string) {
    const objId = validateObjectId(id);
    return this.assessmentService.hardDelete(objId);
  }

  // @Delete('delete-all')
  // @ApiOperation({ 
  //   summary: 'Hard delete all assessments', 
  //   description: `This endpoint hard deletes all assessments. This is accessible only for "${Role.SuperAdmin}" and "${Role.Admin}"` 
  // })

  // @ApiBearerAuth()
  // @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.SuperAdmin, Role.Admin)
  // @ApiResponse({ status: 200, description: 'All assessments are deleted.' })
  // @ApiResponse({ status: 401, description: 'Unauthorized.' })
  // @ApiResponse({ status: 403, description: `Forbidden. Only users with roles "${Role.SuperAdmin}" and "${Role.Admin}" are permitted to use this endpoint.` })
  // @ApiResponse({ status: 404, description: 'Assessments not found.' })
  // deleteAll() {
  //   return this.assessmentService.hardDeleteAllAssessments();
  // }

  // @Get('assessment-filter')
  // @ApiBearerAuth()
  // @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.SuperAdmin, Role.SalesRep)
  // @ApiOperation({
  //   summary: 'Retrieve assessment created by organization',
  //   description: `This endpoint returns a list of assessment created by organization. This is only accessible for "admin", "superAdmin" and "salesRep"`,
  // })
  // @ApiResponse({ status: 200, description: 'Assessment retrieved created by organization.' })
  // @ApiResponse({ status: 401, description: 'Unauthorized.' })
  // @ApiResponse({ status: 403, description: 'Forbidden, user with role admin, superAdmin, and sales rep can only use this endpoint.' })
  // @ApiQuery({ name: 'orgId', required: true, type: String, description: 'Organaization Id' })
  // @ApiQuery({ name: 'isDefault', required: false, type: Boolean, description: 'Default wrokflow of the organization'})
  // async assessmentFilter(@Query('orgId') orgId: string, @Query('isDefault') isDefault?: boolean) {
  //   const objId = validateObjectId(orgId);
  //   return this.assessmentService.assessmentFilter(objId, isDefault);
  // }

}
