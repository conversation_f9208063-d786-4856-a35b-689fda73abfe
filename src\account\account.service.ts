import { HttpException, HttpStatus, Injectable, InternalServerErrorException, Logger, NotFoundException } from '@nestjs/common';
import { CreateAccountDto } from './dto/create-account.dto';
import { UpdateAccountDto } from './dto/update-account.dto';
import { ConfigService } from '@nestjs/config';
import { InjectModel } from '@nestjs/mongoose';
import { Account, AccountDocument } from './schemas/account.schema';
import { Model, Types } from 'mongoose';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';
import { CommentDto } from 'src/common/dto/comment.dto';
import { QueryDTO } from './dto/query-account.dto';

@Injectable()
export class AccountService {

  private readonly logger = new Logger(AccountService.name);

  constructor(private configService: ConfigService, @InjectModel(Account.name) private accountModel: Model<Account>, private eventEmitter: EventEmitter2) {

  }

  async create(createAccountDto: CreateAccountDto): Promise<AccountDocument> {
    try {

      this.logger.debug('Checking for existing account with the same name. ');
      const existingAccount = await this.accountModel.findOne({
        name: createAccountDto.name.trim(),
      })

      if (!existingAccount)
        this.logger.debug('Creating a new account document');

      const createdAccount = new this.accountModel(createAccountDto);
      await createdAccount.save();
      const account = this.findAccountById(createdAccount._id);
      this.emitEvent('account.created', account);
      return createdAccount;
    } catch (error) {
      if (error.code === 11000) { // 11000 is the error code for duplicate key error in MongoDB
        this.logger.error('Duplicate key error', error);
        throw new HttpException('Account with this name already exists', HttpStatus.CONFLICT);
      } else {
        this.logger.error('Error creating account', error);
        throw new HttpException('Error creating account', HttpStatus.INTERNAL_SERVER_ERROR);
      }
    }
  }

  async findAll(page: number, limit: number): Promise<AccountDocument[]> {
    this.logger.debug('Initiating findAll method');
    return this.accountModel.find({ isDeleted: false })
      .populate({ path: 'createdBy', select: '_id roles firstName email' })
      .populate({ path: 'ownedBy', select: '_id roles firstName email' })
      .populate({ path: 'accountType', select: '_id name' })
      .populate({ path: 'industry', select: '_id name' })
      .skip((page - 1) * limit)
      .limit(limit)
      .exec();
  }

  async findAllWithSoftDelete(page: number, limit: number): Promise<AccountDocument[]> {
    return this.accountModel.find({ isDeleted: true })
      .populate({ path: 'createdBy', select: '_id roles firstName' })
      .populate({ path: 'ownedBy', select: '_id roles firstName' })
      .populate({ path: 'accountType', select: '_id name' })
      .populate({ path: 'industry', select: '_id name' })
      .skip((page - 1) * limit)
      .limit(limit)
      .exec();
  }


  async findAccountById(id: Types.ObjectId) {
    const account = await this.accountModel.findById(id)
      .populate({ path: 'createdBy', select: '_id roles firstName' })
      .populate({ path: 'ownedBy', select: '_id roles firstName' })
      .populate({ path: 'accountType', select: '_id name' })
      .populate({ path: 'industry', select: '_id name' })
      .exec();
    if (!account || account.isDeleted) {
      throw new NotFoundException(`Account not found with ID ${id}`);
    }

    return account;
  }

  async findOne(id: Types.ObjectId) {
    try {
      return await (this.findAccountById(id));
    } catch (error) {
      this.logger.error(error);
      this.logger.error(`An error occurred in fetching account by Id ${id}. ${error?.account}`);
      throw error;

    }
  }

  async searchAccounts(name: string, page: number, limit: number) {
    const regex = new RegExp(name, 'i');
    return await this.accountModel.find({ name: { $regex: regex } })
      .populate({ path: 'createdBy', select: '_id roles firstName' })
      .populate({ path: 'ownedBy', select: '_id roles firstName' })
      .populate({ path: 'accountType', select: '_id name' })
      .populate({ path: 'industry', select: '_id name' })
      .skip((page - 1) * limit)
      .limit(limit)
      .exec();
  }


  async getIndustryandAccountType({ industryId, accountTypeId, page, limit }: {
    industryId?: string;
    accountTypeId?: string;
    page: number;
    limit: number;
  }): Promise<AccountDocument[]> {
    try {
      const query: any = {};

      if (industryId) {
        query.industry = industryId;
      }

      if (accountTypeId) {
        query.accountType = accountTypeId;
      }
      const accounts = await this.accountModel.find(query)
        .populate({ path: 'createdBy', select: '_id roles firstName' })
        .populate({ path: 'ownedBy', select: '_id roles firstName' })
        .populate({ path: 'accountType', select: '_id name' })
        .populate({ path: 'industry', select: '_id name' })
        .skip((page - 1) * limit)
        .limit(limit)
        .exec();
      return accounts;
    } catch (error) {
      throw new InternalServerErrorException('An error occurred while fetching accounts by industry and accountType.');
    }
  }

  async update(id: Types.ObjectId, updateAccountDto: UpdateAccountDto, user: Object) {

    try {
      await this.findAccountById(id);
      const { name, websiteUrl, contactNumber, description, address, industry } = updateAccountDto;
      const updatedAccount = await this.accountModel.findByIdAndUpdate(id, updateAccountDto, { new: true })
        .populate({ path: 'createdBy', select: '_id roles firstName' })
        .populate({ path: 'ownedBy', select: '_id roles firstName' })
        .populate({ path: 'accountType', select: '_id name' })
        .populate({ path: 'industry', select: '_id name' })
        .exec();

      if (name)
        this.emitEvent('account.name.updated', { updatedAccount, user });

      if (websiteUrl)
        this.emitEvent('account.websiteUrl.updated', { updatedAccount, user });

      if (contactNumber)
        this.emitEvent('account.contactNumber.updated', { updatedAccount, user });

      if (description)
        this.emitEvent('account.description.updated', { updatedAccount, user });

      if (address)
        this.emitEvent('account.address.updated', { updatedAccount, user });

      if (industry)
        this.emitEvent('account.industry.updated', { updatedAccount, user });

      return updatedAccount;
    } catch (error) {
      this.logger.error(error);
      this.logger.error(`An error occurred in fetching account by Id ${id}. ${error?.account}`);
      throw error;
    }

  }

  async restoreSoftDeletedAccounts(id: Types.ObjectId) {
    const account = await this.accountModel.findById(id);
    if (!account || !account.isDeleted) {
      throw new NotFoundException(`Account not found with ID ${id}`);
    }
    account.isDeleted = false;
    await account.save();

    return account;
  }

  async changeStatus(accountId: Types.ObjectId, status: string, commentDto: CommentDto, user: Object) {
    try {
      const updatedAccount = await this.findAccountById(accountId);
      updatedAccount.status = status;
      await updatedAccount.save();
      this.emitEvent('account.status.changed', { updatedAccount, commentDto, user });
      return updatedAccount;
    } catch (error) {
      this.logger.error(error);
      this.logger.error(`An error occurred in fetching account by Id ${accountId}. ${error?.account}`);
      throw error;
    }

  }

  async moveAccount(accountId: Types.ObjectId, newOwnerId: Types.ObjectId, commentDto: CommentDto, user: Object) {
    try {
      const updatedAccount = await this.accountModel.findByIdAndUpdate(
        accountId,
        { ownedBy: newOwnerId },
        { new: true }
      )
        .populate({ path: 'createdBy', select: '_id roles firstName' })
        .populate({ path: 'ownedBy', select: '_id roles firstName' })
        .populate({ path: 'accountType', select: '_id name' })
        .populate({ path: 'industry', select: '_id name' })
        .exec();
      this.emitEvent('account.moved', { updatedAccount, commentDto, user });
      return updatedAccount;
    } catch (error) {
      this.logger.error(error);
      this.logger.error(`An error occurred in fetching account by Id ${newOwnerId}. ${error?.account}`);
      throw error;
    }
  }

  async remove(id: Types.ObjectId) {

    try {
      const account = await this.accountModel.findById(id);
      if (!account) {
        throw new NotFoundException(`Account not found with ID ${id}`);
      }
      return this.accountModel.findByIdAndDelete(id);
    } catch (error) {
      this.logger.error(error);
      this.logger.error(`An error occurred in fetching account by Id ${id}. ${error?.account}`);
      throw error;
    }
  }

  async delete(id: Types.ObjectId) {
    try {
      const account = await this.findAccountById(id);
      account.isDeleted = true;
      await account.save();
      return account;
    }
    catch (error) {
      this.logger.error(error);
      this.logger.error(`An error occurred in fetching account by Id ${id}. ${error?.account}`);
      throw error;
    }
  }

  deleteAll() {
    return this.accountModel.deleteMany();
  }

  emitEvent(eventName: string, payload: any) {
    // payload['event']= eventName;
    this.eventEmitter.emit(eventName, payload);
  }


}
