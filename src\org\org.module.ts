import { Module, forwardRef } from '@nestjs/common';
import { OrgService } from './org.service';
import { OrgController } from './org.controller';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { MongooseModule, getConnectionToken } from '@nestjs/mongoose';
import { Org, OrgSchema, Placeholder, PlaceholderSchema } from './schemas/org.schema';
import { PipesModule } from 'src/pipes/pipes.module';
import { AuthModule } from 'src/auth/auth.module';
import { JwtModule } from '@nestjs/jwt/dist/jwt.module';
import { UserModule } from 'src/user/user.module';  // Import UserModule with forwardRef
import { BasicUser, BasicUserSchema } from 'src/user/schemas/basic-user.schema';
import { StageModule } from 'src/stage/stage.module';
import { WorkflowModule } from 'src/workflow/workflow.module';
import { CommonModule } from 'src/common/common.module';
import { StatusModule } from 'src/status/status.module';
import { FileUploadModule } from 'src/file-upload/file-upload.module';
import { JobModule } from 'src/job/job.module';
import { TempOrg, TempOrgSchema } from './schemas/temp_org.schema';
import { VendorInvite, VendorInviteSchema } from 'src/vendor-invite/schemas/vendor-invite.schmea';
import { EndpointsRolesModule } from 'src/endpoints-roles/endpoints-roles.module';
import { Contact, ContactSchema } from 'src/contact/schemas/contact.schema';
import { BusinessUnit, BusinessUnitSchema } from 'src/business-unit/schemas/business-unit.schema';
const AutoIncrementFactory = require('mongoose-sequence');

@Module({
  imports: [
    ConfigModule,
    PipesModule,
    JwtModule, EndpointsRolesModule,
    CommonModule,
    StageModule,
    StatusModule,
    WorkflowModule,
    forwardRef(() => JobModule),
    forwardRef(() => UserModule),  // Use forwardRef here to
    AuthModule,
    FileUploadModule,
    MongooseModule.forFeature([{ name: Org.name, schema: OrgSchema }]),
    MongooseModule.forFeature([{ name: Org.name, schema: OrgSchema },
    { name: Placeholder.name, schema: PlaceholderSchema },
    { name: TempOrg.name, schema: TempOrgSchema },
    { name: VendorInvite.name, schema: VendorInviteSchema },
    { name: BasicUser.name, schema: BasicUserSchema },
    { name: Contact.name, schema: ContactSchema },
    { name: BusinessUnit.name, schema: BusinessUnitSchema },

    ]),
    MongooseModule.forFeatureAsync([
      {
        name: Org.name,
        imports: [ConfigModule],
        useFactory: (configService: ConfigService) => {
          const autoIncrement = AutoIncrementFactory(configService);
          const schema = OrgSchema;
          schema.plugin(autoIncrement, {
            inc_field: 'orgCode',
            id: 'org_sequence',
            start_seq: 1,
            reference_fields: []
          });
          return schema;
        },
        inject: [getConnectionToken(), ConfigService],
      },
    ]),
  ],
  controllers: [OrgController],
  providers: [OrgService],
  exports: [OrgService, MongooseModule],  // Export MongooseModule 
})
export class OrgModule { }
