



# Usage

Org Specific Possible roles are - 
- org-admin,
- head, 
- manager, 
- captain,
- associate

### Note

For non-org specific roles like - admin, freelancer, you already have a Roleguard in place.

You can add as many roles you want and use them in controllers. Check the code below for the usage.

```
import { Controller, Get, Request, UseGuards } from '@nestjs/common';
import { CustomRolesGuard } from '../auth/guards/custom-roles.guard';
import { CustomRoles } from '../auth/decorators/custom-roles.decorator';

@Controller('things')
@UseGuards(CustomRolesGuard)
export class ThingsController {
  @Get()
  @CustomRoles('head', 'manager') // Only heads and managers can view things
  async getAllThings(@Request() req) {
    // Access the request's user object and check if they are vendor or customer
    const isVendor = await this.customRolesGuard.isVendorUser(req.user);
    const isCustomer = await this.customRolesGuard.isCustomerUser(req.user);

    if (isVendor) {
      // Handle vendor-specific thing fetching
      console.log('User is a vendor');
    }

    if (isCustomer) {
      // Handle customer-specific thing fetching
      console.log('User is a customer');
    }

    const isInternal = await this.customRolesGuard.isInternalUser(req.user, req); // Check if the user is internal

    if (isInternal) {
      console.log('User is internal');
    } else {
      console.log('User is external');
    }

    return []; // Return the appropriate things
  }
}
```

### Note
You could come up with more functions in the guard itself, if there are a bit of variations how you want to implement the roles based access control. 

You may need some schema level changes, or data available on user.org or user.org.customers or user.org.vendors to further validate some authorization logic.