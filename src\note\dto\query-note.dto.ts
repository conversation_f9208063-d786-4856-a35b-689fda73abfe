import { Transform } from 'class-transformer';
import { IsOptional } from 'class-validator';

export class QueryDTO {

  @IsOptional()
  @Transform(({ value }) => {
    return value === undefined || value === '0' ? '' : value;
  })
  contactId?: string;

  @IsOptional()
  @Transform(({ value }) => {
    return value === undefined || value === '0' ? '' : value;
  })
  orgId?: string;

  // @IsOptional()
  // @Transform(({ value }) => {
  //   return value === undefined || value === '0' ? '' : value;
  // })
  // vendorId?: string;

  @Transform(({ value }) => {
    return value > 0 ? value : 1;
  })
  page: number = 1;

  @Transform(({ value }) => {
    return value > 0 ? value : 10;
  })
  limit: number = 10;
}
