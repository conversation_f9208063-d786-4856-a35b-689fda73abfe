import { Modu<PERSON> } from '@nestjs/common';
import { MessageService } from './message.service';
import { MessageController } from './message.controller';
import { ConfigModule } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { MongooseModule } from '@nestjs/mongoose';
import { Message, MessageSchema } from './schemas/message.schema';
import { UserModule } from 'src/user/user.module';
import { TaskModule } from 'src/task/task.module';
import { AccountModule } from 'src/account/account.module';
import { ContactModule } from 'src/contact/contact.module';
import { JobModule } from 'src/job/job.module';
import { OrgModule } from 'src/org/org.module';
import { EndpointsRolesModule } from 'src/endpoints-roles/endpoints-roles.module';
@Module({
  imports: [
    ConfigModule,
    JwtModule, EndpointsRolesModule,
    UserModule,
    TaskModule,
    AccountModule,
    ContactModule,
    JobModule,
    OrgModule,
    MongooseModule.forFeature([{ name: Message.name, schema: MessageSchema }])
  ],
  controllers: [MessageController],
  providers: [MessageService],
})
export class MessageModule { }
