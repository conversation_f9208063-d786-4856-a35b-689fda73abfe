import { ApiProperty } from '@nestjs/swagger';
import { TransformFnParams } from 'class-transformer';
import { IsDateString, IsEnum, IsNumber, IsOptional, IsString, ValidateIf } from 'class-validator';
import { EmploymentType } from 'src/shared/constants';

export class BudgetConfirmationDto {
  @ApiProperty({ example: '2025-06-10T00:00:00.000Z' })
  @IsDateString()
  @IsOptional()
  effectiveStartDate: string;

  @ApiProperty({ example: 800000, required: false })
  @IsOptional()
  @IsNumber()
  ctc?: number;

  @ApiProperty({ example: 500, required: false })
  @IsOptional()
  @IsNumber()
  hourlyPay?: number;

  @ApiProperty({
    type: String,
    required: false,
    enum: EmploymentType,
    description: 'Employment type',
  })
  @IsString()
  @IsOptional()
  @ValidateIf((obj) => obj.employmentType === '' || Object.values(EmploymentType).includes(obj.employmentType))
  employmentType: string;
}
