import { ApiProperty } from '@nestjs/swagger';
import { Transform, TransformFnParams } from 'class-transformer';
import { IsBoolean, IsNotEmpty, IsString, IsEmail, IsArray, IsOptional, IsDate, IsMongoId } from 'class-validator';
import { sanitizeWithStyle } from "src/utils/sanitize-with-style";

export class CreateEmailConfigDto {

    @ApiProperty({
        type: String,
        required: false,
        description: 'ID of the user\'s inbox configuration (UserInboxConfig reference)'
    })
    @IsOptional()
    @IsMongoId()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    userInboxConfigId?: string;

    // @ApiProperty({
    //     type: String,
    //     required: true,
    //     description: 'Sender\'s email address'
    // })
    // @IsNotEmpty()
    // @IsEmail()
    // @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value).toLowerCase())
    // fromEmail: string;

    // @ApiProperty({
    //     type: String,
    //     required: true,
    //     description: 'Sender\'s full name'
    // })
    // @IsNotEmpty()
    // @IsString()
    // @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    // fromName: string;

    @ApiProperty({
        type: [String],
        required: true,
        description: 'List of recipient email addresses'
    })
    @IsNotEmpty()
    @IsArray()
    @IsEmail({}, { each: true })
    @Transform((params: TransformFnParams) => params.value.map((email: string) => sanitizeWithStyle(email).toLowerCase()))
    to: string[];

    @ApiProperty({
        type: [String],
        required: false,
        description: 'List of CC email addresses'
    })
    @IsOptional()
    @IsArray()
    @IsEmail({}, { each: true })
    @Transform((params: TransformFnParams) => params.value?.map((email: string) => sanitizeWithStyle(email).toLowerCase()))
    cc?: string[];

    @ApiProperty({
        type: [String],
        required: false,
        description: 'List of BCC email addresses'
    })
    @IsOptional()
    @IsArray()
    @IsEmail({}, { each: true })
    @Transform((params: TransformFnParams) => params.value?.map((email: string) => sanitizeWithStyle(email).toLowerCase()))
    bcc?: string[];

    @ApiProperty({
        type: String,
        required: true,
        description: 'Subject of the email'
    })
    @IsNotEmpty()
    @IsString()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    subject: string;

    @ApiProperty({
        type: String,
        required: false,
        description: 'Body of the email'
    })
    @IsOptional()
    @IsString()
    // @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    body?: string;


    @ApiProperty({ description: 'Array of attachment ID', type: [String], required: false, example: ['http://example.com/file1.png', 'http://example.com/file2.pdf'] })
    @IsArray()
    @IsString({ each: true })
    @IsOptional()
    attachments?: string[];

    @ApiProperty({
        type: Date,
        required: true,
        description: 'Date and time the email was sent'
    })
    @IsOptional()
    @IsDate()
    sentAt: Date;

    @ApiProperty({
        type: Date,
        required: false,
        description: 'Date and time the email was received'
    })
    @IsOptional()
    @IsDate()
    receivedAt?: Date;

    @ApiProperty({
        type: String,
        required: false,
        description: 'ID of the selected email template'
    })
    @IsOptional()
    @IsMongoId()
    templateId?: string;

}
