import { ApiHideProperty, ApiProperty } from "@nestjs/swagger";
import { Transform, TransformFnParams } from "class-transformer";
import { IsNotEmpty, IsOptional, IsString, MaxLength, MinLength } from "class-validator";
import { sanitizeWithStyle } from "src/utils/sanitize-with-style";

export class CreateIndustryDto {

    @ApiProperty({
        type: String,
        required: true,
        description: 'The Industry Name.',
    })
    @IsNotEmpty()
    @IsString()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    name: string;

    @ApiProperty({
        type: String,
        required: false,
        description: 'Industry Description.',
    })
    @IsOptional()
    @IsString()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    description?: string;

    @ApiProperty({
        type: String,
        required: false,
        description: 'The Industry Code.',
    })
    @MinLength(2)
    @MaxLength(10)
    @IsOptional()
    @IsString()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    code?: string;

    @ApiHideProperty()
    // @ApiProperty({
    //     required: false,
    //     type: String, 
    //     description: 'Reference to the Created User.',
    // })
    @IsString()
    @IsOptional()
    createdBy?: string;
}
