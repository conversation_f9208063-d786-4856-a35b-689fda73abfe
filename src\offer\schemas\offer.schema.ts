
import { <PERSON>p, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Types } from 'mongoose';
import { FileMetadata } from 'src/file-upload/schemas/file-metadata.schema';
import { JobApplication } from 'src/job-application-form/schemas/job-application.schema';
import { BasicUser } from 'src/user/schemas/basic-user.schema';
import { Bgv } from './bgv.schema';
import { EmploymentType } from 'src/shared/constants';

export type OfferDocument = HydratedDocument<Offer>;


@Schema({ timestamps: true })
export class Offer {

  @Prop({
    type: Types.ObjectId,
    required: true,
    ref: 'JobApplication',
  })
  jobApplication: JobApplication;

  @Prop({
    type: Date,
    required: true,
  })
  dateOfJoining: Date;

  @Prop({
    type: Number,
    required: true,
  })
  salaryPerAnnum: number;

  @Prop({
    required: false,
    type: Boolean,
    default: false
  })
  isAccepted?: boolean;

  @Prop({
    required: false,
    type: Boolean,
    default: false
  })
  isOnBoarded?: boolean;

  @Prop({
    type: Date,
    required: false,
  })
  dueDate: Date; // Due date for accepting the offer

  @Prop({
    required: false,
    type: Types.ObjectId, ref: 'BasicUser'
  })
  createdBy: BasicUser;

  @Prop({
    type: Types.ObjectId,
    required: false,
    ref: 'Bgv',
  })
  bgvId?: Types.ObjectId;

  @Prop({
    type: String,
    required: false,
    trim: true,
    // default: '<h1>Job Description</h1>'
  })
  status?: string;

  @Prop({
    type: Number,
    required: true,
    default: 1,
  })
  stepperKey: number;

  @Prop({
    type: {
      effectiveStartDate: { type: Date, required: false },
      ctc: { type: Number, required: false },
      hourlyPay: { type: Number, required: false },
      employmentType: { type: String, required: false, enum: Object.values(EmploymentType) },
      confirmedBy: { type: Types.ObjectId, ref: 'BasicUser', required: true },
      confirmedAt: { type: Date, required: true },
    },
    default: null,
  })
  budgetConfirmation: {
    effectiveStartDate: Date;
    ctc?: number;
    hourlyPay?: number;
    employmentType: string;
    confirmedBy: Types.ObjectId;
    confirmedAt: Date;
  };

  @Prop({
    required: false,
    type: Boolean,
    default: false
  })
  clientOfferReleased?: boolean;

  @Prop({
    type: Types.ObjectId,
    ref: 'BgvHandler',
    required: false
  })
  bgvHandlerId: string;

  @Prop({
    required: false,
    type: Boolean,
    default: false
  })
  isApproved?: boolean;

}

export const OfferSchema = SchemaFactory.createForClass(Offer);