import { ApiProperty } from '@nestjs/swagger';
import { <PERSON>Array, IsMongoId, IsNotEmpty } from 'class-validator';

export class CreateRolePrivilegeDto {
  @ApiProperty({ description: 'The ID of the role', example: '65f3a1c2d1234567890abcde' })
  @IsMongoId()
  @IsNotEmpty()
  roleId: string;

  @ApiProperty({
    description: 'Array of privilege IDs',
    example: ['65f3b2c3d4567890abcdef12', '65f3b2c3d4567890abcdef13'],
  })
  @IsArray()
  @IsMongoId({ each: true })
  @IsNotEmpty()
  privileges: string[];
}

