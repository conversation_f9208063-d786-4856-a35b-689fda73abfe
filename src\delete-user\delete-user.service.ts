import { ConflictException, Injectable, InternalServerErrorException, Logger, NotFoundException } from '@nestjs/common';
import { CreateDeleteUserDto } from './dto/create-delete-user.dto';
import { UpdateDeleteUserDto } from './dto/update-delete-user.dto';
import { ConfigService } from '@nestjs/config';
import { InjectModel } from '@nestjs/mongoose';
import { DeleteUser } from './schemas/delete-user.schema';
import { Model, Types, Connection } from 'mongoose';
import { BasicUser, BasicUserDocument } from 'src/user/schemas/basic-user.schema';

@Injectable()
export class DeleteUserService {

  private  readonly logger = new Logger(DeleteUserService.name);

   constructor(private configService: ConfigService, @InjectModel(DeleteUser.name) private deleteUserModel: Model<DeleteUser>,
      @InjectModel(BasicUser.name) private basicUserModel: Model<BasicUserDocument>
  ) { }
  create(createDeleteUserDto: CreateDeleteUserDto) {
    return 'This action adds a new deleteUser';
  }

  findAll() {
    return `This action returns all deleteUser`;
  }

  async findOne(userId: string) {
    const deleteRequest = await this.deleteUserModel.findOne({ userId }).exec();
    return deleteRequest;
  }

  update(id: number, updateDeleteUserDto: UpdateDeleteUserDto) {
    return `This action updates a #${id} deleteUser`;
  }

  async remove(deleteUserId: string) {
    try {
      // Check if the delete request exists
      const deleteRequest = await this.deleteUserModel.findById(deleteUserId).exec();
      if (!deleteRequest) {
        throw new NotFoundException(`Delete request with ID ${deleteUserId} not found.`);
      }

      // Perform deletion from deleteUser collection
      await this.deleteUserModel.deleteOne({ _id: deleteUserId });

      return { message: `Delete request with ID ${deleteUserId} successfully removed.` };
    } catch (error) {
      throw new NotFoundException(`Failed to delete request: ${error.message}`);
    }
  }

  // async approveOrRejectDeletion(userId: Types.ObjectId, status: 'APPROVED' | 'REJECTED' | 'PENDING') {
  //   console.log("userId",userId)
  //   try {
  //     const deleteRequest = await this.deleteUserModel.findOne({ _id: userId });
  //     if (!deleteRequest) {
  //       throw new NotFoundException(`No delete request found for user ${userId}.`);
  //     }
  //     let updateFields = {};
      
  //     if (status === 'APPROVED') {
  //       updateFields = {
  //         isDeleted: true,
  //         isRejected: false,
  //         isPending: false,
  //       };
  //     } else if (status === 'REJECTED') {
  //       updateFields = {
  //         isRejected: true,
  //         isDeleted: false,
  //         isPending: false,
  //       };
  //     } else if (status === 'PENDING') {
  //       updateFields = {
  //         isPending: true,
  //         isDeleted: false,
  //         isRejected: false,
  //       };
  //     }
  //     deleteRequest.status = status;
  //     await deleteRequest.save();
  
  //     return { message: `User deletion request ${status}.` };
  //   } catch (error) {
  //     this.logger.error(`Error processing delete request: ${error.message}`);
  //     throw new InternalServerErrorException(`Error processing delete request: ${error.message}`);
  //   }
  // }

 
    async approveOrRejectDeletion(userId: Types.ObjectId, status: 'APPROVED' | 'REJECTED' | 'PENDING') {
      console.log("userId", userId);
      
      try {
        // Find the deletion request
        const deleteRequest = await this.deleteUserModel.findOne({ _id: userId });
        if (!deleteRequest) {
          throw new NotFoundException(`No delete request found for user ${userId}.`);
        }
  
        // Get the associated user ID from the delete request
        const basicUserId = deleteRequest.userId;
  
        // Update the delete request status
        deleteRequest.status = status;
        await deleteRequest.save();
  
        // Update the corresponding user document
        const updateFields = {
          isDeleted: status === 'APPROVED',
          isRejected: status === 'REJECTED',
          isPending: status === 'PENDING'
        };
  
        await this.basicUserModel.findByIdAndUpdate(
          basicUserId,
          updateFields
        );
  
        return { message: `User deletion request ${status.toLowerCase()}.` };
  
      } catch (error) {
        this.logger.error(`Error processing delete request: ${error.message}`);
        throw new InternalServerErrorException(`Error processing delete request: ${error.message}`);
      }
    }


  
}
