import { BadRequestException, Injectable, InternalServerErrorException, Logger, NotFoundException } from '@nestjs/common';
import { DynamicField } from './schemas/dynamic-field.schema';
import { Model, Types } from 'mongoose';
import { ConfigService } from '@nestjs/config';
import { InjectModel } from '@nestjs/mongoose';
import { Stage } from 'src/stage/schemas/stage.schema';
import { Workflow } from 'src/workflow/schemas/workflow.schema';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';
import { EmailTemplateEvent, EventPayload, FieldType, OrgType, StageType } from 'src/shared/constants';
import { DynamicFieldDto } from './dto/dynamic-field.dto';
import { UpdateDynamicFieldDto } from './dto/update-dynamic-field.dto';
import { EmailTemplate } from 'src/email-template-builder/schemas/email-template-builder.schema';
import { Placeholder } from 'src/org/schemas/org.schema';
import moment from 'moment';
import { WorkflowService } from 'src/workflow/workflow.service';
import { Job } from 'src/job/schemas/job.schema';
import { query } from 'express';
import { Offer } from 'src/offer/schemas/offer.schema';
import { RecruiterTarget } from 'src/recruiter-target/schemas/recuriter-target.schema';
import { Interview } from 'src/interview/schemas/interview.schema';
import { UpdateFieldOrderBulkDto } from './dto/update-order.dto';

@Injectable()
export class DynamicFieldService {

  private readonly logger = new Logger(DynamicFieldService.name);

  constructor(private configService: ConfigService,
    @InjectModel(Stage.name) private stageModel: Model<Stage>, @InjectModel(Workflow.name) private workflowModel: Model<Workflow>,
    @InjectModel(DynamicField.name) private readonly dynamicFieldModel: Model<DynamicField>,
    @InjectModel(EmailTemplate.name) private emailTemplateService: Model<EmailTemplate>,
    @InjectModel(Placeholder.name) private placeholderService: Model<Placeholder>,
    private eventEmitter: EventEmitter2, private readonly workflowService: WorkflowService,
    @InjectModel(Job.name) private jobModel: Model<Job>,
    @InjectModel(Offer.name) private offerModel: Model<Offer>,
    @InjectModel(Interview.name) private interviewModel: Model<Interview>,
    @InjectModel(RecruiterTarget.name) private recruiterTargetModel: Model<RecruiterTarget>
  ) { }



  emitEvent(eventName: string, payload: any) {
    // this.logger.debug(payload)
    this.eventEmitter.emit(eventName, payload);
  }


  async addDynamicField(dynamicFieldDto: DynamicFieldDto) {
    try {
      // if ((dynamicFieldDto.type === FieldType.DROPDOWN || dynamicFieldDto.type === FieldType.MULTISELECT) &&
      //   (!dynamicFieldDto.options || dynamicFieldDto.options.length === 0)) {
      //   throw new BadRequestException('Options are required for dropdown and multi-select types');
      // }

      // Validate Date Fields
      console.log("dto for dynamic", dynamicFieldDto)
      // Remove options if not DROPDOWN or MULTISELECT
      if (
        dynamicFieldDto.type !== FieldType.DROPDOWN &&
        dynamicFieldDto.type !== FieldType.MULTISELECT
      ) {
        dynamicFieldDto.options = undefined;
      }

      if (dynamicFieldDto.type === FieldType.DATE) {
        if (!dynamicFieldDto.dateProperties) {
          throw new BadRequestException('Date properties are required for date type fields');
        }

        const { minDate, maxDate, format } = dynamicFieldDto.dateProperties;

        if (minDate && isNaN(Date.parse(minDate))) {
          throw new BadRequestException(`Invalid minDate format: ${minDate}`);
        }

        if (maxDate && isNaN(Date.parse(maxDate))) {
          throw new BadRequestException(`Invalid maxDate format: ${maxDate}`);
        }

        if (minDate && maxDate && new Date(minDate) > new Date(maxDate)) {
          throw new BadRequestException(`minDate cannot be greater than maxDate`);
        }

        // Default format if not provided
        dynamicFieldDto.dateProperties.format = format || 'YYYY-MM-DD';
      }

      // Find the current highest order value in the collection
      dynamicFieldDto.canDelete = true; // Set canDelete to true by default
      let highestOrder: any;
      if (dynamicFieldDto.isJobApplicationField) {
        // if (dynamicFieldDto.orgId) {
        //   highestOrder = await this.dynamicFieldModel
        //     .findOne({ orgId: dynamicFieldDto.orgId, isJobApplicationField: true }, { order: 1 })
        //     .sort({ order: -1 })
        //     .lean();
        // }
        // if (dynamicFieldDto.departmentId) {
        //   highestOrder = await this.dynamicFieldModel
        //     .findOne({ departmentId: dynamicFieldDto.departmentId, isJobApplicationField: true }, { order: 1 })
        //     .sort({ order: -1 })
        //     .lean();
        // }
        if (dynamicFieldDto.contactId ) {
          highestOrder = await this.dynamicFieldModel
            .findOne({ contactId: dynamicFieldDto.contactId, isJobApplicationField: true }, { order: 1 })
            .sort({ order: -1 })
            .lean();
        } else if (dynamicFieldDto.departmentId) {
          highestOrder = await this.dynamicFieldModel
            .findOne({ departmentId: dynamicFieldDto.departmentId, isJobApplicationField: true }, { order: 1 })
            .sort({ order: -1 })
            .lean();
        } else if (dynamicFieldDto.orgId) {
          highestOrder = await this.dynamicFieldModel
            .findOne({ orgId: dynamicFieldDto.orgId, isJobApplicationField: true }, { order: 1 })
            .sort({ order: -1 })
            .lean();
        }
      }

      console.log("dto fro contact",dynamicFieldDto.contactId )
      console.log("testing the field for the order",   highestOrder = await this.dynamicFieldModel
            .findOne({ contactId: dynamicFieldDto.contactId, isJobApplicationField: true }, { order: 1 })
            .sort({ order: -1 })
            .lean())

      if (dynamicFieldDto.isJobField) {
        // if (dynamicFieldDto.orgId) {
        //   highestOrder = await this.dynamicFieldModel
        //     .findOne({ orgId: dynamicFieldDto.orgId, isJobField: true }, { order: 1 })
        //     .sort({ order: -1 })
        //     .lean();
        // }
        // if (dynamicFieldDto.departmentId) {
        //   highestOrder = await this.dynamicFieldModel
        //     .findOne({ departmentId: dynamicFieldDto.departmentId, isJobField: true }, { order: 1 })
        //     .sort({ order: -1 })
        //     .lean();
        // }
        if (dynamicFieldDto.contactId) {
          highestOrder = await this.dynamicFieldModel
            .findOne({ contactId: dynamicFieldDto.contactId, isJobField: true }, { order: 1 })
            .sort({ order: -1 })
            .lean();
        } else if (dynamicFieldDto.departmentId) {
          highestOrder = await this.dynamicFieldModel
            .findOne({ departmentId: dynamicFieldDto.departmentId, isJobField: true }, { order: 1 })
            .sort({ order: -1 })
            .lean();
        } else if (dynamicFieldDto.orgId) {
          highestOrder = await this.dynamicFieldModel
            .findOne({ orgId: dynamicFieldDto.orgId, isJobField: true }, { order: 1 })
            .sort({ order: -1 })
            .lean();
        }
      }

      

      // const highestOrder = await this.dynamicFieldModel
      //   .findOne({orgId : dynamicFieldDto.orgId}, { order: 1 })
      //   .sort({ order: -1 })
      //   .lean();

     
      // Set the order for the new field (either increment highest order or start from 1)
      const newOrder = highestOrder ? highestOrder.order + 1 : 1;
      dynamicFieldDto.order = newOrder; // Set the order for the new field

      // if (dynamicFieldDto.contactId && typeof dynamicFieldDto.contactId === 'string') {
      //   dynamicFieldDto.contactId = new Types.ObjectId(dynamicFieldDto.contactId);
      // }

      this.logger.log(JSON.stringify(dynamicFieldDto));
     
      const newField = new this.dynamicFieldModel(dynamicFieldDto);
    
      const finalPayload = {
        ...newField.toObject(),
        createdAt: new Date(),
        updatedAt: new Date(),
       // contactId: dynamicFieldDto.contactId // explicitly set it here
      };
      const savedField = await this.dynamicFieldModel.collection.insertOne(finalPayload);
    
      return finalPayload;
      // console.log(finalPayload)
      // const savedField = await newField.save();
      // console.log(savedField)

      // await newField.save();

    } catch (error) {
      throw new BadRequestException(`Error adding dynamic field of job application: ${error.message}`);
    }
  }

  async updateDynamicField(fieldId: string, dynamicFieldDto: UpdateDynamicFieldDto) {
    try {
      // if ((dynamicFieldDto.type === FieldType.DROPDOWN || dynamicFieldDto.type === FieldType.MULTISELECT) &&
      //   (!dynamicFieldDto.options || dynamicFieldDto.options.length === 0)) {
      //   throw new BadRequestException('Options are required for dropdown and multi-select types');
      // }
      if (
        dynamicFieldDto.type !== FieldType.DROPDOWN &&
        dynamicFieldDto.type !== FieldType.MULTISELECT
      ) {
        dynamicFieldDto.options = undefined;
      }
      const updatedField = await this.dynamicFieldModel.findByIdAndUpdate(
        fieldId,
        dynamicFieldDto,
        { new: true }
      );
      if (!updatedField) {
        throw new NotFoundException('Dynamic field not found');
      }
      return updatedField;
    } catch (error) {
      throw new BadRequestException(`Error updating dynamic field of job application: ${error.message}`);
    }
  }

  async deleteDynamicField(fieldId: string) {
    try {
      const updatedField = await this.dynamicFieldModel.findByIdAndUpdate(
        fieldId,
        { isDeleted: true, updatedAt: new Date() }, // Soft delete by setting isDeleted to true
        { new: true } // Return the updated document
      );

      if (!updatedField) {
        throw new NotFoundException('Dynamic field not found');
      }

      return updatedField;
    } catch (error) {
      throw new BadRequestException(`Error marking dynamic field as deleted: ${error.message}`);
    }
  }


  async getAllDynamicFields(orgId?: string, contactId?: string, isJobField?: boolean, isJobApplicationField?: boolean, departmentId?: string) {
    try {
      // console.log(orgId)
      // console.log(isJobField)
      // console.log(isJobApplicationField)

      const filter: any = { isDeleted: false };

      // if (orgId) {
      //   filter.orgId = orgId;
      // }
      // if (contactId) {
      //   filter.contactId = contactId;
      // }
      if (isJobField) {
        filter.isJobField = isJobField;
      }
      if (isJobApplicationField) {
        filter.isJobApplicationField = isJobApplicationField;
      }
      if (departmentId) {
        filter.departmentId = departmentId;
      }

      //Step 1: Try with contactId (if present)
      if (contactId) {
        const contactFilter = { ...filter, contactId };
        const contactFields = await this.dynamicFieldModel.find(contactFilter).sort({ order: 1 }).exec();

        if (contactFields.length > 0) {
          return contactFields;
        }
      }

      // Step 2: Fallback to orgId (if present)
      if (orgId) {
        const orgFilter = { ...filter, orgId };
        const orgFields = await this.dynamicFieldModel.find(orgFilter).sort({ order: 1 }).exec();
        return orgFields;
      }


      // if (isJobField && orgId) {
      return await this.dynamicFieldModel.find(filter)
        .sort({ order: 1 }).exec();
      // }

      return []

    } catch (error) {
      throw new BadRequestException(`Error retrieving dynamic fields for job applications: ${error.message}`);
    }
  }

  async getDynamicField(fieldId: string) {
    try {
      const field = await this.dynamicFieldModel.findById(fieldId).exec();
      if (!field) {
        throw new NotFoundException('Dynamic field not found');
      }
      return field;
    } catch (error) {
      throw new BadRequestException(`Error retrieving dynamic field of job application: ${error.message}`);
    }
  }

  async addBulkDynamicFields(dynamicFieldsDto: DynamicFieldDto[]) {
    try {
      if (!dynamicFieldsDto.length) {
        throw new BadRequestException('No dynamic fields provided');
      }

      // Step 1: Validate Each Field
      for (const field of dynamicFieldsDto) {
        if (!field.title || !field.type) {
          throw new BadRequestException('Title and type are required for all fields');
        }

        // Validate Dropdown & Multiselect Options
        // if ((field.type === FieldType.DROPDOWN || field.type === FieldType.MULTISELECT) &&
        //   (!field.options || field.options.length === 0)) {
        //   throw new BadRequestException(`Options are required for dropdown and multi-select fields: ${field.title}`);
        // }

        // Validate Date Properties
        if (field.type === FieldType.DATE) {
          if (!field.dateProperties) {
            throw new BadRequestException(`Date properties are required for date type fields: ${field.title}`);
          }

          const { minDate, maxDate, format } = field.dateProperties;

          if (minDate && isNaN(Date.parse(minDate))) {
            throw new BadRequestException(`Invalid minDate format for field: ${field.title}`);
          }

          if (maxDate && isNaN(Date.parse(maxDate))) {
            throw new BadRequestException(`Invalid maxDate format for field: ${field.title}`);
          }

          if (minDate && maxDate && new Date(minDate) > new Date(maxDate)) {
            throw new BadRequestException(`minDate cannot be greater than maxDate for field: ${field.title}`);
          }

          field.dateProperties.format = format || 'YYYY-MM-DD';
        }
      }

      // Step 2: Fetch Existing Fields to Prevent Duplicates
      const existingFields = await this.dynamicFieldModel.find().select('title type');
      const existingTitles = new Set(existingFields.map(field => `${field.title}-${field.type}`));

      // Step 3: Prepare Bulk Operations
      const bulkOperations = dynamicFieldsDto
        .filter(field => !existingTitles.has(`${field.title}-${field.type}`)) // Avoid duplicates
        .map(field => ({
          insertOne: {
            document: {
              title: field.title,
              type: field.type,
              placeholder: field.placeholder || '',
              options: field.options || [],
              dateProperties: field.dateProperties || null,
              isRequired: field.isRequired || false,
              isJobApplicationField: field.isJobApplicationField || false,
              isJobField: field.isJobField || false,
              isDefault: true,
              createdAt: { $currentDate: { $type: "date" } },
              updatedAt: { $currentDate: { $type: "date" } },
              order: field.order || 0,
              canDelete: field.canDelete || false,
              name: field.name || '',
              canEdit: field.canEdit || false,
              isVisible: field.isVisible || false,
              // orgId: field.orgId 
            }
          }
        }));

      // if (!bulkOperations.length) {
      //   throw new BadRequestException('All provided fields already exist');
      // }

      // Step 4: Perform Bulk Insert
      await this.dynamicFieldModel.bulkWrite(bulkOperations);

      return { message: 'Bulk dynamic fields added successfully', count: bulkOperations.length };
    } catch (error) {
      throw new BadRequestException(`Error adding bulk dynamic fields: ${error.message}`);
    }
  }

  async updateFieldOrder(updateDto: UpdateFieldOrderBulkDto) {
    const fieldIds = updateDto.fields.map(field => field.id);

    // Fetch existing fields to verify they exist
    const existingFields = await this.dynamicFieldModel.find(
      { _id: { $in: fieldIds } },
      { _id: 1 }
    ).lean();

    const existingFieldIds = new Set(existingFields.map(field => field._id.toString()));

    // Filter out non-existing fields
    const validFields = updateDto.fields.filter(field => existingFieldIds.has(field.id));

    if (validFields.length === 0) {
      throw new BadRequestException('No valid fields provided.');
    }

    // Create bulk operations only for valid fields
    const bulkOps = validFields.map(field => ({
      updateOne: {
        filter: { _id: field.id },
        update: { $set: { order: field.order } },
      },
    }));

    const result = await this.dynamicFieldModel.bulkWrite(bulkOps);

    return {
      message: 'Field order updated successfully.',
      modifiedCount: result.modifiedCount,
      skippedCount: fieldIds.length - validFields.length
    };
  }



  @OnEvent('org.CloneDefaultDynamicFieldsForOrg') // Also listen for activation event
  async cloneDefaultJobApplicationDynamicFields(payload: any): Promise<void> {
    const { org, orgType, adminOrg } = payload;
    const orgId = org?.toString();
    const adminOrgId = adminOrg?.toString();
    let checkOrgId: any;
    // console.log(orgId, orgType, adminOrgId)
    try {
      // Fetch default dynamic fields
      let defaultFields: any;
      if (orgType === OrgType.ADMIN_CUSTOMER_ORG) {
        checkOrgId = orgId;
        defaultFields = await this.dynamicFieldModel.find({ isDefault: true, isJobApplicationField: true }).lean();
      }
      if (orgType === OrgType.ACCOUNT_ORG) {
        checkOrgId = adminOrgId;
        defaultFields = await this.dynamicFieldModel.find({ isDefault: false, isJobApplicationField: true, orgId: adminOrgId }).lean();
      }
      if (orgType === OrgType.CUSTOMER_ORG) {
        checkOrgId = adminOrgId;
        defaultFields = await this.dynamicFieldModel.find({ isDefault: false, isJobApplicationField: true, orgId: adminOrgId }).lean();
      }


      if (!defaultFields.length) {
        console.log('No default dynamic fields found.');
        return;
      }

      // Fetch existing fields for the org to avoid duplicates
     
      const existingFields = await this.dynamicFieldModel.find({ orgId, isDefault: false, isJobApplicationField: true }).lean();
      // console.log(existingFields)
      const existingTitles = new Set(existingFields.map(field => field.title.toLowerCase()));
      // console.log(existingTitles)

      // Filter out already existing fields
      const fieldsToClone = defaultFields
        .filter((field: { title: string; }) => !existingTitles.has(field.title.toLowerCase()))
        .map((field: any) => ({
          ...field,
          _id: undefined, // Remove _id to let MongoDB generate a new one
          orgId,
          isDefault: false,
          createdAt: new Date(),
          updatedAt: new Date(),
        }));

      if (fieldsToClone.length > 0) {
        await this.dynamicFieldModel.insertMany(fieldsToClone);
        
      } else {
        console.log('No new fields to clone.');
      }
    } catch (error) {
      console.error('Error cloning default dynamic fields:', error);
      throw error;
    }
  }

  @OnEvent('org.CloneDefaultJobsDynamicFieldsForOrg') // Also listen for activation event
  async cloneDefaultJobsDynamicFields(payload: any): Promise<void> {
    const { org, orgType, adminOrg } = payload;
    const orgId = org?.toString();
    const adminOrgId = adminOrg?.toString();
    let checkOrgId: any;
    // console.log(orgId, orgType, adminOrgId)
    try {
      // Fetch default dynamic fields
      let defaultFields: any;
      if (orgType === OrgType.ADMIN_CUSTOMER_ORG) {
        checkOrgId = orgId;
        defaultFields = await this.dynamicFieldModel.find({ isDefault: true, isJobField: true }).lean();
      }
      if (orgType === OrgType.ACCOUNT_ORG) {
        checkOrgId = adminOrgId;
        defaultFields = await this.dynamicFieldModel.find({ isDefault: false, isJobField: true, orgId: adminOrgId }).lean();
      }
      if (orgType === OrgType.CUSTOMER_ORG) {
        checkOrgId = adminOrgId;
        defaultFields = await this.dynamicFieldModel.find({ isDefault: false, isJobField: true, orgId: adminOrgId }).lean();
      }


      if (!defaultFields.length) {
        console.log('No default dynamic fields found.');
        return;
      }

      // Fetch existing fields for the org to avoid duplicates
      console.log(checkOrgId)
      const existingFields = await this.dynamicFieldModel.find({ orgId, isDefault: false, isJobField: true }).lean();
      // console.log(existingFields)
      const existingTitles = new Set(existingFields.map(field => field.title.toLowerCase()));
      // console.log(existingTitles)

      // Filter out already existing fields
      const fieldsToClone = defaultFields
        .filter((field: { title: string; }) => !existingTitles.has(field.title.toLowerCase()))
        .map((field: any) => ({
          ...field,
          _id: undefined, // Remove _id to let MongoDB generate a new one
          orgId,
          isDefault: false,
          createdAt: new Date(),
          updatedAt: new Date(),
        }));

      if (fieldsToClone.length > 0) {
        await this.dynamicFieldModel.insertMany(fieldsToClone);
        console.log(`Cloned ${fieldsToClone.length} fields for orgId: ${orgId}`);
      } else {
        console.log('No new fields to clone.');
      }
    } catch (error) {
      console.error('Error cloning default dynamic fields:', error);
      throw error;
    }
  }

  @OnEvent('business-unit.tracker') // Also listen for activation event
  async cloneDefaultJobApplicationDynamicFieldsForDepartment(payload: any): Promise<void> {
    // const { org, orgType, adminOrg } = payload;
    const { createdBusinessUnit, user } = payload;
    const orgId = createdBusinessUnit?.org?.toString();
    const businessUnitId = createdBusinessUnit?._id?.toString();
    const parentBusinessUnitId = createdBusinessUnit?.parentBusinessUnit?.toString();
    console.log(orgId, businessUnitId, parentBusinessUnitId)


    // console.log(orgId, orgType, adminOrgId)
    try {
      // Fetch default dynamic fields
      let defaultFields: any;
      // let existingFields: any;
      if (!parentBusinessUnitId) {
        defaultFields = await this.dynamicFieldModel.find({ isDefault: false, isJobApplicationField: true, orgId: orgId }).lean();
        // existingFields = await this.dynamicFieldModel.find({ orgId: orgId, isDefault: false, isJobApplicationField: true, departmentId: businessUnitId }).lean();

      }
      else {
        defaultFields = await this.dynamicFieldModel.find({ isDefault: false, isJobApplicationField: true, departmentId: parentBusinessUnitId }).lean();
        // existingFields = await this.dynamicFieldModel.find({ orgId: orgId, isDefault: false, isJobApplicationField: true, departmentId: parentBusinessUnitId }).lean();
      }
      console.log(defaultFields)

      if (!defaultFields.length) {
        throw new BadRequestException('No default dynamic fields found for the Orgnization.');
      }

      // Fetch existing fields for the org to avoid duplicates
      const existingFields = await this.dynamicFieldModel.find({ orgId: orgId, isDefault: false, isJobApplicationField: true, departmentId: businessUnitId }).lean();
      // console.log(existingFields)
      const existingTitles = new Set(existingFields.map((field: { title: string; }) => field.title.toLowerCase()));
      // console.log(existingTitles)

      // Filter out already existing fields
      // const fieldsToClone = defaultFields
      //   .filter((field: { title: string; }) => !existingTitles.has(field.title.toLowerCase()))
      //   .map((field: any) => ({
      //     ...field,
      //     _id: undefined, // Remove _id to let MongoDB generate a new one
      //     orgId,
      //     isDefault: false,
      //     createdAt: new Date(),
      //     updatedAt: new Date(),
      //   }));

      const fieldsToClone = defaultFields
        .filter((field: { title: string }) => !existingTitles.has(field.title.toLowerCase()))
        .map((field: any) => {
          const { _id, orgId, ...rest } = field; // remove _id and orgId
          return {
            ...rest,
            isDefault: false,
            departmentId: businessUnitId, // Set the departmentId to the new one
            createdAt: new Date(),
            updatedAt: new Date(),
          };
        });


      if (fieldsToClone.length > 0) {
        await this.dynamicFieldModel.insertMany(fieldsToClone);
        console.log(`Cloned ${fieldsToClone.length} fields for departmentId: ${businessUnitId}`);
      } else {
        console.log('No new fields to clone.');
      }
    } catch (error) {
      console.error('Error cloning default dynamic fields:', error);
      throw error;
    }
  }

  @OnEvent('business-unit.tracker') // Also listen for activation event
  async cloneDefaultJobDynamicFieldsForDepartment(payload: any): Promise<void> {
    // const { org, orgType, adminOrg } = payload;
    const { createdBusinessUnit, user } = payload;
    const orgId = createdBusinessUnit?.org?.toString();
    const businessUnitId = createdBusinessUnit?._id?.toString();
    const parentBusinessUnitId = createdBusinessUnit?.parentBusinessUnit?.toString();
    console.log(orgId, businessUnitId, parentBusinessUnitId)


    // console.log(orgId, orgType, adminOrgId)
    try {
      // Fetch default dynamic fields
      let defaultFields: any;
      // let existingFields: any;
      if (!parentBusinessUnitId) {
        defaultFields = await this.dynamicFieldModel.find({ isDefault: false, isJobField: true, orgId: orgId }).lean();
        // existingFields = await this.dynamicFieldModel.find({ orgId: orgId, isDefault: false, isJobApplicationField: true, departmentId: businessUnitId }).lean();

      }
      else {
        defaultFields = await this.dynamicFieldModel.find({ isDefault: false, isJobField: true, departmentId: parentBusinessUnitId }).lean();
        // existingFields = await this.dynamicFieldModel.find({ orgId: orgId, isDefault: false, isJobApplicationField: true, departmentId: parentBusinessUnitId }).lean();
      }
      console.log(defaultFields)

      if (!defaultFields.length) {
        throw new BadRequestException('No default dynamic fields found for the Orgnization.');
      }

      // Fetch existing fields for the org to avoid duplicates
      const existingFields = await this.dynamicFieldModel.find({ orgId: orgId, isDefault: false, isJobField: true, departmentId: businessUnitId }).lean();
      // console.log(existingFields)
      const existingTitles = new Set(existingFields.map((field: { title: string; }) => field.title.toLowerCase()));
      // console.log(existingTitles)

      // Filter out already existing fields
      // const fieldsToClone = defaultFields
      //   .filter((field: { title: string; }) => !existingTitles.has(field.title.toLowerCase()))
      //   .map((field: any) => ({
      //     ...field,
      //     _id: undefined, // Remove _id to let MongoDB generate a new one
      //     orgId,
      //     isDefault: false,
      //     createdAt: new Date(),
      //     updatedAt: new Date(),
      //   }));

      const fieldsToClone = defaultFields
        .filter((field: { title: string }) => !existingTitles.has(field.title.toLowerCase()))
        .map((field: any) => {
          const { _id, orgId, ...rest } = field; // remove _id and orgId
          return {
            ...rest,
            isDefault: false,
            departmentId: businessUnitId, // Set the departmentId to the new one
            createdAt: new Date(),
            updatedAt: new Date(),
          };
        });


      if (fieldsToClone.length > 0) {
        await this.dynamicFieldModel.insertMany(fieldsToClone);
        console.log(`Cloned ${fieldsToClone.length} fields for departmentId: ${businessUnitId}`);
      } else {
        console.log('No new fields to clone.');
      }
    } catch (error) {
      console.error('Error cloning default dynamic fields:', error);
      throw error;
    }
  }

  @OnEvent('contact.CloneJobFieldsForContact')
  async cloneJobFieldsForContact(payload: any): Promise<void> {
    const { contact, accountOrg, createdOrg, user } = payload;


    const contactId = contact?.toString();

 
    const sourceOrgId = accountOrg || createdOrg;
    if (!sourceOrgId) {
      console.log('No org ID found for cloning job dynamic fields.');
      return;
    }

  
    try {
      // Fetch source fields from sourceOrgId (only non-default job fields)
      const sourceFields = await this.dynamicFieldModel.find({
        orgId: sourceOrgId,
        isJobField: true,
        isDefault: false,
        isDeleted: false,
      }).lean();



     

      if (!sourceFields.length) {
        console.log(`No job dynamic fields found in source org (${sourceOrgId})`);
        return;
      }

      // Fetch existing dynamic fields already linked to this contact to avoid duplication
      const existingFields = await this.dynamicFieldModel.find({
        // orgId: sourceOrgId,
        contactId: contactId,
        isDefault: false,
        isJobField: true,
      }).lean();

     

      const existingTitles = new Set(existingFields.map((field: { title: string; }) => field.title.toLowerCase()));

    
      // Prepare fields to clone
      const fieldsToClone = sourceFields
        .filter((field: { title: string }) => !existingTitles.has(field.title.toLowerCase()))
        .map(field => ({
          title: field.title,
          type: field.type,
          placeholder: field.placeholder || '',
          options: field.options || [],
          isRequired: field.isRequired || false,
          isJobApplicationField: false,
          isJobField: true,
          isDefault: field.isDefault || false,
          isDeleted: false,
          canDelete: field.canDelete ?? true,
          order: field.order || 0,
          isVisible: field.isVisible ?? true,
          canEdit: field.canEdit ?? true,
          createdAt: new Date(),
          updatedAt: new Date(),
          contactId: contactId, // 👈 explicitly setting this
          orgId: undefined
        }));


      
      if (fieldsToClone.length > 0) {
        await this.dynamicFieldModel.insertMany(fieldsToClone);
        console.log(`Cloned ${fieldsToClone.length} fields for orgId: ${contactId}`);

      } else {
        console.log('No new dynamic fields to clone for contact.');
      }
    } catch (error) {
      console.error('Error cloning job dynamic fields to contact:', error);
      throw error;
    }
  }


  
 @OnEvent('contact.CloneJobFieldsForContact')
  async cloneJobApplicationFieldsForContact(payload: any): Promise<void> {
    const { contact, accountOrg, createdOrg, user } = payload;

   

    const contactId = contact?.toString();

   

    const sourceOrgId = accountOrg || createdOrg;
    if (!sourceOrgId) {
      console.log('No org ID found for cloning job dynamic fields.');
      return;
    }

    console.log("source Id", sourceOrgId)

    try {
      // Fetch source fields from sourceOrgId (only non-default job fields)
      const sourceFields = await this.dynamicFieldModel.find({
        orgId: sourceOrgId,
        isJobApplicationField: true,
        isDefault: false,
        isDeleted: false,
      }).lean();



      

      if (!sourceFields.length) {
        console.log(`No job dynamic fields found in source org (${sourceOrgId})`);
        return;
      }

      // Fetch existing dynamic fields already linked to this contact to avoid duplication
      const existingFields = await this.dynamicFieldModel.find({
        // orgId: sourceOrgId,
        contactId: contactId,
        isDefault: false,
       isJobApplicationField: true
      }).lean();

   

      const existingTitles = new Set(existingFields.map((field: { title: string; }) => field.title.toLowerCase()));

   
      // Prepare fields to clone
      const fieldsToClone = sourceFields
        .filter((field: { title: string }) => !existingTitles.has(field.title.toLowerCase()))
        .map(field => ({
          title: field.title,
          type: field.type,
          placeholder: field.placeholder || '',
          options: field.options || [],
          isRequired: field.isRequired || false,
          isJobApplicationField: true,
          isJobField: false,
          isDefault: field.isDefault || false,
          isDeleted: false,
          canDelete: field.canDelete ?? true,
          order: field.order || 0,
          isVisible: field.isVisible ?? true,
          canEdit: field.canEdit ?? true,
          createdAt: new Date(),
          updatedAt: new Date(),
          contactId: contactId, // 👈 explicitly setting this
          orgId: undefined
        }));


   
      if (fieldsToClone.length > 0) {
        await this.dynamicFieldModel.insertMany(fieldsToClone);
        console.log(`Cloned ${fieldsToClone.length} fields for orgId: ${contactId}`);

      } else {
        console.log('No new dynamic fields to clone for contact.');
      }
    } catch (error) {
      console.error('Error cloning job dynamic fields to contact:', error);
      throw error;
    }
  }





  async getDynamicFieldsForJobApplication(jobId: string) {
    try {
      if (!jobId) {
        throw new BadRequestException('jobId is mandatory.');
      }

      // Step 1: Fetch job details
      const job = await this.jobModel.findOne({ _id: new Types.ObjectId(jobId) }).lean();
      console.log(job)
      if (!job) {
        throw new NotFoundException('Job not found.');
      }

      const { endClientOrg, postingOrg, hiringOrg, department, spoc } = job;
      // console.log(endClientOrg)
      // console.log(postingOrg)

      let dynamicFields: any = [];

       if(spoc){
         const contactId = spoc.toString()
        console.log("departmentId", spoc)
        dynamicFields = await this.dynamicFieldModel.find({
          contactId: contactId,
          isDeleted: false,
          isDefault: false,
          orgId: { $exists: false }, // 🔥 This line ensures orgId does NOT exist
          isJobField: true
        }).sort({ order: 1 }).exec();
      }

      // Step 2: Check dynamic fields for department
      if (department) {
        const departmentId = department.toString()
        console.log("departmentId", departmentId)
        dynamicFields = await this.dynamicFieldModel.find({
          departmentId: departmentId,
          isDeleted: false,
          isDefault: false,
          orgId: { $exists: false }, // 🔥 This line ensures orgId does NOT exist
          isJobApplicationField: true
        }).sort({ order: 1 }).exec();
      }

      // Step 3: Check dynamic fields for endClientOrg
      if ((!dynamicFields || dynamicFields.length === 0) && endClientOrg) {
        console.log("endClientOrg", endClientOrg)
        dynamicFields = await this.dynamicFieldModel.find({
          orgId: endClientOrg,
          isDeleted: false,
          isDefault: false,
          isJobApplicationField: true
        }).sort({ order: 1 }).exec();
      }

      // console.log(dynamicFields)

      // Step 4: If no fields found, check hiringOrg
      if ((!dynamicFields || dynamicFields.length === 0) && hiringOrg) {
        console.log("hiringOrg", hiringOrg)
        dynamicFields = await this.dynamicFieldModel.find({
          orgId: hiringOrg,
          isDeleted: false,
          isDefault: false,
          isJobApplicationField: true

        }).sort({ order: 1 }).exec();
      }
      // console.log(dynamicFields)

      // Step 5: If no fields found, check postingOrg
      if ((!dynamicFields || dynamicFields.length === 0) && postingOrg) {
        console.log("postingOrg", postingOrg)
        dynamicFields = await this.dynamicFieldModel.find({
          orgId: postingOrg,
          isDeleted: false,
          isDefault: false,
          isJobApplicationField: true

        }).sort({ order: 1 }).exec();
        // console.log(dynamicFields)

      }

      return dynamicFields.length > 0 ? dynamicFields : [];
    } catch (error) {
      console.error('Error in getDynamicFieldsForJobApplication:', error);
      throw new BadRequestException('An error occurred while fetching dynamic fields.');
    }
  }


  async getDynamicFieldsForPostJob(orgId?: string, contact?: string, department?: string, postingOrg?: string) {
    try {
      console.log(orgId)
      console.log(department)
      console.log(postingOrg)
      console.log(contact)

      let dynamicFields: any = [];

      if(contact){
         const contactId = contact.toString()
        console.log("departmentId", contact)
        dynamicFields = await this.dynamicFieldModel.find({
          contactId: contactId,
          isDeleted: false,
          isDefault: false,
          orgId: { $exists: false }, // 🔥 This line ensures orgId does NOT exist
          isJobField: true
        }).sort({ order: 1 }).exec();
      }

      // Step 2: Check dynamic fields for department
      if (department) {
        const departmentId = department.toString()
        console.log("departmentId", departmentId)
        dynamicFields = await this.dynamicFieldModel.find({
          departmentId: departmentId,
          isDeleted: false,
          isDefault: false,
          orgId: { $exists: false }, // 🔥 This line ensures orgId does NOT exist
          isJobField: true
        }).sort({ order: 1 }).exec();
      }

      // Step 3: Check dynamic fields for endClientOrg
      if (orgId) {
        console.log("orgId", orgId)
        dynamicFields = await this.dynamicFieldModel.find({
          orgId: orgId,
          isDeleted: false,
          isDefault: false,
          isJobField: true
        }).sort({ order: 1 }).exec();
      }

      if ((!dynamicFields || dynamicFields.length === 0)) {
        dynamicFields = await this.dynamicFieldModel.find({
          orgId: postingOrg,
          isDeleted: false,
          isDefault: false,
          isJobField: true
        }).sort({ order: 1 }).exec();
      }

      // console.log(dynamicFields)

      return dynamicFields.length > 0 ? dynamicFields : [];
    } catch (error) {
      console.error('Error in getDynamicFieldsForJobApplication:', error);
      throw new BadRequestException('An error occurred while fetching dynamic fields.');
    }
  }


}



