import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Types } from 'mongoose';
import { BasicUser } from 'src/user/schemas/basic-user.schema';
import { Org } from 'src/org/schemas/org.schema';

export type RolesDocument = HydratedDocument<Roles>;


// Prop decorator can accept more options - read here - https://mongoosejs.com/docs/schematypes.html#schematype-options
// and here - https://docs.nestjs.com/techniques/mongodb#model-injection


// Define the subdocument schema for Placeholder
@Schema({
  timestamps: true
})
export class Roles {

  // TODO: UNIQUE -> check all possibilities 
  // Not unique verified from salesforce
  @Prop({
    type: String,
    required: true,
    // unique: true,
    trim: true
  })
  role: string;

  @Prop({
    type: String,
    required: true,
    // unique: true,
    trim: true
  })
  roleAlias: string;

  @Prop({
    type: String,
    required: false,
    trim: true
  })
  description?: string;

  @Prop({
    type: Types.ObjectId,
    required: false,
    ref: 'Org'
  })
  orgId?: Org;

  @Prop({
    type: Types.ObjectId,
    required: false,
    ref: 'BasicUser'
  })
  createdBy?: BasicUser;

  @Prop({
    type: Boolean,
    required: false,
    default: false
  })
  isDeleted?: boolean;

  @Prop({
    type: Boolean,
    required: false,
    default: false
  })
  isDefault?: boolean;

  @Prop({
    type: Boolean,
    required: false,
    default: false
  })
  canDelete?: boolean;
  

}

export const RolesSchema = SchemaFactory.createForClass(Roles);