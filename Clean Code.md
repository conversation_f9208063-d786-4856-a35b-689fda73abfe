## Things to keep in check for clean code

Proper schema and loading of schema  
API documentation with swagger  
Proper api validations for swagger  
Read me file for every module  
Try catch blocks   
Logger Statements  
Dto validations  
Write Comments in the code
The names of the resource should be singular
Create a collection in postman for testing endpoints

Service, Classes and Models need to start with capital letters

Be specific with sending proper error response 

Give a space after a fullstop or comma if the next sentence starts in the same line
