import { Module } from '@nestjs/common';
import { EmailTemplateBuilderService } from './email-template-builder.service';
import { EmailTemplateBuilderController } from './email-template-builder.controller';
import { ConfigModule } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { UserModule } from 'src/user/user.module';
import { MongooseModule } from '@nestjs/mongoose';
import { EmailTemplate, EmailTemplateSchema } from './schemas/email-template-builder.schema';
import { EndpointsRolesModule } from 'src/endpoints-roles/endpoints-roles.module';
import { Placeholder, PlaceholderSchema } from 'src/org/schemas/org.schema';
@Module({
  imports: [
    ConfigModule,
    JwtModule, EndpointsRolesModule,
    UserModule,
    MongooseModule.forFeature([
      { name: EmailTemplate.name, schema: EmailTemplateSchema },
      { name: Placeholder.name, schema: PlaceholderSchema },
    ]),
  ],
  controllers: [EmailTemplateBuilderController],
  providers: [EmailTemplateBuilderService],
})
export class EmailTemplateBuilderModule { }
