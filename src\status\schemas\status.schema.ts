import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Types } from 'mongoose';
import { StatusConfigType, StatusFunctionality } from 'src/shared/constants';


export type StatusDocument = HydratedDocument<Status>;

@Schema({
    timestamps: true
})
export class Status {

    @Prop({
        type: String,
        required: true,
        trim: true
    })
    name: string;

    @Prop({
        type: String,
        required: false,
        trim: true
    })
    description?: string;

    @Prop({
        type: Boolean,
        required: false,
        default: false
    })
    isDeleted?: boolean;

    @Prop({
        type: String,
        required: true,
        trim: true,
        enum: Object.values(StatusConfigType),
    })
    statusType: string;

    @Prop({
        type: String,
        required: true,
        trim: true,
        enum: Object.values(StatusFunctionality),
    })
    functionality: string;

}

export const StatusSchema = SchemaFactory.createForClass(Status);

StatusSchema.index({ functionality: 1, statusType: 1 }, { unique: true })