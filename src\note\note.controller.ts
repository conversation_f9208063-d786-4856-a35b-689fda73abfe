import { Controller, Get, Post, Body, Patch, Param, Delete, Logger, UseGuards, Req, Query, BadRequestException } from '@nestjs/common';
import { NoteService } from './note.service';
import { CreateNoteDto } from './dto/create-note.dto';
import { UpdateNoteDto } from './dto/update-note.dto';
import { ApiBearerAuth, ApiOperation, ApiParam, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { AuthJwtGuard } from 'src/auth/guards/auth-jwt/auth-jwt.guard';
import { RolesGuard } from 'src/auth/guards/roles/roles.guard';
import { Roles } from 'src/auth/decorators/roles.decorator';
import { Role } from 'src/auth/enums/role.enum';
import { validateObjectId } from 'src/utils/validation.utils';
import { Types } from 'mongoose';
import { QueryDTO } from './dto/query-note.dto';
import { NoteDocument } from './schemas/note.schema';

@Controller('')
@ApiTags('Notes')
export class NoteController {
  private readonly logger = new Logger(NoteController.name);
  constructor(private readonly noteService: NoteService) { }

  @Post()
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager)
  @Roles()
  @ApiResponse({ status: 201, description: 'Note is saved.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Data. ' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  // @ApiResponse({ status: 403, description: 'Forbidden, user with role admin and sales rep can only use this end point.' })
  @ApiOperation({ summary: 'Create a Note', description: `This endpoint for creating a note.This is accessible for everyone.` })
  async create(@Req() req: any, @Body() createNoteDto: CreateNoteDto) {
    createNoteDto.createdBy = req.user._id
    return await this.noteService.create(createNoteDto);
  }

  @Get('all')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager)
  @Roles()
  @ApiOperation({ summary: 'Retrieve all notes', description: `This endpoint returns a list of all notes.This is accessible for everyone.` })
  @ApiResponse({ status: 200, description: 'Note retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  // @ApiResponse({ status: 403, description: 'Forbidden, user with role admin and sales rep  can only use this end point.' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number', example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of items per page', example: 10 })
  findAll(@Query('page') page: number = 1, @Query('limit') limit: number = 10) {
    return this.noteService.findAll(page, limit);
  }

  @Get(':noteId')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager)
  @Roles()
  @ApiOperation({ summary: 'Retrieve a note by ID', description: 'This endpoint returns a note by its Id.This is accessible for everyone.' })
  @ApiResponse({ status: 200, description: 'Note is retrieved.' })
  @ApiResponse({ status: 404, description: 'Note not found.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  // @ApiResponse({ status: 403, description: 'Forbidden, user with role admin and salesrep can only use this end point.' })
  @ApiParam({ name: 'noteId', description: 'id of the note.' })
  findOne(@Param('noteId',) noteId: string) {
    const objId = validateObjectId(noteId);
    return this.noteService.findById(objId);
  }

  @Get('find-by-org-and-contact')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager)
  @Roles()
  @ApiOperation({  summary: 'Search notes by contact and/or org ID', 
  description: `This endpoint returns a list of notes filtered by contact and/or org ID. This is accessible for everyone.`,})
  @ApiResponse({ status: 200, description: 'note is retrieved.' })
  @ApiResponse({ status: 404, description: 'note not found.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  // @ApiResponse({ status: 403, description: 'Forbidden, user with role admin and salesrep can only use this end point.' })
  async findByContactAndOrg(
    @Req() req: any,
    @Query() query: QueryDTO
  ): Promise<NoteDocument[]> {
    return await this.noteService.findByContactAndOrg(query);
  }


  // @Get(':clientId/client')
  // @ApiBearerAuth()
  // @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.SuperAdmin, Role.SalesRep)
  // @ApiOperation({ summary: 'Retrieve all notes by client ID', description: `This endpoint returns a list of all notes by client ID. This is only accessible for "admin", "superAdmin" and "salesRep"` })
  // @ApiResponse({ status: 200, description: 'Notes retrieved by client ID.' })
  // @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  // @ApiResponse({ status: 403, description: 'Forbidden, user with role admin, superAdmin and sales rep can only use this end point.' })
  // @ApiParam({ name: 'clientId', description: 'ID of the client' })
  // @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number', example: 1 })
  // @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of items per page', example: 10 })
  // findByClient(@Param('clientId') clientId: string, @Query('page') page: number = 1, @Query('limit') limit: number = 10) {
  //   return this.noteService.findByClient(clientId, page, limit);
  // }


  @Get('user-notes')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager)
  @Roles()
  @ApiOperation({ summary: 'Retrieve notes by current user', description: 'This endpoint returns a list of notes created by a specific user. This is accessible for everyone.' })
  @ApiResponse({ status: 200, description: 'Notes are retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  // @ApiResponse({ status: 403, description: 'Forbidden, users with the role admin, superAdmin, or salesRep can only use this endpoint.' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number', example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of items per page', example: 10 })
  findNotesByUser(@Req() req: any, @Query('page') page: number = 1, @Query('limit') limit: number = 10) {
    return this.noteService.findNotesByUser(req.user._id, page, limit);
  }

  @Get('public')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager)
  @Roles()
  @ApiOperation({ summary: 'Retrieve all notes which are public', description: `This endpoint returns a list of all notes which are public.This is accessible for everyone.` })
  @ApiResponse({ status: 200, description: 'Notes retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  // @ApiResponse({ status: 403, description: 'Forbidden, user with role admin, superAdmin and sales rep can only use this end point.' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number', example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of items per page', example: 10 })
  getPublicNotes(@Query('page') page: number = 1, @Query('limit') limit: number = 10) {
    return this.noteService.findPublicNotes(page, limit);
  }


  @Patch(':noteId')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager)
  @Roles()
  @ApiOperation({ summary: 'Update a note by ID', description: `This endpoint updates a note by Id.This is accessible for everyone.` })
  @ApiResponse({ status: 200, description: 'Note is saved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  // @ApiResponse({ status: 403, description: 'Forbidden, user with role admin and sales rep can only use this end point.' })
  @ApiParam({ name: 'noteId', description: 'id of the note.' })
  update(@Req() req: any, @Param('noteId',) noteId: string, @Body() updateNoteDto: UpdateNoteDto) {
    const objId = validateObjectId(noteId);
    return this.noteService.update(objId, updateNoteDto,  req.user);
  }

  @Patch(':noteId/privacy')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager)
  @Roles()
  @ApiOperation({ summary: 'Update a note by ID', description: `This endpoint updates a privacy field in note by Id.This is accessible for everyone.` })
  @ApiResponse({ status: 200, description: 'Note is saved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  // @ApiResponse({ status: 403, description: 'Forbidden, user with role admin and sales rep can only use this end point.' })
  @ApiParam({ name: 'noteId', description: 'id of the note.' })
  updatePrivacy(@Param('noteId') noteId: string) {
    const objId = validateObjectId(noteId);
    return this.noteService.updatePrivacy(objId);
  }

  @Delete(':noteId/soft-delete')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager)
  @Roles()
  @ApiOperation({ summary: 'Soft delete a note by ID', description: 'This endpoint soft deletes a note by Id.This is accessible for everyone.' })
  @ApiResponse({ status: 200, description: 'Note is deleted.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  // @ApiResponse({ status: 403, description: 'Forbidden, user with role admin and sales rep can only use this end point.' })
  @ApiParam({ name: 'noteId', description: 'id of the note' })
  remove(@Req() req: any, @Param('noteId') noteId: string) {
    const objId = validateObjectId(noteId);
    return this.noteService.remove(objId, req.user);
  }

  @Delete(':noteId/hard-delete')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager)
  @Roles()
  @ApiOperation({ summary: 'Delete a note by ID', description: 'This endpoint deletes a note by Id.This is accessible for everyone.' })
  @ApiResponse({ status: 200, description: 'Note is hard deleted.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  // @ApiResponse({ status: 403, description: 'Forbidden, user with role admin  can only use this end point.' })
  @ApiParam({ name: 'noteId', description: 'ID of the note type' })
  hardDelete(@Param('noteId') noteId: string) {
    const objId = validateObjectId(noteId);
    return this.noteService.hardDelete(objId);
  }

  @Delete('delete-all')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager)
  @Roles()
  @ApiOperation({ summary: 'Remove  all notes', description: `This endpoint deletes all notes. This is accessible for everyone.` })
  @ApiResponse({ status: 200, description: 'All notes deleted.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  // @ApiResponse({ status: 403, description: 'Forbidden, User with role admin can only use this end point.' })
  deleteAll() {
    return this.noteService.deleteAll();
  }


}
