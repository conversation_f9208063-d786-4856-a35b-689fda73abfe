import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { CreateLeaveManagmentDto, CreateLeavePolicyDto } from './dto/create-leave-managment.dto';
import { UpdateLeaveManagmentDto, UpdateLeavePolicyDto } from './dto/update-leave-managment.dto';

import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { LeavePolicy, LeavePolicyDocument } from './schemas/leave-managment.schema';
import { CreateLeaveRequestDto } from './dto/create-leave-request.dto';


@Injectable()
export class LeavePolicyService {
  constructor(
    @InjectModel(LeavePolicy.name)
    private leavePolicyModel: Model<LeavePolicyDocument>,
  ) {}

  async create(orgId: string, dto: CreateLeavePolicyDto): Promise<LeavePolicy> {
    return this.leavePolicyModel.create({ ...dto, orgId });
  }

async findAll(orgId: string, filter: { leaveType?: string; planeName?: string }): Promise<LeavePolicy[]> {
  const query: any = { orgId };

  // Add filters based on the provided query parameters
  if (filter.leaveType) {
    query.leaveType = filter.leaveType;
  }

  if (filter.planeName) {
    query.planeName = filter.planeName;
  }

  return this.leavePolicyModel.find(query).exec();
}


  async findOne(id: string): Promise<LeavePolicy> {
    const policy = await this.leavePolicyModel.findById(id).exec();
    if (!policy) throw new NotFoundException('Leave policy not found');
    return policy;
  }

async update(id: string, dto: UpdateLeavePolicyDto): Promise<LeavePolicy> {
  // Destructure the data from the DTO
  const { leaveTypes, planName } = dto;

  // Prepare the data to be updated
  const updateData: any = {};

  // Only update planName if it's provided
  if (planName) {
    updateData.planName = planName;
  }

  // Only update leaveTypes if it's provided
  if (leaveTypes) {
    updateData.leaveTypes = leaveTypes;
  }

  // Find the policy by its ID and update it
  const updatedPolicy = await this.leavePolicyModel.findByIdAndUpdate(id, updateData, { new: true }).exec();

  // If the policy doesn't exist, throw a NotFoundException
  if (!updatedPolicy) {
    throw new NotFoundException('Leave policy not found');
  }

  return updatedPolicy;
}






   async addLeaveRequest(
    policyId: string,
    dto: CreateLeaveRequestDto,
  ): Promise<LeavePolicy> {
    const policy = await this.leavePolicyModel.findById(policyId);
    console.log("policy", policy)
    if (!policy) throw new NotFoundException('Leave policy not found');

    policy.leaveRequests.push(dto as any); // Cast needed due to subdoc typing
    await policy.save();
    console.log("policy after leave request ", policy)
    return policy;
  }
  
async updateLeaveRequestStatus(
  policyId: string,
  requestId: string,
  dto: CreateLeaveRequestDto,
): Promise<LeavePolicy> {
  const policy = await this.leavePolicyModel.findById(policyId);
  if (!policy) throw new NotFoundException('Leave policy not found');

  const request = policy.leaveRequests.find(
    (req: any) => req._id?.toString() === requestId,
  );

  if (!request) throw new NotFoundException('Leave request not found');

  if (!dto.status || !['Approved', 'Rejected'].includes(dto.status)) {
    throw new BadRequestException('Invalid status — must be Approved or Rejected.');
  }

  if (!dto.approvedBy || !Types.ObjectId.isValid(dto.approvedBy)) {
    throw new BadRequestException('Invalid approver ID.');
  }

  request.status = dto.status;
  request.approvedBy = new Types.ObjectId(dto.approvedBy);

  await policy.save();
  return policy;
}


  async delete(id: string): Promise<void> {
    await this.leavePolicyModel.findByIdAndDelete(id).exec();
  }
}
