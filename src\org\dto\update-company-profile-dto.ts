import { ApiProperty } from "@nestjs/swagger"; 
import { sanitizeWithStyle } from "src/utils/sanitize-with-style";
import { IsBoolean, IsMongoId, IsOptional, IsString } from "class-validator";
import { Transform, TransformFnParams } from "class-transformer";

export class UpdateCompanyProfileDto {

    @ApiProperty({
    type: String,
    required: false,
    description: 'The logo of your org',
    })
    @IsString()
    @IsOptional()
    // @IsMongoId()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    logo?: string;

    @ApiProperty({
    type: String,
    required: false,
    description: 'The linkedin URL of your org',
    })
    @IsString()
    @IsOptional()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    linkedInUrl?: string;
    
    @ApiProperty({
        type: String,
        required: false,
        description: 'Comapny profile name',
      })
    @IsString()
    @IsOptional()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    companyProfileName?: string;

    @ApiProperty({
    type: Boolean,
    required: false,
    description: "Tells whether to show social platform logins",
    default: false
    })
    @IsOptional()
    @IsBoolean()
    showSocialLogins?: boolean;
    
    @ApiProperty({
    type: String,
    required: false,
    description: 'Instagram url of your org',
    })
    @IsString()
    @IsOptional()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    instagramUrl?: string;

    @ApiProperty({
    type: String,
    required: false,
    description: 'Facebook url of your org',
    })
    @IsString()
    @IsOptional()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    facebookUrl?: string;
}
