// bgv.schema.ts
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, HydratedDocument, SchemaTypes, Types } from 'mongoose';
import { FileMetadata } from 'src/file-upload/schemas/file-metadata.schema';
import { JobApplication } from 'src/job-application-form/schemas/job-application.schema';

export type BgvDocument = HydratedDocument<Bgv>;

class InterimBgvDocsHistoryItem {
  @Prop({ type: Types.ObjectId, ref: 'FileMetadata', required: true })
  replacedFileMetadataId: string;

  @Prop({ type: Date, required: true })
  replacedAt: Date;

  @Prop({ type: String, required: true })
  replacedStatus: string; // e.g., 'rejected'

  @Prop({ type: Types.ObjectId, ref: 'FileMetadata', required: true })
  replacedByFileMetadataId: string;

  @Prop({ type: Date, required: true })
  replacedByAt: Date;

  @Prop({ type: String, required: true })
  rejectionReason?: string;

}

@Schema({ timestamps: true })
export class Bgv extends Document {
  @Prop({
    type: Types.ObjectId,
    required: true,
    ref: 'JobApplication',
  })
  jobApplication: JobApplication;

  @Prop({
    type: String,
    required: false,
    trim: true,
  })
  bgvHandlerName: string;

  @Prop({
    type: String,
    required: false,
    lowercase: true,
    trim: true
  })
  bgvHandlerEmail: string;

  @Prop({
    type: String,
    required: false,
    trim: true,
  })
  bgvHandlerContactNo?: string;

  @Prop({
    type: Date,
    required: false,
  })
  expectedFinalReportDate: Date;

  // @Prop({
  //     type: [
  //         {
  //             key: { type: String, required: true },
  //             fileMetadataId: [{ type: Types.ObjectId, ref: 'FileMetadata', required: true }],
  //         },
  //     ],
  //     default: [],
  // })
  // documents: { key: string; fileMetadataId: FileMetadata[] }[];

  @Prop({
    type: [
      {
        key: { type: String, required: true }, // e.g., "pan_card"
        versions: [
          {
            fileId: { type: Types.ObjectId, ref: 'FileMetadata', required: true },
            uploadedAt: { type: Date, default: Date.now },
            status: {
              type: String,
              enum: ['PENDING', 'APPROVED', 'REJECTED'],
              default: 'PENDING',
            },
            approvedAt: { type: Date },
            rejectedAt: { type: Date },
            approvedBy: { type: Types.ObjectId, ref: 'BasicUser' },
            rejectedBy: { type: Types.ObjectId, ref: 'BasicUser' },
            rejectionReason: { type: String },
          },
        ],
      },
    ],
    default: [],
  })
  documents: {
    key: string;
    versions: {
      fileId: Types.ObjectId;
      uploadedAt: Date;
      status: 'PENDING' | 'APPROVED' | 'REJECTED';
      approvedAt?: Date;
      rejectedAt?: Date;
      approvedBy?: Types.ObjectId;
      rejectedBy?: Types.ObjectId;
      rejectionReason?: string;
    }[];
  }[];


  @Prop([
    {
      fileMetadataId: { type: String, ref: 'FileMetadata', required: true },
      status: { type: String, enum: ['pending', 'approved', 'rejected'], default: 'pending' },
      uploadedAt: { type: Date, default: Date.now },
      reviewedAt: { type: Date },
      reviewedBy: { type: Types.ObjectId, ref: 'BasicUser' },
      rejectionReason: { type: String },
      //   history: [
      //     {
      //       fileMetadataId: { type: Types.ObjectId, ref: 'FileMetadata' },
      //       status: { type: String, enum: ['pending', 'approved', 'rejected'] },
      //       uploadedAt: { type: Date },
      //     }
      //   ]
    }
  ])
  interimBgvDocs: {
    fileMetadataId: string;
    status: string;
    uploadedAt: Date;
    reviewedAt?: Date;
    reviewedBy?: Types.ObjectId;
    rejectionReason?: string;
    // history: {
    //   fileMetadataId: string;
    //   status: string;
    //   uploadedAt: Date;
    // }[];
  }[];

  //   @Prop({ type: Map, of: [SchemaTypes.ObjectId], default: {} })
  //   customBgvDocuments: Record<string, Types.ObjectId[]>;

  @Prop({
    type: [
      {
        replacedFileMetadataId: { type: Types.ObjectId, ref: 'FileMetadata', required: true },
        replacedAt: { type: Date, required: true },
        replacedStatus: { type: String, required: true },
        rejectionReason: { type: String, required: true },
        replacedByFileMetadataId: { type: Types.ObjectId, ref: 'FileMetadata', required: true },
        replacedByAt: { type: Date, required: true },
      },
    ],
    default: [],
  })
  interimBgvDocsHistory: InterimBgvDocsHistoryItem[];

  @Prop({
    type: {
      fileMetadataId: { type: String, ref: 'FileMetadata', required: true },
      uploadedBy: { type: Types.ObjectId, ref: 'BasicUser', required: true },
      uploadedAt: { type: Date, required: true },
      isAccepted: { type: Boolean, default: false },
      offerAcceptedAt: { type: Date },
      offerRejectedAt: { type: Date },
      isRejected: { type: Boolean, default: false },
      expectedCtc: { type: Number, default: false },
    },
    default: null,
  })
  offerLetterDoc: {
    fileMetadataId?: string;
    uploadedBy: Types.ObjectId;
    uploadedAt: Date;
    isAccepted: boolean;
    offerAcceptedAt?: Date;
    isRejected: boolean;
    offerRejectedAt?: Date;
    expectedCtc?: number;
  };

  @Prop({
    type: [
      {
        fileMetadataId: { type: String, ref: 'FileMetadata', required: true },
        uploadedBy: { type: Types.ObjectId, ref: 'BasicUser', required: true },
        uploadedAt: { type: Date, required: true },
        expectedCtc: { type: Number, default: false },

      },
    ],
    default: [],
  })
  offerLetterHistory: {
    fileMetadataId?: string;
    uploadedBy: Types.ObjectId;
    uploadedAt: Date;
    expectedCtc?: number;

  }[];

  @Prop({
    type: String,
    required: false,
    trim: true,
    // default: '<h1>Job Description</h1>'
  })
  rejectionReason?: string;

  @Prop({
    type: String,
    required: false,
    trim: true,
    // default: '<h1>Job Description</h1>'
  })
  status?: string;
}

export const BgvSchema = SchemaFactory.createForClass(Bgv);
