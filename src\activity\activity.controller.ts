import { Controller, Get, UseGuards, Req, Query } from '@nestjs/common';
import { ActivityService } from './activity.service';
import { CreateActivityDto } from './dto/create-activity.dto';
import { UpdateActivityDto } from './dto/update-activity.dto';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Roles } from 'src/auth/decorators/roles.decorator';
import { AuthJwtGuard } from 'src/auth/guards/auth-jwt/auth-jwt.guard';
import { RolesGuard } from 'src/auth/guards/roles/roles.guard';
import { Role } from 'src/auth/enums/role.enum';
import { Activity } from './schemas/activity.schema';
import { QueryDTO } from './dto/query-activity.dto';
@Controller('')
@ApiTags('Activities')
export class ActivityController {
  constructor(private readonly activityService: ActivityService) { }

  @Get()
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager)
  @Roles()
  @ApiOperation({ summary: 'Retrieve a activity by ID', description: 'This endpoint returns a activity by its Id. This is Accessible for everyone' })
  @ApiResponse({ status: 200, description: 'Activity is retrieved.' })
  @ApiResponse({ status: 404, description: 'Activity not found.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  // @ApiResponse({ status: 403, description: 'Forbidden, user with role admin and salesrep can only use this end point.' })
  async getActivities(
    @Req() req: any,
    @Query() query: QueryDTO
  ): Promise<Activity[]> {
    const { contactId = '', orgId = '', taskId = '', regionId,businessUnitId, userId, dateFilter, customDate, page, limit } = query;//query.filter
    const actor = req.user._id; // Extracting user ID from the request
    const queryOptions = { contactId, orgId, taskId, regionId, businessUnitId, userId, dateFilter, customDate, page, limit, actor };
    console.log(queryOptions);
    return await this.activityService.getActivities(queryOptions);
  }

  //Activities/orgId 
@Get('contact')
@ApiBearerAuth()
@UseGuards(AuthJwtGuard, RolesGuard)
// @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager)
@Roles()
@ApiOperation({ summary: 'Retrieve activities by contactId', description: 'This endpoint returns activities filtered by contactId. This is Accessible for Everyone' })
async getActivitiesByContact(
  @Req() req: any,
  @Query() query: QueryDTO
): Promise<Activity[]> {
  const { contactId = '', dateFilter, customDate, page, limit } = query;
  const actor = req.user._id;
  return await this.activityService.getActivitiesOfContact({ contactId, dateFilter, customDate, page, limit, actor });
}

@Get('org')
@ApiBearerAuth()
@UseGuards(AuthJwtGuard, RolesGuard)
// @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager)
@Roles()
@ApiOperation({ summary: 'Retrieve activities by orgId', description: 'This endpoint returns activities filtered by orgId. This is Accessible for everyone' })
async getActivitiesByOrg(
  @Req() req: any,
  @Query() query: QueryDTO
): Promise<Activity[]> {
  const { orgId = '', dateFilter, customDate, page, limit } = query; 
  const actor = req.user._id;
  return await this.activityService.getActivitiesOfOrg({ orgId, dateFilter, customDate, page, limit, actor });
}

@Get('task')
@ApiBearerAuth()
@UseGuards(AuthJwtGuard, RolesGuard)
// @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager)
@Roles()
@ApiOperation({ summary: 'Retrieve activities by taskId', description: 'This endpoint returns activities filtered by taskId. This is Accessible for everyone' })
async getActivitiesByTask(
  @Req() req: any,
  @Query() query: QueryDTO
): Promise<Activity[]> {
  const { taskId = '', dateFilter, customDate, page, limit } = query;
  const actor = req.user._id;
  return await this.activityService.getActivitiesOfTask({ taskId, dateFilter, customDate, page, limit, actor });
}

}
