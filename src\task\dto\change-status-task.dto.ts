import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty, IsEnum, IsOptional } from "class-validator";
import { CommentDto } from "src/common/dto/comment.dto";
import { Status } from "src/shared/constants";


export class ChangeStatusTaskDto {

    @ApiProperty({
        type: String,
        required: true,
        default: Status.TO_DO,
        enum: Status,
        description: 'Status of the task',
    })
    @IsNotEmpty()
    @IsEnum(Status)
    status: Status

    @ApiProperty({
        type: CommentDto,
        required: false
    })
    @IsOptional()
    comment?: CommentDto;
}
