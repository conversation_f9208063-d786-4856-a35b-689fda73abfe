import { Prop, SchemaFactory, Schema } from "@nestjs/mongoose";
import { HydratedDocument } from "mongoose";

export type SocialMediaLinkDocument = HydratedDocument<SocialMediaLink>;

@Schema({ _id: false, timestamps: false })
export class SocialMediaLink {

    @Prop({ type: String, required: true, trim: true })
    platform: string;

    @Prop({ type: String, required: true, trim: true })
    url: string;
}

export const SocialMediaLinkSchema = SchemaFactory.createForClass(SocialMediaLink);