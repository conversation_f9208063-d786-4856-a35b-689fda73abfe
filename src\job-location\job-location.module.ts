import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { JobLocationService } from './job-location.service';
import { JobLocationController } from './job-location.controller';
import { ConfigModule } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { MongooseModule } from '@nestjs/mongoose';
import { JobLocation, JobLocationSchema } from './schemas/job-location.schema';
import { JobsLocation, JobsLocationSchema } from './schemas/jobs-location.schema';
import { EndpointsRolesModule } from 'src/endpoints-roles/endpoints-roles.module';

@Module({

  imports: [
    ConfigModule,
    JwtModule, EndpointsRolesModule,
    MongooseModule.forFeature([{ name: JobLocation.name, schema: JobLocationSchema},{ name: JobsLocation.name, schema: JobsLocationSchema}])
  ],
  controllers: [JobLocationController],
  providers: [JobLocationService],
  exports: [JobLocationService]
})
export class JobLocationModule {}
