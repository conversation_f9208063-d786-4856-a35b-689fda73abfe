import { ApiHideProperty, ApiProperty } from "@nestjs/swagger";
import { IsArray, IsEmail, IsEnum, IsMongoId, IsNotEmpty, IsOptional, IsString, <PERSON><PERSON>ength, <PERSON><PERSON><PERSON>th, ValidateNested } from 'class-validator';
import { Transform, TransformFnParams, Type } from 'class-transformer';
import sanitizeWithStyle from 'sanitize-html'
import { Role } from "src/auth/enums/role.enum";


// Read about operators of swagger here - https://docs.nestjs.com/openapi/decorators
export class CreateMemberDto {

  @ApiProperty({
    type: String,
    required: true,
    description: 'Enter email of your account',
    format: 'email',
    default: '<EMAIL>'
  })
  @IsNotEmpty()
  @IsEmail()
  @IsString()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value).toLowerCase())
  email: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'First name of member'
  })
  @IsString()
  @IsNotEmpty()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  firstName: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Last name of member'
  })
  @IsString()
  @IsNotEmpty()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  lastName: string;

  // @ApiProperty({
  //   type: [String],
  //   required: true,
  //   isArray: true,
  //   description: 'Name of business unit of member',
  // })
  // @IsNotEmpty()
  // @IsArray()
  // @Transform((params: TransformFnParams) => params.value.map((businessUnit: string) => sanitizeWithStyle(businessUnit)))
  // businessUnit: string[];

  @ApiProperty({
    type: Object,
    required: true,
    description: 'Key-value map of selected business units (e.g., {"0-0": true, "0-1-0": true})',
  })
  // @IsNotEmpty()
  @ValidateNested()
  @Transform(({ value }) => {
    const sanitized: Record<string, boolean> = {};
    for (const key in value) {
      if (Object.prototype.hasOwnProperty.call(value, key)) {
        sanitized[sanitizeWithStyle(key)] = !!value[key]; // sanitize key, cast value to boolean
      }
    }
    return sanitized;
  })
  businessUnit: Record<string, boolean>;
  

  @ApiProperty({
    type: String,
    required: false,
    description: 'target of member',
  })
  @IsString()
  @IsOptional()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  target?: string;

  @ApiProperty({
    type: [String],
    required: false,
    isArray: true,
    description: 'Represents a member with senior role',
  })
  @IsOptional()
  @IsArray()
  @Transform((params: TransformFnParams) => params.value.map((reportingTo: string) => sanitizeWithStyle(reportingTo)))
  reportingTo?: string[];

  @ApiProperty({
    type: [String],
    required: true,
    isArray: true,
    // default: [Role.OrgUser],
    // enum: Role,
    description: "Role of member"
  })
  // @IsEnum(Role, { each: true }) 
  @IsArray()
  @Transform((params: TransformFnParams) => params.value.map((role: string) => sanitizeWithStyle(role)))
  roles: string[];

}
