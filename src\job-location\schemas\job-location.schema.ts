import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Types } from 'mongoose';

export type JobLocationDocument = HydratedDocument<JobLocation>;

@Schema({
  timestamps:true
})
export class JobLocation {
   
  @Prop({
    type: String,
    required: true,
    unique: true,
    trim: true
  })
  city: string;
 
  @Prop({
    type: String,
    required: true,
    trim: true
  })
  state: string;

  @Prop({
    type: String,
    required: true,
    trim: true
  })
  country: string;

  @Prop({
    type: String,
    required:false,
    trim: true
  })
  postalCode?: string;

  @Prop({ required: false, ref: 'Org' })
  orgId?: Types.ObjectId;

  @Prop({ required: false, type: Number }) // Latitude (X-Axis)
  latitude?: number; 

  @Prop({ required: false, type: Number }) // Longitude (Y-Axis)
  longitude?: number;

}

export const JobLocationSchema = SchemaFactory.createForClass(JobLocation);

