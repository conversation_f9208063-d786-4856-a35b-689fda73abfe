import { ApiHideProperty, PartialType } from '@nestjs/swagger';
import { CreateAccountDto } from './create-account.dto';
import { IsOptional } from 'class-validator';
import { Transform, TransformFnParams } from 'class-transformer';
import { sanitizeWithStyle } from "src/utils/sanitize-with-style";

export class UpdateAccountDto extends PartialType(CreateAccountDto) {

    @ApiHideProperty()
    @IsOptional()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    updatedBy?: string;

}
