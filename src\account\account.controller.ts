// import { Controller, Get, Post, Body, Patch, Param, Delete, Logger, Req, UseGuards, Query } from '@nestjs/common';
// import { AccountService } from './account.service';
// import { CreateAccountDto } from './dto/create-account.dto';
// import { UpdateAccountDto } from './dto/update-account.dto';
// import { ApiBearerAuth, ApiBody, ApiOperation, ApiParam, ApiProperty, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
// import { AuthJwtGuard } from 'src/auth/guards/auth-jwt/auth-jwt.guard';
// import { RolesGuard } from 'src/auth/guards/roles/roles.guard';
// import { Roles } from 'src/auth/decorators/roles.decorator';
// import { Role } from 'src/auth/enums/role.enum';
// import { validateObjectId } from 'src/utils/validation.utils';
// import { ChangeStatusDto } from '../common/dto/change-status.dto';
// import { MoveToDto } from '../common/dto/move-to.dto';
// import { QueryDTO } from './dto/query-account.dto';
// import { AccountDocument } from './schemas/account.schema';

// @Controller('')
// // @ApiTags('Accounts')
// export class AccountController {
//   private readonly logger = new Logger(AccountController.name);

//   constructor(private readonly accountService: AccountService) { }

//   @Post()
//   @ApiBearerAuth()
//   @UseGuards(AuthJwtGuard, RolesGuard)
//   @Roles(Role.Admin, Role.SuperAdmin, Role.SalesRep)
//   @ApiOperation({ summary: 'Create a new account', description: `This endpoint allows you to create a new account.This is accessible only for "admin" and "sales-rep"` })
//   @ApiResponse({ status: 201, description: 'Account is saved. ' })
//   @ApiResponse({ status: 400, description: 'Bad Request / Data. ' })
//   @ApiResponse({ status: 401, description: 'Unauthorized. ' })
//   @ApiResponse({ status: 403, description: 'Forbidden. User with role admin or sales-rep can only use this end point.' })
//   @ApiResponse({ status: 409, description: 'Account with this name already exists. ' })
//   @ApiBody({ type: CreateAccountDto, description: 'The account details to create' })
//   async create(@Req() req: any, @Body() createAccountDto: CreateAccountDto) {

//     const { accountType, ownedBy, industry } = createAccountDto;

//     createAccountDto.createdBy = req.user._id;

//     if (!ownedBy)
//       createAccountDto.ownedBy = req.user._id;
//     else {
//       validateObjectId(ownedBy);
//     }

//     if (accountType) {
//       try {
//         validateObjectId(accountType);
//       } catch (error) {
//         this.logger.error('Provide a valid account type mongoId')
//       }
//     }

//     if (industry) {
//       try {
//         validateObjectId(industry);
//       } catch (error) {
//         this.logger.error('Provide a valid account type mongoId')
//       }
//     }
//     return await this.accountService.create(createAccountDto);
//   }

//   @Get('all')
//   @ApiBearerAuth()
//   @UseGuards(AuthJwtGuard, RolesGuard)
//   @Roles(Role.Admin, Role.SuperAdmin, Role.SalesRep)
//   @ApiOperation({ summary: 'Retrieve all accounts', description: `This endpoint returns a list of all accounts. This is accessible only for "admin" and "sales-rep"` })
//   @ApiResponse({ status: 200, description: 'Accounts are retrieved.' })
//   @ApiResponse({ status: 401, description: 'User not logged in.' })
//   @ApiResponse({ status: 403, description: 'Forbidden. User with role admin or sales-rep  can only use this end point.' })
//   @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number', example: 1 })
//   @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of items per page', example: 10 })
//   findAll(@Query('page') page: number = 1, @Query('limit') limit: number = 10) {
//     return this.accountService.findAll(page, limit);
//   }


//   @Get('find-all-soft-deleted')
//   @ApiBearerAuth()
//   @UseGuards(AuthJwtGuard, RolesGuard)
//   @Roles(Role.Admin, Role.SuperAdmin)
//   @ApiOperation({ summary: 'Retrieve all accounts that are soft deleted', description: `This endpoint returns a list of all accounts that are soft deleted accounts. This is accessible only for "admin"` })
//   @ApiResponse({ status: 200, description: 'Accounts are retrieved.' })
//   @ApiResponse({ status: 401, description: 'User not logged in.' })
//   @ApiResponse({ status: 403, description: 'Forbidden. User with role admin can only use this end point.' })
//   @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number', example: 1 })
//   @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of items per page', example: 10 })
//   findAllWithSoftDelete(@Query('page') page: number = 1, @Query('limit') limit: number = 10) {
//     return this.accountService.findAllWithSoftDelete(page, limit);
//   }

//   @Get(':accountId')
//   @ApiBearerAuth()
//   @UseGuards(AuthJwtGuard, RolesGuard)
//   @Roles(Role.Admin, Role.SuperAdmin, Role.SalesRep)
//   @ApiOperation({ summary: 'Retrieve an account by Id', description: `This endpoint returns an account by its Id. This is accessible only for "admin" and "sales-rep"` })
//   @ApiResponse({ status: 200, description: 'Account is retrieved.' })
//   @ApiResponse({ status: 400, description: 'Bad Request: Invalid ID format.' })
//   @ApiResponse({ status: 401, description: 'Unauthorized. ' })
//   @ApiResponse({ status: 403, description: 'Forbidden. User with role admin or sales-rep can only use this end point.' })
//   @ApiResponse({ status: 404, description: 'Account not found.' })
//   @ApiParam({ name: 'accountId', description: 'id of the account' })
//   findOne(@Param('accountId') id: string) {

//     const objId = validateObjectId(id);
//     return this.accountService.findOne(objId);
//   }

//   @Get('search')
//   @ApiBearerAuth()
//   @UseGuards(AuthJwtGuard, RolesGuard)
//   @Roles(Role.Admin, Role.SuperAdmin, Role.SalesRep)
//   @ApiOperation({ summary: 'Retrieve all accounts by name', description: `This endpoint returns a list of all accounts by similar name . This is accessible only for "admin" and "sales-rep"` })
//   @ApiResponse({ status: 200, description: 'Accounts are retrieved.' })
//   @ApiResponse({ status: 401, description: 'User not logged in.' })
//   @ApiResponse({ status: 403, description: 'Forbidden. User with role admin or sales-rep  can only use this end point.' })
//   @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number', example: 1 })
//   @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of items per page', example: 10 })
//   async search(@Query('name') name: string, @Query('page') page: number = 1, @Query('limit') limit: number = 10) {
//     return this.accountService.searchAccounts(name, page, limit);
//   }

//   @Get('find-by-industry-and-accountType')
//   @ApiBearerAuth()
//   @UseGuards(AuthJwtGuard, RolesGuard)
//   @Roles(Role.Admin, Role.SuperAdmin, Role.SalesRep)
//   @ApiOperation({ summary: 'Filter all accounts by industry and account-type', description: 'This endpoint returns a list of all accounts filtered by industry or/and accountType. This is accessible only for "admin" and "sales-rep"' })
//   @ApiResponse({ status: 200, description: 'Accounts retrieved.' })
//   @ApiResponse({ status: 401, description: 'Unauthorized. ' })
//   @ApiResponse({ status: 403, description: 'Forbidden, user with role admin and sales-rep can only use this end point.' })
//   @ApiResponse({ status: 404, description: 'Account not found.' })
//   async getIndustryandAccountType(@Query() query: QueryDTO): Promise<AccountDocument[]> {
//     return await this.accountService.getIndustryandAccountType(query);
//   }

//   @Patch(':accountId')
//   @ApiBearerAuth()
//   @UseGuards(AuthJwtGuard, RolesGuard)
//   @Roles(Role.Admin, Role.SuperAdmin, Role.SalesRep)
//   @ApiOperation({ summary: 'Update an account by Id', description: `This endpoint updates an account by Id. This is accessible only for "admin" and "sales-rep"` })
//   @ApiParam({ name: 'accountId', description: 'id of the account' })
//   @ApiResponse({ status: 200, description: 'Account is updated.' })
//   @ApiResponse({ status: 401, description: 'Unauthorized. ' })
//   @ApiResponse({ status: 403, description: 'Forbidden. User with role admin or sales-rep can only use this end point.' })
//   @ApiResponse({ status: 404, description: 'Account not found.' })
//   update(@Req() req: any, @Param('accountId') id: string, @Body() updateAccountDto: UpdateAccountDto) {
//     const objId = validateObjectId(id);
//     return this.accountService.update(objId, updateAccountDto, req.user);
//   }

//   @Patch(':accountId/restore')
//   @ApiBearerAuth()
//   @UseGuards(AuthJwtGuard, RolesGuard)
//   @Roles(Role.Admin, Role.SuperAdmin)
//   @ApiOperation({ summary: 'Restore a soft-deleted account by Id', description: `This endpoint restores a soft-deleted account by Id. This is accessible only for "admin"` })
//   @ApiParam({ name: 'accountId', description: 'id of the account' })
//   @ApiResponse({ status: 200, description: 'Account is restored.' })
//   @ApiResponse({ status: 401, description: 'Unauthorized. ' })
//   @ApiResponse({ status: 403, description: 'Forbidden. User with role admin can only use this end point.' })
//   @ApiResponse({ status: 404, description: 'Account not found.' })
//   restoreSoftDeletedAccounts(@Param('accountId') id: string) {

//     const objId = validateObjectId(id);

//     return this.accountService.restoreSoftDeletedAccounts(objId);
//   }

//   @Patch(':accountId/status')
//   @ApiBearerAuth()
//   @UseGuards(AuthJwtGuard, RolesGuard)
//   @Roles(Role.Admin, Role.SuperAdmin, Role.SalesRep)
//   @ApiOperation({ summary: 'Update status of an account by Id', description: `This endpoint updates status of an account by Id. This is accessible only for "admin" and "sales-rep"` })
//   @ApiParam({ name: 'accountId', description: 'id of the account' })
//   @ApiResponse({ status: 200, description: 'Account is updated.' })
//   @ApiResponse({ status: 401, description: 'Unauthorized. ' })
//   @ApiResponse({ status: 403, description: 'Forbidden. User with role admin or sales-rep can only use this end point.' })
//   @ApiResponse({ status: 404, description: 'Account not found.' })
//   @ApiBody({ type: ChangeStatusDto, description: 'The account details to change status' })
//   changeStatus(@Req() req: any, @Param('accountId') accountId: string, @Body() changeStatusDto: ChangeStatusDto) {
//     const objId = validateObjectId(accountId);
//     const { status, comment } = changeStatusDto;
//     return this.accountService.changeStatus(objId, status, comment, req.user);
//   }
//   @Patch(':accountId/move')
//   @ApiBearerAuth()
//   @UseGuards(AuthJwtGuard, RolesGuard)
//   @Roles(Role.Admin, Role.SuperAdmin, Role.SalesRep)
//   @ApiOperation({ summary: 'Move the ownership of an account by Id', description: `This endpoint moves the ownership of an account by Id. This is accessible only for "admin" and "sales-rep"` })
//   @ApiParam({ name: 'accountId', description: 'id of the account' })
//   @ApiResponse({ status: 200, description: 'Account is updated.' })
//   @ApiResponse({ status: 401, description: 'Unauthorized. ' })
//   @ApiResponse({ status: 403, description: 'Forbidden. User with role admin or sales-rep can only use this end point.' })
//   @ApiResponse({ status: 404, description: 'Account not found.' })
//   @ApiBody({ type: MoveToDto, description: 'The account details to move' })
//   moveAccount(@Req() req: any, @Param('accountId') accountId: string, @Body() moveToDto: MoveToDto) {
//     const accountObjId = validateObjectId(accountId);
//     const { moveTo, comment } = moveToDto;
//     const newOwnerId = validateObjectId(moveTo);
//     return this.accountService.moveAccount(accountObjId, newOwnerId, comment, req.user);
//   }

//   @Delete(':accountId/delete')
//   @ApiBearerAuth()
//   @UseGuards(AuthJwtGuard, RolesGuard)
//   @Roles(Role.Admin, Role.SuperAdmin)
//   @ApiOperation({ summary: 'Delete an account by Id', description: `This endpoint deletes an account by Id. This is accessible only for "admin"` })
//   @ApiResponse({ status: 200, description: 'Account is deleted.' })
//   @ApiResponse({ status: 401, description: 'Unauthorized. ' })
//   @ApiResponse({ status: 403, description: 'Forbidden. User with role admin can only use this end point.' })
//   @ApiResponse({ status: 404, description: 'Account not found.' })
//   @ApiParam({ name: 'accountId', description: 'id of the account' })
//   remove(@Param('accountId') id: string) {

//     const objId = validateObjectId(id);
//     return this.accountService.remove(objId);
//   }

//   @Delete(':accountId/soft-delete')
//   @ApiBearerAuth()
//   @UseGuards(AuthJwtGuard, RolesGuard)
//   @Roles(Role.Admin, Role.SuperAdmin)
//   @ApiOperation({ summary: 'Soft Delete an account by Id', description: `This endpoint soft deletes an account by Id. This is accessible only for "admin"` })
//   @ApiResponse({ status: 200, description: 'Account is deleted.' })
//   @ApiResponse({ status: 401, description: 'Unauthorized. ' })
//   @ApiResponse({ status: 403, description: 'Forbidden. User with role admin can only use this end point.' })
//   @ApiParam({ name: 'accountId', description: 'id of the account' })
//   delete(@Param('accountId') id: string) {

//     const objId = validateObjectId(id);
//     return this.accountService.delete(objId);
//   }

//   @Delete('all')
//   @ApiBearerAuth()
//   @UseGuards(AuthJwtGuard, RolesGuard)
//   @Roles(Role.Admin, Role.SuperAdmin)
//   @ApiOperation({ summary: 'Remove  all accounts', description: `This endpoint deletes all accounts. This is accessible only for "admin"` })
//   @ApiResponse({ status: 200, description: 'All Accounts deleted.' })
//   @ApiResponse({ status: 401, description: 'Unauthorized. ' })
//   @ApiResponse({ status: 403, description: 'Forbidden. User with role admin can only use this end point.' })
//   deleteAll() {
//     return this.accountService.deleteAll();
//   }

// }
