import { <PERSON>, Get, Post, Body, Patch, Param, Delete, UseGuards, Query, Req, BadRequestException } from '@nestjs/common';
import { EndpointsRolesService } from './endpoints-roles.service';
import { ApiBearerAuth, ApiOperation, ApiParam, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { AuthJwtGuard } from 'src/auth/guards/auth-jwt/auth-jwt.guard';
import { validateObjectId } from 'src/utils/validation.utils';
import { CreateEndpointsRolesDto } from './dto/create-endpoints-role.dto';
import { UpdateEndpointsRolesDto } from './dto/update-endpoints-role.dto';

@ApiTags('Endpoints-Roles')
@Controller('')
export class EndpointsRolesController {
  constructor(private readonly endpointsRolesService: EndpointsRolesService) { }

  @Post()
  @ApiOperation({ summary: 'Create a new EndpointsRoles record' })
  @ApiResponse({ status: 201, description: 'Record created successfully.' })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  async create(@Req() req: any, @Body() createEndpointsRolesDto: CreateEndpointsRolesDto) {
    if (req.user.org) {
      createEndpointsRolesDto.org = req.user.org._id;
    }
    else{
      throw new BadRequestException('No org found in user');
    }
    return this.endpointsRolesService.create(createEndpointsRolesDto);
  }

  @Get('all')
  @ApiOperation({ summary: 'Retrieve all EndpointsRoles' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number', example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of items per page', example: 10 })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  async findAll(@Query('page') page: number = 1, @Query('limit') limit: number = 10) {
    return this.endpointsRolesService.findAll(page, limit);
  }

  @Get(':endpoinsRolesId')
  @ApiOperation({ summary: 'Retrieve a single EndpointsRoles by ID' })
  @ApiParam({ name: 'endpoinsRolesId', description: 'ID of the EndpointsRoles document' })
  @ApiResponse({ status: 200, description: 'Record found.' })
  @ApiResponse({ status: 404, description: 'Record not found.' })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  async findOne(@Param('endpoinsRolesId') endpoinsRolesId: string) {
    const objId = validateObjectId(endpoinsRolesId);
    return this.endpointsRolesService.findOne(objId);
  }

  @Patch(':endpoinsRolesId')
  @ApiOperation({ summary: 'Update an existing EndpointsRoles record' })
  @ApiParam({ name: 'endpoinsRolesId', description: 'ID of the EndpointsRoles document' })
  @ApiResponse({ status: 200, description: 'Record updated successfully.' })
  @ApiResponse({ status: 404, description: 'Record not found.' })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  async update(@Param('endpoinsRolesId') endpoinsRolesId: string, @Body() updateEndpointsRolesDto: UpdateEndpointsRolesDto) {
    const objId = validateObjectId(endpoinsRolesId);
    return this.endpointsRolesService.update(objId, updateEndpointsRolesDto);
  }

  @Delete(':endpoinsRolesId')
  @ApiOperation({ summary: 'Delete an EndpointsRoles record by ID' })
  @ApiParam({ name: 'endpoinsRolesId', description: 'ID of the EndpointsRoles document' })
  @ApiResponse({ status: 200, description: 'Record deleted successfully.' })
  @ApiResponse({ status: 404, description: 'Record not found.' })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  async remove(@Param('endpoinsRolesId') endpoinsRolesId: string) {
    const objId = validateObjectId(endpoinsRolesId);
    return this.endpointsRolesService.remove(objId);
  }
}
