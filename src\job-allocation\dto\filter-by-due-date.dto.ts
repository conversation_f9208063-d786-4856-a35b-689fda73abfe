import { ApiProperty } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { IsDateString, IsOptional, IsN<PERSON>ber, Min, IsISO8601, Length, IsMongoId } from "class-validator";

/**
 * DTO for filtering job allocations by due date
 * @class FilterByDueDateDto
 */
export class FilterByDueDateDto {
    /**
     * Due date for filtering job allocations
     * Must be a valid ISO 8601 date string
     * Examples:
     * - Date only: '2023-12-15'
     * - Date with time: '2023-12-15T14:30:00'
     * - Date with time and timezone: '2023-12-15T14:30:00Z'
     */
    @ApiProperty({
      description: 'Due date to filter by (ISO 8601 format)',
      required: true,
      default: new Date(Date.UTC(new Date().getUTCFullYear(), new Date().getUTCMonth(), new Date().getUTCDate(), new Date().getUTCHours(), new Date().getUTCMinutes())),

    })
    @IsDateString()
    @IsISO8601({strict: true})
    // @Length(10,24)
    dueDate: string;
  
    /**
     * Page number for pagination
     * Must be a positive integer
     * Defaults to 1 if not provided
     */
    @ApiProperty({
      description: 'Page number for pagination',
      example: 1,
      required: false,
      default: 1
    })
    @IsOptional()
    @Type(() => Number)
    @IsNumber()
    @Min(1)
    page?: number = 1;
  
    /**
     * Number of items per page
     * Must be a positive integer
     * Defaults to 10 if not provided
     */
    @ApiProperty({
      description: 'Number of items per page',
      example: 10,
      required: false,
      default: 10
    })
    @IsOptional()
    @Type(() => Number)
    @IsNumber()
    @Min(1)
    limit?: number = 10;

  @ApiProperty({ type: String, required: false, description: 'lead user ID' })
  @IsMongoId()
  @IsOptional()  // Make leadId optional
  leadId?: string;

  @ApiProperty({ type: String, required: false, description: 'Department ID' })
  @IsMongoId()
  @IsOptional()  // Make departmentId optional
  departmentId?: string;
  }