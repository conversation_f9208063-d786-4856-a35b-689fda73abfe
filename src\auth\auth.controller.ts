import {
  Body,
  Controller,
  Post,
  HttpCode,
  HttpStatus,
  Logger,
  HttpException,
  Req,
  Patch,
  Get,
  UseGuards,
  Param,
  Res,
  Query,
} from '@nestjs/common';
import { AuthService } from './auth.service';
import { LoginDto } from './dto/login.dto';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { CreateUserDto } from 'src/user/dto/create-user.dto';
import { ForgotPasswordDto } from 'src/user/dto/forgot-password.dto';
import { VerifyUuidDto } from 'src/user/dto/verify-uuid.dto';
import { ResetPasswordDto } from 'src/user/dto/reset-password.dto';
import { AuthJwtGuard } from './guards/auth-jwt/auth-jwt.guard';
import { PasswordDto } from 'src/user/dto/password.dto';
import { validateObjectId } from 'src/utils/validation.utils';
import { VerifyOtpDto } from 'src/user/dto/verify-otp.dto';
import { UserProfileDto } from './dto/user-profile.dto';
import { FastifyReply } from 'fastify';
import { BasicUser } from 'src/user/schemas/basic-user.schema';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Role } from './enums/role.enum';

@Controller('')
@ApiTags('Auth')
export class AuthController {
  private readonly logger = new Logger(AuthController.name);

  constructor(
    private authService: AuthService,
    @InjectModel(BasicUser.name) private basicUserModel: Model<BasicUser>,
  ) {}

  @Post('register')
  @ApiOperation({
    summary: 'Creates a new user',
    description: `This endpoint allows you to create a new user. This is accessible by everyone.`,
  })
  @ApiResponse({ status: 201, description: 'User is saved.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Data ' })
  create(@Body() createUserDto: CreateUserDto) {
    return this.authService.createUser(createUserDto);
  }

  @Post('login')
  @ApiOperation({
    summary: 'Sign in to Talency app.',
    description: `This endpoint allows you to log in to Talency app. This is accessible only by everyone.`,
  })
  @ApiResponse({ status: 200, description: 'User logged in.' })
  signIn(@Body() signInDto: LoginDto) {
    return this.authService.signIn(signInDto.email, signInDto.password);
  }

  @Get('logged-in-user')
  @ApiOperation({
    summary: 'Retrieves currenlty logged in user',
    description: `This endpoint returns currenlty logged in user. This endpoint accessible by currenlty logged in user.`,
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  @ApiResponse({ status: 200, description: 'User retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized ' })
  findCurrentUserLoggedIn(@Req() req: any) {
    return this.authService.findUserByEmail(req.user['email']);
  }

  @Post('refresh-token')
  @ApiOperation({
    summary: 'Refresh access token',
    description: `This endpoint allows you to refresh the access token.`,
  })
  @ApiResponse({ status: 200, description: 'Token refreshed successfully.' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  @UseGuards(AuthJwtGuard) // Ensure only authenticated users can refresh their tokens
  async refreshToken(@Req() req: any) {
    const userId = req.user.email; // Extract user ID from the request
    return this.authService.refreshToken(userId);
  }

  @Patch('profile')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  @ApiOperation({
    summary: 'Update user profile',
    description:
      'This endpoint allows users to update their profile information.',
  })
  @ApiResponse({ status: 200, description: 'Profile updated successfully.' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async updateProfile(@Req() req: any, @Body() userProfileDto: UserProfileDto) {
    const userId = validateObjectId(req.user._id);
    return this.authService.updateProfile(userId, userProfileDto);
  }

  // @Post('verify-email')
  // @ApiOperation({ summary: 'Verify Email', description: 'This endpoint allows you to verify email. This is accessible by individual user.' })
  // @ApiResponse({ status: 201, description: 'Confirm user account.' })
  // @ApiResponse({ status: 400, description: 'Bad Request / Data ' })
  // async verifyEmail(@Body() verifyUuidDto: VerifyUuidDto) {
  //   return await this.authService.verifyEmail(verifyUuidDto);
  // }

  @Post('verify-email-otp')
  @ApiOperation({
    summary: 'Verify Email by OTP',
    description:
      'This endpoint allows you to verify email by OTP. This is accessible by individual user.',
  })
  @ApiResponse({ status: 201, description: 'Confirm user account.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Data ' })
  async verifyEmailAddressByOtp(@Body() verifyOtpDto: VerifyOtpDto) {
    return await this.authService.verifyEmailByOtp(verifyOtpDto);
  }

  // @Get(':id/confirm/:token')
  // @ApiOperation({ summary: 'Verify Email', description: 'This endpoint allows you to verify email. This is accessible by everyone.' })
  // @ApiResponse({ status: 200, description: 'Confirm user account.' })
  // @ApiResponse({ status: 400, description: 'Bad Request / Data ' })
  // @ApiParam({ name: 'id', description: 'user id' })
  // @ApiParam({ name: 'token', description: 'verification token' })
  // async verifyEmailAddress(@Param('id') userId: string, @Param() params: any) {
  //   let verifyUuidDto: VerifyUuidDto = {
  //     verification: params.token,
  //   };
  //   let userObjId = validateObjectId(userId);
  //   return await this.authService.verifyEmail(verifyUuidDto, userObjId);
  // }

  @Post(':userId/verify-email/otp')
  @ApiOperation({
    summary: 'Verify Email by OTP',
    description:
      'This endpoint allows you to verify email by OTP. This is accessible by individual user.',
  })
  @ApiResponse({ status: 201, description: 'Confirm user account.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Data ' })
  @ApiParam({ name: 'userId', description: 'user id' })
  async verifyEmailByOtp(
    @Body() verifyOtpDto: VerifyOtpDto,
    @Param('userId') userId: string,
  ) {
    let userObjId = validateObjectId(userId);
    return await this.authService.verifyEmailByOtp(verifyOtpDto, userObjId);
  }

  @Post('forgot-password')
  @ApiOperation({
    summary: 'Forgot Password',
    description:
      'This endpoint allows you to reset a forgotten password. This is accessible by everyone.',
  })
  @ApiResponse({ status: 200, description: 'Instructions sent to email.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Data' })
  submitForgotPassword(@Body() forgotPasswordDto: ForgotPasswordDto) {
    return this.authService.handleForgotPasswordRequest(forgotPasswordDto);
  }

  // @Post('forgot-password-verify')
  // @ApiOperation({ summary: 'Verify forget password code', description: 'This endpoint allows you to verify forget password code. This is accessible by everyone.' })
  // @ApiResponse({ status: 200, description: 'Password code verified.' })
  // @ApiResponse({ status: 400, description: 'Bad Request / Data' })
  // async forgotPasswordVerify(@Body() verifyUuidDto: VerifyUuidDto) {
  //   return await this.authService.forgotPasswordVerify(verifyUuidDto);
  // }

  @Post('forgot-password-verify-otp')
  @ApiOperation({
    summary: 'Verify forget password code',
    description:
      'This endpoint allows you to verify forget password code by Otp. This is accessible by everyone.',
  })
  @ApiResponse({ status: 200, description: 'Password code verified.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Data' })
  async forgotPasswordVerifyByOtp(@Body() verifyOtpDto: VerifyOtpDto) {
    return await this.authService.forgotPasswordVerifyByOtp(verifyOtpDto);
  }

  // @Patch('reset-password')
  // @ApiOperation({ summary: 'Reset the password', description: 'This endpoint allows you to reset the password. This is accessible by everyone.' })
  // @ApiResponse({ status: 200, description: 'Password is reset.' })
  // @ApiResponse({ status: 400, description: 'Bad Request / Data' })
  // async resetPassword(@Body() resetPasswordDto: ResetPasswordDto) {
  //   this.logger.log(resetPasswordDto.email)
  //   this.logger.log(resetPasswordDto.password)
  //   return await this.authService.resetPassword(resetPasswordDto);
  // }

  @Patch('update-password')
  @ApiOperation({
    summary: 'Change password of the user after forgotten',
    description: `This endpoint updates password of the user after forget password is verified by otp.`,
  })
  @ApiResponse({ status: 200, description: 'Password updated.' })
  @ApiResponse({ status: 401, description: 'Unauthorized ' })
  @ApiResponse({ status: 404, description: 'User not found.' })
  updatePasswordAfterForget(@Body() passwordDto: PasswordDto) {
    return this.authService.updatePasswordAfterForget(passwordDto);
  }

  @Patch('change-password')
  @ApiOperation({
    summary: 'Change password of the user',
    description: `This endpoint updates password of the user by Id. This is accessible by logged in user.`,
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  @ApiResponse({ status: 200, description: 'Password updated.' })
  @ApiResponse({ status: 401, description: 'Unauthorized ' })
  @ApiResponse({ status: 404, description: 'User not found.' })
  updatePassword(@Req() req: any, @Body() passwordDto: PasswordDto) {
    return this.authService.updatePassword(req.user._id, passwordDto);
  }

  @Post('resend-verification-email')
  @ApiOperation({
    summary: 'Resends verification email',
    description:
      'This endpoint allows to resend verification email. This is accessible by everyone.',
  })
  @ApiResponse({ status: 200, description: 'Verification email sent.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Data' })
  async resendVerificationTokenEmail(
    @Body() forgotPasswordDto: ForgotPasswordDto,
  ) {
    return await this.authService.resendVerificationTokenEmail(
      forgotPasswordDto,
    );
  }

  @Post('resend-forgot-password-email')
  @ApiOperation({
    summary: 'Resends forgot password email',
    description:
      'This endpoint allows to resend forgot password email. This is accessible by everyone.',
  })
  @ApiResponse({ status: 200, description: 'Resends forgot password email.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Data' })
  async resendForgotPasswordEmail(
    @Body() forgotPasswordDto: ForgotPasswordDto,
  ) {
    return await this.authService.resendForgotPasswordEmail(forgotPasswordDto);
  }

  @Get('google')
  async redirectToGoogle(
    @Query('role') role: 'job-seeker' | 'freelancer',
    @Res() res: FastifyReply,
  ) {
    // if (!role) {
    //   res.status(400).send({ message: 'Role is required' });
    //   return;
    // }
    const rootUrl = 'https://accounts.google.com/o/oauth2/v2/auth';
    const options = {
      redirect_uri:
        process.env.GOOGLE_OAUTH_REDIRECT_URI ||
        'https://api.talsy.ai/api/auth/google/callback',
      client_id:
        process.env.GOOGLE_OAUTH_CLIENT_ID ||
        '************-phmr5rkfupou72upte2ac7f98sm55q2o.apps.googleusercontent.com',
      access_type: 'offline',
      response_type: 'code',
      prompt: 'consent',
      state: role, // ✅ Store role in "state" parameter
      scope: [
        'https://www.googleapis.com/auth/userinfo.profile',
        'https://www.googleapis.com/auth/userinfo.email',
      ].join(' '),
    };

    const qs = new URLSearchParams(options).toString();
    // console.log('qs', qs);
    const redirectUrl = `${rootUrl}?${qs}`;
    // console.log('Redirecting to:', redirectUrl);
    // res.redirect(redirectUrl); // Make sure this is called
    // return res.redirect(`${rootUrl}?${qs}`);
    res.redirect(302, redirectUrl); // ✅ Send 302 status
  }

  @Get('google/callback')
  async handleGoogleCallback(
    @Query('code') code: string,
    @Query('state') role: string,
    @Query('error') error: string,
    @Res() res: FastifyReply,
  ) {
    console.log(error, role);
    if (error && role === 'undefined') {
      console.log('Google login error:', error); // Log the error message
      const msg = encodeURIComponent(`Google login failed: ${error}`);
      return res.redirect(
        302,
        `https://portal.talsy.ai/auth/login?error=${msg}`,
      );
    }
    if (error && role === 'freelancer') {
      console.log('Google login error:', error); // Log the error message
      const msg = encodeURIComponent(`Google login failed: ${error}`);
      return res.redirect(
        302,
        `https://portal.talsy.ai/auth/register?from=freelancer&error=${msg}`,
      );
    }
    if (error && role === 'job-seeker') {
      console.log('Google login error:', error); // Log the error message
      const msg = encodeURIComponent(`Google login failed: ${error}`);
      return res.redirect(
        302,
        `https://portal.talsy.ai/auth/register?from=candidate&error=${msg}`,
      );
    }
    const { id_token, access_token } = await this.authService.getGoogleTokens(
      code,
      process.env.GOOGLE_OAUTH_REDIRECT_URI ||
        'https://api.talsy.ai/api/auth/google/callback',
    );
    console.log('Access Token:', access_token);
    console.log('ID Token:', id_token);
    const user = await this.authService.getGoogleUserInfo(
      id_token,
      access_token,
    );
    console.log('User info:', user);
    const userExists = await this.basicUserModel.findOne({ email: user.email });
    // console.log('User exists:', userExists); // Log the user existence check
    // console.log('Role:', role); // Log the role for debugging
    if (userExists === null && role === 'undefined') {
      const msg = encodeURIComponent('No account found. Please register.');
      console.log('Redirecting to Talency frontend with error:', msg); // Log the error message
      res.redirect(302, `https://portal.talsy.ai/auth/login?error=${msg}`);
      return res;
    }
    // Here: Find or create user in DB (not shown)
    const jwt = await this.authService.generateJwtForGoogleUser(user, role);
    console.log('JWT:', jwt); // Log the JWT for debugging

    // You can return the token or redirect with the token
    // return res.send({
    //   // message: 'Login successful',
    //   // token: jwt,
    //   // user,
    // });
    // return res.send(jwt)
    const frontendRedirectUrl = `https://portal.talsy.ai/auth/login#token=${jwt?.access_token}`;

    res.redirect(302, frontendRedirectUrl);
    return res; // 🔁 Important with Fastify
  }

  @Get('linkedin')
  async redirectToLinkedIn(
    @Query('role') role: 'job-seeker' | 'freelancer',
    @Res() res: FastifyReply,
  ) {
    const rootUrl = 'https://www.linkedin.com/oauth/v2/authorization';

    const options = {
      response_type: 'code',
      redirect_uri:
        process.env.LINKEDIN_OAUTH_REDIRECT_URI ||
        'https://api.talsy.ai/api/auth/linkedin/callback',
      client_id: process.env.LINKEDIN_OAUTH_CLIENT_ID || '86a4d0btrpqvar',
      // client_id: process.env.LINKEDIN_CLIENT_ID,
      // redirect_uri: process.env.LINKEDIN_REDIRECT_URI || 'https://apis.gana.talency.in/api/auth/linkedin/callback',
      // scope: 'r_liteprofile r_emailaddress',
      scope: 'openid email profile',
      state: role, // Pass role via LinkedIn's `state` param
    };

    const qs = new URLSearchParams(options).toString();
    const redirectUrl = `${rootUrl}?${qs}`;

    res.redirect(302, redirectUrl);
  }

  @Get('linkedin/callback')
  async handleLinkedInCallback(
    @Query('code') code: string,
    @Res() res: FastifyReply,
    @Query('state') role?: string,
    @Query('error') error?: string,
    @Query('error_description') errorDescription?: string,
  ) {
    console.log(error, role);
    if (error && role === 'undefined') {
      const msg = encodeURIComponent(`LinkedIn login failed: ${error}`);
      return res.redirect(
        302,
        `https://portal.talsy.ai/auth/login?error=${msg}`,
      );
    }
    if (error && role === 'freelancer') {
      console.log('LinkedIn login error:', error); // Log the error message
      const msg = encodeURIComponent(`LinkedIn login failed: ${error}`);
      return res.redirect(
        302,
        `https://portal.talsy.ai/auth/register?from=freelancer&error=${msg}`,
      );
    }
    if (error && role === 'job-seeker') {
      console.log('LinkedIn login error:', error); // Log the error message
      const msg = encodeURIComponent(`LinkedIn login failed: ${error}`);
      return res.redirect(
        302,
        `https://portal.talsy.ai/auth/register?from=candidate&error=${msg}`,
      );
    }
    const redirectUri = process.env.LINKEDIN_OAUTH_REDIRECT_URI || '';

    // Exchange code for tokens
    const tokenRes = await this.authService.exchangeLinkedInCodeForTokens(
      code,
      redirectUri,
    );
    console.log('LinkedIn Token Response:', tokenRes); // Log the token response for debugging

    // Decode ID Token (JWT) to extract user info
    // const userInfo = await this.authService.decodeLinkedInIdToken(tokenRes.id_token);
    const userInfo = await this.authService.getLinkedInUserInfo(
      tokenRes.access_token,
    );
    console.log('LinkedIn User Info:', userInfo); // Log the user info for debugging
    const userExists = await this.basicUserModel.findOne({
      email: userInfo.email,
    });
    // console.log('User exists:', userExists); // Log the user existence check
    // console.log('Role:', role); // Log the role for debugging
    // console.log('userExists:', userExists, typeof userExists); // log type too
    // console.log('Is userExists null?', userExists === null); // Check if it's truly null
    // console.log('role:', role, typeof role);
    // console.log('Is role undefined?', role === undefined, role === null, !role, role == undefined, role == null, role === 'undefined'); // Explicit check for undefined

    // Check if user exists and role is undefined or null
    // if (!userExists && (role === undefined || role === null)) {
    //   const msg = encodeURIComponent('No account found. Please register.');
    //   console.log('Redirecting to Talency frontend with error:', msg); // Log the error message
    //   res.redirect(302, `https://talency-frontend.vercel.app/auth/login?error=${msg}`);
    //   return res;
    // }

    // Check if user exists and role is undefined or null
    if (userExists === null && role === 'undefined') {
      const msg = encodeURIComponent('No account found. Please register.');
      console.log('Redirecting to Talency frontend with error:', msg); // Log the error message
      res.redirect(302, `https://portal.talsy.ai/auth/login?error=${msg}`);
      return res;
      //  return res.send(msg)
      //  return res.status(404).send({ error: msg });
    }
    try {
      const jwt = await this.authService.generateJwtForLinkedInUser(
        userInfo,
        role,
      );
      const frontendRedirectUrl = `https://portal.talsy.ai/auth/login#token=${jwt?.access_token}`;
      res.redirect(302, frontendRedirectUrl);
    } catch (error) {
      console.error('Error generating JWT for LinkedIn user:', error);
      const msg = encodeURIComponent(`LinkedIn login failed: ${error.message}`);
      return res.redirect(
        302,
        `https://portal.talsy.ai/auth/login?error=${msg}`,
      );
    }

    // return res.send(jwt)
    // return res.status(404).send({ error: jwt });
  }
}
