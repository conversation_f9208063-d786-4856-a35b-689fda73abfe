import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { BgvHandlerController } from './bgv-handler.controller';
import { BgvHandlerService } from './bgv-handler.service';
import { Bgv<PERSON><PERSON><PERSON>, BgvHandlerSchema } from './schemas/bgv-handler.schema';
import { JwtModule } from '@nestjs/jwt';
import { EndpointsRolesModule } from 'src/endpoints-roles/endpoints-roles.module';
import { Org, OrgSchema } from 'src/org/schemas/org.schema';
import { BusinessUnit, BusinessUnitSchema } from 'src/business-unit/schemas/business-unit.schema';
import { Offer, OfferSchema } from 'src/offer/schemas/offer.schema';
import { BasicUser, BasicUserSchema } from 'src/user/schemas/basic-user.schema';
import { Bgv, BgvSchema } from 'src/offer/schemas/bgv.schema';

@Module({
    imports: [JwtModule, EndpointsRolesModule,
        MongooseModule.forFeature([
            { name: BgvHandler.name, schema: BgvHandlerSchema },
            { name: Org.name, schema: OrgSchema },
            { name: BusinessUnit.name, schema: BusinessUnitSchema },
            { name: Offer.name, schema: OfferSchema },
            { name: BasicUser.name, schema: BasicUserSchema },
            { name: Bgv.name, schema: BgvSchema },

        ])],
    controllers: [BgvHandlerController],
    providers: [BgvHandlerService],
    exports: [BgvHandlerService],
})
export class BgvHandlerModule { }