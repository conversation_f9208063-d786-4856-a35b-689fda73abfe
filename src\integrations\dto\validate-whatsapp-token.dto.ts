import { ApiPropertyOptional } from "@nestjs/swagger";
import { IsNotEmpty, IsString } from "class-validator";

export class ValidateWhatsAppTokenDto {


    @ApiPropertyOptional({
        description: 'Permanent WhatsApp access token from Meta Developer portal',
        example: 'EAAGm0PX4ZCpsBAKZA...',
        required: true
    })
    @IsNotEmpty()
    @IsString()
    whatsappAccessToken: string;

}
