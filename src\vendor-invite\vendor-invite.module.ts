import { forwardRef, Module } from '@nestjs/common';
import { VendorInviteService } from './vendor-invite.service';
import { VendorInviteController } from './vendor-invite.controller';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { UserModule } from 'src/user/user.module';
import { CommonModule } from 'src/common/common.module';
import { MongooseModule } from '@nestjs/mongoose';
import { VendorInvite, VendorInviteSchema } from './schemas/vendor-invite.schmea';
import { Org, OrgSchema } from 'src/org/schemas/org.schema';
import { BasicUser, BasicUserSchema } from 'src/user/schemas/basic-user.schema';
import { UserTemp, UserTempSchema } from 'src/user/schemas/user-temp.schema';
import { EndpointsRolesModule } from 'src/endpoints-roles/endpoints-roles.module';
@Module({
  imports: [
    ConfigModule,
    JwtModule, EndpointsRolesModule,
    forwardRef(() => UserModule),
    CommonModule,
    MongooseModule.forFeature([
      { name: VendorInvite.name, schema:VendorInviteSchema },
      { name: Org.name, schema:OrgSchema },
      { name: BasicUser.name, schema:BasicUserSchema },
      {name: UserTemp.name, schema:UserTempSchema}
    ]),
   
  ],
  controllers: [VendorInviteController],
  providers: [VendorInviteService],
  exports: [VendorInviteService, MongooseModule],
})
export class VendorInviteModule {}

