import { ApiHideProperty, ApiProperty } from "@nestjs/swagger";
import { Transform, TransformFnParams } from "class-transformer";
import { IsBoolean, IsMongoId, IsNotEmpty, IsNumber, IsOptional, IsString } from "class-validator";
import { sanitizeWithStyle } from "src/utils/sanitize-with-style";

export class CreateJobLocationDto {

  @ApiProperty({
    type: String,
    required: true,
    description: 'Name of the city',
  })
  @IsNotEmpty()
  @IsString()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  city: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Name of the state',
  })
  @IsNotEmpty()
  @IsString()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  state: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Name of the country',
  })
  @IsNotEmpty()
  @IsString()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  country: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Postal code of the job location',
  })
  @IsString()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  postalCode?: string;

  @ApiProperty({ example: 17.3850, description: 'Latitude (X-Axis)', required: false })
  @IsOptional()
  @IsNumber()
  latitude?: number;

  @ApiProperty({ example: 78.4867, description: 'Longitude (Y-Axis)', required: false })
  @IsOptional()
  @IsNumber()
  longitude?: number;

  @ApiHideProperty()
  @IsMongoId()
  @IsOptional()
  orgId?: string;

}
