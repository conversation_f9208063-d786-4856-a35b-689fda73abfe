import { Controller, Get, Post, Body, Patch, Param, Delete, Logger, Query, Req, UseGuards, BadRequestException } from '@nestjs/common';
import { WorkflowService } from './workflow.service';
import { ApiBearerAuth, ApiBody, ApiOperation, ApiParam, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Roles } from 'src/auth/decorators/roles.decorator';
import { Role } from 'src/auth/enums/role.enum';
import { AuthJwtGuard } from 'src/auth/guards/auth-jwt/auth-jwt.guard';
import { RolesGuard } from 'src/auth/guards/roles/roles.guard';
import { validateObjectId } from 'src/utils/validation.utils';
import { CreateWorkflowDto } from './dto/create-workflow.dto';
import { UpdateWorkflowDto } from './dto/update-workflow.dto';
import { CreateStageDto } from 'src/stage/dto/create-stage.dto';
import { UpdateStageOrderDto } from './dto/update-stage-order.dto';
import { SendTrackerEmailContentDto } from './dto/tracker-email-template.dto';
import { UpdateJobApplicationFormDto } from 'src/job-application-form/dto/update-job-application-form.dto';
import { JobApplicationFormService } from 'src/job-application-form/job-application-form.service';
import { InterviewService } from 'src/interview/interview.service';
import { UpdateJobApplicationStatusDto } from 'src/job-application-form/dto/update-job-application-status.dto';
@Controller('')
@ApiTags('Workflow')
export class WorkflowController {

  private readonly logger = new Logger(WorkflowController.name);

  constructor(private readonly workflowService: WorkflowService, private readonly jobApplicationFormService: JobApplicationFormService, private readonly interviewService: InterviewService) { }


  @Post()
  @ApiOperation({
    summary: 'Create a new workflow',
    description: `This endpoint allows you to create a new workflow. Accessible only to users with roles "${Role.BUHead}", "${Role.ResourceManager}", "${Role.DeliveryManager}","${Role.TeamLead}", and "${Role.AccountManager}".`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager, Role.Vendor)
  @Roles()
  @ApiResponse({ status: 201, description: 'The workflow is created.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Data.' })
  @ApiResponse({ status: 401, description: 'Unauthorized access. Authentication is required.' })
  @ApiResponse({ status: 403, description: `Forbidden. Only users with roles "${Role.Admin}","${Role.BUHead}", "${Role.ResourceManager}", "${Role.DeliveryManager}","${Role.TeamLead}", and "${Role.AccountManager}" are permitted to use this endpoint.` })
  create(@Req() req: any, @Body() createWorkflowDto: CreateWorkflowDto) {
    if (!createWorkflowDto.org && req.user.org) {
      createWorkflowDto.org = req.user.org._id
    }
    return this.workflowService.create(createWorkflowDto);
  }


  @Get('all')
  @ApiOperation({
    summary: 'Retrieve all workflows.',
    description: `This endpoint retrieves a list of all workflows including soft deleted workflows. Accessible is for everyone.`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Recruiter, Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager, Role.Vendor)
  @Roles()
  @ApiResponse({ status: 200, description: 'All workflows are retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  // @ApiResponse({ status: 403, description: `Forbidden. Only users with roles "${Role.SuperAdmin}" and "${Role.Admin}" are permitted to use this endpoint.` })
  getAllWorkflows() {
    return this.workflowService.getAllWorkflows();
  }

  @Get(':workflowId')
  @ApiOperation({
    summary: 'Retrieve an workflow by Id',
    description: 'This endpoint retrieves an workflow by its Id. Accessible is for everyone.'
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.AccountManager, Role.TeamLead, Role.TeamMember, Role.Recruiter, Role.Vendor, Role.JobSeeker, Role.Freelancer)
  @Roles()
  @ApiResponse({ status: 200, description: 'Workflow retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  // @ApiResponse({ status: 403, description: 'Forbidden' })
  @ApiResponse({ status: 404, description: 'Workflow not found.' })
  findOne(@Param('workflowId') workflowId: string) {
    const objId = validateObjectId(workflowId);
    return this.workflowService.findOne(objId);
  }

  @Patch(':workflowId')
  @ApiOperation({
    summary: 'Update an workflow by Id',
    description: `This endpoint updates an workflow by Id. Accessible only to users with roles "${Role.BUHead}", "${Role.ResourceManager}", "${Role.DeliveryManager}","${Role.TeamLead}", and"${Role.AccountManager}".`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard,)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager, Role.Vendor)
  @Roles()
  @ApiResponse({ status: 200, description: 'Workflow is updated.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 403, description: `Forbidden. Only users with roles "${Role.Admin}", ,"${Role.BUHead}", "${Role.ResourceManager}", "${Role.DeliveryManager}","${Role.TeamLead}", and "${Role.AccountManager}" are permitted to use this endpoint.` })
  @ApiResponse({ status: 404, description: 'Workflow not found.' })
  @ApiParam({ name: 'workflowId', description: 'ID of the workflow' })
  update(@Req() req: any, @Param('workflowId') workflowId: string, @Body() updateWorkflowDto: UpdateWorkflowDto) {
    const objId = validateObjectId(workflowId);
    return this.workflowService.update(objId, updateWorkflowDto, req.user);
  }

  @Patch(':workflowId/stage')
  @ApiResponse({ status: 200, description: `Created stage and updated in workflow.` })
  @ApiResponse({ status: 401, description: `Unauthorized.` })
  @ApiResponse({ status: 400, description: `Bad Request / Data.` })
  @ApiResponse({ status: 403, description: `Forbidden, User with role "${Role.Admin}", "${Role.BUHead}", "${Role.ResourceManager}", "${Role.DeliveryManager}","${Role.TeamLead}", "${Role.AccountManager}"can only use this end point.` })
  @ApiOperation({ summary: `Create stage and updated in workflow.`, description: `This endpoint allows you to create a new stage and update in workflow. This is accessible only for "${Role.BUHead}", "${Role.ResourceManager}", "${Role.DeliveryManager}","${Role.TeamLead}", and "${Role.AccountManager}".` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager, Role.Vendor)
  @Roles()
  createStageAndUpdateInWorkflow(@Param('workflowId') workflowId: string, @Body() createStageDto: CreateStageDto) {
    const objId = validateObjectId(workflowId);
    return this.workflowService.createStageAndUpdateInWorkflow(objId, createStageDto);
  }

  @Patch(':workflowId/update-stage-order')
  @ApiResponse({ status: 200, description: `Reorder stages in workflow.` })
  @ApiResponse({ status: 401, description: `Unauthorized.` })
  @ApiResponse({ status: 400, description: `Bad Request / Data.` })
  @ApiResponse({ status: 403, description: `Forbidden, User with role "${Role.Admin}",  "${Role.BUHead}", "${Role.ResourceManager}", "${Role.DeliveryManager}","${Role.TeamLead}", and "${Role.AccountManager}" can only use this end point.` })
  @ApiOperation({ summary: `Reorder stages in workflow.`, description: `This endpoint allows you to stages in workflow. This is accessible only for "${Role.BUHead}", "${Role.ResourceManager}", "${Role.DeliveryManager}","${Role.TeamLead}", and"${Role.AccountManager}".` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager, Role.Vendor)
  @Roles()
  async updateWorkflowStageOrder(@Param('workflowId') workflowId: string, @Body() updateStageOrderDto: UpdateStageOrderDto) {
    const objId = validateObjectId(workflowId);
    return this.workflowService.updateWorkflowStageOrder(objId, updateStageOrderDto.stageOrder);
  }

  @Delete(':workflowId/hard-delete')
  @ApiOperation({
    summary: 'Hard delete an workflow by Id',
    description: `Deletes an workflow permanently by its Id. This is accessible only for "${Role.Admin}", "${Role.BUHead}", "${Role.ResourceManager}", "${Role.DeliveryManager}","${Role.TeamLead}", and"${Role.AccountManager}".`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard,)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager, Role.Vendor)
  @Roles()
  @ApiResponse({ status: 200, description: 'Workflow is hard deleted.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 403, description: `Forbidden. Only users with roles "${Role.Admin}", "${Role.BUHead}", "${Role.ResourceManager}", "${Role.DeliveryManager}","${Role.TeamLead}", and "${Role.AccountManager}" are permitted to use this endpoint.` })
  @ApiResponse({ status: 404, description: 'Workflow not found.' })
  @ApiParam({ name: 'workflowId', description: 'ID of the workflow' })
  remove(@Param('workflowId') workflowId: string) {
    const objId = validateObjectId(workflowId);
    return this.workflowService.hardDelete(objId);
  }

  // @Delete('delete-all')
  // @ApiOperation({ 
  //   summary: 'Hard delete all workflows', 
  //   description: `This endpoint hard deletes all workflows. This is accessible only for "${Role.SuperAdmin}" and "${Role.Admin}"` 
  // })

  // @ApiBearerAuth()
  // @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.SuperAdmin, Role.Admin)
  // @ApiResponse({ status: 200, description: 'All workflows are deleted.' })
  // @ApiResponse({ status: 401, description: 'Unauthorized.' })
  // @ApiResponse({ status: 403, description: `Forbidden. Only users with roles "${Role.SuperAdmin}" and "${Role.Admin}" are permitted to use this endpoint.` })
  // @ApiResponse({ status: 404, description: 'Workflows not found.' })
  // deleteAll() {
  //   return this.workflowService.hardDeleteAllWorkflows();
  // }

  @Get('filter')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.AccountManager, Role.TeamLead, Role.TeamMember, Role.Recruiter, Role.Vendor)
  @Roles()
  @ApiOperation({
    summary: 'Retrieve workflow created by organization',
    description: `This endpoint returns a list of workflow created by organization. This is accessible for everyone.`,
  })
  @ApiResponse({ status: 200, description: 'Workflow retrieved created by organization.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  // @ApiResponse({ status: 403, description: 'Forbidden, user with role admin, superAdmin, and sales rep can only use this endpoint.' })
  @ApiQuery({ name: 'orgId', required: true, type: String, description: 'Organization Id' })
  @ApiQuery({ name: 'businessUnitId', required: false, type: String, description: 'Business Unit Id' })
  @ApiQuery({ name: 'isDefault', required: false, type: Boolean, description: 'Filter by default workflow status (true or false)' })
  async workflowFilter(@Query('orgId') orgId: string, @Query('businessUnitId') businessUnitId: string, @Query('isDefault') isDefault?: boolean,) {
    validateObjectId(orgId);
    return this.workflowService.workflowFilter(orgId, businessUnitId, isDefault);
  }

  @Post('send-tracker-email-content')
  @ApiOperation({
    summary: 'Generate tracker email content for job applications',
    description: `This endpoint generates email content for an array of job applications using a tracker template. Accessible to roles "${Role.BUHead}", "${Role.ResourceManager}", "${Role.DeliveryManager}","${Role.TeamLead}", and "${Role.AccountManager}".`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager, Role.Vendor, Role.Recruiter)
  @Roles()
  @ApiResponse({ status: 200, description: 'Email content generated successfully.' })
  @ApiResponse({ status: 400, description: 'Bad Request: Invalid data.' })
  @ApiResponse({ status: 401, description: 'Unauthorized access.' })
  @ApiResponse({ status: 403, description: `Forbidden. Only users with roles "${Role.Admin}", "${Role.BUHead}", "${Role.ResourceManager}", "${Role.DeliveryManager}","${Role.TeamLead}", and "${Role.AccountManager}" are permitted to use this endpoint.` })
  async generateTrackerEmailContent(@Req() req: any, @Body() sendTrackerEmailContentDto: SendTrackerEmailContentDto) {
    const { jobApplicationIds } = sendTrackerEmailContentDto;

    if (!Array.isArray(jobApplicationIds) || !jobApplicationIds.length) {
      throw new BadRequestException('jobApplicationIds must be a non-empty array.');
    }

    // const objectIds = jobApplicationIds.map((id) => validateObjectId(id));

    return this.workflowService.generateTrackerEmailContent(req.user, jobApplicationIds);
  }

  @Patch(':jobApplicationId/forms')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.BUHead, Role.TeamLead, Role.DeliveryManager, Role.Recruiter, Role.Vendor, Role.JobSeeker, Role.TeamMember)
  @Roles()
  @ApiOperation({ summary: 'Update an job application by Id', description: `This endpoint updates an job application by Id. This is only accessible for "BUHead", "TeamLead", "DeliveryManager" and "Recruiter".` })
  @ApiResponse({ status: 200, description: 'Job application is updated.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role "BUHead", "TeamLead", "DeliveryManager" and "Recruiter" can only use this end point.' })
  @ApiParam({ name: 'jobApplicationId', description: 'id of the job application.' })
  updateJobAPplicationForm(@Param('jobApplicationId',) jobApplicationId: string, @Body() updatejobApplicationDto: UpdateJobApplicationFormDto) {
    const objId = validateObjectId(jobApplicationId);
    return this.jobApplicationFormService.update(objId, updatejobApplicationDto);
  }

  @Patch(':jobApplicationId/update-stage')
  @ApiParam({ name: 'jobApplicationId', description: `Id of the job application.` })
  @ApiResponse({ status: 200, description: `Job application stage updated.` })
  @ApiResponse({ status: 401, description: `Unauthorized.` })
  @ApiResponse({ status: 403, description: `Forbidden, user with role "BUHead", "TeamLead", "DeliveryManager" and "Recruiter" use this end point.` })
  @ApiQuery({ name: 'stageId', required: true, type: String, description: 'Stage of the job application.' })
  @ApiOperation({ summary: `Update the stage of the job application`, description: `This endpoint update the stage of the job application. This is accessible only for "BUHead", "TeamLead", "DeliveryManager" and "Recruiter".` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.BUHead, Role.DeliveryManager, Role.ResourceManager, Role.AccountManager, Role.TeamLead)
  @Roles()
  updateStage(@Req() req: any, @Param('jobApplicationId') jobApplicationId: string, @Query('stageId') stageId: string) {
    // this.logger.log(stageId)
    // this.logger.log(jobApplicationId)
    const objId = validateObjectId(jobApplicationId);
    const sanitizedStageId = validateObjectId(stageId);
    return this.jobApplicationFormService.updateStage(objId, sanitizedStageId, req.user);
  }

  @Patch('/reject')
  @ApiResponse({ status: 200, description: 'Interview rejection processed successfully.' })
  @ApiResponse({ status: 404, description: 'Job application not found.' })
  @ApiResponse({ status: 401, description: 'Unauthorized access.' })
  @ApiResponse({ status: 403, description: 'Forbidden only user with "BUHead", "ResourceManager", "DeliveryManager", "TeamLead", "AccountManager", "Recuriter", and "TeamMember" can access this endpoint.' })
  @ApiOperation({
    summary: 'Reject an interview',
    description: 'This endpoint allows you to reject an interview for a given job application. This is Accessible only for  "BUHead", "ResourceManager", "DeliveryManager", "TeamLead", and "AccountManager" .'
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager, Role.Recruiter, Role.TeamMember)
  @Roles()
  rejectInterview(
    @Req() req: any,
    @Query('jobApplicationId') jobApplicationId: string,
  ) {
    const objId = validateObjectId(jobApplicationId);
    if (!objId) {
      throw new BadRequestException('Job ID is required to process this request.');
    }
    return this.interviewService.rejectInterview(req.user, jobApplicationId);
  }

  @Patch(':jobApplicationId/update-status')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.BUHead, Role.TeamLead, Role.DeliveryManager, Role.Recruiter, Role.Vendor, Role.JobSeeker, Role.JobSeeker)
  @Roles()
  @ApiOperation({ summary: 'Update an job application status by Id', description: `This endpoint updates an job application status by Id. This is only accessible for "BUHead", "TeamLead", "DeliveryManager" and "Recruiter".` })
  @ApiResponse({ status: 200, description: 'Job application status is updated.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role "BUHead", "TeamLead", "DeliveryManager" and "Recruiter" can only use this end point.' })
  @ApiParam({ name: 'jobApplicationId', description: 'id of the job application.' })
  // @ApiParam({ name: 'jobId',type: String, description: 'Id of the job' })
  updateStatus(@Param('jobApplicationId',) jobApplicationId: string, @Body() updateJobApplicationStatusDto: UpdateJobApplicationStatusDto) {
    const objId = validateObjectId(jobApplicationId);
    return this.jobApplicationFormService.updateStatus(objId, updateJobApplicationStatusDto);
  }
}
