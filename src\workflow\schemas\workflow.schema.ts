import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Stage } from 'aws-sdk/clients/apigateway';
import { HydratedDocument, Types } from 'mongoose';
import { BusinessUnit } from 'src/business-unit/schemas/business-unit.schema';
import { Org } from 'src/org/schemas/org.schema';

export type WorkflowDocument = HydratedDocument<Workflow>;

@Schema({
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true },

})
export class Workflow {

  @Prop({
    type: String,
    required: true,
    trim: true
  })
  name: string;

  @Prop({
    type: [Types.ObjectId],
    required: true,
    ref: 'Stage'
  })
  stages: Types.ObjectId[];

  @Prop({
    type: Types.ObjectId,
    ref: 'BusinessUnit',
    required: false
  })
  businessUnitId?: BusinessUnit;

  @Prop({
    type: Boolean,
    required: false,
    default: false
  })
  isDefault?: boolean;

  @Prop({
    type: Boolean,
    required: false,
    default: false
  })
  isGlobal?: boolean;

  @Prop({
    type: Boolean,
    required: false,
    default: false
  })
  isActive?: boolean;

  @Prop({
    type: Types.ObjectId,
    ref: 'Job',
    required: false
  }) // ✅ Optional Job reference
  job?: Types.ObjectId;

  // @Prop({

  //   type: Boolean,
  //   required: false,
  //   default: false
  // })
  // isJobSpecific?: boolean;

  @Prop({
    type: Types.ObjectId,
    ref: 'Org',
    required: true
  })
  org: Org;

}

export const WorkflowSchema = SchemaFactory.createForClass(Workflow);


WorkflowSchema.virtual('totalJobApplicationsCount').get(function (this: WorkflowDocument) {
  if (this.stages && Array.isArray(this.stages)) {
    return this.stages.reduce((sum: number, stage: any) => sum + (stage.jobApplicationsCount || 0), 0);
  }
  return 0;
});
