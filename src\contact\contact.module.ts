import { Modu<PERSON> } from '@nestjs/common';
import { ContactService } from './contact.service';
import { ContactController } from './contact.controller';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { UserModule } from 'src/user/user.module';
import { JwtModule } from '@nestjs/jwt';
import { AuthModule } from 'src/auth/auth.module';
import { ContactSchema, Contact } from './schemas/contact.schema';
import { MongooseModule, getConnectionToken } from '@nestjs/mongoose';
import { CommonModule } from 'src/common/common.module';
import { EndpointsRolesModule } from 'src/endpoints-roles/endpoints-roles.module';
const autoIncrementFactory = require('mongoose-sequence');

@Module({
  imports: [
    ConfigModule,
    UserModule,
    JwtModule, EndpointsRolesModule,
    AuthModule,
    CommonModule,
    // MongooseModule.forFeature([
    //   { name: Contact.name, schema: ContactSchema },
    // ])
    MongooseModule.forFeatureAsync([
      {
        name: Contact.name,
        imports: [ConfigModule],
        useFactory: (configService: ConfigService) => {
          const autoIncrement = autoIncrementFactory(configService);
          const schema = ContactSchema;
          schema.plugin(autoIncrement, {
            inc_field: 'contactCode',
            id: 'contact_sequence',
            start_seq: 1,
            reference_fields: [ ]
          });
          return schema;
        },
        inject: [getConnectionToken(),ConfigService]
      }
    ])
  ],
  controllers: [ContactController],
  providers: [ContactService],
})
export class ContactModule {}
