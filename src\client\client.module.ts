import { Module } from '@nestjs/common';
import { ClientService } from './client.service';
import { ClientController } from './client.controller';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { PipesModule } from 'src/pipes/pipes.module';
import { JwtModule } from '@nestjs/jwt';
import { UserModule } from 'src/user/user.module';
import { CountryModule } from 'src/country/country.module';
import { MongooseModule, getConnectionToken } from '@nestjs/mongoose';
import { Client, ClientSchema } from './schemas/client.schema';
import { EndpointsRolesModule } from 'src/endpoints-roles/endpoints-roles.module';

@Module({
  imports: [
    ConfigModule,
    PipesModule,
    JwtModule, EndpointsRolesModule,
    UserModule,
    CountryModule,
    // MongooseModule.forFeature([{ name: Account.name, schema: AccountSchema }])
    MongooseModule.forFeatureAsync([
      {
        name: Client.name,
        imports: [ConfigModule],
        useFactory: (configService: ConfigService) => {
          const autoIncrement = AutoIncrementFactory(configService);
          const schema = ClientSchema;
          schema.plugin(autoIncrement, {
            inc_field: 'clientCode',
            id: 'client_sequence',
            start_seq: 1,
            reference_fields: []
          });

          return schema;
        },
        inject: [getConnectionToken(), ConfigService],

      },
    ]),
  ],
  controllers: [ClientController],
  providers: [ClientService],
})
export class ClientModule {}
 
const AutoIncrementFactory = require('mongoose-sequence');

