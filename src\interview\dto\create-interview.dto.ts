import { ApiProperty } from "@nestjs/swagger";
import { Transform, TransformFnParams } from "class-transformer";
import { IsArray, IsBoolean, IsEnum, IsISO8601, IsMongoId, IsNotEmpty, IsOptional, IsString, IsUrl, Length } from "class-validator";
import { sanitizeWithStyle } from "src/utils/sanitize-with-style";
import { Platform, ScreeningType } from "src/shared/constants";

export class CreateInterviewDto {

    @ApiProperty({
        type: String,
        required: true,
    })
    @IsNotEmpty()
    @IsMongoId()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    jobApplication: string;

    @ApiProperty({
        type: Date,
        required: true,
        description: 'Interview date',
        default: new Date(Date.UTC(new Date().getUTCFullYear(), new Date().getUTCMonth(), new Date().getUTCDate(), new Date().getUTCHours(), new Date().getUTCMinutes())),
    })
    // YYYY-MM-DDThh:mm:ssZ - example : 2024-05-27T00:00:00.000Z
    @IsISO8601({ strict: true })
    @Length(10, 24)
    interviewDate: Date;

    @ApiProperty({
        type: [String],
        required: false,
        description: 'Technical panel members',
    })
    @IsOptional()
    @IsString({ each: true })
    @IsArray()
    @Transform((params: TransformFnParams) => params.value.map((value: string) => sanitizeWithStyle(value)))
    technicalPanel?: string[];

    @ApiProperty({
        type: [String],
        required: false,
        description: 'Other participants',
    })
    @IsString({ each: true })
    @IsArray()
    @IsOptional()
    @Transform((params: TransformFnParams) => params.value.map((value: string) => sanitizeWithStyle(value)))
    others?: string[];

    @ApiProperty({
        type: String,
        required: true,
        default: ScreeningType.PHONE_SCREENING,
        enum: ScreeningType,
        description: 'Screening type',
    })
    @IsEnum(ScreeningType)
    @IsString()
    @IsNotEmpty()
    screeningType: ScreeningType;

    @ApiProperty({
        type: String,
        required: false,
        default: Platform.GoogleMeet,
        enum: Platform,
        description: 'Select a platform for video screening',
    })
    @IsEnum(Platform)
    @IsString()
    @IsOptional()
    platform?: Platform;

    @ApiProperty({
        type: String,
        required: false,
    })
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    @IsString()
    @IsOptional()
    @IsUrl()
    meetingUrl?: string;

    @ApiProperty({
        type: String,
        required: false,
        description: 'Meeting Id',
    })
    @IsString()
    @IsOptional()
    meetingId?: string;

    @ApiProperty({
        type: String,
        required: false,
        description: 'Meeting code',
    })
    @IsOptional()
    @IsString()
    meetingCode?: string;

    @ApiProperty({
        type: String,
        required: false,
        description: 'Single Point of Contact (SPOC)',
    })
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    @IsString()
    @IsOptional()
    spoc?: string;

    @ApiProperty({
        type: String,
        required: false,
        description: 'SPOC Phone Number',
    })
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    @IsString()
    @IsOptional()
    spocPhoneNumber?: string;

    @ApiProperty({
        type: String,
        required: false,
        description: 'Company Address',
    })
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    @IsString()
    @IsOptional()
    companyAddress?: string;

    @ApiProperty({
        required: false,
        default: false
    })
    @IsBoolean()
    @IsOptional()
    isRescheduled?: boolean;


    @ApiProperty({
        required: false,
        default: false
    })
    @IsBoolean()
    @IsOptional()
    isCandidateAttended?: boolean;
}