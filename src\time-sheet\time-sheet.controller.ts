import { Controller, Get, Post, Body, Patch, Param, Delete, Req, UseGuards, Logger, NotFoundException, Query, BadRequestException } from '@nestjs/common';

import { CreateTimeSheetDto, TimesheetType } from './dto/create-time-sheet.dto';
import { UpdateTimeSheetDto } from './dto/update-time-sheet.dto';
import { TimeSheetService } from './time-sheet.service';
import { ApiBearerAuth, ApiBody, ApiOperation, ApiParam, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { AuthJwtGuard } from 'src/auth/guards/auth-jwt/auth-jwt.guard';
import { SaveTimeSheetSettingsDto } from './dto/save-timeshhet-settings.dto';
import { SubmitTimesheetDto } from './dto/submit-timesheet.dto';
import { ApproveTimesheetDto } from './dto/approve-timesheet.dto';
import { RejectTimesheetDto } from './dto/reject-timesheet.dto';
import { HrUpdateTimeSheetDto } from './dto/hr-update-timesheet.dto';
import { Invoice } from 'src/invoices/schemas/invoice.schema';

@Controller()
@ApiTags('Timesheets')
export class TimeSheetController {


  constructor(private readonly timesheetService: TimeSheetService) { }

  private readonly logger = new Logger(TimeSheetController.name);


  /* ------------------------------------------------------------------ */
  /* 📥 Create                                                          */
  /* ------------------------------------------------------------------ */
  @Post()
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  @ApiOperation({ summary: 'Create a new timesheet (Daily / Weekly / Monthly)' })
  @ApiResponse({ status: 201, description: 'Timesheet created.' })
  create(
    @Req() req: any,
    @Body() createDto: CreateTimeSheetDto,
  ) {
    // createDto.employee = req.user;          // 🔐 current logged‑in user
    return this.timesheetService.create(req.user, createDto);
  }

  /* ------------------------------------------------------------------ */
  /* 📄 List                                                            */
  /* ------------------------------------------------------------------ */
  @Get()
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  @ApiOperation({ summary: 'Get all timesheets for current user' })
  @ApiQuery({ name: 'date', required: false, description: 'Filter daily timesheets by specific date (YYYY-MM-DD)' }) // ✅ Add this

  findAll(@Req() req: any,
    @Query('date') date?: string) {
    console.log("requested user Id", req.user._id);
    return this.timesheetService.findAllByEmployee(req.user._id, date);
  }

  /* ------------------------------------------------------------------ */
  /* 🔍 Get by ID                                                       */
  /* ------------------------------------------------------------------ */
  @Get(':id')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  @ApiParam({ name: 'id', description: 'Timesheet ID' })
  @ApiOperation({ summary: 'Get a specific timesheet' })
  findOne(@Param('id') id: string) {
    return this.timesheetService.findOne(id);
  }

  /* ------------------------------------------------------------------ */
  /* ✏️ Update                                                          */
  /* ------------------------------------------------------------------ */
  @Patch(':id')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  @ApiParam({ name: 'id', description: 'Timesheet ID' })
  @ApiOperation({ summary: 'Update a timesheet' })
  update(
    @Param('id') id: string,
    @Body() updateDto: UpdateTimeSheetDto,
  ) {
    return this.timesheetService.update(id, updateDto);
  }

  /* ------------------------------------------------------------------ */
  /* 🗑️ Delete                                                          */
  /* ------------------------------------------------------------------ */
  @Delete(':id')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  @ApiParam({ name: 'id', description: 'Timesheet ID' })
  @ApiOperation({ summary: 'Delete a timesheet' })
  remove(@Param('id') id: string) {
    return this.timesheetService.remove(id);
  }

  /* ------------------------------------------------------------------ */
  /* 📊 Quick totals (optional)                                         */
  /* ------------------------------------------------------------------ */
  @Get(':id/totals')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  @ApiParam({ name: 'id', description: 'Timesheet ID' })
  @ApiOperation({ summary: 'Compute grand totals (billable + overtime) for this timesheet' })
  async totals(@Param('id') id: string) {
    const ts = await this.timesheetService.findOne(id);
    if (!ts) throw new NotFoundException('Timesheet not found');
    return this.timesheetService.computeTotals(ts);
  }

  @Post('settings')
  @ApiOperation({ summary: 'Create a new settings for timesheet' })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  async saveSettings(@Req() req: any, @Body() body: SaveTimeSheetSettingsDto) {
    const orgId = req.user?.org?._id;
    const { projectId, taskId, ...settings } = body;
    return this.timesheetService.saveSettings(orgId, projectId ?? null, taskId ?? null, settings);
  }

  @Patch('settings/:id')
  @ApiOperation({ summary: 'Update timesheet settings' })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  @ApiBody({ type: SaveTimeSheetSettingsDto })
  async updateSettings(
    @Param('id') id: string,
    @Body() body: Partial<SaveTimeSheetSettingsDto> // ✅ allows partial updates
  ) {
    return this.timesheetService.updateSettingsById(id, body);
  }

  @Get('settings')
  @ApiOperation({ summary: 'Get timesheet settings of this org' })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  @ApiQuery({ name: 'projectId', required: false }) // 👈 explicitly mark as optional
  @ApiQuery({ name: 'taskId', required: false }) // 👈 explicitly mark as optional

  async getSettings(@Req() req: any, @Query('projectId') projectId?: string, @Query('taskId') taskId?: string) {
    const orgId = req.user?.org?._id;
    return this.timesheetService.getSettings(orgId, projectId || null, taskId || null);
  }


  @Get('Settingspreview')
  @ApiOperation({ summary: 'Get auto-filled timesheet preview using settings' })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  @ApiQuery({ name: 'projectId', required: false, type: [String], isArray: true })
  @ApiQuery({ name: 'taskId', required: false, type: [String], isArray: true })
  @ApiQuery({ name: 'month', required: false })
  async previewSettings(@Req() req: any, @Query('timesheetType') type: TimesheetType,
    @Query('taskId') taskId?: string | string[],
    @Query('projectId') projectId?: string | string[],
    @Query('month') month?: string) {
    const orgId = req.user?.org?._id;
    return this.timesheetService.getTimesheetPreviewOnSettings(type, orgId, req.user._id, projectId ?? null, taskId ?? null, month);
  }

  @Get('time-org/all')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  @ApiOperation({ summary: 'Get all timesheets for the organization' })
  @ApiQuery({ name: 'timesheetType', required: false, enum: ['Daily', 'Weekly', 'Monthly'] })
  @ApiQuery({ name: 'status', required: false, enum: ['Pending', 'Approved', 'Rejected', 'Submitted', 'Draft'] })
  @ApiQuery({ name: 'month', required: false, description: 'Filter by month (YYYY-MM format)' })
  @ApiQuery({ name: 'employeeId', required: false, description: 'Filter by specific employee' })
  @ApiQuery({ name: 'projectId', required: false, description: 'Filter by specific project' })
    @ApiQuery({ name: 'taskId', required: false, description: 'Filter by specific project' })
  @ApiQuery({ name: 'startDate', required: false, description: 'Start date for date range filter' })
  @ApiQuery({ name: 'endDate', required: false, description: 'End date for date range filter' })
  @ApiQuery({ name: 'date', required: false, description: ' date for date range filter' })
  async findAllOrgTimesheets(
    @Req() req: any,
    @Query('timesheetType') timesheetType?: string,
    @Query('status') status?: string,
    @Query('month') month?: string,
    @Query('employeeId') employeeId?: string,
    @Query('projectId') projectId?: string,
    @Query('taskId') taskId?: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('date') date?: string,
  ) {
    const orgId = req.user?.org?._id;
    if (!orgId) {
      throw new BadRequestException('User organization not found');
    }

    const filters: any = {};
    if (timesheetType) filters.timesheetType = timesheetType;
    if (status) filters.status = status;
    if (month) filters.month = month;
    if (employeeId) filters.employeeId = employeeId;
    if (projectId) filters.projectId = projectId;
    if (taskId) filters.taskId = taskId;
    if (startDate && endDate) {
      filters.startDate = new Date(startDate);
      filters.endDate = new Date(endDate);
    }
    if (date) {
      filters.date = new Date(date);
    }

    this.logger.log(`Fetching org timesheets for orgId: ${orgId}`);
    return this.timesheetService.findAllByOrganization(orgId, filters);
  }

  @Get('time-org/paginated')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  @ApiOperation({ summary: 'Get paginated timesheets for the organization' })
  @ApiQuery({ name: 'page', required: false, description: 'Page number (default: 1)' })
  @ApiQuery({ name: 'limit', required: false, description: 'Items per page (default: 10)' })
  @ApiQuery({ name: 'timesheetType', required: false, enum: ['Daily', 'Weekly', 'Monthly'] })
  @ApiQuery({ name: 'status', required: false, enum: ['Pending', 'Approved', 'Rejected', 'Submitted', 'Draft'] })
  @ApiQuery({ name: 'month', required: false, description: 'Filter by month (YYYY-MM format)' })
  @ApiQuery({ name: 'employeeId', required: false, description: 'Filter by specific employee' })
  @ApiQuery({ name: 'projectId', required: false, description: 'Filter by specific project' })
  @ApiQuery({ name: 'taskId', required: false, description: 'Filter by specific task' })
  async findOrgTimesheetsPaginated(
    @Req() req: any,
    @Query('page') page?: string,
    @Query('limit') limit?: string,
    @Query('timesheetType') timesheetType?: string,
    @Query('status') status?: string,
    @Query('month') month?: string,
    @Query('employeeId') employeeId?: string,
    @Query('projectId') projectId?: string,
    @Query('taskId') taskId?: string,
  ) {
    const orgId = req.user?.org?._id;
    if (!orgId) {
      throw new BadRequestException('User organization not found');
    }

    const pageNum = page ? parseInt(page) : 1;
    const limitNum = limit ? parseInt(limit) : 10;

    const filters: any = {};
    if (timesheetType) filters.timesheetType = timesheetType;
    if (status) filters.status = status;
    if (month) filters.month = month;
    if (employeeId) filters.employeeId = employeeId;
    if (projectId) filters.projectId = projectId;
    if (taskId) filters.taskId = taskId;

    return this.timesheetService.findOrgTimesheetsWithPagination(
      orgId,
      pageNum,
      limitNum,
      filters
    );
  }

  @Get('time-org/stats')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  @ApiOperation({ summary: 'Get organization timesheet statistics' })
  @ApiQuery({ name: 'month', required: false, description: 'Filter stats by month (YYYY-MM format)' })
  async getOrgStats(
    @Req() req: any,
    @Query('month') month?: string,
  ) {
    const orgId = req.user?.org?._id;
    if (!orgId) {
      throw new BadRequestException('User organization not found');
    }

    return this.timesheetService.getOrgTimesheetStats(orgId, month);
  }
  /* 📋 Approval Flow Endpoints                                        */
  /* ------------------------------------------------------------------ */

  @Patch(':id/submit')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  @ApiParam({ name: 'id', description: 'Timesheet ID' })
  @ApiOperation({ summary: 'Submit timesheet for approval' })
  @ApiBody({ type: SubmitTimesheetDto, required: false })
  @ApiResponse({ status: 200, description: 'Timesheet submitted successfully.' })
  @ApiResponse({ status: 404, description: 'Timesheet not found.' })
  @ApiResponse({ status: 400, description: 'Timesheet cannot be submitted.' })
  async submitTimesheet(
    @Param('id') id: string,
    @Req() req: any,
    @Body() body: SubmitTimesheetDto = {}
  ) {
    return this.timesheetService.submitTimesheet(id, req.user._id, body.comments);
  }

  @Patch(':id/approve')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  @ApiParam({ name: 'id', description: 'Timesheet ID' })
  @ApiOperation({ summary: 'Approve timesheet (HR/Manager only)' })
  @ApiBody({ type: ApproveTimesheetDto })
  @ApiResponse({ status: 200, description: 'Timesheet approved successfully.' })
  @ApiResponse({ status: 404, description: 'Timesheet not found.' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions.' })
  async approveTimesheet(
    @Param('id') id: string,
    @Req() req: any,
    @Body() body: ApproveTimesheetDto
  ) {
    return this.timesheetService.approveTimesheet(
      id,
      req.user._id,
      body.comments,
      body.approvedHours
    );
  }

  @Patch(':id/reject')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  @ApiParam({ name: 'id', description: 'Timesheet ID' })
  @ApiOperation({ summary: 'Reject timesheet (HR/Manager only)' })
  @ApiBody({ type: RejectTimesheetDto })
  @ApiResponse({ status: 200, description: 'Timesheet approved successfully.' })
  @ApiResponse({ status: 404, description: 'Timesheet not found.' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions.' })
  async rejectTimesheet(
    @Param('id') id: string,
    @Req() req: any,
    @Body() body: RejectTimesheetDto
  ) {
    return this.timesheetService.rejectTimesheet(
      id,
      req.user._id,
      body.reason,
      body.comments
    );
  }




  @Get('pending-approval')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  @ApiOperation({ summary: 'Get all timesheets pending approval (HR/Manager view)' })
  @ApiQuery({ name: 'page', required: false, description: 'Page number (default: 1)' })
  @ApiQuery({ name: 'limit', required: false, description: 'Items per page (default: 10)' })
  @ApiQuery({ name: 'timesheetType', required: false, enum: ['Daily', 'Weekly', 'Monthly'] })
  @ApiQuery({ name: 'employeeId', required: false, description: 'Filter by specific employee' })
  @ApiQuery({ name: 'projectId', required: false, description: 'Filter by specific project' })
    @ApiQuery({ name: 'projectId', required: false, description: 'Filter by specific project' })
  async getPendingApprovals(
    @Req() req: any,
    @Query('page') page?: string,
    @Query('limit') limit?: string,
    @Query('timesheetType') timesheetType?: string,
    @Query('employeeId') employeeId?: string,
    @Query('projectId') projectId?: string,
     @Query('taskId') taskId?: string,
  ) {
    const orgId = req.user?.org?._id;
    if (!orgId) {
      throw new BadRequestException('User organization not found');
    }

    const pageNum = page ? parseInt(page) : 1;
    const limitNum = limit ? parseInt(limit) : 10;

    const filters: any = { status: 'Submitted' }; // Only submitted timesheets
    if (timesheetType) filters.timesheetType = timesheetType;
    if (employeeId) filters.employeeId = employeeId;
    if (projectId) filters.projectId = projectId;
    if (taskId) filters.taskId = taskId;


    return this.timesheetService.findOrgTimesheetsWithPagination(
      orgId,
      pageNum,
      limitNum,
      filters
    );
  }

  @Get('approval-history')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  @ApiOperation({ summary: 'Get timesheet approval history' })
  @ApiQuery({ name: 'page', required: false, description: 'Page number (default: 1)' })
  @ApiQuery({ name: 'limit', required: false, description: 'Items per page (default: 10)' })
  @ApiQuery({ name: 'status', required: false, enum: ['Approved', 'Rejected'] })
  @ApiQuery({ name: 'employeeId', required: false, description: 'Filter by specific employee' })
  async getApprovalHistory(
    @Req() req: any,
    @Query('page') page?: string,
    @Query('limit') limit?: string,
    @Query('status') status?: string,
    @Query('employeeId') employeeId?: string,
  ) {
    const orgId = req.user?.org?._id;
    if (!orgId) {
      throw new BadRequestException('User organization not found');
    }

    const pageNum = page ? parseInt(page) : 1;
    const limitNum = limit ? parseInt(limit) : 10;

    const filters: any = {};

    // Only show approved or rejected timesheets
    if (status && ['Approved', 'Rejected'].includes(status)) {
      filters.status = status;
    } else {
      filters.status = { $in: ['Approved', 'Rejected'] };
    }

    if (employeeId) filters.employeeId = employeeId;

    return this.timesheetService.findOrgTimesheetsWithPagination(
      orgId,
      pageNum,
      limitNum,
      filters
    );
  }

  @Get('my-submissions')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  @ApiOperation({ summary: 'Get current user submitted timesheets with status' })
  @ApiQuery({ name: 'status', required: false, enum: ['Submitted', 'Approved', 'Rejected'] })
  async getMySubmissions(
    @Req() req: any,
    @Query('status') status?: string,
  ) {
    const filters: any = {};
    if (status) {
      filters.status = status;
    } else {
      // Show all submitted, approved, or rejected (not draft)
      filters.status = { $in: ['Submitted', 'Approved', 'Rejected'] };
    }

    return this.timesheetService.findAllByEmployeeWithFilters(req.user._id, filters);
  }

  @Patch(':id/recall')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  @ApiParam({ name: 'id', description: 'Timesheet ID' })
  @ApiOperation({ summary: 'Recall submitted timesheet (back to draft)' })
  @ApiResponse({ status: 200, description: 'Timesheet recalled successfully.' })
  @ApiResponse({ status: 404, description: 'Timesheet not found.' })
  @ApiResponse({ status: 400, description: 'Timesheet cannot be recalled.' })
  async recallTimesheet(
    @Param('id') id: string,
    @Req() req: any,
    @Body() body?: { reason?: string }
  ) {
    return this.timesheetService.recallTimesheet(id, req.user._id, body?.reason);
  }

  @Patch(':id/hr-update')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  @ApiBody({ type: HrUpdateTimeSheetDto })
  async hrUpdateTimesheet(
    @Param('id') id: string,
    @Body() body: HrUpdateTimeSheetDto
  ) {
    return this.timesheetService.partialUpdate(id, body);
  }


  @Post(':id/generate-invoice')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Generate invoice from approved timesheet' })
  @UseGuards(AuthJwtGuard)
  async generateInvoiceFromTimesheet(
    @Param('id') timesheetId: string,
    @Req() req: any,
  ) {
    const userId = req.user._id;
    const orgId = req.user.org._id;
    return this.timesheetService.generateInvoice(timesheetId, userId, orgId);
  }



  @Post(':id/generate-invoice-timesheet')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Generate invoice from a timesheet (monthly or specific week)' })
  @UseGuards(AuthJwtGuard)
  @ApiQuery({ name: 'weekLabel', required: false, description: 'week label to invoice' })
  async generateInvoiceTimesheet(
    @Req() req: any,
    @Param('id') timesheetId: string,
    @Query('weekLabel') weekLabel?: string, // Optional query parameter
  ): Promise<Invoice> {
    const userId = req.user._id;
    const orgId = req.user.org._id;
    return this.timesheetService.generateInvoiceFromTimesheet(timesheetId, userId, orgId, { weekLabel });
  }




}