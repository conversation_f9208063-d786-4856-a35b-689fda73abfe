import { ApiHideProperty, ApiProperty } from "@nestjs/swagger";
import {
  IsMongoId,
  IsNotEmpty,
  IsOptional,
  IsString,
  MaxLength,
  IsNumber,
  Min,
  IsObject,
  IsArray,
  ValidateNested,
} from "class-validator";
import { Transform, TransformFnParams, Type } from "class-transformer";
import { sanitizeWithStyle } from "src/utils/sanitize-with-style";
import { RateCardOtherFieldDto } from "./rate-card-other-fields.dto";

export class CreateRateCardDto {
  @ApiProperty({
    type: String,
    required: true,
    description: 'ID of the rate card category',
  })
  @IsNotEmpty()
  @IsMongoId()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  category: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Name of the rate card',
  })
  @IsNotEmpty()
  @IsString()
  // @MaxLength(200)
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  name: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Description of the rate card',
  })
  @IsOptional()
  @IsString()
  // @MaxLength(500)
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  description?: string;

  @ApiProperty({
    type: Number,
    required: false,
    description: 'Fixed rate value (optional)',
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  fixedRate?: number;

  @ApiProperty({
    type: Number,
    required: false,
    description: 'Percentage of CTC value (optional)',
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  percentageOfCTC?: number;

  @ApiHideProperty()
  @IsOptional()
  @IsMongoId()
  org?: string;

  @ApiHideProperty()
  @IsOptional()
  @IsMongoId()
  createdBy?: string;

  @ApiProperty({
    description: 'Dynamic fields associated with the rate card',
    type: [RateCardOtherFieldDto],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => RateCardOtherFieldDto)
  other?: RateCardOtherFieldDto[];
}
