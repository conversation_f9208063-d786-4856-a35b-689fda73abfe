import { IsOptional, IsEnum, IsString, <PERSON>Array } from 'class-validator';
import { Type } from 'class-transformer';
import { Types } from 'mongoose';

export class QueryProjectDto {
  @IsOptional()
  @IsString()
  @Type(() => String)
  projectType?: string;

  @IsOptional()
//   @Type(() => Types.ObjectId)
  client?: string;

  @IsOptional()
//   @Type(() => Types.ObjectId)
  orgId?: string;

  @IsOptional()
  @IsString()
  department?: string;

  @IsOptional()
  @Type(() => Date)
  startDate?: Date;

  @IsOptional()
  @Type(() => Date)
  endDate?: Date;
}
