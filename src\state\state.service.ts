import { BadRequestException, HttpException, HttpStatus, Injectable, InternalServerErrorException, Logger, NotFoundException } from '@nestjs/common';
import { CreateStateDto } from './dto/create-state.dto';
import { UpdateStateDto } from './dto/update-state.dto';
import { ConfigService } from '@nestjs/config';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { State, StateDocument } from './schemas/state.schema';
import { CountryService } from 'src/country/country.service';
import { City } from './schemas/city.schema';

const countryStatesData = require('../public/data/country-states/country-states.json')
// console.log(countryStatesData);

@Injectable()
export class StateService {

  private readonly logger = new Logger(StateService.name);

  constructor(private configService: ConfigService, @InjectModel(State.name) private stateModel: Model<State>, private countryService: CountryService, @InjectModel(City.name) private cityModel: Model<City>
  ) { }

  async seedStatesFromFile(countryId: Types.ObjectId) {
    try {

      const country = await this.countryService.findById(countryId);

      const countryStates = countryStatesData.find((c: { name: any; }) => c.name === country.countryName)?.states
      if (!countryStates) {
        throw new HttpException(
          `States data not found for country: ${country.countryName}`,
          HttpStatus.NOT_FOUND,
        );
      }
      // const filePath = path.resolve(__dirname, '../public/data/states/usa.json');
      // const rawData = await readFile(filePath, 'utf8');
      // const statesData = JSON.parse(rawData);

      const existingStates = await this.stateModel.find({ country: countryId });

      if (existingStates.length > 0) {
        throw new HttpException(
          `States for country ${country.countryName} have already been added.`,
          HttpStatus.CONFLICT,
        );
      }

      // Prepare states to insert
      const statesToInsert = [
        { stateName: 'ALL', country: countryId, isDeleted: false },
        ...countryStates.map((state: any) => ({
          stateName: state.name,
          country: countryId,
          isDeleted: false
        })),
      ];

      // Mongoose bulkWrite for better performance
      const result = await this.stateModel.bulkWrite(
        statesToInsert.map((doc: any) => ({
          insertOne: { document: doc },
        })),
      );

      return {
        statusCode: HttpStatus.CREATED,
        message: `Successfully seeded ${result.insertedCount} states for country with ID ${countryId}.`,
        data: result
      };
    } catch (error) {
      console.error('Error seeding states:', error);
      throw error; // Handle or log the error as needed
    }
  }

  async create(createStateDto: CreateStateDto, countryId: Types.ObjectId) {
    const { stateId, stateName } = createStateDto;
    try {
      const existingState = await this.stateModel.findOne({
        $or: [
          { stateName: stateName.trim() },
          { stateId: stateId }
        ]
      });

      if (existingState) {
        this.logger.log(existingState);
        throw new BadRequestException("State with the provided name or Id already exists.");
      }

      const country = await this.countryService.findById(countryId)

      if (!country) {
        throw new NotFoundException('Country not found with exsisting country Id');
      }

      const stateData = {
        ...createStateDto,
        country: country.countryId
      }

      return await new this.stateModel(stateData).save();
    } catch (error) {
      throw new InternalServerErrorException(`Error while creating state: ${error?.message}`);
    }
  }

  async findAll(countryId: Types.ObjectId): Promise<StateDocument[]> {
    try {
      const country = await this.countryService.findById(countryId);

      if (!country) {
        throw new NotFoundException('Country not found with existing country Id');
      }

      return this.stateModel.find({ isDeleted: false, country: country.countryId }).exec();
    } catch (error) {
      this.logger.error(`Error while fetching states for country ID ${countryId}: ${error?.message}`, error?.stack);

      if (error instanceof NotFoundException) {
        throw error; // Rethrow specific errors if needed
      } else {
        throw new InternalServerErrorException('An unexpected error occurred while fetching states.');
      }
    }
  }

  // if admin wants to see list of records include deleting records we need one more function
  async getAllStates(countryId: Types.ObjectId): Promise<StateDocument[]> {
    try {
      const country = await this.countryService.findById(countryId);

      if (!country) {
        throw new NotFoundException('Country not found with existing country Id');
      }

      return this.stateModel.find({ country: country.countryId }).exec();
    } catch (error) {
      this.logger.error(`Error while fetching states for country ID ${countryId}: ${error?.message}`, error?.stack);

      if (error instanceof NotFoundException) {
        throw error; // Rethrow specific errors if needed
      } else {
        throw new InternalServerErrorException('An unexpected error occurred while fetching states.');
      }
    }
  }

  async getAllSoftDeletedStates(countryId: Types.ObjectId): Promise<StateDocument[]> {
    try {
      const country = await this.countryService.findById(countryId);

      if (!country) {
        throw new NotFoundException('Country not found with existing country Id');
      }

      return this.stateModel.find({ isDeleted: true, country: country.countryId }).exec();
    } catch (error) {
      this.logger.error(`Error while fetching soft-deleted states for country ID ${countryId}: ${error?.message}`, error?.stack);

      if (error instanceof NotFoundException) {
        throw error; // Rethrow specific errors if needed
      } else {
        throw new InternalServerErrorException('An unexpected error occurred while fetching soft-deleted states.');
      }
    }
  }

  async findById(stateId: Types.ObjectId) {
    try {
      const state = await this.stateModel.findById(stateId).exec();
      if (!state || state.isDeleted) {
        throw new NotFoundException(`State not found with ID ${stateId}`);
      }
      return state;
    } catch (error) {
      this.logger.error(`An error occurred while fetching a state by ID ${stateId}. ${error?.message}`);
      throw error;
    }
  }

  async searchByName(name: string, countryId: Types.ObjectId) {
    try {
      const country = await this.countryService.findById(countryId);

      if (!country) {
        throw new NotFoundException('Country not found with existing country Id');
      }

      const regex = new RegExp(name, 'i'); // 'i' for case-insensitive search
      return await this.stateModel.find({ stateName: { $regex: regex }, country: country.countryId }).exec();
    } catch (error) {
      this.logger.error(`Error while searching for states with name '${name}' and country ID ${countryId}: ${error?.message}`, error?.stack);
      if (error instanceof NotFoundException) {
        throw error; // Re-throw NotFoundException as is
      }
      throw new InternalServerErrorException('Error while searching for states. Please try again later.');
    }
  }

  async update(stateId: Types.ObjectId, updateStateDto: UpdateStateDto) {
    try {
      await this.findById(stateId);
      const existingState = await this.stateModel.findOne({
        $or: [
          { stateName: updateStateDto?.stateName?.trim() },
          { stateId: updateStateDto?.stateId }
        ]
      });

      if (existingState) {
        this.logger.log(existingState);
        throw new BadRequestException("State with the provided name or Id already exists.");
      }

      const updatedState = await this.stateModel
        .findByIdAndUpdate(stateId, updateStateDto, { new: true, runValidators: true });
      return updatedState;
    } catch (error) {
      this.logger.error(`An error occurred while updating state by ID ${stateId}. ${error?.message}`);
      throw error;
    }
  }

  async softDelete(stateId: Types.ObjectId) {
    try {
      const state = await this.findById(stateId);
      state.isDeleted = true;
      await state.save();
      return state;
    }
    catch (error) {
      this.logger.error(error);
      this.logger.error(`An error occurred while soft deleting state by ID ${stateId}. ${error?.message}`);
      throw error;
    }
  }

  async restoreSoftDeletedState(stateId: Types.ObjectId) {
    try {
      const state = await this.stateModel.findById(stateId);
      if (!state) {
        throw new NotFoundException(`The state with ID: "${stateId}" doesn't exist.`);
      }
      if (!state.isDeleted) {
        throw new BadRequestException(`The state with ID: "${stateId}" is not soft deleted.`);
      }
      state.isDeleted = false
      await state.save();
      return state
    }
    catch (error) {
      this.logger.error(`An error occurred while restoring State by ID ${stateId}. ${error?.message}`);
      throw error;
    }
  }

  async getCitiesByState(stateId: Types.ObjectId) {
    try {
      const state = await this.stateModel.findById(stateId);
      if (!state) {
        throw new NotFoundException('State not found.');
      }

      return await this.cityModel.find({ stateId: state.stateId });
    } catch (error) {
      throw new InternalServerErrorException(`Error while retrieving cities: ${error?.message}`);
    }
  }

}
