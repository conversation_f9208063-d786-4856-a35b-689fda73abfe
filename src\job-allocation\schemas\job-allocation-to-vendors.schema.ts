import { <PERSON>p, SchemaFactory, Schema } from '@nestjs/mongoose';
import { Types } from 'mongoose';
import { JobAllocationBase, JobAllocationBaseSchema } from './job-allocation-base.schema';
import { Org } from 'src/org/schemas/org.schema';
import { JobAllocationType } from 'src/shared/constants';

@Schema()
export class JobAllocationToVendors extends JobAllocationBase {
    @Prop({ required: true, type: Types.ObjectId, ref: 'Org' })
    vendor: Org;

    @Prop({ required: false, type: [Types.ObjectId], ref: 'Org', default: [] })
    vendors: Org[];
}

export const JobAllocationToVendorsSchema = JobAllocationBaseSchema.discriminator(
    JobAllocationType.VENDORS,
    SchemaFactory.createForClass(JobAllocationToVendors),
);
