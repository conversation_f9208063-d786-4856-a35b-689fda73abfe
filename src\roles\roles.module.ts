import { <PERSON>du<PERSON> } from '@nestjs/common';
import { RolesService } from './roles.service';
import { RolesController } from './roles.controller';
import { JwtModule } from '@nestjs/jwt';
import { MongooseModule } from '@nestjs/mongoose';
import { Roles, RolesSchema } from './schemas/roles.schema';
import { EndpointsRolesModule } from 'src/endpoints-roles/endpoints-roles.module';
@Module({
  providers: [RolesService],
  imports: [JwtModule, EndpointsRolesModule, MongooseModule.forFeature([{ name: Roles.name, schema: RolesSchema }])],
  exports: [RolesService, MongooseModule],
  controllers: [RolesController]
})
export class RolesModule {}
