import { Injectable, Logger } from '@nestjs/common';
import { CreateEmailConfigDto } from './dto/create-email-config.dto';
import { UpdateEmailConfigDto } from './dto/update-email-config.dto';
import { UserInboxConfig } from 'src/user-inbox-config/schemas/user-inbox-config.schema';
import { EmailConfig } from './schemas/email-config.schema';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import * as nodemailer from 'nodemailer';
import { ConfigService } from '@nestjs/config';
import * as ImapSimple from 'imap-simple';
import { EmailTemplate } from 'src/email-template-builder/schemas/email-template-builder.schema';
import { unescape } from 'lodash';
import { JSONPath } from 'jsonpath-plus';
import { Org, Placeholder } from 'src/org/schemas/org.schema';
import { OnEvent } from '@nestjs/event-emitter';
import { CalendarPayload, EmailTemplateEvent, EventPayload, MeetingPayload, TechnicalPanelInvite } from 'src/shared/constants';
import { FileMetadata } from 'src/file-upload/schemas/file-metadata.schema';
import { Buffer } from 'buffer';
import { Role } from 'src/auth/enums/role.enum';

@Injectable()
export class EmailConfigService {
  private readonly logger = new Logger(EmailConfigService.name);
  constructor(private configService: ConfigService,
    @InjectModel(EmailConfig.name) private emailConfigModel: Model<EmailConfig>,
    @InjectModel(UserInboxConfig.name) private userInboxConfigModel: Model<UserInboxConfig>,
    @InjectModel(FileMetadata.name) private fileMetadataModel: Model<FileMetadata>,
    @InjectModel(EmailTemplate.name) private emailTemplateService: Model<EmailTemplate>,
    @InjectModel(Placeholder.name) private placeholderService: Model<Placeholder>,
  ) { }

  async sendEmail(createEmailConfigDto: CreateEmailConfigDto, user: any) {
    // console.log("user", user)
    const { userInboxConfigId, to, cc, bcc, subject, attachments } = createEmailConfigDto;

    // this.logger.log(JSON.stringify(createEmailConfigDto))

    let body = createEmailConfigDto.body ?? ''

    if (!userInboxConfigId) {
      throw new Error('userInboxConfigId is required');  // Handle missing field
    }

    const userInboxConfig = await this.userInboxConfigModel.findById(userInboxConfigId).exec();
    // this.logger.log("after by id:", userInboxConfig);

    if (!userInboxConfig) {
      throw new Error('User Inbox Config not found');
    }

    // // Add user's signature
    // const signature = userInboxConfig.signature && userInboxConfig.signature.trim()
    //   ? `
    //     <div style="font-family:Arial, sans-serif ; font-size: 16px; line-height: 1.6; color: #333;">
    //       <p>Best regards,</p>
    //       <p style="font-size: 16px; font-family:'Brush Script MT', 'Lucida Handwriting', cursive; color: #e58d75;"><em>${userInboxConfig.signature.trim()}</em></p>
    //     </div>`
    //   : `
    //     <div style="font-family: Arial, sans-serif; font-size: 14px; line-height: 1.6; color: #333;">
    //       <p>Best regards,</p>
    //       <p style="color: #e58d75;">${userInboxConfig.fromName}</p>
    //     </div>`;

    // body += `<br><br>${signature}`;

    // Prepare attachments with base64 decoding to binary format
    const attachmentIds: { filename: string; path: string; contentType: string; content: string }[] = [];
    if (attachments && attachments.length > 0) {
      const fileMetadataRecords = await this.fileMetadataModel.find({ _id: { $in: attachments } })
        .select('_id originalName locationUrl fileType')
        .exec();

      for (const file of fileMetadataRecords) {
        if (file.locationUrl) {
          // Fetch the file as a buffer
          const response = await fetch(file.locationUrl);

          // Check if the response is successful
          if (!response.ok) {
            throw new Error(`Failed to fetch attachment: ${file.originalName}`);
          }

          // Convert the response body to an ArrayBuffer and then to a Buffer
          const arrayBuffer = await response.arrayBuffer();
          const buffer = Buffer.from(arrayBuffer);

          // Convert the buffer to base64
          const base64Content = buffer.toString('base64');

          // Push the attachment data with content into the attachment array
          attachmentIds.push({
            filename: file.originalName,
            path: file.locationUrl,
            contentType: file.fileType,
            content: base64Content, // Base64-encoded content
          });
        } else {
          this.logger.error(`Attachment ${file.originalName} has no locationUrl defined`);
        }
      }
    }

    // Fetch the signature template for the user or organization
    const emailTemplate = await this.findEmailTemplate(EmailTemplateEvent.SIGNATURE, user.org);

    let signatureHtml = '';

    // console.log("template",emailTemplate)

    // Generate the signature body with placeholders replaced
    if (emailTemplate) {
      // Use a similar method to `generateEmailBody` to replace placeholders in the signature
      signatureHtml = await this.generateEmailBody(emailTemplate, {
        user: user,  // Pass user data
        data: user.org,  // Pass organization data if needed
      });

    } else {
      // Default signature if no template is found
      signatureHtml = `
    <div style="font-family: Arial, sans-serif; font-size: 14px; color: #333;">
      <p>Best regards,</p>
      <p>${userInboxConfig.fromName}</p>
    </div>`;
    }

    // Append the signature to the email body
    body += `<br><br>${signatureHtml}`;

    // Configure the transporter with the user's SMTP settings
    const transporter = nodemailer.createTransport({
      host: userInboxConfig.smtpHost,
      port: userInboxConfig.smtpPort,
      secure: userInboxConfig.isEnableSSL, // true for 465, false for other ports
      auth: {
        user: userInboxConfig.userName,
        pass: userInboxConfig.password,
      },
    });

    const mailOptions = {
      from: userInboxConfig.fromEmail, // Use user's fromEmail
      to: to.join(','), // Join multiple recipients as comma-separated string
      cc: cc ? cc.join(',') : undefined, // CC (if provided)
      bcc: bcc ? bcc.join(',') : undefined, // BCC (if provided)
      subject: subject,
      text: body.replace(/<\/?[^>]+(>|$)/g, ""),  // Send plain text version (strip HTML)
      html: body,
      attachments: attachmentIds,
    };

    const fullHtmlBody = `<html><head><meta charset="UTF-8"></head><body>${body}</body></html>`;

    try {
      // Send the email
      await transporter.sendMail(mailOptions);

      // Save the email configuration in the database
      const createdEmail = new this.emailConfigModel({
        userInboxConfigId: userInboxConfigId,  // Ensure this matches your schema
        fromEmail: userInboxConfig.fromEmail,
        fromName: userInboxConfig.fromName,
        to,
        cc,
        bcc,
        subject,
        body: fullHtmlBody,
        sentAt: createEmailConfigDto.sentAt ? new Date(createEmailConfigDto.sentAt) : new Date(),
        attachments
      });

      // Populate the 'attachments' field before saving
      const populatedEmail = await createdEmail.populate({
        path: 'attachments',
        model: 'FileMetadata',  // Assuming your attachment model is FileMetadata
        select: '_id originalName locationUrl fileType'  // Specify the fields you want to populate
      }); // Use exec() to resolve the promise

      const savedEmail = await populatedEmail.save();



      // IMAP configuration for appending the email to "Sent" folder
      const config = {
        imap: {
          user: userInboxConfig.userName,
          password: userInboxConfig.password,
          host: userInboxConfig.imapHost,
          port: userInboxConfig.imapPort,
          tls: userInboxConfig.isEnableSSL,
          authTimeout: 3000,
        },
      };

      // Connect to the IMAP server
      const connection = await ImapSimple.connect(config);

      // Construct raw email for appending
      let rawEmail = `From: ${userInboxConfig.fromEmail}\r\n` +
        `To: ${to.join(',')}\r\n` +
        `Cc: ${cc ? cc.join(',') : ''}\r\n` +
        `Bcc: ${bcc ? bcc.join(',') : ''}\r\n` +
        `Subject: ${subject}\r\n` +
        `Date: ${new Date().toUTCString()}\r\n` +
        `MIME-Version: 1.0\r\n` +
        `Content-Type: multipart/mixed; boundary="boundary1"\r\n\r\n` +
        `--boundary1\r\n` +
        `Content-Type: text/html; charset=UTF-8\r\n` +
        `Content-Transfer-Encoding: 7bit\r\n\r\n` +
        `${fullHtmlBody}\r\n\r\n`;

      // Add attachments to the raw email
      attachmentIds.forEach(att => {
        rawEmail += `--boundary1\r\n` +
          `Content-Type: ${att.contentType}; name="${att.filename}"\r\n` +
          `Content-Disposition: attachment; filename="${att.filename}"\r\n` +
          `Content-Transfer-Encoding: base64\r\n\r\n` +
          `${att.content}\r\n\r\n`;
      });

      rawEmail += `--boundary1--`;  // End of multipart

      const mailboxes = await connection.getBoxes();  // Fetch mailboxes

      // List of common "Sent" folder variations
      const sentFolderVariations = ['Sent', 'Sent Mail', 'Sent Items', 'Sent Messages', 'Envoyés', 'Enviados'];

      let sentFolder = null;

      // Check if any of the common variations exist
      for (const variation of sentFolderVariations) {
        if (mailboxes[variation]) {
          sentFolder = variation;
          break;
        }
      }

      // If no folder found, default to "Sent" and create it if necessary
      if (!sentFolder) {
        sentFolder = 'Sent';  // Default to "Sent"
        if (!mailboxes[sentFolder]) {
          await connection.addBox(sentFolder);  // Create the folder if it doesn't exist
          console.log(`Created new Sent folder: ${sentFolder}`);
        }
      }

      // Access the raw IMAP client from the connection
      const imap = connection.imap;

      // Subscribe to the "Sent" folder using raw IMAP commands
      imap.subscribeBox(sentFolder, (err) => {
        if (err) {
          this.logger.error(`Failed to subscribe to folder: ${sentFolder}`, err.message);
        } else {
          this.logger.log(`Successfully subscribed to folder: ${sentFolder}`);
        }
      });
      // Append the email to the "Sent" folder
      await connection.append(rawEmail, {
        mailbox: sentFolder, // The target mailbox (folder)
        flags: ['\\Seen'],  // Mark it as read
      });

      this.logger.log('Email successfully appended to Sent folder');
      // Close IMAP connec
      return savedEmail;
    } catch (error) {
      console.error('Error sending email:', error);
      // throw new Error('Failed to send email');
    }
  }

  @OnEvent(EmailTemplateEvent.WORKFLOW_SOURCING_PRE_CONSENT)
  async sendOfferLetterEmail(payload: EventPayload) {
    await this.processEmailEvent(payload, {
      eventName: EmailTemplateEvent.WORKFLOW_SOURCING_PRE_CONSENT,
      defaultSubject: 'Thank You for Applying for the {jobTitle} Position.',
      logMessage: 'Pre consentment Email',
    });
  }

  @OnEvent(EmailTemplateEvent.WORKFLOW_SCREENING)
  async sendScreeningEmail(payload: EventPayload) {
    await this.processEmailEvent(payload, {
      eventName: EmailTemplateEvent.WORKFLOW_SCREENING,
      defaultSubject: 'Invitation for Screening – {jobTitle} Position',
      logMessage: 'Screening Email',
    });
  }

  @OnEvent(EmailTemplateEvent.WORKFLOW_REJECTION)
  async sendRejectEmail(payload: EventPayload) {
    await this.processEmailEvent(payload, {
      eventName: EmailTemplateEvent.WORKFLOW_REJECTION,
      defaultSubject: 'Rejection Mail – {jobTitle} Position',
      logMessage: 'Rejection Email',
    });
  }

  @OnEvent(EmailTemplateEvent.WORKFLOW_INTERVIEW_AI)
  async userAIInterviewInvite(payload: EventPayload) {
    await this.processEmailEvent(payload, {
      eventName: EmailTemplateEvent.WORKFLOW_INTERVIEW_AI,
      defaultSubject: 'Invitation for AI Interview – {jobTitle} Position',
      logMessage: 'AI Interview Email',
    });
  }


  @OnEvent('meeting.updated')
  async sendUpdatedMeeting(payload: MeetingPayload) {
    try {
      const { user, data } = payload;

      // this.logger.log(user)

      // Validate user inbox configuration
      if (!user.userInboxConfig) {
        throw new Error('User inbox configuration ID is missing.');
      }

      const guestEmails = data?.guests?.filter(email => email) ?? [];

      const technicalPanelEmails = data?.invitees?.filter(email => email) ?? [];

      if (!guestEmails.length) {
        this.logger.log('No guests found, skipping email notification.');
        return;
      }

      const createEmailConfigDto = new CreateEmailConfigDto();
      createEmailConfigDto.userInboxConfigId = user.userInboxConfig._id.toString();

      createEmailConfigDto.to = [...guestEmails, ...technicalPanelEmails];
      createEmailConfigDto.subject = 'Meeting Rescheduled – Updated Details';

      // Fetch the appropriate email template
      const emailTemplate = await this.findEmailTemplate(EmailTemplateEvent.MEETING_RESCHEDULE, user.org);
      if (!emailTemplate) {
        throw new Error(`Email template not found for the ${EmailTemplateEvent.MEETING_RESCHEDULE} event.`);
      }

      // Generate email body with placeholders replaced
      createEmailConfigDto.body = await this.generateEmailBody(emailTemplate, { user, data });

      // this.logger.log(`Triggered ${EmailTemplateEvent.MEETING_RESCHEDULE} event.`);
      // this.logger.log(createEmailConfigDto.body);

      // Send the email
      this.sendEmail(createEmailConfigDto, user);
    } catch (error) {
      this.logger.error(`Error in ${EmailTemplateEvent.MEETING_RESCHEDULE}: ${error.message}`);
      throw new Error(`Error in ${EmailTemplateEvent.MEETING_RESCHEDULE}: ${error.message}`);
    }
  }

  /**
   * Process email events by handling template retrieval, placeholder replacements, and email sending.
   */

  private async processEmailEvent(payload: EventPayload, options: {
    eventName: EmailTemplateEvent;
    defaultSubject: string;
    logMessage: string;
  }) {
    const { eventName, defaultSubject, logMessage } = options;

    try {
      const { user, data, body } = payload;

      // this.logger.log(data)

      // Validate user inbox configuration
      if (!user.userInboxConfig) {
        throw new Error('User inbox configuration ID is missing.');
      }

      const createEmailConfigDto = new CreateEmailConfigDto();
      createEmailConfigDto.userInboxConfigId = user.userInboxConfig._id.toString();
      // createEmailConfigDto.fromEmail = user.email;
      // createEmailConfigDto.fromName = user.firstName ?? 'Candidate';

      // Validate candidate's contact email
      const contactEmail = data.contactDetails?.contactEmail;
      if (!contactEmail) {
        throw new Error('Contact email is missing from job application details.');
      }

      createEmailConfigDto.to = [contactEmail];
      createEmailConfigDto.subject = defaultSubject.replace('{jobTitle}', data.jobId.title);

      if (!body) {
        // Fetch the appropriate email template
        const emailTemplate = await this.findEmailTemplate(eventName, user.org);
        if (!emailTemplate) {
          throw new Error(`Email template not found for the ${logMessage} event.`);
        }

        // Generate email body with placeholders replaced
        createEmailConfigDto.body = await this.generateEmailBody(emailTemplate, { user, data });
      } else {
        createEmailConfigDto.body = body;
      }

      this.logger.log(`Triggered ${eventName} event for ${logMessage}.`);
      // this.logger.log(createEmailConfigDto.body);

      // Send the email
      this.sendEmail(createEmailConfigDto, user);
    } catch (error) {
      this.logger.error(`Error in ${logMessage}: ${error.message}`);
      throw new Error(`Error in ${logMessage}: ${error.message}`);
    }
  }

  /**
   * Find the appropriate email template for the given event and organization.//
   */
  private async findEmailTemplate(eventName: EmailTemplateEvent, org?: any) {
    let emailTemplate = null;

    if (org) {
      emailTemplate = await this.emailTemplateService
        .findOne({ eventName, isDefault: true,org: org._id,isDeleted: false })
        .exec();
    }

    if (!emailTemplate) {
      emailTemplate = await this.emailTemplateService
        .findOne({ eventName, canDelete: false,org: org._id,isDeleted: false })
        .exec();
    }

    return emailTemplate;
  }

  
  // private async processEmailEvent(payload: EventPayload, options: {
  //   eventName: EmailTemplateEvent;
  //   defaultSubject: string;
  //   logMessage: string;
  // }) {
  //   const { eventName, defaultSubject, logMessage } = options;

  //   try {
  //     const { user, data } = payload;

  //     // this.logger.log(data)

  //     // Validate user inbox configuration
  //     console.log("user in process email", user)
  //     if (!user.userInboxConfig) {
  //       throw new Error('User inbox configuration ID is missing.');
  //     }

  //     const createEmailConfigDto = new CreateEmailConfigDto();

  //     createEmailConfigDto.userInboxConfigId = user.userInboxConfig._id.toString();
  //     // createEmailConfigDto.fromEmail = user.email;
  //     // createEmailConfigDto.fromName = user.firstName ?? 'Candidate';
  //     console.log("user in process email create email config dto", createEmailConfigDto)
  //     console.log("user in configid", user.userInboxConfig._id.toString())
  //     // Validate candidate's contact email
  //     const contactEmail = data.contactDetails?.contactEmail;
  //     if (!contactEmail) {
  //       throw new Error('Contact email is missing from job application details.');
  //     }

  //     createEmailConfigDto.to = [contactEmail];
  //     createEmailConfigDto.subject = defaultSubject.replace('{jobTitle}', data.jobId.title);

  //     // Fetch the appropriate email template
  //     const emailTemplate = await this.findEmailTemplate(eventName, user);
  //     if (!emailTemplate) {
  //       throw new Error(`Email template not found for the ${logMessage} event.`);
  //     }

  //     // Generate email body with placeholders replaced
  //     createEmailConfigDto.body = await this.generateEmailBody(emailTemplate, { user, data });

  //     this.logger.log(`Triggered ${eventName} event for ${logMessage}.`);
  //     // this.logger.log(createEmailConfigDto.body);

  //     // Send the email
  //     this.sendEmail(createEmailConfigDto, user);
  //   } catch (error) {
  //     this.logger.error(`Error in ${logMessage}: ${error.message}`);
  //     throw new Error(`Error in ${logMessage}: ${error.message}`);
  //   }
  // }

  // /**
  //  * Find the appropriate email template for the given event and organization.//
  //  */
  // private async findEmailTemplate(eventName: EmailTemplateEvent, user?: any) {
  //   let emailTemplate = null;

  //   // Check if the user is a vendor and use companyId._id instead of org._id
  //   const orgId = user?.roles?.includes(Role.Vendor) ? user.companyId._id : user?.org?._id;
  //   if (orgId) {
  //     // First, try to find the email template with isDefault: true
  //     emailTemplate = await this.emailTemplateService
  //       .findOne({ eventName, isDefault: true, org: orgId, isDeleted: false })
  //       .exec();
  //   }

  //   // If no template is found, try to find it with canDelete: false
  //   if (!emailTemplate && orgId) {
  //     emailTemplate = await this.emailTemplateService
  //       .findOne({ eventName, canDelete: false, org: orgId, isDeleted: false })
  //       .exec();
  //   }
  //   this.logger.log(`Looking for email template with eventName: ${eventName}, orgId: ${orgId}`);


  //   return emailTemplate;
  // }

  /**
   * Generate email body by replacing placeholders with actual values.
   */
  private async generateEmailBody(emailTemplate: any, payload: { user: any; data: any }) {
    let body = emailTemplate.templateHTMLContent ? unescape(emailTemplate.templateHTMLContent) : '';
    const placeholders = await this.placeholderService.find({ emailTemplate: emailTemplate._id.toString() }).exec();
    // console.log(JSON.stringify(payload));

    placeholders.forEach((placeholder) => {
      const value = JSONPath({ path: placeholder.jsonPath, json: payload });
      const regex = new RegExp(`#${placeholder.name}`, 'g');
      if (value.length > 0) {
        // console.log(`Replaced '#${placeholder.name}' with <b>${value[0]}</b>`);
        body = body.replace(regex, `<b>${value[0]}</b>`);
      }
    });

    // Clean up mentions or leftover placeholders
    return body.replace(/<span[^>]*class="TiptapEditor_mention__[^"]*"[^>]*>(.*?)<\/span>/g, '$1');
  }

  @OnEvent('technicalPanel.invite')
  async technicalPanelInvite(payload: TechnicalPanelInvite) {
    try {
      const { user, data } = payload;

      // this.logger.log(user)

      // Validate user inbox configuration
      if (!user.userInboxConfig) {
        throw new Error('User inbox configuration ID is missing.');
      }

      const technicalPanelEmails = data?.technicalPanel?.filter(email => email) ?? [];

      if (!technicalPanelEmails.length) {
        this.logger.log('No technical members found, skipping email notification.');
        return;
      }

      const createEmailConfigDto = new CreateEmailConfigDto();
      createEmailConfigDto.userInboxConfigId = user.userInboxConfig._id.toString();

      createEmailConfigDto.to = technicalPanelEmails;
      createEmailConfigDto.subject = 'Invitation to Join as a Technical Panel Member';

      // Fetch the appropriate email template
      const emailTemplate = await this.findEmailTemplate(EmailTemplateEvent.TECHNICAL_PANEL_INVITE, user.org);
      if (!emailTemplate) {
        throw new Error(`Email template not found for the ${EmailTemplateEvent.TECHNICAL_PANEL_INVITE} event.`);
      }

      // Generate email body with placeholders replaced
      createEmailConfigDto.body = await this.generateEmailBody(emailTemplate, { user, data });

      this.logger.log(`Triggered ${EmailTemplateEvent.TECHNICAL_PANEL_INVITE} event.`);
      // this.logger.log(createEmailConfigDto.body);

      // Send the email
      this.sendEmail(createEmailConfigDto, user);
    } catch (error) {
      this.logger.error(`Error in ${EmailTemplateEvent.TECHNICAL_PANEL_INVITE}: ${error.message}`);
      throw new Error(`Error in ${EmailTemplateEvent.TECHNICAL_PANEL_INVITE}: ${error.message}`);
    }
  }


  @OnEvent('calendar.updated')
  async sendUpdatedCalendar(payload: CalendarPayload) {
    try {
      const { user, data } = payload;

      // this.logger.log(user)

      // Validate user inbox configuration
      if (!user.userInboxConfig) {
        throw new Error('User inbox configuration ID is missing.');
      }

      const attendees = data?.attendees ?? [];

      if (!attendees.length) {
        this.logger.log('No attendees found, skipping email notification.');
        return;
      }

      const createEmailConfigDto = new CreateEmailConfigDto();
      createEmailConfigDto.userInboxConfigId = user.userInboxConfig._id.toString();

      createEmailConfigDto.to = [...attendees];
      createEmailConfigDto.subject = 'Meeting Rescheduled – Updated Details';

      // Fetch the appropriate email template
      const emailTemplate = await this.findEmailTemplate(EmailTemplateEvent.MEETING_RESCHEDULE, user.org);
      if (!emailTemplate) {
        throw new Error(`Email template not found for the ${EmailTemplateEvent.MEETING_RESCHEDULE} event.`);
      }

      // Generate email body with placeholders replaced
      createEmailConfigDto.body = await this.generateEmailBody(emailTemplate, { user, data });

      // this.logger.log(`Triggered ${EmailTemplateEvent.MEETING_RESCHEDULE} event.`);
      // this.logger.log(createEmailConfigDto.body);

      // Send the email
      this.sendEmail(createEmailConfigDto, user);
    } catch (error) {
      this.logger.error(`Error in ${EmailTemplateEvent.MEETING_RESCHEDULE}: ${error.message}`);
      throw new Error(`Error in ${EmailTemplateEvent.MEETING_RESCHEDULE}: ${error.message}`);
    }
  }
}
