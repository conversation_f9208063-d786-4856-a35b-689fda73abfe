import { Prop, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Types } from 'mongoose';
import { Account } from 'src/account/schemas/account.schema';
import { Priority, RecurrenceInterval, Status } from 'src/shared/constants';
import { Contact } from 'src/contact/entities/contact.entity';
import { BasicUser } from 'src/user/schemas/basic-user.schema';
import { Org } from 'src/org/schemas/org.schema';
import { Job } from 'src/job/schemas/job.schema';

export type TaskDocument = HydratedDocument<Task>;


// Prop decorator can accept more options - read here - https://mongoosejs.com/docs/schematypes.html#schematype-options
// and here - https://docs.nestjs.com/techniques/mongodb#model-injection
@Schema({
  timestamps: true
})
export class Task {

  @Prop({
    required: true,
    trim: true,
  })
  title: string;

  @Prop({
    required: false,
    trim: true,
  })
  summary?: string;

  @Prop({
    required: false,
  })
  taskCode?: number;

  @Prop({
    required: false,
    type: Types.ObjectId, ref: 'Task'
  })
  parentTask?: Types.ObjectId;

  // is it mandatory?- it is mandatory
  @Prop({
    type: Date,
    required: true,
  })
  dueDate: Date;

  // @Prop({
  //   type: String,
  //   required: false,
  //   trim: true,
  //   default:'12:00',
  //   validate: {
  //     validator: function(v: string) {
  //       return /\b([01]?[0-9]|2[0-3]):[0-5][0-9]\b/.test(v); // Simple regex for HH:mm format
  //     },
  //     message: props => `${props.value} is not a valid time format!`
  //   }
  // })
  // dueTime?: string;

  //TODO: is it mandatory? -NO
  @Prop({
    required: false,
    trim: true,
  })
  location?: string;

  // @Prop({
  //   required: false,
  //   type: Types.ObjectId, ref: 'Account'
  // })
  // account?: Account;

  @Prop({
    required: false,
    type: Types.ObjectId, ref: 'Org'
  })
  org?: Org;

  @Prop({
    required: false,
    type: Types.ObjectId, ref: 'Job'
  })
  job?: Job;

  @Prop({
    required: false,
    type: Number
  })
  targetProfiles?: number;

  //Single Point of Contact
  @Prop({
    required: false,
    type: Types.ObjectId, ref: 'Contact'
  })
  spoc?: Contact;

  @Prop({
    required: false,
    type: Types.ObjectId, ref: 'BasicUser'
  })
  assignee: BasicUser;

  // list of users or single user
  @Prop({ type: [{ type: Types.ObjectId, ref: 'BasicUser' }] })
  assignees: BasicUser[];

  @Prop({
    required: true,
    type: Types.ObjectId, ref: 'BasicUser'
  })
  createdBy: BasicUser;

  @Prop({
    type: String,
    required: false,
    trim: true,
    default: Priority.LOW,
    enum: Object.values(Priority),
  })
  priority?: string;

  @Prop({
    type: String,
    required: false,
    trim: true,
    default: Status.TO_DO,
    enum: Object.values(Status),
  })
  status?: string;

  @Prop({
    required: false,
    default: false,
    type: Boolean,
  })
  isDeleted?: boolean;

  @Prop({
    required: false,
    default: false,
    type: Boolean,
  })
  isJobAssign?: boolean;


  @Prop({
    type: String,
    required: false,
    trim: true,
    enum: Object.values(RecurrenceInterval),
  })
  recurrenceInterval?: string;

  @Prop({
    required: false,
    type: Types.ObjectId, ref: 'Org'
  })
  vendor?: Org;
}

export const TaskSchema = SchemaFactory.createForClass(Task);
