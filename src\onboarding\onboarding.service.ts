import { Injectable, InternalServerErrorException, Logger, NotFoundException } from '@nestjs/common';
import { CreateOnboardingDto } from './dto/create-onboarding.dto';
import { UpdateOnboardingDto } from './dto/update-onboarding.dto';
import { Onboarding } from './schemas/onboarding.schema';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { BasicUser } from 'src/user/schemas/basic-user.schema';
import { Org } from 'src/org/schemas/org.schema';
import { OrgType } from 'src/shared/constants';

@Injectable()
export class OnboardingService {

  private readonly logger = new Logger(OnboardingService.name);

  constructor(
    @InjectModel(Onboarding.name) private onboardingModel: Model<Onboarding>,
    @InjectModel(Org.name) private orgModel: Model<Org>,
    private eventEmitter: EventEmitter2
  ) {
  }

  async create(createOnboardingDto: CreateOnboardingDto) {
    try {
      this.logger.log(`Processing DTO: ${JSON.stringify(createOnboardingDto)}`);
      const createdOnboarding = new this.onboardingModel(createOnboardingDto);
      // return await createdOnboarding.save();
      const savedOnboarding = await createdOnboarding.save();

      // Populate org and companyId
      await savedOnboarding.populate({
        path: 'org',
        populate: {
          path: 'companyId', select: 'title contactDetails createdBy', populate: {
            path: 'createdBy',  // Populate createdBy (user)
            select: '_id name email'  // Fetch only _id, name, and email
          }

        }  // Populate companyId and its contact details
      });

      // Check if org exists and is a vendor-org
      if (savedOnboarding.org?.orgType === 'vendor-org' && savedOnboarding.org.companyId) {
        const parentCompany = savedOnboarding.org.companyId;

        // Get the primary contact email of the parent company
        const primaryContact = parentCompany.contactDetails?.find((contact: any) => contact.isPrimary);
        let companyEmail = primaryContact?.contactEmail;
        // If no primary contact email, fall back to createdBy email
        if (!companyEmail && parentCompany.createdBy) {
          companyEmail = parentCompany.createdBy.email;
        }


        if (companyEmail) {
          // Emit event to notify the parent company
          this.emitEvent('vendor.onboarding_submitted', {
            vendorName: savedOnboarding.org.title,
            companyName: parentCompany.title,
            companyEmail: companyEmail,
            message: `The vendor "${savedOnboarding.org.title}" has submitted the onboarding form. Please review and approve.`
          });
        } else {
          this.logger.log
            (`No primary email found for parent company: ${parentCompany.title}`);
        }
      }

      return savedOnboarding;
    } catch (error) {
      this.logger.error(`Failed to create Onboarding. ${error}`);
      throw new InternalServerErrorException(`Failed to create Onboarding. ${error.message}`);
    }
  }

  async findAll() {
    return await this.onboardingModel.find()
      .populate({ path: 'org', select: '_id title' })
      .populate({ path: 'user', select: '_id firstName' })
      .populate({
        path: 'identifiers.identifier',
        select: '_id name'
      })
      .populate({
        path: 'identifiers.attachmentUrls',
        select: '_id status originalName'
      })
      .exec();
  }

  async findAllByOrg(orgId: string) {
    return await this.onboardingModel.find({ org: orgId })
      .populate({ path: 'org', select: '_id title isOnboarded' })
      .populate({
        path: 'identifiers.identifier',
        select: '_id name'
      })
      .populate({
        path: 'identifiers.attachmentUrls',
        select: '_id status originalName locationUrl'
      })
      .exec();
  }

  async findAllByCustomer(orgId: string) {
    return await this.onboardingModel.find({ org: orgId })
      .populate({ path: 'org', select: '_id title isOnboarded' })
      .populate({
        path: 'identifiers.identifier',
        select: '_id name'
      })
      .populate({
        path: 'identifiers.attachmentUrls',
        select: '_id status originalName locationUrl'
      })
      .exec();
  }

  async findAllByUser(userId: string) {
    return await this.onboardingModel.find({ user: userId })
      .populate({ path: 'user', select: '_id firstName' })
      .populate({
        path: 'identifiers.identifier',
        select: '_id name'
      })
      .populate({
        path: 'identifiers.attachmentUrls',
        select: '_id status originalName'
      })
      .exec();
  }

  async findOne(onboardingId: Types.ObjectId) {
    try {
      const onboarding = await this.onboardingModel.findById(onboardingId)
        .populate({ path: 'org', select: '_id title isOnboarded' })
        .populate({ path: 'user', select: '_id firstName' })
        .populate({
          path: 'identifiers.identifier',
          select: '_id name'
        })
        .populate({
          path: 'identifiers.attachmentUrls',
          select: '_id status originalName uniqueName locationUrl'
        })
        .exec();
      if (!onboarding) {
        throw new NotFoundException(`Onboarding not found with ID ${onboardingId}`);
      }
      return onboarding;
    } catch (error) {
      this.logger.error(error);
      this.logger.error(`An error occurred in fetching Onboarding by Id ${onboardingId}. ${error?.message}`);
      throw error;
    }
  }

  async update(onboardingId: Types.ObjectId, updateOnboardingDto: UpdateOnboardingDto, user: BasicUser) {
    try {
      const existingOnboarding = await this.findOne(onboardingId)
      if (!existingOnboarding) {
        this.logger.error(`Onboarding with ID ${onboardingId} not found`);
        throw new NotFoundException(`Job application  not found with ID ${onboardingId}`);
      }
      console.log(existingOnboarding)

      const updatedOnboarding = await this.onboardingModel.findByIdAndUpdate(onboardingId, updateOnboardingDto, { new: true })
        .populate({
          path: 'org', populate: {
            path: 'companyId', select: 'title contactDetails createdBy', populate: {
              path: 'createdBy',  // Populate createdBy (user)
              select: '_id name email'  // Fetch only _id, name, and email
            }
          }
        }) // Ensure we get org & company details
        .populate({ path: 'user', select: '_id firstName' })
        .populate({
          path: 'identifiers.identifier',
          select: '_id name'
        })
        .populate({
          path: 'identifiers.attachmentUrls',
          select: '_id status originalName uniqueName locationUrl',
        })
        .exec();

      if (!updatedOnboarding) {
        this.logger.error(`Failed to update onboarding with ID ${onboardingId}`);
        throw new InternalServerErrorException(`Failed to update onboarding with ID ${onboardingId}`);
      }

      console.log(updatedOnboarding)

      const existingIdentifiers = existingOnboarding.identifiers ?? [];
      const updatedIdentifiers = updatedOnboarding.identifiers ?? [];
      let documentUpdated = false;

      existingIdentifiers.forEach((existingIdentifier, index) => {
        const updatedIdentifier = updatedIdentifiers[index];
        console.log(updatedIdentifier)

        if (existingIdentifier?.value !== updatedIdentifier?.value) {
          this.emitEvent('onboarding.identifier.value.updated', { existingIdentifier, updatedOnboarding, user, updatedIdentifier });
        }

        const existingFiles = existingIdentifier?.attachmentUrls ?? [];
        const updatedFiles = updatedIdentifier?.attachmentUrls ?? [];

        console.log('existingFiles', existingFiles);
        console.log('updatedFiles', updatedFiles);
        if (existingFiles[0]?.uniqueName !== updatedFiles[0]?.uniqueName) {
          documentUpdated = true;
          this.emitEvent('onboarding.identifier.attachment.updated', { existingIdentifier, updatedOnboarding, user, updatedIdentifier });
        }

      });

      // **Send email notification if document is updated**
      if (documentUpdated && updatedOnboarding.org?.orgType === 'vendor-org' && updatedOnboarding.org.companyId) {
        const parentCompany = updatedOnboarding.org.companyId;
        const primaryContact = parentCompany.contactDetails?.find((contact: any) => contact.isPrimary);
        let companyEmail = primaryContact?.contactEmail;
        // If no primary contact email, fall back to createdBy email
        if (!companyEmail && parentCompany.createdBy) {
          companyEmail = parentCompany.createdBy.email;
        }

        if (companyEmail) {
          this.emitEvent('vendor.onboarding_updated', {
            vendorName: updatedOnboarding.org.title,
            companyName: parentCompany.title,
            companyEmail: companyEmail,
            message: `The vendor "${updatedOnboarding.org.title}" has updated their onboarding documents. Please review the changes.`
          });
        } else {
          this.logger.log(`No primary email found for parent company: ${parentCompany.title}`);
        }
      }

      return updatedOnboarding;
    } catch (error) {
      this.logger.error(error);
      this.logger.error(`An error occurred while updating Onboarding by ID ${onboardingId}. ${error?.message}`);
      throw error;
    }
  }


  async remove(onboardingId: Types.ObjectId) {
    try {
      const onboarding = await this.findOne(onboardingId)
      if (!onboarding) {
        throw new NotFoundException(`Job application  not found with ID ${onboardingId}`);
      }
      await this.onboardingModel.deleteOne({ _id: onboardingId });
      return { message: 'Onboarding deleted' };
    } catch (error) {
      this.logger.error(error);
      this.logger.error(`An error occurred while removing Onboarding by ID ${onboardingId}. ${error?.message}`);
      throw error;
    }
  }

  async updateCustomerOnboarding(onboardingId: Types.ObjectId, updateOnboardingDto: UpdateOnboardingDto) {
    try {
      const existingOnboarding = await this.findOne(onboardingId)
      if (!existingOnboarding) {
        this.logger.error(`Onboarding with ID ${onboardingId} not found`);
        throw new NotFoundException(`Job application  not found with ID ${onboardingId}`);
      }
      console.log(existingOnboarding)

      const updatedOnboarding = await this.onboardingModel.findByIdAndUpdate(onboardingId, updateOnboardingDto, { new: true })
        .populate({
          path: 'org', populate: {
            path: 'companyId', select: 'title contactDetails createdBy', populate: {
              path: 'createdBy',  // Populate createdBy (user)
              select: '_id name email'  // Fetch only _id, name, and email
            }
          }
        }) // Ensure we get org & company details
        .populate({ path: 'user', select: '_id firstName' })
        .populate({
          path: 'identifiers.identifier',
          select: '_id name'
        })
        .populate({
          path: 'identifiers.attachmentUrls',
          select: '_id status originalName uniqueName locationUrl',
        })
        .exec();

      if (!updatedOnboarding) {
        this.logger.error(`Failed to update onboarding with ID ${onboardingId}`);
        throw new InternalServerErrorException(`Failed to update onboarding with ID ${onboardingId}`);
      }

      console.log(updatedOnboarding)

      // const existingIdentifiers = existingOnboarding.identifiers ?? [];
      // const updatedIdentifiers = updatedOnboarding.identifiers ?? [];
      // let documentUpdated = false;

      // existingIdentifiers.forEach((existingIdentifier, index) => {
      //   const updatedIdentifier = updatedIdentifiers[index];
      //   console.log(updatedIdentifier)

      //   // if (existingIdentifier?.value !== updatedIdentifier?.value) {
      //   //   this.emitEvent('onboarding.identifier.value.updated', { existingIdentifier, updatedOnboarding, user, updatedIdentifier });
      //   // }

      //   const existingFiles = existingIdentifier?.attachmentUrls ?? [];
      //   const updatedFiles = updatedIdentifier?.attachmentUrls ?? [];

      //   console.log('existingFiles', existingFiles);
      //   console.log('updatedFiles', updatedFiles);
      //   if (existingFiles[0]?.uniqueName !== updatedFiles[0]?.uniqueName) {
      //     documentUpdated = true;
      //     // this.emitEvent('onboarding.identifier.attachment.updated', { existingIdentifier, updatedOnboarding, user, updatedIdentifier });
      //   }

      // });

      // **Send email notification if documents are re-uploaded**
      if (updatedOnboarding.org?.orgType === 'admin-customer-org') {
        this.emitEvent('file.customer.reupload', {
          org: existingOnboarding?.org
        });
      }

      return updatedOnboarding;
    } catch (error) {
      this.logger.error(error);
      this.logger.error(`An error occurred while updating Onboarding by ID ${onboardingId}. ${error?.message}`);
      throw error;
    }
  }

  async findAllByVendor(user: any) {
    const contactEmail = user?.email;
    console.log(contactEmail)
    const existingOrgs = await this.orgModel.find({ 'contactDetails.contactEmail': user.email, isDeleted: false, orgType: OrgType.VENDOR_ORG }).exec();

    // Step 2: Extract org _ids
    const vendorOrgIds = existingOrgs.map(org => org._id);
    console.log("vendorOrgIds", vendorOrgIds);
    // Create a map to fetch org details by ID quickly
    const orgMap = new Map(existingOrgs.map(org => [org._id.toString(), org]));

    // const newOrgIds: Types.ObjectId[] = [];
    const newOrgIds: { _id: Types.ObjectId; title: string; country: any; state: any }[] = [];
    const existingRejected: any[] = [];

    for (const orgId of vendorOrgIds) {
      const onboarding = await this.onboardingModel.findOne({ org: orgId.toString() })
        .populate({ path: 'org', select: '_id title country state' })
        .populate({ path: 'user', select: '_id firstName' })
        .populate({ path: 'identifiers.identifier', select: '_id name' })
        .populate({ path: 'identifiers.attachmentUrls', select: '_id status originalName' })
        .exec();

      if (!onboarding) {
        // newOrgIds.push(orgId);
        const org = orgMap.get(orgId.toString());
        newOrgIds.push({
          _id: org?._id || new Types.ObjectId(orgId),
          title: org?.title ?? '',
          country: org?.country,
          state: org?.state
        });
        continue;
      }

      const hasRejected = onboarding.identifiers?.some((identifier: any) =>
        identifier.attachmentUrls?.some((attachment: any) => attachment.status === 'rejected')
      );

      if (hasRejected) {
        existingRejected.push(onboarding);
      }
    }

    return {
      newOrgIds,
      existingRejected
    };

    // return await this.onboardingModel.find({ org:  { $in: vendorOrgIds } })
    //   .populate({ path: 'user', select: '_id firstName' })
    //   .populate({
    //     path: 'identifiers.identifier',
    //     select: '_id name'
    //   })
    //   .populate({
    //     path: 'identifiers.attachmentUrls',
    //     select: '_id status originalName'
    //   })
    //   .exec();
  }

  emitEvent(eventName: string, payload: any) {
    this.logger.debug(payload)
    this.eventEmitter.emit(eventName, payload);
  }

}
