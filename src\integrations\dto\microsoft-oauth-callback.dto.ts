// dtos/microsoft-oauth-callback.dto.ts
import { IsNotEmpty, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class MicrosoftOAuthCallbackDto {
    @ApiProperty({ description: 'Authorization code from Microsoft', required: true })
    @IsString()
    @IsNotEmpty()
    code: string;

    @ApiProperty({ description: 'State to identify integration instance (usually integration ID)', required: true })
    @IsString()
    @IsNotEmpty()
    state: string;
}
