import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Types } from 'mongoose';
import { BasicUser } from 'src/user/schemas/basic-user.schema';

export type IndustryDocument = HydratedDocument<Industry>;


// Prop decorator can accept more options - read here - https://mongoosejs.com/docs/schematypes.html#schematype-options
// and here - https://docs.nestjs.com/techniques/mongodb#model-injection

@Schema({
  timestamps: true
})
@Schema()
export class Industry {

  @Prop({
    required:true,
    trim: true,
  })
  name: string;

  @Prop({
    required:false,
    trim: true,
  })
  description?: string;

  @Prop({
    required:false,
    trim: true,
  })
  code?: string;
  
  @Prop({
    required: true,
    type: Types.ObjectId, ref: 'BasicUser'
  })
  createdBy: BasicUser;

  @Prop({
    required: false,
    default: false,
    type: Boolean,
  })
  isDeleted?: boolean;


}

export const IndustrySchema = SchemaFactory.createForClass(Industry);
