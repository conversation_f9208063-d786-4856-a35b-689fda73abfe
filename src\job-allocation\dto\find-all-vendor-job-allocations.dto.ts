import { ApiProperty } from '@nestjs/swagger';
import { IsMongoId, IsOptional, IsEnum, IsInt, Min } from 'class-validator';
import { Status, Priority } from 'src/shared/constants';
import { Transform } from 'class-transformer';
export class FindAllVendorJobAllocationsDto {
    @ApiProperty({ required: false, description: 'Vendor ID to filter by' })
    @IsOptional()
    @IsMongoId()
    vendor?: string;

    @ApiProperty({ required: false, enum: Priority, description: 'Priority to filter by' })
    @IsOptional()
    @IsEnum(Priority)
    priority?: string;

    @ApiProperty({ required: false, type: Number, description: 'Page number', default: 1, minimum: 1 })
    @IsOptional()
    @IsInt()
    @Min(1)
    @Transform(({ value }) => {
        const parsed = parseInt(value);
        return !isNaN(parsed) && parsed > 0 ? parsed : 1;
    })
    page: number = 1;

    @ApiProperty({ required: false, type: Number, description: 'Number of items per page', default: 10, minimum: 1 })
    @IsOptional()
    @IsInt()
    @Min(1)
    @Transform(({ value }) => {
        const parsed = parseInt(value);
        return !isNaN(parsed) && parsed > 0 ? parsed : 10;
    })
    limit: number = 10;
}
