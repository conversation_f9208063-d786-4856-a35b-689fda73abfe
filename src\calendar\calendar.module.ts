import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { CalendarService } from './calendar.service';
import { JwtModule } from '@nestjs/jwt';
import { MongooseModule } from '@nestjs/mongoose';
import { Calendar, CalendarSchema } from './schemas/calender.schema';
import { CalendarController } from './calender.controller';
import { EndpointsRolesModule } from 'src/endpoints-roles/endpoints-roles.module';
import { Integration, IntegrationSchema } from 'src/integrations/schemas/integrations.schema';

@Module({
  imports: [
    ConfigModule,
    JwtModule, EndpointsRolesModule,
    MongooseModule.forFeature([{ name: Calendar.name, schema: CalendarSchema }]),
    MongooseModule.forFeature([{ name: Integration.name, schema: IntegrationSchema }]),
  ],
  controllers: [CalendarController],
  providers: [CalendarService],
  exports: [CalendarService]
})
export class CalendarModule { }