// dto/query-bgvhandler.dto.ts
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsOptional, IsString, IsBoolean, IsArray } from 'class-validator';

export class BgvHandlerQueryDto {
  @IsOptional()
  @IsBoolean()
  isDefault?: boolean;

  @IsOptional()
  @IsString()
  assignToOrgId?: string;

  @IsOptional()
  @IsString()
  assignToDepartmentId?: string;

  @IsOptional()
  @ApiProperty({ required: false, default: 1 })
  @Transform(({ value }) => {
    return value > 0 ? value : 1;
  })
  page: number = 1;

  @IsOptional()
  @ApiProperty({ required: false })
  @Transform(({ value }) => {
    const parsed = parseInt(value);
    return !isNaN(parsed) && parsed > 0 ? parsed : 1000000;
  })
  limit: number = 1000000;

  @IsOptional()
  @IsString()
  email?: string;

  @IsOptional()
  @IsString()
  title?: string;

  @IsOptional()
  @IsString()
  search?: string;


  // @IsOptional()
  // @IsArray()
  // documentKeys?: string[]; // For filtering by required documents
  @IsOptional()
  @IsArray()
  @IsString({ each: true }) // Ensure each item in the array is string
  @ApiPropertyOptional({
    type: [String],
    description: 'Filter by required document keys',
    example: ['passport_photo', 'qualification_certificates'],
  })
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      try {
        // Try to parse JSON string
        return JSON.parse(value);
      } catch {
        // Fallback to comma-separated string
        return value.split(',').map((v: string) => v.trim());
      }
    }
    return value;
  })
  documentKeys?: string[];
}
