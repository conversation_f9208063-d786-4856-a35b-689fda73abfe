import {
    IsDateString,
    IsEnum,
    IsMongoId,
    IsNotEmpty,
    IsNumber,
    IsOptional,
    IsString,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateLeaveRequestDto {
    @ApiProperty({ example: '64b2f6ab3c31e21c88a4d9f1' })
    // @IsMongoId()
    requestedBy: string;

    @ApiProperty({ example: 'Sick Leave' })
    // @IsMongoId()
    leaveType: string;

    @ApiProperty({ example: '2025-07-07' })
    @IsDateString()
     @IsOptional()
    fromDate?: string;

    @ApiProperty({ example: '2025-07-09' })
    @IsDateString()
     @IsOptional()
    toDate?: string;

    @ApiProperty({ example: 3 })
    @IsNumber()
     @IsOptional()
    numberOfDays?: number;

    @ApiProperty({ example: 'Fever and doctor visit' })
    @IsString()
     @IsOptional()
    reason?: string;

    @ApiProperty({ enum: ['Pending', 'Approved', 'Rejected'], required: false })
    @IsOptional()
    @IsEnum(['Pending', 'Approved', 'Rejected'])
    status?: 'Pending' | 'Approved' | 'Rejected';

    @ApiProperty({ required: false })
    @IsOptional()
    // @IsMongoId()
    approvedBy?: string;
}
