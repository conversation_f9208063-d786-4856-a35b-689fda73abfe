import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsDateString, IsEnum, IsNumber, IsOptional } from 'class-validator';
import { AiInterviewStatus } from '../schemas/job-application.schema';

export class UpdateAiInterviewDto {
  @ApiProperty({
    enum: AiInterviewStatus,
    description: 'Status of AI interview',
  })
  @IsOptional()
  @IsEnum(AiInterviewStatus)
  aiInterviewStatus: AiInterviewStatus;

  @ApiPropertyOptional({
    description: 'Score of AI interview',
    minimum: 0,
    maximum: 100,
  })
  @IsOptional()
  @IsNumber()
  aiInterviewScore?: number;

  @ApiPropertyOptional({
    description: 'AI interview session id',
  })
  @IsOptional()
  aiInterviewId?: string;

  @ApiPropertyOptional({
    description: 'Duration of AI interview in minutes',
    minimum: 0,
  })
  @IsOptional()
  @IsNumber()
  aiInterviewDuration?: number;

  @ApiPropertyOptional({ description: 'Date of AI interview' })
  @IsOptional()
  @IsDateString()
  aiInterviewDate?: string;
}
