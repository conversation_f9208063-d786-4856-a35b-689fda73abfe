import { Delete, Module, forwardRef } from '@nestjs/common';
import { UserService } from './user.service';
import { UserController } from './user.controller';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { BasicUser, BasicUserSchema } from './schemas/basic-user.schema';
import { MongooseModule } from '@nestjs/mongoose';
import { PipesModule } from 'src/pipes/pipes.module';
import { JwtModule } from '@nestjs/jwt';
import * as bcrypt from 'bcrypt';
import { Logger } from '@nestjs/common';
import { ForgotPassword, ForgotPasswordSchema } from './schemas/forgot-password.schema';
import { AuthModule } from 'src/auth/auth.module';
import { OrgModule } from 'src/org/org.module';
import { CommonModule } from 'src/common/common.module';
import { StatusModule } from 'src/status/status.module';
import { BusinessUnitModule } from 'src/business-unit/business-unit.module';
import { UserTemp, UserTempSchema } from './schemas/user-temp.schema';
import { VendorInviteModule } from 'src/vendor-invite/vendor-invite.module';
import { DeleteUserModule } from 'src/delete-user/delete-user.module';
import { DeleteUser, DeleteUserSchema } from 'src/delete-user/schemas/delete-user.schema';
import { EndpointsRolesModule } from 'src/endpoints-roles/endpoints-roles.module';
import { BusinessUnit, BusinessUnitSchema } from 'src/business-unit/schemas/business-unit.schema';
import { Roles, RolesSchema } from 'src/roles/schemas/roles.schema';
import { NotificationsModule } from 'src/notification/notifications.module';
@Module({
  imports: [
    ConfigModule,
    JwtModule, EndpointsRolesModule,
    PipesModule,
    StatusModule,
    BusinessUnitModule,
    forwardRef(() =>DeleteUserModule),
    forwardRef(() => VendorInviteModule),
    forwardRef(() => NotificationsModule),
    CommonModule,
    forwardRef(() => AuthModule),
    // MongooseModule.forFeature([{ name: BasicUser.name, schema: BasicUserSchema }]),
    MongooseModule.forFeature([{ name: BusinessUnit.name, schema: BusinessUnitSchema }]),
    MongooseModule.forFeature([{ name: Roles.name, schema: RolesSchema }]),
    MongooseModule.forFeatureAsync([
      {
        name: BasicUser.name,
        imports: [ConfigModule],
        useFactory: (configService: ConfigService) => {
          const logger = new Logger('BasicUserSchemaPreHook')
          const schema = BasicUserSchema;
          schema.pre('save', async function (next: any) {
            logger.debug('pre hook on user schema');

            try {
              if (!this.isModified('password')) {
                return next();
              }
              logger.debug('noticed a password change.')
              // tslint:disable-next-line:no-string-literal
              const hashed = await bcrypt.hash(this['password'], 10);
              // tslint:disable-next-line:no-string-literal
              this['password'] = hashed;
              return next();
            } catch (err) {
              return next(err);
            }
          });
          return schema;
        },
        inject: [ConfigService],
      },
      {
        name: UserTemp.name,
        imports: [ConfigModule],
        useFactory: (configService: ConfigService) => {
          const logger = new Logger('BasicUserSchemaPreHook')
          const schema = UserTempSchema;
          schema.pre('save', async function (next: any) {
            logger.debug('pre hook on user schema');

            try {
              if (!this.isModified('password')) {
                return next();
              }
              logger.debug('noticed a password change.')
              // tslint:disable-next-line:no-string-literal
              const hashed = await bcrypt.hash(this['password'], 10);
              // tslint:disable-next-line:no-string-literal
              this['password'] = hashed;
              return next();
            } catch (err) {
              return next(err);
            }
          });
          return schema;
        },
        inject: [ConfigService],
      },
    ]),
    MongooseModule.forFeature([{ name: ForgotPassword.name, schema: ForgotPasswordSchema },
      {name:DeleteUser.name, schema: DeleteUserSchema}
    ]),
    forwardRef(() => OrgModule),
  ],
  controllers: [UserController],
  providers: [
    UserService,
  ],
  exports: [UserService]
})
export class UserModule { }

