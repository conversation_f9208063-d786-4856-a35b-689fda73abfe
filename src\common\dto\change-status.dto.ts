import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty, IsEnum, ArrayNotEmpty, IsArray } from "class-validator";
import { AccountStatus } from "src/shared/constants";
import { CommentDto } from "./comment.dto";

export class ChangeStatusDto {

    @ApiProperty({
        type: String,
        required: true,
        default: AccountStatus.PROSPECT,
        enum: AccountStatus,
        description: 'Status of account, contact, client',
    })
    @IsNotEmpty()
    @IsEnum(AccountStatus)
    status: AccountStatus

    @ApiProperty({
        type: CommentDto
    })
    comment: CommentDto;
    


}
