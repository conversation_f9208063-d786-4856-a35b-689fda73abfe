import { Lo<PERSON>, Module,forwardRef } from '@nestjs/common';
import { JobApplicationFormService } from './job-application-form.service';
import { JobApplicationFormController } from './job-application-form.controller';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { UserModule } from 'src/user/user.module';
import { MongooseModule, getConnectionToken } from '@nestjs/mongoose';
import { DynamicField, DynamicFieldSchema, JobApplication, JobApplicationSchema } from './schemas/job-application.schema';
import { StageModule } from 'src/stage/stage.module';
import { WorkflowModule } from 'src/workflow/workflow.module';
import { snakeCase } from 'lodash';
import { EmailTemplate, EmailTemplateSchema } from 'src/email-template-builder/schemas/email-template-builder.schema';
import { Org, OrgSchema, Placeholder, PlaceholderSchema } from 'src/org/schemas/org.schema';
import { BasicUser, BasicUserSchema } from 'src/user/schemas/basic-user.schema';
import { Job, JobSchema } from 'src/job/schemas/job.schema';
import { Offer, OfferSchema } from 'src/offer/schemas/offer.schema';
import { RecruiterTarget, RecruiterTargetSchema } from 'src/recruiter-target/schemas/recuriter-target.schema';
import { Interview, InterviewSchema } from 'src/interview/schemas/interview.schema';
import { EndpointsRolesModule } from 'src/endpoints-roles/endpoints-roles.module';
import { JobAllocationModule } from 'src/job-allocation/job-allocation.module';
import { JobAllocationBase, JobAllocationBaseSchema } from 'src/job-allocation/schemas/job-allocation-base.schema';
import { NotificationsModule } from 'src/notification/notifications.module';
@Module({
  controllers: [JobApplicationFormController],
  imports: [
    ConfigModule,
    JwtModule, EndpointsRolesModule,
    forwardRef(() => UserModule),
    StageModule,
    forwardRef(() => WorkflowModule),
    JobAllocationModule,
    forwardRef(() => NotificationsModule),
    // MongooseModule.forFeature([{ name:JobApplication.name, schema: JobApplicationSchema}])
    // MongooseModule.forFeature([{ name: DynamicField.name, schema: DynamicFieldSchema }]),
    MongooseModule.forFeature([
      { name: EmailTemplate.name, schema: EmailTemplateSchema },
      { name: Placeholder.name, schema: PlaceholderSchema },
      { name: Job.name, schema: JobSchema },
      { name: Offer.name, schema: OfferSchema },
      { name: Interview.name, schema: InterviewSchema },
      { name: RecruiterTarget.name, schema: RecruiterTargetSchema },
      { name: BasicUser.name, schema: BasicUserSchema },
      { name: Org.name, schema: OrgSchema },
      { name: JobAllocationBase.name, schema: JobAllocationBaseSchema },
    ]),
    MongooseModule.forFeatureAsync([
      {
        name: DynamicField.name,
        imports: [ConfigModule],
        useFactory: (configService: ConfigService) => {
          const logger = new Logger('DynamicFieldPreHook')
          const schema = DynamicFieldSchema;
          logger.log("pre hook on DynamicField schema")
          // Pre-save hook to set `fieldName` from `title`
          schema.pre('save', function (next) {
            logger.debug("pre hook on DynamicField schema")

            this.title = snakeCase(this.title);  // Convert title to snake_case

            next();
          });

          return schema;
        },
        inject: [getConnectionToken(), ConfigService],
      },
    ]),
    MongooseModule.forFeatureAsync([
      {
        name: JobApplication.name,
        imports: [ConfigModule],
        useFactory: (configService: ConfigService) => {
          const AutoIncrement = AutoIncrementFactory(configService);

          const schema = JobApplicationSchema;
          schema.plugin(AutoIncrement, {
            inc_field: 'jobApplicationCode',
            id: 'jobApplication_sequence',
            start_seq: 1,
            reference_fields: []
          });

          return schema;
        },
        inject: [getConnectionToken(), ConfigService],

      },
    ]),

  ],
  providers: [JobApplicationFormService],
  exports: [MongooseModule, JobApplicationFormService],
})
export class JobApplicationFormModule { }
const AutoIncrementFactory = require('mongoose-sequence');