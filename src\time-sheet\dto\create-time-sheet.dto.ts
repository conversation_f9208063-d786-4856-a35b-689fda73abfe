import {
  IsNotEmpty,
  IsMongoId,
  IsString,
  IsOptional,
  IsEnum,
  IsDate,
  IsNumber,
  IsBoolean,
  ValidateNested,
  IsArray,
  IsDateString,
} from 'class-validator';
import { Type, Transform, TransformFnParams } from 'class-transformer';
import { ApiHideProperty, ApiProperty } from '@nestjs/swagger';
import { sanitizeWithStyle } from "src/utils/sanitize-with-style";

export enum TimesheetType {
  DAILY = 'Daily',
  WEEKLY = 'Weekly',
  MONTHLY = 'Monthly',
}

export class DailyEntryDto {
  @IsOptional()
  @IsDateString()
  date?: string;

  @IsOptional()
  @IsNumber()
  hoursWorked?: number; // Direct input for weekly/monthly

  @IsOptional()
  @IsBoolean()
  isWeekOff?: boolean;

  @IsOptional()
  @IsBoolean()
  isInCycle?: boolean;

  @IsOptional()
  @IsString()
  remarks?: string;

  @IsOptional()
  @IsNumber()
  overtimeHours?: number;

  @IsOptional()
  @IsNumber()
  overtimeAmount?: number;

}

export class WeeklyEntryDto {
  @IsString()
  weekLabel: string; // e.g., "2025-W27"

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => DailyEntryDto)
  days: DailyEntryDto[];

  @IsOptional()
  @IsNumber()
  totalWeekHours?: number; // Will be auto-calculated
}


export class CreateTimeSheetDto {
  @ApiProperty({ type: String })
  @IsMongoId()
  @IsNotEmpty()
  @Transform(({ value }) => sanitizeWithStyle(value))
  employee: string;

  @ApiProperty({ type: String })
  @IsOptional()
  @Transform(({ value }) => sanitizeWithStyle(value))
  employeeId: string;

  @ApiProperty({ type: [String], required: false })
  @Transform(({ value }) => Array.isArray(value) ? value.map(v => sanitizeWithStyle(v)) : [sanitizeWithStyle(value)])
  @IsOptional()
  projectId?: string[];

  @ApiProperty({ type: [String], required: false })
  @Transform(({ value }) => Array.isArray(value) ? value.map(v => sanitizeWithStyle(v)) : [sanitizeWithStyle(value)])
  @IsOptional()
  taskId?: string[];

  @ApiProperty({
    type: String,
    required: false,
    description: 'task description',
  })
  @IsOptional()
  @IsString()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  description?: string;


  @ApiProperty({ type: String, required: false })
  @Transform(({ value }) => sanitizeWithStyle(value))
  orgId?: string;

  @ApiProperty({ enum: TimesheetType })
  @IsEnum(TimesheetType)
  timesheetType: TimesheetType;

  // For DAILY
  @ApiProperty({ type: String, required: false })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  date?: Date;

  @ApiProperty({ type: String, required: false })
  @IsOptional()
  @IsString()
  startTime?: string; // Format: "09:00" or "09:00:00"

  @ApiProperty({ type: String, required: false })
  @IsOptional()
  @IsString()
  endTime?: string; // Format: "17:30" or "17:30:00"

  @ApiProperty({ required: false })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  startDate?: Date;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  endDate?: Date;



  @ApiProperty({ type: Number, required: false })
  @IsOptional()
  @IsNumber()
  hoursWorked?: number;

  @ApiProperty({ type: Number, required: false })
  @IsOptional()
  @IsNumber()
  overtimeAmount?: number;


  @ApiProperty({ type: Boolean, required: false })
  @IsOptional()
  @IsBoolean()
  isWeekOff?: boolean;

  // For WEEKLY
  @ApiProperty({ type: String, required: false })
  @IsOptional()
  @IsString()
  week?: string;

  @ApiProperty({ type: [WeeklyEntryDto], required: false })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => WeeklyEntryDto)
  weeklyEntries?: WeeklyEntryDto[];

  @ApiProperty({ type: [DailyEntryDto], required: false })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => DailyEntryDto)
  monthlyEntries?: DailyEntryDto[];


  // For MONTHLY
  @ApiProperty({ type: String, required: false })
  @IsOptional()
  @IsString()
  month?: string;

  @IsOptional()
  @IsNumber()
  billableDays?: number;

  @IsOptional()
  @IsNumber()
  leaveDays?: number;

  @IsOptional()
  @IsNumber()
  totalBillableDays?: number;

  @IsOptional()
  @IsNumber()
  totalBillableAmount?: number;

  @IsOptional()
  @IsNumber()
  totalBillableAmountHours?: number;


  // Common
  @ApiProperty({ type: Number, required: false })
  //   @IsNumber()
  hourlyRate?: number;



  @ApiProperty({ type: Number, required: false })
  @IsOptional()
  @IsNumber()
  overtimeHours?: number;

  @ApiProperty({ type: Number, required: false })
  @IsOptional()
  @IsNumber()
  overtimeHourlyRate?: number;

  @ApiProperty({ type: [String], required: false })
  @IsOptional()
  @IsArray()
  @IsMongoId({ each: true })
  attachments?: string[];

  @ApiProperty({ enum: ['Pending', 'Approved', 'Rejected'], required: false })
  @IsOptional()
  @IsString()
  status?: string;




  @ApiProperty({ type: String, required: false })
  @IsOptional()
  @IsString()
  remarks?: string;

  @ApiProperty({ type: String, required: false })
  @IsOptional()
  @IsMongoId()
  reviewedBy?: string;

  @ApiProperty({ type: String, required: false })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  reviewedAt?: Date;


  @ApiHideProperty()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  createdBy?: string;
}
