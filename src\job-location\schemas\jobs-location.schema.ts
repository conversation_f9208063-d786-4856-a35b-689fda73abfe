import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Types } from 'mongoose';
import { Country } from 'src/country/schemas/country.schema';
import { City } from 'src/state/schemas/city.schema';
import { State } from 'src/state/schemas/state.schema';

export type JobsLocationDocument = HydratedDocument<JobsLocation>;

@Schema({
  timestamps: true,
  collection: 'jobs_location'
})
export class JobsLocation {

  @Prop({
    type: Types.ObjectId,
    required: true,
    ref: 'Country'
  })
  country: Country;

  @Prop({
    type: Types.ObjectId,
    required: true,
    ref: 'State'
  })
  state: State;

  @Prop({
    type: Types.ObjectId,
    required: true,
    ref: 'City'
  })
  city: City;

  @Prop({ type: String, required: false, trim: true })
  postalCode?: string;

  @Prop({ type: Number, required: false }) // Latitude (X-Axis)
  latitude?: number;

  @Prop({ type: Number, required: false }) // Longitude (Y-Axis)
  longitude?: number;
}

export const JobsLocationSchema = SchemaFactory.createForClass(JobsLocation);

// 🔹 Create indexes for optimized lookups
JobsLocationSchema.index({ city: 1, state: 1, country: 1 }, { unique: true });
