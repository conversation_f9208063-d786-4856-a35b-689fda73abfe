import { Injectable } from '@nestjs/common';
import { Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { OnEvent } from '@nestjs/event-emitter';

@Injectable()
export class AppService {
  private readonly logger = new Logger(AppService.name);

  constructor(private configService: ConfigService) {

  }

  // @OnEvent('user.created')
  // handleUserCreatedEvent(payload: any) {
  //   this.logger.debug(payload);
  //   this.logger.debug("Learning Nest JS....")
  //   // handle and process "OrderCreatedEvent" event
  // }

  getHello(): string {
    // return 'Hello World!';
    this.logger.debug(`Hello world from appservice. ${this.configService.get('APP_NAME')}`);
    return `<h1>Talency</h1>
        <p>Here goes some summary.</p>
      `;
  }

}
