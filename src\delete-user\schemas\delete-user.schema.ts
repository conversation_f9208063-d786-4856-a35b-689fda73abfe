import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Types } from 'mongoose';
import { Role } from 'src/auth/enums/role.enum';
import { Org } from 'src/org/schemas/org.schema';
import { BasicUser } from 'src/user/schemas/basic-user.schema';


export type DeleteUserDocument = HydratedDocument<DeleteUser>;

@Schema({
  timestamps: true
})
export class DeleteUser {
  @Prop({
    type: Types.ObjectId,
    required: true,
    unique:true,
    ref: 'BasicUser'
  })
  userId: BasicUser;

  @Prop({
    type: Types.ObjectId,
    required: false,
    ref: 'BasicUser'
  })
  actionTakenBy?: BasicUser;


  @Prop({
    type: Types.ObjectId,
    required: false,
    ref: 'org'
  })
  org?: Org;

    @Prop({
      required: false,
      default: [Role.User]
    })
    roles?: Role[];
  

  @Prop({
    type: String,
    required: true,
    trim: true
  })
  reason: string;

  @Prop({
    type: String,
    required: false,
    trim: true
  })
  adminComment?: string;


  @Prop({
    type: String,
    enum: ['PENDING', 'APPROVED', 'REJECTED'],
    default: 'PENDING'
  })
  status: string;

  @Prop({
    type: Date,
    required: false
  })
  actionDate?: Date;
}

export const DeleteUserSchema = SchemaFactory.createForClass(DeleteUser);