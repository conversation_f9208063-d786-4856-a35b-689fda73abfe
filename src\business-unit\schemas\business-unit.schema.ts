import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";
import { HydratedDocument, Types } from 'mongoose';
import { BasicUser } from 'src/user/schemas/basic-user.schema';
import { Org } from "src/org/schemas/org.schema";
import { BusinessUnitType, EmploymentType } from "src/shared/constants";
import { BgvHandler } from "src/bgv-handler/schemas/bgv-handler.schema";


export type BusinessUnitDocument = HydratedDocument<BusinessUnit>;

@Schema({
  timestamps: true
})
export class BusinessUnit {

  @Prop({
    type: String,
    required: true,
    trim: true,
    maxlength: 100,
  })
  label: string;

  @Prop({
    type: String,
    required: true,
    trim: true
  })
  key: string;

  @Prop({
    type: Types.ObjectId,
    ref: 'BusinessUnit',
    required: false
  })
  parentBusinessUnit?: BusinessUnit | Types.ObjectId | String;

  @Prop({
    type: Types.ObjectId,
    ref: 'Org',
    required: true
  })
  org: Org;

  @Prop({
    type: Boolean,
    required: false,
    default: false
  })
  isDeleted?: boolean;

  @Prop({
    type: Types.ObjectId,
    required: true,
    ref: 'BasicUser'
  })
  createdBy: BasicUser;

  @Prop({
    type: Number,
    required: true,
    default: 1,
    min: 1
  })
  level: number;

  @Prop({
    type: String,
    required: true,
    trim: true,
    default: BusinessUnitType.RECRUITMENT,
    enum: Object.values(BusinessUnitType)
  })
  type: string;

  @Prop([{ type: Types.ObjectId, ref: 'BusinessUnit' }])
  children?: (Types.ObjectId | BusinessUnit)[];

  @Prop({
    type: String,
    required: true,
    trim: true,
  })
  breadcrumb: string;

  @Prop({
    type: Types.ObjectId,
    ref: 'BasicUser',
    required: true
  })
  departmentHead: string | BasicUser;

  @Prop({
    type: Boolean,
    required: false,
    default: false
  })
  isDemand?: boolean;

  @Prop({
    type: Boolean,
    required: false,
    default: false
  })
  isSupply?: boolean;

  @Prop({
    type: Types.ObjectId,
    ref: 'BgvHandler',
    required: false
  })
  bgvHandlerId: string;
  
  @Prop({
    type: Map,
    of: {
      minMarginPercentage: Number,
      maxHikePercentage: Number,
      requiresApproval: Boolean,
      approverRole: String,
    },
    default: {},
  })
  hikeSettings?: Record<EmploymentType, {
    minMarginPercentage: number;
    maxHikePercentage: number;
    requiresApproval: boolean;
    approverRole: string;
  }>;

}

export const BusinessUnitSchema = SchemaFactory.createForClass(BusinessUnit);
