import { Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { EmployeeService } from './employee.service';
import { EmployeeController } from './employee.controller';
import { MongooseModule } from '@nestjs/mongoose';
import { EmailTemplate, EmailTemplateSchema } from 'src/email-template-builder/schemas/email-template-builder.schema';
import { Placeholder, PlaceholderSchema } from 'src/org/schemas/org.schema';
import { JobApplication, JobApplicationSchema } from 'src/job-application-form/schemas/job-application.schema';
import { EndpointsRolesModule } from 'src/endpoints-roles/endpoints-roles.module';
import { Job, JobSchema } from 'src/job/schemas/job.schema';
import { Offer, OfferSchema } from 'src/offer/schemas/offer.schema';
import { BankDetails, BankDetailsSchema } from './schemas/bank-details.schema';
import { Employee, EmployeeSchema } from './schemas/employee.schema';
@Module({
  controllers: [EmployeeController],
  providers: [EmployeeService],
  imports: [
    JwtModule, EndpointsRolesModule,
    MongooseModule.forFeature([
      { name: Employee.name, schema: EmployeeSchema },
      { name: Offer.name, schema: OfferSchema },
      { name: EmailTemplate.name, schema: EmailTemplateSchema },
      { name: Placeholder.name, schema: PlaceholderSchema },
      { name: JobApplication.name, schema: JobApplicationSchema },
      { name: Job.name, schema: JobSchema },
      { name: BankDetails.name, schema: BankDetailsSchema },

    ])
  ],
  exports: [MongooseModule]
})
export class EmployeeModule { }
