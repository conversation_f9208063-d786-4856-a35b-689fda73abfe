import { Controller, Get, Post, Body, Patch, Param, Delete, Logger, UseGuards, BadRequestException, Req } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { AuthJwtGuard } from 'src/auth/guards/auth-jwt/auth-jwt.guard';
import { RolesGuard } from 'src/auth/guards/roles/roles.guard';
import { Roles } from 'src/auth/decorators/roles.decorator';
import { Role } from 'src/auth/enums/role.enum';
import { validateObjectId } from 'src/utils/validation.utils';
import { BlogService } from './blog.service';
import { CreateBlogDto } from './dto/create-blog.dto';
import { UpdateBlogDto } from './dto/update-blog.dto';

@Controller('')
@ApiTags('blog')
export class BlogController {

    private readonly logger = new Logger(BlogController.name);

    constructor(private readonly blogService: BlogService) { }

    @Post()
    @ApiResponse({ status: 201, description: 'Blog is saved.' })
    @ApiResponse({ status: 401, description: 'Unauthorized.' })
    @ApiResponse({ status: 400, description: 'Bad Request / Data.' })
    @ApiResponse({ status: 403, description: 'Forbidden, User with role admin can only use this end point.' })
    @ApiOperation({ summary: 'Create a new Blog', description: `This endpoint allows you to create a new Blog. This is accessible only for "admin".` })
    @ApiBearerAuth()
    @UseGuards(AuthJwtGuard, RolesGuard)
    // @Roles(Role.Admin, Role.SuperAdmin)
    @Roles()
    create(@Req() req: any,@Body() createBlogDto: CreateBlogDto) {
        if (req.user) {
            createBlogDto.createdBy = req.user._id; 
        }
        return this.blogService.create(createBlogDto);
    }

    @Get('all')
    @ApiResponse({ status: 200, description: 'Blog details retrieved.' })
    @ApiResponse({ status: 401, description: 'Unauthorized.' })
    @ApiResponse({ status: 403, description: 'Forbidden, user with role admin can only use this end point.' })
    @ApiOperation({ summary: 'Retrieve all  blog details', description: `This endpoint returns a list of all blog details.This is accessible only to users with roles "Admin"` })
    @ApiBearerAuth()
    @UseGuards(AuthJwtGuard, RolesGuard)
    // @Roles(Role.Admin, Role.SuperAdmin)
    @Roles()
    findAll() {
        return this.blogService.findAll();
    }


    @Patch(':blogId')
    @ApiParam({ name: 'blogId', description: 'Id of the blog.' })
    @ApiResponse({ status: 200, description: 'Blog details updated.' })
    @ApiResponse({ status: 401, description: 'Unauthorized.' })
    @ApiResponse({ status: 403, description: 'Forbidden, user with role admin use this end point.' })
    @ApiOperation({ summary: 'Update a blog details by Id', description: `This endpoint updates a blog details by Id. This is accessible only for "admin".` })
    @ApiBearerAuth()
    @UseGuards(AuthJwtGuard, RolesGuard)
    // @Roles(Role.Admin, Role.SuperAdmin)
    @Roles()
    update(@Param('blogId') blogId: string, @Body() updateBlogDto: UpdateBlogDto) {
        const objId = validateObjectId(blogId);
        return this.blogService.update(objId, updateBlogDto);
    }

    @Get(':blogId')
    @ApiOperation({
        summary: 'Retrieve a blog by Id',
        description: 'This endpoint retrieves a blog by its Id. Accessible only to users with roles "Admin"".'
    })
    @ApiBearerAuth()
    @UseGuards(AuthJwtGuard, RolesGuard)
    // @Roles(Role.Admin, Role.SuperAdmin)
    @Roles()
    @ApiResponse({ status: 200, description: 'Blog retrieved.' })
    @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
    @ApiResponse({ status: 403, description: 'Forbidden. Only users with roles "Admin" are permitted to use this endpoint.' })
    @ApiResponse({ status: 404, description: 'Blog not found.' })
    findOne(@Param('blogId') blogId: string) {
        const objId = validateObjectId(blogId);
        return this.blogService.findOne(objId);
    }

}
