import { Module } from '@nestjs/common';
import { EmailConfigService } from './email-config.service';
import { EmailConfigController } from './email-config.controller';
import { ConfigModule } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { UserModule } from 'src/user/user.module';
import { MongooseModule } from '@nestjs/mongoose';
import { EmailConfig, EmailConfigSchema } from './schemas/email-config.schema';
import { UserInboxConfigModule } from 'src/user-inbox-config/user-inbox-config.module';
import { AuthModule } from 'src/auth/auth.module';
import { UserInboxConfigService } from 'src/user-inbox-config/user-inbox-config.service';
import { EmailTemplate, EmailTemplateSchema } from 'src/email-template-builder/schemas/email-template-builder.schema';
import { Placeholder, PlaceholderSchema } from 'src/org/schemas/org.schema';
import { FileUploadModule } from 'src/file-upload/file-upload.module';
import { FileMetadata, FileMetadataSchema } from 'src/file-upload/schemas/file-metadata.schema';
import { EndpointsRolesModule } from 'src/endpoints-roles/endpoints-roles.module';
@Module({
  imports: [
    ConfigModule,
    JwtModule, EndpointsRolesModule,
    AuthModule,
    UserModule,
    UserInboxConfigModule,
    MongooseModule.forFeature([
      { name: EmailConfig.name, schema: EmailConfigSchema },
      { name: EmailTemplate.name, schema: EmailTemplateSchema },
      { name: Placeholder.name, schema: PlaceholderSchema },
      {name: FileMetadata.name, schema:FileMetadataSchema}
    ])
  ],
  controllers: [EmailConfigController],
  providers: [EmailConfigService],

})
export class EmailConfigModule { }
