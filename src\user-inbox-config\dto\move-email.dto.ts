import { ApiProperty } from "@nestjs/swagger";
import { Transform, TransformFnParams } from "class-transformer";
import { ArrayNotEmpty, IsArray, IsMongoId, IsNotEmpty, IsOptional, IsString } from "class-validator";

export class MoveEmailsDto {


  @ApiProperty({
    type: String,
    required: false,
    description: 'Id for the email connection.',
  })
  @IsOptional()
  @IsMongoId()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  userInboxConfigId: string;  // ID of the user's inbox configuration

  @IsString()
  @IsNotEmpty()
  sourceFolder: string;

  @IsString()
  @IsNotEmpty()
  destinationFolder: string;

  @IsArray()
  @ArrayNotEmpty()
  @IsString({ each: true })
  emailUIDs: string[];
}

function sanitizeWithStyle(value: any): any {
  throw new Error("Function not implemented.");
}
