import { Controller, Get, Post, Body, Patch, Param, Delete, Logger, UseGuards, BadRequestException, Req } from '@nestjs/common';
import { AccountTypeService } from './account-type.service';
import { CreateAccountTypeDto } from './dto/create-account-type.dto';
import { UpdateAccountTypeDto } from './dto/update-account-type.dto';
import { ApiTags, ApiParam, ApiResponse, ApiBearerAuth, ApiOperation } from '@nestjs/swagger';
import { Types } from 'mongoose';
import { RolesGuard } from 'src/auth/guards/roles/roles.guard';
import { AuthJwtGuard } from 'src/auth/guards/auth-jwt/auth-jwt.guard';
import { Roles } from 'src/auth/decorators/roles.decorator';
import { Role } from 'src/auth/enums/role.enum';
import { validateObjectId } from 'src/utils/validation.utils'
@Controller('')
@ApiTags('Account Types')
export class AccountTypeController {
  private readonly logger = new Logger(AccountTypeController.name);

  constructor(private readonly accountTypeService: AccountTypeService) { }

  @Post()
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin)
  @Roles()
  @ApiResponse({ status: 201, description: 'Account Type is saved.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Data. ' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role admin can only use this end point.' })
  @ApiOperation({ summary: 'Create a account type', description: `This endpoint for creating a account type. This is only accessible for "admin".` })
  create(@Req() req: any, @Body() createAccountTypeDto: CreateAccountTypeDto) {
    createAccountTypeDto.createdBy = req.user._id
    return this.accountTypeService.create(createAccountTypeDto);
  }

  @Get()
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.AccountManager, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.BUHead, Role.Recruiter)
  @Roles()
  @ApiOperation({ summary: 'Retrieve all account types', description: `This endpoint returns a list of all account types. This is accessible for all. ` })
  @ApiResponse({ status: 200, description: 'Account Type retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  // @ApiResponse({ status: 403, description: 'Forbidden, user with role admin and salesrep  can only use this end point.' })
  findAll() {
    return this.accountTypeService.findAll();
  }


  @Get(':accountTypeId')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.AccountManager, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.BUHead, Role.Recruiter)
  @Roles()
  @ApiOperation({ summary: 'Retrieve an account type by Id', description: 'This endpoint returns an account type by its Id. This is accessible for all' })
  @ApiResponse({ status: 200, description: 'Account Type is retrieved.' })
  @ApiResponse({ status: 404, description: 'Account Type not found.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  // @ApiResponse({ status: 403, description: 'Forbidden, user with role admin can only use this end point.' })
  @ApiParam({ name: 'accountTypeId', description: 'id of the accounttype.' })
  findOne(@Param('accountTypeId',) accountTypeId: string) {
    const objId = validateObjectId(accountTypeId);
    return this.accountTypeService.findById(objId);
  }

  //end point if only we want soft deleted 
  @Get('find-all-soft-deleted')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.AccountManager, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.BUHead, Role.Recruiter)
  @Roles()
  @ApiOperation({ summary: 'Get only the soft deleted account types ', description: `This endpoint gets an all the soft deleted account types. This is only accessible for all.` })
  @ApiResponse({ status: 200, description: 'All deleted account types retrieved successfully.' })
  findAllSoftDeleted() {
    return this.accountTypeService.findAllSoftDeleted();
  }

  @Get('find-all-with-soft-deleted')
  @ApiResponse({ status: 200, description: 'Account Type retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  // @ApiResponse({ status: 403, description: 'Forbidden, User with role admin or sales-rep can only use this end point.' })
  @ApiOperation({ summary: 'Retrieve all Account Types along with soft-deleted Account Types', description: `This endpoint returns a list of all account Types including soft deleted account Types. This end point is accessible for all` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.AccountManager, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.BUHead, Role.Recruiter)
  @Roles()
  findAllWithSoftDeleted() {
    return this.accountTypeService.findAllIncludingSoftDeleted();
  }

  @Patch(':accountTypeId')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin)
  @Roles()
  @ApiOperation({ summary: 'Update an account type by Id', description: `This endpoint updates an account type by Id. This is only accessible for "admin".` })
  @ApiResponse({ status: 200, description: 'Account Type is saved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role admin can only use this end point.' })
  @ApiParam({ name: 'accountTypeId', description: 'id of the accounttype.' })
  update(@Param('accountTypeId',) accountTypeId: string, @Body() updateAccountTypeDto: UpdateAccountTypeDto) {
    const objId = validateObjectId(accountTypeId);
    return this.accountTypeService.update(objId, updateAccountTypeDto);
  }

  @Patch(':accountTypeId/restore') // Endpoint for reverting soft delete
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin)
  @Roles()
  @ApiOperation({ summary: 'Restore soft delete for an account type by Id', description: `This endpoint reverts the soft delete of an account type by its Id. This is accessible for "admin".` })
  @ApiResponse({ status: 200, description: 'Soft delete restored successfully.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role admin can only use this end point.' })
  @ApiParam({ name: 'accountTypeId', description: 'id of the accounttype.' })
  restoreSoftDelete(@Param('accountTypeId') accountTypeId: string) {
    const objId = validateObjectId(accountTypeId);
    return this.accountTypeService.restoreSoftDelete(objId);
  }

  @Delete(':accountTypeId/soft-delete')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin)
  @Roles()
  @ApiOperation({ summary: 'Soft Delete an account type by Id', description: 'This endpoint soft deletes an account type by Id.This is only accessible for "admin".' })
  @ApiResponse({ status: 200, description: 'Account Type is deleted.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role admin can only use this end point.' })
  @ApiParam({ name: 'accountTypeId', description: 'id of the accounttype' })
  remove(@Param('accountTypeId') accountTypeId: string) {
    const objId = validateObjectId(accountTypeId);
    return this.accountTypeService.remove(objId);
  }

  @Delete(':accountTypeId/hard-delete')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin)
  @Roles()
  @ApiOperation({ summary: 'Delete an account type by Id', description: 'This endpoint deletes an account type by Id.This is only accessible for "admin".' })
  @ApiResponse({ status: 200, description: 'Account Type is hard deleted.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role admin can only use this end point.' })
  @ApiParam({ name: 'accountTypeId', description: 'ID of the account type' })
  hardDelete(@Param('accountTypeId') accountTypeId: string) {
    const objId = validateObjectId(accountTypeId);
    return this.accountTypeService.hardDelete(objId);
  }

  @Delete('delete-all')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin)
  @Roles()
  @ApiOperation({ summary: 'Remove  all account types', description: `This endpoint deletes all account types. This is accessible only for "admin".` })
  @ApiResponse({ status: 200, description: 'All account types deleted.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  @ApiResponse({ status: 403, description: 'Forbidden, User with role admin can only use this end point.' })
  deleteAll() {
    return this.accountTypeService.deleteAll();
  }

}


