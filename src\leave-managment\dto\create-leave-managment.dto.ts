
import {
  IsArray,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsString,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

export class CreateLeaveManagmentDto {
  @IsString()
  @IsNotEmpty()
  name: string;

  @IsNumber()
  days: number;
  
 @ApiProperty({
    example: 'carryForward',
    enum: ['carryForward', 'zero', 'optional'],
  }) // 👈 Fix for enum rendering
  @IsEnum(['carryForward', 'zero', 'optional'])
  endOfYear: 'carryForward' | 'zero' | 'optional';
}

export class CreateLeavePolicyDto {
  @IsString()
  @IsNotEmpty()
  planName: string;

 @ApiProperty({ type: [CreateLeaveManagmentDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateLeaveManagmentDto)
  leaveTypes: CreateLeaveManagmentDto[];
}
