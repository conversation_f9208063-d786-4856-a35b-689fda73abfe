import { forwardRef, Module } from '@nestjs/common';
import { EndpointPermissionsService } from './endpoint-permissions.service';
import { EndpointPermissionsController } from './endpoint-permissions.controller';
import { MongooseModule } from '@nestjs/mongoose';
import { EndpointPermission, EndpointPermissionSchema } from './schemas/endpointpermissions.schema';
import { JwtModule } from '@nestjs/jwt';
import { EndpointsRoles, EndpointsRolesSchema } from 'src/endpoints-roles/schemas/endpoints-roles.schema';
import { Roles, RolesSchema } from 'src/roles/schemas/roles.schema';
import { UserModule } from 'src/user/user.module';
import { BasicUser, BasicUserSchema } from 'src/user/schemas/basic-user.schema';
import { EndpointsUsers, EndpointsUsersSchema } from 'src/endpoints-roles/schemas/endpoints-users.schema';

@Module({
  imports: [
    JwtModule,
    forwardRef(() => UserModule),  // Use forwardRef here to
    MongooseModule.forFeature([{ name: EndpointPermission.name, schema: EndpointPermissionSchema },{ name: EndpointsRoles.name, schema: EndpointsRolesSchema },
      { name: Roles.name, schema: RolesSchema },    {name: BasicUser.name, schema: BasicUserSchema},
      { name: EndpointsUsers.name, schema: EndpointsUsersSchema }
    ]),
  ],
  controllers: [EndpointPermissionsController],
  providers: [EndpointPermissionsService],
})
export class EndpointPermissionsModule { }
