import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsEmail, IsNotEmpty, IsOptional, ValidateIf } from 'class-validator';

export class RespondToMeetingDto {

    @ApiProperty({
        description: 'Email of the invitee responding to the meeting',
        example: '<EMAIL>',
        required: true,
    })
    @IsEmail()
    @IsNotEmpty()
    email: string;

    @ApiProperty({
        description: 'Set to true if accepting the meeting',
        example: true,
        required: false,
    })
    @IsBoolean()
    @IsOptional()
    isAccept?: boolean;

    @ApiProperty({
        description: 'Set to true if rejecting the meeting',
        example: true,
        required: false,
    })
    @IsBoolean()
    @IsOptional()
    isReject?: boolean;
}
