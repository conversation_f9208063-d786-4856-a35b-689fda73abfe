import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { OnboardingService } from './onboarding.service';
import { OnboardingController } from './onboarding.controller';
import { Onboarding, OnboardingSchema } from './schemas/onboarding.schema';
import { JwtModule } from '@nestjs/jwt';
import { MongooseModule } from '@nestjs/mongoose';
import { EndpointsRolesModule } from 'src/endpoints-roles/endpoints-roles.module';
import { Org, OrgSchema } from 'src/org/schemas/org.schema';
@Module({
  controllers: [OnboardingController],
  imports: [JwtModule, EndpointsRolesModule, MongooseModule.forFeature([{ name: Onboarding.name, schema: OnboardingSchema },{ name: Org.name, schema: OrgSchema } ])],
  providers: [OnboardingService],
})
export class OnboardingModule { }
