import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsEmail, IsNotEmpty, IsOptional, IsS<PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> } from 'class-validator';
import { IsObjectId } from 'class-validator-mongo-object-id';
import { Transform, TransformFnParams } from 'class-transformer';
import sanitizeWithStyle from 'sanitize-html'


// Read about operators of swagger here - https://docs.nestjs.com/openapi/decorators
export class PasswordDto {

    @ApiProperty({
        type: String,
        required: false,
        description: 'Enter email of your account',
        format: 'email'
    })
    @IsOptional()
    @IsEmail()
    @IsString()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value).toLowerCase())
    email?: string;


    @ApiProperty({
        description: 'Give me some password'
    })
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    password: string;


}
