import { Prop, SchemaFactory, Schema } from '@nestjs/mongoose';
import { Types } from 'mongoose';
import { JobAllocationBase } from './job-allocation-base.schema';
import { BasicUser } from 'src/user/schemas/basic-user.schema';
import { JobAllocationBaseSchema } from './job-allocation-base.schema';
import { JobAllocationType } from 'src/shared/constants';

@Schema()
export class JobAllocationToAssignees extends JobAllocationBase {
    kind: JobAllocationType.ASSIGNEES; // TypeScript type definition

    @Prop({ required: false, type: Types.ObjectId, ref: 'BasicUser' })
    assignee?: BasicUser;

    @Prop({ required: false, type: [Types.ObjectId], ref: 'BasicUser', default: [] })
    assignees?: BasicUser[];
}

// Create the discriminator schema
const schema = SchemaFactory.createForClass(JobAllocationToAssignees);

// Register the discriminator
export const JobAllocationToAssigneesSchema = JobAllocationBaseSchema.discriminator(
    JobAllocationType.ASSIGNEES,
    schema
);
