import { ApiProperty } from '@nestjs/swagger';
import { Transform, TransformFnParams } from 'class-transformer';
import { IsNotEmpty, IsDateString, IsMongoId, IsOptional, IsISO8601 } from 'class-validator';
import { sanitizeWithStyle } from "src/utils/sanitize-with-style";


export class CreateAttendanceSessionDto {
    @ApiProperty({
        type: Date,
        required: true,
        description: "check in",
    })
    @IsISO8601()
    @IsDateString()
    @IsNotEmpty()
    checkIn: string;

    @ApiProperty({
        type: Date,
        required: false,
        description: "check out",
    })
    @IsOptional()
    @IsISO8601()
    @IsDateString()
    checkOut?: string;

    @ApiProperty({
        type: String,
        required: false,
    })
    @IsOptional()
    @IsMongoId()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    projectId?: string;

    @ApiProperty({
        type: String,
        required: false,
    })
    @IsOptional()
    @IsMongoId()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    taskId?: string;
}
