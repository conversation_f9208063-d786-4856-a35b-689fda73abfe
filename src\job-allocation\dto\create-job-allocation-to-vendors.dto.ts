import { ApiProperty } from "@nestjs/swagger";
import { IsMongoId, IsNotEmpty, IsOptional } from "class-validator";
import { CreateJobAllocationBaseDto } from "./create-job-allocation.dto";

export class CreateJobAllocationToVendorsDto extends CreateJobAllocationBaseDto {
    @ApiProperty({ type: String, required: true, description: 'Vendor ID' })
    @IsMongoId()
    @IsNotEmpty()
    vendor: string;

    @ApiProperty({
        type: [String],
        required: false,
        description: 'List of vendor IDs associated with the job allocation',
    })
    @IsOptional()
    @IsMongoId({ each: true })
    vendors?: string[];
}
