import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsArray, IsMongoId } from 'class-validator';

export class CreateEndpointsRolesDto {
  @ApiProperty({ 
    description: 'The ObjectId reference to the EndpointPermission document', 
    example: '660f8d5e8d2b3c5a7a1c7e8f' 
  })
  @IsNotEmpty()
  @IsMongoId()
  endPoint: string;

  @ApiPropertyOptional({ 
    description: 'List of roles associated with this endpoint', 
    example: ['admin', 'user', 'moderator'],
    type: [String]
  })
  @IsOptional()
  @IsArray()
  roles?: string[];

  @ApiPropertyOptional({ 
    description: 'The ObjectId reference to the Org document', 
    example: '660f8d5e8d2b3c5a7a1c7e90' 
  })
  @IsOptional()
  @IsMongoId()
  org?: string;
}
