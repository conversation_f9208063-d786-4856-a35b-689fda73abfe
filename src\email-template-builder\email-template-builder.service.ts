import { Injectable, InternalServerErrorException, Logger, NotFoundException } from '@nestjs/common';
import { CreateEmailTemplateDto } from './dto/create-email-template-builder.dto';
import { UpdateEmailTemplateDto } from './dto/update-email-template-builder.dto';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { EmailTemplate, EmailTemplateDocument } from './schemas/email-template-builder.schema';
import mjml2html from 'mjml';
import { escape, unescape } from 'lodash';
import { OnEvent } from '@nestjs/event-emitter';
import { Placeholder } from 'src/org/schemas/org.schema';

@Injectable()
export class EmailTemplateBuilderService {
  private readonly logger = new Logger(EmailTemplateBuilderService.name);

  constructor(@InjectModel(EmailTemplate.name) private emailTemplateModel: Model<EmailTemplateDocument>,
    @InjectModel(Placeholder.name) private placeholderModel: Model<Placeholder>,) { }

  async create(createEmailTemplateDto: CreateEmailTemplateDto): Promise<EmailTemplateDocument> {
    try {
      // Convert MJML content to HTML
      const mjmlResult = mjml2html(createEmailTemplateDto.templateMJMLContent);

      this.logger.log(createEmailTemplateDto.templateMJMLContent)

      this.logger.log(mjmlResult)

      if (mjmlResult.errors.length > 0) {
        this.logger.error(mjmlResult.errors);
        throw new InternalServerErrorException('Failed to convert MJML to HTML');
      }

      this.logger.log(mjmlResult.html)

      // Set the converted HTML content in the DTO
      createEmailTemplateDto.templateHTMLContent = mjmlResult.html;

      const createdEmailTemplate = new this.emailTemplateModel(createEmailTemplateDto);
      return await createdEmailTemplate.save();
    } catch (error) {
      this.logger.error(`Failed to create email template. ${error.message}`);
      throw new InternalServerErrorException(`An error occurred while creating the email template. ${error.message}`);
    }
  }

  async createEmailTemplate(createEmailTemplateDto: CreateEmailTemplateDto,user :any): Promise<EmailTemplateDocument> {
    try {

      let { templateHTMLContent } = createEmailTemplateDto;

      if (!templateHTMLContent) {
        throw new Error("templateHTMLContent is undefined.");
      }

      // Escape the HTML content before saving it to the database
      templateHTMLContent = escape(templateHTMLContent);

      // Update the DTO with the escaped HTML content
      createEmailTemplateDto.templateHTMLContent = templateHTMLContent;
      createEmailTemplateDto.canDelete = true; // Set canDelete to true by default

      const createdEmailTemplate = new this.emailTemplateModel(createEmailTemplateDto);
      const existingCompanyTemplate = await this.emailTemplateModel.findOne({
        // org: user.org._id.toString(),
        isGlobal: false,
        isDeleted:false,
        canDelete: false,
        eventName: createdEmailTemplate.eventName,
      }).exec();
       // Clone placeholders
      const placeholdersToInsert: Placeholder[] = [];
      if (existingCompanyTemplate) {
        const placeholders = await this.placeholderModel.find({ emailTemplate: existingCompanyTemplate._id.toString() }).lean();
        placeholders.forEach(ph => {
          placeholdersToInsert.push({
            ...(this.omitId(ph) as Omit<Placeholder, '_id'>),
            emailTemplate: createdEmailTemplate._id.toString(),
          });
        });
      }

      if (placeholdersToInsert.length) {
        await this.placeholderModel.insertMany(placeholdersToInsert);
      }

      if (createdEmailTemplate.isDefault === true) {
        const existingDefaultTemplate = await this.emailTemplateModel.findOne({
          org: user.org._id.toString(),
          isGlobal: false,
          // canDelete: false,
          isDefault: true,
          isDeleted:false,
          eventName: createdEmailTemplate.eventName,
        }).exec();
        if (existingDefaultTemplate) {
          existingDefaultTemplate.isDefault = false;
          await existingDefaultTemplate.save();
        }
      }

      return await createdEmailTemplate.save();
    } catch (error) {
      this.logger.error(`Failed to create email template. ${error.message}`);
      throw new InternalServerErrorException(`An error occurred while creating the email template. ${error.message}`);
    }
  }

  async findAll(page: number, limit: number, user: any, templateName?: string): Promise<EmailTemplateDocument[]> {
    try {
      const query: any = { isDeleted: false ,isGlobal : false};

      if (templateName) {
        const regex = new RegExp(templateName, 'i'); // Case-insensitive search
        query.templateName = { $regex: regex };
      }
      // if (user.org) {
      //   query.$or = [
      //     { org: user.org?._id },
      //     { isDefault: true }
      //   ];
      // }

      if (user.org?._id) {
        query.org = user.org._id.toString();
      }

      const emailTemplates = await this.emailTemplateModel
        .find(query)
        .skip((page - 1) * limit)
        .limit(limit)
        .populate({ path: 'createdBy', select: '_id roles firstName' })
        .sort({ updatedAt: -1 })
        // .populate({ path: 'org', select: '_id title' })
        .exec();

      return emailTemplates;
    } catch (error) {
      this.logger.error(`An error occurred while retrieving email templates. ${error.message}`);
      throw new InternalServerErrorException(`An error occurred while retrieving email templates. ${error.message}`);
    }
  }



  async findById(templateId: Types.ObjectId): Promise<EmailTemplateDocument> {
    try {
      const emailTemplate = await this.emailTemplateModel.findById(templateId)
        .populate({ path: 'createdBy', select: '_id roles firstName' })
        .exec();

      if (!emailTemplate) {
        throw new NotFoundException(`Email template not found with ID ${templateId}`);
      }

      emailTemplate.templateHTMLContent = unescape(emailTemplate.templateHTMLContent);

      return emailTemplate;
    } catch (error) {
      this.logger.error(`An error occurred while retrieving email template by ID ${templateId}. ${error.message}`);
      throw error;
    }
  }

  async update(templateId: Types.ObjectId, updateEmailTemplateDto: UpdateEmailTemplateDto,user : any) {
    try {
      const emailTemplate = await this.findById(templateId);

      if (!emailTemplate) {
        throw new NotFoundException(`Email template not found with ID ${templateId}`);
      }

      const updatedEmailTemplate = await this.emailTemplateModel.findByIdAndUpdate(templateId, {
        ...updateEmailTemplateDto,
        templateHTMLContent: escape(updateEmailTemplateDto.templateHTMLContent)
      }, { new: true })
        .populate({ path: 'createdBy', select: '_id roles firstName' })
        // .populate({ path: 'org', select: '_id title' })
        .exec();

        if(emailTemplate.isDefault === true && updatedEmailTemplate?.isDefault === false) {
          const existingDefaultTemplate = await this.emailTemplateModel.findOne({
            org: user?.org?._id.toString(),
            isGlobal: false,
            canDelete: false,
            isDeleted:false,
            eventName: emailTemplate.eventName,
          }).exec();
          if (existingDefaultTemplate) {
            existingDefaultTemplate.isDefault = true;
            await existingDefaultTemplate.save();
          }
          return updatedEmailTemplate;
        }


      if (emailTemplate.isDefault === false && updatedEmailTemplate?.isDefault === true) {
        const existingDefaultTemplate = await this.emailTemplateModel.updateMany(
          {
            _id: { $ne: updatedEmailTemplate._id },
            org: user?.org?._id.toString(),
            isGlobal: false,
            isDeleted:false,
            eventName: emailTemplate.eventName,
          },
          {
            $set: { isDefault: false },
          }
        ).exec();
        return updatedEmailTemplate;
      }

        // return updatedEmailTemplate;
    } catch (error) {
      this.logger.error(`An error occurred while updating email template by ID ${templateId}. ${error.message}`);
      throw error;
    }
  }


  async remove(templateId: Types.ObjectId): Promise<{ message: string }> {
    try {
      const emailTemplate = await this.findById(templateId);

      if (!emailTemplate) {
        throw new NotFoundException(`Email template not found with ID ${templateId}`);
      }

      await this.emailTemplateModel.deleteOne({ _id: templateId });
      return { message: 'Email template deleted successfully' };
    } catch (error) {
      this.logger.error(`An error occurred while deleting email template by ID ${templateId}. ${error.message}`);
      throw new InternalServerErrorException('An error occurred while deleting the email template');
    }
  }

  async delete(templateId: Types.ObjectId,user : any) {
    try {
      const emailTemplate = await this.findById(templateId);
      if (!emailTemplate) {
        throw new NotFoundException(`Email template not found with ID ${templateId}`);
      }
      const deletedTemplate = await this.emailTemplateModel.findByIdAndUpdate(templateId, { isDeleted: true,isDefault: false });
      if(deletedTemplate) {
        const placeholders = await this.placeholderModel.find({ emailTemplate: deletedTemplate._id.toString() }).lean();
        if (placeholders.length > 0) {
          await this.placeholderModel.deleteMany({ emailTemplate: deletedTemplate._id.toString() });
        }
      }
      if (emailTemplate.isDefault === true) {
        const existingDefaultTemplate = await this.emailTemplateModel.findOne({
          org: user.org._id.toString(),
          isGlobal: false,
          canDelete: false,
          isDeleted:false,
          eventName: emailTemplate.eventName,
        }).exec();
        if (existingDefaultTemplate) {
          existingDefaultTemplate.isDefault = true;
          await existingDefaultTemplate.save();
        }
      }
      return { message: 'Email template soft deleted' };
    } catch (error) {
      this.logger.error(`An error occurred while soft deleting email template by ID ${templateId}. ${error?.message}`);
      throw new InternalServerErrorException('An error occurred while soft deleting the Email template');
    }
  }

  async bulkDelete(templateIds: Types.ObjectId[]) {
    try {
      const result = await this.emailTemplateModel.updateMany(
        { _id: { $in: templateIds } },
        { $set: { isDeleted: true } }
      ).exec();

      if (result.modifiedCount > 0) {
        this.logger.log(`Successfully soft-deleted ${result.modifiedCount} email templates.`);
      } else {
        this.logger.error('No email templates were deleted.');
      }

      return result;
    } catch (error) {
      this.logger.error(`Error while bulk deleting email templates: ${error.message}`, error.stack);
      throw new InternalServerErrorException('Error while bulk deleting email templates.');
    }
  }

  async getEmailTemplatesCount(user : any) {
    try {
      const templatesCount = await this.emailTemplateModel.countDocuments({ isDeleted: false ,org : user.org._id.toString()}).exec();
      return templatesCount
    } catch (error) {
      throw new Error(`Error fetching email template count. ${error.message}`);
    }
  }

  // Utility method
  private omitId(doc: Record<string, any>) {
    const clone = { ...doc };
    delete clone._id;
    return clone;
  }

  @OnEvent('org.CloneDefaultEmailTemplates') // Also listen for activation event
  async cloneAllGlobalTemplatesForOrg(payload: any) {
    const { org } = payload;
    const orgId = org.toString();

    const globalTemplates = await this.emailTemplateModel.find({ isGlobal: true }).lean();

    // Fetch existing template names for the org to prevent duplicates
    const existingOrgTemplates = await this.emailTemplateModel.find({
      org : orgId,
      isGlobal: false,
    }).select('templateName').lean();

    const existingNames = new Set(existingOrgTemplates.map(t => t.templateName));

    const newTemplates = [];
    const placeholdersToInsert: Placeholder[] = [];

    for (const template of globalTemplates) {
      // ❌ Skip if already cloned
      if (existingNames.has(template.templateName)) continue;

      const oldTemplateId = template._id.toString();

      // Clone template
      const newTemplate = await this.emailTemplateModel.create({
        ...this.omitId(template),
        org : orgId,
        isGlobal: false,
        isDefault: true,
        canDelete: false,
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      newTemplates.push(newTemplate);

      // Clone placeholders
      const placeholders = await this.placeholderModel.find({ emailTemplate: oldTemplateId }).lean();
      placeholders.forEach(ph => {
        placeholdersToInsert.push({
          ...(this.omitId(ph) as Omit<Placeholder, '_id'>),
          emailTemplate: newTemplate._id.toString(),
        });
      });
    }

    if (placeholdersToInsert.length) {
      await this.placeholderModel.insertMany(placeholdersToInsert);
    }

    return newTemplates;
  }


}
