import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Types } from 'mongoose';
import { Country } from 'src/country/schemas/country.schema';

export type StateDocument = HydratedDocument<State>;

@Schema({
  timestamps: true
})
export class State {

  @Prop({
    required: true,
    type: Number,
  })
  stateId: number;

  @Prop({
    type: String,
    required: true,
    trim: true
  })
  stateName: string;

  @Prop({
    type: Boolean,
    required: false,
    default: false
  })
  isDeleted?: boolean;

  // @Prop({
  //   required: true,
  //   type: Types.ObjectId,
  //   ref: 'Country',
  // })
  // country: Country;

  @Prop({
    required: true,
    type: Number,
  })
  country: number;

}

export const StateSchema = SchemaFactory.createForClass(State);

StateSchema.index({ stateName: 1 })

