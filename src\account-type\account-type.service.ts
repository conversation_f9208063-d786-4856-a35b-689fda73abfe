import { BadRequestException, ConflictException, Injectable, InternalServerErrorException, Logger, NotFoundException, Type } from '@nestjs/common';
import { CreateAccountTypeDto } from './dto/create-account-type.dto';
import { UpdateAccountTypeDto } from './dto/update-account-type.dto';
import { ConfigService } from '@nestjs/config';
import { InjectModel } from '@nestjs/mongoose';
import { AccountType } from './schemas/account-type.schema';
import { Model, Types } from 'mongoose';

@Injectable()
export class AccountTypeService {
  private readonly logger = new Logger(AccountTypeService.name);

  constructor(private configService: ConfigService, @InjectModel(AccountType.name) private accountTypeModel: Model<AccountType>,) {}

  async create(createAccountTypeDto: CreateAccountTypeDto) {
    try {
      const isThere = await this.accountTypeModel.findOne({
        name: createAccountTypeDto.name.trim()
      });
      if (isThere) {
        throw new NotFoundException(`The account type with name: "${createAccountTypeDto.name}" already exists.`);
      }

      const createdAccountType = new this.accountTypeModel(createAccountTypeDto);
      return await createdAccountType.save();
    }
    catch (error) {
      this.logger.error('Failed to create account type', error);
      throw new InternalServerErrorException('Unknown error when creating account type.');
    }
  }


  findAll() {
    return this.accountTypeModel.find({ isDeleted: false })
      .populate({ path: 'createdBy', select: '_id roles firstName' })
      .sort({ updatedAt: -1 })
      .exec();
  }


  //only the soft deleted ones
  findAllSoftDeleted() {
    return this.accountTypeModel
      .find({ isDeleted: true })
      .sort({ updatedAt: -1 })
      .exec();
  }

  findAllIncludingSoftDeleted() {
    return this.accountTypeModel.find({})
      .populate({ path: 'createdBy', select: '_id roles firstName' })
      .sort({ updatedAt: -1 })
      .exec();
  }


  async findOne(accountTypeId: Types.ObjectId) {
    try {
      const accountType = await this.accountTypeModel.findById(accountTypeId);
      if (!accountType) {
        throw new NotFoundException(`Account type not found with ID ${accountTypeId}`);
      }
      return accountType;

    } catch (error) {
      this.logger.error(error);
      this.logger.error(`An error occurred in fetching  account type by Id ${accountTypeId}. ${error?.message}`);
      throw error;
    }
  }

  async findById(accountTypeId: Types.ObjectId) {
    try {
      const accountType = await this.accountTypeModel.findById(accountTypeId)
        .populate({ path: 'createdBy', select: '_id roles name' })
        .exec();

      if (!accountType) {
        throw new NotFoundException(`Account type not found with ID ${accountTypeId}`);
      }
      return accountType;
    } catch (error) {
      this.logger.error(error);
      this.logger.error(`An error occurred in feching account type by Id ${accountTypeId}. ${error?.message}`);
      throw error;

    }
  }


  async update(accountTypeId: Types.ObjectId, updateAccountTypeDto: UpdateAccountTypeDto) {
    try {
      const accountType = await this.findById(accountTypeId);
      if (!accountType || accountType.isDeleted) {
        throw new NotFoundException(`Account type not found with ID ${accountTypeId}`);
      }
      const updated = await this.accountTypeModel.findByIdAndUpdate(accountTypeId, updateAccountTypeDto, { new: true });
      return updated;
    } catch (error) {
      this.logger.error(error);
      this.logger.error(`An error occurred while updating account type by ID ${accountTypeId}. ${error?.message}`);
      throw error;
    }
  }


  async remove(accountTypeId: Types.ObjectId) {
    try {
      const accountType = await this.findById(accountTypeId);
      if (!accountType) {
        throw new NotFoundException(`Account type not found with ID ${accountTypeId}`);
      }
      accountType.isDeleted = true;
      return await accountType.save();
    } catch (error) {
      this.logger.error(error);
      this.logger.error(`An error occurred while deleting by ID ${accountTypeId}. ${error?.message}`);
      throw new InternalServerErrorException('Invalid account type ID');
    }
  }


  async hardDelete(accountTypeId: Types.ObjectId) {
    try {
      const accountType = await this.findById(accountTypeId);
      if (!accountType) {
        throw new NotFoundException(`Account type not found with ID ${accountTypeId}`);
      }
      await this.accountTypeModel.deleteOne({ _id: accountTypeId });
      return { message: 'Account type deleted' };
    } catch (error) {
      this.logger.error(`An error occurred while hard deleting by ID ${accountTypeId}. ${error?.message}`);
      throw new InternalServerErrorException('An error occurred while hard deleting the account type');
    }
  }

  async restoreSoftDelete(accountTypeId: Types.ObjectId) {
    try {
      const accountType = await this.accountTypeModel.findById(accountTypeId);
      if (!accountType) {
        throw new NotFoundException(`Account type not found with ID ${accountTypeId}`);
      }
      if (!accountType.isDeleted) {
        throw new NotFoundException(`Account type with ID ${accountTypeId} is not soft deleted`);
      }
      accountType.isDeleted = false;
      return await accountType.save();
    } catch (error) {
      this.logger.error(error);
      this.logger.error(`An error occurred while reverting soft delete for account type with ID ${accountTypeId}: ${error?.message}`);
      throw error;
    }
  }

  async deleteAll() {
    await this.accountTypeModel.deleteMany();
    return "All account types deleted"
  }

}