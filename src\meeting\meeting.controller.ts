import {
    Controller,
    Get,
    Post,
    Body,
    Param,
    Put,
    Delete,
    UseGuards,
    Logger,
    NotFoundException,
    Patch,
    Req,
    Query
} from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiBearerAuth, ApiTags, ApiQuery } from '@nestjs/swagger';
import { MeetingService } from './meeting.service';
import { CreateMeetingDto } from './dto/create-meeting.dto';
import { UpdateMeetingDto } from './dto/update-meeting.dto';
import { Meeting } from './schemas/meeting.schema';
import { Roles } from 'src/auth/decorators/roles.decorator';
import { Role } from 'src/auth/enums/role.enum';
import { AuthJwtGuard } from 'src/auth/guards/auth-jwt/auth-jwt.guard';
import { RolesGuard } from 'src/auth/guards/roles/roles.guard';
import { validateObjectId } from 'src/utils/validation.utils';
import { Types } from 'mongoose';
import { RespondToMeetingDto } from './dto/respond-meeting.dto';


@Controller('')
@ApiTags('Meetings')

export class MeetingController {
    private readonly logger = new Logger(MeetingController.name);

    constructor(private readonly meetingService: MeetingService) { }

    @Post()
    @ApiOperation({ summary: 'Create a new meeting', description: 'This endpoint allows you to create a new meeting.' })
    @ApiBearerAuth()
    @UseGuards(AuthJwtGuard,RolesGuard)
    // @Roles(Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager, Role.Recruiter, Role.TeamMember, Role.TechPanel)
    @Roles()  
    @ApiResponse({ status: 201, description: 'Meeting created successfully' })
    @ApiResponse({ status: 400, description: 'Bad Request / Invalid Data' })
    @ApiResponse({ status: 401, description: 'Unauthorized ' })
    @ApiResponse({ status: 403, description: `Forbidden. Only users with roles "BUHead", "ResourceManager", "DeliveryManager", "TeamLead", "AccountManager", "Recuriter", and "TeamMember" are permitted to use this endpoint.` })
    async create(@Req() req: any, @Body() createMeetingDto: CreateMeetingDto) {
        createMeetingDto.organizer = req.user._id
        createMeetingDto.createdBy = req.user._id
        createMeetingDto.org = req.user.org._id
        return await this.meetingService.create(createMeetingDto);
    }

    @Get()
    @ApiOperation({ summary: 'Get all meetings', description: 'This endpoint fetches all the meetings.' })
    @ApiBearerAuth()
    @UseGuards(AuthJwtGuard, RolesGuard)
    // @Roles(Role.Admin,Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager, Role.Recruiter, Role.TeamMember, Role.TechPanel)
    @Roles()  
    @ApiResponse({ status: 401, description: 'Unauthorized ' })
    @ApiResponse({ status: 200, description: 'List of all meetings', type: [Meeting] })
    @ApiResponse({ status: 500, description: 'Internal server error' })
    @ApiResponse({ status: 403, description: `Forbidden. Only users with roles "Admin","BUHead", "ResourceManager", "DeliveryManager", "TeamLead", "AccountManager", "Recuriter", and "TeamMember" are permitted to use this endpoint.` })
    @ApiQuery({ name: 'orgId', description: 'ID of the organization', required: false })
    @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number', example: 1 })
    @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of items per page', example: 10 })
    @ApiQuery({ name: 'subject', required: false, type: String, description: 'Subject of the meeting', example: "Tracker meeting" })
    @ApiQuery({ name: 'email', required: false, type: String, description: 'Email of the invitee', example: "<EMAIL>" })
    async findAll(@Req() req: any, @Query('orgId') orgId?: string, @Query('subject') subject?: string, @Query('email') email?: string, @Query('page') page: number = 1, @Query('limit') limit: number = 10) {
        if (!orgId && req.user.org) {
            orgId = req.user.org._id;
        }
        return await this.meetingService.findAll(page, limit, orgId, subject, email);
    }

    @Get(':meetingId')
    @ApiOperation({ summary: 'Get a meeting by ID', description: 'Fetch a specific meeting by its ID.' })
    @ApiBearerAuth()
    @UseGuards(AuthJwtGuard, RolesGuard)
    // @Roles(Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager, Role.Recruiter, Role.TeamMember, Role.TechPanel)
    @Roles()  
    @ApiResponse({ status: 401, description: 'Unauthorized ' })
    @ApiResponse({ status: 200, description: 'Meeting found', type: Meeting })
    @ApiResponse({ status: 403, description: `Forbidden. Only users with roles "BUHead", "ResourceManager", "DeliveryManager", "TeamLead", "AccountManager", "Recuriter", and "TeamMember" are permitted to use this endpoint.` })
    @ApiResponse({ status: 404, description: 'Meeting not found' })
    async findOne(@Param('meetingId') meetingId: string) {
        const meetingObjId = validateObjectId(meetingId);
        return await this.meetingService.findOne(meetingObjId);
    }

    @Patch(':meetingId')
    @ApiOperation({ summary: 'Update a meeting', description: 'This endpoint allows you to update a specific meeting.' })
    @ApiBearerAuth()
    @UseGuards(AuthJwtGuard,RolesGuard)
    // @Roles(Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager, Role.Recruiter, Role.TeamMember, Role.TechPanel)
    @Roles()  
    @ApiResponse({ status: 401, description: 'Unauthorized ' })
    @ApiResponse({ status: 200, description: 'Meeting updated successfully', type: Meeting })
    @ApiResponse({ status: 400, description: 'Bad Request / Invalid Data' })
    @ApiResponse({ status: 403, description: `Forbidden. Only users with roles "BUHead", "ResourceManager", "DeliveryManager", "TeamLead", "AccountManager", "Recuriter", and "TeamMember" are permitted to use this endpoint.` })
    @ApiResponse({ status: 404, description: 'Meeting not found' })
    async update(@Req() req: any, @Param('meetingId') meetingId: string, @Body() updateMeetingDto: UpdateMeetingDto) {
        const meetingObjId = validateObjectId(meetingId);
        return await this.meetingService.update(req.user, meetingObjId, updateMeetingDto);
    }

    @Delete(':meetingId')
    @ApiOperation({ summary: 'Delete a meeting', description: 'This endpoint allows you to delete a specific meeting.' })
    @ApiBearerAuth()
    @UseGuards(AuthJwtGuard, RolesGuard)
    // @Roles(Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager, Role.Recruiter, Role.TeamMember, Role.TechPanel)
    @Roles()  
    @ApiResponse({ status: 401, description: 'Unauthorized ' })
    @ApiResponse({ status: 200, description: 'Meeting deleted successfully' })
    @ApiResponse({ status: 403, description: `Forbidden. Only users with roles "BUHead", "ResourceManager", "DeliveryManager", "TeamLead", "AccountManager", "Recuriter", and "TeamMember" are permitted to use this endpoint.` })
    @ApiResponse({ status: 404, description: 'Meeting not found' })
    async remove(@Param('meetingId') meetingId: string) {
        const meetingObjId = validateObjectId(meetingId);
        return await this.meetingService.remove(meetingObjId);
    }

    @Get('count')
    @ApiOperation({ summary: 'Get meeting count', description: 'Fetch the total count of meetings with optional filters.' })
    @ApiBearerAuth()
    @UseGuards(AuthJwtGuard, RolesGuard)
    // @Roles(Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager, Role.Recruiter, Role.TeamMember, Role.TechPanel, Role.Admin)
    @Roles()  
    @ApiResponse({ status: 200, description: 'Meeting count fetched successfully' })
    @ApiResponse({ status: 401, description: 'Unauthorized' })
    @ApiResponse({ status: 403, description: `Forbidden. Only users with roles "BUHead", "ResourceManager", "DeliveryManager", "TeamLead", "AccountManager", "Recuriter", and "TeamMember" are permitted to use this endpoint.` })
    @ApiResponse({ status: 500, description: 'Internal server error' })
    @ApiQuery({ name: 'orgId', description: 'ID of the organization', required: false })
    @ApiQuery({ name: 'email', required: false, type: String, description: 'Email of the invitee', example: "<EMAIL>" })
    async count(@Req() req: any, @Query('orgId') orgId?: string, @Query('email') email?: string) {
        if (!orgId && req.user.org) {
            orgId = req.user.org._id; // Default to the logged-in user's organization
        }
        return await this.meetingService.count(orgId, email);
    }

    // @Post(':meetingId/participants')
    // @ApiOperation({ summary: 'Add participants to a meeting', description: 'This endpoint adds participants to an existing meeting.' })
    // @ApiBearerAuth()
    // @UseGuards(AuthJwtGuard)
    // @ApiResponse({ status: 401, description: 'Unauthorized ' })
    // @ApiResponse({ status: 200, description: 'Participants added successfully', type: Meeting })
    // @ApiResponse({ status: 400, description: 'Bad Request / Invalid Data' })
    // @ApiResponse({ status: 404, description: 'Meeting not found' })
    // async addParticipants(
    //     @Param('meetingId') meetingId: string,
    //     @Body() inviteeIds: string[]
    // ) {
    //     try {
    //         const meetingObjId = validateObjectId(meetingId);
    //         const validInvitees = inviteeIds.map(id => new Types.ObjectId(id));
    //         return await this.meetingService.addParticipants(meetingObjId, validInvitees);
    //     } catch (error) {
    //         throw new NotFoundException(error.message);
    //     }
    // }

    // @Post(':meetingId/reminders')
    // @ApiOperation({ summary: 'Send reminders to participants', description: 'This endpoint sends reminders to participants of the meeting.' })
    // @ApiBearerAuth()
    // @UseGuards(AuthJwtGuard)
    // @ApiResponse({ status: 401, description: 'Unauthorized ' })
    // @ApiResponse({ status: 200, description: 'Reminders sent successfully' })
    // @ApiResponse({ status: 404, description: 'Meeting not found' })
    // async sendReminders(@Param('meetingId') meetingId: string) {
    //     const meetingObjId = validateObjectId(meetingId);
    //     return await this.meetingService.sendReminders(meetingObjId);
    // }

    // @Post(':meetingId/participants/:userId/join')
    // @ApiOperation({ summary: 'Mark participant as joined', description: 'This endpoint marks a participant as joined in the meeting.' })
    // @ApiBearerAuth()
    // @UseGuards(AuthJwtGuard)
    // @ApiResponse({ status: 401, description: 'Unauthorized ' })
    // @ApiResponse({ status: 200, description: 'Participant marked as joined', type: Meeting })
    // @ApiResponse({ status: 404, description: 'Meeting or participant not found' })
    // async markParticipantJoined(
    //     @Param('meetingId') meetingId: string,
    //     @Param('userId') userId: string
    // ) {
    //     return await this.meetingService.markParticipantJoined(meetingId, userId);
    // }

    // @Post(':meetingId/participants/:userId/leave')
    // @ApiOperation({ summary: 'Mark participant as left', description: 'This endpoint marks a participant as left in the meeting.' })
    // @ApiBearerAuth()
    // @UseGuards(AuthJwtGuard)
    // @ApiResponse({ status: 401, description: 'Unauthorized ' })
    // @ApiResponse({ status: 200, description: 'Participant marked as left', type: Meeting })
    // @ApiResponse({ status: 404, description: 'Meeting or participant not found' })
    // async markParticipantLeft(
    //     @Param('meetingId') meetingId: string,
    //     @Param('userId') userId: string
    // ) {
    //     return await this.meetingService.markParticipantLeft(meetingId, userId);
    // }

    @Get("/getMeetingDetails/:url")
    @ApiOperation({ summary: 'Get a meeting initial details', description: 'Fetch meeting intial details.' })
    @ApiBearerAuth()
    @UseGuards(AuthJwtGuard, RolesGuard)
    // @Roles(Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager, Role.Recruiter, Role.TeamMember, Role.TechPanel)
    @Roles()  
    @ApiResponse({ status: 401, description: 'Unauthorized ' })
    @ApiResponse({ status: 200, description: 'Meeting details found' })
    @ApiResponse({ status: 403, description: `Forbidden. Only users with roles "BUHead", "ResourceManager", "DeliveryManager", "TeamLead", "AccountManager", "Recuriter", and "TeamMember" are permitted to use this endpoint.` })
    @ApiResponse({ status: 404, description: 'Meeting details not found' })
    async getMeetingDetails(@Param('url') url: string) {
        return await this.meetingService.getMeetingDetails(url);
    }


    @Get('validate-link')
    @ApiOperation({ summary: 'Validate a meeting link', description: 'Validates if a meeting link is active and valid' })
    @ApiQuery({ name: 'meetingLink', type: String, required: true, description: 'The unique meeting link' })
    @ApiResponse({ status: 200, description: 'Meeting link is valid' })
    @ApiResponse({ status: 400, description: 'Invalid meeting link' })
    @ApiResponse({ status: 404, description: 'Meeting not found' })
    async validateMeetingLink(@Query('meetingLink') meetingLink: string) {
        return await this.meetingService.findOneByLink(meetingLink);
    }

    @Patch(':meetingId/accept-reject')
    @ApiOperation({ summary: 'Accept or Reject a meeting', description: 'Allows an invitee to accept or reject a meeting.' })
    @ApiBearerAuth()
    @UseGuards(AuthJwtGuard, RolesGuard)
    // @Roles(Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager, Role.Recruiter, Role.TeamMember, Role.TechPanel)
    @Roles()  
    @ApiResponse({ status: 200, description: 'Response recorded successfully' })
    @ApiResponse({ status: 400, description: 'Bad Request / Invalid Data' })
    @ApiResponse({ status: 403, description: `Forbidden. Only users with roles "BUHead", "ResourceManager", "DeliveryManager", "TeamLead", "AccountManager", "Recuriter", and "TeamMember" are permitted to use this endpoint.` })
    @ApiResponse({ status: 404, description: 'Meeting not found' })
    async respondToMeeting(@Param('meetingId') meetingId: string, @Body() respondToMeetingDto: RespondToMeetingDto) {
        return await this.meetingService.respondToMeeting(meetingId, respondToMeetingDto);
    }
}
