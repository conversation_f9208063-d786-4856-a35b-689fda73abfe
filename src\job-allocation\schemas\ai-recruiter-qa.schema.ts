import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose, { Document, Types } from 'mongoose';
import { FileMetadata } from 'src/file-upload/schemas/file-metadata.schema';

export type AIRecruiterQADocument = AIRecruiterQA & Document;

@Schema({ timestamps: true })
export class AIRecruiterQA {
  @Prop({ type: Types.ObjectId, ref: 'Job', required: true })
  jobId: string;

  @Prop({ type: Types.ObjectId, ref: 'User', required: false })
  createdBy?: string;

  // @Prop({
  //   type: Map,
  //   of: [{
  //     _id: false, // Prevent Mongoose from generating _id for each Q&A pair
  //     question: { type: String, required: true },
  //     answer: { type: String, required: false }, // Ensure answer is optional
  //   }],
  //   default: () => new Map(),
  // })
  // skillQAPairs: Map<string, { question: string; answer?: string }[]>;

  @Prop({
    type: mongoose.Schema.Types.Mixed, // Accepts any valid JS object
    default: {},
  })
  skillQAPairs: Record<string, { question: string; answer?: string }[]>;

  @Prop({
    type: Types.ObjectId,
    required: false,
  })
  QAMetadata?: FileMetadata;

  @Prop({
    type: [String],
    default: [],
    required: false,
  })
  evaluationCriteria?: string[];
  
}

export const AIRecruiterQASchema = SchemaFactory.createForClass(AIRecruiterQA);
