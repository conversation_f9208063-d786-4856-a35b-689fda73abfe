import { <PERSON>, Get, Post, Body, Patch, Param, Delete, Req, UseGuards } from '@nestjs/common';
import { EmailConfigService } from './email-config.service';
import { CreateEmailConfigDto } from './dto/create-email-config.dto';
import { UpdateEmailConfigDto } from './dto/update-email-config.dto';
import { ApiBearerAuth } from '@nestjs/swagger';
import { AuthJwtGuard } from 'src/auth/guards/auth-jwt/auth-jwt.guard';

@Controller('email-config')
export class EmailConfigController {
  constructor(private readonly emailConfigService: EmailConfigService) { }

  @Post('send')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  async sendEmail(@Req() req: any, @Body() createEmailConfigDto: CreateEmailConfigDto) {
    if (!req.user.userInboxConfig._id) {
      throw new Error('userInboxConfigId is required');  
    }
    createEmailConfigDto.userInboxConfigId = req.user.userInboxConfig._id
    return this.emailConfigService.sendEmail(createEmailConfigDto,req.user);
  }

}
