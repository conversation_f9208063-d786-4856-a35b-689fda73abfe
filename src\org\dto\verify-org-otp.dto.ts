import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsString, Length } from 'class-validator';

export class VerifyOrgOtpDto {
  @ApiProperty({
    example: '<EMAIL>',
    description: 'The email address of the org',
  })
  @IsEmail()
  email: string;

  @ApiProperty({
    example: '123456',
    description: 'The 6-digit OTP code sent to the org email',
  })
  @IsString()
  @Length(6, 6)
  otpCode: string;
}