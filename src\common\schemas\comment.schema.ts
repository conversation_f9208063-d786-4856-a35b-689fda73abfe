import { Prop, SchemaFactory, Schem<PERSON> } from "@nestjs/mongoose";
import { HydratedDocument, Types } from "mongoose";
import { FileMetadata } from "src/file-upload/schemas/file-metadata.schema";
import { Org } from "src/org/schemas/org.schema";
import { Task } from "src/task/schemas/task.schema";
import { BasicUser } from "src/user/schemas/basic-user.schema";


export type CommentDocument = HydratedDocument<Comment>;

@Schema({ _id: true, timestamps: true })
export class Comment {

  @Prop({ type: String, required: false, trim: true })
  contents?: string;

  @Prop({
    type: [Types.ObjectId],
    default: [],
    required: false,
    ref: 'FileMetadata',
  })
  attachments?: FileMetadata[];

  @Prop({
    type: Types.ObjectId,
    ref: 'Org',
    required: false
  })
  org?: Types.ObjectId;

  @Prop({
    type: Types.ObjectId,
    ref: 'BasicUser',
    required: false
  })
  user?: Types.ObjectId;

  // @Prop({
  //   type: Types.ObjectId,
  //   ref: 'Country',
  //   required: false
  // })
  // country?: Types.ObjectId;

  @Prop({
    type: Types.ObjectId,
    ref: 'Region',
    required: false
  })
  region?: Types.ObjectId;

  @Prop({
    type: Types.ObjectId,
    ref: 'Task',
    required: false
  })
  taskId?: Types.ObjectId;

  @Prop({
    type: Types.ObjectId,
    ref: 'Contact',
    required: false
  })
  contact?: Types.ObjectId;

}

export const CommentSchema = SchemaFactory.createForClass(Comment);
