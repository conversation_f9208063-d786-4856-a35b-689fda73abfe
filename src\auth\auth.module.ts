import { Module, forwardRef } from '@nestjs/common';
import { PassportModule } from '@nestjs/passport';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { AuthService } from './auth.service';
import { AuthController } from './auth.controller';
import { JwtModule } from '@nestjs/jwt';
import { UserModule } from 'src/user/user.module';
import { MongooseModule } from '@nestjs/mongoose';
import { BasicUser, BasicUserSchema } from 'src/user/schemas/basic-user.schema';


@Module({
  imports: [
    ConfigModule,
    PassportModule,
    forwardRef(() => UserModule),

    // JwtModule.register({
    //   global: true,
    //   secret: 'SOME_SECRET_KEY',
    //   signOptions: { expiresIn: '1h' },
    // }),
    MongooseModule.forFeature([
      { name: BasicUser.name, schema: BasicUserSchema },
    ]),
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get<string>('SECRET_KEY'),
        signOptions: {
          // expiresIn: '1h' ,
          expiresIn: configService.get('TOKEN_EXPIRTY_PERIOD')
        },
      }),
      inject: [ConfigService],
    }),

    // forwardRef(() =>   UserModule),

  ],
  providers: [AuthService],
  controllers: [AuthController],
  exports: [AuthService],

})
export class AuthModule { }
