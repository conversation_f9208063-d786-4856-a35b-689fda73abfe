import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, HydratedDocument, Schema as MongooseSchema, Types } from 'mongoose';
import { Privilege } from 'src/privilege/schemas/privilege.schema';
import { Roles } from 'src/roles/schemas/roles.schema';


export type RolePrivilegeDocument = HydratedDocument<RolePrivilege>;
@Schema({ timestamps: true })
export class RolePrivilege {
    @Prop({ type: Types.ObjectId, ref: 'Roles', required: true, unique: true })
    roleId: Roles;

    @Prop({ type: [{ type: Types.ObjectId, ref: 'Privilege' }], required: true })
    privileges: Privilege[];
}

export const RolePrivilegeSchema = SchemaFactory.createForClass(RolePrivilege);
