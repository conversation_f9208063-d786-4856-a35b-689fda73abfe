import { IsArray, IsOptional, IsString } from "class-validator";

export class CreateProjectDto {
    @IsString()
    @IsOptional()
    name: string;

    @IsString()
    @IsOptional()
    description?: string;

    @IsOptional()
    startDate: Date;

    @IsOptional()
    endDate: Date;

    @IsOptional()
    budget: number;

    @IsOptional()
    currency: string;

    @IsOptional()
    projectType: 'Billable' | 'Non-Billable';

    @IsArray()
    @IsOptional()
    departments: string[];

    @IsOptional()
    client?: string;

    @IsOptional()
    org?: string;

    @IsOptional()
    projectManagerId: string;

    @IsOptional()
    isInternal?: boolean;
}