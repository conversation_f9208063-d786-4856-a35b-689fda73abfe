apiVersion: apps/v1
kind: Deployment
metadata:
  name: nestjs-app
  labels:
    app: nestjs-app
spec:
  replicas: 2
  selector:
    matchLabels:
      app: nestjs-app
  template:
    metadata:
      labels:
        app: nestjs-app
    spec:
      containers:
      - name: nestjs-app
        image: asia-south1-docker.pkg.dev/talsy-production/talsy/nest-app:v1.0.1
        ports:
        - containerPort: 80
        env:
        - name: NODE_ENV
          value: "production"
        - name: PORT
          value: "80"
        - name: MONGODB_URI
          valueFrom:
            secretKeyRef:
              name: talsy-secrets
              key: MONGODB_URI
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: talsy-secrets
              key: REDIS_URL
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: talsy-secrets
              key: REDIS_PASSWORD
              optional: true
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: talsy-secrets
              key: JWT_SECRET
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"