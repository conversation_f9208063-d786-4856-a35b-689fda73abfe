import { <PERSON><PERSON>, Schema, SchemaFactory } from "@nestjs/mongoose";
import { Types } from "mongoose";
import { Document } from 'mongoose';


@Schema()
export class AttendanceSession extends Document {
  @Prop({ type: Types.ObjectId, ref: 'Attendance' })
  attendanceId: Types.ObjectId;

  @Prop({ required: true })
  checkIn: Date;

  @Prop({ required: false })
  checkOut?: Date;

  @Prop({ type: Types.ObjectId, ref: 'Project', required: false })
  projectId?: Types.ObjectId;

  @Prop({ type: Types.ObjectId, ref: 'Task', required: false })
  taskId?: Types.ObjectId;
}

export const AttendanceSessionSchema = SchemaFactory.createForClass(AttendanceSession);