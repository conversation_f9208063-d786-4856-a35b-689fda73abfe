import { Module } from '@nestjs/common';
import { MeetingService } from './meeting.service';
import { MeetingGateway } from './meeting.gateway';
import { UserModule } from 'src/user/user.module';
import { AuthModule } from 'src/auth/auth.module';
// import { RealTimeMessagingModule } from 'src/real-time-messaging/real-time-messaging.module';
// import { RedisStoreService } from 'src/real-time-messaging/redis-store/redis-store.service';
import { Meeting, MeetingSchema } from './schemas/meeting.schema';
import { ConfigModule } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { MongooseModule } from '@nestjs/mongoose';
import { RedisStoreService } from './redis-store/redis-store.service';
import { MeetingController } from './meeting.controller';
import { EndpointsRolesModule } from 'src/endpoints-roles/endpoints-roles.module';
@Module({
  controllers: [MeetingController],
  imports: [
    UserModule,
    AuthModule,
    // RealTimeMessagingModule,
    ConfigModule,
    JwtModule, EndpointsRolesModule,
    MongooseModule.forFeature([{ name: Meeting.name, schema: MeetingSchema }])
  ],
  providers: [MeetingGateway, MeetingService, RedisStoreService],
  exports: [MeetingService]
})
export class MeetingModule {}
