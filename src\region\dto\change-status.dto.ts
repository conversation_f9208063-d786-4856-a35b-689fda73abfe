import { ApiProperty } from "@nestjs/swagger";
import { Transform, TransformFnParams } from "class-transformer";
import { IsMongoId, IsNotEmpty, IsString } from "class-validator";
import { sanitizeWithStyle } from "src/utils/sanitize-with-style";
import { CommentDto } from "src/common/dto/comment.dto";

export class ChangeStatusDto extends CommentDto {

  @ApiProperty({
    type: String,
    required: true,
    description: 'Id of the status',
  })
  @IsString()
  @IsNotEmpty()
  @IsMongoId()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  status: string;


}