import { Prop, SchemaFactory, Schema } from '@nestjs/mongoose';
import { Types } from 'mongoose';
import { JobAllocationBase, JobAllocationBaseSchema } from './job-allocation-base.schema';
import { JobAllocationType } from 'src/shared/constants';

@Schema({
    timestamps: true,
})
export class JobAllocationToFreelancers extends JobAllocationBase {
    @Prop({ required: true, type: Boolean, default: false })
    isAvailableInFreeLancerPool: boolean;

    // TODO: Name
    @Prop({ required: true, type: Number })
    reward: number;
}

export const JobAllocationToFreelancersSchema = JobAllocationBaseSchema.discriminator(
    JobAllocationType.FREELANCERS,  // Use enum instead of string
    SchemaFactory.createForClass(JobAllocationToFreelancers),
);