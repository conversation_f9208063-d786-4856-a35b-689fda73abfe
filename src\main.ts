import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import {
  FastifyAdapter,
  NestFastifyApplication,
} from '@nestjs/platform-fastify';
import { SwaggerModule, DocumentBuilder, SwaggerCustomOptions } from '@nestjs/swagger';
import compression from '@fastify/compress';
import { ValidationPipe } from '@nestjs/common';
import { join } from 'path';
// import { Logger } from 'nestjs-pino';
import { WinstonModule } from 'nest-winston';
import * as winston from 'winston';
import { RedisIoAdapter } from './adapters/redis.adapter';
import { ConfigService } from '@nestjs/config';

async function bootstrap() {
  try {
    console.log('Starting Talency API server...');
    console.log(`Node.js version: ${process.version}`);
    console.log(`Environment: ${process.env.NODE_ENV || 'development'}`);
    console.log(`Port: ${process.env.PORT || 3333}`);

    // Create a Winston logger instance
    const transports: winston.transport[] = [
      new winston.transports.Console({
        debugStdout: true,
        format: winston.format.combine(
          winston.format.timestamp(),
          winston.format.colorize(),
          winston.format.printf(({ level, message, timestamp }) => {
            return `${timestamp} ${level}: ${message}`;
          }),
        ),
      }),
    ];

    // Only add file transport if not in Cloud Run (where filesystem is read-only)
    if (process.env.NODE_ENV !== 'production' || !process.env.K_SERVICE) {
      try {
        transports.push(
          new winston.transports.File({
            filename: 'logs/app.log',
            format: winston.format.combine(
              winston.format.timestamp(),
              winston.format.json(),
            ),
            maxsize: 10000000, // Max size of each log file
            maxFiles: 10, // Max number of log files to keep
          })
        );
      } catch (error: any) {
        console.warn('Could not create file logger, continuing with console only:', error.message);
      }
    }

    const winstonLogger = winston.createLogger({
      levels: winston.config.syslog.levels,
      transports,
    });

  const app = await NestFactory.create<NestFastifyApplication>(
    AppModule,
    new FastifyAdapter(
      {
        logger: true
      }
    ),
    {
      logger: WinstonModule.createLogger({
        instance: winstonLogger,
      }),
    }
    // {
    //   bufferLogs: true
    // }
  );


    const configService = app.get(ConfigService);

    // Set up Redis adapter for WebSocket clustering (optional)
    const redisIoAdapter = new RedisIoAdapter(app, configService);
    try {
      await redisIoAdapter.connectToRedis();
      app.useWebSocketAdapter(redisIoAdapter);
    } catch (error: any) {
      console.warn('Redis connection failed, continuing without clustering:', error.message);
    }
  // read https://www.npmjs.com/package/nestjs-pino
  // app.useLogger(app.get(Logger));


  // https://docs.nestjs.com/openapi/other-features#global-prefix
  app.setGlobalPrefix('api');

  // read https://docs.nestjs.com/security/cors
  app.enableCors();

  // read https://docs.nestjs.com/techniques/compression
  await app.register(compression);

  // // read https://www.npmjs.com/package/nestjs-pino
  // app.useLogger(app.get(Logger));

  // read about helmet here - https://docs.nestjs.com/security/helmet#use-with-fastify
  // await app.register(helmet)

  // read warning section here - https://docs.nestjs.com/security/helmet#use-with-fastify
  // for now commented since css is not loading in index.html
  // await app.register(helmet, {
  //   contentSecurityPolicy: false,
  // });


  // read https://docs.nestjs.com/recipes/serve-static
  app.useStaticAssets({
    root: join(__dirname, '/public'),
    // root: join(__dirname ,  '/public'),
    prefix: '/'
  });


  const version = '0.0.341';
  const siteTitle = `Talency | APIs | v${version}`;

  const config = new DocumentBuilder()
    .setTitle(siteTitle)
    .setDescription('The APIs for Talency.')
    .setVersion(`${version}`)
    .addTag('Talency')
    .addBearerAuth()
    .build();

  const customOptions: SwaggerCustomOptions = {
    swaggerOptions: {
      persistAuthorization: true,
    },
    customSiteTitle: siteTitle,
  };
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('explorer', app, document, customOptions);

    // Remove unused swagger JSON variable
    // const swaggerJson = JSON.stringify(document);

    // Takes care of auto - validation as explained here - https://docs.nestjs.com/techniques/validation#auto-validation
    app.useGlobalPipes(new ValidationPipe({
      transform: true, // Enable automatic transformation
    }));

    const PORT = process.env.PORT || 3333;
    const HOST = process.env.NODE_ENV !== 'production' ? '127.0.0.1' : '0.0.0.0';
    // Start the server
    await app.listen(PORT, HOST);
    console.log(`✅ Application is running on: ${await app.getUrl()}`);
    console.log(`✅ Health check available at: ${await app.getUrl()}/health`);
    console.log(`✅ API documentation available at: ${await app.getUrl()}/explorer`);

  } catch (error: any) {
    console.error('❌ Failed to start the application:', error.message);
    console.error(error.stack);
    process.exit(1);
  }
}

// Start the application with proper error handling
bootstrap().catch((error) => {
  console.error('❌ Fatal error during application startup:', error);
  process.exit(1);
});
