import { Prop, Schema } from '@nestjs/mongoose';
import validator from 'validator';
import { HydratedDocument } from 'mongoose';
import { SchemaFactory } from '@nestjs/mongoose';


export type PermissionDocument = HydratedDocument<Permission>;

@Schema({
    timestamps: true
})
export class Permission {

    @Prop({
        required: [true, 'ACTION_CANNOT_BE_BLANK'],
        lowercase: true,
    })
    action: string;

    @Prop({
        required: true
    })
    subject: string;

   
}

export const PermissionSchema = SchemaFactory.createForClass(Permission);
