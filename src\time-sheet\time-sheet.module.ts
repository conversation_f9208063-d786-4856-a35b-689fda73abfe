import { Module } from '@nestjs/common';

import { ConfigModule } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { UserModule } from 'src/user/user.module';
import { EndpointsRolesModule } from 'src/endpoints-roles/endpoints-roles.module';
import { CommonModule } from 'src/common/common.module';
import { MongooseModule } from '@nestjs/mongoose';
import { TimeSheetController } from './time-sheet.controller';
import { TimeSheetSchema, TimeSheetSettingsSchema } from './schemas/time-sheet.schema';
import { TimeSheetService } from './time-sheet.service';
import { BasicUser, BasicUserSchema } from 'src/user/schemas/basic-user.schema';
import { Project, ProjectSchema } from 'src/projects/schemas/project.schema';
import { Invoice, InvoiceSchema } from 'src/invoices/schemas/invoice.schema';

@Module({
  controllers: [TimeSheetController],
  imports: [
    ConfigModule,
    JwtModule, EndpointsRolesModule,
    UserModule,
    CommonModule,
    MongooseModule.forFeature([{ name: 'TimeSheet', schema: TimeSheetSchema },
      {name:'TimeSheetSettings',schema:TimeSheetSettingsSchema},
    { name: BasicUser.name, schema: BasicUserSchema },
     { name: Invoice.name, schema: InvoiceSchema },
    { name: Project.name, schema: ProjectSchema }]),
  ],
  providers: [TimeSheetService],
  exports: [TimeSheetService],
})
export class TimeSheetModule { }
