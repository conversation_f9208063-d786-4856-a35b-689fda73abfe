// src/guards/org-access.guard.ts

import { Injectable, CanActivate, ExecutionContext, Logger, UnauthorizedException, ForbiddenException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { ObjectId } from 'mongoose'; // Import ObjectId from mongoose or appropriate library

@Injectable()
export class OrgAccessGuard implements CanActivate {

  private readonly logger = new Logger(OrgAccessGuard.name);

  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean | Promise<boolean> | Observable<boolean> {

    const request = context.switchToHttp().getRequest();
    const { user } = request;
    const orgIdFromParams = request.params.orgId; // Extract orgId from request parameters
    const orgIdFromUser = user?.org; // Extract orgId associated with user's org

    // Log org access guard activation
    this.logger.log(`Org Access Guard`);

    // Function to check if user has any associated org
    const hasOrgAccess = () => {
      return !!user?.org; // Check if user has any associated org
    };

    // If orgId exists in the request parameters, enforce org access check
    if (orgIdFromParams !== orgIdFromUser) {
      throw new ForbiddenException(`Forbidden, the user doesn't have access to the org with ID ${orgIdFromParams}.`);
    }    

    // Return true to allow access
    return true;
  }
}
