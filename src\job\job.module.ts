import { Module, forwardRef } from '@nestjs/common';
import { JobService } from './job.service';
import { JobController } from './job.controller';
import { MongooseModule } from '@nestjs/mongoose';
import { Job, JobSchema } from './schemas/job.schema';
import { ConfigModule } from '@nestjs/config';
import { UserModule } from 'src/user/user.module';
import { JwtModule } from '@nestjs/jwt';
import { StageModule } from 'src/stage/stage.module';
import { WorkflowModule } from 'src/workflow/workflow.module';
import { InterviewModule } from 'src/interview/interview.module';
import { OrgModule } from 'src/org/org.module';
import { PreferenceModule } from 'src/preferences/preference.module';
import { JobApplication } from 'src/job-application-form/schemas/job-application.schema';
import { ResumeModule } from 'src/resume/resume.module';
import { Offer, OfferSchema } from 'src/offer/schemas/offer.schema';
import { EndpointsRolesModule } from 'src/endpoints-roles/endpoints-roles.module';
import { JobAllocationModule } from 'src/job-allocation/job-allocation.module';
import { NotificationsModule } from 'src/notification/notifications.module';
@Module({
  controllers: [JobController],
  imports: [
    ConfigModule,
    JwtModule, EndpointsRolesModule,
    forwardRef(() => UserModule),
    StageModule,
    forwardRef(() => WorkflowModule),
    InterviewModule,
    PreferenceModule,
    ResumeModule,
    JobApplication,
    forwardRef(() => JobAllocationModule),
    forwardRef(() => OrgModule),
    forwardRef(() => NotificationsModule),
    MongooseModule.forFeature([
      { name: Offer.name, schema: OfferSchema }
    ]),
    MongooseModule.forFeatureAsync([
      {
        name: Job.name,
        imports: [ConfigModule],
        useFactory: () => {
          const schema = JobSchema;

          // Pre-save hook to generate the job code
          schema.pre('save', async function (next) {
            const job = this as any;

            if (!job.jobCode) {
              const date = new Date().toISOString().slice(0, 10).replace(/-/g, '');

              // Find the latest job with a jobCode starting with today's date
              const lastJob = await job.constructor.findOne({
                jobCode: new RegExp(`^TJ-${date}`)
              }).sort({ updatedAt: -1 });

              const nextSequenceNumber = lastJob
                ? parseInt(lastJob.jobCode.split('-')[2], 10) + 1
                : 1;

              job.jobCode = `TJ-${date}-${nextSequenceNumber.toString().padStart(3, '0')}`;
            }

            next();
          });

          return schema;
        },
        inject: [],
      },
    ]),
  ],
  providers: [JobService],
  exports: [JobService, MongooseModule],
})
export class JobModule { }
