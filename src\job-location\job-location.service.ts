import { BadRequestException, Injectable, InternalServerErrorException, Logger, NotFoundException } from '@nestjs/common';
import { CreateJobLocationDto } from './dto/create-job-location.dto';
import { UpdateJobLocationDto } from './dto/update-job-location.dto';
import { ConfigService } from '@nestjs/config';
import { JobLocation, JobLocationDocument } from './schemas/job-location.schema';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { JobsLocation } from './schemas/jobs-location.schema';
import { CreateJobsLocationDto } from './dto/create-jobs-location.dto';
import { UpdateJobsLocationDto } from './dto/update-jobs-location.dto';

@Injectable()
export class JobLocationService {

  private readonly logger = new Logger(JobLocationService.name);

  constructor(private configService: ConfigService, @InjectModel(JobLocation.name) private JobLocationModel: Model<JobLocation>,
    @InjectModel(JobsLocation.name) private JobsLocationModel: Model<JobsLocation>) { }


  async create(createJobLocationDto: CreateJobLocationDto) {
    try {
      const createdJobLocation = new this.JobLocationModel(createJobLocationDto);
      return await createdJobLocation.save();
    } catch (error) {
      this.logger.error(`Failed to create job location. ${error}`);
      throw new InternalServerErrorException(`Error while creating job location. ${error?.message}`);
    }
  }

  findAll(): Promise<JobLocationDocument[]> {
    return this.JobLocationModel.find().exec();
  }

  async findOne(jobLocationId: Types.ObjectId): Promise<JobLocationDocument> {
    try {
      const jobLocation = await this.JobLocationModel.findById(jobLocationId)
      if (!jobLocation) {
        throw new NotFoundException(`Job location not found with ID ${jobLocationId}`);
      }
      return jobLocation;
    } catch (error) {
      this.logger.error(error);
      this.logger.error(`An error occurred in fetching job location by Id ${jobLocationId}. ${error?.message}`);
      throw error;
    }
  }

  async findLocationByOrgId(org: any): Promise<JobLocationDocument[]> {
    const jobLocations = await this.JobLocationModel.find({ orgId: org })
    return jobLocations;
  }

  async update(jobLocationId: Types.ObjectId, updateJobLocationDto: UpdateJobLocationDto) {
    try {
      const jobLocation = await this.JobLocationModel.findById(jobLocationId)
      if (!jobLocation) {
        throw new NotFoundException(`Job location not found with ID ${jobLocationId}`);
      }
      return await this.JobLocationModel.findByIdAndUpdate(jobLocationId, updateJobLocationDto, { new: true });
    } catch (error) {
      this.logger.error(error);
      this.logger.error(`An error occurred while updating job location by ID ${jobLocationId}. ${error?.message}`);
      throw error;
    }
  }

  async remove(jobLocationId: Types.ObjectId) {
    try {
      const jobLocation = await this.JobLocationModel.findById(jobLocationId)
      if (!jobLocation) {
        throw new NotFoundException(`Job location not found with ID ${jobLocationId}`);
      }
      await this.JobLocationModel.deleteOne({ _id: jobLocationId });
      return { message: 'Job location deleted' };
    } catch (error) {
      this.logger.error(`An error occurred while deleting job location by ID ${jobLocationId}. ${error?.message}`);
      throw new InternalServerErrorException('An error occurred while deleting job location');
    }
  }

  //CURD operations for jobs-location
  async createJobsLocation(createJobLocationDto: CreateJobsLocationDto): Promise<JobsLocation> {
    try {
      const jobLocationData = {
        ...createJobLocationDto,
        city: new Types.ObjectId(createJobLocationDto.city),
        state: new Types.ObjectId(createJobLocationDto.state),
        country: new Types.ObjectId(createJobLocationDto.country),
      };
      return await this.JobsLocationModel.create(jobLocationData);
    } catch (error) {
      if (error) {
        throw new BadRequestException('Job location with the same city, state, and country already exists.');
      }
      throw error;
    }
  }

  async findAllJobsLocation(): Promise<JobsLocation[]> {
    try {
      return await this.JobsLocationModel.find().populate('city state country').exec();
    } catch (error) {
      throw new BadRequestException('Error retrieving job locations');
    }
  }
  
  // ✅ Get a single Job Location by ID with try-catch
  async findOneJobsLocation(id: string): Promise<JobsLocation> {
    try {
      const jobLocation = await this.JobsLocationModel.findById(id).populate('city state country').exec();
  
      if (!jobLocation) {
        throw new BadRequestException(`Job location with ID ${id} not found`);
      }
  
      return jobLocation;
    } catch (error) {
      if (error.kind === 'ObjectId') {
        throw new BadRequestException(`Invalid ID format: ${id}`);
      }
      throw new BadRequestException('Error retrieving job location');
    }
  }

  // ✅ Update Job Location by ID
  async updateJobsLocation(id: string, updateJobLocationDto: UpdateJobsLocationDto): Promise<JobsLocation> {
    try {
      const updatedData: any = { ...updateJobLocationDto };
      if (updateJobLocationDto.city) {
        updatedData.city = new Types.ObjectId(updateJobLocationDto.city);
      }
      if (updateJobLocationDto.state) {
        updatedData.state = new Types.ObjectId(updateJobLocationDto.state);
      }
      if (updateJobLocationDto.country) {
        updatedData.country = new Types.ObjectId(updateJobLocationDto.country);
      }
      const updatedJobLocation = await this.JobsLocationModel.findByIdAndUpdate(id, updatedData, { new: true });
      if (!updatedJobLocation) {
        throw new BadRequestException(`Job location with ID ${id} not found`);
      }
      return updatedJobLocation;
    } catch (error) {
      if (error.code === 11000) {
        throw new BadRequestException('A job location with the same city, state, and country already exists.');
      }
      throw error;
    }

  }

  // ✅ Delete Job Location by ID
  async removeJobsLocation(id: string): Promise<JobsLocation> {
    const deletedJobLocation = await this.JobsLocationModel.findByIdAndDelete(id);
    if (!deletedJobLocation) {
      throw new BadRequestException(`Job location with ID ${id} not found`);
    }
    return deletedJobLocation;
  }
}
