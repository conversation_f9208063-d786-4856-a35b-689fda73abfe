import { ApiHideProperty, ApiProperty } from '@nestjs/swagger';
import { Transform, TransformFnParams } from 'class-transformer';
import { IsString, IsBoolean, IsOptional, IsEmail, IsEnum, IsNotEmpty, IsMongoId } from 'class-validator';
import { sanitizeWithStyle } from "src/utils/sanitize-with-style";

export class CreateUserInboxConfigDto {
    @ApiProperty({
        type: String,
        required: true,
        description: 'Enter email of your account',
        format: 'email',
    })
    @IsNotEmpty()
    @IsEmail()
    @IsString()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value).toLowerCase())
    fromEmail: string;


    @ApiProperty({
        type: String,
        required: true,
        description: 'Full name of user'
    })
    @IsString()
    @IsNotEmpty()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    fromName: string;

    @ApiProperty({
        type: String,
        required: false,
        description: 'Imap host user'
    })
    // @IsString()
    // @IsNotEmpty()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    imapHost?: string;


    @ApiProperty({
        type: Number,
        required: false,
        description: 'Imap port user'
    })

    // @IsNotEmpty()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    imapPort?: number;

    @ApiProperty({
        type: String,
        required: false,
        description: 'SMTP host user'
    })
    // @IsString()
    // @IsNotEmpty()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    smtpHost?: string;

    @ApiProperty({
        type: Number,
        required: false,
        description: 'SMTP port user'
    })
    // @IsNotEmpty()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    smtpPort?: number;

    @ApiProperty({
        type: String,
        required: true,
        description: 'Username for IMAP/SMTP'
    })
    @IsString()
    @IsNotEmpty()
    userName: string;

    @ApiProperty({
        type: String,
        required: true,
        description: 'Password for IMAP/SMTP'
    })
    @IsString()
    @IsNotEmpty()
    password: string;

    @ApiProperty({
        type: Boolean,
        required: false,
        default: false,
        description: 'isEnale SSL'
    })
    @IsOptional()
    @IsBoolean()
    isEnableSSL?: boolean;


    @ApiProperty({
        type: String,
        required: false,
    })
    @IsOptional()
    @IsMongoId()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    org?: string;

    @ApiProperty({
        type: Boolean,
        required: false,
        default: false,
        description: 'is Org Default settings'
    })
    @IsOptional()
    @IsBoolean()
    isOrgDefault?: boolean;

    // @IsString()
    // @IsEnum(['Inbox', 'Sent', 'Deleted', 'Drafts'])
    // folder?: string;

    // @ApiProperty({
    //     type: String,
    //     required: false,
    //     description: 'subject to compose'
    // })
    // @IsString()
    // @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    // subject?: string;

    // @ApiProperty({
    //     type: String,
    //     required: false,
    //     description: 'Body of the mail'
    // })
    // @IsString()
    // @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    // body?: string;


    // @ApiProperty({
    //     type: String,
    //     required: false,
    //     description: 'Signature of the mail'
    // })
    // @IsString()
    // @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    // signature?: string;


    @ApiHideProperty()
    @IsOptional()
    createdBy?: string;

}