import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsArray, IsMongoId, IsOptional, IsString } from 'class-validator';
import { Transform } from 'class-transformer';

export class GetMembersDto {
  @ApiPropertyOptional({
    type: [String],
    description: 'Array of business unit IDs',
  })
  @IsOptional()
  @IsArray()
  @IsMongoId({ each: true })
  @Transform(({ value }) => (Array.isArray(value) ? value : [value])) // Ensure array format
  businessUnitIds?: string[];
}
