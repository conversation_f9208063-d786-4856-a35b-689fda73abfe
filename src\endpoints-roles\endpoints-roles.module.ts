import { Modu<PERSON> } from '@nestjs/common';
import { EndpointsRolesService } from './endpoints-roles.service';
import { EndpointsRolesController } from './endpoints-roles.controller';
import { JwtModule } from '@nestjs/jwt';
import { MongooseModule } from '@nestjs/mongoose';
import { EndpointsRoles, EndpointsRolesSchema } from './schemas/endpoints-roles.schema';
import { EndpointPermission, EndpointPermissionSchema } from 'src/endpoint-permissions/schemas/endpointpermissions.schema';
import { EndpointsUsers, EndpointsUsersSchema } from './schemas/endpoints-users.schema';
import { BasicUser, BasicUserSchema } from 'src/user/schemas/basic-user.schema';
@Module({
  imports: [JwtModule, MongooseModule.forFeature([{ name: EndpointsRoles.name, schema: EndpointsRolesSchema },
  { name: EndpointsUsers.name, schema: EndpointsUsersSchema },
  { name: EndpointPermission.name, schema: EndpointPermissionSchema },
  { name: BasicUser.name, schema: BasicUserSchema },
  ])],
  controllers: [EndpointsRolesController],
  providers: [EndpointsRolesService],
  exports: [EndpointsRolesService, MongooseModule],
})
export class EndpointsRolesModule { }
