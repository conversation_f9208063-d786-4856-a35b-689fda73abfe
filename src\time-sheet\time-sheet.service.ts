// src/timesheets/timesheet.service.ts
import {
  Injectable,
  Logger,
  BadRequestException,
  InternalServerErrorException,
  NotFoundException,
  ConsoleLogger,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, PopulateOptions, Types } from 'mongoose';


import { Project } from 'src/projects/schemas/project.schema';
import { BasicUser } from 'src/user/schemas/basic-user.schema';
import { FileMetadata } from 'src/file-upload/schemas/file-metadata.schema';
import { TimeSheet, TimeSheetSettings } from './schemas/time-sheet.schema';
import { CreateTimeSheetDto, TimesheetType } from './dto/create-time-sheet.dto';
import { UpdateTimeSheetDto } from './dto/update-time-sheet.dto';
import moment from 'moment';
import { SaveTimeSheetSettingsDto } from './dto/save-timeshhet-settings.dto';
import { Invoice, InvoiceDocument } from 'src/invoices/schemas/invoice.schema';


@Injectable()
export class TimeSheetService {
  private readonly logger = new Logger(TimeSheetService.name);

  constructor(
    @InjectModel(TimeSheet.name) private timesheetModel: Model<TimeSheet>,
    @InjectModel(Project.name) private projectModel: Model<Project>,
    @InjectModel('TimeSheetSettings') private settingsModel: Model<TimeSheetSettings>,
    @InjectModel(BasicUser.name) private basicUserModel: Model<BasicUser>,
    @InjectModel(Invoice.name) private invoiceModel: Model<InvoiceDocument>,
  ) { }

  /* 🕒 Time Calculation Utilities                                      */
  /* ------------------------------------------------------------------ */

  /**
   * Calculate hours worked based on start time, end time, and optional break duration
   */
  private calculateHoursWorked(startTime: string, endTime: string, breakDuration?: string): number {
    if (!startTime || !endTime) return 0;

    try {
      const start = this.parseTime(startTime);
      const end = this.parseTime(endTime);

      let totalMinutes = end - start;

      // Handle overnight shifts (end time is next day)
      if (totalMinutes < 0) {
        totalMinutes += 24 * 60; // Add 24 hours in minutes
      }

      // Subtract break duration if provided
      if (breakDuration) {
        const breakMinutes = this.parseTimeToMinutes(breakDuration);
        totalMinutes -= breakMinutes;
      }

      return Math.max(0, parseFloat((totalMinutes / 60).toFixed(2)));
    } catch (error) {
      this.logger.error(`Error calculating hours: ${error.message}`);
      return 0;
    }
  }

  /**
   * Parse time string (HH:mm or HH:mm:ss) to minutes since midnight
   */
  private parseTime(timeStr: string): number {
    const parts = timeStr.split(':');
    const hours = parseInt(parts[0], 10);
    const minutes = parseInt(parts[1], 10);

    if (isNaN(hours) || isNaN(minutes) || hours < 0 || hours > 23 || minutes < 0 || minutes > 59) {
      throw new Error(`Invalid time format: ${timeStr}`);
    }

    return hours * 60 + minutes;
  }

  /**
   * Parse duration string (HH:mm) to minutes
   */
  private parseTimeToMinutes(durationStr: string): number {
    const parts = durationStr.split(':');
    const hours = parseInt(parts[0], 10);
    const minutes = parseInt(parts[1], 10);

    if (isNaN(hours) || isNaN(minutes)) {
      throw new Error(`Invalid duration format: ${durationStr}`);
    }

    return hours * 60 + minutes;
  }


  /* ------------------------------------------------------------------ */
  /* 🛠️ Create                                                          */
  /* ------------------------------------------------------------------ */

  async create(user: any, dto: CreateTimeSheetDto): Promise<TimeSheet> {
    const orgId = user?.org?._id;
    const projectId = dto.projectId ?? null;

    const settings = await this.getSettings(orgId, projectId);

    const roundToTwo = (val: number) => Number(val.toFixed(2));


    if (!settings) throw new BadRequestException('Timesheet settings not found');
    // 🚫 Prevent duplicate Daily timesheets
    if (dto.timesheetType === TimesheetType.DAILY) {
      const conflictQuery: any = {
        orgId,
        employee: new Types.ObjectId(user._id),
        date: dto.date,
        timesheetType: TimesheetType.DAILY,
      };

      if (Array.isArray(dto.projectId) && dto.projectId.length > 0) {
        conflictQuery.projectId = { $all: dto.projectId };
      } else {
        conflictQuery.$or = [
          { projectId: { $exists: false } },
          { projectId: { $size: 0 } }
        ];
      }

      const existing = await this.timesheetModel.findOne(conflictQuery);
      if (existing) {
        throw new BadRequestException(`A daily timesheet already exists for this employee on ${dto.date} .`);
      }
    }
    // Weekly Timesheet Auto-Fill Logic
    if (dto.timesheetType === TimesheetType.WEEKLY) {
      if (!dto.weeklyEntries?.length) {
        const targetMonth = dto.month ? moment(dto.month, 'YYYY-MM') : moment();
        const startDay = settings.monthlyCycleStartDay ?? 1;
        const endDay = settings.monthlyCycleEndDay ?? targetMonth.daysInMonth();

        let cycleStart = targetMonth.clone().date(startDay).startOf('day');
        let cycleEnd = targetMonth.clone().date(endDay).startOf('day');

        if (endDay < startDay) {
          cycleEnd = targetMonth.clone().add(1, 'month').date(endDay).startOf('day');
        }

        const weekStartDayIndex = moment().day(settings.weekStartDay).day();
        let current = cycleStart.clone();
        while (current.day() !== weekStartDayIndex) {
          current.add(1, 'day');
        }

        const entries = [];
        while (current.isSameOrBefore(cycleEnd)) {
          const weekStart = current.clone();
          const weekEnd = current.clone().add(6, 'days');

          entries.push({
            weekLabel: `Week of ${weekStart.format('YYYY-MM-DD')}`,
            weekStartDate: weekStart.toDate(),
            weekEndDate: weekEnd.toDate(),
            days: Array.from({ length: 7 }, (_, i) => {
              const day = weekStart.clone().add(i, 'days');
              const isWeekOff = settings.weekends.includes(day.format('dddd'));
              const isInCycle = day.isSameOrAfter(cycleStart) && day.isSameOrBefore(cycleEnd);
              return {
                day: day.format('dddd'),
                isWeekOff,
                hoursWorked: 0,
                isInCycle,
              };
            }),
            totalWeekHours: 0,
          });

          current = current.add(1, 'week');
        }

        dto.weeklyEntries = entries;
        dto.startDate = cycleStart.toDate();
        dto.endDate = cycleEnd.toDate();
      }

      // ✅ This part is common: recalculate totals regardless of whether auto-filled or provided
      let grandTotal = 0;
      let billableDays = 0;

      dto.weeklyEntries.forEach((week) => {
        const totalWeekHours = roundToTwo(
          week.days.reduce((sum, d) => sum + (d.hoursWorked ?? 0), 0)
        );
        week.totalWeekHours = totalWeekHours;
        grandTotal += totalWeekHours;

        billableDays += week.days.filter(
          (d) => !d.isWeekOff && (d.hoursWorked ?? 0) > 0
        ).length;
      });

      dto.hoursWorked = roundToTwo(grandTotal);
      dto.totalBillableDays = billableDays;
      dto.billableDays = billableDays;
    }


    // Monthly calculations (only default logic, settings already checked)
    // if (dto.timesheetType === TimesheetType.MONTHLY) {
    //   if (!dto.month || dto.billableDays == null || dto.leaveDays == null) {
    //     throw new BadRequestException('Monthly timesheets require month, billableDays, and leaveDays.');
    //   }
    //   const targetMonth = moment(dto.month, 'YYYY-MM');
    //   const startDay = settings.monthlyCycleStartDay ?? 1;
    //   const endDay = settings.monthlyCycleEndDay ?? targetMonth.daysInMonth();

    //   let cycleStart = targetMonth.clone().date(startDay).startOf('day');
    //   let cycleEnd = targetMonth.clone().date(endDay).startOf('day');

    //   if (endDay < startDay) {
    //     cycleEnd = targetMonth.clone().add(1, 'month').date(endDay).startOf('day');
    //   }

    //   dto.startDate = cycleStart.toDate();
    //   dto.endDate = cycleEnd.toDate();

    //   // Auto-fill monthlyEntries if not provided
    //   if (!dto.monthlyEntries?.length) {
    //     const days = [];
    //     let current = cycleStart.clone();
    //     while (current.isSameOrBefore(cycleEnd)) {
    //       const isWeekOff = settings.weekends.includes(current.format('dddd'));
    //       const isInCycle = current.isSameOrAfter(cycleStart) && current.isSameOrBefore(cycleEnd);

    //       days.push({
    //         date: current.toISOString(), // ✅ convert to string
    //         day: current.format('dddd'),
    //         isWeekOff,
    //         isInCycle,
    //         hoursWorked: 0,
    //       });


    //       current.add(1, 'day');
    //     }

    //     dto.monthlyEntries = days;
    //   }


    //   // Calculate total days
    //   let billableDays = 0;
    //   let leaveDays = 0;
    //   let totalHoursWorked = 0;
    //   const today = moment().startOf("day");

    //   dto.monthlyEntries.forEach((d) => {
    //     const entryDate = moment(d.date).startOf("day");

    //     if (!d.isWeekOff && d.isInCycle) {
    //       if ((d.hoursWorked ?? 0) > 0) {
    //         billableDays++;
    //         totalHoursWorked += d.hoursWorked ?? 0;
    //       } else if (entryDate.isSameOrBefore(today)) {
    //         leaveDays++;
    //       }
    //     }
    //   });

    //   const totalBillableDays = billableDays;
    //   const hourlyRate = dto.hourlyRate || 0;
    //   const overtimeRate = dto.overtimeHourlyRate || 0;
    //   const overtimeHours = dto.overtimeHours || 0;

    //   dto.billableDays = billableDays;
    //   dto.leaveDays = leaveDays;
    //   dto.totalBillableDays = totalBillableDays;
    //   dto.totalBillableAmount = (totalBillableDays * hourlyRate) + (overtimeHours * overtimeRate);
    //   dto.hoursWorked = roundToTwo(totalHoursWorked);
    // }

     if (dto.timesheetType === TimesheetType.MONTHLY) {
    if (!dto.month) {
      throw new BadRequestException('Monthly timesheets require month.');
    }

    const targetMonth = moment(dto.month, 'YYYY-MM');
    const startDay = settings.monthlyCycleStartDay ?? 1;
    const endDay = settings.monthlyCycleEndDay ?? targetMonth.daysInMonth();

    let cycleStart = targetMonth.clone().date(startDay).startOf('day');
    let cycleEnd = targetMonth.clone().date(endDay).startOf('day');
    if (endDay < startDay) {
      cycleEnd = targetMonth.clone().add(1, 'month').date(endDay).startOf('day');
    }

    dto.startDate = cycleStart.toDate();
    dto.endDate = cycleEnd.toDate();

    // Auto-fill monthlyEntries if not provided
    if (!dto.monthlyEntries?.length) {
      const days = [];
      let current = cycleStart.clone();
      while (current.isSameOrBefore(cycleEnd)) {
        const isWeekOff = settings.weekends.includes(current.format('dddd'));
        const isInCycle = current.isSameOrAfter(cycleStart) && current.isSameOrBefore(cycleEnd);

        days.push({
          date: current.toISOString(),
          day: current.format('dddd'),
          isWeekOff,
          isInCycle,
          hoursWorked: 0,
        });

        current.add(1, 'day');
      }

      dto.monthlyEntries = days;
    }

    const hourlyRate = dto.hourlyRate || 0;
    const overtimeRate = dto.overtimeHourlyRate || 0;
    const overtimeHours = dto.overtimeHours || 0;

    // 🧠 Use provided billableDays if passed
    if (dto.billableDays != null && dto.leaveDays != null) {
      dto.totalBillableDays = dto.totalBillableDays ?? dto.billableDays;
      dto.totalBillableAmount = (dto.totalBillableDays * hourlyRate) + (overtimeHours * overtimeRate);
    } else {
      // Recalculate only if needed
      let billableDays = 0;
      let leaveDays = 0;
      let totalHoursWorked = 0;
      const today = moment().startOf("day");

      dto.monthlyEntries.forEach((d) => {
        const entryDate = moment(d.date).startOf("day");

        if (!d.isWeekOff && d.isInCycle) {
          if ((d.hoursWorked ?? 0) > 0) {
            billableDays++;
            totalHoursWorked += d.hoursWorked ?? 0;
          } else if (entryDate.isSameOrBefore(today)) {
            leaveDays++;
          }
        }
      });

      dto.billableDays = billableDays;
      dto.leaveDays = leaveDays;
      dto.totalBillableDays = billableDays;
      dto.totalBillableAmount = (billableDays * hourlyRate) + (overtimeHours * overtimeRate);
      dto.hoursWorked = roundToTwo(totalHoursWorked);
    }
  }

    // Daily hours calculation
    if (dto.timesheetType === TimesheetType.DAILY) {
      if (!dto.isWeekOff && dto.startTime && dto.endTime) {
        dto.hoursWorked = this.calculateHoursWorked(dto.startTime, dto.endTime);

      } else if (dto.isWeekOff) {
        dto.hoursWorked = 0;
      }
    }

    dto.createdBy = user._id.toString();

    const doc = new this.timesheetModel({
      ...dto,
      orgId,
      employee: new Types.ObjectId(dto.createdBy),
      projectId: Array.isArray(dto.projectId)
        ? dto.projectId.map((id) => new Types.ObjectId(id))
        : dto.projectId
          ? [new Types.ObjectId(dto.projectId)]
          : [],
      taskId: Array.isArray(dto.taskId)
        ? dto.taskId.map((id) => new Types.ObjectId(id))
        : dto.taskId
          ? [new Types.ObjectId(dto.taskId)]
          : [],
      attachments: dto.attachments?.map((id) => new Types.ObjectId(id)) ?? [],
    });

    await doc.save();
    this.logger.log(`Timesheet created: ${doc._id}`);
    // Sync daily to weekly after save



    if (dto.timesheetType === TimesheetType.DAILY) {
      const hasProjects = Array.isArray(doc.projectId) && doc.projectId.length > 0;
      const firstProjectId = Array.isArray(doc.projectId) && doc.projectId.length > 0
        ? doc.projectId[0].toString()
        : undefined;
      const workDate = doc.date ? new Date(doc.date) : null;

      // Skip sync if either projectId or date is missing
      if (workDate) {
        await this.syncDailyToWeekly({
          orgId,
          employeeId: doc.employee.toString(),
          projectId: firstProjectId,
          date: workDate,
          hoursWorked: roundToTwo(doc.hoursWorked || 0)

        });

        await this.syncWeeklyToMonthly(orgId, doc.employee.toString(), workDate, firstProjectId);
      } else {
        this.logger.log(
          `Skipping weekly sync for timesheet ${doc._id}: ${!firstProjectId ? 'Missing projectId' : ''
            } ${!workDate ? 'Missing date' : ''}`.trim()
        );
      }
    }

    return doc;
  }

  getCycleRange(settings: any, targetMonth: moment.Moment): { cycleStart: moment.Moment, cycleEnd: moment.Moment } {
    const startDay = settings.monthlyCycleStartDay ?? 1;
    const endDay = settings.monthlyCycleEndDay ?? targetMonth.daysInMonth();

    let cycleStart = targetMonth.clone().date(startDay).startOf('day');
    let cycleEnd = targetMonth.clone().date(endDay).startOf('day');

    if (endDay < startDay) {
      cycleEnd = targetMonth.clone().add(1, 'month').date(endDay).startOf('day');
    }

    return { cycleStart, cycleEnd };
  }


  private async syncDailyToWeekly({
    orgId,
    employeeId,
    projectId,
    taskId,
    date,
    hoursWorked,
  }: {
    orgId: string;
    employeeId: string;
    projectId?: string;
    taskId?: string;
    date: Date;
    hoursWorked: number;
  }) {
    const dateStr = moment(date).format("YYYY-MM-DD");
    const monthStr = moment(date).format("YYYY-MM");
    const query: any = {
      orgId,
      employee: employeeId,
      timesheetType: TimesheetType.WEEKLY,
      month: monthStr,
    };

    // Properly handle optional projectId
    if (projectId) {
      query.projectId = { $in: [projectId] };
    } else {
      query.$or = [
        { projectId: { $exists: false } },
        { projectId: { $size: 0 } }
      ];
    }

    if (taskId) {
      query.taskId = { $in: [taskId] };
    } else {
      query.$or = [
        { taskId: { $exists: false } },
        { taskId: { $size: 0 } }
      ];
    }

    let weeklyTimesheet = await this.timesheetModel.findOne(query);

    let cycleStart: moment.Moment;
    let cycleEnd: moment.Moment;

    const settings = await this.getSettings(orgId, projectId ?? null);
    if (!settings) return;

    // Compute cycle range (always required for isInCycle)
    const startDay = settings.monthlyCycleStartDay ?? 1;
    const endDay = settings.monthlyCycleEndDay ?? moment(date).daysInMonth();

    cycleStart = moment(monthStr).date(startDay).startOf("day");
    cycleEnd = moment(monthStr).date(endDay).startOf("day");

    if (endDay < startDay) {
      cycleEnd = moment(monthStr).add(1, "month").date(endDay).startOf("day");
    }


    // If no existing weekly timesheet, create it using settings
    if (!weeklyTimesheet) {
      const settings = await this.getSettings(orgId, projectId ?? null);
      if (!settings) return;

      const startDay = settings.monthlyCycleStartDay ?? 1;
      const endDay = settings.monthlyCycleEndDay ?? moment(date).daysInMonth();

      let cycleStart = moment(monthStr).date(startDay).startOf("day");
      let cycleEnd = moment(monthStr).date(endDay).startOf("day");

      if (endDay < startDay) {
        cycleEnd = moment(monthStr).add(1, "month").date(endDay).startOf("day");
      }

      const weekStartDayIndex = moment().day(settings.weekStartDay).day();
      let current = cycleStart.clone();
      while (current.day() !== weekStartDayIndex) {
        current.subtract(1, "day");
      }

      const entries = [];
      while (current.isSameOrBefore(cycleEnd)) {
        const weekStart = current.clone();
        const weekEnd = current.clone().add(6, "days");

        const days = Array.from({ length: 7 }, (_, i) => {
          const day = weekStart.clone().add(i, "days");
          const isInCycle = day.isSameOrAfter(cycleStart) && day.isSameOrBefore(cycleEnd);
          const isWeekOff = settings.weekends.includes(day.format("dddd"));
          return {
            day: day.format("dddd"),
            date: day.format("YYYY-MM-DD"),
            isWeekOff,
            hoursWorked: 0,
            isInCycle,
          };
        });

        entries.push({
          weekLabel: `Week ${weekStart.format("MMM D")} - ${weekEnd.format("MMM D")}`,
          weekStartDate: weekStart.format("YYYY-MM-DD"),
          weekEndDate: weekEnd.format("YYYY-MM-DD"),
          days,
          totalWeekHours: 0,
        });

        current = current.add(1, "week");
      }

      weeklyTimesheet = new this.timesheetModel({
        orgId,
        employee: employeeId,
        projectId: projectId ? [projectId] : [],

        timesheetType: TimesheetType.WEEKLY,
        cycleStartDate: cycleStart.toDate(),
        cycleEndDate: cycleEnd.toDate(),
        weeklyEntries: entries,
        month: monthStr,
      });
      await weeklyTimesheet.save();
      this.logger.log(`Weekly timesheet created for employee ${employeeId}, month ${monthStr}`);

    }

    let isUpdated = false;

    if (!weeklyTimesheet?.weeklyEntries?.length) return;

    for (const week of weeklyTimesheet.weeklyEntries) {
      for (const d of week.days) {
        if (moment(d.date).format("YYYY-MM-DD") === dateStr) {
          d.hoursWorked = Number(hoursWorked.toFixed(2));

          // ✅ Ensure isInCycle is set correctly even for older records
          const currentDate = moment(d.date, "YYYY-MM-DD");
          d.isInCycle = currentDate.isSameOrAfter(cycleStart) &&
            currentDate.isSameOrBefore(cycleEnd);

          isUpdated = true;

          // Recalculate totalWeekHours
          week.totalWeekHours = Number(
            week.days.reduce(
              (sum, day) => {
                const isValid = day.isInCycle && !day.isWeekOff;
                return sum + (isValid ? (day.hoursWorked || 0) : 0);
              },
              0
            ).toFixed(2)
          );

          break;
        }
      }

      if (isUpdated) break;
    }


    if (isUpdated) {
      // Recalculate full timesheet hours
      weeklyTimesheet.hoursWorked = Number(
        weeklyTimesheet.weeklyEntries.reduce(
          (sum, week) => sum + (week.totalWeekHours || 0),
          0
        ).toFixed(2)
      );
      await weeklyTimesheet.save();
    }
  }

  // async syncWeeklyToMonthly(orgId: string, employeeId: string, date: Date, projectId?: string) {
  //   const month = date.toISOString().substring(0, 7); // "2025-07"

  //   const query: any = {
  //     orgId,
  //     employee: employeeId,
  //     timesheetType: TimesheetType.WEEKLY,
  //     month,
  //   };

  //   // Handle projectId properly
  //   if (projectId) {
  //     query.projectId = { $in: [new Types.ObjectId(projectId)] };
  //   } else {
  //     query.$or = [
  //       { projectId: { $exists: false } },
  //       { projectId: { $size: 0 } }
  //     ];
  //   }

  //   const weeklyTimesheet = await this.timesheetModel.findOne(query);

  //   if (!weeklyTimesheet || !weeklyTimesheet.weeklyEntries?.length) {
  //     this.logger.log(`No weekly timesheet found for sync to monthly`);
  //     return;
  //   }

  //   // Calculate totals from weekly data
  //   const totalHoursWorked = weeklyTimesheet.hoursWorked || 0;
  //   let billableDays = 0;
  //   let leaveDays = 0;

  //   // weeklyTimesheet.weeklyEntries.forEach((week) => {
  //   //   week.days.forEach((day) => {
  //   //     if (!day.isWeekOff && day.isInCycle) {
  //   //       if ((day.hoursWorked ?? 0) > 0) {
  //   //         billableDays++;
  //   //         console.log("Billable Day:", day.date, day.hoursWorked);
  //   //       } else {
  //   //         leaveDays++;
  //   //         console.log("Leave Day:", day.date);
  //   //       }
  //   //     }
  //   //   });
  //   // });

  //   const today = moment().startOf("day");


  //   weeklyTimesheet.weeklyEntries.forEach((week) => {
  //     week.days.forEach((day) => {
  //       const dayDate = moment(day.date).startOf("day");

  //       if (!day.isWeekOff && day.isInCycle) {
  //         if ((day.hoursWorked ?? 0) > 0) {
  //           billableDays++; // ✅ Allow billable even for future days

  //         } else if (dayDate.isSameOrBefore(today)) {
  //           // ❌ Only count as leave if it's in the past or today
  //           leaveDays++;

  //         }
  //       }
  //     });
  //   });


  //   const totalBillableDays = billableDays;



  //   // Get rates from the first available source (could be from settings or weekly timesheet)
  //   const hourlyRate = weeklyTimesheet.hourlyRate ?? 0;
  //   const overtimeRate = weeklyTimesheet.overtimeHourlyRate ?? 0;
  //   const overtimeHours = weeklyTimesheet.overtimeHours ?? 0;

  //   const totalBillableAmount = (totalBillableDays * hourlyRate) + (overtimeHours * overtimeRate);

  //   const monthlyQuery: any = {
  //     orgId,
  //     employee: employeeId,
  //     timesheetType: TimesheetType.MONTHLY,
  //     month,
  //   };

  //   if (projectId) {
  //     monthlyQuery.projectId = { $in: [new Types.ObjectId(projectId)] };
  //   } else {
  //     monthlyQuery.$or = [
  //       { projectId: { $exists: false } },
  //       { projectId: { $size: 0 } },
  //     ];
  //   }

  //   await this.timesheetModel.findOneAndUpdate(
  //     monthlyQuery,
  //     {
  //       $set: {
  //         orgId,
  //         employee: employeeId,
  //         timesheetType: TimesheetType.MONTHLY,
  //         month,
  //         projectId: projectId ? [new Types.ObjectId(projectId)] : [],
  //         billableDays,
  //         leaveDays,
  //         totalBillableDays,
  //         totalBillableAmount,
  //         hoursWorked: totalHoursWorked,
  //         hourlyRate,
  //         overtimeHourlyRate: overtimeRate,
  //         overtimeHours,
  //       },
  //     },
  //     { upsert: true, new: true }
  //   );


  //   this.logger.log(`Monthly timesheet synced for employee ${employeeId}, month ${month}`);
  // }

  async syncWeeklyToMonthly(orgId: string, employeeId: string, date: Date, projectId?: string) {
    const month = date.toISOString().substring(0, 7); // "2025-07"

    const query: any = {
      orgId,
      employee: employeeId,
      timesheetType: TimesheetType.WEEKLY,
      month,
    };

    // Handle projectId properly
    if (projectId) {
      query.projectId = { $in: [new Types.ObjectId(projectId)] };
    } else {
      query.$or = [
        { projectId: { $exists: false } },
        { projectId: { $size: 0 } }
      ];
    }

    const weeklyTimesheet = await this.timesheetModel.findOne(query);

    if (!weeklyTimesheet || !weeklyTimesheet.weeklyEntries?.length) {
      this.logger.log(`No weekly timesheet found for sync to monthly`);
      return;
    }

    // Aggregate data from weekly to monthly
    const totalHoursWorked = Number((weeklyTimesheet.hoursWorked || 0).toFixed(2));
    let billableDays = 0;
    let leaveDays = 0;
    const today = moment().startOf("day");

    // 💡 New array to collect daily entries for monthly view
    const monthlyEntries: any = [];

    weeklyTimesheet.weeklyEntries.forEach((week) => {
      week.days.forEach((day) => {
        const dayDate = moment(day.date).startOf("day");
        const hours = Number((day.hoursWorked ?? 0).toFixed(2));

        if (!day.isWeekOff && day.isInCycle) {
          if (hours > 0) {
            billableDays++;
          } else if (dayDate.isSameOrBefore(today)) {
            leaveDays++;
          }
        }

        monthlyEntries.push({
          date: day.date ? moment(day.date).toISOString() : null,
          hoursWorked: hours,
          isWeekOff: day.isWeekOff ?? false,
          isInCycle: day.isInCycle ?? false,

        });
      });
    });

    const totalBillableDays = billableDays;

    // Get rates
    const hourlyRate = weeklyTimesheet.hourlyRate ?? 0;
    const overtimeRate = weeklyTimesheet.overtimeHourlyRate ?? 0;
    const overtimeHours = weeklyTimesheet.overtimeHours ?? 0;

    const totalBillableAmount = (totalBillableDays * hourlyRate) + (overtimeHours * overtimeRate);

    const monthlyQuery: any = {
      orgId,
      employee: employeeId,
      timesheetType: TimesheetType.MONTHLY,
      month,
    };

    if (projectId) {
      monthlyQuery.projectId = { $in: [new Types.ObjectId(projectId)] };
    } else {
      monthlyQuery.$or = [
        { projectId: { $exists: false } },
        { projectId: { $size: 0 } },
      ];
    }

    await this.timesheetModel.findOneAndUpdate(
      monthlyQuery,
      {
        $set: {
          orgId,
          employee: employeeId,
          timesheetType: TimesheetType.MONTHLY,
          month,
          projectId: projectId ? [new Types.ObjectId(projectId)] : [],
          billableDays,
          leaveDays,
          totalBillableDays,
          totalBillableAmount,
          hoursWorked: totalHoursWorked,
          hourlyRate,
          overtimeHourlyRate: overtimeRate,
          overtimeHours,
          monthlyEntries, // ✅ Store the expanded view of all cycle days
        },
      },
      { upsert: true, new: true }
    );

    this.logger.log(`Monthly timesheet synced for employee ${employeeId}, month ${month}`);
  }


  async findAllByEmployee(employeeId: string, employee?: string, date?: string): Promise<TimeSheet[]> {
    const objectId = new Types.ObjectId(employeeId);

    const query: any = {
      $or: [
        { employee: employeeId },
        { employee: objectId }
      ]
    };

    // ✅ If a specific date is provided, filter Daily timesheets with matching date
    if (date) {
      const dayStart = new Date(date);
      const dayEnd = new Date(date);
      dayEnd.setHours(23, 59, 59, 999);

      query.timesheetType = TimesheetType.DAILY;
      query.date = {
        $gte: dayStart,
        $lte: dayEnd
      };
    }

    return this.timesheetModel
      .find(query)
      .populate(this.populateOptions())
      .sort({ createdAt: -1 })
      .exec();
  }


  async findOne(id: string): Promise<TimeSheet> {
    const ts = await this.timesheetModel
      .findById(id)
      .populate(this.populateOptions())
      .exec();
    if (!ts) throw new NotFoundException('Timesheet not found');
    return ts;
  }

  /*  Update   */

  async update(
    id: string,
    updateDto: UpdateTimeSheetDto,
  ): Promise<TimeSheet> {
    const existing = await this.timesheetModel.findById(id);
    if (!existing) throw new NotFoundException('Timesheet not found');

    if (!updateDto.timesheetType) {
      updateDto.timesheetType = existing.timesheetType as TimesheetType;
    }

    // Recalculate hours for daily timesheet
    if (
      updateDto.timesheetType === TimesheetType.DAILY ||
      (!updateDto.timesheetType && updateDto.startTime && updateDto.endTime)
    ) {
      const startTime = updateDto.startTime ?? existing.startTime;
      const endTime = updateDto.endTime ?? existing.endTime;


      if (!updateDto.isWeekOff && startTime && endTime) {
        updateDto.hoursWorked = this.calculateHoursWorked(
          startTime,
          endTime,
        );
      } else if (updateDto.isWeekOff) {
        updateDto.hoursWorked = 0;
      }
      // ⏱ Include overtime in total amount
      const hourlyRate = updateDto.hourlyRate ?? existing.hourlyRate ?? 0;
      const overtimeRate = updateDto.overtimeHourlyRate ?? existing.overtimeHourlyRate ?? 0;
      const overtimeHours = updateDto.overtimeHours ?? existing.overtimeHours ?? 0;

      updateDto.totalBillableAmount =
        (updateDto.hoursWorked ?? 0) * hourlyRate +
        overtimeHours * overtimeRate;

    }

    // Validate timesheet structure
    if (updateDto.timesheetType) this.validateByType(updateDto);

    // ⏱️ Weekly: Recompute week totals + full timesheet totals
    if (
      updateDto.timesheetType !== TimesheetType.DAILY &&
      updateDto.weeklyEntries
    ) {
      let totalWorkedHours = 0;
      let billableDayCount = 0;



      updateDto.weeklyEntries = updateDto.weeklyEntries.map((w, i) => {
        const roundedtotal = w.days.reduce((sum, d) => {
          const worked = d?.isWeekOff ? 0 : d?.hoursWorked || 0;
          if (!d?.isWeekOff && worked > 0) billableDayCount++;
          return sum + worked;
        }, 0);
        const total = Number(roundedtotal.toFixed(2));
        totalWorkedHours += total;
        return { ...w, totalWeekHours: total };
      });



      updateDto.hoursWorked = totalWorkedHours;
      updateDto.billableDays = billableDayCount;
      updateDto.totalBillableDays = updateDto.leaveDays != null
        ? billableDayCount - updateDto.leaveDays
        : billableDayCount;
      // 🔁 Add overtime handling
      const hourlyRate = updateDto.hourlyRate ?? existing.hourlyRate ?? 0;
      const overtimeRate = updateDto.overtimeHourlyRate ?? existing.overtimeHourlyRate ?? 0;
      const overtimeHours = updateDto.overtimeHours ?? existing.overtimeHours ?? 0;

      updateDto.totalBillableAmount =
        updateDto.totalBillableDays * hourlyRate +
        overtimeHours * overtimeRate;

    }


    // 📅 Monthly: Recompute billable days and total amount
    if (updateDto.timesheetType === TimesheetType.MONTHLY) {
      if (
        updateDto.month == null ||
        updateDto.billableDays == null ||
        updateDto.leaveDays == null
      ) {
        throw new BadRequestException(
          'Monthly timesheets require month, billableDays, and leaveDays.'
        );
      }

      updateDto.totalBillableDays = updateDto.billableDays - updateDto.leaveDays;

      const hourlyRate = updateDto.hourlyRate ?? existing.hourlyRate ?? 0;
      const overtimeHourlyRate = updateDto.overtimeHourlyRate ?? existing.overtimeHourlyRate ?? 0;
      const overtimeHours = updateDto.overtimeHours ?? existing.overtimeHours ?? 0;

      updateDto.totalBillableAmount =
        updateDto.totalBillableDays * hourlyRate + overtimeHours * overtimeHourlyRate;
    }

    const ts = await this.timesheetModel.findByIdAndUpdate(id, updateDto, {
      new: true,
    });

    if (!ts) throw new NotFoundException('Timesheet not found');

    // 🔁 Sync daily update to weekly timesheet if applicable
    if (ts.timesheetType === TimesheetType.DAILY) {
      const firstProjectId = Array.isArray(ts.projectId) && ts.projectId.length > 0
        ? ts.projectId[0].toString()
        : undefined;
      const workDate = ts.date ? new Date(ts.date) : null;
      const orgId = ts.orgId?.toString();

      if (!orgId) {
        throw new BadRequestException('User organization not found');
      }

      if (workDate) {
        await this.syncDailyToWeekly({
          orgId: orgId,
          employeeId: ts.employee.toString(),
          projectId: firstProjectId,
          date: workDate,
          hoursWorked: Number((ts.hoursWorked || 0).toFixed(2)),
        });

        // ✅ Sync updated weekly totals into monthly
        await this.syncWeeklyToMonthly(
          orgId,
          ts.employee.toString(),
          workDate,
          firstProjectId
        );


      }
    }


    return ts;
  }


  async partialUpdate(id: string, updateDto: Partial<UpdateTimeSheetDto>): Promise<TimeSheet> {
    const existing = await this.timesheetModel.findById(id);
    if (!existing) throw new NotFoundException('Timesheet not found');

    const billableDays = updateDto.billableDays ?? existing.billableDays ?? 0;
  const hoursPerDay = 8;
  const billableHours = billableDays * hoursPerDay;

    // Extract values from updateDto or fallback to existing document
    const totalBillableDays = existing.totalBillableDays ?? 0;
    const hoursWorked = Number((existing.hoursWorked ?? 0).toFixed(2));
    const hourlyRate = updateDto.hourlyRate ?? existing.hourlyRate ?? 0;
    const overtimeHourlyRate = updateDto.overtimeHourlyRate ?? existing.overtimeHourlyRate ?? 0;
    const overtimeHours = updateDto.overtimeHours ?? existing.overtimeHours ?? 0;

    // Calculate totalBillableAmount only if one of the relevant fields is being updated
    if (
      updateDto.hourlyRate != null ||
      updateDto.overtimeHourlyRate != null ||
      updateDto.overtimeHours != null
      ||  updateDto.billableDays != null
    ) {
      updateDto.totalBillableAmount =
        (billableHours * hourlyRate) + (overtimeHours * overtimeHourlyRate);
      updateDto.totalBillableAmountHours =
        (hoursWorked * hourlyRate) + (overtimeHours * overtimeHourlyRate);
    }

    const ts = await this.timesheetModel.findByIdAndUpdate(id, updateDto, {
      new: true,
    });

    if (!ts) throw new NotFoundException('Timesheet not found');

    return ts;
  }



  /* ------------------------------------------------------------------ */
  /* 🗑️ Delete                                                          */
  /* ------------------------------------------------------------------ */
  async remove(id: string) {
    const deleted = await this.timesheetModel.findByIdAndDelete(id);
    if (!deleted) throw new NotFoundException('Timesheet not found');
    return deleted;
  }

  /* ------------------------------------------------------------------ */
  /* 📊 Utility: on-the-fly totals                                      */
  /* ------------------------------------------------------------------ */
  /**
   * Compute grand totals (billable + overtime) for any timesheet.
   * Returns `{ totalHours, totalOvertimeHours }`
   */
  computeTotals(ts: TimeSheet): { totalHours: number; overtimeHours: number } {
    if (ts.timesheetType === TimesheetType.DAILY) {
      const hrs = ts.isWeekOff ? 0 : ts.hoursWorked ?? 0;
      return { totalHours: Number(hrs.toFixed(2)), overtimeHours: 0 };
    }

    const weekly = ts.weeklyEntries ?? [];
    const totalHours = Number(
      weekly.reduce((sum, w) => sum + (w.totalWeekHours ?? 0), 0).toFixed(2) // ⬅️ round here
    );
    // Optional: derive overtime per your own rule set here
    const overtimeHours = 0;
    return { totalHours, overtimeHours };
  }

  /* ------------------------------------------------------------------ */
  /* 🔒 Private helpers                                                 */
  /* ------------------------------------------------------------------ */
  private validateByType(dto: Partial<CreateTimeSheetDto>) {
    switch (dto.timesheetType) {
      case TimesheetType.DAILY:
        // if (
        //   !dto.date ||
        //   dto.hoursWorked === undefined ||
        //   dto.hoursWorked === null
        // )
        //   throw new BadRequestException(
        //     'Daily timesheets require date and hoursWorked.',
        //   );
        break;

      case TimesheetType.WEEKLY:
        if (!dto.week || !dto.weeklyEntries?.length)
          throw new BadRequestException(
            'Weekly timesheets require week and weeklyEntries.',
          );
        break;

      case TimesheetType.MONTHLY:
        if (!dto.month)
          throw new BadRequestException(
            'Monthly timesheets require month.',
          );
        break;

      default:
        throw new BadRequestException('Invalid timesheetType');
    }
  }

  private populateOptions() {
    return [
      { path: 'employee', select: '_id firstName roles org', model: 'BasicUser' },
      {
        path: 'employeeId',
        model: 'Employee',
        populate: [
          { path: 'payRollOrg', model: 'Org', select: '_id title' },
          { path: 'endClientOrg', model: 'Org', select: '_id title' },
          { path: 'job', model: 'Job', select: '_id title employmentType' },
          { path: 'jobApplication', model: 'JobApplication', select: '_id firstName lastName' },
          { path: 'bgvId', model: 'Bgv', select: '_id' },
          { path: 'bankDetails', model: 'BankDetails', select: '_id accountNumber ifscCode' },
          {
            path: 'createdBy',
            model: 'BasicUser',
            select: '_id email firstName lastName'
          },

        ]
      },
      { path: 'projectId', select: '_id name org client', model: 'Project' },
      { path: 'taskId', select: '_id title', model: 'Task' },
      { path: 'attachments', select: '_id originalName fileSize fileType locationUrl', model: 'FileMetadata' },
    ];
  }

  async saveSettings(
    orgId: string,
    projectId: string[] | string | null,
    taskId: string[] | string | null,
    settingsDto: Partial<TimeSheetSettings>,
  ) {
    const projectIds = Array.isArray(projectId)
      ? projectId
      : projectId
        ? [projectId]
        : [];

    const taskIds = Array.isArray(taskId)
      ? taskId
      : taskId
        ? [taskId]
        : [];

    return this.settingsModel.findOneAndUpdate(
      {
        orgId,
        projectId: { $in: projectIds },
        taskId: { $in: taskIds },
      },
      {
        ...settingsDto,
        orgId,
        projectId: projectIds,
        taskId: taskIds
      },
      { upsert: true, new: true }
    );
  }


  async updateSettingsById(id: string, dto: Partial<SaveTimeSheetSettingsDto>) {
    const updatePayload: any = { ...dto };

    // Convert projectId from string[] to ObjectId[]
    if (dto.projectId) {
      updatePayload.projectId = dto.projectId.map(id => new Types.ObjectId(id));
    }

    if (dto.taskId) {
      updatePayload.projectId = dto.taskId.map(id => new Types.ObjectId(id));
    }


    const updated = await this.settingsModel.findByIdAndUpdate(id, updatePayload, { new: true });

    if (!updated) {
      throw new NotFoundException('Timesheet settings not found');
    }

    return updated;
  }
  async getSettings(orgId: string, projectId?: string | string[] | null, taskId?: string | string[] | null) {
    let settings = null;

    if (projectId) {
      settings = await this.settingsModel
        .findOne({
          orgId,
          projectId: { $in: [projectId] }, // checks if projectId exists in the array
        })
        .sort({ createdAt: -1 });
    }

    if (taskId) {
      settings = await this.settingsModel
        .findOne({
          orgId,
          projectId: { $in: [projectId] }, // checks if projectId exists in the array
        })
        .sort({ createdAt: -1 });
    }

    if (!settings) {
      // fallback to default settings without project-specific config
      settings = await this.settingsModel
        .findOne({ orgId, projectId: null })
        .sort({ createdAt: -1 });
    }

    return settings;
  }



  async getTimesheetPreviewOnSettings(
    type: TimesheetType,
    orgId: string,
    userId: string,
    projectId?: string | string[] | null,
    taskId?: string | string[] | null,
    month?: string
  ) {
    const projectIds = Array.isArray(projectId)
      ? projectId.map((id) => new Types.ObjectId(id))
      : projectId
        ? [new Types.ObjectId(projectId)]
        : [];

    const taskIds = Array.isArray(taskId)
      ? taskId.map((id) => new Types.ObjectId(id))
      : taskId
        ? [new Types.ObjectId(taskId)]
        : [];


    let settings = await this.settingsModel
      .findOne({
        orgId,
        projectId: { $in: projectIds },
        taskId: { $in: taskIds },
      })
      .sort({ createdAt: -1 });

    // Fallback to org-wide default setting if no specific setting found
    if (!settings) {
      settings = await this.settingsModel
        .findOne({ orgId, projectId: null, taskId: null })
        .sort({ createdAt: -1 });
    }
    if (!settings) throw new BadRequestException('Settings not found');

    const parsedMonth = moment(month, 'YYYY-MM', true); // true = strict parsing
    const targetMonth = parsedMonth.isValid() ? parsedMonth : moment();

    const endDay = settings.monthlyCycleEndDay ?? targetMonth.daysInMonth();

    // ✅ End of the current cycle
    const cycleEnd = targetMonth.clone().date(endDay).startOf('day');

    // ✅ Start is one day after the previous cycle's end
    const cycleStart = cycleEnd.clone().subtract(1, 'month').add(1, 'day');

   if (type === TimesheetType.MONTHLY) {
  let workingDays = 0;
  let current = cycleStart.clone();

  const monthlyEntries = [];

  while (current.isSameOrBefore(cycleEnd)) {
    const dayName = current.format('dddd');
    const isWeekOff = settings.weekends.includes(dayName);
    const isInCycle = true;

    if (!isWeekOff) workingDays++;

    monthlyEntries.push({
       date: current.format('YYYY-MM-DD'),
      isWeekOff,
      isInCycle,
      hoursWorked: 0,
    });

    current.add(1, 'day');
  }

  return {
    timesheetType: type,
    employee: userId,
    projectId: projectIds,
    taskId: taskIds,
    month: targetMonth.format('YYYY-MM'),
    cycleStartDate: cycleStart.format('YYYY-MM-DD'),
    cycleEndDate: cycleEnd.format('YYYY-MM-DD'),
    billableDays: workingDays,
    leaveDays: 0,
    hourlyRate: 0,
    totalBillableDays: workingDays,
    totalBillableAmount: 0,
    monthlyEntries, // ✅ Include daily entries
  };
}


    // If the end day is numerically before the start day, assume it's next month
    // if (endDay && endDay < startDay) {
    //   cycleEnd.add(1, 'month');
    // }

    // Handle WEEKLY
    if (type === TimesheetType.WEEKLY) {
      // const weekStartDayIndex = moment().day(settings.weekStartDay).weekday();
      const weekStartDayIndex = moment().isoWeekday(settings.weekStartDay).isoWeekday(); // ISO = 1 (Monday) ... 7 (Sunday)



      // Backtrack to nearest previous (or same) weekStartDay
      let current = cycleStart.clone();
      while (current.isoWeekday() !== weekStartDayIndex) {
        current.subtract(1, 'day');
      }

      const weeklyEntries = [];


      while (current.isSameOrBefore(cycleEnd)) {
        const weekStart = current.clone();
        const weekEnd = current.clone().add(6, 'days');

        weeklyEntries.push({
          weekLabel: `Week ${weekStart.format('MMM D')} - ${weekEnd.format('MMM D')}`,
          weekStartDate: weekStart.format('YYYY-MM-DD'),
          weekEndDate: weekEnd.format('YYYY-MM-DD'),
          days: Array.from({ length: 7 }, (_, i) => {
            const d = weekStart.clone().add(i, 'days');
            const isWeekOff = settings.weekends.includes(d.format('dddd'));
            const isInCycle = d.isSameOrAfter(cycleStart) && d.isSameOrBefore(cycleEnd);
            return {
              day: d.format('dddd'),
              date: d.format('YYYY-MM-DD'),
              isWeekOff,
              hoursWorked: 0,
              isInCycle,
            };
          }),
          totalWeekHours: 0,
        });

        current = current.add(1, 'week');
      }

      return {
        timesheetType: type,
        employee: userId,
        projectId: projectIds,
        taskId: taskIds,
        month: targetMonth.format('YYYY-MM'),
        cycleStartDate: cycleStart.format('YYYY-MM-DD'),
        cycleEndDate: cycleEnd.format('YYYY-MM-DD'),
        weeklyEntries,
      };
    }

    // Handle MONTHLY
    // if (type === TimesheetType.MONTHLY) {
    //   let workingDays = 0;
    //   let current = cycleStart.clone();
    //   while (current.isSameOrBefore(cycleEnd)) {
    //     const dayName = current.format('dddd');
    //     if (!settings.weekends.includes(dayName)) workingDays++;
    //     current.add(1, 'day');
    //   }

    //   return {
    //     timesheetType: type,
    //     employee: userId,
    //     projectId: projectIds,
    //     taskId: taskIds,
    //     month: targetMonth.format('YYYY-MM'),
    //     cycleStartDate: cycleStart.format('YYYY-MM-DD'),
    //     cycleEndDate: cycleEnd.format('YYYY-MM-DD'),
    //     billableDays: workingDays,
    //     leaveDays: 0,
    //     hourlyRate: 0,
    //     totalBillableDays: workingDays,
    //     totalBillableAmount: 0,
    //   };
    // }

    // Handle DAILY
    if (type === TimesheetType.DAILY) {
      const dayDate = cycleStart.clone();
      const isWeekOff = settings.weekends.includes(dayDate.format('dddd'));

      return {
        timesheetType: type,
        employee: userId,
        projectId: projectIds,
        taskId: taskIds,
        date: dayDate.format('YYYY-MM-DD'),
        isWeekOff,
        hoursWorked: isWeekOff ? 0 : 8,
        startTime: '09:00',
        endTime: '17:00',
      };
    }

    return {};
  }



  // Add this method to your existing TimeSheetService class

  /* ------------------------------------------------------------------ */
  /* 🏢 Organization-level reads                                       */
  /* ------------------------------------------------------------------ */

  /**
   * Get all timesheets for a specific organization
   * @param orgId - Organization ID
   * @param filters - Optional filters (timesheetType, status, month, etc.)
   */
  async findAllByOrganization(
    orgId: string,
    filters?: {
      timesheetType?: string;
      status?: string;
      month?: string;
      employeeId?: string;
      projectId?: string;
      taskId?: string;
      startDate?: Date;
      endDate?: Date;
      date?: Date; // ✅ Add this for specific daily date
    }
  ): Promise<TimeSheet[]> {
    const query: any = {
      $and: [
        {
          $or: [
            { orgId },
            { orgId: new Types.ObjectId(orgId) },
          ]
        }
      ]
    };

    if (filters) {
      if (filters.timesheetType) {
        query.$and.push({ timesheetType: filters.timesheetType });
      }

      if (filters.status) {
        query.$and.push({ status: filters.status });
      }

      if (filters.month) {
        query.$and.push({ month: filters.month });
      }

      if (filters.employeeId) {
        query.$and.push({
          $or: [
            { employee: filters.employeeId },
            { employee: new Types.ObjectId(filters.employeeId) },
          ],
        });
      }

      if (filters.projectId) {
        query.$and.push({
          $or: [
            { projectId: filters.projectId },
            { projectId: new Types.ObjectId(filters.projectId) },
          ],
        });
      }

      if (filters.taskId) {
        query.$and.push({
          $or: [
            { taskId: filters.taskId },
            { taskId: new Types.ObjectId(filters.taskId) },
          ],
        });
      }

      if (filters.date) {
        const startOfDay = new Date(moment(filters.date).startOf('day').toISOString());
        const endOfDay = new Date(moment(filters.date).endOf('day').toISOString());
        query.$and.push({ date: { $gte: startOfDay, $lte: endOfDay } });
      }

      if (filters.startDate && filters.endDate) {
        query.$and.push({
          $or: [
            { date: { $gte: filters.startDate, $lte: filters.endDate } }, // Daily
            { createdAt: { $gte: filters.startDate, $lte: filters.endDate } } // Weekly/Monthly
          ]
        });
      }
    }

    this.logger.log(`Fetching timesheets for org: ${orgId} with filters: ${JSON.stringify(filters)}`);

    return this.timesheetModel
      .find(query)
      .populate(this.populateOptions())
      .sort({ createdAt: -1 })
      .exec();
  }



  /**
   * Get organization timesheets with pagination
   */
  async findOrgTimesheetsWithPagination(
    orgId: string,
    page: number = 1,
    limit: number = 10,
    filters?: {
      timesheetType?: string;
      status?: string;
      month?: string;
      employeeId?: string;
      projectId?: string;
      taskId?: string;
    }
  ) {
    const query: any = { orgId };

    // Apply filters
    if (filters) {
      if (filters.timesheetType) query.timesheetType = filters.timesheetType;
      if (filters.status) query.status = filters.status;
      if (filters.month) query.month = filters.month;
      if (filters.employeeId) {
        query.employee = new Types.ObjectId(filters.employeeId.toString());
      }
      if (filters.projectId) query.projectId = new Types.ObjectId(filters.projectId);
      if (filters.taskId) query.taskId = new Types.ObjectId(filters.taskId);
    }

    const skip = (page - 1) * limit;

    const [timesheets, total] = await Promise.all([
      this.timesheetModel
        .find(query)
        .populate(this.populateOptions())
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .exec(),
      this.timesheetModel.countDocuments(query)
    ]);

    return {
      timesheets,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    };
  }

  /**
   * Get organization timesheet summary/stats
   */
  async getOrgTimesheetStats(orgId: string, month?: string) {
    const matchQuery: any = { orgId };
    if (month) {
      matchQuery.month = month;
    }

    const stats = await this.timesheetModel.aggregate([
      { $match: matchQuery },
      // Populate employee
      {
        $lookup: {
          from: 'employees',
          localField: 'employee',
          foreignField: '_id',
          as: 'employee'
        }
      },
      { $unwind: '$employee' },

      // Populate employee.payRollOrg
      {
        $lookup: {
          from: 'orgs',
          localField: 'employee.payRollOrg',
          foreignField: '_id',
          as: 'employee.payRollOrg'
        }
      },
      {
        $unwind: {
          path: '$employee.payRollOrg',
          preserveNullAndEmptyArrays: true
        }
      },
      // Merge limited payRollOrg fields into employee.payRollOrg
      {
        $addFields: {
          'employee.payRollOrg': {
            _id: '$payRollOrgInfo._id',
            title: '$payRollOrgInfo.title'
          }
        }
      },

      {
        $group: {
          _id: '$status',
          count: { $sum: 1 },
          totalHours: { $sum: '$hoursWorked' },
          totalBillableAmount: { $sum: '$totalBillableAmount' }
        }
      }
    ]);

    // Get employee count
    const employeeCount = await this.timesheetModel
      .distinct('employee', matchQuery)
      .exec();

    return {
      stats,
      totalEmployees: employeeCount.length,
      summary: {
        totalTimesheets: stats.reduce((sum, s) => sum + s.count, 0),
        totalHours: Number(
          stats.reduce((sum, s) => sum + (s.totalHours || 0), 0).toFixed(2)
        ),
        totalBillableAmount: Number(
          stats.reduce((sum, s) => sum + (s.totalBillableAmount || 0), 0).toFixed(2)
        )
      }
    };
  }

  //Approval flow 

  async submitTimesheet(
    timesheetId: string,
    userId: string,
    comments?: string
  ): Promise<TimeSheet> {
    const timesheet = await this.timesheetModel.findById(timesheetId);

    if (!timesheet) {
      throw new NotFoundException('Timesheet not found');
    }

    // Check if user owns this timesheet
    if (timesheet.employee.toString() !== userId) {
      throw new BadRequestException('You can only submit your own timesheets');
    }

    // // Check if timesheet is in valid state for submission
    // if (timesheet.status && !['Draft', 'Rejected', 'Pending'].includes(timesheet.status)) {
    //   throw new BadRequestException('Timesheet cannot be submitted in current state');
    // }

    // Validate required fields based on timesheet type
    this.validateTimesheetForSubmission(timesheet);

    const updatedTimesheet = await this.timesheetModel.findByIdAndUpdate(
      timesheetId,
      {
        status: 'Submitted',
        submittedAt: new Date(),
        submissionComments: comments,
        // Clear any previous approval data
        approvedBy: null,
        approvedAt: null,
        rejectedBy: null,
        rejectedAt: null,
        rejectionReason: null,
      },
      { new: true }
    ).populate(this.populateOptions());
    if (!updatedTimesheet) {
      throw new NotFoundException('Timesheet could not be updated');
    }


    this.logger.log(`Timesheet ${timesheetId} submitted by user ${userId}`);
    return updatedTimesheet;
  }


  async approveTimesheet(
    timesheetId: string,
    approverId: string,
    comments?: string,
    approvedHours?: number
  ): Promise<TimeSheet> {
    const timesheet = await this.timesheetModel.findById(timesheetId);

    if (!timesheet) {
      throw new NotFoundException('Timesheet not found');
    }

    // Check if timesheet is in submitted state
    if (timesheet.status !== 'Submitted') {
      throw new BadRequestException('Only submitted timesheets can be approved');
    }

    // TODO: Add role-based permission check here
    // You might want to check if the approver has HR/Manager role
    // const approver = await this.basicUserModel.findById(approverId);
    // if (!this.hasApprovalPermission(approver)) {
    //   throw new ForbiddenException('Insufficient permissions to approve timesheets');
    // }

    const updateData: any = {
      status: 'Approved',
      approvedBy: new Types.ObjectId(approverId),
      approvedAt: new Date(),
      approvalComments: comments,
      // Clear rejection data
      rejectedBy: null,
      rejectedAt: null,
      rejectionReason: null,
    };


    // If approved hours are provided and different from original
    if (approvedHours !== undefined && approvedHours !== timesheet.hoursWorked) {
      updateData.approvedHours = Number(approvedHours.toFixed(2));
      updateData.originalHours = Number((timesheet.hoursWorked ?? 0).toFixed(2)); // Store original for reference
    }

    const updatedTimesheet = await this.timesheetModel.findByIdAndUpdate(
      timesheetId,
      updateData,
      { new: true }
    ).populate(this.populateOptions());
    if (!updatedTimesheet) {
      throw new NotFoundException('Timesheet could not be updated');
    }


    this.logger.log(`Timesheet ${timesheetId} approved by user ${approverId}`);
    return updatedTimesheet;
  }




  async rejectTimesheet(
    timesheetId: string,
    rejectorId: string,
    reason: string,
    comments?: string
  ): Promise<TimeSheet> {
    const timesheet = await this.timesheetModel.findById(timesheetId);

    if (!timesheet) {
      throw new NotFoundException('Timesheet not found');
    }

    // Check if timesheet is in submitted state
    if (timesheet.status !== 'Submitted') {
      throw new BadRequestException('Only submitted timesheets can be rejected');
    }

    // TODO: Add role-based permission check here
    // const rejector = await this.basicUserModel.findById(rejectorId);
    // if (!this.hasApprovalPermission(rejector)) {
    //   throw new ForbiddenException('Insufficient permissions to reject timesheets');
    // }

    const updatedTimesheet = await this.timesheetModel.findByIdAndUpdate(
      timesheetId,
      {
        status: 'Rejected',
        rejectedBy: new Types.ObjectId(rejectorId),
        rejectedAt: new Date(),
        rejectionReason: reason,
        rejectionComments: comments,
        // Clear approval data
        approvedBy: null,
        approvedAt: null,
        approvalComments: null,
      },
      { new: true }
    ).populate(this.populateOptions());
    if (!updatedTimesheet) {
      throw new NotFoundException('Timesheet could not be updated');
    }


    this.logger.log(`Timesheet ${timesheetId} rejected by user ${rejectorId}. Reason: ${reason}`);
    return updatedTimesheet;
  }


  async recallTimesheet(
    timesheetId: string,
    userId: string,
    reason?: string
  ): Promise<TimeSheet> {
    const timesheet = await this.timesheetModel.findById(timesheetId);

    if (!timesheet) {
      throw new NotFoundException('Timesheet not found');
    }

    // Check if user owns this timesheet
    if (timesheet.employee.toString() !== userId) {
      throw new BadRequestException('You can only recall your own timesheets');
    }

    // Check if timesheet is in submitted state
    if (timesheet.status !== 'Submitted') {
      throw new BadRequestException('Only submitted timesheets can be recalled');
    }

    const updatedTimesheet = await this.timesheetModel.findByIdAndUpdate(
      timesheetId,
      {
        status: 'Draft',
        recalledAt: new Date(),
        recallReason: reason,
        // Clear submission data
        submittedAt: null,
        submissionComments: null,
      },
      { new: true }
    ).populate(this.populateOptions());
    if (!updatedTimesheet) {
      throw new NotFoundException('Timesheet could not be updated');
    }


    this.logger.log(`Timesheet ${timesheetId} recalled by user ${userId}`);
    return updatedTimesheet;
  }


  async findAllByEmployeeWithFilters(
    employeeId: string,
    filters?: { status?: string | { $in: string[] }; timesheetType?: string }
  ): Promise<TimeSheet[]> {
    const query: any = { employee:employeeId };

    if (filters) {
      if (filters.status) {
        query.status = filters.status;
      }
      if (filters.timesheetType) {
        query.timesheetType = filters.timesheetType;
      }
    }

    return this.timesheetModel
      .find(query)
      .populate({
        path: 'employee',
        model: 'Employee',
        populate: [
          { path: 'payRollOrg', model: 'Org', select: '_id title' },
          { path: 'endClientOrg', model: 'Org', select: '_id title' },
          { path: 'job', model: 'Job', select: '_id title employmentType' },
          { path: 'jobApplication', model: 'JobApplication', select: '_id firstName lastName' },
          { path: 'bgvId', model: 'Bgv', select: '_id' },
          { path: 'bankDetails', model: 'BankDetails', select: '_id accountNumber ifscCode' },
          {
            path: 'createdBy',
            model: 'BasicUser',
            select: '_id email firstName lastName',
          },
        ],
      })
      .populate(this.populateOptions())
      .sort({ createdAt: -1 })
      .exec();
  }

  /**
   * Validate timesheet before submission
   */
  private validateTimesheetForSubmission(timesheet: TimeSheet): void {
    switch (timesheet.timesheetType) {
      case TimesheetType.DAILY:
        if (!timesheet.date) {
          throw new BadRequestException('Daily timesheet must have a date');
        }
        if (!timesheet.isWeekOff && (timesheet.hoursWorked === undefined || timesheet.hoursWorked === null)) {
          throw new BadRequestException('Daily timesheet must have hours worked');
        }
        break;

      case TimesheetType.WEEKLY:
        if (!timesheet.weeklyEntries || timesheet.weeklyEntries.length === 0) {
          throw new BadRequestException('Weekly timesheet must have weekly entries');
        }
        // Check if at least one day has hours
        const hasHours = timesheet.weeklyEntries.some(week =>
          week.days.some(day => !day.isWeekOff && day.hoursWorked > 0)
        );
        if (!hasHours) {
          throw new BadRequestException('Weekly timesheet must have at least one working day with hours');
        }
        break;

      case TimesheetType.MONTHLY:
        if (!timesheet.month) {
          throw new BadRequestException('Monthly timesheet must have a month');
        }
        if (timesheet.billableDays === undefined || timesheet.billableDays === null) {
          throw new BadRequestException('Monthly timesheet must have billable days');
        }
        break;

      default:
        throw new BadRequestException('Invalid timesheet type');
    }
  }

  /**
   * Check if user has approval permissions (implement based on your role system)
   */
  private async hasApprovalPermission(user: any): Promise<boolean> {
    // TODO: Implement your role-based permission logic here
    // Example:
    // return user.roles.includes('HR') || user.roles.includes('Manager');
    return true; // Temporary - implement based on your role system
  }

  /**
   * Get approval statistics for organization
   */
  async getApprovalStats(orgId: string, month?: string) {
    const matchQuery: any = { orgId };
    if (month) {
      matchQuery.month = month;
    }

    const stats = await this.timesheetModel.aggregate([
      { $match: matchQuery },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 },
          totalHours: { $sum: '$hoursWorked' }
        }
      }
    ]);

    // Calculate approval rate
    const totalSubmitted = stats.find(s => s._id === 'Submitted')?.count || 0;
    const totalApproved = stats.find(s => s._id === 'Approved')?.count || 0;
    const totalRejected = stats.find(s => s._id === 'Rejected')?.count || 0;
    const totalProcessed = totalApproved + totalRejected;

    const approvalRate = totalProcessed > 0 ? (totalApproved / totalProcessed) * 100 : 0;

    return {
      stats,
      summary: {
        pending: totalSubmitted,
        approved: totalApproved,
        rejected: totalRejected,
        approvalRate: Math.round(approvalRate * 100) / 100,
        totalProcessed
      }
    };
  }



  async generateInvoice(
    timesheetId: string,
    userId: string,
    orgId: string,
  ): Promise<Invoice> {
    const timesheet = await this.timesheetModel.findById(timesheetId);
    if (!timesheet) throw new NotFoundException('Timesheet not found');

    if (!timesheet.totalBillableAmountHours || !timesheet.hourlyRate) {
      throw new BadRequestException('Finance must update rates before generating invoice.');
    }

    const existingInvoice = await this.invoiceModel.findOne({
      amount: timesheet.totalBillableAmountHours,
      billedByOrg: orgId,
      isDeleted: false,
    });

    if (existingInvoice) {
      throw new BadRequestException('Invoice already generated for this timesheet.');
    }

    const invoiceNumber = `INV-${Date.now()}`; // You can improve with padded sequence if needed

    const invoice = await this.invoiceModel.create({
      invoiceNumber,
      amount: timesheet.totalBillableAmountHours,
      status: 'New',
      raisedDate: new Date(),
      billedByOrg: new Types.ObjectId(orgId),
      generatedBy: new Types.ObjectId(userId),
      isTemp: true,
      isDeleted: false,
    });

    return invoice;
  }

  async generateInvoiceFromTimesheet(timesheetId: string, userId: string, orgId: string, options?: { weekLabel?: string }) {
    const timesheet = await this.timesheetModel.findById(timesheetId).populate('employee');

    if (!timesheet) throw new NotFoundException('Timesheet not found');

    const hourlyRate = timesheet.hourlyRate || 0;
    let billableHours = 0;
    let label = '';

    // ✅ Check for duplicate invoices BEFORE processing
    if (options?.weekLabel) {
      // Check if weekly invoice already exists - try multiple query approaches
      const existingWeeklyInvoices = await this.invoiceModel.find({
        timesheet: new Types.ObjectId(timesheetId),
        weekLabel: options.weekLabel,
        isDeleted: false
      });

      // console.log('Checking for existing weekly invoices:', {
      //   timesheetId,
      //   weekLabel: options.weekLabel,
      //   count: existingWeeklyInvoices.length,
      //   existingIds: existingWeeklyInvoices.map(inv => inv._id)
      // });

      if (existingWeeklyInvoices.length > 0) {
        throw new BadRequestException(`Invoice already exists for week: ${options.weekLabel}. Existing invoice IDs: ${existingWeeklyInvoices.map(inv => inv._id).join(', ')}`);
      }

      // Weekly invoice logic
      if (!Array.isArray(timesheet.weeklyEntries)) {
        throw new BadRequestException('Weekly entries not available in the selected timesheet');
      }

      const week = timesheet.weeklyEntries.find(w => w.weekLabel === options.weekLabel);
      if (!week) throw new NotFoundException('Week not found in timesheet');

      billableHours = week.totalWeekHours || 0;
      label = week.weekLabel;
    } else {
      // Check if monthly invoice already exists
      const existingMonthlyInvoice = await this.invoiceModel.findOne({
        timesheet: new Types.ObjectId(timesheetId), // Convert to ObjectId for proper comparison
        weekLabel: { $exists: false }, // Monthly invoices don't have weekLabel
        isDeleted: false
      });

      if (existingMonthlyInvoice) {
        throw new BadRequestException(`Monthly invoice already exists for timesheet`);
      }

      // Monthly invoice logic
      billableHours = timesheet.hoursWorked || 0;
      label = `Month: ${timesheet.month}`;
    }

    const amount = billableHours * hourlyRate;
    const invoiceNumber = `INV-${Date.now()}`;

    const invoice = new this.invoiceModel({
      invoiceNumber,
      amount,
      billedByOrg: new Types.ObjectId(orgId),
      generatedBy: new Types.ObjectId(userId),
      raisedDate: new Date(),
      employee: timesheet.employeeId,
      timesheet: new Types.ObjectId(timesheetId), // Ensure this is properly set
      periodLabel: label,
      hoursBilled: billableHours,
      rate: hourlyRate,
      totalAmount: amount,
      status: 'New',
      isTemp: true,
      isDeleted: false,
      ...(options?.weekLabel && { weekLabel: options.weekLabel })
    });

    await invoice.save();

    // Only mark the timesheet as invoiced if generating for the full month
    if (!options?.weekLabel) {
      timesheet.isInvoiced = true;
      await timesheet.save();
    }

    console.log("invoice", invoice);
    return invoice;
  }


  // async generateInvoiceFromTimesheet(timesheetId: string, userId: string,
  //   orgId: string, options?: { weekLabel?: string }) {
  //   const timesheet = await this.timesheetModel.findById(timesheetId).populate('employee');

  //   if (!timesheet) throw new NotFoundException('Timesheet not found');

  //   const hourlyRate = timesheet.hourlyRate || 0;

  //   let billableHours = 0;
  //   let label = '';



  //   // ✅ 2. Billable hours & label
  //   if (options?.weekLabel) {
  //     // Weekly invoice logic
  //     if (!Array.isArray(timesheet.weeklyEntries)) {
  //       throw new BadRequestException('Weekly entries not available in the selected timesheet');
  //     }

  //     const week = timesheet.weeklyEntries.find(w => w.weekLabel === options.weekLabel);
  //     if (!week) throw new NotFoundException('Week not found in timesheet');

  //     billableHours = week.totalWeekHours || 0;  // ✅ use totalWeekHours directly
  //     label = week.weekLabel;
  //   } else {
  //     // Monthly invoice logic
  //     billableHours = timesheet.hoursWorked || 0;
  //     label = `Month: ${timesheet.month}`;
  //   }

  //   const amount = billableHours * hourlyRate;
  //   const invoiceNumber = `INV-${Date.now()}`;

  //   const invoice = new this.invoiceModel({
  //     invoiceNumber,
  //     amount,
  //     billedByOrg: new Types.ObjectId(orgId),
  //     generatedBy: new Types.ObjectId(userId),
  //     raisedDate: new Date(),
  //     employee: timesheet.employee,
  //     timesheet: timesheet._id,
  //     periodLabel: label,
  //     hoursBilled: billableHours,
  //     rate: hourlyRate,
  //     totalAmount: amount,
  //     status: 'New',
  //     isTemp: true,
  //     isDeleted: false,
  //     ...(options?.weekLabel && { weekLabel: options.weekLabel })
  //   });

  //   await invoice.save();

  //   // Only mark the timesheet as invoiced if generating for the full month
  //   if (!options?.weekLabel) {
  //     timesheet.isInvoiced = true;
  //     await timesheet.save();
  //   }

  //   console.log("invoice", invoice)

  //   return invoice;
  // }




  // async generateInvoiceForTimesheet(timesheetId: string, mode: 'weekly' | 'monthly', user: any): Promise<Invoice[] | Invoice> {
  //   const timesheet = await this.timesheetModel.findById(timesheetId).populate('employee').exec();

  //   if (!timesheet) throw new BadRequestException('Timesheet not found');

  //   const invoices: Invoice[] = [];

  //   const getDateMonthYear = (dateStr: string) => {
  //     const date = new Date(dateStr);
  //     return { month: date.getMonth(), year: date.getFullYear() };
  //   };

  //   const isSameMonthYear = (dateStr: string, targetMonth: number, targetYear: number) => {
  //     const date = new Date(dateStr);
  //     return date.getMonth() === targetMonth && date.getFullYear() === targetYear;
  //   };

  //   const orgId = user.org._id.toString();


  //   const baseInvoiceData = {
  //     billedByOrg: orgId,

  //     status: 'unpaid',
  //     generatedBy: user._id,
  //     isTemp: false,
  //   };

  //   if (mode === 'weekly') {
  //     for (const week of timesheet.weeklyEntries) {
  //       const inCycleDays = week.days.filter(day => day.isInCycle);
  //       const totalWeekHours = inCycleDays.reduce((sum, day) => sum + (day.hoursWorked || 0), 0);

  //       if (totalWeekHours === 0) continue; // Skip empty weeks

  //       const invoice = new this.invoiceModel({
  //         ...baseInvoiceData,
  //         invoiceNumber: `INV-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
  //         amount: totalWeekHours * this.hourlyRate, // 💰 Adjust hourlyRate source as needed
  //         raisedDate: new Date(),
  //         dueDate: new Date(new Date().setDate(new Date().getDate() + 7)),
  //       });

  //       invoices.push(await invoice.save());
  //     }

  //     return invoices;
  //   }

  //   if (mode === 'monthly') {
  //     const { month: targetMonth, year: targetYear } = getDateMonthYear(timesheet.cycleStartDate);
  //     let totalMonthlyHours = 0;

  //     for (const week of timesheet.weeklyEntries) {
  //       for (const day of week.days) {
  //         if (day.isInCycle && isSameMonthYear(day.date, targetMonth, targetYear)) {
  //           totalMonthlyHours += day.hoursWorked || 0;
  //         }
  //       }
  //     }

  //     if (totalMonthlyHours === 0) throw new BadRequestException('No billable hours for the month');

  //     const invoice = new this.invoiceModel({
  //       ...baseInvoiceData,
  //       invoiceNumber: `INV-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
  //       amount: totalMonthlyHours * this.hourlyRate,
  //       raisedDate: new Date(),
  //       dueDate: new Date(new Date().setDate(new Date().getDate() + 7)),
  //     });

  //     return await invoice.save();
  //   }

  //   throw new BadRequestException('Invalid invoice mode');
  // }


  getPopulateOptions(): PopulateOptions[] {
    const populateOptions = [
      {
        path: 'employee',
        model: 'Employee',
        populate: [
          { path: 'payRollOrg', model: 'Org', select: '_id title' },
          { path: 'endClientOrg', model: 'Org', select: '_id title' },
          { path: 'job', model: 'Job', select: '_id title employmentType' },
          { path: 'jobApplication', model: 'JobApplication', select: '_id firstName lastName' },
          { path: 'bgvId', model: 'Bgv', select: '_id' },
          { path: 'bankDetails', model: 'BankDetails', select: '_id accountNumber ifscCode' },
          {
            path: 'createdBy',
            model: 'BasicUser',
            select: '_id email firstName lastName'
          },
          {
            path: 'currentAddress.country state city',
            model: 'Country', // Make sure this chain matches your actual Address population logic
          },
        ]
      },
      // Add more for other fields like project, task, etc., if needed
    ];
    return populateOptions;
  }




}

