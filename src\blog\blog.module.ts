import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { BlogService } from './blog.service';
import { JwtModule } from '@nestjs/jwt';
import { MongooseModule } from '@nestjs/mongoose';
import { Blog, BlogSchema } from './schemas/blog.schema';
import { BlogController } from './blog.controller';
import { EndpointsRolesModule } from 'src/endpoints-roles/endpoints-roles.module';


@Module({
  imports: [
    ConfigModule,
    JwtModule, EndpointsRolesModule,
    MongooseModule.forFeature([{ name: Blog.name, schema: BlogSchema }])
  ],
  controllers: [BlogController],
  providers: [BlogService],
  exports: [BlogService]
})
export class BlogModule { }