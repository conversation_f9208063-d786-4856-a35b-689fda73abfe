import { Transform } from 'class-transformer';
import { IsOptional, IsString, IsInt, Min, IsBoolean } from 'class-validator';

export class QueryContactCountDto {


  @IsOptional()
  @IsString()
  @Transform(({ value }) => (value === undefined || value === '0' ? '' : value))
  accountId?: string;

  @IsOptional()
  @IsString()
  @Transform(({ value }) => (value === undefined || value === '0' ? '' : value))
  vendorId?: string;

  @IsOptional()
  @Transform(({ value }) => {
    return value === undefined || value === '0' ? '' : value;
  })
  isInternal?: boolean;
}
