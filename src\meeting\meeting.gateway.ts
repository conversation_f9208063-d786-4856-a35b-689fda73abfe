import { WebSocketGateway, SubscribeMessage, MessageBody, ConnectedSocket } from '@nestjs/websockets';
import { MeetingService } from './meeting.service';
import { CreateMeetingDto } from './dto/create-meeting.dto';
import { UpdateMeetingDto } from './dto/update-meeting.dto';
import {
  WebSocketServer,
  OnGatewayInit,
  OnGatewayConnection,
  OnGatewayDisconnect,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { Logger } from '@nestjs/common';
// import { RedisStoreService } from 'src/real-time-messaging/redis-store/redis-store.service';
import { ConfigService } from '@nestjs/config';
import { validateObjectId } from 'src/utils/validation.utils';
import { RedisStoreService } from './redis-store/redis-store.service';

interface TranscriptMessage {
  roomId: string;
  userId: string;
  username: string;
  content: string;
  timestamp: string;
}
@WebSocketGateway({
  // namespace: 'meetings',
  // path: '/meetings',
  cors: {
    origin: '*',
    //  methods: ['GET', 'POST'],
    //  allowedHeaders: ['Content-Type'],
    //  credentials: true,
  },
  pingInterval: 60000,
  maxHttpBufferSize: 1e8 //100 mb
  // Read about server options here - https://socket.io/docs/v4/server-options/#
}
)
export class MeetingGateway implements OnGatewayInit, OnGatewayConnection, OnGatewayDisconnect {
  private logger: Logger = new Logger(MeetingGateway.name);


  @WebSocketServer()
  server: Server;

  private rooms = new Map<string, Set<string>>(); // Maps room IDs to client IDs
  private activeSockets: { room: string; id: string }[] = [];
  private roomTranscripts = new Map<string, TranscriptMessage[]>();


  constructor(private readonly meetingService: MeetingService, private configService: ConfigService, private readonly redisStoreService: RedisStoreService) { }

  afterInit(server: Server) {
    this.logger.log('WebSocket Server Initialized')
    this.redisStoreService.storeObject('activeSockets', this.activeSockets)

  }

  @SubscribeMessage('ping')
  // @UseGuards(WebSocketAuthGuard)
  handlePing(@ConnectedSocket() client: Socket, payload: any): any {
    client.emit('pong', { id: client.id });
    return;
  }

  handleConnection(@ConnectedSocket() client: Socket) {
    this.logger.log(`Client connected in meeting gateway: ${client.id}`)
    client.emit('ping', { message: "Client successfully connected" })
  }

  handleDisconnect(@ConnectedSocket() client: Socket) {
    this.logger.log(`Client disconnected: ${client.id}`)
    this.leaveAllRooms(client);
  }


  @SubscribeMessage('createMeeting')
  create(@ConnectedSocket() client: Socket, @MessageBody() createMeetingDto: CreateMeetingDto) {
    return this.meetingService.create(createMeetingDto);
  }

  @SubscribeMessage('findAllMeeting')
  findAll(@ConnectedSocket() client: Socket,) {
    // return this.meetingService.findAll();
  }

  @SubscribeMessage('findOneMeeting')
  findOne(@ConnectedSocket() client: Socket, @MessageBody() meetingID: string) {
    const meetingObjId = validateObjectId(meetingID)
    return this.meetingService.findOne(meetingObjId);
  }

  @SubscribeMessage('updateMeeting')
  update(@ConnectedSocket() client: Socket, @MessageBody() updateMeetingDto: UpdateMeetingDto) {
    // const meetingObjId = validateObjectId(updateMeetingDto.meetingId)
    // return this.meetingService.update(meetingObjId, updateMeetingDto);
  }

  @SubscribeMessage('removeMeeting')
  remove(@ConnectedSocket() client: Socket, @MessageBody() meetingId: string) {
    const meetingObjId = validateObjectId(meetingId)
    return this.meetingService.remove(meetingObjId);
  }

  private leaveAllRooms(@ConnectedSocket() client: Socket) {
    this.rooms.forEach((clients, roomId) => {
      if (clients.delete(client.id)) {
        this.server.to(roomId).emit('user-left', client.id);
      }
    });
  }

  @SubscribeMessage('join-room')
  handleJoinRoom(@ConnectedSocket() client: Socket, @MessageBody() roomId: string) {
    client.join(roomId);
    if (!this.rooms.has(roomId)) this.rooms.set(roomId, new Set());
    this.rooms.get(roomId)?.add(client.id);

    // Notify others in the room
    client.to(roomId).emit('user-joined', client.id);
  }

  @SubscribeMessage('offer')
  handleOffer(@ConnectedSocket() client: Socket, @MessageBody() payload: { roomId: string; offer: RTCSessionDescription }) {
    // this.logger.log(`Offer event received. Room ID: ${payload.roomId}, Client ID: ${client.id}, Offer: ${JSON.stringify(payload.offer)}`);
    client.to(payload.roomId).emit('offer', { sender: client.id, offer: payload.offer });
    // client.emit('offer', { sender: client.id, offer: payload.offer })
  }

  @SubscribeMessage('answer')
  handleAnswer(@ConnectedSocket() client: Socket, @MessageBody() payload: { roomId: string; answer: RTCSessionDescription }) {
    client.to(payload.roomId).emit('answer', { sender: client.id, answer: payload.answer });
  }

  @SubscribeMessage('ice-candidate')
  handleIceCandidate(@ConnectedSocket() client: Socket, @MessageBody() payload: { roomId: string; candidate: RTCIceCandidate }) {
    client.to(payload.roomId).emit('ice-candidate', { sender: client.id, candidate: payload.candidate });
  }


  @SubscribeMessage('broadcast-transcript')
  handleTranscript(@ConnectedSocket() client: Socket, @MessageBody() payload: TranscriptMessage) {
    const { roomId } = payload;

    this.logger.log(`Received transcript from ${payload.userId} in room ${roomId}`);

    // Store transcript in memory
    if (!this.roomTranscripts.has(roomId)) {
      this.roomTranscripts.set(roomId, []);
    }
    const roomTranscripts = this.roomTranscripts.get(roomId);
    if (roomTranscripts) {
      roomTranscripts.push(payload);

      // Broadcast to all users in the room INCLUDING the sender
      this.server.to(roomId).emit('transcript-received', payload);

      this.logger.log(`Broadcast transcript to room ${roomId}`);
    }
  }

  @SubscribeMessage('get-room-transcripts')
  handleGetTranscripts(
    @ConnectedSocket() client: Socket,
    @MessageBody() roomId: string
  ) {
    const transcripts = this.roomTranscripts.get(roomId) || [];
    client.emit('room-transcripts', transcripts);
  }

  @SubscribeMessage('clear-room-transcripts')
  handleClearTranscripts(
    @ConnectedSocket() client: Socket,
    @MessageBody() roomId: string
  ) {
    this.roomTranscripts.delete(roomId);
    this.server.to(roomId).emit('transcripts-cleared');
  }

  // constructor(private configService: ConfigService, private readonly redisStoreService: RedisStoreService){

  // }

  // private activeSockets: { room: string; id: string }[] = [];


  // @SubscribeMessage('auth.test')
  // @UseGuards(WebSocketAuthGuard)
  // handleAuthTest(@ConnectedSocket() client: Socket, payload: any): any {
  //   // return 'Hello world!';
  //   return client.emit('auth.success', {message: 'auth succeeded.' });
  // }

  // @SubscribeMessage('message')
  // @UseGuards(WebSocketAuthGuard)
  // handleMessage(@ConnectedSocket() client: Socket, payload: any): any {
  //   // return 'Hello world!';
  //   return client.emit('message.received', {message: 'msg recieved.' });
  // }

  // @SubscribeMessage('ping')
  // // @UseGuards(WebSocketAuthGuard)
  // handlePing(@ConnectedSocket() client: Socket, payload: any): any {
  //   client.emit('pong', {id: client.id});
  //   return ;
  // }



  // public afterInit(server: Server): void {
  //   this.redisStoreService.storeObject('activeSockets', this.activeSockets)
  //   this.logger.log('Init');
  // }

  // public handleDisconnect(@ConnectedSocket() client: Socket): void {
  //   const existingSocket = this.activeSockets.find(
  //     (socket) => socket.id === client.id,
  //   );

  //   if (!existingSocket) return;

  //   this.activeSockets = this.activeSockets.filter(
  //     (socket) => socket.id !== client.id,
  //   );

  //   client.broadcast.emit(`${existingSocket.room}-remove-user`, {
  //     socketId: client.id,
  //   });

  //   this.logger.log(`Client disconnected: ${client.id}`);
  // }

  // @SubscribeMessage('joinRoom')
  // public joinRoom(@ConnectedSocket() client: Socket, room: string): void {
  //   /*
  //   client.join(room);
  //   client.emit('joinedRoom', room);
  //   */

  //   const existingSocket = this.activeSockets?.find(
  //     (socket) => socket.room === room && socket.id === client.id,
  //   );

  //   if (!existingSocket) {
  //     this.activeSockets = [...this.activeSockets, { id: client.id, room }];
  //     client.emit(`${room}-update-user-list`, {
  //       users: this.activeSockets
  //         .filter((socket) => socket.room === room && socket.id !== client.id)
  //         .map((existingSocket) => existingSocket.id),
  //       current: client.id,
  //     });

  //     client.broadcast.emit(`${room}-add-user`, {
  //       user: client.id,
  //     });
  //   }

  //   return this.logger.log(`Client ${client.id} joined ${room}`);
  // }

  // @SubscribeMessage('call-user')
  // public callUser(@ConnectedSocket() client: Socket, data: any): void {
  //   client.to(data.to).emit('call-made', {
  //     offer: data.offer,
  //     socket: client.id,
  //   });
  // }

  // @SubscribeMessage('make-answer')
  // public makeAnswer(@ConnectedSocket() client: Socket, data: any): void {
  //   client.to(data.to).emit('answer-made', {
  //     socket: client.id,
  //     answer: data.answer,
  //   });
  // }

  // @SubscribeMessage('reject-call')
  // public rejectCall(@ConnectedSocket() client: Socket, data: any): void {
  //   client.to(data.from).emit('call-rejected', {
  //     socket: client.id,
  //   });
  // }


}
