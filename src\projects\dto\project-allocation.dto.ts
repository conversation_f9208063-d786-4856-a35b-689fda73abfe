import { IsArray, IsOptional } from "class-validator";

export class ProjectAllocationDto {
    @IsOptional()
    userId: string;

    @IsOptional()
    roles: string[];

    @IsOptional()
    allocation?: number;

    @IsArray()
    @IsOptional()
    departments?: string[];

    @IsArray()
    @IsOptional()
    reportingTo?: string[];
}

export class AllocateUsersDto {
    @IsOptional()
    projectId: string;

    @IsOptional()
    allocations: ProjectAllocationDto[];
}
