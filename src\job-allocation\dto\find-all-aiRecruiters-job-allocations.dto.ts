import { ApiProperty } from '@nestjs/swagger';
import { IsMongoId, IsOptional, IsEnum, IsNumber, IsBoolean, IsDateString, IsISO8601 } from 'class-validator';
import { Status, Priority } from 'src/shared/constants';

export class FindAllAiRecruiterJobAllocationsDto {
    @ApiProperty({ required: false, description: 'Job ID to filter by' })
    @IsOptional()
    @IsMongoId()
    job?: string;

    @ApiProperty({ required: false, enum: Priority, description: 'Priority to filter by' })
    @IsOptional()
    @IsEnum(Priority)
    priority?: string;

    @ApiProperty({ required: false, description: 'Filter by whether job is available in AiRecruiter pool' })
    @IsOptional()
    @IsBoolean()
    isAvailableInAiRecruiterPool?: boolean;

    @ApiProperty({ required: false, description: 'Minimum reward amount' })
    @IsOptional()
    @IsNumber()
    minReward?: number;

    @ApiProperty({ required: false, description: 'Maximum reward amount' })
    @IsOptional()
    @IsNumber()
    maxReward?: number;

    @ApiProperty({ required: false, type: Number, description: 'Page number', default: 1 })
    @IsOptional()
    page?: number;

    @ApiProperty({ required: false, type: Number, description: 'Number of items per page', default: 10 })
    @IsOptional()
    limit?: number;

    @ApiProperty({
        description: 'Due date to filter by (ISO 8601 format)',
        required: true,
        default: new Date(Date.UTC(new Date().getUTCFullYear(), new Date().getUTCMonth(), new Date().getUTCDate(), new Date().getUTCHours(), new Date().getUTCMinutes())),

    })
    @IsDateString()
    @IsISO8601({ strict: true })
    // @Length(10,24)
    dueDate: string;
}