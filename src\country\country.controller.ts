import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Query } from '@nestjs/common';
import { CountryService } from './country.service';
import { CreateCountryDto } from './dto/create-country.dto';
import { UpdateCountryDto } from './dto/update-country.dto';
import { ApiOperation, ApiBearerAuth, ApiResponse, ApiTags, ApiParam, ApiQuery } from '@nestjs/swagger';
import { Roles } from 'src/auth/decorators/roles.decorator';
import { Role } from 'src/auth/enums/role.enum';
import { AuthJwtGuard } from 'src/auth/guards/auth-jwt/auth-jwt.guard';
import { RolesGuard } from 'src/auth/guards/roles/roles.guard';
import { validateObjectId } from 'src/utils/validation.utils';

@Controller('')
@ApiTags('Countries')
export class CountryController {

  constructor(private readonly countryService: CountryService) { }

  // @Post('seed')
  // @ApiBearerAuth()
  // @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.SuperAdmin, Role.Admin)
  // @ApiOperation({ summary: 'Seed countries from JSON file', description: `Seeds countries into the database from a predefined JSON file. This is accessible only for "${Role.SuperAdmin}" , "${Role.Admin}".` })
  // @ApiResponse({ status: 201, description: 'Countries seeded successfully.' })
  // @ApiResponse({ status: 400, description: 'Bad Request / Data ' })
  // @ApiResponse({ status: 401, description: 'Unauthorized ' })
  // @ApiResponse({ status: 403, description: 'Forbidden, user with role super admin and admin can only use this end point.' })
  // seedCountries() {
  //   return this.countryService.seedCountriesFromFile();
  // }

  @Post()
  @ApiOperation({ summary: 'Creates a new country', description: `This endpoint allows you to create a new country. This is accessible only for "${Role.SuperAdmin}" , "${Role.Admin}".` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.SuperAdmin, Role.Admin)
  @Roles()
  @ApiResponse({ status: 201, description: 'Country is saved.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Data ' })
  @ApiResponse({ status: 401, description: 'Unauthorized ' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role super admin and admin can only use this end point.' })
  create(@Body() createCountryDto: CreateCountryDto) {
    return this.countryService.create(createCountryDto);
  }

  @Get('all')
  @ApiOperation({
    summary: 'Retrieve all countries excluding soft-deleted countries',
    description: `This endpoint returns a list of all countries excluding soft-deleted countries. This is accessible by everyone.`
  })
  @ApiResponse({ status: 200, description: 'All countries are retrieved.' })
  @ApiResponse({ status: 404, description: 'No countries found.' })
  getAllCountries() {
    return this.countryService.getAllCountries();
  }

  @Get('all-with-soft-deleted')
  @ApiOperation({
    summary: 'Retrieve all countries including deleted countries',
    description: `This endpoint returns a list of all countries, including soft-deleted ones. This is accessible only for "${Role.SuperAdmin}" and "${Role.Admin}"`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.SuperAdmin, Role.Admin)
  @Roles()
  @ApiResponse({ status: 200, description: 'All countries are retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role SuperAdmin or Admin can only use this endpoint.' })
  @ApiResponse({ status: 404, description: 'No countries found.' })
  getAllCountriesIncludingSoftDeleted() {
    return this.countryService.getAllCountriesWithSoftDeleted();
  }

  @Get('find-all-soft-deleted')
  @ApiOperation({
    summary: 'Retrieve all soft-deleted countries',
    description: `This endpoint returns a list of all soft-deleted countries. This is accessible only for "${Role.SuperAdmin}" and "${Role.Admin}".`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.SuperAdmin, Role.Admin)
  @Roles()
  @ApiResponse({ status: 200, description: 'All soft-deleted countries are retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role SuperAdmin or Admin can only use this endpoint.' })
  @ApiResponse({ status: 404, description: 'No soft-deleted countries found.' })
  getAllSoftDeletedCountries() {
    return this.countryService.getAllSoftDeletedCountries();
  }

  @Get(':countryId')
  @ApiOperation({
    summary: 'Retrieve a country by Id',
    description: 'This endpoint returns a country by its Id. This is accessible by everyone.'
  })
  @ApiResponse({ status: 200, description: 'Country found.' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'Country not found' })
  @ApiParam({ name: 'countryId', description: 'ID of the country to retrieve.' })
  findOne(@Param('countryId') countryId: string) {
    const objId = validateObjectId(countryId);
    return this.countryService.findById(objId);
  }

  @Post('search')
  @ApiOperation({
    summary: 'Search for country',
    description: `This endpoint allows you to search for a country by its name. This is accessible by everyone.`
  })
  @ApiResponse({ status: 200, description: 'Country found.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Invalid Data' })
  @ApiResponse({ status: 404, description: 'Country not found' })
  @ApiQuery({ name: 'name', required: true, type: String, description: 'Name of country to search for', example: "India" })
  search(@Query('name') name: string) {
    return this.countryService.searchByName(name);
  }


  @Patch(':countryId')
  @ApiOperation({
    summary: 'Update a country by Id',
    description: `This endpoint updates a country by its ID. This is accessible only for "${Role.SuperAdmin}", "${Role.Admin}".`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.SuperAdmin, Role.Admin)
  @Roles()
  @ApiResponse({ status: 200, description: 'Country is updated successfully.' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden, only SuperAdmin and Admin can use this endpoint.' })
  @ApiResponse({ status: 404, description: 'Country not found.' })
  @ApiParam({ name: 'countryId', description: 'ID of the country to update.' })
  async update(@Param('countryId') countryId: string, @Body() updateCountryDto: UpdateCountryDto) {
    const objId = validateObjectId(countryId);
    return await this.countryService.update(objId, updateCountryDto);
  }

  @Patch(':countryId/restore')
  @ApiOperation({
    summary: 'Restore a soft-deleted country by ID',
    description: `This endpoint restores a soft-deleted country by its ID. This is accessible only for "${Role.SuperAdmin}" and "${Role.Admin}".`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.SuperAdmin, Role.Admin)
  @Roles()
  @ApiResponse({ status: 200, description: 'Country has been restored successfully.' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden, only SuperAdmin and Admin can use this endpoint.' })
  @ApiResponse({ status: 404, description: 'Country not found or not soft-deleted.' })
  @ApiParam({ name: 'countryId', description: 'ID of the soft-deleted country to restore.' })
  async restoreSoftDeletedCountry(@Param('countryId') countryId: string) {
    const objId = validateObjectId(countryId);
    return await this.countryService.restoreSoftDeletedCountry(objId);
  }

  @Patch('restore')
  @ApiOperation({
    summary: 'Restore all soft-deleted countries',
    description: `This endpoint restores all soft-deleted countries. This is accessible only for "${Role.SuperAdmin}" and "${Role.Admin}".`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.SuperAdmin, Role.Admin)
  @Roles()
  @ApiResponse({ status: 201, description: 'All soft-deleted countries are restored successfully.' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden, only SuperAdmin and Admin can use this endpoint.' })
  @ApiResponse({ status: 404, description: 'No soft-deleted countries found.' })
  restore() {
    return this.countryService.restore();
  }

  @Delete(':countryId/soft-delete')
  @ApiOperation({
    summary: 'Soft delete a country by ID',
    description: `This endpoint soft deletes a country by its ID. This is accessible only for "${Role.SuperAdmin}" and "${Role.Admin}".`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.SuperAdmin, Role.Admin)
  @Roles()
  @ApiResponse({ status: 200, description: 'Country has been soft-deleted successfully.' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden, only SuperAdmin and Admin can use this endpoint.' })
  @ApiResponse({ status: 404, description: 'Country not found.' })
  @ApiParam({ name: 'countryId', description: 'ID of the country to soft delete.' })
  delete(@Param('countryId') countryId: string) {
    const objId = validateObjectId(countryId);
    return this.countryService.softDelete(objId);
  }

}
