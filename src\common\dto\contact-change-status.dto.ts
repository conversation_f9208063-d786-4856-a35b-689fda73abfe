import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty, IsEnum, IsOptional, IsArray } from "class-validator";
import { CommentDto } from "src/common/dto/comment.dto";
import { AccountStatus } from "src/shared/constants";


export class ChangeStatusContactDto {

    @ApiProperty({
        type: String,
        required: true,
        default: AccountStatus.PROSPECT,
        enum: AccountStatus,
        description: 'Status of account, contact, client and task',
    })
    @IsNotEmpty()
    @IsEnum(AccountStatus)
    status: AccountStatus

    @ApiProperty({
        type: [String],
        required: false, // Marking this as optional in Swagger documentation
        description: 'Array of organization IDs to update the status for',
    })
    @IsOptional() // Makes the orgIds field optional
    @IsArray({ message: 'orgIds must be an array' }) // Validation will only run if orgIds is provided
    contactIds?: string[];

    @ApiProperty({
        type: CommentDto,
        required: false
    })
    @IsOptional()
    comment?: CommentDto;
}
