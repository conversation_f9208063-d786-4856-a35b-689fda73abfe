import { ApiHideProperty, ApiProperty } from "@nestjs/swagger";
import { Transform, TransformFnParams } from "class-transformer";
import { IsMongoId, IsNotEmpty, IsOptional, IsString, } from 'class-validator';
import { sanitizeWithStyle } from "src/utils/sanitize-with-style";

export class CreateEducationQualificationDto {

    @ApiProperty({
        type: String,
        required: true,
    })
    @IsNotEmpty()
    @IsMongoId()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    jobId: string;
    
    @ApiProperty({
        type: String,
        required: true,
        description: 'The Highest education degree',
    })
    @IsNotEmpty()
    @IsString()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    courseName: string;

    @ApiProperty({
        type: String,
        required: true,
        description: 'In which university'
    })
    @IsString()
    @IsNotEmpty()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    university: string;

    @ApiProperty({
        type: Date,
        required: false,
        description: 'Start date of the university'
    })
    @IsString()
    @IsOptional()
    startDate?: Date;

    @ApiProperty({
        type: Date,
        required: false,
        description: 'End date of the university'
    })
    @IsString()
    @IsOptional()
    endDate?: Date;

    @ApiHideProperty()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    createdBy?: string;


}

