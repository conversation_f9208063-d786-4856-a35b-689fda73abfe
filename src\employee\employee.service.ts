import { BadRequestException, Injectable, InternalServerErrorException, Logger, NotFoundException } from '@nestjs/common';
import { Model, Types } from 'mongoose';
import { InjectModel } from '@nestjs/mongoose';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { EmailTemplateEvent } from 'src/shared/constants';
import { EmailTemplate } from 'src/email-template-builder/schemas/email-template-builder.schema';
import { Placeholder } from 'src/org/schemas/org.schema';
import { JSONPath } from 'jsonpath-plus';
import { unescape } from 'lodash';
import { JobApplication } from 'src/job-application-form/schemas/job-application.schema';
import { format } from 'date-fns';
import { toZonedTime } from 'date-fns-tz';
import { Job } from 'src/job/schemas/job.schema';
import { CreateBankDetailsDto } from './dto/create-bank-details.dto';
import { BankDetails } from './schemas/bank-details.schema';
import { Offer } from 'src/offer/schemas/offer.schema';
import { BasicUser } from 'src/user/schemas/basic-user.schema';
import { UpdateEmployeeAddressDto } from './dto/update-employee-address.dto';
import { Employee } from './schemas/employee.schema';
import { EmergencyContactDto } from './dto/emergency-contact.dto';
import { EmployeesQueryDTO } from './dto/query.employees.dto';
import moment from 'moment';

@Injectable()
export class EmployeeService {

  private readonly logger = new Logger(EmployeeService.name);
  constructor(
    @InjectModel(Job.name) private jobModel: Model<Job>,
    @InjectModel(JobApplication.name) private jobApplicationModel: Model<JobApplication>,
    private eventEmitter: EventEmitter2,
    @InjectModel(EmailTemplate.name) private emailTemplateService: Model<EmailTemplate>,
    @InjectModel(Placeholder.name) private placeholderService: Model<Placeholder>,
    @InjectModel(JobApplication.name) private jobApplicationService: Model<JobApplication>,
    @InjectModel(BankDetails.name) private bankDetailsModel: Model<BankDetails>,
    @InjectModel(Offer.name) private offerModel: Model<Offer>,
    @InjectModel(BasicUser.name) private basicUserModel: Model<BasicUser>,
    @InjectModel(Employee.name) private employeeModel: Model<Employee>,
  ) { }

  async findAllEmployees(user: any, query: EmployeesQueryDTO) {
    try {

      const { search, page = 1, limit = 10, jobType, fromDate, toDate } = query;

      const orgId = user.org?._id?.toString();
      if (!orgId) {
        throw new BadRequestException('Invalid organization');
      }

      const conditions: any = {
        payRollOrg: orgId,
        isDeleted: false,
      };
      if (search) {
        const searchRegex = new RegExp(search, 'i'); // Case-insensitive search
        conditions.$or = [
          { firstName: searchRegex },
          { lastName: searchRegex },
          { email: searchRegex },
          { contactNumber: searchRegex },
        ];
      }

      if (fromDate) {
        conditions.createdAt = { ...conditions.createdAt, $gte: moment(fromDate).startOf('day').toDate() };
      }
      if (toDate) {
        conditions.createdAt = {
          ...conditions.createdAt,
          $lte: moment(toDate).endOf('day').toDate(),
        };
      }

      const employees = await this.employeeModel.find(conditions)
        .populate([
          { path: 'payRollOrg', select: '_id title' },
          { path: 'createdBy', select: '_id firstName lastName email' },
        ]).skip((page - 1) * limit)
        .limit(limit)
        .exec();

      const totalCount = await this.employeeModel.countDocuments(conditions);


      return {
        data: employees,
        pagination: {
          total: totalCount,
          limit,
          page,
        },
      };
      // return employees;
    } catch (error) {
      this.logger.error(`An error occurred while fetching bank details: ${error.message}`);
      throw error;

    }
  }

  async findEmployeeById(user: any, employeeId: string) {
    try {
      const orgId = user.org?._id?.toString();
      if (!orgId) {
        throw new BadRequestException('Invalid organization');
      }

      const employee = await this.employeeModel.findOne({
        _id: employeeId,
        payRollOrg: orgId,
        isDeleted: false,
      })
        .populate([
          { path: 'payRollOrg', select: '_id title' },
          { path: 'createdBy', select: '_id firstName lastName email' },
        ])
        .exec();

      if (!employee) {
        throw new NotFoundException('Employee not found for the given ID and organization');
      }

      return employee;
    } catch (error) {
      this.logger.error(`An error occurred while fetching employee ${employeeId}: ${error.message}`);
      throw error;
    }
  }

  async saveBankDetails(user: any, createBankDetailsDto: CreateBankDetailsDto) {
    try {
      createBankDetailsDto.createdBy = user._id.toString();

      const userDetails = await this.basicUserModel.findById(user._id).exec();

      // Check if revenue details already exist for the employee
      const existingBankDetails = await this.bankDetailsModel.findOne({
        empId: userDetails?.employeeId,
        isActive: true,
        isDeleted: false
      });

      if (existingBankDetails) {
        existingBankDetails.isDeleted = true;
        existingBankDetails.isActive = false;

        await existingBankDetails.save();
        // return existingRevenue;
      }

      // Create and save the TempEmployee
      (createBankDetailsDto.empId as any) = userDetails?.employeeId;
      const createdBankDetails = new this.bankDetailsModel(createBankDetailsDto);
      await createdBankDetails.save();

      const employee = await this.employeeModel.findById(userDetails?.employeeId).exec();
      if (!employee) {
        throw new NotFoundException('Employee not found');
      }

      (employee.bankDetails as any) = createdBankDetails?._id?.toString();
      await employee.save();

      return createdBankDetails;

    } catch (error) {
      this.logger.error(`Failed to create and send offer. ${error.message}`);
      throw new InternalServerErrorException(`Error in creating and sending offer. ${error?.message}`);
    }
  }

  async findMyBankDetails(user: any) {
    try {
      // const offer = await this.offerModel.findById(offerId)
      const userDetails = await this.basicUserModel.findById(user._id).exec();

      // Check if revenue details already exist for the employee
      const existingBankDetails = await this.bankDetailsModel.findOne({
        empId: userDetails?.employeeId,
        isActive: true,
        isDeleted: false
      });
      if (!existingBankDetails) {
        throw new NotFoundException(`Bank details not found for employee`);
      }
      return existingBankDetails;
    }
    catch (error) {
      this.logger.error(`An error occurred while fetching bank details: ${error.message}`);
      throw error;

    }
  }

  async findEmployeeBankDetails(employeeId: string, user: any) {
    try {
      // Check if revenue details already exist for the employee
      const existingBankDetails = await this.bankDetailsModel.findOne({
        empId: employeeId.toString(),
        isActive: true,
        isDeleted: false
      });
      if (!existingBankDetails) {
        throw new NotFoundException(`Bank details not found for employee`);
      }
      return existingBankDetails;
    }
    catch (error) {
      this.logger.error(`An error occurred while fetching bank details: ${error.message}`);
      throw error;

    }
  }

  async updateEmployeeAddressDetails(
    user: any,
    dto: UpdateEmployeeAddressDto,
  ): Promise<Employee> {

    const userDetails = await this.basicUserModel.findById(user._id).exec();
    const employee = await this.employeeModel.findById(userDetails?.employeeId).exec();
    if (!employee) throw new NotFoundException('Employee not found');

    (employee.currentAddress as any) = dto.currentAddress;
    employee.sameAsCurrentAddress = dto.sameAsCurrentAddress;

    if (dto.sameAsCurrentAddress) {
      (employee.permanentAddress as any) = { ...dto.currentAddress };
    } else {
      if (!dto.permanentAddress) {
        throw new BadRequestException('Permanent address required if not same as current');
      }
      (employee.permanentAddress as any) = dto.permanentAddress;
    }

    return employee.save();
  }

  async findMyAddressDetails(user: any) {
    try {
      // Check if revenue details already exist for the employee
      const userDetails = await this.basicUserModel.findById(user._id).exec();
      const employee = await this.employeeModel.findById(userDetails?.employeeId).exec();
      if (!employee) {
        throw new NotFoundException(`Employee not found`);
      }
      return { currentAddress: employee.currentAddress, permanentAddress: employee.permanentAddress, employeeId: employee._id?.toString() };
    }
    catch (error) {
      this.logger.error(`An error occurred while fetching bank details: ${error.message}`);
      throw error;

    }
  }

  async updateEmergencyContact(user: any, dto: EmergencyContactDto): Promise<Employee> {

    const userDetails = await this.basicUserModel.findById(user._id).exec();
    const employee = await this.employeeModel.findById(userDetails?.employeeId).exec();

    if (!employee) throw new NotFoundException('Employee not found');

    employee.emergencyContact = dto;
    return employee.save();
  }

  async findMyEmergencyContactDetails(user: any) {
    try {
      // Check if revenue details already exist for the employee
      const userDetails = await this.basicUserModel.findById(user._id).exec();
      const employee = await this.employeeModel.findById(userDetails?.employeeId).exec();
      if (!employee) {
        throw new NotFoundException(`Employee not found`);
      }
      return { emergencyContactDetails: employee.emergencyContact, employeeId: employee._id?.toString() };
    }
    catch (error) {
      this.logger.error(`An error occurred while fetching bank details: ${error.message}`);
      throw error;

    }
  }

}
