// dto/update-org.dto.ts

import { ApiHideProperty, ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsArray, IsBoolean, IsEmail, IsEnum, IsISO8601, IsMongoId, IsNotEmpty, IsOptional, IsString, ValidateIf, ValidateNested } from "class-validator";
import { sanitizeWithStyle } from "src/utils/sanitize-with-style";
import { Transform, TransformFnParams, Type } from "class-transformer";
import { AccountType } from "src/shared/constants";

export class UpdateAddressDetailsDto {
    @ApiProperty({
        type: String,
        required: false,
        description: "Enter Door, suite, apartment information"
    })
    @IsOptional()
    @IsString()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value).toLowerCase())
    addressLine?: string;

    @ApiProperty({
        type: String,
        required: false,
        description: "Enter city"
    })
    @IsMongoId()
    @IsOptional()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value).toLowerCase())
    city?: string;

    @ApiProperty({
        type: String,
        required: false,
        description: "Enter state"
    })
    @IsMongoId()
    @IsOptional()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value).toLowerCase())
    state?: string;

    @ApiProperty({
        type: String,
        required: false,
        description: "Enter country"
    })
    @IsMongoId()
    @IsOptional()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value).toLowerCase())
    country?: string;

    @ApiProperty({
        type: String,
        required: false,
        description: "Enter Door, suite, apartment information"
    })
    @IsOptional()
    @IsString()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value).toLowerCase())
    postalCode?: string;

    @ApiProperty({
        type: String,
        required: false,
        description: 'Contact e-mail'
    })
    @IsOptional()
    @IsString()
    @IsEmail()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value).toLowerCase())
    email?: string;

    @ApiProperty({
        type: String,
        required: false,
        description: 'Contact number'
    })
    @IsString()
    @IsOptional()
    // @IsPhoneNumber()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    contactNumber?: string;

    @ApiProperty({
        type: String,
        required: false,
        description: 'Contact number'
    })
    @IsString()
    @IsOptional()
    // @IsPhoneNumber()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    lutNumber?: string;

    @ApiProperty({
        type: Date,
        required: false,
        description: "The end date of the recurring invoice (required if `neverExpires` is false).",
        default: new Date(Date.UTC(new Date().getUTCFullYear(), new Date().getUTCMonth(), new Date().getUTCDate(), new Date().getUTCHours(), new Date().getUTCMinutes())),
    })
    @IsISO8601({ strict: true })
    @IsOptional()
    lutDate?: Date;
}

export class UpdateBankDetailsDto {

    @ApiProperty({
        type: String,
        required: true,
    })
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    @IsString()
    accountHolderName: string;

    @ApiProperty({
        type: String,
        required: true,
    })
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    @IsString()
    accountNumber: string;

    @ApiProperty({
        type: String,
        required: true,
    })
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    @IsString()
    ifscCode: string;

    @ApiProperty({
        type: String,
        required: true,
    })
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    @IsString()
    bankName: string;

    @ApiProperty({
        type: String,
        required: false,
    })
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    @IsString()
    @IsOptional()
    branchName?: string;


    @ApiProperty({
        type: String,
        required: false,
        enum: AccountType,
        description: 'Account type',
    })
    @IsString()
    @IsOptional()
    @ValidateIf((obj) => obj.employmentType === '' || Object.values(AccountType).includes(obj.AccountType))
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    accountType?: string;


    @ApiProperty({
        type: String,
        required: false,
    })
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    @IsString()
    gstin: string;
}
