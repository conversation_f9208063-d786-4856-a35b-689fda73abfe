import { Controller, Get, Post, Body, Patch, Param, Delete, Logger, UseGuards, BadRequestException, Req } from '@nestjs/common';
import { IndustryService } from './industry.service';
import { CreateIndustryDto } from './dto/create-industry.dto';
import { UpdateIndustryDto } from './dto/update-industry.dto';
import { ApiBearerAuth, ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { AuthJwtGuard } from 'src/auth/guards/auth-jwt/auth-jwt.guard';
import { RolesGuard } from 'src/auth/guards/roles/roles.guard';
import { Roles } from 'src/auth/decorators/roles.decorator';
import { Role } from 'src/auth/enums/role.enum';
import { validateObjectId } from 'src/utils/validation.utils';



@Controller('')
@ApiTags('Industries')
export class IndustryController {

  private readonly logger = new Logger(IndustryController.name);

  constructor(private readonly industryService: IndustryService) { }

  @Post()
  @ApiResponse({ status: 201, description: 'Industry is saved.' })
  @ApiResponse({ status: 401, description: 'Industry not logged in.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Data.' })
  @ApiResponse({ status: 403, description: 'Forbidden, User with role admin can only use this end point.' })
  @ApiOperation({ summary: 'Create a new Industry', description: `This endpoint allows you to create a new industry.This endpoint is accessible only for "admin".` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin)
  @Roles()
  create(@Req() req: any, @Body() createIndustryDto: CreateIndustryDto) {
    createIndustryDto.createdBy = req.user._id;
    return this.industryService.create(createIndustryDto);
  }

  @Get()
  @ApiResponse({ status: 200, description: 'Industries retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  // @ApiResponse({ status: 403, description: 'Forbidden, user with role admin and salesrep can only use this end point.' })
  @ApiOperation({ summary: 'Retrieve all Industries', description: `This endpoint returns a list of all industries. This endpoint is accessible for everyone.` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard,RolesGuard)
  // @Roles(Role.Admin, Role.AccountManager, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.BUHead, Role.Recruiter, Role.Vendor)
  @Roles()
  findAll() {
    return this.industryService.findAll();
  }

  @Get('find-all-soft-deleted')
  @ApiResponse({ status: 200, description: 'Industries retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  // @ApiResponse({ status: 403, description: 'Forbidden, User with role admin or sales-rep can only use this end point.' })
  @ApiOperation({ summary: 'Retrieve all soft deleted Industries', description: `This endpoint returns a list of all industries that are soft deleted industrys. This end point is accessible for everyone.` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.AccountManager, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.BUHead, Role.Recruiter, Role.Vendor)
  @Roles()
  findAllSoftDeleted() {
    return this.industryService.findAllSoftDeleted();
  }

  @Get('find-all-with-soft-deleted')
  @ApiResponse({ status: 200, description: 'Industries retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  // @ApiResponse({ status: 403, description: 'Forbidden, User with role admin or sales-rep can only use this end point.' })
  @ApiOperation({ summary: 'Retrieve all Industries along with soft-deleted industries', description: `This endpoint returns a list of all industries including soft deleted industries. This end point is accessible for everyone.` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.AccountManager, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.BUHead, Role.Recruiter, Role.Vendor)
  @Roles()
  findAllWithSoftDeleted() {
    return this.industryService.findAllIncludingSoftDeleted();
  }

  @Get(':industryId')
  @ApiResponse({ status: 200, description: 'Industry is retrieved.' })
  @ApiResponse({ status: 404, description: 'Industry Type not found.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  // @ApiResponse({ status: 403, description: 'Forbidden user with role admin can only use this end point.' })
  @ApiParam({ name: 'industryId', description: 'id of the Industry.' })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  // @Roles(Role.Admin, Role.AccountManager, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.BUHead, Role.Recruiter, Role.Vendor)
  @Roles()
  @ApiOperation({ summary: 'Retrieve an Industry by Id', description: `This endpoint returns an industry by its Id. This endpoint accessible for everyone.` })
  findOne(@Param('industryId') industryId: string) {
    const objId = validateObjectId(industryId);
    return this.industryService.findOne(objId);
  }


  @Patch(':industryId')
  @ApiParam({ name: 'industryId', description: 'id of the Industry.' })
  @ApiResponse({ status: 200, description: 'Industry updated.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role admin can only use this end point.' })
  @ApiOperation({ summary: 'Update an Industry by Id', description: `This endpoint updates an industry by Id. This is accessible only for "admin".` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin)
  @Roles()
  async update(@Param('industryId') industryId: string, @Body() updateIndustryDto: UpdateIndustryDto) {
    const objId = validateObjectId(industryId);
    return await this.industryService.update(objId, updateIndustryDto);
  }

  @Patch(':industryId/restore')
  @ApiParam({ name: 'industryId', description: 'id of the Industry.' })
  @ApiResponse({ status: 200, description: 'Industry updated.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role admin can only use this end point.' })
  @ApiOperation({ summary: 'Restore soft deleted Industry by Id.', description: `This endpoint restores a soft-deleted industry by Id. This endpoint is accessible only for "admin".` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin)
  @Roles()
  async restoreSoftDeletedIndustry(@Param('industryId') industryId: string) {
    const objId = validateObjectId(industryId);
    return await this.industryService.restoreSoftDeletedIndustry(objId);
  }


  @Delete(':industryId/soft-delete')
  @ApiResponse({ status: 200, description: 'Indutry is deleted.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role admin can only use this end point.' })
  @ApiParam({ name: 'industryId', description: 'id of the Industry.' })
  @ApiOperation({ summary: 'soft delete an Industry by Id', description: `This endpoint deletes an industry by Id. This endpoint is accessible only for "admin".` })
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin)
  @Roles()
  @ApiBearerAuth()
  remove(@Param('industryId') industryId: string) {
    const objId = validateObjectId(industryId);
    return this.industryService.remove(objId);
  }

  @Delete(':industryId/hard-delete')
  @ApiResponse({ status: 200, description: 'Industry is hard deleted.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: `Forbidden, user with role admin can only use this end point.` })
  @ApiParam({ name: 'industryId', description: 'ID of the Industry.' })
  @ApiOperation({ summary: 'Delete an Industry by Id', description: `This endpoint soft deletes an industry by Id. This endpoint is accessible only for "admin".` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin)
  @Roles()
  async hardDelete(@Param('industryId') industryId: string) {
    const objId = validateObjectId(industryId);
    return this.industryService.hardDelete(objId);

  }

  @Delete('all')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin)
  @Roles()
  @ApiOperation({ summary: 'Remove  all industries', description: `This endpoint deletes all industries. This is accessible only for "admin".` })
  @ApiResponse({ status: 200, description: 'All industries deleted.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  @ApiResponse({ status: 403, description: 'Forbidden, User with role admin can only use this end point.' })
  deleteAll() {
    return this.industryService.deleteAll();
  }


}
