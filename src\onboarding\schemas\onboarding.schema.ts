import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Types } from 'mongoose';

import { Identifier } from 'src/identifier/schemas/identifier.schema';
import { FileMetadata } from 'src/file-upload/schemas/file-metadata.schema';
import { BasicUser } from 'src/user/schemas/basic-user.schema';
import { Org } from 'src/org/schemas/org.schema';

export type OnboardingDocument = HydratedDocument<Onboarding>;

export type IdentifierDataRow = {
    identifier: Identifier;
    value?: string;
    attachmentUrls?: FileMetadata[];
}

@Schema({
    timestamps: true
})
export class Onboarding {

    @Prop({
        required: true,
        type: [{
            identifier: { type: Types.ObjectId, ref: 'Identifier' },
            value: String,
            attachmentUrls: [{ type: Types.ObjectId, ref: 'FileMetadata' }]
        }]
    })
    identifiers: IdentifierDataRow[];

    @Prop({ required: false, type: Types.ObjectId, ref: 'BasicUser' })
    user?: BasicUser;

    @Prop({
        required: false,
        type: Types.ObjectId, ref: 'Org'
    })
    org?: Org;

    @Prop({
        type: Types.ObjectId,
        required: false,
        ref: 'BasicUser'
      })
      createdBy?: BasicUser;

}

export const OnboardingSchema = SchemaFactory.createForClass(Onboarding);