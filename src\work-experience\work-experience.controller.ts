import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Req } from '@nestjs/common';
import { WorkExperienceService } from './work-experience.service';
import { CreateWorkExperienceDto } from './dto/create-work-experience.dto';
import { UpdateWorkExperienceDto } from './dto/update-work-experience.dto';
import { ApiBearerAuth, ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { AuthJwtGuard } from 'src/auth/guards/auth-jwt/auth-jwt.guard';
import { Roles } from 'src/auth/decorators/roles.decorator';
import { Role } from 'src/auth/enums/role.enum';
import { RolesGuard } from 'src/auth/guards/roles/roles.guard';
import { validateObjectId } from 'src/utils/validation.utils';

@Controller('')
@ApiTags('Work-Experiences')
export class WorkExperienceController {
  constructor(private readonly workExperienceService: WorkExperienceService) {}

  @Post()
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.AccountManager, Role.TeamLead, Role.TeamMember, Role.Recruiter, Role.Vendor, Role.JobSeeker, Role.Freelancer)
  @Roles()
  @ApiResponse({ status: 201, description: 'Work experience is saved.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Data. ' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role "BUHead", "TeamLead", "DeliveryManager" and "Recruiter" can only use this end point.' })
  @ApiOperation({ summary: 'Create a work experience', description: `This endpoint for creating a work experience. This is only accessible for "BUHead", "TeamLead", "DeliveryManager" and "Recruiter".` })
  create(@Req() req: any, @Body() createWorkExperienceDto: CreateWorkExperienceDto) {
    createWorkExperienceDto.createdBy = req.user._id
    return this.workExperienceService.create(createWorkExperienceDto);
  }


  @Get('all')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.AccountManager, Role.TeamLead, Role.TeamMember, Role.Recruiter, Role.Vendor, Role.JobSeeker, Role.Freelancer)
  @Roles()
  @ApiOperation({ summary: 'Retrieve all work experiences', description: `This endpoint returns a list of all work experiences. This is accessible for everyone. ` })
  @ApiResponse({ status: 200, description: 'Work experience retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  // @ApiResponse({ status: 403, description: 'Forbidden, user with role admin and salesrep  can only use this end point.' })
  findAll() {
    return this.workExperienceService.findAll();
  }


  @Get(':workExperienceId')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.AccountManager, Role.TeamLead, Role.TeamMember, Role.Recruiter, Role.Vendor, Role.JobSeeker, Role.Freelancer)
  @Roles()
  @ApiOperation({ summary: 'Retrieve an work experience by Id', description: 'This endpoint returns an work experience by its Id. This is accessible for everyone.' })
  @ApiResponse({ status: 200, description: 'Work experience is retrieved.' })
  @ApiResponse({ status: 404, description: 'Work experience not found.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  // @ApiResponse({ status: 403, description: 'Forbidden, user with role admin can only use this end point.' })
  @ApiParam({ name: 'workExperienceId', description: 'Id of the work experience.' })
  findOne(@Param('workExperienceId',) workExperienceId: string) {
    const objId = validateObjectId(workExperienceId);
    return this.workExperienceService.findById(objId);
  }


  @Patch(':workExperienceId')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.AccountManager, Role.TeamLead, Role.TeamMember, Role.Recruiter, Role.Vendor, Role.JobSeeker, Role.Freelancer)
  @Roles()
  @ApiOperation({ summary: 'Update an work experience by Id', description: `This endpoint updates an work experience by Id. This is only accessible for "BUHead", "TeamLead", "DeliveryManager" and "Recruiter".`})
  @ApiResponse({ status: 200, description: 'Work experience is saved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role "BUHead", "TeamLead", "DeliveryManager" and "Recruiter" can only use this end point.' })
  @ApiParam({ name: 'workExperienceId', description: 'Id of the work experience.' })
  update(@Param('workExperienceId',) workExperienceId: string, @Body() updateworkExperienceDto: UpdateWorkExperienceDto) {
    const objId = validateObjectId(workExperienceId);
    return this.workExperienceService.update(objId, updateworkExperienceDto);
  }

  @Delete(':workExperienceId/hard-delete')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.AccountManager, Role.TeamLead, Role.TeamMember, Role.Recruiter, Role.Vendor, Role.JobSeeker, Role.Freelancer)
  @Roles()
  @ApiOperation({ summary: 'Delete an work experience by Id', description: 'This endpoint deletes an work experience by Id. This is only accessible for "BUHead", "TeamLead", "DeliveryManager" and "Recruiter".' })
  @ApiResponse({ status: 200, description: 'Work experience is hard deleted.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role "BUHead", "TeamLead", "DeliveryManager" and "Recruiter" can only use this end point.' })
  @ApiParam({ name: 'workExperienceId', description: 'Id of the work experience' })
  remove(@Param('workExperienceId') workExperienceId: string) {
    const objId = validateObjectId(workExperienceId);
    return this.workExperienceService.remove(objId);
  }
}
