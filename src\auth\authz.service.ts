import { Injectable, Logger } from '@nestjs/common';
import { newEnforcer } from 'casbin';
// import { newAdapter } from 'casbin-mongoose-adapter';
import { MongooseAdapter } from 'casbin-mongoose-adapter';
import { ConfigService } from '@nestjs/config';
import path from 'path';

@Injectable()
export class AuthzService {
    private enforcer: any;

    private readonly logger = new Logger(AuthzService.name);

    constructor(private configService: ConfigService) {
  
    }
    async getEnforcer(): Promise<any> {
        const model = path.resolve(__dirname, './your_model.conf');
        const mongoUri = this.configService.get<string>('MONGODB_URI')
        if(!this.enforcer) {
            // const adapter = await MongooseAdapter.newAdapter('mongodb://<user>:<password>@<host>:<port>/<database>');
            const adapter = await MongooseAdapter.newAdapter(`${mongoUri}`);
            this.enforcer = await newEnforcer(model, adapter);

            // this.enforcer = await newEnforcer('path/to/model.conf', adapter);
        }
        this.logger.debug(`Casbin Enforcer is SET`)
        return this.enforcer;
    }
}