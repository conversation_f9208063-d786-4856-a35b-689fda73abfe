import { PartialType } from '@nestjs/swagger';
import { CreateBusinessUnitDto } from './create-business-unit.dto';
import { IsOptional, IsString } from 'class-validator';
import { Transform } from 'class-transformer';

export class GetDepartmentTreeDto {
  @IsOptional()
  @IsString()
  type?: string;

  @IsOptional()  // ✅ Makes deptName optional
  @IsString()
  deptName?: string;

  @IsOptional()
  @Transform(({ value }) => {
    return value === undefined || value === '0' ? '' : value;
  })
  isJobAllocation?: boolean;
}
