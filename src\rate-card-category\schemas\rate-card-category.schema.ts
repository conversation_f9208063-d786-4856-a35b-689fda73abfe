import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";
import { HydratedDocument, Types } from 'mongoose';
import { Org } from "src/org/schemas/org.schema";
import { BasicUser } from "src/user/schemas/basic-user.schema";
import { BusinessUnit } from "src/business-unit/schemas/business-unit.schema";
import { RateCard } from "src/rate-card/schemas/rate-card.schema";

export type RateCardCategoryDocument = HydratedDocument<RateCardCategory>;

@Schema({
    timestamps: true
})
export class RateCardCategory {

    @Prop({
        type: String,
        required: true,
        trim: true,
        maxlength: 1000
    })
    label: string;

    @Prop({
        type: String,
        required: false,
        trim: true,
        maxlength: 1000
    })
    description?: string

    // @Prop({
    //     type: Types.ObjectId,
    //     ref: 'RateCardCategory',
    //     required: false
    // })
    // parentCategory?: RateCardCategory | Types.ObjectId;

    @Prop({
        type: Types.ObjectId,
        ref: 'Org',
        required: true
    })
    org: Org;

    @Prop({
        type: Types.ObjectId,
        ref: 'BasicUser',
        required: true
    })
    createdBy: BasicUser;

    @Prop([{ type: Types.ObjectId, ref: 'RateCard' }])
    children?: (Types.ObjectId | RateCard)[];

    @Prop({
        type: Boolean,
        required: false,
        default: false
    })
    isDeleted?: boolean;
    
    @Prop({
        type: Types.ObjectId,
        ref: 'Org',
        required: true
    })
    client: Org;
}

export const RateCardCategorySchema = SchemaFactory.createForClass(RateCardCategory);
