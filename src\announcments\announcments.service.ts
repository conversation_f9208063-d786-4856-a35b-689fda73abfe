import { Injectable, Logger } from '@nestjs/common';
import { CreateAnnouncmentDto } from './dto/create-announcment.dto';
import { UpdateAnnouncmentDto } from './dto/update-announcment.dto';
import { ConfigService } from '@nestjs/config';
import { Announcments } from './schemas/announcment.schema';
import { Model, Types } from 'mongoose';
import { InjectModel } from '@nestjs/mongoose';
import { JobAllocationBase } from 'src/job-allocation/schemas/job-allocation-base.schema';

@Injectable()
export class AnnouncmentsService {
  private readonly logger = new Logger(AnnouncmentsService.name);
  constructor(private configService: ConfigService,
    @InjectModel(Announcments.name) private AnnouncmentsModel: Model<Announcments>,
    @InjectModel(JobAllocationBase.name) private JobAllocationModel: Model<JobAllocationBase>
  ) { }
  create(createAnnouncmentDto: CreateAnnouncmentDto) {
    const createdAnnouncment = new this.AnnouncmentsModel(createAnnouncmentDto);
    return createdAnnouncment.save();
  }

  async findAll(userId: string, orgId: string, page = 1, limit = 10, search?: string,jobId?: string) {
    const skip = (page - 1) * limit;
    const query: any = {
      org: orgId,
      isDeleted: false,
      $or: [
      // { assignTo: userId },           // User is assigned
      { createdBy: userId },          // User is the creator
    ],
    };

    if(jobId) {
      query.job = jobId; // Filter by specific job ID
    }

    if (search) {
      query.title = { $regex: search, $options: 'i' }; // Case-insensitive search on title
    }

    const [data, total] = await Promise.all([
      this.AnnouncmentsModel.find(query)
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit),
      this.AnnouncmentsModel.countDocuments(query),
    ]);

    return {
      data,
      total,
      page,
      totalPages: Math.ceil(total / limit),
    };
  }

  async findForAssignee(userId: string) {
  // Step 1: Get job allocations for this user
  const allocations = await this.JobAllocationModel.find({ assignee: userId.toString() ,isDeleted: false,kind:'JobAllocationToAssignees'});
  const jobIds = allocations.map(allocation => allocation.job);

  // Step 2: Return announcements linked to those jobs
  return this.AnnouncmentsModel.find({
    // job: { $in: jobIds },
    isDeleted: false,
    $or: [
      { job: { $in: jobIds } },
      { assignTo: userId.toString() }  // If assignTo is an array
    ]
  }).sort({ createdAt: -1 });
}


  async findOne(announcmentId: string) {
    return this.AnnouncmentsModel.findById(announcmentId);
  }

  update(announcmentId: string, updateAnnouncmentDto: UpdateAnnouncmentDto) {
    return this.AnnouncmentsModel.findByIdAndUpdate(announcmentId, updateAnnouncmentDto, { new: true });
  }


  async remove(announcmentId: Types.ObjectId) {

    return this.AnnouncmentsModel.findByIdAndUpdate(announcmentId, { isDeleted: true }, { new: true });
  }

}
