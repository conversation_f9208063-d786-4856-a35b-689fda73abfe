import { ApiHideProperty, ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsArray, IsBoolean, IsEnum, IsMongoId, IsNotEmpty, IsOptional, IsString, ValidateNested } from "class-validator";
import { Transform, TransformFnParams, Type } from "class-transformer";
import { sanitizeWithStyle } from "src/utils/sanitize-with-style";

export class CreateRolesDto {

  @ApiProperty({
    type: String,
    required: true,
    description: 'Role name you want to create',
  })
  @IsNotEmpty()
  @IsString()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  role: string;

  @ApiHideProperty()
  @IsOptional()
  @IsString()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  roleAlias?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'The description of your Role',
  })
  @IsString()
  @IsOptional()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  description?: string;

  @ApiHideProperty()
  // @ApiPropertyOptional({
  //   type: String,
  //   required: false,
  //   description: 'The parent org of your org',
  // })
  @IsOptional()
  // @IsMongoId()
  @IsString()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  orgId?: string;

  @ApiHideProperty()
  @IsOptional()
  @IsString()
  createdBy?: string;

  @ApiProperty({ type: Boolean, required: false, default: false, description: 'Default email template' })
  @IsOptional()
  isDefault?: boolean;

}
