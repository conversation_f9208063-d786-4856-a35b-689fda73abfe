import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, BadRequestException } from '@nestjs/common';
import { DeleteUserService } from './delete-user.service';
import { CreateDeleteUserDto } from './dto/create-delete-user.dto';
import { UpdateDeleteUserDto } from './dto/update-delete-user.dto';
import { ApiBearerAuth, ApiBody, ApiTags } from '@nestjs/swagger';
import { AuthJwtGuard } from 'src/auth/guards/auth-jwt/auth-jwt.guard';
import { RolesGuard } from 'src/auth/guards/roles/roles.guard';
import { Roles } from 'src/auth/decorators/roles.decorator';
import { Role } from 'src/auth/enums/role.enum';
import { validateObjectId } from 'src/utils/validation.utils';


@Controller()
@ApiTags('delete-user')
export class DeleteUserController {
  constructor(private readonly deleteUserService: DeleteUserService) { }

  @Post()
  create(@Body() createDeleteUserDto: CreateDeleteUserDto) {
    return this.deleteUserService.create(createDeleteUserDto);
  }

  @Get()
  findAll() {
    return this.deleteUserService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.deleteUserService.findOne(id);
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() updateDeleteUserDto: UpdateDeleteUserDto) {
    return this.deleteUserService.update(+id, updateDeleteUserDto);
  }

  @Delete(':deleteUserId')
  remove(@Param('deleteUserId') deleteUserId: string) {
    return this.deleteUserService.remove(deleteUserId);
  }

  @Patch(':id/delete-status')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.BUHead, Role.TeamLead, Role.DeliveryManager, Role.Recruiter, Role.Vendor, Role.SuperAdmin)
  @Roles()
  @ApiBody({ type: UpdateDeleteUserDto, description: 'The status to update' })  
  updateDeleteStatus(
    @Param('id') id: string,
    @Body() body: { status: 'APPROVED' | 'REJECTED' | 'PENDING' }
  ) {
    if (!body || !body.status) {
      throw new BadRequestException('Status is required');
    }
    const objId = validateObjectId(id);
    return this.deleteUserService.approveOrRejectDeletion(objId, body.status);
  }
  

}
