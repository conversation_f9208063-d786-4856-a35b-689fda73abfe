import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Types } from 'mongoose';

export type CountryDocument = HydratedDocument<Country>;

@Schema({
  timestamps: true
})
export class Country {

  @Prop({
    required: true,
    type: Number,
  })
  countryId: number;

  @Prop({
    type: String,
    required: true,
    trim: true
  })
  countryName: string;

  @Prop({
    type: String,
    required: true,
    trim: true
  })
  countryPhoneCode: string;

  @Prop({
    type: String,
    required: true,
    trim: true
  })
  currencyCode: string;

  @Prop({
    type: Boolean,
    required: false,
    default: false
  })
  isDeleted?: boolean;

}

export const CountrySchema = SchemaFactory.createForClass(Country);

CountrySchema.index({ countryName: 1 })

