import { BadRequestException, HttpException, HttpStatus, Injectable, InternalServerErrorException, Logger, NotFoundException } from '@nestjs/common';
import { CreateRolesDto } from './dto/create-roles.dto';
// import { UpdateStatusDto } from './dto/update-status.dto';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Roles, RolesDocument } from './schemas/roles.schema';
import { UpdateRolesDto } from './dto/update-roles.dto';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';
import { Role } from 'src/auth/enums/role.enum';
import { EndpointsRoles } from 'src/endpoints-roles/schemas/endpoints-roles.schema';

@Injectable()
export class RolesService {

    private readonly logger = new Logger(RolesService.name);

    constructor(@InjectModel(Roles.name) private rolesModel: Model<Roles>,
    @InjectModel(EndpointsRoles.name) private endpointsRolesModel: Model<EndpointsRoles>,
        private eventEmitter: EventEmitter2,) {
    }

    private normalizeRole(role: string): string {
        // return role.replace(/[_\s]+/g, ' ').toUpperCase(); // Replace underscores/spaces & convert to uppercase
        // return role
        //     .toLowerCase() // Convert to lowercase
        //     .replace(/[_\s]+/g, ' ') // Replace underscores and multiple spaces with a single space
        //     .replace(/\b\w/g, char => char.toUpperCase()); // Capitalize the first letter of each word
        return role.replace(/[_\s]+/g, ' ').trim();// Replace underscores/spaces & trim whitespace
    }

    //   private toTitleCase(role: string): string {
    //     return role
    //       .toLowerCase() // Convert to lowercase
    //       .replace(/[_\s]+/g, ' ') // Replace underscores and multiple spaces with a single space
    //       .replace(/\b\w/g, char => char.toUpperCase()); // Capitalize the first letter of each word
    //   }

    emitEvent(eventName: string, payload: any) {
        // payload['event']= eventName;
        this.logger.debug(payload)
        this.eventEmitter.emit(eventName, payload);
    }

    async create(createStatusDto: CreateRolesDto): Promise<RolesDocument> {
        try {
            createStatusDto.roleAlias = this.normalizeRole(createStatusDto.role);
            createStatusDto.role = this.normalizeRole(createStatusDto.role);
            if (createStatusDto.isDefault) {
                const roleNameLowerCase = createStatusDto.role.toLowerCase();
                const existingDefaultRole = await this.rolesModel.findOne({
                    role: { $regex: new RegExp(`^${roleNameLowerCase}$`, 'i') },// Case-insensitive search
                    isDefault: true,
                    isDeleted: false
                }).exec();
                if (existingDefaultRole) {
                    throw new BadRequestException(`A default role ${createStatusDto.role} already exists.`);
                }
            }
            else {
                const roleNameLowerCase = createStatusDto.role.toLowerCase();
                const existingRole = await this.rolesModel.findOne({
                    role: { $regex: new RegExp(`^${roleNameLowerCase}$`, 'i') },// Case-insensitive search
                    isDefault: false,
                    orgId: createStatusDto.orgId,
                    isDeleted: false
                }).exec();
                if (existingRole) {
                    throw new BadRequestException(`A role ${createStatusDto.role} already exists.`);
                }
            }
            const createdStatus = new this.rolesModel(createStatusDto);
            createdStatus.canDelete = true;
            await createdStatus.save();

            const populatedRole = await this.rolesModel.findById(createdStatus._id).populate({
                path: 'orgId',
                select: 'title',  // Fetch only the title field from the Organization
            }).exec();

            this.emitEvent('org.CloneDefaultPermissionsRoleCreated', {
                org: createdStatus?.orgId?.toString()
              });

            if (!populatedRole) {
                throw new BadRequestException('Role creation failed, please try again.');
            }

            return populatedRole;

        } catch (error) {
            this.logger.error('Failed to create Role', error);
            throw new BadRequestException(`Error when creating Role. ${error.message}`);
        }
    }

    async findAll(): Promise<RolesDocument[]> {
        return await this.rolesModel
            .find().populate({
                path: 'orgId',
                select: 'title',  // Fetch only the title field from the Organization
            }).exec();
    }

    async findAllRolesByOrg(orgId?: string, isDefault?: boolean, search?: string, page: number = 1, limit: number = 10): Promise<{ roles: RolesDocument[]; total: number }> {
        try {
            let conditions: any = { isDeleted: false };
            if (orgId) {
                conditions.orgId = orgId;
            }
            if (isDefault !== undefined) {
                conditions.isDefault = isDefault;
            }
            if (search) {
                conditions.roleAlias = { $regex: search, $options: 'i' }; // Case-insensitive search
            }
            console.log(conditions);
            const total = await this.rolesModel.countDocuments(conditions); // Get total count
            const roles = await this.rolesModel
                .find(conditions)
                .populate({
                    path: 'orgId',
                    select: 'title',  // Fetch only the title field from the Organization
                })
                .sort({ updatedAt: -1 })
                .skip((page - 1) * limit)
                .limit(limit)
                .exec();
            // return roles;
            return { roles, total };
        } catch (error) {
            throw new BadRequestException(`Error while fetching Roles by Org: ${error.message}`);
        }
    }

    async findById(roleId: Types.ObjectId): Promise<RolesDocument> {
        try {
            const role = await this.rolesModel.findOne({
                _id: roleId,       // Find by ID
                isDeleted: false,  // Ensure it's not deleted
            }).populate({
                path: 'orgId',
                select: 'title',  // Fetch only the title field from the Organization
            }).exec();
            if (!role) {
                throw new NotFoundException(`role with ID ${roleId} not found`)
            }
            return role;
        } catch (error) {
            this.logger.error(error);
            this.logger.error(`An error occurred in fetching role by Id ${roleId}. ${error?.message}`);
            throw error;
        }
    }

    async update(roleId: Types.ObjectId, updateRolesDto: UpdateRolesDto) {
        try {
            const role = await this.rolesModel.findById(roleId).populate({
                path: 'orgId',
                select: 'title',  // Fetch only the title field from the Organization
            }).exec();
            if (!role) {
                throw new NotFoundException(`role with ID ${roleId} not found`)
            }
            updateRolesDto.role = this.normalizeRole(updateRolesDto?.role || role.role);
            updateRolesDto.roleAlias = this.normalizeRole(updateRolesDto.role);
            if (updateRolesDto.isDefault) {
                const roleNameLowerCase = updateRolesDto.role?.toLowerCase();
                const existingDefaultRole = await this.rolesModel.findOne({
                    role: { $regex: new RegExp(`^${roleNameLowerCase}$`, 'i') },// Case-insensitive search
                    isDefault: true,
                    isDeleted: false
                }).exec();
                if (existingDefaultRole) {
                    throw new BadRequestException(`A default role ${updateRolesDto.role} already exists.`);
                }
            }
            else {
                const roleNameLowerCase = updateRolesDto.role?.toLowerCase();
                const existingRole = await this.rolesModel.findOne({
                    _id: { $ne: roleId }, // Exclude current role
                    role: { $regex: new RegExp(`^${roleNameLowerCase}$`, 'i') },// Case-insensitive search
                    isDefault: false,
                    orgId: updateRolesDto.orgId,
                    isDeleted: false
                }).exec();
                if (existingRole) {
                    throw new BadRequestException(`A role ${updateRolesDto.role} already exists.`);
                }
            }
            return await this.rolesModel.findByIdAndUpdate(roleId, updateRolesDto, { new: true })
                .exec();
        } catch (error) {
            this.logger.error(error);
            this.logger.error(`An error occurred in updating role by Id ${roleId}. ${error?.message}`);
            throw error;
        }
    }

    async delete(roleId: Types.ObjectId) {
        try {
            const role = await this.rolesModel.findById(roleId).populate({
                path: 'orgId',
                select: 'title',  // Fetch only the title field from the Organization
            }).exec();
            if (!role) {
                throw new NotFoundException(`role with ID ${roleId} not found`)
            }
            role.isDeleted = true;
            const roleNameToRemove = role.role.toLowerCase();

            await this.endpointsRolesModel.updateMany(
                { roles: roleNameToRemove },
                { $pull: { roles: roleNameToRemove } }
            );
            await role.save();
            return role;
        } catch (error) {
            this.logger.error(`An error occurred while deleting role by ID ${roleId}. ${error?.message}`);
            throw new BadRequestException('An error occurred while soft deleting the role');
        }
    }

    @OnEvent('roles.clone')
    async handleCloneDefaultRoles(event: any) {
        const { orgId } = event;
        this.logger.log(`Cloning default roles for orgId: ${orgId}`);

        try {
            // Fetch default roles
            const defaultRoles = await this.rolesModel.find({ isDefault: true }).lean();

            if (!defaultRoles.length) {
                this.logger.warn(`No default roles found to clone.`);
                return;
            }

            // Fetch existing roles for this orgId
            const existingRoles = await this.rolesModel.find({ orgId: orgId, isDeleted: false }).lean();
            const existingRoleNames = new Set(existingRoles.map(role => role.role)); // Store existing role names

            // Filter roles that are missing for this orgId
            const rolesToClone = defaultRoles.filter(role => !existingRoleNames.has(role.role));

            if (!rolesToClone.length) {
                this.logger.log(`All default roles already exist for orgId: ${orgId}. Skipping cloning.`);
                return; // No need to proceed
            }

            // Prepare roles to insert
            const clonedRoles = rolesToClone.map(role => ({
                ...role,
                _id: undefined, // Ensure MongoDB generates a new ID
                orgId, // Assign the organization ID
                isDefault: false,
                canDelete: false, // Set canDelete to true for cloned roles
            }));

            // Insert only missing roles
            await this.rolesModel.insertMany(clonedRoles);
            this.logger.log(`Successfully added ${clonedRoles.length} missing roles for orgId: ${orgId}`);
        } catch (error) {
            this.logger.error(`Error cloning default roles for orgId: ${orgId}`, error);
        }
    }
}
