import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';

import { HydratedDocument, Types } from 'mongoose';
import { Country } from 'src/country/schemas/country.schema';
import { State } from 'src/state/schemas/state.schema';
import { City } from 'src/state/schemas/city.schema';
export type CompanyAddressDocument = HydratedDocument<CompanyAddress>;

@Schema({
    _id: false,
    timestamps: true
})
export class CompanyAddress {

    @Prop({
        type: Types.ObjectId,
        required: false,
        ref: 'Country'
    })
    country?: Types.ObjectId;

    @Prop({
        type: Types.ObjectId,
        required: false,
        ref: 'State'
    })
    state?: Types.ObjectId;

    @Prop({
        type: Types.ObjectId,
        required: false,
        ref: 'City'
    })
    city?: Types.ObjectId;

    @Prop({
        type: String,
        required: false,
        trim: true
    })
    postalCode?: string;

    @Prop({
        type: String,
        required: false,
        trim: true
    })
    contactNumber?: string;

    @Prop({
        type: String,
        required: false,
        trim: true
    })
    email?: string;

    @Prop({
        type: String,
        required: false,
        trim: true
    })
    addressLine?: string;

    @Prop({
        type: String,
        required: false,
        trim: true
    })
    lutNumber?: string;

    @Prop({
        type: String,
        required: false,
        trim: true
    })
    lutDate?: string;

    @Prop({
        type: Boolean,
        required: false,
        default: false
    })
    sez?: boolean; // Special Economic Zone - yes/no

    @Prop({
        type: Boolean,
        required: false,
        default: false
    })
    registeredAddress?: boolean;
}

export const CompanyAddressSchema = SchemaFactory.createForClass(CompanyAddress);
