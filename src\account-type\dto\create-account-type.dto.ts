import { ApiHideProperty, ApiProperty } from "@nestjs/swagger";
import { Transform, TransformFnParams } from "class-transformer";
import { IsNotEmpty, IsOptional, IsString, } from 'class-validator';
import { sanitizeWithStyle } from "src/utils/sanitize-with-style";

export class CreateAccountTypeDto {
    @ApiProperty({
        type: String,
        required: true,
        description: 'The Name of the account type',
    })
    @IsNotEmpty()
    @IsString()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    name: string;

    @ApiProperty({
        required: false,
        default: false,
        description: 'The description of the account type'
    })
    @IsString()
    @IsOptional()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    description?: string;

    @ApiHideProperty()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    createdBy?: string;

    // @ApiProperty({
    //     required: false,
    // })
    // @IsString()
    // @IsOptional()
    // createdBy?: string;

}
