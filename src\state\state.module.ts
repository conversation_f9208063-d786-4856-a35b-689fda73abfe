import { Module } from '@nestjs/common';
import { StateService } from './state.service';
import { StateController } from './state.controller';
import { ConfigModule } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { MongooseModule } from '@nestjs/mongoose';
import { AuthModule } from 'src/auth/auth.module';
import { UserModule } from 'src/user/user.module';
import { State } from './entities/state.entity';
import { StateSchema } from './schemas/state.schema';
import { CountryModule } from 'src/country/country.module';
import { City, CitySchema } from './schemas/city.schema';
import { EndpointsRolesModule } from 'src/endpoints-roles/endpoints-roles.module';
@Module({
  imports: [
    ConfigModule,
    UserModule,
    JwtModule, EndpointsRolesModule,
    AuthModule,
    CountryModule,
    MongooseModule.forFeature([
      { name: State.name, schema: StateSchema },
      { name: City.name, schema: CitySchema }
    ])
  ],
  controllers: [StateController],
  providers: [StateService],
  exports: [MongooseModule]
})
export class StateModule { }
