// dto/emergency-contact.dto.ts

import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional } from 'class-validator';
import { sanitizeWithStyle } from "src/utils/sanitize-with-style";
import { Transform, TransformFnParams } from 'class-transformer';

export class EmergencyContactDto {
  @ApiProperty({
    type: String,
    required: false,
  })
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  @IsString()
  @IsOptional()
  addressLine1: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  @IsString()
  @IsOptional()
  relationship: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  @IsString()
  @IsOptional()
  contactNumber: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  @IsString()
  @IsOptional()
  alternateNumber: string;
}
