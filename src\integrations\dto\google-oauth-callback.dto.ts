// dto/google-oauth-callback.dto.ts
import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from 'class-validator';

export class GoogleOAuthCallbackDto {
    @ApiProperty({ description: 'OAuth code returned from Google', required: true })
    @IsString()
    @IsNotEmpty()
    code: string;

    @ApiProperty({ description: 'State string returned (used as Integration ID)', required: true })
    @IsString()
    @IsNotEmpty()
    state: string;
}
