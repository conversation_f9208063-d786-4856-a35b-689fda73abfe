import { Delete, forwardRef, Module } from '@nestjs/common';
import { DeleteUserService } from './delete-user.service';
import { DeleteUserController } from './delete-user.controller';
import { ConfigModule } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { MongooseModule } from '@nestjs/mongoose';
import { DeleteUser, DeleteUserSchema } from './schemas/delete-user.schema';
import { BasicUser, BasicUserSchema } from 'src/user/schemas/basic-user.schema';
import { UserModule } from 'src/user/user.module';
import { EndpointsRolesModule } from 'src/endpoints-roles/endpoints-roles.module';
@Module({
  controllers: [DeleteUserController],
  providers: [DeleteUserService],
  imports: [
      ConfigModule,
      JwtModule, EndpointsRolesModule,
       forwardRef(() => UserModule),
      MongooseModule.forFeature([
        { name: DeleteUser.name, schema: DeleteUserSchema },
       {name: BasicUser.name, schema: BasicUserSchema}
      ])
    ],
})
export class DeleteUserModule {}
