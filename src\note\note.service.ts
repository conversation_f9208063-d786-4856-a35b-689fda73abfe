import { Inject, Injectable, InternalServerErrorException, Logger, NotFoundException, forwardRef } from '@nestjs/common';
import { CreateNoteDto } from './dto/create-note.dto';
import { UpdateNoteDto } from './dto/update-note.dto';
import { ConfigService } from '@nestjs/config';
import { InjectModel } from '@nestjs/mongoose';
import { Note, NoteDocument } from './schemas/note.schema';
import { Model, Types } from 'mongoose';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';
import { validateObjectId } from 'src/utils/validation.utils';


@Injectable()
export class NoteService {
  private readonly logger = new Logger(NoteService.name);

  constructor(private configService: ConfigService,
    @InjectModel(Note.name) private noteModel: Model<Note>,
    private eventEmitter: EventEmitter2,
  ) { }


  async create(createNoteDto: CreateNoteDto) {
    try {
      const createdNote = new this.noteModel(createNoteDto);
      await createdNote.save();
      const note = await this.findOne(createdNote._id);
      this.logger.log('created a note');
      this.emitEvent('note.created', note);
      return createdNote;
    } catch (error) {
      this.logger.error('Failed to create note', error);
      throw new InternalServerErrorException('Unknown error when creating note.');
    }
  }


  async findAll(page: number, limit: number): Promise<NoteDocument[]> {
    const populateOptions = this.getPopulateOptions();
    return this.noteModel.find({ isDeleted: false })
      .populate(populateOptions)
      .skip((page - 1) * limit)
      .limit(limit)
      .exec();
  }

  async findOne(noteId: Types.ObjectId): Promise<NoteDocument> {
    const populateOptions = this.getPopulateOptions();
    try {
      const note = await this.noteModel.findById(noteId)
        .populate(populateOptions)
        .exec();
      if (!note) {
        throw new NotFoundException(`Note not found with ID ${noteId}`);
      }
      return note;
    } catch (error) {
      this.logger.error(error);
      this.logger.error(`An error occurred in fetching note by Id ${noteId}. ${error?.message}`);
      throw error;
    }
  }

  async findById(noteId: Types.ObjectId): Promise<NoteDocument> {
    const populateOptions = this.getPopulateOptions();
    try {
      const note = await this.noteModel.findById(noteId)
        .populate(populateOptions)
        .exec();
      if (!note || note.isDeleted) {
        throw new NotFoundException(`Note not found with ID ${noteId}`);
      }
      return note;
    } catch (error) {
      this.logger.error(error);
      this.logger.error(`An error occurred in feching note by Id ${noteId}. ${error?.message}`);
      throw error;

    }
  }

  async update(noteId: Types.ObjectId, updateNoteDto: UpdateNoteDto, user: Object) {
    const { title, contact, org } = updateNoteDto;
    const populateOptions = this.getPopulateOptions();
    try {
      const note = await this.findById(noteId);
      if (!note || note.isDeleted) {
        throw new NotFoundException(`Note not found with ID ${noteId}`);
      }
      await this.noteModel.findByIdAndUpdate(noteId, updateNoteDto, { new: true });
      const updatedNote = await this.noteModel.findById(noteId)
        .populate(populateOptions)
        .exec()

      if (title) {
        this.emitEvent('note.title.updated', { updatedNote, user });
      }

      if (org) {
        this.emitEvent('note.org.updated', { updatedNote, user });
      }

      if (contact) {
        this.emitEvent('note.contact.updated', { updatedNote, user });
      }
      return updatedNote

    } catch (error) {
      this.logger.error(error);
      this.logger.error(`An error occurred while updating note by ID ${noteId}. ${error?.message}`);
      throw error;
    }
  }



  // async findByClient(clientId: string, page: number, limit: number): Promise<NoteDocument[]> {
  //   try {
  //     const notes = await this.noteModel.find({ client: clientId, isDeleted: false })
  //       .populate({ path: 'createdBy', select: '_id roles firstName' })
  //       .populate({ path: 'account', select: '_id name headCount accountStatus' })
  //       .populate({ path: 'contact', select: '_id name contactEmail' })
  //       .skip((page - 1) * limit)
  //       .limit(limit)
  //       .exec();
  //     return notes;
  //   } catch (error) {
  //     this.logger.error(`An error occurred while fetching notes by contact ID ${clientId}. ${error?.message}`);
  //     throw new InternalServerErrorException('An error occurred while fetching notes for the contact.');
  //   }
  // }


  async findByContactAndOrg({ contactId, orgId, page, limit }: {
    contactId?: string;
    orgId?: string;
    // vendorId?: string;
    page: number;
    limit: number;
  }): Promise<NoteDocument[]> {
    const populateOptions = this.getPopulateOptions();
    try {
      const query: any = {};

      if (contactId) {
        query.contact = contactId;
      }

      if (orgId) {
        query.org = orgId;
      }

      // if (vendorId) {
      //   query.vendor = vendorId;
      // }
      const notes = await this.noteModel.find(query)
        .populate(populateOptions)
        .skip((page - 1) * limit)
        .limit(limit)
        .exec();
      return notes;
    } catch (error) {
      throw new InternalServerErrorException('An error occurred while fetching notes by contact and org.');
    }
  }

  async findPublicNotes(page: number, limit: number): Promise<NoteDocument[]> {
    const populateOptions = this.getPopulateOptions();
    try {
      const notes = await this.noteModel.find({ isPrivate: false, isDeleted: false })
        .populate(populateOptions)
        .skip((page - 1) * limit)
        .limit(limit)
        .exec();
      return notes;
    } catch (error) {
      this.logger.error('An error occurred while fetching private notes.', error);
      throw new InternalServerErrorException('An error occurred while fetching private notes.');
    }
  }

  async findNotesByUser(userId: Types.ObjectId, page: number, limit: number) {
    try {
      return await this.noteModel.find({ createdBy: userId, isDeleted: false })
        .populate({ path: 'createdBy', select: '_id roles firstName' })
        .populate({ path: 'org', select: '_id orgType' })
        .populate({ path: 'contact', select: '_id firstName lastName ' })
        .skip((page - 1) * limit)
        .limit(limit)
        .exec();
    } catch (error) {
      this.logger.error(`An error occurred while fetching notes by user ID ${userId}. ${error.message}`);
      throw new InternalServerErrorException('An error occurred while fetching notes for the user.');
    }
  }

  async updatePrivacy(noteId: Types.ObjectId) {
    try {
      const note = await this.findById(noteId);
      if (!note || note.isDeleted) {
        throw new NotFoundException(`Note not found with ID ${noteId}`);
      }
      // Update only the isPrivate field
      note.isPrivate = !note.isPrivate;
      // Save the updated note
      const updatedNote = await note.save();
      return updatedNote;
    } catch (error) {
      this.logger.error(error);
      this.logger.error(`An error occurred while updating privacy for note by ID ${noteId}. ${error?.message}`);
      throw error;
    }
  }



  async remove(noteId: Types.ObjectId, user: Object): Promise<NoteDocument> {
    try {
      const note = await this.findById(noteId);
      if (!note) {
        throw new NotFoundException(`Note not found with ID ${noteId}`);
      }
      note.isDeleted = true;
      await note.save();
      this.emitEvent('note.remove', { note, user });
      return note;
    } catch (error) {
      this.logger.error(error);
      this.logger.error(`An error occurred while deleting by ID ${noteId}. ${error?.message}`);
      throw new InternalServerErrorException('Invalid note ID');
    }
  }


  async hardDelete(noteId: Types.ObjectId) {
    try {
      const note = await this.findById(noteId);
      if (!note) {
        throw new NotFoundException(`Note not found with ID ${noteId}`);
      }
      await this.noteModel.deleteOne({ _id: noteId });
      this.emitEvent('note.remove', note);
      return { message: 'Note deleted' };
    } catch (error) {
      this.logger.error(`An error occurred while hard deleting by ID ${noteId}. ${error?.message}`);
      throw new InternalServerErrorException('An error occurred while hard deleting the note.');
    }
  }

  async deleteAll() {
    await this.noteModel.deleteMany();
    return { message: "All notes deleted" };
  }

  getPopulateOptions(): any[] {
    const populateOptions = [
      { path: 'createdBy', select: '_id roles firstName', model: 'BasicUser' },
    ];

    if (this.noteModel.schema.paths['org']) {
      populateOptions.push({ path: 'org', select: '_id title orgType', model: 'Org' });
    }

    if (this.noteModel.schema.paths['contact']) {
      populateOptions.push({ path: 'contact', select: '_id firstName lastName', model: 'Contact' });
    }



    return populateOptions;
  }


  emitEvent(eventName: string, payload: any) {
    // payload['event']= eventName;
    this.eventEmitter.emit(eventName, payload);
  }
}
