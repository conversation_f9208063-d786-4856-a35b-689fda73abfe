import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

@Schema()
export class DashboardMetadata extends Document {
    @Prop({ required: true })
    role: string;

    @Prop({ required: true, type: Number }) // ✅ explicitly define type
    dashboardId: number;

    @Prop({ type: [String] }) // ✅ Array of strings
    params: string[];

    @Prop({ default: Date.now })
    createdAt: Date;

    @Prop()
    title?: string;
}

export const DashboardMetadataSchema = SchemaFactory.createForClass(DashboardMetadata);
