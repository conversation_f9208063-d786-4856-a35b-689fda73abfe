import { ApiHideProperty, ApiProperty } from "@nestjs/swagger";
import { Transform, TransformFnParams } from "class-transformer";
import { IsArray, IsBoolean, IsEnum, IsMongoId, IsNotEmpty, IsOptional, IsString } from "class-validator";
import { sanitizeWithStyle } from "src/utils/sanitize-with-style";
export class CreateNoteDto {

    @ApiProperty({ type: String, required: false, description: 'Reference to the Organization' })
    @IsMongoId()
    @IsOptional()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    org?: string;

    @ApiProperty({ type: String, required: false, description: 'Reference to the Contact' })
    @IsMongoId()
    @IsOptional()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    contact?: string;


    @ApiProperty({
        type: String,
        required: false,
        description: 'The title of the note.',
    })
    @IsString()
    @IsOptional()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    title?: string;

    @ApiProperty({
        type: String,
        required: false,
        description: 'The summary of the note.'
    })
    @IsString()
    @IsOptional()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    summary?: string;

    @ApiProperty({
        type: String,
        required: false,
        description: 'The content of the note.'
    })
    @IsString()
    @IsOptional()
    // @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    content?: string;

    @ApiProperty({
        type: Boolean,
        required: false,
        default: false,
        description: 'Indicates if the note is private.'
    })
    @IsBoolean()
    @IsOptional()
    isPrivate?: boolean;

    @ApiHideProperty()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    createdBy?: string;

    @ApiProperty({ description: 'Array of attachment URLs', type: [String], required: false, example: ['http://example.com/file1.png', 'http://example.com/file2.pdf'] })
    @IsArray()
    @IsString({ each: true })
    @IsOptional()
    @Transform((params: TransformFnParams) => params.value.map((value: string) => sanitizeWithStyle(value)))
    attachments?: string[];

    // @ApiProperty({ type: String, required: false, description: 'Reference to the Vendor' })
    // @IsMongoId()
    // @IsOptional()
    // @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    // vendor?: string;
}

