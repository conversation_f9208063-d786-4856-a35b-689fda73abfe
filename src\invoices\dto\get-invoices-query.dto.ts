import { IsOptional, IsString, <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from 'class-validator';
import { Type } from 'class-transformer';

export class GetInvoicesQueryDto {
  @IsOptional()
  @IsString()
  billedToOrg?: string;

  @IsOptional()
  @IsString()
  employmentType?: string;

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  page?: number = 1;

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  limit?: number = 10;
}
