import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Types } from 'mongoose';
import { Country } from 'src/country/schemas/country.schema';
import { AddressType } from 'src/shared/constants';
import { State } from 'src/state/schemas/state.schema';
import {Region} from 'src/region/schemas/region.schema'

export type AddressInformationDocument = HydratedDocument<AddressInformation>;


@Schema({
  _id: false,
   timestamps: true
})
export class AddressInformation {
  
  @Prop({
    type: String,
    required: false,
    trim: true
  })
  apartment?: string;

  @Prop({
    type: String,
    required: false,
    trim: true
  })
  street?: string;

  @Prop({
    type: String,
    required: false,
    trim: true
  })
  city?: string;

  @Prop({
    type: String,
    required: false,
    trim: true
  })
  postalCode?: string;

  // @Prop({
  //   type: Types.ObjectId,
  //   required: false,
  //   ref: 'State'
  // })
  // state?: State;
  
  // @Prop({
  //   type: Types.ObjectId,
  //   required: false,
  //   ref: 'Country'
  // })
  // country?: Country;

  @Prop({
    type: Types.ObjectId,
    required: false,
    ref: 'Region'
  })
  region?: Region;

  @Prop({
    type: String,
    required: false,
    trim: true,
    default: AddressType.HOME,
    enum: Object.values(AddressType), 
  })
  addressType?: string;

}

export const AddressInformationSchema = SchemaFactory.createForClass(AddressInformation);
