import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Logger } from '@nestjs/common';
import { StageService } from './stage.service';
import { CreateStageDto } from './dto/create-stage.dto';
import { UpdateStageDto } from './dto/update-stage.dto';
import { ApiBearerAuth, ApiOperation, ApiParam, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { AuthJwtGuard } from 'src/auth/guards/auth-jwt/auth-jwt.guard';
import { RolesGuard } from 'src/auth/guards/roles/roles.guard';
import { Roles } from 'src/auth/decorators/roles.decorator';
import { Role } from 'src/auth/enums/role.enum';
import { validateObjectId } from 'src/utils/validation.utils';
@Controller('')
@ApiTags('Stages')
export class StageController {
  constructor(private readonly stageService: StageService) { }

  private readonly logger = new Logger(StageController.name);

  @Post()
  @ApiResponse({ status: 201, description: `Stage is created.` })
  @ApiResponse({ status: 401, description: `Unauthorized.` })
  @ApiResponse({ status: 400, description: `Bad Request / Data.` })
  @ApiResponse({ status: 403, description: `Forbidden, User with role "Admin","BUHead" , "ResourceManager", "DeliveryManager", "TeamLead" and "AccountManager" can only use this end point.` })
  @ApiOperation({ summary: `Create a new stage.`, description: `This endpoint allows you to create a new stage. This is accessible only for "BUHead" , "ResourceManager", "DeliveryManager", "TeamLead" and "AccountManager".` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager)
  @Roles()
  create(@Body() createStageDto: CreateStageDto) {
    return this.stageService.create(createStageDto);
  }

  @Get('all')
  @ApiResponse({ status: 200, description: `Stages retrieved.` })
  @ApiResponse({ status: 401, description: `Unauthorized.` })
  // @ApiResponse({ status: 403, description: `Forbidden, user with role "Admin","BUHead" , "ResourceManager", "DeliveryManager", "TeamLead","Recruiter", and "AccountManager"` })
  @ApiOperation({ summary: `Retrieve all stages`, description: `This endpoint returns a list of all stages. This is accessible only for everyone.` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager, Role.Recruiter)
  @Roles()
  findAll() {
    return this.stageService.findAll();
  }

  @Get(':stageId')
  @ApiResponse({ status: 200, description: `Stage is retrieved.` })
  @ApiResponse({ status: 404, description: `Stage is not found.` })
  @ApiResponse({ status: 401, description: `Unauthorized. ` })
  // @ApiResponse({ status: 403, description: `Forbidden, user with role "Admin","BUHead" , "ResourceManager", "DeliveryManager", "TeamLead","Recruiter", and "AccountManager"` })
  @ApiOperation({ summary: `Retrieve an stage by Id`, description: `This endpoint returns an stage by its Id. This is accessible only for everyone.` })
  @ApiParam({ name: 'stageId', description: `Id of the stage.` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager, Role.Recruiter)
  @Roles()
  findOne(@Param('stageId') stageId: string) {
    const objId = validateObjectId(stageId);
    return this.stageService.findOne(objId);
  }

  @Patch(':stageId')
  @ApiParam({ name: 'stageId', description: `Id of the stage.` })
  @ApiResponse({ status: 200, description: `Stage updated.` })
  @ApiResponse({ status: 401, description: `Unauthorized.` })
  @ApiResponse({ status: 403, description: `Forbidden, user with role "Admin", "BUHead" , "ResourceManager", "DeliveryManager", "TeamLead" and "AccountManager" use this end point.` })
  @ApiOperation({ summary: `Update an stage by id`, description: `This endpoint updates an stage by Id. This is accessible only for "BUHead" , "ResourceManager", "DeliveryManager", "TeamLead" and "AccountManager".` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager)
  @Roles()
  update(@Param('stageId') stageId: string, @Body() updateStageDto: UpdateStageDto) {
    const objId = validateObjectId(stageId);
    return this.stageService.update(objId, updateStageDto);
  }

  @Delete(':stageId')
  @ApiResponse({ status: 200, description: `Stage is deleted.` })
  @ApiResponse({ status: 401, description: `Unauthorized.` })
  @ApiResponse({ status: 403, description: `Forbidden, user with role "Admin", "BUHead" , "ResourceManager", "DeliveryManager", "TeamLead" and "AccountManager"can only use this end point.` })
  @ApiParam({ name: 'stageId', description: `id of the stage.` })
  @ApiOperation({ summary: `Delete an stage by Id`, description: `This endpoint deletes an stage by Id. This is accessible only for "BUHead" , "ResourceManager", "DeliveryManager", "TeamLead" and "AccountManager".` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager)
  @Roles()
  remove(@Param('stageId') stageId: string) {
    const objId = validateObjectId(stageId);
    return this.stageService.remove(objId);
  }
}
