import { ApiProperty } from "@nestjs/swagger";
import { Transform, TransformFnParams } from "class-transformer";
import { IsArray, IsMongoId, IsNotEmpty } from "class-validator";
import { sanitizeWithStyle } from "src/utils/sanitize-with-style";

export class SendTrackerEmailContentDto {
  @ApiProperty({
    type: [String],
    required: true,
    description: 'Array of job application IDs to generate tracker email content',
  })
  @IsMongoId({ each: true })
  @IsArray()
  @IsNotEmpty({ each: true })
  jobApplicationIds: string[];
}
