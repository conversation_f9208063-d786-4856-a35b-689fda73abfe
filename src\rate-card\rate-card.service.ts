import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
  ConflictException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { EventEmitter2 } from '@nestjs/event-emitter';

import { RateCard } from './schemas/rate-card.schema';
import { CreateRateCardDto } from './dto/create-rate-card.dto';
import { UpdateRateCardDto } from './dto/update-rate-card.dto';
import { FilterRateCardDto } from './dto/filter-rate-card.dto';
import { validateObjectId } from 'src/utils/validation.utils';
import { RateCardCategory } from 'src/rate-card-category/schemas/rate-card-category.schema';

@Injectable()
export class RateCardService {
  private readonly logger = new Logger(RateCardService.name);

  constructor(
    @InjectModel(RateCard.name) private rateCardModel: Model<RateCard>,
    @InjectModel(RateCardCategory.name) private readonly rateCardCategoryModel: Model<RateCardCategory>,
    private readonly eventEmitter: EventEmitter2,
  ) { }

  async create(dto: CreateRateCardDto, user: any) {
    try {
      // const existing = await this.rateCardModel.findOne({
      //   name: dto.name,
      //   org: user.org._id,
      //   createdBy: user._id,
      //   isDeleted: false,
      // });

      // if (existing) {
      //   throw new ConflictException('Rate card with this name already exists.');
      // }

      const rateCardCategory = await this.rateCardCategoryModel.findById(dto.category);
      if (!rateCardCategory) {
        throw new NotFoundException('Rate card category not found.');
      }

      const newRateCard = new this.rateCardModel(dto);

      const rateCard = await newRateCard.save();

      await this.rateCardCategoryModel.findByIdAndUpdate(rateCardCategory._id, { $push: { children: rateCard._id.toString() } }, { new: true });
      this.emitEvent('rate-card.created', { rateCard, user, org: rateCardCategory.client || null });

      return rateCard;
    } catch (error) {
      throw new BadRequestException(
        `Error creating rate card: ${error.message}`,
      );
    }
  }

  async findAll(query: FilterRateCardDto) {
    try {
      const { name, org, client, category, page = 1, limit = 1000 } = query;

      const conditions: any = { isDeleted: false };

      if (name) {
        conditions.name = new RegExp(name, 'i');
      }

      if (org) {
        conditions.org = org;
      }

      if (category) {
        conditions.category = category
      }
      if (client) {
        const rateCardCategories = await this.rateCardCategoryModel.find({ client: client, isDeleted: false });
        const categoryIds = rateCardCategories.map(category => category._id.toString());
        this.logger.log(JSON.stringify(categoryIds));
        conditions.category = { $in: categoryIds };
      }
      return await this.rateCardModel
        .find(conditions)
        .populate({ path: 'category', select: '_id label description' })
        .sort({ createdAt: -1 })
        .skip((page - 1) * limit)
        .limit(limit)
        .exec();
    } catch (error) {
      this.logger.error('Error fetching rate cards', error.stack);
      throw error;
    }
  }

  async countAll(query: FilterRateCardDto) {
    try {
      const { name, org, category } = query;
      const conditions: any = { isDeleted: false };

      if (name) {
        conditions.name = new RegExp(name, 'i');
      }

      if (org) {
        conditions.org = org;
      }

      if (category) {
        conditions.category = category
      }

      const count = await this.rateCardModel.countDocuments(conditions);
      return { count };
    } catch (error) {
      throw new InternalServerErrorException(
        `Error counting rate cards: ${error.message}`,
      );
    }
  }

  async findOne(rateCardId: Types.ObjectId) {
    try {
      const card = await this.rateCardModel
        .findById(rateCardId)
        .populate({ path: 'category', select: '_id label description client' })
        .exec();

      if (!card || card.isDeleted) {
        throw new NotFoundException('Rate card not found.');
      }

      return card;
    } catch (error) {
      throw error;
    }
  }

  async update(rateCardId: Types.ObjectId, dto: UpdateRateCardDto, user: any) {
    try {
      const previous = await this.rateCardModel.findById(rateCardId);
      if (!previous) {
        throw new NotFoundException('Rate card not found.');
      }

      const updated = await this.rateCardModel.findByIdAndUpdate(
        rateCardId,
        dto,
        { new: true },
      ).populate({ path: 'category', select: '_id label description client' });

      if (!updated) {
        throw new NotFoundException('Rate card not found.');
      }

      this.eventEmitter.emit('rate-card.updated', {
        updated,
        previous,
        user
      });


      return updated;
    } catch (error) {
      throw error;
    }
  }

  async softDelete(rateCardId: Types.ObjectId, user: any) {
    try {

      const previous = await this.rateCardModel.findById(rateCardId);
      if (!previous) {
        throw new NotFoundException('Rate card not found.');
      }

      const updated = await this.rateCardModel.findByIdAndUpdate(
        rateCardId,
        { isDeleted: true, deletedAt: new Date(), deletedBy: user._id },
        { new: true },
      ).populate({ path: 'category', select: '_id label description client' });;

      if (!updated) {
        throw new NotFoundException('Rate card not found.');
      }

      this.eventEmitter.emit('rate-card.updated', {
        updated,
        previous,
        user
      });

      return updated;
    } catch (error) {
      throw error;
    }
  }

  async hardDelete(rateCardId: Types.ObjectId) {
    try {
      const found = await this.findOne(rateCardId);

      const rateCardCategory = await this.rateCardCategoryModel.findById(found.category);
      if (rateCardCategory) {
        await this.rateCardCategoryModel.findByIdAndUpdate(rateCardCategory._id, { $pull: { children: rateCardId.toString() } }, { new: true });
      }
      if (!found) {
        throw new NotFoundException('Rate card not found.');
      }

      return await this.rateCardModel.findByIdAndDelete(rateCardId);
    } catch (error) {
      throw error;
    }
  }

  async restore(rateCardId: Types.ObjectId) {
    try {
      const restored = await this.rateCardModel.findByIdAndUpdate(
        rateCardId,
        { isDeleted: false, deletedAt: null, deletedBy: null },
        { new: true },
      );

      if (!restored) {
        throw new NotFoundException('Rate card not found.');
      }

      return restored;
    } catch (error) {
      throw new InternalServerErrorException(
        `Error restoring rate card: ${error.message}`,
      );
    }
  }

  emitEvent(eventName: string, payload: any) {
    // payload['event']= eventName;
    this.logger.debug(payload)
    this.eventEmitter.emit(eventName, payload);
  }
}
