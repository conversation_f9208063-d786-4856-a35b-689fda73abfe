import { Transform } from 'class-transformer';
import { IsOptional, IsString, IsInt, Min, IsBoolean } from 'class-validator';

export class QueryContactDto {

  @IsOptional()
  @IsString()
  @Transform(({ value }) => (value === undefined || value === '0' ? '' : value))
  name?: string;

  @IsOptional()
  @IsString()
  @Transform(({ value }) => (value === undefined || value === '0' ? '' : value))
  accountId?: string;

  @IsOptional()
  @IsString()
  @Transform(({ value }) => (value === undefined || value === '0' ? '' : value))
  industryId?: string;

  // @IsOptional()
  // @IsString()
  // @Transform(({ value }) => (value === undefined || value === '0' ? '' : value))
  // location?: string;

  @IsOptional()
  @IsString()
  @Transform(({ value }) => (value === undefined || value === '0' ? '' : value))
  designation?: string;

  @IsOptional()
  @IsString()
  @Transform(({ value }) => (value === undefined || value === '0' ? '' : value))
  referredBy?: string;

  @IsOptional()
  @Transform(({ value }) => {
    return value > 0 ? value : 1;
  })
  page: number = 1;


  @IsOptional()
  @Transform(({ value }) => {
    return value > 0 ? value : 10;
  })
  limit: number = 10;

  @IsOptional()
  @Transform(({ value }) => {
    return value === undefined || value === '0' ? '' : value;
  })
  isInternal?: boolean;

  @IsOptional()
  @Transform(({ value }) => {
    return value === undefined || value === '0' ? '' : value;
  })
  isDeleted?: boolean;

  @IsOptional()
  @IsString()
  @Transform(({ value }) => (value === undefined || value === '0' ? '' : value))
  assignTo?: string;

  @IsOptional()
  @IsString()
  @Transform(({ value }) => (value === undefined || value === '0' ? '' : value))
  vendorId?: string;

  @IsOptional()
  @IsString()
  @Transform(({ value }) => (value === undefined || value === '0' ? '' : value))
  contactId?: string;

}
