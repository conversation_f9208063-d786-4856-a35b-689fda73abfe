import { Prop, Schema, SchemaFactory, raw } from "@nestjs/mongoose";
import { HydratedDocument, Types } from "mongoose";
import { Org } from "src/org/schemas/org.schema";
import { InviteStatus } from "src/shared/constants";
import { BasicUser } from "src/user/schemas/basic-user.schema";

export type VendorInviteDocument = HydratedDocument<VendorInvite>;
@Schema({
    timestamps: true
})

export class VendorInvite {


    @Prop({
        type: String,
        required: true,
        trim: true,
    })
    vendorName: string;

    @Prop({
        type: String,
        required: true,
        trim: true,
    })
    userName: string;

    @Prop({
        type: String,
        required: true,
        // unique: true,
        // index:true
        lowercase: true,
        trim: true
    })
    email: string;


    @Prop({
        required: true,
        type: Types.ObjectId, ref: 'BasicUser'
    })
    createdBy: BasicUser;

     @Prop({
       required: false,
       type: Types.ObjectId, ref: 'Org'
     })
     companyId?: Org;
    
     @Prop({
        type: String,
        required: false,
        enum: Object.values(InviteStatus),
        default: InviteStatus.SEND
    })
    status?: InviteStatus;

}



export const VendorInviteSchema = SchemaFactory.createForClass(VendorInvite);