import { ApiProperty } from "@nestjs/swagger";
import { IsArray, IsMongoId, IsNotEmpty, IsOptional, IsString, ValidateNested } from "class-validator";
import { each } from "lodash";
import { Transform, TransformFnParams, Type } from 'class-transformer';
import sanitizeWithStyle from 'sanitize-html'

export class CommentDto {
  @ApiProperty({
    type: String,
    required: false,
    description: 'Content of the comment',
  })
  @IsOptional()
  @IsString()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  contents?: string;

  @ApiProperty({
    type: [String],
    required: false,
    description: 'Array of file metadata IDs associated',
  })
  @IsOptional()
  @IsArray()
  attachments?: string[];

  @ApiProperty({
    type: String,
    required: false,
    description: 'ID of the organization associated with the comment',
  })
  @IsOptional()
  @IsMongoId()
  org?: string;

  @ApiProperty({
    type: [String], // Updated to an array of strings (for multiple org IDs)
    required: false,
    description: 'Array of organization IDs associated with the comment',
  })
  @IsOptional()
  @IsArray() // Ensures it's an array
  @IsMongoId({ each: true }) // Ensures each item in the array is a valid MongoId
  orgIds?: string[]; // Array of organization IDs

  @ApiProperty({
    type: String,
    required: false,
    description: 'ID of the user who made the comment',
  })
  @IsOptional()
  @IsMongoId()
  user?: string;

  // @ApiProperty({
  //   type: String,
  //   required: false,
  //   description: 'ID of the country associated with the comment',
  // })
  // @IsOptional()
  // @IsMongoId()
  // country?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'ID of the region associated with the comment',
  })
  @IsOptional()
  @IsMongoId()
  region?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'ID of the task associated with the comment',
  })
  @IsOptional()
  @IsMongoId()
  taskId?: string;

  // @ApiProperty({
  //   type: String,
  //   required: false,
  //   description: 'ID of the contact associated with the comment',
  // })
  // @IsOptional()
  // @IsMongoId()
  // contact?: string;
}