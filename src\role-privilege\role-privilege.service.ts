import { BadRequestException, Injectable, InternalServerErrorException, NotFoundException } from '@nestjs/common';
import { CreateRolePrivilegeDto } from './dto/create-role-privilege.dto';
import { UpdateRolePrivilegeDto } from './dto/update-role-privilege.dto';
import { Model, Types } from 'mongoose';
import { InjectModel } from '@nestjs/mongoose';
import { RolePrivilege } from './entities/role-privilege.entity';
import { RolePrivilegeDocument } from './schemas/role-privilege.schema';

@Injectable()
export class RolePrivilegeService {
  constructor(
    @InjectModel(RolePrivilege.name) private readonly rolePrivilegeModel: Model<RolePrivilegeDocument>,
  ) { }

  async create(createRolePrivilegeDto: CreateRolePrivilegeDto): Promise<RolePrivilege> {
    try {
      const existingRolePrivilege = await this.rolePrivilegeModel.findOne({ roleId: createRolePrivilegeDto.roleId }).exec();
      if (existingRolePrivilege) {
        throw new BadRequestException('Privileges already assigned to this role.');
      }
      const rolePrivilege = new this.rolePrivilegeModel(createRolePrivilegeDto);
      return await rolePrivilege.save();
    } catch (error) {
      throw new InternalServerErrorException(`Error assigning privileges. ${error?.message}`);
    }
  }

  async findAll(page: number = 1, limit: number = 10): Promise<RolePrivilege[]> {
    try {
      const skip = (page - 1) * limit;
      return await this.rolePrivilegeModel.find()
        .populate({ path: 'roleId', select: '_id role description orgId', model: 'Roles' })
        .populate({ path: 'privileges', select: '_id name description', model: 'Privilege' })
        .skip(skip).limit(limit).exec();
    } catch (error) {
      throw new InternalServerErrorException(`Error retrieving role privileges. ${error?.message}`);
    }
  }

  async findOne(rolePrivilegeId: Types.ObjectId): Promise<RolePrivilege> {
    try {
      const rolePrivilege = await this.rolePrivilegeModel.findById(rolePrivilegeId)
        .populate({ path: 'roleId', select: '_id role description orgId', model: 'Roles' })
        .populate({ path: 'privileges', select: '_id name description', model: 'Privilege' })
        .exec();
      if (!rolePrivilege) {
        throw new NotFoundException('Role privilege not found');
      }
      return rolePrivilege;
    } catch (error) {
      throw error;
    }
  }

  async update(rolePrivilegeId: Types.ObjectId, updateRolePrivilegeDto: UpdateRolePrivilegeDto): Promise<RolePrivilege> {
    try {
      const updatedRolePrivilege = await this.rolePrivilegeModel.findByIdAndUpdate(rolePrivilegeId, updateRolePrivilegeDto, { new: true })
        .populate({ path: 'roleId', select: '_id role description orgId', model: 'Roles' })
        .populate({ path: 'privileges', select: '_id name description', model: 'Privilege' })
        .exec();
      if (!updatedRolePrivilege) {
        throw new NotFoundException('Role privilege not found');
      }
      return updatedRolePrivilege;
    } catch (error) {
      throw error;
    }
  }

  async remove(rolePrivilegeId: Types.ObjectId) {
    try {
      const deletedRolePrivilege = await this.rolePrivilegeModel.findByIdAndDelete(rolePrivilegeId).exec();
      if (!deletedRolePrivilege) {
        throw new NotFoundException('Role privilege not found');
      }
      return 'Role privilege deleted successfully';
    } catch (error) {
      throw error;
    }
  }

  async findPrivilegeByRole(roleId: string): Promise<RolePrivilege> {
    try {
      const rolePrivilege = await this.rolePrivilegeModel.findOne({ roleId: roleId })
        .populate({ path: 'roleId', select: '_id role description orgId', model: 'Roles' })
        .populate({ path: 'privileges', select: '_id name description', model: 'Privilege' })
        .exec();
      if (!rolePrivilege) {
        throw new NotFoundException('Role privilege not found for this role');
      }
      return rolePrivilege;
    } catch (error) {
      throw error;
    }
  }
  async findPrivilegeByRoleIds(roleIds: string[]): Promise<string[]> {
    try {
      // Fetch privileges for all roleIds
      const rolePrivileges = await this.rolePrivilegeModel.find({ roleId: { $in: roleIds } })
        .populate({ path: 'roleId', select: '_id role description orgId', model: 'Roles' })
        .populate({ path: 'privileges', select: '_id name description', model: 'Privilege' })
        .exec();

      if (!rolePrivileges || rolePrivileges.length === 0) {
        throw new NotFoundException('Role privileges not found for the provided roles');
      }

      // Extract privilege names and ensure uniqueness
      const allPrivileges = new Set(
        rolePrivileges.flatMap(role => role.privileges.map(priv => priv.name))
      );

      return [...allPrivileges]; // Convert Set back to an array
    } catch (error) {
      throw error;
    }
  }

}
