import { ApiProperty, ApiHideProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsString, IsNotEmpty, IsOptional, IsEnum, IsMongoId, IsBoolean, IsArray, ValidateIf, ValidateNested, IsISO8601, <PERSON><PERSON><PERSON>ber } from 'class-validator';
import { Types } from 'mongoose';
import { FieldType } from 'src/shared/constants';

export class OptionDto {
    @ApiProperty({ description: 'Key for the option', example: 'S' })
    @IsString()
    label: string;

    @ApiProperty({ description: 'Value for the option', example: 'Option 1' })
    @IsString()
    value: string;
}

/**
 * Separate DTO for Date Field Properties
 */
export class DateFieldProperties {
    @ApiProperty({ description: 'Minimum date allowed (ISO format)', required: false, example: '2023-01-01' })
    @IsISO8601()
    @IsOptional()
    minDate?: string;

    @ApiProperty({ description: 'Maximum date allowed (ISO format)', required: false, example: '2025-12-31' })
    @IsISO8601()
    @IsOptional()
    maxDate?: string;

    @ApiProperty({ description: 'Date format (e.g., YYYY-MM-DD)', required: false, example: 'YYYY-MM-DD' })
    @IsString()
    @IsOptional()
    format?: string;
}

export class DynamicFieldDto {
    @ApiProperty({ description: 'Label or display name of the field' })
    @IsString()
    @IsNotEmpty()
    title: string;

    @ApiProperty({ description: 'Type of the field', enum: FieldType })
    @IsEnum(FieldType)
    @IsNotEmpty()
    type: FieldType;

    @ApiProperty({ description: 'Placeholder text for the field', required: false })
    @IsString()
    @IsOptional()
    placeholder?: string;

    // @ApiProperty({ description: 'List of options for dropdown-type fields', required: false })
    // @ValidateIf(o => o.type === FieldType.DROPDOWN || o.type === FieldType.MULTISELECT)
    // @IsArray()
    // @IsOptional()
    // options?: Record<string, string>[];

    @ApiProperty({
        description: 'List of options for dropdown-type fields',
        required: false,
        type: [OptionDto], // Defines the type as an array of key-value pairs
        example: [
            { "label": "S", "value": "Option 1" },
            { "label": "A", "value": "Option 2" },
            { "label": "B", "value": "Option 3" }
        ]
    })
    @ValidateIf(o => o.type === FieldType.DROPDOWN || o.type === FieldType.MULTISELECT)
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => OptionDto) // Ensures correct transformation
    @IsOptional()
    options?: OptionDto[];


    // @ApiProperty({
    //     description: 'List of options for dropdown-type fields',
    //     required: false,
    //     type: [String], // Defines the type as an array of key-value pairs
    //     // example: [
    //     //     { "label": "S", "value": "Option 1" },
    //     //     { "label": "A", "value": "Option 2" },
    //     //     { "label": "B", "value": "Option 3" }
    //     // ]
    // })
    // @ValidateIf(o => o.type === FieldType.DROPDOWN || o.type === FieldType.MULTISELECT)
    // @IsArray()
    // // @ValidateNested({ each: true })
    // // @Type(() => OptionDto) // Ensures correct transformation
    // @IsOptional()
    // options?: string[];

    /**
     * Handles date fields dynamically for DATE type fields
     */
    @ApiProperty({
        description: 'Date field properties', required: false,
        example: { minDate: '2023-01-01', maxDate: '2025-12-31', format: 'YYYY-MM-DD' },
        type: () => DateFieldProperties
    })
    @ValidateIf(o => o.type === FieldType.DATE)
    @Type(() => DateFieldProperties)
    @ValidateNested()
    @IsOptional()
    dateProperties?: DateFieldProperties;

    // Organization ID associated with the dynamic field
    // @ApiHideProperty()
    @ApiProperty({ description: 'Org Id of current account', required: false })
    @IsMongoId()
    @IsOptional()
    orgId?: string;

    @ApiProperty({ description: 'Indicates if the field is required', type: Boolean, required: false, default: false })
    @IsBoolean()
    @IsOptional()
    isRequired?: boolean;

    @ApiProperty({ description: 'Indicates if the field is related to job applications', type: Boolean, required: false, default: false })
    @IsBoolean()
    @IsOptional()
    isJobField?: boolean;

    @ApiProperty({ description: 'Indicates if the field is related to job applications', type: Boolean, required: false, default: false })
    @IsBoolean()
    @IsOptional()
    isJobApplicationField?: boolean;

    // @ApiProperty({ description: 'Indicates if the field can be deleted or not', type: Boolean, required: false, default: false })
    @ApiHideProperty()
    @IsBoolean()
    @IsOptional()
    canDelete?: boolean;

    @ApiHideProperty()
    @IsNumber()
    @IsOptional()
    order: number;

    @ApiProperty({ description: 'Indicates if the field is visible in job applications', type: Boolean, required: false, default: false })
    @IsBoolean()
    @IsOptional()
    isVisible?: boolean;

    @ApiHideProperty()
    @IsString()
    @IsOptional()
    name: string;

    @ApiProperty({ description: 'Indicates if the field is editable in job applications tracker', type: Boolean, required: false, default: true })
    @IsBoolean()
    @IsOptional()
    canEdit?: boolean;

    @ApiProperty({ description: 'departmentId of the department', required: false })
    @IsMongoId()
    @IsOptional()
    departmentId?: string;

    @ApiProperty({ description: 'contacts ', required: false })
    @IsMongoId()
    @IsOptional()
    contactId?: string | Types.ObjectId;


}