import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { UserModule } from 'src/user/user.module';
import { MongooseModule } from '@nestjs/mongoose';
import { CommonModule } from 'src/common/common.module';
import { BasicUser, BasicUserSchema } from 'src/user/schemas/basic-user.schema';
import { EndpointsRolesModule } from 'src/endpoints-roles/endpoints-roles.module';
import { IntegrationController } from './integrations.controller';
import { IntegrationService } from './integrations.service';
import { Integration, IntegrationSchema } from './schemas/integrations.schema';
@Module({
  controllers: [IntegrationController],
  imports: [
    ConfigModule,
    JwtModule, EndpointsRolesModule,
    UserModule,
    CommonModule,
    MongooseModule.forFeature([
      { name: Integration.name, schema: IntegrationSchema },
      { name: BasicUser.name, schema: BasicUserSchema },
    ]),
  ],
  providers: [IntegrationService],
})
export class IntegrationsModule { }
