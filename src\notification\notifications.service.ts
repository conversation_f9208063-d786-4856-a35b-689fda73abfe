import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Notification } from './schemas/notification.schema';
import { NotificationsGateway } from './notifications.gateway';
import { NotificationType } from 'src/shared/constants';

@Injectable()
export class NotificationsService {
  constructor(
    @InjectModel(Notification.name) private notificationModel: Model<Notification>,
    private readonly gateway: NotificationsGateway
  ) {}

  async createAndNotify(userId: string, message: string, triggeredBy?: string, metadata = {}, type = 'general', jobId?: string) {
    const notif = await this.notificationModel.create({ userId, message, triggeredBy, metadata, type,jobId });
    this.gateway.sendToUser(userId, notif);
    return notif;
  }

  async getUserNotifications(userId: string,jobId?: string): Promise<Notification[]> {
    let query: any = {};

    if (jobId) {
      // Only fetch job-related notifications when jobId is present
      // query = {
      //   type: { $in: [NotificationType.WORKFLOW_UPDATE,NotificationType.JOB_ALLOCATION] },
      //   jobId: jobId,
      //   userId: userId,
      // };

      query = {
        jobId: jobId,
        $or: [
          {
            type: { $in: [NotificationType.WORKFLOW_UPDATE,NotificationType.JOB_ALLOCATION] },
            userId: userId,
          },
          {
            type: NotificationType.JOB_CREATED, // no userId filter
          },
        ],
      };

    } else {
      // Only fetch profile-update notifications when no jobId is given
      query = {
        type: NotificationType.PROFILE_UPDATE,
        userId: userId,
      };
    }
    return this.notificationModel.find(query).sort({ createdAt: -1 }).exec();
  }

  async getAllUserNotifications(userId: string): Promise<Notification[]> {
    let query: any = {};
      query = {
        userId: userId,
        isRead: false,
      };
    return this.notificationModel.find(query).sort({ createdAt: -1 }).exec();
  }

  async markAsRead(notificationId: string) {
    return this.notificationModel.findByIdAndUpdate(notificationId, { isRead: true }, { new: true });
  }

  async markAllAsRead(userId: string) {
    return this.notificationModel.updateMany({ userId, isRead: false }, { isRead: true });
  }

  async deleteNotification(id: string) {
    return this.notificationModel.findByIdAndDelete(id);
  }
}
