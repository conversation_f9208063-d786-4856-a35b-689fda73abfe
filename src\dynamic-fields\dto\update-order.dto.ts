import { IsNotEmpty, <PERSON>N<PERSON>ber, IsString } from 'class-validator';

export class UpdateFieldOrderDto {
  @IsString()
  @IsNotEmpty()
  id: string;

  @IsNumber()
  @IsNotEmpty()
  order: number;
}

import { Type } from 'class-transformer';
import { ValidateNested } from 'class-validator';

export class UpdateFieldOrderBulkDto {
  @ValidateNested({ each: true })
  @Type(() => UpdateFieldOrderDto)
  fields: UpdateFieldOrderDto[];
}
