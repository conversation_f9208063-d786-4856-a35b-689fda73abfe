import { ApiHideProperty, ApiProperty } from "@nestjs/swagger";
import { IsEmail, IsEnum, IsNotEmpty, IsOptional, IsString } from "class-validator";
import { Transform, TransformFnParams } from 'class-transformer';
import { sanitizeWithStyle } from "src/utils/sanitize-with-style";
import { InviteStatus } from "src/shared/constants";



export class CreateVendorInviteDto {
    @ApiProperty({
        type: String,
        required: true,
        description: 'Enter email of the vendor',
        format: 'email',
    })
    @IsNotEmpty()
    @IsEmail()
    @IsString()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value).toLowerCase())
    email: string;

    @ApiProperty({
        type: String,
        required: true,
        description: 'Username'
    })
    @IsString()
    @IsNotEmpty()
    userName: string;

    @ApiProperty({
        type: String,
        required: true,
        description: 'vendor name'
    })
    @IsString()
    @IsNotEmpty()
    vendorName: string;

    @ApiHideProperty()
    @IsOptional()
    createdBy?: string;

     @ApiProperty({
        type: String,
        required: false,
        default: InviteStatus.SEND,
        enum: InviteStatus,
        description: 'Source where the organization was added',
      })
      @IsString()
      @IsOptional()
      @IsEnum(InviteStatus)
      @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
      status?: string;

}
