import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, HydratedDocument, Types } from 'mongoose';
import { Identifier } from 'src/identifier/schemas/identifier.schema';
import { FileStatus, OrgStatus } from 'src/shared/constants';
import { BasicUser } from 'src/user/schemas/basic-user.schema';

export type FileMetadataDocument = HydratedDocument<FileMetadata>;

@Schema({
    timestamps: true
})
export class FileMetadata {

    @Prop({ required: true })
    originalName: string;

    @Prop({ required: true })
    uniqueName: string;

    @Prop({ required: true, default: 0 })
    fileSize: number;

    @Prop({ required: true })
    fileType: string;

    @Prop({
        required: false,
        type: Types.ObjectId, ref: 'BasicUser'
    })
    uploadedBy?: BasicUser;

    @Prop({ required: true })
    locationUrl?: string;

    @Prop({ required: true })
    etag?: string;
  
    @Prop({
        type: String,
        required: false,
        trim: true,
        default: FileStatus.PENDING,
        enum: Object.values(FileStatus),
      })
    status?: string;
    
    @Prop({
        required: false,
        type: Types.ObjectId, ref: 'Identifier'
    })
    identifier?: Types.ObjectId;

}

export const FileMetadataSchema = SchemaFactory.createForClass(FileMetadata);

// Indexing fields for better query performance
FileMetadataSchema.index({ uniqueName: 1 }, { unique: true });
FileMetadataSchema.index({ filePathId: 1 });
