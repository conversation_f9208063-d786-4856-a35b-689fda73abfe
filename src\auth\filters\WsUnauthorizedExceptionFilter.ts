import { Injectable } from '@nestjs/common';
import { BaseWsExceptionFilter, WsException } from '@nestjs/websockets';

import { Socket } from 'socket.io';
import { ExecutionContext, UnauthorizedException } from '@nestjs/common';

@Injectable()
export class WsUnauthorizedExceptionFilter extends BaseWsExceptionFilter {
  catch(exception: WsException, context: ExecutionContext): void {
    const client: Socket = context.switchToWs().getClient();
    client.emit('auth.error', { message: 'Invalid authentication token' });
  }
}
