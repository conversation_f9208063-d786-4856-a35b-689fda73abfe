import { Modu<PERSON> } from '@nestjs/common';
import { RegionService } from './region.service';
import { RegionController } from './region.controller';
import { ConfigModule } from '@nestjs/config';
import { AuthModule } from 'src/auth/auth.module';
import { JwtModule } from '@nestjs/jwt';
import { UserModule } from 'src/user/user.module';
import { MongooseModule } from '@nestjs/mongoose';
import { Region, RegionSchema } from './schemas/region.schema';
import { CommonModule } from 'src/common/common.module';
import { StatusModule } from 'src/status/status.module';
import { StateModule } from 'src/state/state.module';
import { EndpointsRolesModule } from 'src/endpoints-roles/endpoints-roles.module';
@Module({
  imports: [
    ConfigModule,
    UserModule,
    JwtModule, EndpointsRolesModule,
    AuthModule,
    CommonModule,
    StatusModule,
    StateModule,
    MongooseModule.forFeature([{ name: Region.name, schema: RegionSchema }])
  ],
  controllers: [RegionController],
  providers: [RegionService],
})
export class RegionModule { }
