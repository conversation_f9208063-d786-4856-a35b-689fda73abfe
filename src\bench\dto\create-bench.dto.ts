import { ApiHideProperty, ApiProperty } from "@nestjs/swagger";
import { IsAlphanumeric, IsArray, IsBoolean, IsEnum, IsISO8601, IsMongoId, IsNotEmpty, IsNumber, IsObject, IsOptional, IsString, IsUrl, Length, Max, Min, ValidateNested } from "class-validator";
import { Transform, TransformFnParams, Type } from 'class-transformer';
import { sanitizeWithStyle } from "src/utils/sanitize-with-style";
import { ContactInformationDto } from "src/common/dto/contact-information.dto";
import { Currency, Gender } from "src/shared/constants";
import { AddressInformationDto } from "src/common/dto/address-information.dto";

export class SkillDto {
    @ApiProperty({
      type: String,
      required: false,
      description: "Skill name",
    })
    // @IsString()
    // @IsNotEmpty()
    skill?: string;
  
    @ApiProperty({
      type: Number,
      required: false,
      description: "Years of experience in this skill",
    })
    @IsOptional()
    @IsNumber()
    @Min(0)
    years?: number;
  
    @ApiProperty({
      type: Number,
      required: false,
      description: "Months of experience in this skill",
    })
    @IsOptional()
    @IsNumber()
    @Min(0)
    @Max(11) // Months should be between 0 and 11
    months?: number;
  
    @ApiProperty({
      type: Number,
      required: false,
      description: "Skill rating",
    })
    @IsNumber()
    @Min(1)
    @Max(10) // Assuming rating is between 1-5
    rating?: number;
  
    @ApiProperty({
      type: Boolean,
      required: false,
      description: "Is this the primary skill?",
    })
    @IsBoolean()
    isPrimary?: boolean;
  }

// DTO for WorkExperience
export class WorkExperienceDto {
    @ApiProperty({
      type: String,
      required: false,
      description: "Name of the company",
    })
    @IsOptional()
    @IsString()
    companyName?: string;
  
    @ApiProperty({
      type: Date,
      required: false,
      description: "Start date of the job",
    })
    @IsOptional()
    @IsISO8601()
    jobStartDate?: Date;
  
    @ApiProperty({
      type: Date,
      required: false,
      description: "End date of the job",
    })
    @IsOptional()
    @IsISO8601()
    jobEndDate?: Date;
  
    @ApiProperty({
      type: Boolean,
      required: false,
      description: "Whether currently working at this company",
    })
    @IsOptional()
    @IsBoolean()
    currentlyWorking?: boolean;
  }
  
  // DTO for EducationQualification
  export class EducationQualificationDto {
    @ApiProperty({
      type: String,
      required: false,
      description: "Name of the course",
    })
    @IsOptional()
    courseName?: string;
  
    @ApiProperty({
      type: String,
      required: false,
      description: "Name of the university",
    })
    @IsOptional()
    university?: string;
  
    @ApiProperty({
      type: Date,
      required: false,
      description: "Start date of the course",
    })
    @IsOptional()
    @IsISO8601()
    startDate?: Date;
  
    @ApiProperty({
      type: Date,
      required: false,
      description: "End date of the course",
    })
    @IsOptional()
    @IsISO8601()
    endDate?: Date;
  }

export class CreateBenchDto {

    
      @ApiProperty({
        type: String,
        required: false,
        description: 'The description of your org',
      })
      @IsString()
      @IsOptional()
      @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
      description?: string;
    

    @ApiProperty({
        type: String,
        required: false,
    })
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    @IsString()
    @IsOptional()
    // @IsMongoId()
    resumeMetadata?: string;

    @ApiProperty({
        type: String,
        required: false,
    })
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    // @IsString()
    @IsOptional()
    // @IsMongoId()
    candidateImage?: string;
    

    @ApiProperty({
        type: String,
        required: true,
    })
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    @IsString()
    firstName: string;

    @ApiProperty({
        type: String,
        required: false,
    })
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    @IsOptional()
    middleName?: string;

    @ApiProperty({
        type: String,
        required: true,
    })
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    @IsString()
    lastName: string;

    @ApiProperty({
        type: String,
        required: false,
    })
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    @IsOptional()
    designation?: string;


    @ApiProperty({
        type: String,
        required: false,
    })
    @IsAlphanumeric()
    @IsOptional()
    panNumber?: String;

     @ApiProperty({
        type: String,
        required: false,
        description: 'Contact number'
      })
      @IsString()
      @IsOptional()
      // @IsPhoneNumber()
      @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
      mobileNumber?: string;

    @ApiProperty({
        type: ContactInformationDto,
        required: false,
        description: 'Contact information of job applicant'
    })
    @IsOptional()
    @ValidateNested()
    @Type(() => ContactInformationDto)
    contactDetails?: ContactInformationDto;

    @ApiProperty({
        type: AddressInformationDto,
        required: false,
        description: 'Contact address'
    })
    @IsOptional()
    @ValidateNested()
    @Type(() => AddressInformationDto)
    contactAddress?: AddressInformationDto;

    @ApiProperty({
        type: String,
        required: false,
        description: "Enter country"
    })
    @IsMongoId()
    @IsOptional()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value).toLowerCase())
    country?: string;

    @ApiProperty({
        type: String,
        required: false,
        description: "Enter state"
    })
    @IsMongoId()
    @IsOptional()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value).toLowerCase())
    state?: string;

    @ApiProperty({
        type: String,
        required: false,
        description: "Enter city"
    })
    @IsMongoId()
    @IsOptional()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value).toLowerCase())
    city?: string;

    @ApiProperty({
        type: Date,
        required: false,
        default: new Date(Date.UTC(new Date().getUTCFullYear(), new Date().getUTCMonth()))
    })
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    @IsISO8601({ strict: true })
    @Length(10, 24)
    @IsOptional()
    dob?: string;

    @ApiProperty({
        type: String,
        required: false,
        enum: Gender,
    })
    @IsOptional()
    @IsEnum(Gender)
    gender?: Gender;

    @ApiProperty({
        type: Boolean,
        required: false,
        default: false
    })
    @IsOptional()
    @IsBoolean()
    disability?: boolean;

    @ApiProperty({
        type: String,
        required: false,
    })
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    @IsOptional()
    @IsUrl()
    linkedInUrl?: string;

    @ApiProperty({
        type: String,
        required: false,
    })
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    @IsOptional()
    @IsUrl()
    websiteOrBlogUrl?: string;

    @ApiProperty({
        type: Boolean,
        required: false,

    })
    @IsBoolean()
    @IsOptional()
    isExperienced?: boolean;

    @ApiProperty({
        type: Number,
        required: false,
        description: 'Number of years of experience',
    })
    @IsNumber()
    @IsOptional()
    @Min(0) // To ensure the number of years is non-negative
    yearsOfExperience?: number;

    @ApiProperty({
        type: [WorkExperienceDto],
        required: false,
        description: "Work experience for experienced professionals",
      })
      @IsOptional()
      @ValidateNested({ each: true })
      @Type(() => WorkExperienceDto)
      workExperience?: WorkExperienceDto[];
    
      @ApiProperty({
        type: [EducationQualificationDto],
        required: false,
        description: "Educational qualifications",
      })
      @IsOptional()
      @ValidateNested({ each: true })
      @Type(() => EducationQualificationDto)
      educationQualification?: EducationQualificationDto[];

    // @ApiProperty({
    //     type: [String],
    //     required: false,
    // })
    // @IsArray()
    // @IsMongoId({ each: true })
    // @IsOptional()
    // @Transform((params: TransformFnParams) => params.value.map((value: string) => sanitizeWithStyle(value)))
    // evaluationForm?: string[];

    @ApiProperty({
        type: [SkillDto],
        required: false,
        description: "List of skills associated with the bench profile",
      })
      @IsOptional()
      @ValidateNested({ each: true })
      @Type(() => SkillDto)
      skills?: SkillDto[];


    @ApiProperty({
        type: Number,
        required: false,
    })
    @IsOptional()
    @IsNumber()
    @Min(0)
    @Max(90)
    noticePeriodDays?: number;

    @ApiProperty({
        type: Boolean,
        required: false,
    })
    @IsOptional()
    @IsBoolean()
    servingNoticePeriod?: boolean;

    @ApiProperty({
        type: Date,
        required: false,
        default: new Date(Date.UTC(new Date().getUTCFullYear(), new Date().getUTCMonth()))
    })
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    @IsISO8601({ strict: true })
    @Length(10, 24)
    @IsOptional()
    lastWorkingDate?: string;

    @ApiProperty({
        type: String,
        required: false,
    })
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    @IsOptional()
    @IsString()
    currentLocation?: string;

    @ApiProperty({
        type: Boolean,
        required: false,
    })
    @IsOptional()
    @IsBoolean()
    willingToRelocate?: boolean;

    @ApiProperty({
        type: [String],
        required: false,
    })
    @IsOptional()
    @IsMongoId({ each: true })
    @IsArray()
    @Transform((params: TransformFnParams) => params.value.map((value: string) => sanitizeWithStyle(value)))
    reLocation?: string[];

    @ApiProperty({
        type: String,
        required: false,
    })
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    @IsOptional()
    @IsString()
    preferredLocation?: string;

    @ApiProperty({
        type: Number,
        required: false,
    })
    @IsOptional()
    @IsNumber()
    currentCTC?: number;

    @ApiProperty({
        type: Number,
        required: false,
    })
    @IsOptional()
    @IsNumber()
    expectedCTC?: number;

    @ApiProperty({
        type: Number,
        required: false,
    })
    @IsOptional()
    @IsNumber()
    @Max(100)
    ctcPercentage?: number;

    @ApiProperty({
        type: String,
        required: false,
        enum: Currency,
        default: Currency.INR,
    })
    @IsOptional()
    @IsEnum(Currency)
    currency?: Currency;

    @ApiProperty({
        type: Boolean,
        required: false,
    })
    @IsOptional()
    @IsBoolean()
    companyNorms?: boolean;

    @ApiProperty({
        type: String,
        required: false,
    })
    @IsOptional()
    @IsMongoId()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    org?: string;

    @ApiHideProperty()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    createdBy?: string;


    @ApiProperty({
        type: Boolean,
        required: false,
        default: false
    })
    @IsBoolean()
    @IsOptional()
    isSelected?: boolean;

    @ApiProperty({
        type: Boolean,
        required: false,
        default: false
    })
    @IsBoolean()
    @IsOptional()
    isRejected?: boolean;

    @ApiProperty({
        required: false,
        default: false
    })
    @IsBoolean()
    @IsOptional()
    isDraft?: boolean;

    @ApiProperty({
      required: false,
      default: false
    })
    @IsBoolean()
    @IsOptional()
    isDeleted?: boolean;

    
}
