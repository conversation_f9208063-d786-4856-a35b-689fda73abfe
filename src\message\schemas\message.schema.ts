import { <PERSON>p, Schem<PERSON>, SchemaFactory } from "@nestjs/mongoose";
import { HydratedDocument, Types } from "mongoose";
import { Account } from "src/account/schemas/account.schema";
import { Task } from "src/task/schemas/task.schema";
import { BasicUser } from "src/user/schemas/basic-user.schema";
import { Contact } from 'src/contact/schemas/contact.schema';
import { Job } from "src/job/schemas/job.schema";
import { Org } from "src/org/schemas/org.schema";
import { FileMetadata } from 'src/file-upload/schemas/file-metadata.schema';

export type MessageDocument = HydratedDocument<Message>;

@Schema({ timestamps: true })
export class Message {

    @Prop({
        required: true,
        trim: true,
    })
    subject: string;

    @Prop({
        required: true,
        type: Types.ObjectId, ref: 'BasicUser'
    })
    sender: BasicUser;

    @Prop({ required: true, type: [{ type: Types.ObjectId, ref: 'BasicUser' }] })
    recipients: BasicUser[];

    @Prop({
        required: false,
        trim: true,
    })
    contents?: string;

    // @Prop({
    //     required: false,
    //     default: false,
    // })
    // isRead?: boolean;

    @Prop({ type: Map, of: Boolean, default: {} })
    readStatus: Map<string, boolean>;

    @Prop({
        required: false,
        default: [],
        type: [{ type: Types.ObjectId, ref: 'BasicUser' }],
    })
    starredBy?: BasicUser[];

    @Prop({
        required: false,
        default: false,
    })
    isDeleted?: boolean;

    @Prop({
        required: false,
        type: [String],
        default: [],
    })
    attachments?: FileMetadata[];

    @Prop({ default: [], required: true, type: [{ type: Types.ObjectId }] })
    members: Types.ObjectId[];

    @Prop({
        required: false,
        default: [],
        type: [{ type: Types.ObjectId, ref: 'BasicUser' }],
    })
    deletedBy?: Types.ObjectId[];

    @Prop({
        required: false,
        type: Types.ObjectId, ref: 'Task'
    })
    task?: Task;

    @Prop({
        required: false,
        type: Types.ObjectId, ref: 'Org'
    })
    org?: Org;

    @Prop({
        required: false,
        type: Types.ObjectId, ref: 'Contact'
    })
    contact?: Contact;

    @Prop({
        required: false,
        type: Types.ObjectId, ref: 'Job'
    })
    job?: Job;

    @Prop({
        required: false,
        type: Types.ObjectId, ref: 'Message',
        default: null,
    })
    parentMessage?: Message;

    @Prop({
        required: false,
        type: Types.ObjectId,
    })
    threadId?: Types.ObjectId;

    @Prop({
        required: false,
        type: [String],
        default: [],
    })
    labels?: string[];


}

export const MessageSchema = SchemaFactory.createForClass(Message);