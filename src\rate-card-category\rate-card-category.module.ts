import { Module } from '@nestjs/common';
import { RateCardCategoryService } from './rate-card-category.service';
import { RateCardCategoryController } from './rate-card-category.controller';
import { ConfigModule } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { MongooseModule } from '@nestjs/mongoose';
import { BusinessUnit, BusinessUnitSchema } from 'src/business-unit/schemas/business-unit.schema';
import { EndpointsRolesModule } from 'src/endpoints-roles/endpoints-roles.module';
import { BasicUser, BasicUserSchema } from 'src/user/schemas/basic-user.schema';
import { RateCardCategory, RateCardCategorySchema } from './schemas/rate-card-category.schema';
import { RateCard, RateCardSchema } from 'src/rate-card/schemas/rate-card.schema';

@Module({
  imports: [
    ConfigModule,
    JwtModule, EndpointsRolesModule,
    MongooseModule.forFeature([
      { name: RateCardCategory.name, schema: RateCardCategorySchema },
      { name: RateCard.name, schema: RateCardSchema }
    ])
  ],
  controllers: [RateCardCategoryController],
  providers: [RateCardCategoryService],
})
export class RateCardCategoryModule { }
