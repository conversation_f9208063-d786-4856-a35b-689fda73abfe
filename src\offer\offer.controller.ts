import { Controller, Get, Post, Body, Patch, Param, Delete, Logger, Query, Req, UseGuards, BadRequestException } from '@nestjs/common';
import { OfferService } from './offer.service';
import { ApiBearerAuth, ApiOperation, ApiParam, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Roles } from 'src/auth/decorators/roles.decorator';
import { Role } from 'src/auth/enums/role.enum';
import { AuthJwtGuard } from 'src/auth/guards/auth-jwt/auth-jwt.guard';
import { RolesGuard } from 'src/auth/guards/roles/roles.guard';
import { validateObjectId } from 'src/utils/validation.utils';
import { CreateOfferDto } from './dto/create-offer.dto';
import { UpdateOfferDto } from './dto/update-offer.dto';
import { OffersQueryDTO } from './dto/query-offers.dto';
import { CreateBgvDto } from './dto/create-bgv.dto';
import { BudgetConfirmationDto } from './dto/budget-confirmation.dto';
import { ReviewInterimDocDto } from './dto/review-interim-doc.dto';
import { SendUploadLinksDto } from './dto/send-upload-links.dto';
import { ApproveRejectInterimBgvDto } from './dto/approve-reject-interim-bgv.dto';
import { ReviewDocDto } from './dto/review-doc.dto';
import { ReviewHikeApprovalDto } from './dto/review-hike-approval.dto';

@Controller('')
@ApiTags('Offer')
export class OfferController {

  private readonly logger = new Logger(OfferController.name);

  constructor(private readonly offerService: OfferService) { }


  @Post()
  @ApiOperation({
    summary: 'Create a new offer',
    description: `This endpoint allows you to create a new offer. Accessible only to users with roles "${Role.BUHead}", "${Role.ResourceManager}", "${Role.DeliveryManager}"."${Role.TeamLead},and "${Role.AccountManager}"`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager)
  @Roles()
  @ApiResponse({ status: 201, description: 'The offer is created.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Data.' })
  @ApiResponse({ status: 401, description: 'Unauthorized access. Authentication is required.' })
  @ApiResponse({ status: 403, description: `Forbidden. Only users with roles "${Role.BUHead}", "${Role.ResourceManager}", "${Role.DeliveryManager}"."${Role.TeamLead},and "${Role.AccountManager}" are permitted to use this endpoint.` })
  create(@Req() req: any, @Body() createOfferDto: CreateOfferDto) {
    return this.offerService.create(req.user, createOfferDto);
  }


  @Get('all')
  @ApiOperation({
    summary: 'Retrieve all offers.',
    description: `This endpoint retrieves a list of all offers including soft deleted offers. This is accessible only for everyone.`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.Recruiter, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager)
  @Roles()
  @ApiResponse({ status: 200, description: 'All offers are retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  // @ApiResponse({ status: 403, description: `Forbidden. Only users with roles "${Role.BUHead}", "${Role.ResourceManager}", "${Role.DeliveryManager}"."${Role.TeamLead}, and "${Role.AccountManager}" are permitted to use this endpoint.` })
  getAllOffers() {
    return this.offerService.getAllOffers();
  }

  @Get(':id')
  @ApiOperation({
    summary: 'Retrieve an offer by Id',
    description: 'This endpoint retrieves an offer by its Id. This is accessible only for everyone..'
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  // @Roles(Role.Admin, Role.Recruiter, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager)
  @Roles()
  @ApiResponse({ status: 200, description: 'Offer retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  // @ApiResponse({ status: 403, description: 'Forbidden' })
  @ApiResponse({ status: 404, description: 'Offer not found.' })
  findOne(@Param('id') id: string) {
    const objId = validateObjectId(id);
    return this.offerService.findOne(objId);
  }

  @Get(':applicationId/offers')
  @ApiOperation({
    summary: 'Retrieve an offer by Application Id',
    description: 'This endpoint retrieves an offer by its Application Id. This is accessible only for everyone..'
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  // @Roles(Role.Admin, Role.Recruiter, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager)
  // @Roles()
  @ApiResponse({ status: 200, description: 'Offer retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  // @ApiResponse({ status: 403, description: 'Forbidden' })
  @ApiResponse({ status: 404, description: 'Offer not found.' })
  findOfferByApplicationId(@Req() req: any, @Param('applicationId') applicationId: string) {
    const objId = validateObjectId(applicationId);
    return this.offerService.findOfferByApplicationId(req.user, objId);
  }

  @Patch(':id')
  @ApiOperation({
    summary: 'Update an offer by Id',
    description: `This endpoint updates an offer by Id. Accessible only to users with roles "${Role.BUHead}", "${Role.ResourceManager}", "${Role.DeliveryManager}", "${Role.TeamLead}", and "${Role.AccountManager}" .`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard,)
  // @Roles(Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager)
  @Roles()
  @ApiResponse({ status: 200, description: 'Offer is updated.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 403, description: `Forbidden. Only users with roles "${Role.BUHead}", "${Role.ResourceManager}", "${Role.DeliveryManager}", "${Role.TeamLead}", and "${Role.AccountManager}" are permitted to use this endpoint.` })
  @ApiResponse({ status: 404, description: 'Offer not found.' })
  @ApiParam({ name: 'id', description: 'ID of the offer' })
  update(@Req() req: any, @Param('id') id: string, @Body() updateOfferDto: UpdateOfferDto) {
    const objId = validateObjectId(id);
    return this.offerService.update(objId, updateOfferDto, req.user);
  }

  @Delete(':id/hard-delete')
  @ApiOperation({
    summary: 'Hard delete an offer by Id',
    description: `Deletes an offer permanently by its Id. This is accessible only for "${Role.BUHead}", "${Role.ResourceManager}", "${Role.DeliveryManager}", "${Role.TeamLead}", and "${Role.AccountManager}" .`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard,)
  // @Roles(Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager)
  @Roles()
  @ApiResponse({ status: 200, description: 'Offer is hard deleted.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 403, description: `Forbidden. Only users with roles "${Role.BUHead}", "${Role.ResourceManager}", "${Role.DeliveryManager}", "${Role.TeamLead}", and "${Role.AccountManager}"  are permitted to use this endpoint.` })
  @ApiResponse({ status: 404, description: 'Offer not found.' })
  @ApiParam({ name: 'id', description: 'ID of the offer' })
  remove(@Param('id') id: string) {
    const objId = validateObjectId(id);
    return this.offerService.hardDelete(objId);
  }

  @Get('/acceptance-reminder')
  @ApiOperation({
    summary: 'Send an offer letter acceptance reminder',
    description: `This endpoint sends a reminder email for offer letter acceptance for the given job application ID. This Accessible is for everyone.`,
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.Recruiter, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager)
  @Roles()
  @ApiResponse({ status: 200, description: 'Reminder email sent successfully.' })
  @ApiResponse({ status: 400, description: 'Invalid job application ID or other validation error.' })
  @ApiResponse({ status: 401, description: 'Unauthorized access. Authentication is required.' })
  // @ApiResponse({ status: 403, description: `Forbidden. Only users with roles "${Role.SuperAdmin}", "${Role.Admin}", "${Role.SalesRep}" are permitted to use this endpoint.` })
  @ApiQuery({ name: 'jobApplicationId', required: true, type: String, description: 'Job Application ID for which the reminder is being sent.' })
  @ApiQuery({ name: 'dueDate', required: true, type: Date, description: 'DueDate for which the offer is to be accepted' })
  async sendAcceptanceReminder(@Req() req: any, @Query('jobApplicationId') jobApplicationId: string, @Query('dueDate') dueDate: Date) {
    const objId = validateObjectId(jobApplicationId);
    if (!objId) {
      throw new BadRequestException('Invalid or missing Job Application ID.');
    }
    return this.offerService.sendAcceptanceReminder(req.user, jobApplicationId, dueDate);
  }

  @Get('/document-reminder')
  @ApiOperation({
    summary: 'Send an offer letter acceptance reminder',
    description: `This endpoint sends a reminder email for offer letter acceptance for the given job application ID. This Accessible is for everyone.`,
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.Recruiter, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager)
  @Roles()
  @ApiResponse({ status: 200, description: 'Reminder email sent successfully.' })
  @ApiResponse({ status: 400, description: 'Invalid job application ID or other validation error.' })
  @ApiResponse({ status: 401, description: 'Unauthorized access. Authentication is required.' })
  // @ApiResponse({ status: 403, description: `Forbidden. Only users with roles "${Role.SuperAdmin}", "${Role.Admin}", "${Role.SalesRep}" are permitted to use this endpoint.` })
  @ApiQuery({ name: 'jobApplicationId', required: true, type: String, description: 'Job Application ID for which the reminder is being sent.' })
  async sendDocumentReminder(@Req() req: any, @Query('jobApplicationId') jobApplicationId: string) {
    const objId = validateObjectId(jobApplicationId);
    if (!objId) {
      throw new BadRequestException('Invalid or missing Job Application ID.');
    }
    return this.offerService.sendDocumentReminder(req.user, jobApplicationId);
  }

  @Get('/offer-ratio')
  @ApiResponse({ status: 200, description: 'Offer ratio calculated successfully.' })
  @ApiResponse({ status: 401, description: 'Unauthorized access.' })
  // @ApiResponse({ status: 403, description: 'Forbidden: Only admin or sales-rep can access this endpoint.' })
  @ApiOperation({
    summary: 'Get offer ratio for logged-in user',
    description: `This endpoint calculates and returns the offer acceptance ratio for the logged-in user. This Accessible is for everyone.`,
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.Recruiter, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager)
  @Roles()
  async getOfferRatio(@Req() req: any) {
    const userId = req.user._id; // Extract user ID from the request object
    return this.offerService.calculateOfferRatio(userId);
  }

  @Patch('/accept-offer')
  @ApiResponse({ status: 200, description: 'Offer updated successfully.' })
  @ApiResponse({ status: 400, description: 'Invalid job application ID or no offers found.' })
  @ApiResponse({ status: 401, description: 'Unauthorized access.' })
  @ApiResponse({ status: 403, description: 'Forbidden only user with "BUHead", "ResourceManager", "DeliveryManager", "TeamLead", and "AccountManager" can access this endpoint.' })
  @ApiOperation({
    summary: 'Update the most recent offer as accepted for a specific job application',
    description: `This endpoint updates the most recent offer for the specified job application ID by setting its "isAccepted" field to true. It is Accessible to "BUHead", "ResourceManager", "DeliveryManager", "TeamLead", and "AccountManager".`,
  })
  @ApiQuery({ name: 'jobApplicationId', type: String, required: true, description: 'The ID of the job application' })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager)
  @Roles()
  async acceptMostRecentOffer(@Query('jobApplicationId') jobApplicationId: string): Promise<{ message: string }> {
    return this.offerService.acceptMostRecentOffer(jobApplicationId);
  }

  @Patch('/onBoarded')
  @ApiResponse({ status: 200, description: 'Candidate onboarded successfully.' })
  @ApiResponse({ status: 400, description: 'Invalid job application ID or no offers found.' })
  @ApiResponse({ status: 401, description: 'Unauthorized access.' })
  @ApiResponse({ status: 403, description: 'Forbidden only user with "BUHead", "ResourceManager", "DeliveryManager", "TeamLead", and "AccountManager" can access this endpoint.' })
  @ApiOperation({
    summary: 'Update the most recent onboarded for a specific job application',
    description: `This endpoint updates the most recent onboarded specified job application ID by setting its "isOnBoarded" field to true. It is Accessible to  only user with "BUHead", "ResourceManager", "DeliveryManager", "TeamLead", and "AccountManager"`,
  })
  @ApiQuery({ name: 'jobApplicationId', type: String, required: true, description: 'The ID of the job application' })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager)
  @Roles()
  async markAsOnBoarded(@Query('jobApplicationId') jobApplicationId: string): Promise<{ message: string }> {
    return this.offerService.markAsOnBoarded(jobApplicationId);
  }

  // @Delete('delete-all')
  // @ApiOperation({ 
  //   summary: 'Hard delete all offers', 
  //   description: `This endpoint hard deletes all offers. This is accessible only for "${Role.SuperAdmin}" and "${Role.Admin}"` 
  // })

  // @ApiBearerAuth()
  // @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.SuperAdmin, Role.Admin)
  // @ApiResponse({ status: 200, description: 'All offers are deleted.' })
  // @ApiResponse({ status: 401, description: 'Unauthorized.' })
  // @ApiResponse({ status: 403, description: `Forbidden. Only users with roles "${Role.SuperAdmin}" and "${Role.Admin}" are permitted to use this endpoint.` })
  // @ApiResponse({ status: 404, description: 'Offers not found.' })
  // deleteAll() {
  //   return this.offerService.hardDeleteAllOffers();
  // }

  // @Get('offer-filter')
  // @ApiBearerAuth()
  // @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.SuperAdmin, Role.SalesRep)
  // @ApiOperation({
  //   summary: 'Retrieve offer created by organization',
  //   description: `This endpoint returns a list of offer created by organization. This is only accessible for "admin", "superAdmin" and "salesRep"`,
  // })
  // @ApiResponse({ status: 200, description: 'Offer retrieved created by organization.' })
  // @ApiResponse({ status: 401, description: 'Unauthorized.' })
  // @ApiResponse({ status: 403, description: 'Forbidden, user with role admin, superAdmin, and sales rep can only use this endpoint.' })
  // @ApiQuery({ name: 'orgId', required: true, type: String, description: 'Organaization Id' })
  // @ApiQuery({ name: 'isDefault', required: false, type: Boolean, description: 'Default wrokflow of the organization'})
  // async offerFilter(@Query('orgId') orgId: string, @Query('isDefault') isDefault?: boolean) {
  //   const objId = validateObjectId(orgId);
  //   return this.offerService.offerFilter(objId, isDefault);
  // }

  @Get('pending-onboardings')
  @ApiOperation({
    summary: 'Retrieve all onboardings.',
    description: `This endpoint retrieves a list of all onboardings . This is accessible only for everyone.`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  // @Roles(Role.Admin, Role.Recruiter, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager)
  @Roles()
  @ApiResponse({ status: 200, description: 'All offers are retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  // @ApiResponse({ status: 403, description: `Forbidden. Only users with roles "${Role.BUHead}", "${Role.ResourceManager}", "${Role.DeliveryManager}"."${Role.TeamLead}, and "${Role.AccountManager}" are permitted to use this endpoint.` })
  getAllOnboardings(@Req() req: any, @Query() query: OffersQueryDTO) {
    return this.offerService.getAllOnboardings(req.user, query);
  }

  @Post('save-documents')
  @ApiOperation({
    summary: 'Save BGV Documents',
    description: `This endpoint allows you to Save BGV Documents. Accessible only to users with roles "${Role.BUHead}", "${Role.ResourceManager}", "${Role.DeliveryManager}"."${Role.TeamLead},and "${Role.AccountManager}"`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  // @Roles(Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager)
  @Roles()
  @ApiResponse({ status: 201, description: 'The offer is created.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Data.' })
  @ApiResponse({ status: 401, description: 'Unauthorized access. Authentication is required.' })
  @ApiResponse({ status: 403, description: `Forbidden. Only users with roles "${Role.BUHead}", "${Role.ResourceManager}", "${Role.DeliveryManager}"."${Role.TeamLead},and "${Role.AccountManager}" are permitted to use this endpoint.` })
  saveDocuments(@Req() req: any, @Body() createBgvDto: CreateBgvDto) {
    return this.offerService.saveBgvDocuments(req.user, createBgvDto);
  }

  // @Get(':applicationId/bgv-documents')
  // @ApiOperation({
  //   summary: 'Retrieve an bgv documents by Application Id',
  //   description: 'This endpoint retrieves an bgv documents by its Application Id. This is accessible only for everyone..'
  // })
  // @ApiBearerAuth()
  // @UseGuards(AuthJwtGuard)
  // // @Roles(Role.Admin, Role.Recruiter, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager)
  // @Roles()
  // @ApiResponse({ status: 200, description: 'Offer retrieved.' })
  // @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  // @ApiResponse({ status: 404, description: 'Offer not found.' })
  // getBgvDocuments(@Param('applicationId') applicationId: string) {
  //   const objId = validateObjectId(applicationId);
  //   return this.offerService.findBgvDocumentsByApplicationId(objId);
  // }

  @Patch(':applicationId/submit-bgv-documents')
  @ApiOperation({
    summary: 'Retrieve an bgv documents by Application Id',
    description: 'This endpoint retrieves an bgv documents by its Application Id. This is accessible only for everyone..'
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  // @Roles(Role.Admin, Role.Recruiter, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager)
  @Roles()
  @ApiResponse({ status: 200, description: 'Offer retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 404, description: 'Offer not found.' })
  submitBgvDocuments(@Param('applicationId') applicationId: string) {
    const objId = validateObjectId(applicationId);
    return this.offerService.submitBgvDocuments(objId);
  }


  @Post('save-bgvHandlerInfo')
  @ApiOperation({
    summary: 'Save BGV Handler Information',
    description: `This endpoint allows you to Save BGV Handler Information.`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  // @Roles(Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager)
  @Roles()
  @ApiResponse({ status: 201, description: 'The offer is created.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Data.' })
  @ApiResponse({ status: 401, description: 'Unauthorized access. Authentication is required.' })
  @ApiResponse({ status: 403, description: `Forbidden. Only users with roles` })
  updateBgvHandlerMeta(@Req() req: any, @Body() createBgvDto: CreateBgvDto) {
    return this.offerService.saveBgvHandlerInfo(req.user, createBgvDto);
  }

  @Get(':applicationId/bgvInfo')
  @ApiOperation({
    summary: 'Retrieve an BGV Information by Application Id',
    description: 'This endpoint retrieves an BGV Information by its Application Id. This is accessible only for everyone..'
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  @Roles()
  @ApiResponse({ status: 200, description: 'Offer retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 404, description: 'Offer not found.' })
  getBgvInfo(@Param('applicationId') applicationId: string) {
    const objId = validateObjectId(applicationId);
    return this.offerService.findBgvInfoByApplicationId(objId);
  }

  @Post('upload-interim-bgv')
  @ApiOperation({
    summary: 'Save BGV Documents',
    description: `This endpoint allows you to Save BGV Documents. Accessible only to users with roles "${Role.BUHead}", "${Role.ResourceManager}", "${Role.DeliveryManager}"."${Role.TeamLead},and "${Role.AccountManager}"`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  // @Roles(Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager)
  @Roles()
  @ApiResponse({ status: 201, description: 'The offer is created.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Data.' })
  @ApiResponse({ status: 401, description: 'Unauthorized access. Authentication is required.' })
  @ApiResponse({ status: 403, description: `Forbidden. Only users with roles "${Role.BUHead}", "${Role.ResourceManager}", "${Role.DeliveryManager}"."${Role.TeamLead},and "${Role.AccountManager}" are permitted to use this endpoint.` })
  saveInterimBgv(@Req() req: any, @Body() createBgvDto: CreateBgvDto) {
    return this.offerService.saveInterimBgv(req.user, createBgvDto);
  }

  @Patch(':applicationId/submit-interimBgv-Report')
  @ApiOperation({
    summary: 'Retrieve an bgv documents by Application Id',
    description: 'This endpoint retrieves an bgv documents by its Application Id. This is accessible only for everyone..'
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  // @Roles(Role.Admin, Role.Recruiter, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager)
  @Roles()
  @ApiResponse({ status: 200, description: 'Offer retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 404, description: 'Offer not found.' })
  submitInterimBgvReports(@Param('applicationId') applicationId: string) {
    const objId = validateObjectId(applicationId);
    return this.offerService.submitIntrimBgv(objId);
  }

  @Patch(':applicationId/replace-rejected-doc')
  @ApiOperation({
    summary: 'Retrieve an bgv documents by Application Id',
    description: 'This endpoint retrieves an bgv documents by its Application Id. This is accessible only for everyone..'
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  // @Roles(Role.Admin, Role.Recruiter, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager)
  @Roles()
  @ApiResponse({ status: 200, description: 'Offer retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 404, description: 'Offer not found.' })
  @ApiQuery({ name: 'rejectedFileMetadataId', required: true, type: String, description: 'Id of Job' })
  @ApiQuery({ name: 'newFileMetadataId', required: true, type: String, description: 'Id of Job' })
  replaceRejectedInterimBgvReports(
    @Param('applicationId') applicationId: string,
    @Query('rejectedFileMetadataId') rejectedFileMetadataId: string,
    @Query('newFileMetadataId') newFileMetadataId: string) {
    const objId = validateObjectId(applicationId);
    return this.offerService.replaceRejectedDoc(applicationId, rejectedFileMetadataId, newFileMetadataId);
  }

  @Patch(':applicationId/save-budget-confirmation')
  @ApiOperation({
    summary: 'Retrieve an bgv documents by Application Id',
    description: 'This endpoint retrieves an bgv documents by its Application Id. This is accessible only for everyone..'
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  // @Roles(Role.Admin, Role.Recruiter, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager)
  @Roles()
  @ApiResponse({ status: 200, description: 'Offer retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 404, description: 'Offer not found.' })
  saveBudgetConfirmation(@Req() req: any, @Param('applicationId') applicationId: string, @Body() budgetConfirmationDto: BudgetConfirmationDto) {
    const objId = validateObjectId(applicationId);
    return this.offerService.saveBudgetConfirmation(req.user, objId, budgetConfirmationDto);
  }

  @Patch(':applicationId/interim-docs/review-single')
  @ApiOperation({
    summary: 'Retrieve an bgv documents by Application Id',
    description: 'This endpoint retrieves an bgv documents by its Application Id. This is accessible only for everyone..'
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  // @Roles(Role.Admin, Role.Recruiter, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager)
  @Roles()
  @ApiResponse({ status: 200, description: 'Offer retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 404, description: 'Offer not found.' })
  reviewSingleInterimBgvDoc(@Req() req: any, @Param('applicationId') applicationId: string, @Body() reviewSingleInterimDocDto: ReviewInterimDocDto) {
    const objId = validateObjectId(applicationId);
    return this.offerService.reviewSingleInterimDoc(req.user, objId, reviewSingleInterimDocDto);
  }

  @Patch(':applicationId/client/offer-release')
  @ApiOperation({
    summary: 'Retrieve an bgv documents by Application Id',
    description: 'This endpoint retrieves an bgv documents by its Application Id. This is accessible only for everyone..'
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  // @Roles(Role.Admin, Role.Recruiter, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager)
  @Roles()
  @ApiResponse({ status: 200, description: 'Offer retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 404, description: 'Offer not found.' })
  clientOfferRelease(@Req() req: any, @Param('applicationId') applicationId: string) {
    const objId = validateObjectId(applicationId);
    return this.offerService.clientOfferRelease(req.user, objId);
  }

  @Post('upload-offer-letter')
  @ApiOperation({
    summary: 'Save offer letter Documents',
    description: `This endpoint allows you to Save offer letter Documents. Accessible only to users with roles "${Role.BUHead}", "${Role.ResourceManager}", "${Role.DeliveryManager}"."${Role.TeamLead},and "${Role.AccountManager}"`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  // @Roles(Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager)
  @Roles()
  @ApiResponse({ status: 201, description: 'The offer is created.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Data.' })
  @ApiResponse({ status: 401, description: 'Unauthorized access. Authentication is required.' })
  @ApiResponse({ status: 403, description: `Forbidden. Only users with roles "${Role.BUHead}", "${Role.ResourceManager}", "${Role.DeliveryManager}"."${Role.TeamLead},and "${Role.AccountManager}" are permitted to use this endpoint.` })
  saveOfferLetters(@Req() req: any, @Body() createBgvDto: CreateBgvDto) {
    return this.offerService.saveOfferLetters(req.user, createBgvDto);
  }

  @Post('revise-offer-letter')
  @ApiOperation({
    summary: 'Save offer letter Documents',
    description: `This endpoint allows you to Save offer letter Documents. Accessible only to users with roles "${Role.BUHead}", "${Role.ResourceManager}", "${Role.DeliveryManager}"."${Role.TeamLead},and "${Role.AccountManager}"`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  // @Roles(Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager)
  @Roles()
  @ApiResponse({ status: 201, description: 'The offer is created.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Data.' })
  @ApiResponse({ status: 401, description: 'Unauthorized access. Authentication is required.' })
  @ApiResponse({ status: 403, description: `Forbidden. Only users with roles "${Role.BUHead}", "${Role.ResourceManager}", "${Role.DeliveryManager}"."${Role.TeamLead},and "${Role.AccountManager}" are permitted to use this endpoint.` })
  reviseOfferLetters(@Req() req: any, @Body() createBgvDto: CreateBgvDto) {
    return this.offerService.reviseOfferLetter(req.user, createBgvDto);
  }

  @Patch(':applicationId/accept-offer')
  @ApiOperation({
    summary: 'Retrieve an bgv documents by Application Id',
    description: 'This endpoint retrieves an bgv documents by its Application Id. This is accessible only for everyone..'
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  // @Roles(Role.Admin, Role.Recruiter, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager)
  @Roles()
  @ApiResponse({ status: 200, description: 'Offer retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 404, description: 'Offer not found.' })
  acceptOfferLetters(@Req() req: any, @Param('applicationId') applicationId: string) {
    const objId = validateObjectId(applicationId);
    return this.offerService.acceptOffer(objId);
  }

  @Patch(':applicationId/revoke-release')
  @ApiOperation({
    summary: 'Retrieve an bgv documents by Application Id',
    description: 'This endpoint retrieves an bgv documents by its Application Id. This is accessible only for everyone..'
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  // @Roles(Role.Admin, Role.Recruiter, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager)
  @Roles()
  @ApiResponse({ status: 200, description: 'Offer retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 404, description: 'Offer not found.' })
  rejectOfferLetters(@Req() req: any, @Param('applicationId') applicationId: string) {
    const objId = validateObjectId(applicationId);
    return this.offerService.revokeOffer(objId);
  }

  @Patch(':applicationId/inviteEmployee')
  @ApiOperation({
    summary: 'Retrieve an bgv documents by Application Id',
    description: 'This endpoint retrieves an bgv documents by its Application Id. This is accessible only for everyone..'
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  // @Roles(Role.Admin, Role.Recruiter, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager)
  @Roles()
  @ApiResponse({ status: 200, description: 'Offer retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 404, description: 'Offer not found.' })
  inviteEmployee(@Req() req: any, @Param('applicationId') applicationId: string) {
    const objId = validateObjectId(applicationId);
    return this.offerService.convertCandidateToEmployee(objId, req.user);
  }

  @Patch(':applicationId/onboard-perm-employee')
  @ApiOperation({
    summary: 'Retrieve an bgv documents by Application Id',
    description: 'This endpoint retrieves an bgv documents by its Application Id. This is accessible only for everyone..'
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  // @Roles(Role.Admin, Role.Recruiter, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager)
  @Roles()
  @ApiResponse({ status: 200, description: 'Offer retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 404, description: 'Offer not found.' })
  onboardPermCandidateToEmployee(@Req() req: any, @Param('applicationId') applicationId: string) {
    const objId = validateObjectId(applicationId);
    return this.offerService.onboardPermCandidateToEmployee(objId, req.user);
  }

  @Post('bgv/send-upload-links')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  @Roles()
  async sendUploadLinks(
    @Body() dto: SendUploadLinksDto,
  ) {
    return this.offerService.sendUploadLinksToCandidates(dto);
  }

  @Post(':applicationId/bgv/initiate-bgv')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  @Roles()
  async initiateBgv(
    // @Body() dto: SendUploadLinksDto,
    @Req() req: any,
    @Param('applicationId') applicationId: string
  ) {
    // const objId = validateObjectId(applicationId);

    return this.offerService.initiateBgv(applicationId,req.user);
  }

  @Get(':applicationId/getBgvDocuments')
  @ApiOperation({
    summary: 'Retrieve an bgv documents by Application Id',
    description: 'This endpoint retrieves an bgv documents by its Application Id. This is accessible only for everyone..'
  })
  // @ApiBearerAuth()
  // @UseGuards(AuthJwtGuard)
  // @Roles(Role.Admin, Role.Recruiter, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager)
  // @Roles()
  @ApiResponse({ status: 200, description: 'Offer retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 404, description: 'Offer not found.' })
  getBgvDocumentsforCandidate(@Param('applicationId') applicationId: string) {
    const objId = validateObjectId(applicationId);
    return this.offerService.findBgvDocumentsForCandidateByApplicationId(objId);
  }

  @Post('candidate/save-documents')
  @ApiOperation({
    summary: 'Save BGV Documents',
    description: `This endpoint allows you to Save BGV Documents. Accessible only to users with roles "${Role.BUHead}", "${Role.ResourceManager}", "${Role.DeliveryManager}"."${Role.TeamLead},and "${Role.AccountManager}"`
  })
  // @ApiBearerAuth()
  // @UseGuards(AuthJwtGuard)
  // @Roles()
  @ApiResponse({ status: 201, description: 'The offer is created.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Data.' })
  @ApiResponse({ status: 401, description: 'Unauthorized access. Authentication is required.' })
  @ApiResponse({ status: 403, description: `Forbidden. Only users with roles "${Role.BUHead}", "${Role.ResourceManager}", "${Role.DeliveryManager}"."${Role.TeamLead},and "${Role.AccountManager}" are permitted to use this endpoint.` })
  candidateSaveDocuments(@Body() createBgvDto: CreateBgvDto) {
    return this.offerService.saveCandidateBgvDocuments(createBgvDto);
  }

  @Patch(':applicationId/candidate/submit-bgv-documents')
  @ApiOperation({
    summary: 'Retrieve an bgv documents by Application Id',
    description: 'This endpoint retrieves an bgv documents by its Application Id. This is accessible only for everyone..'
  })
  // @ApiBearerAuth()
  // @UseGuards(AuthJwtGuard)
  // @Roles(Role.Admin, Role.Recruiter, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager)
  // @Roles()
  @ApiResponse({ status: 200, description: 'Offer retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 404, description: 'Offer not found.' })
  submitCandidateBgvDocuments(@Param('applicationId') applicationId: string) {
    const objId = validateObjectId(applicationId);
    return this.offerService.submitCandidateBgvDocuments(objId);
  }

  @Get(':applicationId/candidate/bgv-documents')
  @ApiOperation({
    summary: 'Retrieve an bgv documents by Application Id',
    description: 'This endpoint retrieves an bgv documents by its Application Id. This is accessible only for everyone..'
  })
  // @ApiBearerAuth()
  // @UseGuards(AuthJwtGuard)
  // @Roles(Role.Admin, Role.Recruiter, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager)
  @Roles()
  @ApiResponse({ status: 200, description: 'Offer retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 404, description: 'Offer not found.' })
  findBgvDocumentsByCandidateId(@Param('applicationId') applicationId: string) {
    const objId = validateObjectId(applicationId);
    return this.offerService.findBgvDocumentsByCandidateId(objId);
  }

  // @Get(':applicationId/getBgvHandlerDocs')
  // @ApiOperation({
  //   summary: 'Retrieve an bgv documents by Application Id',
  //   description: 'This endpoint retrieves an bgv documents by its Application Id. This is accessible only for everyone..'
  // })
  // // @ApiBearerAuth()
  // // @UseGuards(AuthJwtGuard)
  // // @Roles(Role.Admin, Role.Recruiter, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager)
  // // @Roles()
  // @ApiResponse({ status: 200, description: 'Offer retrieved.' })
  // @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  // @ApiResponse({ status: 404, description: 'Offer not found.' })
  // findBgvHandlerForCandidateByApplicationId(@Param('applicationId') applicationId: string) {
  //   const objId = validateObjectId(applicationId);
  //   return this.offerService.findBgvHandlerForCandidateByApplicationId(objId);
  // }

  @Patch(':applicationId/interim-bgv-decision')
  @ApiOperation({
    summary: 'Retrieve an bgv documents by Application Id',
    description: 'This endpoint retrieves an bgv documents by its Application Id. This is accessible only for everyone..'
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  // @Roles(Role.Admin, Role.Recruiter, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager)
  @Roles()
  @ApiResponse({ status: 200, description: 'Offer retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 404, description: 'Offer not found.' })
  approveCandidateInterimBgv(@Param('applicationId') applicationId: string,@Body() payload: ApproveRejectInterimBgvDto) {
    const objId = validateObjectId(applicationId);
    return this.offerService.approveCandidateInterimBgv(objId,payload);
  }

  @Get(':applicationId/bgv/status')
  @ApiOperation({
    summary: 'Retrieve an bgv documents by Application Id',
    description: 'This endpoint retrieves an bgv documents by its Application Id. This is accessible only for everyone..'
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  // @Roles(Role.Admin, Role.Recruiter, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager)
  @Roles()
  @ApiResponse({ status: 200, description: 'Offer retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 404, description: 'Offer not found.' })
  getBgvDocuments(@Param('applicationId') applicationId: string) {
    const objId = validateObjectId(applicationId);
    return this.offerService.getCandidateBgvStatus(applicationId);
  }

  @Patch(':applicationId/bgv/review')
  @ApiOperation({
    summary: 'Retrieve an bgv documents by Application Id',
    description: 'This endpoint retrieves an bgv documents by its Application Id. This is accessible only for everyone..'
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  // @Roles(Role.Admin, Role.Recruiter, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager)
  @Roles()
  @ApiResponse({ status: 200, description: 'Offer retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 404, description: 'Offer not found.' })
  reviewCandidateDocument(@Req() req: any,@Param('applicationId') applicationId: string,@Body() payload: ReviewDocDto) {
    const objId = validateObjectId(applicationId);
    return this.offerService.approveCandidateDocuments(req.user,objId,payload);
  }

  @Get('job-seeker/details')
  @ApiOperation({
    summary: 'Retrieve an bgv documents by Application Id',
    description: 'This endpoint retrieves an bgv documents by its Application Id. This is accessible only for everyone..'
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  // @Roles(Role.Admin, Role.Recruiter, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager)
  @Roles()
  @ApiResponse({ status: 200, description: 'Offer retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 404, description: 'Offer not found.' })
  getCandidateDetails(@Req() req: any) {
    return this.offerService.getCandidateDetails(req.user);
  }

  @Patch('hikeApprovals/review')
  @ApiOperation({
    summary: 'Retrieve an bgv documents by Application Id',
    description: 'This endpoint retrieves an bgv documents by its Application Id. This is accessible only for everyone..'
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  @Roles()
  @ApiResponse({ status: 200, description: 'Offer retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 404, description: 'Offer not found.' })
  reviewHikeApprovals(@Req() req: any,@Body() payload: ReviewHikeApprovalDto) {
    return this.offerService.reviewHikeApproval(req.user,payload);
  }

  @Get('pending-offer-approvals')
  @ApiOperation({
    summary: 'Retrieve all onboardings.',
    description: `This endpoint retrieves a list of all onboardings . This is accessible only for everyone.`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  @Roles()
  @ApiResponse({ status: 200, description: 'All offers are retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  getPendingOfferApprovals(@Req() req: any, @Query() query: OffersQueryDTO) {
    return this.offerService.getPendingOfferApprovals(req.user, query);
  }

  @Get(':approvalId/offerApproval')
  @ApiOperation({
    summary: 'Retrieve an BGV Information by Application Id',
    description: 'This endpoint retrieves an BGV Information by its Application Id. This is accessible only for everyone..'
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  @Roles()
  @ApiResponse({ status: 200, description: 'Offer retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 404, description: 'Offer not found.' })
  getOfferApprovalInfo(@Param('approvalId') approvalId: string) {
    const objId = validateObjectId(approvalId);
    return this.offerService.getApprovalById(objId);
  }

  @Patch(':applicationId/bgv/sendReminder')
  @ApiOperation({
    summary: 'Retrieve an bgv documents by Application Id',
    description: 'This endpoint retrieves an bgv documents by its Application Id. This is accessible only for everyone..'
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  // @Roles(Role.Admin, Role.Recruiter, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager)
  @Roles()
  @ApiResponse({ status: 200, description: 'Offer retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 404, description: 'Offer not found.' })
  sendReminderToCandidate(@Req() req: any,@Param('applicationId') applicationId: string) {
    const objId = validateObjectId(applicationId);
    return this.offerService.sendDocumentReminderToCandidate(applicationId);
  }

  @Get(':applicationId/approvals/offer')
  @ApiOperation({
    summary: 'Retrieve an bgv documents by Application Id',
    description: 'This endpoint retrieves an bgv documents by its Application Id. This is accessible only for everyone..'
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  // @Roles(Role.Admin, Role.Recruiter, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.AccountManager)
  @Roles()
  @ApiResponse({ status: 200, description: 'Offer retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 404, description: 'Offer not found.' })
  getApprovalsByOffer(@Param('applicationId') applicationId: string) {
    const objId = validateObjectId(applicationId);
    return this.offerService.getApprovalsByOffer(applicationId);
  }

}
