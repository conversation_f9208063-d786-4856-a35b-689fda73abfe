import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsBoolean, IsOptional, IsString, IsMongoId, IsDate, IsEmail, IsNotEmpty } from 'class-validator';
import { Transform, TransformFnParams } from 'class-transformer';
import sanitizeWithStyle from 'sanitize-html'

export class InviteeDto {

  @ApiProperty({
    type: String,
    required: true,
    description: 'Email address of the invitee',
    example: '<EMAIL>',
  })
  @IsNotEmpty()
  @IsEmail()
  @IsString()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value).toLowerCase())
  emailAddress: string;

  @ApiPropertyOptional({
    type: Boolean,
    required: false,
    description: 'Indicates if the invitee is an internal user',
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  isInternalUser?: boolean;

  @ApiPropertyOptional({
    type: String,
    required: false,
    description: 'User ID of the invitee if the invitee is a registered user'
  })
  @IsOptional()
  @IsMongoId()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value).toLowerCase())
  user?: string;

  @ApiPropertyOptional({
    type: Boolean,
    required: false,
    description: 'Indicates whether the invitee has accepted the invitation',
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  isInviteAccepted?: boolean; // Whether the invitee has accepted the invitation

  @ApiPropertyOptional({
    required: false,
    description: 'The user ID who admitted the invitee to the meeting'
  })
  @IsOptional()
  @IsMongoId()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value).toLowerCase())
  admittedBy?: string; // The user who admitted the invitee to the meeting

  @ApiPropertyOptional({
    required: false,
    description: 'The user ID who denied the invitee to the meeting'
  })
  @IsOptional()
  @IsMongoId()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value).toLowerCase())
  deniedBy?: string; // The user who denied the invitee to the meeting

  @ApiPropertyOptional({
    required: false,
    description: 'Timestamp when the invitee joined the meeting',
    example: '2024-11-14T10:00:00Z',
    type: Date,
  })
  @IsOptional()
  @IsDate()
  joinedAt?: Date; // Timestamp when the invitee joins the meeting

  @ApiPropertyOptional({
    required: false,
    description: 'Timestamp when the invitee left the meeting',
    example: '2024-11-14T12:00:00Z',
    type: Date,
  })
  @IsOptional()
  @IsDate()
  leftAt?: Date; // Timestamp when the invitee leaves the meeting
}
