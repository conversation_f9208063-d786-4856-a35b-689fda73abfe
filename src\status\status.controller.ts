import { <PERSON>, Get, Post, Body, Patch, Param, Delete, Logger, UseGuards, Query, BadRequestException, HttpException, HttpStatus } from '@nestjs/common';
import { StatusService } from './status.service';
import { CreateStatusDto } from './dto/create-status.dto';
import { UpdateStatusDto } from './dto/update-status.dto';
import { ApiBearerAuth, ApiOperation, ApiParam, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Roles } from 'src/auth/decorators/roles.decorator';
import { Role } from 'src/auth/enums/role.enum';
import { AuthJwtGuard } from 'src/auth/guards/auth-jwt/auth-jwt.guard';
import { RolesGuard } from 'src/auth/guards/roles/roles.guard';
import { validateObjectId } from 'src/utils/validation.utils';

@Controller('')
@ApiTags('Status')
export class StatusController {

  private readonly logger = new Logger(StatusController.name);

  constructor(private readonly statusService: StatusService) {}

  @Post()
  @ApiOperation({
    summary: 'Create a new status',
    description: `This endpoint allows you to create a new status. Accessible only to users with role "${Role.SuperAdmin}".`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.SuperAdmin)
  @Roles()
  @ApiResponse({ status: 201, description: 'The status is created.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Data.' })
  @ApiResponse({ status: 401, description: 'Unauthorized access. Authentication is required.' })
  @ApiResponse({ status: 403, description: `Forbidden. Only users with role "${Role.SuperAdmin}" are permitted to use this endpoint.` })
  create(@Body() createStatusDto: CreateStatusDto) {
    // try {
    //   return this.statusService.create(createStatusDto);
    // } catch (error) {
    //   if ( error.code === 11000) {
    //     throw new HttpException(
    //       'Status with the provided name and status type already exists.',
    //       HttpStatus.CONFLICT
    //     );
    //   }

    //   throw new HttpException(
    //     `Error while creating Status: ${error.message || 'Internal Server Error'}`,
    //     HttpStatus.INTERNAL_SERVER_ERROR
    //   );
    // }
    return this.statusService.create(createStatusDto);
  }

  @Get('all')
  @ApiOperation({
    summary: 'Retrieve all statuses',
    description: `This endpoint returns a list of all statuses. Accessible only to users with role "${Role.SuperAdmin}"`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.SuperAdmin)
  @Roles()
  @ApiResponse({ status: 200, description: 'All statuses are retrieved.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Data.' })
  @ApiResponse({ status: 401, description: 'Unauthorized access. Authentication is required.' })
  @ApiResponse({ status: 403, description: `Forbidden. Only users with role "${Role.SuperAdmin}" are permitted to use this endpoint.` })
  findAll() {
    return this.statusService.findAll();
  }

  @Get()
  @ApiOperation({ summary: 'Get list of statuses by type', description: `This endpoint gives the list of statuses by type. This is accessible only for "${Role.SuperAdmin}" and "${Role.Admin}"` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.SuperAdmin, Role.Admin)
  @Roles()
  @ApiResponse({ status: 200, description: 'All statuses are retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized ' })
  @ApiResponse({ status: 403, description: 'Forbidden user with role super admin and admin can only use this end point.' })
  @ApiQuery({ name: 'statusType', required: true, type: String, description: 'Type of Status', example: "Region" })
  async getCountriesByStatus(@Query('statusType') statusType: string) {
    if (!statusType) {
      throw new BadRequestException('Status type query parameter is required');
    }
    return await this.statusService.findAllStatusByType(statusType);
  }

  @Get(':statusId')
  @ApiOperation({
    summary: 'Retrieve a status by Id',
    description: `This endpoint retrieves a status by its Id. Accessible only to users with role "${Role.SuperAdmin}"`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.SuperAdmin)
  @Roles()
  @ApiResponse({ status: 200, description: 'Status is retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized access. Authentication is required.' })
  @ApiResponse({ status: 403, description: `Forbidden. Only users with role "${Role.SuperAdmin}" are permitted to use this endpoint.` })
  @ApiResponse({ status: 404, description: 'Status not found.' })
  @ApiParam({ name: 'statusId', description: 'ID of the status' })
  findOne(@Param('statusId') statusId: string) {
    const statusObjId = validateObjectId(statusId);
    return this.statusService.findById(statusObjId);
  }

  @Patch(':statusId')
  @ApiOperation({
    summary: 'Update a status by Id',
    description: `This endpoint updates a status by Id. Accessible only to users with roles "${Role.SuperAdmin}".`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.SuperAdmin)
  @Roles()
  @ApiResponse({ status: 200, description: 'Status is updated.' })
  @ApiResponse({ status: 401, description: 'Unauthorized access. Authentication is required.' })
  @ApiResponse({ status: 403, description: `Forbidden. Only users with role "${Role.SuperAdmin}" are permitted to use this endpoint.` })
  @ApiResponse({ status: 404, description: 'Status not found.' })
  @ApiParam({ name: 'statusId', description: 'ID of the status' })
  update(@Param('statusId') statusId: string, @Body() updateStatusDto: UpdateStatusDto) {
    const statusObjId = validateObjectId(statusId);
    return this.statusService.update(statusObjId, updateStatusDto);
  }

  @Patch(':statusId/restore')
  @ApiOperation({
    summary: 'Restore a status by Id',
    description: `This endpoint restores a status by Id. Accessible only to users with roles "${Role.SuperAdmin}".`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.SuperAdmin)
  @Roles()
  @ApiResponse({ status: 200, description: 'Status is restored' })
  @ApiResponse({ status: 401, description: 'Unauthorized access. Authentication is required.' })
  @ApiResponse({ status: 403, description: `Forbidden. Only users with role "${Role.SuperAdmin}" are permitted to use this endpoint.` })
  @ApiResponse({ status: 404, description: 'Status not found.' })
  @ApiParam({ name: 'statusId', description: 'ID of the status' })
  restore(@Param('statusId') statusId: string) {
    const statusObjId = validateObjectId(statusId);
    return this.statusService.restore(statusObjId);
  }

  @Delete(':statusId')
  @ApiOperation({
    summary: 'Delete a status by Id',
    description: `This endpoint deletes a status by Id. Accessible only to users with roles "${Role.SuperAdmin}".`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.SuperAdmin)
  @Roles()
  @ApiResponse({ status: 200, description: 'Status is deleted' })
  @ApiResponse({ status: 401, description: 'Unauthorized access. Authentication is required.' })
  @ApiResponse({ status: 403, description: `Forbidden. Only users with role "${Role.SuperAdmin}" are permitted to use this endpoint.` })
  @ApiResponse({ status: 404, description: 'Status not found.' })
  @ApiParam({ name: 'statusId', description: 'ID of the status' })
  remove(@Param('statusId') statusId: string) {
    const statusObjId = validateObjectId(statusId);
    return this.statusService.delete(statusObjId);
  }
}
