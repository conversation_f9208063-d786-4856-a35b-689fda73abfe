import { Module } from '@nestjs/common';
import { StatusService } from './status.service';
import { StatusController } from './status.controller';
import { JwtModule } from '@nestjs/jwt';
import { MongooseModule } from '@nestjs/mongoose';
import { Status, StatusSchema } from './schemas/status.schema';
import { EndpointsRolesModule } from 'src/endpoints-roles/endpoints-roles.module';
@Module({
  controllers: [StatusController],
  imports: [JwtModule, EndpointsRolesModule, MongooseModule.forFeature([{ name: Status.name, schema: StatusSchema }])],
  providers: [StatusService],
  exports: [StatusService, MongooseModule],
})
export class StatusModule {}
