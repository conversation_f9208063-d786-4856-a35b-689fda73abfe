name: talency.in Cap Build & Deploy
env:
  CAPROVER_HOST: 'https://captain.gana.talency.in/'
  #remember to change the secret.app_token if changing the caprover_app  
  CAPROVER_APP: apis
on:
  push:
    branches: [ "main" ]
  workflow_dispatch:
jobs:
  cap_build:
    runs-on: ubuntu-latest
    timeout-minutes: 5
    steps:
      - name: Checkout the repo 
        uses: actions/checkout@v4
        with:
         ref: main
      - name: C<PERSON> 
        uses: actions/cache@v4
        with:
            # See here for caching with `yarn` https://github.com/actions/cache/blob/main/examples.md#node---yarn or you can leverage caching with actions/setup-node https://github.com/actions/setup-node
            path: |
              ~/.npm
              ${{ github.workspace }}/.next/cache
            # Generate a new cache whenever packages or source files change.
            key: ${{ runner.os }}-nextjs-${{ hashFiles('**/package-lock.json') }}-${{ hashFiles('**/*.[jt]s', '**/*.[jt]sx') }}
            # If source files changed but packages didn't, rebuild from a prior cache.
            restore-keys: |
              ${{ runner.os }}-nextjs-${{ hashFiles('**/package-lock.json') }}-
      - uses: actions/setup-node@v4
        with:
          node-version: 20
      - run: npm ci
      - name: Build the app with increased heap limit
        run: npm run build --if-present
        env:
          NODE_OPTIONS: --max-old-space-size=4096
      
      - name: copy files
        run: |
          cp captain-definition dist/.
          cp Dockerfile dist/.
          cp package.json dist/.
      - uses: a7ul/tar-action@v1.2.0
        with:
          command: c
          cwd: "./"
          files: |
            ./
          outPath: deploy.tar
      - name: Deploy App to CapRover
        uses: caprover/deploy-from-github@v1.1.2
        with:
          server: ${{ env.CAPROVER_HOST }}
          app: ${{ env.CAPROVER_APP }}
          token: '${{ secrets.GANA_TALENCY_TOKEN }}'  
          #token: '${{ secrets.TEST_APP_TOKEN }}'            
