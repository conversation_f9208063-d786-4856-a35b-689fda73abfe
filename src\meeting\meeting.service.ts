import { BadRequestException, Injectable, InternalServerErrorException, Logger, NotFoundException } from '@nestjs/common';
import { CreateMeetingDto } from './dto/create-meeting.dto';
import { UpdateMeetingDto } from './dto/update-meeting.dto';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { InjectModel } from '@nestjs/mongoose';
import { Model, ObjectId, Types } from 'mongoose';
import { Meeting, MeetingDocument } from './schemas/meeting.schema';
import { InviteeDto } from './dto/create-invitee.dto';
import { Invitee, InviteeDocument } from './schemas/invitee.schema';
import { MeetingStatus } from 'src/shared/constants';
import { format } from 'date-fns';
import { RespondToMeetingDto } from './dto/respond-meeting.dto';


@Injectable()
export class MeetingService {

  private readonly logger = new Logger(MeetingService.name);

  constructor(
    @InjectModel(Meeting.name) private meetingModel: Model<Meeting>,
    private eventEmitter: EventEmitter2
  ) { }

  async create(createMeetingDto: CreateMeetingDto) {
    try {
      // const meetingLink = await this.meetingModel.findOne({ meetingLink: createMeetingDto.meetingLink }).exec();
      // if (meetingLink) {
      //   throw new BadRequestException('Meeting link already exists');
      // }
      const meeting = new this.meetingModel(createMeetingDto);
      await meeting.save();
      // Emit an event for meeting creation
      //TODO
      this.eventEmitter.emit('meeting.created', meeting);
      return meeting;
    } catch (error) {
      this.logger.error('Error creating meeting', error);
      throw error;
    }
  }

  async findAll(page: number, limit: number, orgId?: string, subject?: string, email?: string) {
    try {
      const filter: any = {};
      if (orgId) filter.org = orgId;
      if (subject) {
        const regex = new RegExp(subject, 'i');
        filter.subject = { $regex: regex };
      }
      if (email) {
        filter.invitees = { $in: [email] };
      }
      const meetings = await this.meetingModel.find(filter)
        .populate({ path: 'organizer', select: '_id roles firstName lastName email', model: 'BasicUser' })
        .populate({ path: 'org', select: '_id title', model: 'Org' })
        .populate({ path: 'createdBy', select: '_id roles firstName' })
        .sort({ createdAt: -1 })
        .skip((page - 1) * limit)
        .limit(limit)
        .exec();
      return meetings;
    } catch (error) {
      this.logger.error('Error fetching all meetings', error);
      throw new InternalServerErrorException(`Error while retriveing meetings. ${error.message}`);
    }
  }

  async findOne(meetingId: Types.ObjectId) {
    try {
      const meeting = await this.meetingModel.findById(meetingId)
        .populate({ path: 'organizer', select: '_id roles firstName lastName email', model: 'BasicUser' })
        .populate({ path: 'org', select: '_id title', model: 'Org' })
        .populate({ path: 'createdBy', select: '_id roles firstName' })
        .exec();
      if (!meeting) {
        throw new NotFoundException(`Meeting with ID ${meetingId} not found`);
      }
      return meeting;
    } catch (error) {
      this.logger.error(`Error fetching meeting with ID ${meetingId}`, error);
      throw error;
    }
  }

  async update(user: any, meetingId: Types.ObjectId, updateMeetingDto: UpdateMeetingDto) {
    try {
      const updatedMeeting = await this.meetingModel.findByIdAndUpdate(
        meetingId,
        updateMeetingDto,
        { new: true }
      ).exec();

      if (!updatedMeeting) {
        throw new NotFoundException(`Meeting with ID ${meetingId} not found`);
      }

      // Adjust the scheduledAt time and format it in one step
      const formattedScheduledAt = format(
        new Date(new Date(updatedMeeting.scheduledAt).getTime() + new Date().getTimezoneOffset()),
        'MMM dd yyyy, h:mm a'
      );

      // this.logger.log(formattedScheduledAt);
      // Emit an event for meeting update
      this.eventEmitter.emit('meeting.updated', {
        user: user, data: {
          ...updatedMeeting.toJSON(),
          // scheduledAt: format(new Date(updatedMeeting.scheduledAt), 'MMM dd yyyy, h:mm a')
          scheduledAt: formattedScheduledAt
        }
      });

      return updatedMeeting;
    } catch (error) {
      this.logger.error(`Error updating meeting with ID ${meetingId}`, error);
      throw error;
    }
  }

  async remove(meetingId: Types.ObjectId) {
    try {
      const deletedMeeting = await this.meetingModel.findByIdAndDelete(meetingId).exec();

      if (!deletedMeeting) {
        throw new NotFoundException(`Meeting with ID ${meetingId} not found`);
      }

      // Emit an event for meeting deletion
      //todo
      this.eventEmitter.emit('meeting.deleted', deletedMeeting);

      return { message: 'Meeting deleted successfully' };
    } catch (error) {
      this.logger.error(`Error deleting meeting with ID ${meetingId}`, error);
      throw error;
    }
  }

  async count(orgId?: string, email?: string): Promise<Object> {
    const query: any = {};
    if (orgId) query.org = orgId;

    if (email) {
      query.invitees = { $in: [email] };
    }

    const meetingsCount = await this.meetingModel.countDocuments(query).exec();
    return {
      count: meetingsCount
    };
  }

  async addParticipants(meetingId: Types.ObjectId, invitees: Types.ObjectId[]) {
    try {
      const meeting = await this.meetingModel.findById(meetingId).exec();

      if (!meeting) {
        throw new NotFoundException(`Meeting with ID ${meetingId} not found`);
      }

      // Add invitees to the meeting's invitees list
      // if (meeting.invitees) {
      //   meeting.invitees.push(...invitees);
      // }

      // Save updated meeting
      await meeting.save();

      // Emit an event for adding participants
      this.eventEmitter.emit('meeting.participants.added', meeting);

      return meeting;
    } catch (error) {
      this.logger.error(`Error adding participants to meeting with ID ${meetingId}`, error);
      throw error;
    }
  }

  async sendReminders(meetingId: Types.ObjectId) {
    try {
      const meeting = await this.meetingModel
        .findById(meetingId)
        .populate<{ invitees: InviteeDocument[] }>('invitees')
        .exec();

      if (!meeting) {
        throw new NotFoundException(`Meeting with ID ${meetingId} not found`);
      }

      // Example logic to send reminders (pseudo-logic for actual reminder sending)
      meeting.invitees.forEach((invitee) => {
        if (!invitee.isInviteAccepted) {
          // Send reminder (this could be an email, SMS, etc.)
          this.eventEmitter.emit('meeting.reminder.sent', {
            meetingId: meeting._id,
            inviteeId: invitee._id,
            emailAddress: invitee.emailAddress,
          });

          this.logger.log(`Reminder sent to ${invitee.emailAddress}`);
        }
      });

      // Emit an event for sending reminders
      this.eventEmitter.emit('meeting.reminder.sent', meeting);

      return { message: 'Reminders sent to invitees' };
    } catch (error) {
      this.logger.error(`Error sending reminders for meeting with ID ${meetingId}`, error);
      throw error;
    }
  }

  async markParticipantJoined(meetingId: string, userId: string) {
    try {
      const meeting = await this.meetingModel
        .findById(meetingId)
        .populate<{ invitees: InviteeDocument[] }>('invitees')
        .exec();

      if (!meeting) {
        throw new NotFoundException(`Meeting with ID ${meetingId} not found`);
      }

      const invitee = meeting.invitees.find(
        (invitee) => invitee.user?.toString() === userId || invitee.emailAddress === userId
      );

      if (!invitee) {
        throw new NotFoundException(`Invitee not found in the meeting`);
      }

      invitee.joinedAt = new Date();
      await meeting.save();

      // Emit an event for participant joining
      this.eventEmitter.emit('meeting.participant.joined', meeting);

      return meeting;
    } catch (error) {
      this.logger.error(`Error marking participant as joined for meeting with ID ${meetingId}`, error);
      throw error;
    }
  }

  async markParticipantLeft(meetingId: string, userId: string) {
    try {
      const meeting = await this.meetingModel
        .findById(meetingId)
        .populate<{ invitees: InviteeDocument[] }>('invitees')
        .exec();

      if (!meeting) {
        throw new NotFoundException(`Meeting with ID ${meetingId} not found`);
      }

      const invitee = meeting.invitees.find(
        (invitee) => invitee.user?.toString() === userId || invitee.emailAddress === userId
      );

      if (!invitee) {
        throw new NotFoundException(`Invitee not found in the meeting`);
      }

      invitee.leftAt = new Date();
      await meeting.save();

      // Emit an event for participant leaving
      this.eventEmitter.emit('meeting.participant.left', meeting);

      return meeting;
    } catch (error) {
      this.logger.error(`Error marking participant as left for meeting with ID ${meetingId}`, error);
      throw error;
    }
  }

  async getMeetingDetails(url: string) {
    const meetingId = this.generateOTP(10);
    const passcode = this.generateOTP(6);

    return url + "/" + meetingId + "?pwd=" + passcode;

  }

  generateOTP(length: number): string {
    const digits = '0123456789';
    let OTP = '';
    for (let i = 0; i < length; i++) {
      OTP += digits[Math.floor(Math.random() * 10)];
    }
    return OTP;
  }


  // this end point Check conditions:
  // 1. Meeting is scheduled in the future or is ongoing
  // 2. Status is either scheduled or ongoing
  // 3. Meeting exists
  async findOneByLink(meetingLink: string) {
    // Find the meeting by the unique meeting link
    const meeting = await this.meetingModel
      .findOne({ meetingLink: meetingLink })
      .populate({ path: 'organizer', select: '_id roles firstName lastName email', model: 'BasicUser' })
      .populate({ path: 'org', select: '_id title', model: 'Org' })
      .populate({ path: 'createdBy', select: '_id roles firstName' })
      .exec();

    if (!meeting) {
      throw new NotFoundException('Meeting not found');
    }

    // Check current time
    const currentTime = new Date();

    const isValidLink =
      meeting.scheduledAt >= currentTime &&
      (meeting.status === MeetingStatus.SCHEDULED ||
        meeting.status === MeetingStatus.ONGOING) &&
      !meeting.isDeleted;

    if (!isValidLink) {
      return {
        valid: false,
        reason: this.getDenyReason(meeting, currentTime)
      };
    }

    return {
      valid: true,
      meeting: meeting
    };
  }

  // Helper method to provide specific reasons for link invalidity
  private getDenyReason(meeting: Meeting, currentTime: Date): string {

    if (meeting.scheduledAt < currentTime) {
      return 'Meeting time has already passed';
    }

    if (meeting.status !== MeetingStatus.SCHEDULED &&
      meeting.status !== MeetingStatus.ONGOING) {
      return 'Meeting is not in an active state';
    }

    return 'Meeting is not currently available';
  }

  async respondToMeeting(meetingId: string, respondToMeetingDto: RespondToMeetingDto): Promise<{ message: string }> {
    const { email, isAccept, isReject } = respondToMeetingDto;

    if (!email || (isAccept === undefined && isReject === undefined)) {
      throw new BadRequestException('Email and at least one response (isAccept or isReject) are required.');
    }

    if (isAccept && isReject) {
      throw new BadRequestException('Cannot accept and reject at the same time.');
    }

    const meeting = await this.meetingModel.findById(meetingId).exec();

    if (!meeting) {
      throw new NotFoundException('Meeting not found.');
    }

    // Ensure accept and reject are always arrays
    meeting.accept = meeting.accept ?? [];
    meeting.reject = meeting.reject ?? [];

    let responseMessage = '';

    // Accept meeting
    if (isAccept) {
      if (!meeting.accept.includes(email)) {
        meeting.accept.push(email);
      }
      responseMessage = 'Meeting accepted successfully.';
      // Ensure email is removed from reject list
      meeting.reject = meeting.reject.filter(e => e !== email);
    }

    // Reject meeting
    if (isReject) {
      if (!meeting.reject.includes(email)) {
        meeting.reject.push(email);
      }
      responseMessage = 'Meeting rejected successfully.';
      // Ensure email is removed from accept list
      meeting.accept = meeting.accept.filter(e => e !== email);
    }

    await meeting.save();
    return { message: responseMessage };
  }

}
