// preference.controller.ts
import { Controller, Get, Post, Body, Patch, Param, Delete, Logger, UseGuards, Req, Query } from '@nestjs/common';
import { ApiBearerAuth, ApiBody, ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { AuthJwtGuard } from 'src/auth/guards/auth-jwt/auth-jwt.guard';
import { PreferenceService } from './preference.service';
import { CreatePreferenceDto } from './dto/create-preference.dto';
import { UpdatePreferenceDto } from './dto/update.preference.dto';
import { QueryPreferenceDto } from './dto/query.preference.dto';
import { RolesGuard } from 'src/auth/guards/roles/roles.guard';
import { Role } from 'src/auth/enums/role.enum';
import { Roles } from 'src/auth/decorators/roles.decorator';
import { validateObjectId } from 'src/utils/validation.utils';
import { OrgService } from 'src/org/org.service';

@Controller('')
@ApiTags('Preferences')
export class PreferenceController {
  private readonly logger = new Logger(PreferenceController.name);

  constructor(private readonly preferenceService: PreferenceService,private orgService: OrgService) { }

  @Post()
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.JobSeeker)
  @Roles()
  @ApiResponse({ status: 200, description: ' Preferences added sucessfully.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Data.' })
  @ApiResponse({ status: 401, description: 'Unauthorized ' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  create(@Req() req: any, @Body() createPreferenceDto: CreatePreferenceDto) {
    this.logger.log(`Creating preference for user: ${req.user._id}`);
    return this.preferenceService.create(createPreferenceDto, req.user);
  }

  @Get()
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.JobSeeker)
  @Roles()
  @ApiResponse({ status: 200, description: ' Preferences Retireved sucessfully.' })
  @ApiResponse({ status: 400, description: 'BadRequest/Invalid skillId.' })
  @ApiResponse({ status: 401, description: 'Unauthorized ' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @ApiResponse({ status: 404, description: ' Preference not found.' })
  findAll(@Req() req: any) {
    this.logger.log(`Fetching preferences for user: ${req.user._id}`);
    return this.preferenceService.findAll(req.user._id);
  }


  @Delete('skill/:skillId')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.JobSeeker)
  @Roles()
  @ApiResponse({ status: 200, description: 'Skill removed successfully.' })
  @ApiResponse({ status: 404, description: 'Skill not found.' })
  async deleteSkill(@Req() req: any, @Param('skillId') skillId: string) {
    validateObjectId(skillId);
    return this.preferenceService.deleteSkill(req.user._id, skillId);
  }

  @Delete('location/:locationId')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.JobSeeker)
  @Roles()
  @ApiResponse({ status: 200, description: 'Location removed successfully.' })
  @ApiResponse({ status: 404, description: 'Location not found.' })
  async deleteLocation(@Req() req: any, @Param('locationId') locationId: string) {
    validateObjectId(locationId);
    return this.preferenceService.deleteLocation(req.user._id, locationId);
  }

  @Delete('client/:clientId')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.JobSeeker)
  @Roles()
  @ApiResponse({ status: 200, description: 'Client removed successfully.' })
  @ApiResponse({ status: 404, description: 'Client not found.' })

  async deleteClient(@Req() req: any, @Param('clientId') clientId: string) {
    validateObjectId(clientId);
    return this.preferenceService.deleteClient(req.user._id, clientId);
  }

  @Get('clients')
  @ApiOperation({
    summary: 'Retrieve all organizations with type "customer-org"',
    description: 'Job seekers can retrieve all organizations of type "customer-org". No additional filters are applied.'
  })
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.JobSeeker)
  @Roles()
  @ApiBearerAuth()
  @ApiResponse({ status: 200, description: 'List of customer organizations retrieved successfully.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. Authentication is required.' })
  @ApiResponse({ status: 403, description: 'Forbidden. Only users with the JobSeeker role can access this endpoint.' })
  async getCustomerOrgs() {
    return this.orgService.getCustomerOrgs();
  }
}




