import { Modu<PERSON> } from '@nestjs/common';
import { RecruiterTargetService } from './recruiter-target.service';
import { RecruiterTargetController } from './recruiter-target.controller';
import { ConfigModule } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { UserModule } from 'src/user/user.module';
import { AuthModule } from 'src/auth/auth.module';
import { OrgModule } from 'src/org/org.module';
import { RecruiterTarget, RecruiterTargetSchema } from './schemas/recuriter-target.schema';
import { MongooseModule } from '@nestjs/mongoose';
import { EndpointsRolesModule } from 'src/endpoints-roles/endpoints-roles.module';
@Module({
    imports: [
      ConfigModule,
      JwtModule, EndpointsRolesModule,
      UserModule,
      AuthModule,
      OrgModule,
      MongooseModule.forFeature([{ name:RecruiterTarget.name, schema: RecruiterTargetSchema }])
    ],
  controllers: [RecruiterTargetController],
  providers: [RecruiterTargetService],
})
export class RecruiterTargetModule {}
