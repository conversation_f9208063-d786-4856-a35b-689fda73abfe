import { <PERSON>du<PERSON> } from '@nestjs/common';
import { BenchService } from './bench.service';
import { BenchController } from './bench.controller';
import { ConfigModule } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { UserModule } from 'src/user/user.module';
import { CommonModule } from 'src/common/common.module';
import { MongooseModule } from '@nestjs/mongoose';
import { Bench, BenchSchema } from './schemas/bench.schema';
import { Org, OrgSchema } from 'src/org/schemas/org.schema';
import { BasicUser, BasicUserSchema } from 'src/user/schemas/basic-user.schema';
import { JobModule } from 'src/job/job.module';
import { Job, JobSchema } from 'src/job/schemas/job.schema';
import { EndpointsRolesModule } from 'src/endpoints-roles/endpoints-roles.module';
import { JobAllocationBase, JobAllocationBaseSchema } from 'src/job-allocation/schemas/job-allocation-base.schema';

@Module({
  controllers: [BenchController],
  imports: [
    ConfigModule,
    JwtModule, EndpointsRolesModule,
    UserModule,
    CommonModule,
    JobModule,
    MongooseModule.forFeature([
      { name:Bench.name, schema: BenchSchema },
      { name:Job.name, schema: JobSchema },
      { name: Org.name, schema: OrgSchema },
      { name: BasicUser.name, schema: BasicUserSchema },
      { name: JobAllocationBase.name, schema: JobAllocationBaseSchema },
    ]),
  ],
  providers: [BenchService],
})
export class BenchModule { }
