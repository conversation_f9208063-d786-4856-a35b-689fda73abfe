
import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsArray, IsBoolean, IsEnum, IsMongoId, IsOptional, IsString, Validate } from 'class-validator';
import { EmploymentType, JobType, WorkMode } from 'src/shared/constants';
import moment from 'moment';

export class CustomDateFormatValidator {
    validate(date: string, args: any): boolean {
        const [format] = args.constraints;
        return moment(date, format, true).isValid();
    }

    defaultMessage(): string {
        return 'Invalid date format';
    }
}
export class OffersQueryDTO {
    @IsOptional()
    @Validate(CustomDateFormatValidator, ['YYYY-MM-DD'], {
        message: 'fromDate must be in the format YYYY-MM-DD',
    })
    @ApiProperty({ description: 'Filter jobs created after this date (YYYY-MM-DD)', required: false })
    fromDate?: string;

    @IsOptional()
    @Validate(CustomDateFormatValidator, ['YYYY-MM-DD'], {
        message: 'toDate must be in the format YYYY-MM-DD',
    })
    @ApiProperty({ description: 'Filter jobs created before this date (YYYY-MM-DD)', required: false })
    toDate?: string;

    @IsOptional()
    @Transform(({ value }) => {
        return value === undefined || value === '0' ? '' : value;
    })
    postingOrg?: string;

    @IsOptional()
    @IsEnum(EmploymentType, { message: 'Invalid Employment type specified' })
    @Transform(({ value }) => {
        return value === undefined || value.trim() === '' ? null : value;
    })
    employmentType?: string;

    @IsOptional()
    @Transform(({ value }) => {
        return value === undefined || value === '0' ? '' : value;
    })
    endClientOrg?: string;

    @IsOptional()
    @Transform(({ value }) => {
        return value === undefined || value === '0' ? '' : value;
    })
    hiringOrg?: string;

    @IsOptional()
    @IsString()
    @Transform(({ value }) => (value === undefined || value === '0' ? '' : value))
    name?: string;

    @IsOptional()
    @ApiProperty({ required: false, default: 1 })
    @Transform(({ value }) => {
        return value > 0 ? value : 1;
    })
    page: number = 1;

    @IsOptional()
    @ApiProperty({ required: false })
    @Transform(({ value }) => {
        const parsed = parseInt(value);
        return !isNaN(parsed) && parsed > 0 ? parsed : 1000000;
    })
    limit: number = 1000000;


    @ApiProperty({
        type: String,
        required: false,
        enum: JobType,
        description: 'Type of job (e.g., Internal, External)',
    })
    @IsEnum(JobType)
    @IsOptional()
    @Transform(({ value }) => (value === undefined || value === '0' ? '' : value))
    jobType?: string;

}

