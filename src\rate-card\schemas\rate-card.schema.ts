import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";
import { HydratedDocument, Types } from 'mongoose';
import { Schema as MongooseSchema } from "mongoose";
import { Org } from "src/org/schemas/org.schema";
import { RateCardCategory } from "src/rate-card-category/schemas/rate-card-category.schema";
import { BasicUser } from "src/user/schemas/basic-user.schema";

export type RateCardDocument = HydratedDocument<RateCard>;

@Schema({
    timestamps: true
})
export class RateCard {

    @Prop({
        type: Types.ObjectId,
        ref: 'RateCardCategory',
        required: true
    })
    category: RateCardCategory;

    @Prop({
        type: String,
        required: true,
        trim: true,
        maxlength: 1000
    })
    name: string;

    @Prop({
        type: String,
        required: false,
        trim: true,
        maxlength: 1000
    })
    description?: string;

    @Prop({
        type: Number,
        required: false
    })
    fixedRate?: number;

    @Prop({
        type: Number,
        required: false
    })
    percentageOfCTC?: number;

    @Prop({
        type: Types.ObjectId,
        ref: 'Org',
        required: true
    })
    org: Org;

    @Prop({
        type: Types.ObjectId,
        ref: 'BasicUser',
        required: true
    })
    createdBy: BasicUser;

    @Prop({
        type: Boolean,
        default: false
    })
    isDeleted?: boolean;

    @Prop({
        type: [{
            // key: { type: String, required: true },
            label: { type: String, required: true },
            value: { type: MongooseSchema.Types.Mixed, required: true },
            description: { type: String, required: false }, // optional
        }],
        default: [],
    })
    other: Array<{
        // key: string;
        label: string;
        value: any;
        description?: string;
    }>;

}

export const RateCardSchema = SchemaFactory.createForClass(RateCard);
