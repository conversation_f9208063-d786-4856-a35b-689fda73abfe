// src/invoice/schemas/invoice.schema.ts
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { Employee } from 'src/employee/schemas/employee.schema';
import { FileMetadata } from 'src/file-upload/schemas/file-metadata.schema';
import { JobApplication } from 'src/job-application-form/schemas/job-application.schema';
import { Org } from 'src/org/schemas/org.schema';
import { TimeSheet } from 'src/time-sheet/schemas/time-sheet.schema';
import { BasicUser } from 'src/user/schemas/basic-user.schema';

export type InvoiceDocument = Invoice & Document;

@Schema({ timestamps: true })
export class Invoice {
  @Prop({ required: true, unique: true })
  invoiceNumber: string;


  @Prop({
    type: Types.ObjectId,
    required: false,
    ref: 'JobApplication',
  })
  jobApplication?: JobApplication;

  // 🔽 ADD TIMESHEET REFERENCE
  @Prop({
    type: Types.ObjectId,
    required: false,
    ref: 'Timesheet', // Make sure this matches your Timesheet schema name
  })
  timesheet?: TimeSheet;
  


  @Prop({
    type: Number,
    required: true,
    min: 0,
    default: 0,
  })
  amount: number;

  @Prop({ required: true, default: 'New' })
  status: string;

  @Prop({
    type: Date,
    required: false,
  })
  dueDate?: Date;


  @Prop({
    type: Date,
    required: false,
  })
  raisedDate?: Date;

  @Prop({
    type: [Types.ObjectId],
    required: false,
    default: [],
  })
  fileMetaDataId?: FileMetadata[];


  @Prop({
    required: true,
    type: Types.ObjectId, ref: 'Org'
  })
  billedByOrg: Org;

  @Prop({
    required: false,
    type: Types.ObjectId,
    ref: 'Org'
  })
  billedToOrg?: Org;

  
  @Prop({
    required: false,
    type: Types.ObjectId,
    ref: 'Employee'
  })
  employee?: Employee;

  @Prop({
    required: false,
    type: Types.ObjectId, ref: 'BasicUser'
  })
  generatedBy?: BasicUser;

  @Prop({
    required: false,
    type: Types.ObjectId, ref: 'BasicUser'
  })
  forwardedBy?: BasicUser;

  @Prop({
    required: false,
    type: Boolean,
    default: false
  })
  isDeleted?: boolean;

  @Prop({
    required: false,
    type: Boolean,
    default: true
  })
  isTemp?: boolean;
  // 🔽 NEW PROPERTIES BELOW

  @Prop({ type: String, enum: ['weekly', 'monthly'], required: false })
  invoiceType?: 'weekly' | 'monthly';

  @Prop({ type: String }) // For weekly
  weekLabel?: string;

  @Prop({ type: Date }) // For weekly
  weekStartDate?: Date;

  @Prop({ type: Date }) // For weekly
  weekEndDate?: Date;

  @Prop({ type: String }) // For monthly
  period?: string; // e.g., "2025-07-01 to 2025-07-31"

  @Prop({ type: Number, required: false, min: 0, default: 0 })
  hours?: number;
}

export const InvoiceSchema = SchemaFactory.createForClass(Invoice);
