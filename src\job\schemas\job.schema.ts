// shankar sir --for now fields are not mandatory may be we will change the jobs creation fields and told to follow workable.com for the refernce.

import { Prop, Schema, SchemaFactory, raw } from "@nestjs/mongoose";
import { HydratedDocument, Types } from "mongoose";
import { BusinessUnit } from "src/business-unit/schemas/business-unit.schema";
import { SocialMediaLink } from "src/common/schemas/social-media-link.schema";
import { Contact } from "src/contact/schemas/contact.schema";
import { FileMetadata } from "src/file-upload/schemas/file-metadata.schema";
import { Industry } from "src/industry/schemas/industry.schema";
import { JobLocation } from "src/job-location/schemas/job-location.schema";
import { Org } from "src/org/schemas/org.schema";
import { RateCard } from "src/rate-card/schemas/rate-card.schema";
import { Currency, EmploymentType, HiringMode, JobType, WorkMode } from "src/shared/constants";
import { BasicUser } from "src/user/schemas/basic-user.schema";
import { Workflow } from "src/workflow/schemas/workflow.schema";
import { Schema as MongooseSchema } from "mongoose";


export type JobDocument = HydratedDocument<Job>;
@Schema({
  timestamps: true
})
export class Job {

  @Prop({
    type: String,
    required: true,
    trim: true,
  })
  title: string;

  // TBD:: internal , external 
  // is this mandatory??
  //internal and external here we can say internal is for the companies requirement
  // and external is other companies requirement  we are going to fill
  @Prop({
    type: String,
    required: false,
    trim: true,
    // default: JobType.Internal,
    enum: Object.values(JobType),
  })
  jobType?: string;


  //TBD:: department is a select, list
  //should be a separate schema or simply an enum ??
  // is this mandatory??
  //by following workable it should be seperate schema

  // Department should be a mongodb id - should be configured in client settings
  @Prop({
    required: false,
    type: Types.ObjectId, ref: 'BusinessUnit'
  })
  department?: BusinessUnit;

  @Prop({
    type: Number,
    required: false,
    default: 1
  })
  noOfVacancies?: number;


  // this is an url, which is saved, after uploading a file to some third party file storage service, local storage.
  @Prop({
    type: Types.ObjectId,
    required: false,
  })
  jdUrl?: FileMetadata;


  // TBD:: is this mandatory?
  // this should be a list of values
  // an enum or a separate schema - TBD
  // what is the default value? //list Full-time,Part-time,Contract,Temporary,Other
  @Prop({
    type: String,
    required: false,
    trim: true,
    //default: EmploymentType.FullTime,
    enum: Object.values(EmploymentType),
  })
  employmentType?: string;

  // TBD:: is this mandatory?
  // free text in the ui design layout
  // what is the default value? //work mode can be enum like remote, hybrid, wfo
  @Prop({
    type: String,
    required: false,
    trim: true,
    // default: WorkMode.OnSite,
    enum: Object.values(WorkMode),
  })
  workMode?: string;

  @Prop({
    type: String,
    required: false,
    trim: true,
    // default: '<h1>Job Description</h1>'
  })
  description?: string;

  @Prop({
    type: [String],
    default: [],
    required: false,
  })
  primarySkills?: string[];

  @Prop({
    type: [String],
    default: [],
    required: false,
  })
  secondarySkills?: string[];

  @Prop({
    type: Object,
    required: false,
  })
  workExperience?: {
    minimum?: number;
    maximum?: number;
  };

  @Prop({
    required: false,
    type: [Types.ObjectId], ref: 'JobLocation'
  })
  jobLocation?: JobLocation[];


  @Prop({
    type: Types.ObjectId,
    ref: 'Industry',
    required: false
  })
  industryOrDomain?: Industry;

  @Prop({
    type: [String],
    default: [],
    required: false,
  })
  educationalQualification?: string[];

  @Prop({
    type: Boolean,
    required: false,
    default: false
  })
  videoProfileRequested?: boolean;


  // TBD:: is this mandatory?
  // this should be a list of values
  // an enum or a separate schema - TBD
  // what is the default value?
  // 1. walk-in
  // 2. scheduled-interview
  // 3. drive

  @Prop({
    type: String,
    required: false,
    trim: true,
    // default: HiringMode.Walk_In,
    enum: Object.values(HiringMode),
    // enum: ['scheduled-interview', 'walk-in', 'drive', 'on-campus', 'off-campus'],
  })
  hiringMode?: string;


  // this is an url, which is saved, after uploading a file to some third party file storage service, local storage.
  @Prop({
    type: String,
    required: false,
    trim: true,
  })
  instructionsToRecruiter?: string;

  // this is an url, which is saved, after uploading a file to some third party file storage service, local storage.
  @Prop({
    type: String,
    required: false,
    trim: true,
  })
  instructionFile?: string;

  @Prop({
    type: [Object],
    required: false,
  })
  questionAnswers?: {
    question?: string;
    answer?: string;
  }[];

  @Prop({
    required: false,
    type: Boolean,
    default: false
  })
  shareOnSocialMedia?: boolean;

  @Prop({
    type: Boolean,
    required: false,
    default: false
  })
  shareWithVendors?: boolean; // 

  @Prop({
    type: Boolean,
    required: false,
    default: false
  })
  shareWithFreelancers?: boolean; // 

  @Prop({
    required: false,
    type: [Types.ObjectId], ref: 'SocialMediaLink'
  })
  socialMediaLinks?: SocialMediaLink[];

  // TBD:: 
  // When a job is created and shared with selected vendors, getByJobId r getJobList should consider this condition. This i specific to employee of particular org
  @Prop({ type: [{ type: Types.ObjectId, ref: 'Org' }] })
  vendors: Types.ObjectId[];


  @Prop({
    required: false,
    type: Boolean,
    default: false
  })
  isDeleted?: boolean;


  @Prop({
    required: true,
    type: Types.ObjectId, ref: 'BasicUser'
  })
  createdBy: BasicUser;

  //The organization which posted the job
  @Prop({
    required: true,
    type: Types.ObjectId, ref: 'Org'
  })
  postingOrg: Org;

  @Prop({
    required: false,
    type: Types.ObjectId,
    ref: 'Org'
  })
  hiringOrg?: Org;

  //hiring org - spoc
  @Prop({
    required: false,
    type: Types.ObjectId,
    ref: 'Contact'
  })
  spoc?: Contact;

  // Organization for which the job is posted
  //Customer organization
  @Prop({
    required: false,
    type: Types.ObjectId,
    ref: 'Org'
  })
  endClientOrg?: Org;

  @Prop({
    required: false,
  })
  jobCode?: string;

  @Prop({
    required: false,
    type: Boolean,
    default: true
  })
  isOpen?: boolean;

  @Prop({
    required: false,
    type: Boolean,
    default: false
  })
  isDraft?: boolean;

  @Prop({
    type: Number,
    required: false,
    min: 0,
    default: 0,
  })
  maxCtcOffered?: number;

  @Prop({
    type: String,
    required: false,
    trim: true,
    // default: Currency.INR,
    enum: Object.values(Currency),
  })
  currency?: string;

  @Prop({
    required: true,
    type: Types.ObjectId, ref: 'Workflow'
  })
  workflow: Workflow;

  @Prop({
    type: Number,
    required: false,
  })
  targetApplications?: number;

  @Prop({
    required: false,
    type: Boolean,
    default: true
  })
  isClientvisible?: boolean;

  @Prop({
    type: String,
    required: false,
    trim: true,
  })
  endClientName: string;

  @Prop({
    required: false,
    type: Types.ObjectId, ref: 'RateCard'
  })
  rateCard?: RateCard;
  @Prop({
    type: MongooseSchema.Types.Mixed, // Allows flexibility for dynamic key-value pairs
    required: false,
    default: {},
  })
  dynamicFields?: Record<string, any>; // or Record<string, string | number | boolean | null>;

  @Prop({
    type: [
      {
        _id: false,
        vendor: { type: Types.ObjectId, ref: 'Org', required: true },
        rateCard: { type: Types.ObjectId, ref: 'RateCard', required: true },
      },
    ],
    required: false,
    default: [],
  })
  vendorRateCards?: {
    vendor: Types.ObjectId;
    rateCard: Types.ObjectId;
  }[];

  @Prop({
    type: Boolean,
    required: false,
    default: false
  })
  requiredBgv?: boolean;

}

export const JobSchema = SchemaFactory.createForClass(Job);





