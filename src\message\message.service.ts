import { ForbiddenException, HttpException, HttpStatus, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { CreateMessageDto } from './dto/create-message.dto';
import { UpdateMessageDto } from './dto/update-message.dto';
import { InjectModel } from '@nestjs/mongoose';
import { Message, MessageDocument } from './schemas/message.schema';
import { Model, Types } from 'mongoose';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';

@Injectable()
export class MessageService {

  private readonly logger = new Logger(MessageService.name);

  constructor(@InjectModel(Message.name) private messageModel: Model<Message>, private eventEmitter: EventEmitter2) {

  }

  // Utility function to find a message by Id

  async findMessageById(id: Types.ObjectId) {
    const message = await this.messageModel.findById(id)
      .populate({ path: 'sender', select: '_id roles firstName lastName email', model: 'BasicUser' })
      .populate({ path: 'recipients', select: '_id roles firstName lastName email', model: 'BasicUser' })
      .populate({ path: 'attachments', select: '_id originalName fileSize fileType locationUrl', model: 'FileMetadata' })
      .exec();
    if (!message || message.isDeleted) {
      throw new NotFoundException(`Message not found with ID ${id}`);
    }
    return message;
  }

  //TODO: Add event emitter logic for notifications
  async create(createMessageDto: CreateMessageDto): Promise<MessageDocument> {
    try {
      const { sender, recipients } = createMessageDto;
      const members = [sender, ...recipients];

      // Initialize readStatus with false for each recipient
      // const readStatus: Record<string, boolean> = recipients.reduce((status, recipient) => {
      //   status[recipient.toString()] = false;
      //   return status;
      // }, {} as Record<string, boolean>);

      const createdMessage = new this.messageModel({
        ...createMessageDto,
        members,
        // readStatus,
      });

      await createdMessage.save();
      this.emitEvent('message.created', createdMessage);
      return createdMessage;

    } catch (error) {
      this.logger.error('Error creating message', error);
      throw new HttpException('Error creating message', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async star(messageId: Types.ObjectId, userId: Types.ObjectId) {

    const message = await this.findMessageById(messageId);

    if (!message.members.includes(userId)) {
      throw new ForbiddenException('User does not have permission to star this message');
    }

    try {
      const message = await this.messageModel.findByIdAndUpdate(
        messageId,
        { $addToSet: { starredBy: userId } },
        { new: true }
      ).exec();

      if (!message) {
        throw new Error('Message not found');
      }
      return message;
    } catch (error) {
      this.logger.error(`Error starring message: ${error.message}`);
      throw new Error('Error starring message: ' + error.message);
    }
  }

  //Messages for currently loggedin user -  Sent
  async findAllSentByUser(userId: Types.ObjectId, page: number, limit: number): Promise<MessageDocument[]> {
    try {
      this.logger.debug('Finding messages for user:', userId)
      return await this.messageModel.find({ sender: userId, members: { $in: [userId] } })
        .populate({ path: 'sender', select: '_id roles firstName lastName email', model: 'BasicUser' })
        .populate({ path: 'recipients', select: '_id roles firstName lastName email', model: 'BasicUser' })
        .populate({ path: 'attachments', select: '_id originalName fileSize fileType locationUrl', model: 'FileMetadata' })
        .sort({ updatedAt: -1 })
        .skip((page - 1) * limit)
        .limit(limit)
        .exec();
    } catch (error) {
      throw new Error('Error fetching messages: ' + error.message);
    }
  }

  //Messages for currently loggedin user -  Inbox
  async findAllReceivedByUser(userId: Types.ObjectId, page: number, limit: number) {
    try {
      return await this.messageModel.find({ recipients: { $in: [userId] }, members: { $in: [userId] } })
        .populate({ path: 'sender', select: '_id roles firstName lastName email', model: 'BasicUser' })
        .populate({ path: 'recipients', select: '_id roles firstName lastName email', model: 'BasicUser' })
        .populate({ path: 'attachments', select: '_id originalName fileSize fileType locationUrl', model: 'FileMetadata' })
        .sort({ updatedAt: -1 })
        .skip((page - 1) * limit)
        .limit(limit)
        .exec();
    } catch (error) {
      throw new Error('Error fetching messages: ' + error.message);
    }
  }

  async findAllStarredMessages(userId: Types.ObjectId, page: number, limit: number) {
    try {
      return await this.messageModel.find({ starredBy: { $in: [userId] }, members: { $in: [userId] } })
        .populate({ path: 'sender', select: '_id roles firstName lastName email', model: 'BasicUser' })
        .populate({ path: 'recipients', select: '_id roles firstName  lastName email', model: 'BasicUser' })
        .populate({ path: 'starredBy', select: '_id roles firstName lastName email', model: 'BasicUser' })
        .populate({ path: 'attachments', select: '_id originalName fileSize fileType locationUrl', model: 'FileMetadata' })
        .sort({ updatedAt: -1 })
        .skip((page - 1) * limit)
        .limit(limit)
        .exec();
    } catch (error) {
      throw new Error('Error fetching messages: ' + error.message);
    }
  }

  async findAllSoftDeletedMessages(userId: Types.ObjectId, page: number, limit: number) {
    try {
      return await this.messageModel.find({ deletedBy: { $in: [userId] } })
        .populate({ path: 'sender', select: '_id roles firstName lastName email', model: 'BasicUser' })
        .populate({ path: 'recipients', select: '_id roles firstName lastName email', model: 'BasicUser' })
        .populate({ path: 'attachments', select: '_id originalName fileSize fileType locationUrl', model: 'FileMetadata' })
        .sort({ updatedAt: -1 })
        .skip((page - 1) * limit)
        .limit(limit)
        .exec();
    } catch (error) {
      throw new Error('Error fetching messages: ' + error.message);
    }
  }

  async findOne(id: Types.ObjectId, userId: Types.ObjectId): Promise<MessageDocument> {
    try {
      const message = await this.findMessageById(id);
      // console.log("message",message)
      // if (!message.members.includes(userId)) {
      //   throw new ForbiddenException('User does not have permission to get this message');
      // }
      return message;
    } catch (error) {
      this.logger.error(error);
      this.logger.error(`An error occurred in fetching message by Id ${id}. ${error?.message}`);
      throw error;
    }
  }
  //TODO: figure out the restrictions 
  async searchMessages(userId: Types.ObjectId, query: string, page: number, limit: number): Promise<MessageDocument[]> {
    const searchRegex = new RegExp(query, 'i');
    return this.messageModel.find({
      $and: [
        { isDeleted: false },
        {
          $or: [
            { subject: searchRegex },
            { content: searchRegex },
            { 'sender.firstName': searchRegex }
          ]
        },
        {
          $or: [
            { sender: userId },
            { recipients: userId }
          ]
        }
      ]
    })
      .populate('sender', '_id roles firstName email')
      .sort({ updatedAt: -1 })
      .skip((page - 1) * limit)
      .limit(limit)
      .exec();
  }

  async markAsRead(messageId: Types.ObjectId, recipientId: Types.ObjectId) {
    return this.messageModel.findByIdAndUpdate(
      messageId,
      {
        $set: { [`readStatus.${recipientId}`]: true },
        updatedAt: new Date(),
      },
      { new: true }
    );
  }

  async markAsUnread(messageId: Types.ObjectId, recipientId: Types.ObjectId) {
    return this.messageModel.findByIdAndUpdate(
      messageId,
      {
        $set: { [`readStatus.${recipientId}`]: false },
        updatedAt: new Date(),
      },
      { new: true }
    );
  }

  // async update(messageId: Types.ObjectId, updateMessageDto: UpdateMessageDto, userId: Types.ObjectId) {
  //   try {
  //     const message = await this.messageModel.findOne({ _id: messageId, sender: userId }).exec();

  //     if (!message) {
  //       throw new NotFoundException(`Message not found with ID ${messageId}`);
  //     }

  //     if (!message.members.includes(userId)) {
  //       throw new ForbiddenException('User does not have permission to update this message');
  //     }
  //     return await this.messageModel.findByIdAndUpdate(messageId, updateMessageDto, { new: true });
  //   } catch (error) {
  //     this.logger.error(error);
  //     this.logger.error(`An error occurred in fetching message by Id ${messageId}. ${error?.message}`);
  //     throw error;
  //   }
  // }


  async restoreSoftDeletedmessages(id: Types.ObjectId) {
    const message = await this.messageModel.findById(id);
    if (!message || !message.isDeleted) {
      throw new NotFoundException(`Message not found with ID ${id}`);
    }
    message.isDeleted = false;
    message.members.push(id);
    await message.save();
    return message;
  }

  async deleteForAll(messageId: Types.ObjectId, userId: Types.ObjectId) {
    try {

      const message = await this.messageModel.findOne({ _id: messageId, sender: userId }).exec();

      if (!message) {
        throw new NotFoundException(`Message not found with ID ${messageId}`);
      }
      message.deletedBy?.push(userId);
      message.members = [];
      message.isDeleted = true;
      await message.save();
      return message;
    } catch (error) {
      this.logger.error(error);
      this.logger.error(`An error occurred in fetching message by Id ${messageId}. ${error?.message}`);
      throw error;
    }
  }

  async deleteForMe(messageId: Types.ObjectId, userId: Types.ObjectId) {

    try {
      const message = await this.findMessageById(messageId);
      if (!message.members.includes(userId)) {
        throw new ForbiddenException('User does not have permission to delete this message');
      }

      const index = message.members.indexOf(userId);
      if (index !== -1) {
        message.members.splice(index, 1);
      }

      message.deletedBy?.push(userId);
      if (message.members.length === 0) {
        message.isDeleted = true;
      }
      await message.save();
      return message;
    } catch (error) {
      this.logger.error(error);
      this.logger.error(`An error occurred in fetching message by Id ${messageId}. ${error?.message}`);
      throw error;
    }

  }

  async unstarMessage(messageId: Types.ObjectId, userId: Types.ObjectId): Promise<MessageDocument> {

    const message = await this.findMessageById(messageId);

    if (!message.members.includes(userId)) {
      throw new ForbiddenException('User does not have permission to unstar this message');
    }

    try {
      const message = await this.messageModel.findByIdAndUpdate(
        messageId,
        { $pull: { starredBy: userId } },
        { new: true }
      ).exec();

      if (!message) {
        throw new Error('Message not found');
      }

      return message;
    } catch (error) {
      this.logger.error(`Error unstarring message: ${error.message}`);
      throw new Error('Error unstarring message: ' + error.message);
    }
  }

  async deleteForUser(messageId: Types.ObjectId, recipientId: Types.ObjectId, senderId: Types.ObjectId) {
    try {

      if (recipientId.toString() === senderId.toString()) {
        throw new NotFoundException(`Recipient not found with ID ${recipientId}`);
      }

      const message = await this.messageModel.findOne({ _id: messageId, sender: senderId }).exec();

      if (!message) {
        throw new NotFoundException(`Message not found with ID ${messageId}`);
      }

      if (!message.members.includes(senderId)) {
        throw new ForbiddenException('Currently Logged in user does not have permission to delete this message');
      }

      if (!message.members.includes(recipientId)) {
        throw new NotFoundException(`Recipient not found with ID ${recipientId}`);
      }

      const index = message.members.indexOf(recipientId);

      message.members.splice(index, 1);
      await message.save();
      return message;
    } catch (error) {
      this.logger.error(error);
      this.logger.error(`An error occurred in fetching message by Id ${messageId}. ${error?.message}`);
      throw error;
    }
  }

  async getMessageCounts(userId: Types.ObjectId): Promise<any> {
    try {
      // Count of sent messages
      const sentCount = await this.messageModel.countDocuments({
        sender: userId,
        members: { $in: [userId] }
      }).exec();

      // Count of received messages (inbox)
      const inboxCount = await this.messageModel.countDocuments({
        recipients: { $in: [userId] },
        members: { $in: [userId] }
      }).exec();

      // Count of starred messages
      const starredCount = await this.messageModel.countDocuments({
        starredBy: { $in: [userId] },
        members: { $in: [userId] }
      }).exec();

      // Count of soft-deleted (trash) messages
      const softDeletedCount = await this.messageModel.countDocuments({
        deletedBy: { $in: [userId] },
      }).exec();

      return {
        sent: sentCount,
        inbox: inboxCount,
        starred: starredCount,
        trash: softDeletedCount,
      };
    } catch (error) {
      throw new Error('Error fetching message counts: ' + error.message);
    }
  }

  emitEvent(eventName: string, payload: any) {
    // payload['event']= eventName;
    this.eventEmitter.emit(eventName, payload);
  }


  @OnEvent('message.created', { async: true })
  async onMessageCreatedEvent(payload: any) {
    //TODO Add logic for notifications
    this.logger.debug('Message created');
  }

}
