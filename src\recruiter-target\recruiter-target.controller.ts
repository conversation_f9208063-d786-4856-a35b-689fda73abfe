import { Controller, Get, Post, Body, Patch, Param, Delete, Req, UseGuards, Query } from '@nestjs/common';
import { RecruiterTargetService } from './recruiter-target.service';
import { CreateRecruiterTargetDto } from './dto/create-recruiter-target.dto';
import { UpdateRecruiterTargetDto } from './dto/update-recruiter-target.dto';
import { ApiBearerAuth, ApiQuery, ApiTags } from '@nestjs/swagger';
import { AuthJwtGuard } from 'src/auth/guards/auth-jwt/auth-jwt.guard';


@Controller('')
@ApiTags('recruiter-target')
export class RecruiterTargetController {
  constructor(private readonly recruiterTargetService: RecruiterTargetService) {}

  @Post()
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  async create(@Req() req: any, @Body() createRecruiterTargetDto: CreateRecruiterTargetDto) {
    createRecruiterTargetDto.createdBy = req.user._id
    createRecruiterTargetDto.org = req.user.org._id
      return await this.recruiterTargetService.create(createRecruiterTargetDto);
    }

    @Get()
    @ApiQuery({ name: 'recruiterId', required: false, type: String, description: 'Id of the Job.' })
    @ApiBearerAuth()
    @UseGuards(AuthJwtGuard)
    async findAll(@Req() req: any,@Query('recruiterId') recruiterId?: string) {
      // You can access user and org directly from req.user since AuthJwtGuard is applied
      const userId = req.user._id;
      const orgId = req.user.org._id;
      
      return this.recruiterTargetService.findAll({ createdBy: userId, org: orgId,recruiterId });
    }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.recruiterTargetService.findOne(id);
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() updateRecruiterTargetDto: UpdateRecruiterTargetDto) {
    return this.recruiterTargetService.update(id, updateRecruiterTargetDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.recruiterTargetService.remove(id);
  }
}
