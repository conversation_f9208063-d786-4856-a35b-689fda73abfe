import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { InjectModel } from '@nestjs/mongoose';
import moment from 'moment';
import { Model, Types } from 'mongoose';
import { Role } from 'src/auth/enums/role.enum';
import { EmailTemplate } from 'src/email-template-builder/schemas/email-template-builder.schema';
import { Interview } from 'src/interview/schemas/interview.schema';
import { JobAllocationBase } from 'src/job-allocation/schemas/job-allocation-base.schema';
import { Job } from 'src/job/schemas/job.schema';
import { NotificationsService } from 'src/notification/notifications.service';
import { Offer } from 'src/offer/schemas/offer.schema';
import { Org, Placeholder } from 'src/org/schemas/org.schema';
import { RecruiterTarget } from 'src/recruiter-target/schemas/recuriter-target.schema';
import {
  EmailTemplateEvent,
  GroupByType,
  NotificationType,
  OrgType,
  Source,
} from 'src/shared/constants';
import { Stage } from 'src/stage/schemas/stage.schema';
import { BasicUser } from 'src/user/schemas/basic-user.schema';
import { validateObjectId } from 'src/utils/validation.utils';
import { Workflow } from 'src/workflow/schemas/workflow.schema';
import { WorkflowService } from 'src/workflow/workflow.service';
import { CreateJobApplicationFormDto } from './dto/create-job-application-form.dto';
import { CreatePublicJobApplicationFormDto } from './dto/create-public-job-application-dto';
import { CustomFilterDto } from './dto/customFilterDto.dto';
import { DynamicFieldDto } from './dto/dynamic-field.dto';
import { QueryJobApplicationDto } from './dto/query-job-application.dto';
import { UpdateAiInterviewDto } from './dto/update-ai-interview.dto';
import { UpdateDynamicFieldDto } from './dto/update-dynamic-field.dto';
import { UpdateJobApplicationFormDto } from './dto/update-job-application-form.dto';
import { UpdateJobApplicationStatusDto } from './dto/update-job-application-status.dto';
import {
  AiInterview,
  AiInterviewStatus,
  DynamicField,
  JobApplication,
} from './schemas/job-application.schema';

@Injectable()
export class JobApplicationFormService {
  private readonly logger = new Logger(JobApplicationFormService.name);

  constructor(
    private configService: ConfigService,
    @InjectModel(JobApplication.name)
    private jobApplicationModel: Model<JobApplication>,
    @InjectModel(JobAllocationBase.name)
    private jobAllocationModel: Model<JobAllocationBase>,
    @InjectModel(Stage.name) private stageModel: Model<Stage>,
    @InjectModel(Workflow.name) private workflowModel: Model<Workflow>,
    @InjectModel(DynamicField.name)
    private readonly dynamicFieldModel: Model<DynamicField>,
    @InjectModel(EmailTemplate.name)
    private emailTemplateService: Model<EmailTemplate>,
    @InjectModel(Placeholder.name)
    private placeholderService: Model<Placeholder>,
    private eventEmitter: EventEmitter2,
    private readonly workflowService: WorkflowService,
    @InjectModel(Job.name) private jobModel: Model<Job>,
    @InjectModel(Offer.name) private offerModel: Model<Offer>,
    @InjectModel(Interview.name) private interviewModel: Model<Interview>,
    @InjectModel(RecruiterTarget.name)
    private recruiterTargetModel: Model<RecruiterTarget>,
    @InjectModel(BasicUser.name) private basicUserModel: Model<BasicUser>,
    @InjectModel(Org.name) private orgModel: Model<Org>,
    private readonly notificationsService: NotificationsService,
  ) {}

  async updateAiInterview(
    jobApplicationId: Types.ObjectId,
    updateAiInterviewDto: UpdateAiInterviewDto,
  ) {
    try {
      const jobApplication =
        await this.jobApplicationModel.findById(jobApplicationId);
      if (!jobApplication) {
        throw new NotFoundException(
          `Job application not found with ID ${jobApplicationId}`,
        );
      }

      // Only update the other fields if they are not undefined or null
      const aiInterviewData: AiInterview = jobApplication.aiInterview || {
        aiInterviewStatus: AiInterviewStatus.PENDING,
      };
      if (
        updateAiInterviewDto.aiInterviewStatus !== undefined &&
        updateAiInterviewDto.aiInterviewStatus !== null
      ) {
        aiInterviewData.aiInterviewStatus =
          updateAiInterviewDto.aiInterviewStatus;
      }
      if (
        updateAiInterviewDto.aiInterviewScore !== undefined &&
        updateAiInterviewDto.aiInterviewScore !== null
      ) {
        aiInterviewData.aiInterviewScore =
          updateAiInterviewDto.aiInterviewScore;
      }
      if (
        updateAiInterviewDto.aiInterviewDuration !== undefined &&
        updateAiInterviewDto.aiInterviewDuration !== null
      ) {
        aiInterviewData.aiInterviewDuration =
          updateAiInterviewDto.aiInterviewDuration;
      }
      if (
        updateAiInterviewDto.aiInterviewDate !== undefined &&
        updateAiInterviewDto.aiInterviewDate !== null
      ) {
        aiInterviewData.aiInterviewDate = new Date(
          updateAiInterviewDto.aiInterviewDate,
        );
      }

      jobApplication.aiInterview = aiInterviewData;

      const populateOptions = this.getJobApplicationPopulateOptions();
      const updated = await jobApplication.save();

      // Return populated result
      return this.jobApplicationModel
        .findById(updated._id)
        .populate(populateOptions)
        .exec();
    } catch (error) {
      this.logger.error(
        `Failed to update AI interview details: ${error.message}`,
      );
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException(
        `Failed to update AI interview details: ${error.message}`,
      );
    }
  }

  getJobApplicationPopulateOptions(): any[] {
    const populateOptions = [
      {
        path: 'createdBy',
        select: '_id roles firstName roles',
        model: 'BasicUser',
      },
      {
        path: 'jobId',
        select: '_id title employmentType postingOrg hiringOrg requiredBgv',
        model: 'Job',
        populate: [
          { path: 'postingOrg', select: '_id title orgType' },
          { path: 'endClientOrg', select: '_id title orgType' },
        ],
      },
      {
        path: 'resumeMetadata',
        select: '_id originalName fileSize fileType locationUrl',
        model: 'FileMetadata',
      },
      {
        path: 'coverLetterMetadata',
        select: '_id originalName fileSize fileType locationUrl',
        model: 'FileMetadata',
      },
      {
        path: 'workflow',
        select: '_id stages',
        populate: [
          { path: 'stages', select: '_id name type jobApplicationsCount' },
        ],
      },
      {
        path: 'workExperience',
        select:
          '_id jobTitle companyName jobStartDate jobEndDate currentlyWorking',
      },
      {
        path: 'educationQualification',
        select: '_id courseName university startDate endDate',
      },
      {
        path: 'evaluationForm',
        select: '_id skill years months rating isPrimary',
      },
      {
        path: 'reLocation',
        select: '_id city state country postalCode',
        model: 'JobLocation',
      },
      {
        path: 'stage',
        select: '_id name type jobApplicationsCount',
        model: 'Stage',
      },
    ];

    if (this.jobApplicationModel.schema.paths['country']) {
      populateOptions.push({
        path: 'country',
        select:
          '_id countryName countryPhoneCode currencyCode isDeleted createdAt updatedAt __v',
        model: 'Country',
      });
    }

    if (this.jobApplicationModel.schema.paths['state']) {
      populateOptions.push({
        path: 'state',
        select: '_id stateName country isDeleted createdAt updatedAt __v',
        model: 'State',
      });
    }

    if (this.jobApplicationModel.schema.paths['city']) {
      populateOptions.push({
        path: 'city',
        select: '_id name',
        model: 'City',
      });
    }

    if (this.jobApplicationModel.schema.paths['customStatus']) {
      populateOptions.push({
        path: 'customStatus',
        select: '_id name',
        model: 'Status',
      });
    }

    if (this.jobApplicationModel.schema.paths['logo']) {
      populateOptions.push({
        path: 'logo',
        select: '_id locationUrl originalName fileSize fileType',
        model: 'FileMetadata',
      });
    }

    if (this.jobApplicationModel.schema.paths['thumbnail']) {
      populateOptions.push({
        path: 'thumbnail',
        select: '_id locationUrl originalName fileSize fileType',
        model: 'FileMetadata',
      });
    }

    return populateOptions;
  }

  async create(
    user: any,
    createJobApplicationFormDto: CreateJobApplicationFormDto,
  ) {
    try {
      const { firstName, lastName, contactDetails, jobId } =
        createJobApplicationFormDto;

      let contactEmail: string | undefined;
      let contactNumber: string | undefined;

      if (contactDetails) {
        contactEmail = contactDetails.contactEmail;
        contactNumber = contactDetails.contactNumber;
      }

      const job = await this.jobModel.findById(jobId).exec();
      if (!job || !job.postingOrg) {
        throw new BadRequestException(
          'Invalid job ID or job does not belong to an organization.',
        );
      }

      // Assign the posting organization ID
      createJobApplicationFormDto.postingOrg = job.postingOrg.toString();

      // Add jdMatchingScore with random value between 60 and 100
      createJobApplicationFormDto.jdMatchingScore =
        Math.floor(Math.random() * 41) + 60;

      // Fetch the company settings to get the duplicate check duration
      const jobOrg = await this.orgModel.findById(job.postingOrg).exec();
      const duplicateCheckDuration = jobOrg?.duplicateCheckDuration || 90; // Default to 90 days if not set

      // Calculate the date range for checking duplicates
      const duplicateCheckStartDate = new Date();
      duplicateCheckStartDate.setDate(
        duplicateCheckStartDate.getDate() - duplicateCheckDuration,
      );

      // Check if a duplicate application exists in the same organization within the set duration
      const existingApplication = await this.jobApplicationModel.findOne({
        firstName,
        lastName,
        ...(contactEmail && { 'contactDetails.contactEmail': contactEmail }),
        ...(contactNumber && { 'contactDetails.contactNumber': contactNumber }),
        createdAt: { $gte: duplicateCheckStartDate },
        postingOrg: job.postingOrg,
      });

      // this.logger.log(JSON.stringify(existingApplication))
      if (existingApplication) {
        throw new BadRequestException(
          'A job application with the same details already exists.',
        );
      }

      const createdJobApplicationForm = new this.jobApplicationModel(
        createJobApplicationFormDto,
      );

      // Fetch the workflow for the job
      const workflow = await this.workflowModel
        .findById(createJobApplicationFormDto.workflow)
        .populate('stages')
        .exec();

      if (!workflow || !workflow.stages || workflow.stages.length === 0) {
        throw new InternalServerErrorException('Workflow or stages not found.');
      }

      // Assign the first stage as the default stage
      const firstStageId = workflow.stages[0]._id;
      createdJobApplicationForm.stage = firstStageId;

      // Save the job application
      const savedJobApplication = await createdJobApplicationForm.save();

      // Increment the jobApplicationsCount for the first stage
      await this.stageModel.findByIdAndUpdate(
        firstStageId,
        { $inc: { jobApplicationsCount: 1 } },
        { new: true },
      );

      const jobApplication = await this.jobApplicationModel
        .findById(savedJobApplication._id)
        .populate({ path: 'jobId', select: '_id title' })
        .populate({ path: 'createdBy', select: '_id roles firstName' })
        .exec();

      const isVendor = user.roles?.includes(Role.Vendor);
      console.log('isVendor', isVendor);
      // console.log("user",user)
      if (jobApplication) {
        if (isVendor) {
          const adminOrguser = await this.basicUserModel.findOne({
            org: new Types.ObjectId(user.companyId._id),
            roles: { $in: [Role.Admin] },
          });
          console.log('adminOrguser', adminOrguser);
          user.userInboxConfig = adminOrguser?.userInboxConfig;
          console.log('user.userInboxConfig', user);
          // console.log("adminOrguser",user)
          this.emitEvent(EmailTemplateEvent.WORKFLOW_SOURCING_PRE_CONSENT, {
            user: user,
            data: jobApplication.toJSON(),
          });
        } else {
          this.emitEvent(EmailTemplateEvent.WORKFLOW_SOURCING_PRE_CONSENT, {
            user: user,
            data: jobApplication.toJSON(),
          });
        }
      }

      return savedJobApplication;
    } catch (error) {
      this.logger.error(`Failed to create Job application. ${error}`);
      throw new InternalServerErrorException(
        `Failed to create Job application. ${error.message}`,
      );
    }
  }

  async findAll() {
    const populateOptions = this.getJobApplicationPopulateOptions();
    return this.jobApplicationModel.find().populate(populateOptions).exec();
  }

  async findAllUser(userId: string, query: QueryJobApplicationDto) {
    try {
      const { fromDate, toDate, name, page = 1, limit = 10 } = query;

      let conditions: any = { createdBy: userId };

      if (name) {
        const regex = new RegExp(name, 'i'); // Case-insensitive search
        conditions.firstName = regex;
      }
      if (fromDate) {
        conditions.createdAt = {
          ...conditions.createdAt,
          $gte: moment(fromDate).startOf('day').toDate(),
        };
      }
      if (toDate) {
        conditions.createdAt = {
          ...conditions.createdAt,
          $lte: moment(toDate).endOf('day').toDate(),
        };
      }

      const populateOptions = this.getJobApplicationPopulateOptions();

      const applications = await this.jobApplicationModel
        .find(conditions)
        .populate(populateOptions)
        .sort({ updatedAt: -1 })
        .skip((page - 1) * limit)
        .limit(limit)
        .exec();

      const totalApplicationsCount =
        await this.jobApplicationModel.countDocuments(conditions);

      return {
        totalApplicationsCount,
        page,
        limit,
        totalPages: Math.ceil(totalApplicationsCount / limit),
        data: applications,
      };
    } catch (error) {
      this.logger.error(error);
      throw new InternalServerErrorException(
        `An error occurred while retrieving applications. ${error?.message}`,
      );
    }
  }

  async findOne(jobApplicationId: Types.ObjectId) {
    try {
      const populateOptions = this.getJobApplicationPopulateOptions();
      const jobApplication = await this.jobApplicationModel
        .findById(jobApplicationId, { isRejected: false })
        .populate(populateOptions)
        .exec();
      if (!jobApplication) {
        throw new NotFoundException(
          `Job application not found with ID ${jobApplicationId}`,
        );
      }
      return jobApplication;
    } catch (error) {
      this.logger.error(error);
      this.logger.error(
        `An error occurred in fetching Job application by Id ${jobApplicationId}. ${error?.message}`,
      );
      throw error;
    }
  }

  async getCountOfJobApplicationsByCreatedBy(
    jobId: string,
    recruiterId: string,
  ): Promise<{ count: number }> {
    if (!jobId) {
      throw new BadRequestException('Job ID must be provided.');
    }

    try {
      const jobApplicationCounts = await this.jobApplicationModel
        .countDocuments({
          jobId: jobId,
          createdBy: recruiterId,
          isRejected: false,
          isDeleted: false,
        })
        .exec();

      return {
        count: jobApplicationCounts,
      };
    } catch (error) {
      console.error(
        'Error while fetching job application count by recruiter:',
        error,
      );
      throw new InternalServerErrorException(
        `Error while fetching job application count by recruiter: ${error.message}`,
      );
    }
  }

  async getCountOfJobApplications(jobId: string): Promise<{ count: number }> {
    if (!jobId) {
      throw new BadRequestException('Job ID must be provided.');
    }

    try {
      const jobApplicationCounts = await this.jobApplicationModel
        .countDocuments({ jobId: jobId, isRejected: false, isDeleted: false })
        .exec();

      return {
        count: jobApplicationCounts,
      };
    } catch (error) {
      console.error('Error while fetching job application count:', error);
      throw new InternalServerErrorException(
        `Error while fetching job application count: ${error.message}`,
      );
    }
  }

  async getValidSubordinatesRecursively(
    managerId: string,
    jobId: string,
    allUsersMap: Map<string, any>,
    allocationMap: Map<string, any[]>,
  ): Promise<Set<string>> {
    const validSubordinateIds = new Set<string>();

    for (const [userId, user] of allUsersMap) {
      if (!user.reportingTo?.includes(managerId)) continue;

      const userAllocations = allocationMap.get(userId) || [];
      const isValid = userAllocations.some(
        (a) =>
          a.job?.toString() === jobId && a.createdBy?.toString() === managerId,
      );

      if (!isValid) continue;

      validSubordinateIds.add(userId);

      const childSubs = await this.getValidSubordinatesRecursively(
        userId,
        jobId,
        allUsersMap,
        allocationMap,
      );
      childSubs.forEach((id) => validSubordinateIds.add(id));
    }

    return validSubordinateIds;
  }

  async findUserApplicationForJob(
    user: any,
    jobId: string,
    createdBy: Types.ObjectId,
  ) {
    try {
      const populateOptions = this.getJobApplicationPopulateOptions();
      const job = await this.jobModel.findById(jobId).exec();
      if (!job) {
        throw new NotFoundException(`Job not found with ID ${jobId}`);
      }
      if (job) {
        // If the job was created by the user, fetch all applications for that job
        if (
          user.roles.includes(Role.Admin) &&
          user.roles.includes(Role.Vendor)
        ) {
          if (job.postingOrg.toString() === user.companyId._id.toString()) {
            const jobApplication = await this.jobApplicationModel
              .find({
                jobId: jobId,
                isDeleted: false,
                isRejected: false,
              })
              .populate({
                path: 'resumeMetadata',
                select: '_id originalName fileSize fileType locationUrl',
                model: 'FileMetadata',
              })
              .populate({
                path: 'coverLetterMetadata',
                select: '_id originalName fileSize fileType locationUrl',
                model: 'FileMetadata',
              })
              .populate({ path: 'jobId', select: '_id title' })
              .populate({ path: 'createdBy', select: '_id roles firstName' })
              .populate({
                path: 'workExperience',
                select:
                  '_id jobTitle companyName jobStartDate jobEndDate currentlyWorking',
              })
              .populate({
                path: 'educationQualification',
                select: '_id courseName  university startDate endDate',
              })
              .populate({
                path: 'evaluationForm',
                select: '_id skill  years months rating isPrimary',
              })
              .populate({
                path: 'reLocation',
                select: '_id city state country postalCode',
                model: 'JobLocation',
              })
              .populate({
                path: 'workflow',
                populate: [
                  {
                    path: 'stages',
                    select: '_id name type jobApplicationsCount',
                  },
                  // {
                  //   path: 'org',
                  //   select: '_id title legalName'
                  // }
                ],
                select: '_id stages',
              })
              .exec();
            return jobApplication;
          }
          const existingOrgs = await this.orgModel
            .find({
              'contactDetails.contactEmail': user.email,
              isDeleted: false,
              orgType: OrgType.VENDOR_ORG,
            })
            .exec();

          // Step 2: Extract org _ids
          const vendorOrgIds = existingOrgs.map((org) => org._id.toString());
          console.log('vendorOrgIds', vendorOrgIds);
          // Step 3: Prepare allocation query
          const queryConditions: any = {
            kind: 'JobAllocationToVendors',
            job: jobId,
            isDeleted: false,
            vendor: { $in: vendorOrgIds }, // Match any of the vendor orgs
          };
          // Step 4: Query allocations
          const jobAllocations = await this.jobAllocationModel
            .find(queryConditions)
            .exec();
          if (jobAllocations && jobAllocations.length > 0) {
            // Fetch the job application based on jobId and createdBy
            const jobApplication = await this.jobApplicationModel
              .find({
                jobId: jobId,
                createdBy: user._id.toString(),
                isDeleted: false,
                isRejected: false,
              })
              .populate({
                path: 'resumeMetadata',
                select: '_id originalName fileSize fileType locationUrl',
                model: 'FileMetadata',
              })
              .populate({
                path: 'coverLetterMetadata',
                select: '_id originalName fileSize fileType locationUrl',
                model: 'FileMetadata',
              })
              .populate({ path: 'jobId', select: '_id title' })
              .populate({ path: 'createdBy', select: '_id roles firstName' })
              .populate({
                path: 'workExperience',
                select:
                  '_id jobTitle companyName jobStartDate jobEndDate currentlyWorking',
              })
              .populate({
                path: 'educationQualification',
                select: '_id courseName  university startDate endDate',
              })
              .populate({
                path: 'evaluationForm',
                select: '_id skill  years months rating isPrimary',
              })
              .populate({
                path: 'reLocation',
                select: '_id city state country postalCode',
                model: 'JobLocation',
              })
              .populate({
                path: 'workflow',
                populate: [
                  {
                    path: 'stages',
                    select: '_id name type jobApplicationsCount',
                  },
                  // {
                  //   path: 'org',
                  //   select: '_id title legalName'
                  // }
                ],
                select: '_id stages',
              })
              .exec();

            return jobApplication;
          }
        }
        if (
          user.roles?.includes(Role.Admin) ||
          user.roles?.includes(Role.DeliveryManager) ||
          user.roles?.includes(Role.AccountManager) ||
          job.createdBy.toString() === user._id.toString()
        ) {
          const jobApplication = await this.jobApplicationModel
            .find({
              jobId: jobId,
              isDeleted: false,
              isRejected: false,
            })
            .populate({
              path: 'resumeMetadata',
              select: '_id originalName fileSize fileType locationUrl',
              model: 'FileMetadata',
            })
            .populate({
              path: 'coverLetterMetadata',
              select: '_id originalName fileSize fileType locationUrl',
              model: 'FileMetadata',
            })
            .populate({ path: 'jobId', select: '_id title' })
            .populate({ path: 'createdBy', select: '_id roles firstName' })
            .populate({
              path: 'workExperience',
              select:
                '_id jobTitle companyName jobStartDate jobEndDate currentlyWorking',
            })
            .populate({
              path: 'educationQualification',
              select: '_id courseName  university startDate endDate',
            })
            .populate({
              path: 'evaluationForm',
              select: '_id skill  years months rating isPrimary',
            })
            .populate({
              path: 'reLocation',
              select: '_id city state country postalCode',
              model: 'JobLocation',
            })
            .populate({
              path: 'workflow',
              populate: [
                {
                  path: 'stages',
                  select: '_id name type jobApplicationsCount',
                },
                // {
                //   path: 'org',
                //   select: '_id title legalName'
                // }
              ],
              select: '_id stages',
            })
            .exec();
          return jobApplication;
        }
        if (
          user.roles?.includes(Role.Freelancer) ||
          user.roles?.includes(Role.JobSeeker) ||
          user.roles?.includes(Role.Vendor)
        ) {
          // Fetch the job application based on jobId and createdBy
          const jobApplication = await this.jobApplicationModel
            .find({
              jobId: jobId,
              createdBy: user._id.toString(),
              isDeleted: false,
              isRejected: false,
            })
            .populate({
              path: 'resumeMetadata',
              select: '_id originalName fileSize fileType locationUrl',
              model: 'FileMetadata',
            })
            .populate({
              path: 'coverLetterMetadata',
              select: '_id originalName fileSize fileType locationUrl',
              model: 'FileMetadata',
            })
            .populate({ path: 'jobId', select: '_id title' })
            .populate({ path: 'createdBy', select: '_id roles firstName' })
            .populate({
              path: 'workExperience',
              select:
                '_id jobTitle companyName jobStartDate jobEndDate currentlyWorking',
            })
            .populate({
              path: 'educationQualification',
              select: '_id courseName  university startDate endDate',
            })
            .populate({
              path: 'evaluationForm',
              select: '_id skill  years months rating isPrimary',
            })
            .populate({
              path: 'reLocation',
              select: '_id city state country postalCode',
              model: 'JobLocation',
            })
            .populate({
              path: 'workflow',
              populate: [
                {
                  path: 'stages',
                  select: '_id name type jobApplicationsCount',
                },
                // {
                //   path: 'org',
                //   select: '_id title legalName'
                // }
              ],
              select: '_id stages',
            })
            .exec();

          return jobApplication;
        }
        // If the job was not created by the user, fetch only the application created by the user and his sub-ordinates
        // Fetch all users once
        const allUsers = await this.basicUserModel
          .find({ isDeleted: false, org: user.org._id.toString() })
          .lean();
        const allUsersMap = new Map(allUsers.map((u) => [u._id.toString(), u]));

        // Fetch allocations once
        const allocations = (await this.jobAllocationModel
          .find({
            job: jobId,
            kind: 'JobAllocationToAssignees',
            isDeleted: false,
          })
          .lean()) as any[];
        const allocationMap = new Map<string, any[]>();
        for (const allocation of allocations) {
          const assigneeId = allocation.assignee.toString();
          if (!allocationMap.has(assigneeId)) allocationMap.set(assigneeId, []);
          allocationMap.get(assigneeId)!.push(allocation);
        }

        // Get valid subordinates
        const validSubordinates = await this.getValidSubordinatesRecursively(
          user._id.toString(),
          jobId,
          allUsersMap,
          allocationMap,
        );

        const creatorIds = [
          user._id?.toString(),
          ...Array.from(validSubordinates),
        ];
        console.log('Creator IDs:', creatorIds);

        const jobApplication = await this.jobApplicationModel
          .find({
            jobId: jobId,
            createdBy: { $in: creatorIds },
            isDeleted: false,
            isRejected: false,
          })
          .populate({
            path: 'resumeMetadata',
            select: '_id originalName fileSize fileType locationUrl',
            model: 'FileMetadata',
          })
          .populate({
            path: 'coverLetterMetadata',
            select: '_id originalName fileSize fileType locationUrl',
            model: 'FileMetadata',
          })
          .populate({ path: 'jobId', select: '_id title' })
          .populate({ path: 'createdBy', select: '_id roles firstName' })
          .populate({
            path: 'workExperience',
            select:
              '_id jobTitle companyName jobStartDate jobEndDate currentlyWorking',
          })
          .populate({
            path: 'educationQualification',
            select: '_id courseName  university startDate endDate',
          })
          .populate({
            path: 'evaluationForm',
            select: '_id skill  years months rating isPrimary',
          })
          .populate({
            path: 'reLocation',
            select: '_id city state country postalCode',
            model: 'JobLocation',
          })
          .populate({
            path: 'workflow',
            populate: [
              {
                path: 'stages',
                select: '_id name type jobApplicationsCount',
              },
              // {
              //   path: 'org',
              //   select: '_id title legalName'
              // }
            ],
            select: '_id stages',
          })
          .exec();

        return jobApplication;
      }

      // // Return the found job application
      // return jobApplication;
    } catch (error) {
      // Log detailed error messages
      this.logger.error(
        `Error retrieving job application for job ID ${jobId} and user ID ${createdBy}: ${error.message}`,
      );
      throw error;
    }
  }

  async update(
    jobApplicationId: Types.ObjectId,
    updateJobApplicationDto: UpdateJobApplicationFormDto,
  ) {
    try {
      const populateOptions = this.getJobApplicationPopulateOptions();

      const jobApplication =
        await this.jobApplicationModel.findById(jobApplicationId);
      if (!jobApplication) {
        throw new NotFoundException(
          `Job application  not found with ID ${jobApplicationId}`,
        );
      }
      const updated = await this.jobApplicationModel
        .findByIdAndUpdate(jobApplicationId, updateJobApplicationDto, {
          new: true,
        })
        .populate(populateOptions)
        .exec();
      return updated;
    } catch (error) {
      this.logger.error(error);
      this.logger.error(
        `An error occurred while updating Job application by ID ${jobApplicationId}. ${error?.message}`,
      );
      throw error;
    }
  }

  async updateStage(
    jobApplicationId: Types.ObjectId,
    stageId: Types.ObjectId,
    user: any,
  ) {
    try {
      const jobApplication =
        await this.jobApplicationModel.findById(jobApplicationId);
      if (!jobApplication) {
        throw new NotFoundException(
          `Job application not found with ID ${jobApplicationId}`,
        );
      }

      const newStageData = await this.stageModel.findById(stageId);
      if (!newStageData) {
        throw new NotFoundException(`Stage not found with ID ${stageId}`);
      }

      const previousStageId = jobApplication.stage;

      // Update the job application with the new stage
      const updatedJobApplication = await this.jobApplicationModel
        .findByIdAndUpdate(
          jobApplicationId,
          {
            stage: stageId,
            ...(newStageData.type === 'workflow.interview.ai' && {
              aiInterview: {
                aiInterviewStatus: AiInterviewStatus.PENDING,
              },
            }),
          },
          { new: true },
        )
        .populate({ path: 'jobId', select: '_id title' })
        .populate({ path: 'createdBy', select: '_id roles firstName' })
        .populate({
          path: 'workExperience',
          select:
            '_id jobTitle companyName jobStartDate jobEndDate currentlyWorking',
        })
        .populate({
          path: 'educationQualification',
          select: '_id courseName university startDate endDate',
        })
        .populate({
          path: 'evaluationForm',
          select: '_id skill years months rating isPrimary',
        })
        .populate({
          path: 'reLocation',
          select: '_id city state country postalCode',
          model: 'JobLocation',
        })
        .populate({ path: 'org', select: '_id title' })
        .exec();

      // Decrement the jobApplicationsCount of the previous stage
      if (
        previousStageId &&
        previousStageId.toString() !== stageId.toString()
      ) {
        await this.stageModel.findByIdAndUpdate(
          previousStageId,
          { $inc: { jobApplicationsCount: -1 } },
          { new: true },
        );
      }

      // Increment the jobApplicationsCount of the new stage
      const updatedStage = await this.stageModel.findByIdAndUpdate(
        stageId,
        { $inc: { jobApplicationsCount: 1 } },
        { new: true },
      );

      const newStageName = await this.stageModel
        .findById(stageId)
        .select('name')
        .exec();

      if (newStageName) {
        const isSourcingStage = newStageName?.name === 'Sourcing';

        // Update the isScreenSelected flag based on the stage name
        await this.jobApplicationModel.findByIdAndUpdate(
          jobApplicationId,
          { isScreenSelected: !isSourcingStage }, // true if NOT sourcing
        );
      }

      const oldStageName = await this.stageModel
        .findById(previousStageId)
        .select('name')
        .exec();

      const candidateName = `${updatedJobApplication?.firstName} ${updatedJobApplication?.lastName}`;
      const jobTitle = updatedJobApplication?.jobId?.title || 'the job';
      const prevStage = oldStageName?.name || 'previous stage';
      const newStage = newStageName?.name || 'new stage';

      const message = `${candidateName}'s application for ${jobTitle} has moved from ${prevStage} to ${newStage}.`;

      await this.notificationsService.createAndNotify(
        jobApplication.createdBy.toString(),
        message,
        user._id.toString(),
        {},
        NotificationType.WORKFLOW_UPDATE,
        updatedJobApplication?.jobId?.toString(),
      );

      if (!updatedStage) {
        throw new NotFoundException(`Stage not found with ID ${stageId}`);
      }

      if (updatedJobApplication) {
        this.handleStageEvent(
          updatedStage,
          updatedJobApplication.toJSON(),
          user,
        );
      }

      return updatedJobApplication;
    } catch (error) {
      this.logger.error(error);
      this.logger.error(
        `An error occurred while updating Job application stage with ID ${jobApplicationId}. ${error?.message}`,
      );
      throw error;
    }
  }

  private handleStageEvent(
    stage: Stage,
    jobApplication: JobApplication,
    user: any,
  ) {
    if (
      stage.type === EmailTemplateEvent.WORKFLOW_SCREENING &&
      jobApplication
    ) {
      this.eventEmitter.emit(EmailTemplateEvent.WORKFLOW_SCREENING, {
        user,
        data: jobApplication,
      });
      this.logger.log(`Triggered ${EmailTemplateEvent.WORKFLOW_SCREENING}`);
    }
  }

  async remove(jobApplicationId: Types.ObjectId) {
    try {
      const jobApplication =
        await this.jobApplicationModel.findById(jobApplicationId);
      if (!jobApplication) {
        throw new NotFoundException(
          `Job application not found with ID ${jobApplication}`,
        );
      }
      await this.jobApplicationModel.deleteOne({ _id: jobApplicationId });
      return { message: 'Job application deleted' };
    } catch (error) {
      this.logger.error(
        `An error occurred while hard deleting by ID ${jobApplicationId}. ${error?.message}`,
      );
      throw new InternalServerErrorException(
        'An error occurred while hard deleting the Job application.',
      );
    }
  }

  async findByJobAndOrgId(orgId?: string, jobId?: string) {
    try {
      const query: any = {};

      if (orgId) {
        query.org = orgId;
      }

      if (jobId) {
        query.jobId = jobId;
      }
      const jobApplications = await this.jobApplicationModel
        .find(query)
        .populate({
          path: 'resumeMetadata',
          select: '_id originalName fileSize fileType locationUrl',
          model: 'FileMetadata',
        })
        .populate({
          path: 'coverLetterMetadata',
          select: '_id originalName fileSize fileType locationUrl',
          model: 'FileMetadata',
        })
        .populate({ path: 'jobId', select: '_id title' })
        .populate({ path: 'createdBy', select: '_id roles firstName' })
        .populate({
          path: 'workExperience',
          select:
            '_id jobTitle companyName jobStartDate jobEndDate currentlyWorking',
        })
        .populate({
          path: 'educationQualification',
          select: '_id courseName  university startDate endDate',
        })
        .populate({
          path: 'evaluationForm',
          select: '_id skill  years months rating isPrimary',
        })
        .populate({
          path: 'reLocation',
          select: '_id city state country postalCode',
          model: 'JobLocation',
        })
        .populate({
          path: 'workflow',
          populate: [
            {
              path: 'stages',
              select: '_id name type jobApplicationsCount',
            },
            // {
            //   path: 'org',
            //   select: '_id title legalName'
            // }
          ],
          select: '_id stages',
        })
        .exec();
      return jobApplications;
    } catch (error) {
      throw new InternalServerErrorException(
        `An error occurred while fetching job applications by ord id and/or job id. ${error?.message}`,
      );
    }
  }

  async filterJobApplicationsByStage(jobId: string, stageId: string) {
    try {
      // this.logger.log(jobId)
      // this.logger.log(stageId)
      const jobApplications = await this.jobApplicationModel
        .find({ jobId: jobId, stage: stageId })
        .populate({
          path: 'resumeMetadata',
          select: '_id originalName fileSize fileType locationUrl',
          model: 'FileMetadata',
        })
        .populate({
          path: 'coverLetterMetadata',
          select: '_id originalName fileSize fileType locationUrl',
          model: 'FileMetadata',
        })
        .populate({ path: 'jobId', select: '_id title' })
        .populate({ path: 'createdBy', select: '_id roles firstName' })
        .populate({
          path: 'workExperience',
          select:
            '_id jobTitle companyName jobStartDate jobEndDate currentlyWorking',
        })
        .populate({
          path: 'educationQualification',
          select: '_id courseName  university startDate endDate',
        })
        .populate({
          path: 'evaluationForm',
          select: '_id skill  years months rating isPrimary',
        })
        .populate({
          path: 'reLocation',
          select: '_id city state country postalCode',
          model: 'JobLocation',
        })
        .populate({
          path: 'workflow',
          populate: [
            {
              path: 'stages',
              select: '_id name type jobApplicationsCount',
            },
            // {
            //   path: 'org',
            //   select: '_id title legalName'
            // }
          ],
          select: '_id stages',
        })
        .exec();
      return jobApplications;
    } catch (error) {
      throw new InternalServerErrorException(
        `An error occurred while filtering job applications. ${error?.message}`,
      );
    }
  }

  async getJobApplicatinsByJobId(jobId: String) {
    try {
      // this.logger.log(jobId)
      // this.logger.log(stageId)
      const jobApplications = await this.jobApplicationModel
        .find({ jobId: jobId, isRejected: false })
        .sort({ updatedAt: -1, createdAt: -1 })
        .populate({
          path: 'resumeMetadata',
          select: '_id originalName fileSize fileType locationUrl',
          model: 'FileMetadata',
        })
        .populate({
          path: 'coverLetterMetadata',
          select: '_id originalName fileSize fileType locationUrl',
          model: 'FileMetadata',
        })
        .populate({ path: 'jobId', select: '_id title' })
        .populate({ path: 'createdBy', select: '_id roles firstName' })
        .populate({
          path: 'workExperience',
          select:
            '_id jobTitle companyName jobStartDate jobEndDate currentlyWorking',
        })
        .populate({
          path: 'educationQualification',
          select: '_id courseName  university startDate endDate',
        })
        .populate({
          path: 'evaluationForm',
          select: '_id skill  years months rating isPrimary',
        })
        .populate({
          path: 'reLocation',
          select: '_id city state country postalCode',
          model: 'JobLocation',
        })
        .populate({
          path: 'workflow',
          populate: [
            {
              path: 'stages',
              select: '_id name type jobApplicationsCount',
            },
            // {
            //   path: 'org',
            //   select: '_id title legalName'
            // }
          ],
          select: '_id stages',
        })
        .exec();

      const jobApplicationIds = jobApplications.map((app) =>
        app._id.toString(),
      );

      const latestOffers = await this.offerModel.aggregate([
        { $match: { jobApplication: { $in: jobApplicationIds } } },
        { $sort: { createdAt: -1 } },
        {
          $group: {
            _id: '$jobApplication',
            latestOffer: { $first: '$$ROOT' },
            isOnboarded: { $first: '$isOnBoarded' },
          },
        },
      ]);
      console.log(latestOffers);

      const offerMap = new Map(
        latestOffers.map((o) => [o._id.toString(), o.isOnboarded]),
      );

      // Attach isOnboarded to each job application
      const enrichedJobApplications = jobApplications.map((app) => {
        const isOnboarded = offerMap.get(app._id.toString()) ?? false;
        return { ...app.toObject(), isOnboarded };
      });

      return enrichedJobApplications;

      // return jobApplications;
    } catch (error) {
      throw new InternalServerErrorException(
        `An error occurred while filtering job applications. ${error?.message}`,
      );
    }
  }

  async updateStatus(
    jobApplicationId: Types.ObjectId,
    updateJobApplicationStatusDto: UpdateJobApplicationStatusDto,
  ) {
    try {
      const jobApplication =
        await this.jobApplicationModel.findById(jobApplicationId);
      if (!jobApplication) {
        throw new NotFoundException(
          `Job application  not found with ID ${jobApplicationId}`,
        );
      }

      const currentStageId = jobApplication.stage;

      const updated = await this.jobApplicationModel
        .findByIdAndUpdate(jobApplicationId, updateJobApplicationStatusDto, {
          new: true,
        })
        .populate({ path: 'jobId', select: '_id title' })
        .populate({ path: 'createdBy', select: '_id roles firstName' })
        .populate({
          path: 'workExperience',
          select:
            '_id jobTitle companyName jobStartDate jobEndDate currentlyWorking',
        })
        .populate({
          path: 'educationQualification',
          select: '_id courseName  university startDate endDate',
        })
        .populate({
          path: 'evaluationForm',
          select: '_id skill  years months rating isPrimary',
        })
        .populate({
          path: 'reLocation',
          select: '_id city state country postalCode',
          model: 'JobLocation',
        })
        .populate({
          path: 'workflow',
          populate: [
            {
              path: 'stages',
              select: '_id name type jobApplicationsCount',
            },
            // {
            //   path: 'org',
            //   select: '_id title legalName'
            // }
          ],
          select: '_id stages',
        })
        .exec();

      if (currentStageId) {
        await this.stageModel.findByIdAndUpdate(
          currentStageId,
          { $inc: { jobApplicationsCount: -1 } },
          { new: true },
        );
      }

      return updated;
    } catch (error) {
      this.logger.error(error);
      this.logger.error(
        `An error occurred while updating Job application by ID ${jobApplicationId}. ${error?.message}`,
      );
      throw error;
    }
  }

  filterJobApplications(
    jobId: string,
    queryJobApplicationDto: QueryJobApplicationDto,
  ) {
    let query: any = { isDeleted: false };

    const {
      firstName,
      lastName,
      currentLocation,
      industry,
      org,
      dateOfJoining,
      noticePeriodDays,
      minCurrentCTC,
      maxCurrentCTC,
      minYearsOfExperience,
      maxYearsOfExperience,
      minExpectedCTC,
      maxExpectedCTC,
    } = queryJobApplicationDto;

    if (jobId) query.jobId = jobId;
    if (firstName) query.firstName = firstName;
    if (lastName) query.lastName = lastName;
    if (currentLocation) query.currentLocation = currentLocation;
    if (industry) query.industry = industry;
    if (org) query.org = org;
    if (dateOfJoining) query.dateOfJoining = dateOfJoining;

    if (minCurrentCTC || maxCurrentCTC) {
      query.$and = [];
      if (minCurrentCTC) {
        query.$and.push({ currentCTC: { $gte: minCurrentCTC } });
      }
      if (maxCurrentCTC) {
        query.$and.push({ currentCTC: { $lte: maxCurrentCTC } });
      }
    }

    if (minCurrentCTC || maxCurrentCTC) {
      query.$and = [];
      if (minCurrentCTC) {
        query.$and.push({ currentCTC: { $gte: minCurrentCTC } });
      }
      if (maxCurrentCTC) {
        query.$and.push({ currentCTC: { $lte: maxCurrentCTC } });
      }
    }

    if (minYearsOfExperience || maxYearsOfExperience) {
      query.$and = [];
      if (minYearsOfExperience) {
        query.$and.push({ yearsOfExperience: { $gte: minYearsOfExperience } });
      }
      if (maxYearsOfExperience) {
        query.$and.push({ yearsOfExperience: { $lte: maxYearsOfExperience } });
      }
    }

    if (minExpectedCTC || maxExpectedCTC) {
      query.$and = [];
      if (minExpectedCTC) {
        query.$and.push({ expectedCTC: { $gte: minExpectedCTC } });
      }
      if (maxExpectedCTC) {
        query.$and.push({ expectedCTC: { $lte: maxExpectedCTC } });
      }
    }

    if (noticePeriodDays) {
      query.$or = [];
      query.$or.push({ noticePeriodDays: { $lte: noticePeriodDays } });
    }
    this.logger.log(`Query: ${JSON.stringify(query)} `);
    return this.jobApplicationModel.find(query).exec();
  }

  async getUserJobApplicationCount(userId: string): Promise<any> {
    const currentDate = new Date();
    const currentMonth = currentDate.getMonth(); // 0-based index
    const currentYear = currentDate.getFullYear();

    // Get the start of the current month and the start of the current year
    const startOfMonth = new Date(currentYear, currentMonth, 1);
    const startOfYear = new Date(currentYear, 0, 1);

    // Fetch counts for the month and the year (total and active)
    const [totalInMonth, totalInYear, activeInMonth, activeInYear] =
      await Promise.all([
        this.getCountByUserAndDate(userId, startOfMonth, new Date()),
        this.getCountByUserAndDate(userId, startOfYear, new Date()),
        this.getActiveCountByUserAndDate(userId, startOfMonth, new Date()),
        this.getActiveCountByUserAndDate(userId, startOfYear, new Date()),
      ]);

    return {
      totalApplicationsInMonth: totalInMonth,
      totalApplicationsInYear: totalInYear,
      activeApplicationsInMonth: activeInMonth,
      activeApplicationsInYear: activeInYear,
    };
  }

  async getJobApplicationCounts(
    userId: Types.ObjectId,
    filter: string,
    user: any,
  ): Promise<any> {
    const postingOrg = user.org._id;
    const currentDate = new Date();
    let startDate: Date;
    let endDate: Date;
    // Validate the filter value
    const validFilters = ['today', 'yesterday', 'thisWeek', 'thisMonth'];
    if (!validFilters.includes(filter)) {
      filter = 'today';
    }

    switch (filter) {
      case 'yesterday':
        startDate = new Date(currentDate);
        startDate.setDate(currentDate.getDate() - 1); // Set to yesterday
        startDate.setHours(0, 0, 0, 0); // Start of yesterday

        endDate = new Date(currentDate); // Today's end
        endDate.setHours(23, 59, 59, 999); // End of today
        break;

      case 'thisWeek':
        startDate = new Date(currentDate);
        startDate.setDate(currentDate.getDate() - 7); // Move to the same day last week
        startDate.setHours(0, 0, 0, 0); // Start of last week's same day

        // End date is today
        endDate = new Date(currentDate);
        endDate.setHours(23, 59, 59, 999); // End of today
        break;

      case 'thisMonth':
        startDate = new Date(
          currentDate.getFullYear(),
          currentDate.getMonth(),
          1,
        );
        endDate = new Date(currentDate);
        endDate.setHours(23, 59, 59, 999); // End of today
        break;
      default: // today
        startDate = new Date(currentDate);
        startDate.setHours(0, 0, 0, 0);
        endDate = new Date(currentDate);
        endDate.setHours(23, 59, 59, 999); // End of today
        break;
    }

    // Fetch all job applications for the user
    // const jobApplications = await this.jobApplicationModel.find({
    //   // createdBy: userId,
    //   $or: [
    //     { createdAt: { $gte: startDate, $lte: endDate } },
    //     { updatedAt: { $gte: startDate, $lte: endDate } },
    //   ],
    //   isRejected: false
    // });

    const jobApplications = await this.jobApplicationModel.aggregate([
      // Lookup to fetch job details
      { $addFields: { jobId: { $toObjectId: '$jobId' } } },
      {
        $lookup: {
          from: 'jobs', // Jobs collection
          localField: 'jobId',
          foreignField: '_id',
          as: 'jobDetails',
        },
      },
      { $unwind: { path: '$jobDetails', preserveNullAndEmptyArrays: true } }, // Flatten the job details array

      // Match active jobs and non-deleted job applications
      {
        $match: {
          'jobDetails.postingOrg': postingOrg, //from jobsModel
          'jobDetails.isOpen': true, //from jobsModel
          'jobDetails.isDeleted': false, //from jobsModel
          isDeleted: false, //from jobApplicationModel
          isRejected: false, //from
          $or: [
            { createdAt: { $gte: startDate, $lte: endDate } },
            { updatedAt: { $gte: startDate, $lte: endDate } },
          ],
        },
      },
    ]);
    const stageIds = jobApplications.map((application) => application.stage);

    // Fetch relevant stages with specific types
    const stages = await this.stageModel.find({
      _id: { $in: stageIds },
      type: {
        $in: ['workflow.offer', 'workflow.rejection', 'workflow.screening'],
      },
    });

    // Create a map from stage ID to stage type
    const stageTypeMap: Record<string, string> = stages.reduce(
      (map, stage) => {
        map[stage._id.toString()] = stage.type;
        return map;
      },
      {} as Record<string, string>,
    );

    // Count applications by stage type
    const counts = {
      screeningCount: 0,
      hiredCount: 0,
      rejectedCount: 0,
    };

    jobApplications.forEach((application) => {
      if (application.stage) {
        // Check if 'stage' is not undefined
        const stageType = stageTypeMap[application.stage.toString()];
        if (stageType === 'workflow.screening') counts.screeningCount++;
        else if (stageType === 'workflow.offer') counts.hiredCount++;
        else if (stageType === 'workflow.rejection') counts.rejectedCount++;
      } else {
        console.log('Application has no stage: ', application);
      }
    });

    return {
      filter: filter || 'today',
      startDate: startDate.toISOString(), // Use toISOString if UTC is desired
      endDate: endDate.toISOString(),
      startDateLocal: startDate.toLocaleString('en-IN', {
        timeZone: 'Asia/Kolkata',
      }), // Add local time representation
      endDateLocal: endDate.toLocaleString('en-IN', {
        timeZone: 'Asia/Kolkata',
      }),
      ...counts,
    };
  }

  async getInternalJobApplicationCounts(
    userId: Types.ObjectId,
    filter: string,
  ): Promise<any> {
    const currentDate = new Date();
    let startDate: Date;
    let endDate: Date;

    // Validate the filter value
    const validFilters = ['today', 'yesterday', 'thisWeek', 'thisMonth'];
    if (!validFilters.includes(filter)) {
      filter = 'today';
    }

    switch (filter) {
      case 'yesterday':
        startDate = new Date(currentDate);
        startDate.setDate(currentDate.getDate() - 1);
        startDate.setHours(0, 0, 0, 0);

        endDate = new Date(currentDate);
        endDate.setHours(23, 59, 59, 999);
        break;

      case 'thisWeek':
        startDate = new Date(currentDate);
        startDate.setDate(currentDate.getDate() - 7);
        startDate.setHours(0, 0, 0, 0);

        endDate = new Date(currentDate);
        endDate.setHours(23, 59, 59, 999);
        break;

      case 'thisMonth':
        startDate = new Date(
          currentDate.getFullYear(),
          currentDate.getMonth(),
          1,
        );
        endDate = new Date(currentDate);
        endDate.setHours(23, 59, 59, 999);
        break;

      default: // today
        startDate = new Date(currentDate);
        startDate.setHours(0, 0, 0, 0);
        endDate = new Date(currentDate);
        endDate.setHours(23, 59, 59, 999);
        break;
    }

    // First, get all internal jobs
    const internalJobs = await this.jobModel
      .find({
        jobType: 'Internal',
      })
      .select('_id');

    const internalJobIds = internalJobs.map((job) => job._id.toString());

    // Fetch job applications for internal jobs only
    const jobApplications = await this.jobApplicationModel.find({
      createdBy: userId,
      jobId: { $in: internalJobIds }, // Changed from 'job' to 'jobId'
      $or: [
        { createdAt: { $gte: startDate, $lte: endDate } },
        { updatedAt: { $gte: startDate, $lte: endDate } },
      ],
      isRejected: false,
    });

    const stageIds = jobApplications
      .map((application) => application.stage)
      .filter(Boolean);

    // Fetch relevant stages with specific types
    const stages = await this.stageModel.find({
      _id: { $in: stageIds },
      type: {
        $in: ['workflow.offer', 'workflow.rejection', 'workflow.screening'],
      },
    });

    // Create a map from stage ID to stage type
    const stageTypeMap: Record<string, string> = stages.reduce(
      (map, stage) => {
        map[stage._id.toString()] = stage.type;
        return map;
      },
      {} as Record<string, string>,
    );

    // Count applications by stage type
    const counts = {
      screeningCount: 0,
      hiredCount: 0,
      rejectedCount: 0,
    };

    jobApplications.forEach((application) => {
      if (application.stage) {
        const stageType = stageTypeMap[application.stage.toString()];

        if (stageType === 'workflow.screening') {
          counts.screeningCount++;
        } else if (stageType === 'workflow.offer') {
          counts.hiredCount++;
        } else if (stageType === 'workflow.rejection') {
          counts.rejectedCount++;
        }
      }
    });

    return {
      filter: filter || 'today',
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
      startDateLocal: startDate.toLocaleString('en-IN', {
        timeZone: 'Asia/Kolkata',
      }),
      endDateLocal: endDate.toLocaleString('en-IN', {
        timeZone: 'Asia/Kolkata',
      }),
      ...counts,
    };
  }

  private async getCountByUserAndDate(
    userId: string,
    startDate: Date,
    endDate: Date,
  ): Promise<number> {
    return this.jobApplicationModel.countDocuments({
      createdBy: userId,
      $or: [
        { createdAt: { $gte: startDate, $lte: endDate } },
        { updatedAt: { $gte: startDate, $lte: endDate } },
      ],
      isRejected: false,
    });
  }

  private async getActiveCountByUserAndDate(
    userId: string,
    startDate: Date,
    endDate: Date,
  ): Promise<number> {
    return this.jobApplicationModel.countDocuments({
      createdBy: userId,
      isRejected: false,
      $or: [
        { createdAt: { $gte: startDate, $lte: endDate } },
        { updatedAt: { $gte: startDate, $lte: endDate } },
      ],
    });
  }

  //stages this month job-application count

  async getJobApplicationsCountByStageForCurrentMonthAndYear(
    userId: string,
    filter: string = 'month',
  ) {
    try {
      let startDate: Date;
      let endDate: Date = new Date(); // Default to now

      switch (filter) {
        case 'today':
          startDate = moment().startOf('day').toDate();
          endDate = moment().endOf('day').toDate();
          break;
        case 'yesterday':
          startDate = moment().subtract(1, 'days').startOf('day').toDate();
          endDate = moment().subtract(1, 'days').endOf('day').toDate();
          break;
        case 'week':
          startDate = moment().startOf('week').toDate();
          endDate = moment().endOf('week').toDate();
          break;
        case 'month':
        default:
          startDate = moment().startOf('month').toDate();
          endDate = moment().endOf('month').toDate();
          break;
      }

      const jobApplicationsCount = await this.jobApplicationModel.aggregate([
        {
          $match: {
            createdAt: { $gte: startDate, $lte: endDate },
            createdBy: userId,
            isRejected: false,
          },
        },
        {
          $group: {
            _id: '$stage', // Group by stage
            count: { $sum: 1 }, // Count job applications in each stage
          },
        },
        {
          $lookup: {
            from: 'stages',
            localField: '_id',
            foreignField: '_id',
            as: 'stageDetails',
          },
        },
        {
          $unwind: {
            path: '$stageDetails',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $sort: {
            'stageDetails.sequenceNumber': 1, // Sort by sequenceNumber
          },
        },
        {
          $group: {
            _id: '$stageDetails.name', // Group by stage name
            totalCount: { $sum: '$count' }, // Sum counts for same stageName
            sequenceNumber: { $first: '$stageDetails.sequenceNumber' }, // Capture sequence number
          },
        },
        {
          $project: {
            stageName: '$_id', // Use stage name as field
            count: '$totalCount', // Total count for that stage
            sequenceNumber: 1,
            _id: 0, // Exclude _id from the result
          },
        },
        {
          $sort: { sequenceNumber: 1 }, // Final sort just in case
        },
      ]);

      return jobApplicationsCount;
    } catch (error) {
      this.logger.error(
        `Error fetching job application counts by stage for the current month and year: ${error.message}`,
      );
      throw new InternalServerErrorException(
        `Error fetching job application counts by stage for the current month and year: ${error.message}`,
      );
    }
  }

  async getJobApplicationsCountByStageForCurrentMonthAndYearForOrg(
    orgId: string,
  ) {
    try {
      const startOfMonth = moment().startOf('month').toDate();
      const endOfMonth = moment().endOf('month').toDate();

      const jobApplicationsCount = await this.jobApplicationModel.aggregate([
        {
          $match: {
            createdAt: { $gte: startOfMonth, $lte: endOfMonth },
            org: orgId,
            isRejected: false,
          },
        },
        {
          $group: {
            _id: '$stage', // Group by stage
            count: { $sum: 1 }, // Count job applications in each stage
          },
        },
        {
          $lookup: {
            from: 'stages',
            localField: '_id',
            foreignField: '_id',
            as: 'stageDetails',
          },
        },
        {
          $unwind: {
            path: '$stageDetails',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $group: {
            _id: '$stageDetails.name', // Group by stage name
            totalCount: { $sum: '$count' }, // Sum counts for same stageName
          },
        },
        {
          $project: {
            stageName: '$_id', // Use stage name as field
            count: '$totalCount', // Total count for that stage
            _id: 0, // Exclude _id from the result
          },
        },
      ]);

      return jobApplicationsCount;
    } catch (error) {
      this.logger.error(
        `Error fetching job application counts by stage for the current month and year: ${error.message}`,
      );
      throw new InternalServerErrorException(
        `Error fetching job application counts by stage for the current month and year: ${error.message}`,
      );
    }
  }
  async getJobApplicationsCountByStageForPastSixMonths(userId: string) {
    try {
      const results = [];

      // Loop through the past 6 months including the current month
      for (let i = 0; i < 6; i++) {
        const startOfMonth = moment()
          .subtract(i, 'months')
          .startOf('month')
          .toDate();
        const endOfMonth = moment()
          .subtract(i, 'months')
          .endOf('month')
          .toDate();

        const jobApplicationsCount = await this.jobApplicationModel.aggregate([
          {
            $match: {
              createdAt: { $gte: startOfMonth, $lte: endOfMonth },
              createdBy: userId,
              isRejected: false,
            },
          },
          {
            $group: {
              _id: '$stage', // Group by stage
              count: { $sum: 1 }, // Count job applications in each stage
            },
          },
          {
            $lookup: {
              from: 'stages',
              localField: '_id',
              foreignField: '_id',
              as: 'stageDetails',
            },
          },
          {
            $unwind: {
              path: '$stageDetails',
              preserveNullAndEmptyArrays: true,
            },
          },
          {
            $group: {
              _id: '$stageDetails.name', // Group by stage name
              totalCount: { $sum: '$count' }, // Sum counts for same stageName
            },
          },
          {
            $project: {
              stageName: '$_id', // Use stage name as field
              count: '$totalCount', // Total count for that stage
              month: moment(startOfMonth).format('MMMM'), // Month name (e.g., "January")
              year: moment(startOfMonth).year(), // Year (e.g., 2024)
              _id: 0, // Exclude _id from the result
            },
          },
        ]);

        results.push(...jobApplicationsCount); // Append results for each month
      }

      return results;
    } catch (error) {
      this.logger.error(
        `Error fetching job application counts by stage for the past 6 months: ${error.message}`,
      );
      throw new InternalServerErrorException(
        `Error fetching job application counts by stage for the past 6 months: ${error.message}`,
      );
    }
  }

  //stages job-application count
  async getJobApplicationsCountByStageForMonthAndYear(
    userId: string,
    month: number,
    year: number,
  ) {
    try {
      // Calculate the start and end dates for the provided month and year
      const startOfMonth = moment()
        .year(year)
        .month(month - 1) // Month is zero-based in Moment.js
        .startOf('month')
        .toDate();
      const endOfMonth = moment()
        .year(year)
        .month(month - 1)
        .endOf('month')
        .toDate();

      const jobApplicationsCount = await this.jobApplicationModel.aggregate([
        {
          $match: {
            createdAt: { $gte: startOfMonth, $lte: endOfMonth },
            createdBy: userId,
            isRejected: false,
          },
        },
        {
          $group: {
            _id: {
              day: { $dayOfMonth: '$createdAt' }, // Group by day of the month
              stage: '$stage', // Group by stage
            },
            count: { $sum: 1 }, // Count job applications for each stage per day
          },
        },
        {
          $lookup: {
            from: 'stages',
            localField: '_id.stage',
            foreignField: '_id',
            as: 'stageDetails',
          },
        },
        {
          $unwind: {
            path: '$stageDetails',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $group: {
            _id: {
              day: '$_id.day', // Group by day
              stageName: '$stageDetails.name', // Group by stage name
            },
            totalCount: { $sum: '$count' }, // Sum counts for the same day and stageName
          },
        },
        {
          $group: {
            _id: '$_id.day', // Group by day
            stages: {
              $push: {
                stageName: '$_id.stageName',
                count: '$totalCount',
              },
            },
          },
        },
        {
          $project: {
            day: '$_id', // Use day as a field
            stages: 1, // Include stages
            _id: 0, // Exclude _id
          },
        },
        { $sort: { day: 1 } }, // Sort by day
      ]);

      return jobApplicationsCount;
    } catch (error) {
      this.logger.error(
        `Error fetching job application counts by stage for the given month and year: ${error.message}`,
      );
      throw new InternalServerErrorException(
        `Error fetching job application counts by stage for the given month and year: ${error.message}`,
      );
    }
  }

  emitEvent(eventName: string, payload: any) {
    // this.logger.debug(payload)
    this.eventEmitter.emit(eventName, payload);
  }

  async addDynamicField(dynamicFieldDto: DynamicFieldDto) {
    try {
      const newField = new this.dynamicFieldModel(dynamicFieldDto);
      return await newField.save();
    } catch (error) {
      throw new InternalServerErrorException(
        `Error adding dynamic field of job application: ${error.message}`,
      );
    }
  }

  async updateDynamicField(
    fieldId: string,
    dynamicFieldDto: UpdateDynamicFieldDto,
  ) {
    try {
      const updatedField = await this.dynamicFieldModel.findByIdAndUpdate(
        fieldId,
        dynamicFieldDto,
        { new: true },
      );
      if (!updatedField) {
        throw new NotFoundException('Dynamic field not found');
      }
      return updatedField;
    } catch (error) {
      throw new InternalServerErrorException(
        `Error updating dynamic field of job application: ${error.message}`,
      );
    }
  }

  async deleteDynamicField(fieldId: string) {
    try {
      const deletedField =
        await this.dynamicFieldModel.findByIdAndDelete(fieldId);
      if (!deletedField) {
        throw new NotFoundException('Dynamic field not found');
      }
      return deletedField;
    } catch (error) {
      throw new InternalServerErrorException(
        `Error deleting dynamic field of job application: ${error.message}`,
      );
    }
  }

  async getAllDynamicFields(orgId?: string, isJobApplicationField?: boolean) {
    try {
      const filter: any = {};

      if (isJobApplicationField && orgId) {
        filter.orgId = orgId;
        filter.isJobApplicationField = isJobApplicationField;
        return await this.dynamicFieldModel
          .find(filter)
          .sort({ updatedAt: -1 })
          .exec();
      }

      return [];
    } catch (error) {
      throw new InternalServerErrorException(
        `Error retrieving dynamic fields for job applications: ${error.message}`,
      );
    }
  }

  async getDynamicField(fieldId: string) {
    try {
      const field = await this.dynamicFieldModel.findById(fieldId).exec();
      if (!field) {
        throw new NotFoundException('Dynamic field not found');
      }
      return field;
    } catch (error) {
      throw new InternalServerErrorException(
        `Error retrieving dynamic field of job application: ${error.message}`,
      );
    }
  }

  async getJobApplicantsCountByJob(
    filter: string,
    userId: string,
    user: any,
  ): Promise<any> {
    const currentDate = new Date();
    let startDate: Date;
    let endDate: Date;
    const postingOrg = user.org._id;

    // Validate the filter value
    const validFilters = ['today', 'yesterday', 'thisWeek', 'thisMonth'];

    if (!validFilters.includes(filter)) {
      filter = 'today';
    }

    switch (filter) {
      case 'yesterday':
        startDate = new Date(currentDate);
        startDate.setDate(currentDate.getDate() - 1); // Set to yesterday
        startDate.setHours(0, 0, 0, 0); // Start of yesterday
        endDate = new Date(startDate); // Start with startDate for endDate
        endDate.setHours(23, 59, 59, 999); // End of the previous day
        break;

      case 'thisWeek':
        startDate = new Date(currentDate);
        const currentDayOfWeek = startDate.getDay(); // 0 (Sunday) to 6 (Saturday)
        const daysToSubtract = 7; // Go back one week
        startDate.setDate(startDate.getDate() - daysToSubtract); // Move to same day last week
        startDate.setHours(0, 0, 0, 0); // Start of last week's same day

        endDate = new Date(currentDate); // Today's end
        endDate.setHours(23, 59, 59, 999);
        break;

      case 'thisMonth':
        startDate = new Date(
          currentDate.getFullYear(),
          currentDate.getMonth(),
          1,
        );
        endDate = new Date(currentDate);
        endDate.setHours(23, 59, 59, 999); // End of today
        break;
      default: // today
        startDate = new Date(currentDate);
        startDate.setHours(0, 0, 0, 0);
        endDate = new Date(currentDate);
        endDate.setHours(23, 59, 59, 999); // End of today
        break;
    }

    // Fetch all job applications by date filter
    const jobApplications = await this.jobApplicationModel.aggregate([
      {
        $match: {
          $or: [
            { createdAt: { $gte: startDate, $lte: endDate } },
            { updatedAt: { $gte: startDate, $lte: endDate } },
          ],
          isRejected: false,
        },
      },

      { $addFields: { jobId: { $toObjectId: '$jobId' } } },
      {
        $lookup: {
          from: 'jobs',
          localField: 'jobId',
          foreignField: '_id',
          as: 'jobdetails',
        },
      },
      {
        $unwind: {
          path: '$jobdetails',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $match: {
          'jobdetails.postingOrg': postingOrg,
        },
      },

      {
        $group: {
          _id: {
            jobId: '$jobdetails._id',
            jobName: '$jobdetails.title',
          },
          count: { $sum: 1 },
        },
      },

      {
        $project: {
          Job: '$_id.jobName', // Use dept name as field
          count: '$count', // Total count for that stage
          _id: 0, // Exclude _id from the result
        },
      },
    ]);
    console.log('COUNT::', jobApplications);
    return jobApplications;
  }

  async getInternalJobApplicantsCountByJob(filter: string): Promise<any> {
    const currentDate = new Date();
    let startDate: Date;
    let endDate: Date;

    // Validate the filter value
    const validFilters = ['today', 'yesterday', 'thisWeek', 'thisMonth'];
    if (!validFilters.includes(filter)) {
      filter = 'today';
    }

    switch (filter) {
      case 'yesterday':
        startDate = new Date(currentDate);
        startDate.setDate(currentDate.getDate() - 1); // Set to yesterday
        startDate.setHours(0, 0, 0, 0); // Start of yesterday

        endDate = new Date(currentDate); // Today's end
        endDate.setHours(23, 59, 59, 999); // End of today
        break;

      case 'thisWeek':
        startDate = new Date(currentDate);
        startDate.setDate(currentDate.getDate() - 7); // Move to the same day last week
        startDate.setHours(0, 0, 0, 0); // Start of last week's same day

        // End date is today
        endDate = new Date(currentDate);
        endDate.setHours(23, 59, 59, 999); // End of today
        break;

      case 'thisMonth':
        startDate = new Date(
          currentDate.getFullYear(),
          currentDate.getMonth(),
          1,
        );
        endDate = new Date(currentDate);
        endDate.setHours(23, 59, 59, 999); // End of today
        break;

      default: // today
        startDate = new Date(currentDate);
        startDate.setHours(0, 0, 0, 0);
        endDate = new Date(currentDate);
        endDate.setHours(23, 59, 59, 999); // End of today
        break;
    }

    // console.log('Filter:', filter);
    // console.log('Start Date (UTC):', startDate.toISOString());
    // console.log('End Date (UTC):', endDate.toISOString());
    // console.log('Start Date (Local):', startDate.toLocaleString());
    // console.log('End Date (Local):', endDate.toLocaleString());

    // Fetch all job applications with jobType "Internal" by date filter
    const jobApplications = await this.jobApplicationModel.aggregate([
      {
        $match: {
          $or: [
            { createdAt: { $gte: startDate, $lte: endDate } },
            { updatedAt: { $gte: startDate, $lte: endDate } },
          ],
          isRejected: false,
        },
      },
      { $addFields: { jobId: { $toObjectId: '$jobId' } } },
      {
        $lookup: {
          from: 'jobs',
          localField: 'jobId',
          foreignField: '_id',
          as: 'jobdetails',
        },
      },
      {
        $unwind: {
          path: '$jobdetails',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $match: {
          'jobdetails.jobType': 'Internal', // Filter for Internal jobs
        },
      },
      {
        $group: {
          _id: {
            jobId: '$jobdetails._id',
            jobName: '$jobdetails.title',
          },
          count: { $sum: 1 },
        },
      },
      {
        $project: {
          Job: '$_id.jobName', // Use dept name as field
          count: '$count', // Total count for that stage
          _id: 0, // Exclude _id from the result
        },
      },
    ]);

    // console.log('Internal Job COUNT::', jobApplications);
    return jobApplications;
  }

  async createJobApplication(
    createJobApplicationFormDto: CreatePublicJobApplicationFormDto,
  ) {
    try {
      //fetch user associated with job
      const job = await this.jobModel.findById(
        createJobApplicationFormDto.jobId,
      );

      if (!job || !job.createdBy) {
        throw new NotFoundException(`No user is associated with given job.`);
      }

      const createdJobApplicationForm = new this.jobApplicationModel({
        ...createJobApplicationFormDto,
        createdBy: job.createdBy,
      });

      // Fetch the workflow for the job
      const workflow = await this.workflowModel
        .findById(createJobApplicationFormDto.workflow)
        .populate('stages')
        .exec();

      if (!workflow || !workflow.stages || workflow.stages.length === 0) {
        throw new InternalServerErrorException('Workflow or stages not found.');
      }

      // Assign the first stage as the default stage
      const firstStageId = workflow.stages[0]._id;
      createdJobApplicationForm.stage = firstStageId;

      // Save the job application
      const savedJobApplication = await createdJobApplicationForm.save();

      // Increment the jobApplicationsCount for the first stage
      await this.stageModel.findByIdAndUpdate(
        firstStageId,
        { $inc: { jobApplicationsCount: 1 } },
        { new: true },
      );

      return savedJobApplication;
    } catch (error) {
      this.logger.error(
        `Failed to create Job applicatio for pubic route. ${error}`,
      );
      throw new InternalServerErrorException(
        `Failed to create Job application pubic route. ${error.message}`,
      );
    }
  }

  async getJobApplicationsProgressAgainstTarget(userId: string) {
    try {
      const currentDate = moment();
      const pastSixMonthsData = [];

      for (let i = 0; i < 6; i++) {
        const startOfMonth = currentDate
          .clone()
          .subtract(i, 'months')
          .startOf('month')
          .toDate();
        const endOfMonth = currentDate
          .clone()
          .subtract(i, 'months')
          .endOf('month')
          .toDate();

        // Fetch all job applications for the user in the given time range
        const jobApplications = await this.jobApplicationModel.find({
          createdBy: userId,
          $or: [
            { createdAt: { $gte: startOfMonth, $lte: endOfMonth } },
            { updatedAt: { $gte: startOfMonth, $lte: endOfMonth } },
          ],
          isRejected: false,
        });

        // Construct progress data
        const progress = {
          month: moment(startOfMonth).format('MMM').toUpperCase(),
          year: startOfMonth.getFullYear(),
          profilesSubmitted: jobApplications.length,
        };

        pastSixMonthsData.push(progress);
      }

      return pastSixMonthsData.reverse();
    } catch (error) {
      this.logger.error(
        `Error fetching job application progress against target: ${error.message}`,
      );
      throw new InternalServerErrorException(
        `Error fetching job application progress against target: ${error.message}`,
      );
    }
  }

  async processJobApplications(userId: string) {
    try {
      const populateOptions = this.getJobApplicationPopulateOptions();

      const applications = await this.jobApplicationModel
        .find({ createdBy: userId, isRejected: false })
        .populate(populateOptions)
        .sort({ updatedAt: -1 })
        .exec();

      // Step 1: Collect unique stage names
      const stageMap: Record<string, string[]> = {};

      applications.forEach((application) => {
        if (!application.workflow || !application.workflow.stages) return;

        // Extract all stage names using type assertion
        application.workflow.stages.forEach((stage) => {
          const stageName = (stage as any)?.name; // Type assertion
          if (stageName && !stageMap[stageName]) {
            stageMap[stageName] = []; // Initialize empty array
          }
        });
      });

      // Step 2: Populate the stage arrays with endClientOrg.title based on current stage
      applications.forEach((application) => {
        const currentStage = (application.stage as any)?.name; // Type assertion
        const endClientTitle = application.jobId?.endClientOrg?.title;

        if (currentStage && endClientTitle) {
          stageMap[currentStage].push(endClientTitle);
        }
      });

      return stageMap;
    } catch (error) {
      this.logger.error(error);
      throw new InternalServerErrorException(
        `An error occurred while processing job applications. ${error?.message}`,
      );
    }
  }

  //   async getRecruiterPerformanceRatio(user: any, filter: string = 'month') {
  //   try {
  //     const userId = user._id;

  //     let startDate: Date, endDate: Date;
  //     const now = moment().endOf('day').toDate(); // Current time (end of today)

  //     switch (filter) {
  //       case 'today':
  //         startDate = moment().startOf('day').toDate();
  //         endDate = now;
  //         break;
  //       case 'yesterday':
  //         startDate = moment().subtract(1, 'days').startOf('day').toDate();
  //         endDate = moment().subtract(1, 'days').endOf('day').toDate();
  //         break;
  //       case 'week':
  //         startDate = moment().startOf('week').toDate();
  //         endDate = now;
  //         break;
  //       case 'month':
  //       default:
  //         startDate = moment().startOf('month').toDate();
  //         endDate = now;
  //         break;
  //     }

  //     // 1️⃣ Fetch all active jobs (isOpen: true)
  //     const activeJobs = await this.jobModel.find({ isOpen: true, isDeleted: false }).select('_id').exec();
  //     const activeJobIds = activeJobs.map(job => job._id.toString());

  //     // 2️⃣ Retrieve all job applications created by the recruiter for active jobs
  //     const jobApplications = await this.jobApplicationModel.find({
  //       createdBy: userId,
  //       jobId: { $in: activeJobIds },
  //       isRejected: false,
  //       createdAt: { $gte: startDate, $lte: endDate }
  //     })
  //       .populate({
  //         path: 'stage',
  //         select: '_id name type jobApplicationsCount',
  //         model: 'Stage'
  //       })
  //       .select('_id stage')
  //       .exec();

  //     const totalApplications = jobApplications.length;
  //     const jobApplicationIds = jobApplications.map(app => app._id.toString());

  //     // 3️⃣ Count job applications in the "workflow.screening" stage
  //     const screeningCount = jobApplications.filter(app =>
  //       (app.stage as any)?.type === "workflow.screening"
  //     ).length;

  //     // 4️⃣ Retrieve the latest offer per job application
  //     const latestOffers = await this.offerModel.aggregate([
  //       { $match: { jobApplication: { $in: jobApplicationIds } } },
  //       { $sort: { createdAt: -1 } }, // Sort offers by createdAt (latest first)
  //       {
  //         $group: {
  //           _id: "$jobApplication", // Group by job application
  //           latestOffer: { $first: "$$ROOT" } // Select the most recent offer
  //         }
  //       }
  //     ]);

  //     // 5️⃣ Count job applications that received at least one offer
  //     const applicationsWithOffers = latestOffers.length;

  //     // 6️⃣ Count job applications that have been onboarded (from latest offer only)
  //     const onboardedApplications = latestOffers.filter(offer => offer.latestOffer.isOnBoarded).length;

  //     // 7️⃣ Count all attended interviews for job applications
  //     const totalAttendedInterviews = await this.interviewModel.countDocuments({
  //       jobApplication: { $in: jobApplicationIds },
  //       isCandidateAttended: true,
  //     });

  //     return {
  //       totalApplications,
  //       screeningCount, // Number of applications in phone screening stage
  //       applicationsWithOffers: applicationsWithOffers - onboardedApplications,
  //       onboardedApplications,
  //       totalAttendedInterviews,
  //     };

  //   } catch (error) {
  //     this.logger.error(`Error retrieving recruiter performance ratio: ${error.message}`, error.stack);
  //     throw new InternalServerErrorException(`Error retrieving recruiter performance ratio. ${error.message}`);
  //   }
  // }

  async getRecruiterPerformanceLast3Months(user: any, filter: number = 3) {
    try {
      const userId = user._id.toString();
      console.log('userId', userId);

      const results = [];
      for (let i = filter - 1; i >= 0; i--) {
        const startDate = moment()
          .subtract(i, 'months')
          .startOf('month')
          .toDate();
        const endDate = moment().subtract(i, 'months').endOf('month').toDate();
        // const monthLabel = moment(startDate).format('MMMM');
        const monthLabel = moment(startDate).format('MMM-YYYY').toUpperCase(); // 👈 This line

        // 1️⃣ Fetch active jobs
        const activeJobs = await this.jobModel
          .find({ isOpen: true, isDeleted: false })
          .select('_id')
          .exec();
        const activeJobIds = activeJobs.map((job) => job._id.toString());
        // console.log("activeJobIds", activeJobIds)

        // 2️⃣ Recruiter applications for this month
        const jobApplications = await this.jobApplicationModel
          .find({
            createdBy: userId,
            jobId: { $in: activeJobIds },
            isRejected: false,
            createdAt: { $gte: startDate, $lte: endDate },
          })
          .populate({
            path: 'stage',
            select: '_id name type jobApplicationsCount',
            model: 'Stage',
          })
          .select('_id stage isScreenSelected')
          .exec();

        const totalApplications = jobApplications.length;
        const jobApplicationIds = jobApplications.map((app) =>
          app._id.toString(),
        );

        console.log('jobApplications', jobApplications);
        // const screeningCount = jobApplications.filter(app =>
        //   (app.stage as any)?.type === "workflow.screening"
        // ).length;

        // const sourcingCount = jobApplications.filter(app =>
        //   (app.stage as any)?.type === "workflow.sourcing"
        // ).length;

        const screeningCount = jobApplications.filter(
          (app) => app.isScreenSelected,
        ).length;

        const safeDivide = (numerator: number, denominator: number): number =>
          denominator > 0
            ? Number(((numerator / denominator) * 100).toFixed(2))
            : 0;

        // console.log("sourcingCount", sourcingCount)
        console.log('screeningCount', screeningCount);
        // const screenSelectRatio = Number(((screeningCount / sourcingCount) * 100).toFixed(2));

        const screenSelectRatio = safeDivide(screeningCount, totalApplications);

        console.log('screenSelectRatio', screenSelectRatio);

        // 4️⃣ Interviews showUp ratio
        const latestScheduledInterviews = await this.interviewModel.aggregate([
          {
            $match: {
              jobApplication: { $in: jobApplicationIds },
              createdAt: { $gte: startDate, $lte: endDate },
            },
          },
          // { $sort: { createdAt: -1 } },
          // {
          //   $group: {
          //     _id: "$jobApplication",
          //     latestInterview: { $first: "$$ROOT" }
          //   }
          // }
        ]);
        console.log(latestScheduledInterviews);
        const totalInterviewsScheduled = latestScheduledInterviews.length;
        const totalAttendedInterviews = latestScheduledInterviews.filter(
          (interview) => interview.isCandidateAttended,
        ).length;

        // const InterviewShowUpRatio = Number(((totalAttendedInterviews / totalInterviewsScheduled) * 100).toFixed(2));
        const InterviewShowUpRatio = safeDivide(
          totalAttendedInterviews,
          totalInterviewsScheduled,
        );

        // 3️⃣ Latest offers
        let applicationsWithOffers = 0;
        let onboardedApplications = 0;
        let acceptedOffers = 0;

        if (jobApplicationIds.length) {
          const latestOffers = await this.offerModel.aggregate([
            {
              $match: {
                jobApplication: { $in: jobApplicationIds },
                createdAt: { $gte: startDate, $lte: endDate },
              },
            },
            { $sort: { createdAt: -1 } },
            {
              $group: {
                _id: '$jobApplication',
                latestOffer: { $first: '$$ROOT' },
              },
            },
          ]);
          console.log(latestOffers);

          applicationsWithOffers = latestOffers.length;
          onboardedApplications = latestOffers.filter(
            (offer) => offer.latestOffer.isOnBoarded,
          ).length;
          acceptedOffers = latestOffers.filter(
            (offer) => offer.latestOffer.isAccepted,
          ).length;
        }
        // const offerAcceptanceRatio = Number(((acceptedOffers / applicationsWithOffers) * 100).toFixed(2));
        // const onboardingRatio = Number(((onboardedApplications / applicationsWithOffers) * 100).toFixed(2));

        const offerAcceptanceRatio = safeDivide(
          acceptedOffers,
          applicationsWithOffers,
        );
        const onboardingRatio = safeDivide(
          onboardedApplications,
          applicationsWithOffers,
        );

        const jobAllocations = await this.jobAllocationModel.aggregate([
          {
            $match: {
              assignee: userId,
              kind: 'JobAllocationToAssignees',
              createdAt: { $gte: startDate, $lte: endDate },
            },
          },
        ]);
        console.log('jobAllocations', jobAllocations);
        const jobIds = [
          ...new Set(jobAllocations.map((a) => a.job.toString())),
        ]; // to avoid duplicates
        const jobApplicationsforAllocated =
          await this.jobApplicationModel.aggregate([
            {
              $match: {
                jobId: { $in: jobIds },
                createdBy: userId,
              },
            },
            {
              $group: {
                _id: '$jobId',
              },
            },
          ]);

        const submittedJobIds = jobApplicationsforAllocated.map((item) =>
          item._id.toString(),
        );
        const requirementAssigned = jobIds.length;
        const requirementAttempted = submittedJobIds.length;

        const attemptedRatio = safeDivide(
          requirementAttempted,
          requirementAssigned,
        );

        const turnArountTime = await this.jobAllocationModel.aggregate([
          {
            $match: {
              assignee: userId,
              kind: 'JobAllocationToAssignees',
              createdAt: { $gte: startDate, $lte: endDate },
            },
          },
          {
            $group: {
              _id: '$job',
              assignedTime: { $first: '$createdAt' }, // Get the assigned time for the job
            },
          },
          {
            $lookup: {
              from: 'jobapplications', // Your actual Mongo collection name
              let: { jobId: '$_id' },
              pipeline: [
                {
                  $match: {
                    $expr: {
                      $and: [
                        { $eq: ['$jobId', '$$jobId'] },
                        { $eq: ['$createdBy', userId] }, // 🔥 match creator with assignee
                      ],
                    },
                  },
                },
                { $sort: { createdAt: 1 } },
                { $limit: 1 },
              ],
              as: 'firstApplication',
            },
          },
          {
            $unwind: {
              path: '$firstApplication',
              preserveNullAndEmptyArrays: true,
            },
          },
          {
            $addFields: {
              timeDifferenceInMs: {
                $cond: {
                  if: { $ifNull: ['$firstApplication', false] },
                  then: {
                    $subtract: ['$firstApplication.createdAt', '$assignedTime'],
                  },
                  else: 0, // Treat jobs with no application as 0 ms
                },
              },
            },
          },
          {
            $match: {
              firstApplication: { $ne: null },
            },
          },
          {
            $group: {
              _id: null,
              averageTimeInMs: { $avg: '$timeDifferenceInMs' },
              totalJobs: { $sum: 1 },
            },
          },
          {
            $project: {
              _id: 0,
              averageTimeInMs: 1,
              averageTimeInHours: {
                $round: [{ $divide: ['$averageTimeInMs', 1000 * 60 * 60] }, 2],
              },
              totalJobs: 1,
            },
          },
        ]);

        console.log('turnArountTime', turnArountTime);

        const performanceScore =
          screenSelectRatio * 0.15 +
          InterviewShowUpRatio * 0.15 +
          offerAcceptanceRatio * 0.2 +
          onboardingRatio * 0.4 +
          attemptedRatio * 0.1;
        const performanceScoreOutOf10 = performanceScore / 10;
        console.log('performanceScore', performanceScoreOutOf10);

        results.push({
          month: monthLabel,
          screenSelectRatio: screenSelectRatio ?? 0,
          InterviewShowUpRatio: InterviewShowUpRatio ?? 0,
          offerAcceptanceRatio: offerAcceptanceRatio ?? 0,
          onboardingRatio: onboardingRatio ?? 0,
          attemptedRatio: attemptedRatio ?? 0,
          turnArountTimeRatio: turnArountTime[0]?.averageTimeInHours ?? 0,
          // performanceIndex : performanceScoreOutOf10 ?? 0,
        });
      }

      return results;
    } catch (error) {
      this.logger.error(
        `Error retrieving recruiter 3-month performance: ${error.message}`,
        error.stack,
      );
      throw new InternalServerErrorException(
        `Error retrieving 3-month performance. ${error.message}`,
      );
    }
  }

  async getTrendAnalysis(user: any, filter: number = 3) {
    try {
      const userId = user._id.toString();
      console.log('userId', userId);

      const results = [];
      const months = [];
      const submissions = [];
      const interviews = [];
      const onboardings = [];
      for (let i = filter - 1; i >= 0; i--) {
        const startDate = moment()
          .subtract(i, 'months')
          .startOf('month')
          .toDate();
        const endDate = moment().subtract(i, 'months').endOf('month').toDate();
        // const monthLabel = moment(startDate).format('MMMM');
        const monthLabel = moment(startDate).format('MMM-YYYY').toUpperCase();

        // 1️⃣ Fetch active jobs
        const activeJobs = await this.jobModel
          .find({ isOpen: true, isDeleted: false })
          .select('_id')
          .exec();
        const activeJobIds = activeJobs.map((job) => job._id.toString());
        // console.log("activeJobIds", activeJobIds)

        // 2️⃣ Recruiter applications for this month
        const jobApplications = await this.jobApplicationModel
          .find({
            createdBy: userId,
            jobId: { $in: activeJobIds },
            isRejected: false,
            createdAt: { $gte: startDate, $lte: endDate },
          })
          .populate({
            path: 'stage',
            select: '_id name type jobApplicationsCount',
            model: 'Stage',
          })
          .select('_id stage isScreenSelected')
          .exec();

        const totalApplications = jobApplications.length;
        const jobApplicationIds = jobApplications.map((app) =>
          app._id.toString(),
        );

        console.log('Submissions', jobApplications);
        // const screeningCount = jobApplications.filter(app =>
        //   (app.stage as any)?.type === "workflow.screening"
        // ).length;

        // const sourcingCount = jobApplications.filter(app =>
        //   (app.stage as any)?.type === "workflow.sourcing"
        // ).length;

        // 4️⃣ Interviews showUp ratio
        const latestScheduledInterviews = await this.interviewModel.aggregate([
          {
            $match: {
              jobApplication: { $in: jobApplicationIds },
              createdAt: { $gte: startDate, $lte: endDate },
            },
          },
          // { $sort: { createdAt: -1 } },
          // {
          //   $group: {
          //     _id: "$jobApplication",
          //     latestInterview: { $first: "$$ROOT" }
          //   }
          // }
        ]);
        console.log(latestScheduledInterviews);
        const totalInterviewsScheduled = latestScheduledInterviews.length;

        // 3️⃣ Latest offers
        let applicationsWithOffers = 0;
        let onboardedApplications = 0;
        let acceptedOffers = 0;

        if (jobApplicationIds.length) {
          const latestOffers = await this.offerModel.aggregate([
            {
              $match: {
                jobApplication: { $in: jobApplicationIds },
                createdAt: { $gte: startDate, $lte: endDate },
              },
            },
            { $sort: { createdAt: -1 } },
            {
              $group: {
                _id: '$jobApplication',
                latestOffer: { $first: '$$ROOT' },
              },
            },
          ]);
          console.log(latestOffers);

          applicationsWithOffers = latestOffers.length;
          onboardedApplications = latestOffers.filter(
            (offer) => offer.latestOffer.isOnBoarded,
          ).length;
          acceptedOffers = latestOffers.filter(
            (offer) => offer.latestOffer.isAccepted,
          ).length;
        }
        // const offerAcceptanceRatio = Number(((acceptedOffers / applicationsWithOffers) * 100).toFixed(2));
        // const onboardingRatio = Number(((onboardedApplications / applicationsWithOffers) * 100).toFixed(2));
        months.push(monthLabel);
        submissions.push(totalApplications);
        interviews.push(totalInterviewsScheduled);
        onboardings.push(onboardedApplications);

        // results.push({
        //   month: monthLabel,
        //   submissions: totalApplications,
        //   interviews: totalInterviewsScheduled,
        //   onboardings: onboardedApplications,
        // });
      }

      // return results;
      return {
        months,
        submissions,
        interviews,
        onboardings,
      };
    } catch (error) {
      this.logger.error(
        `Error retrieving recruiter 3-month performance: ${error.message}`,
        error.stack,
      );
      throw new InternalServerErrorException(
        `Error retrieving 3-month performance. ${error.message}`,
      );
    }
  }

  async getRecruiterPerformanceReport(
    user: any,
    startDateInput?: string | Date,
    endDateInput?: string | Date,
  ) {
    try {
      const userId = user._id.toString();
      console.log('userId', userId);

      // const startDate = moment(startDateInput).startOf('day').toDate();
      // const endDate = moment(endDateInput).endOf('day').toDate();
      const recruiterDetails = await this.basicUserModel.findById(userId);
      console.log('recruiterDetails', recruiterDetails);

      const startDate = startDateInput
        ? new Date(startDateInput)
        : moment().subtract(7, 'days').startOf('day').toDate();
      // const endDate = endDateInput
      //   ? new Date(endDateInput)
      //   : moment().endOf('day').toDate();
      const endDate = endDateInput
        ? moment(endDateInput).endOf('day').toDate()
        : moment().endOf('day').toDate();
      console.log('startDate', startDate);
      console.log('endDate', endDate);
      // 1️⃣ Fetch active jobs
      const activeJobs = await this.jobModel
        .find({ isOpen: true, isDeleted: false })
        .select('_id')
        .exec();
      const activeJobIds = activeJobs.map((job) => job._id.toString());
      // console.log("activeJobIds", activeJobIds)

      // 2️⃣ Recruiter applications for this month
      const jobApplicationsWithOutDate = await this.jobApplicationModel
        .find({
          createdBy: userId,
          jobId: { $in: activeJobIds },
          isRejected: false,
        })
        .populate({
          path: 'stage',
          select: '_id name type jobApplicationsCount',
          model: 'Stage',
        })
        .select('_id stage isScreenSelected')
        .exec();

      const totalApplicationsWithOutDate = jobApplicationsWithOutDate.length;
      const jobApplicationIdsWithOutDate = jobApplicationsWithOutDate.map(
        (app) => app._id.toString(),
      );

      // 2️⃣ Recruiter applications for this month
      const jobApplications = await this.jobApplicationModel
        .find({
          createdBy: userId,
          jobId: { $in: activeJobIds },
          isRejected: false,
          createdAt: { $gte: startDate, $lte: endDate },
        })
        .populate({
          path: 'stage',
          select: '_id name type jobApplicationsCount',
          model: 'Stage',
        })
        .select('_id stage isScreenSelected')
        .exec();

      const totalApplications = jobApplications.length;
      const jobApplicationIds = jobApplications.map((app) =>
        app._id.toString(),
      );

      console.log('jobApplications', jobApplications);

      const screeningCount = jobApplications.filter(
        (app) => app.isScreenSelected,
      ).length;

      // console.log("sourcingCount", sourcingCount)
      console.log('screeningCount', screeningCount);
      // const screenSelectRatio = Number(((screeningCount / sourcingCount) * 100).toFixed(2));

      // 4️⃣ Interviews showUp ratio
      const latestScheduledInterviews = await this.interviewModel.aggregate([
        {
          $match: {
            jobApplication: { $in: jobApplicationIdsWithOutDate },
            createdAt: { $gte: startDate, $lte: endDate },
          },
        },
        // { $sort: { createdAt: -1 } },
        // {
        //   $group: {
        //     _id: "$jobApplication",
        //     latestInterview: { $first: "$$ROOT" }
        //   }
        // }
      ]);
      console.log(latestScheduledInterviews);
      const totalInterviewsScheduled = latestScheduledInterviews.length;
      const totalAttendedInterviews = latestScheduledInterviews.filter(
        (interview) => interview.isCandidateAttended,
      ).length;

      // 3️⃣ Latest offers
      let applicationsWithOffers = 0;
      let onboardedApplications = 0;
      let acceptedOffers = 0;

      if (jobApplicationIds.length) {
        const latestOffers = await this.offerModel.aggregate([
          {
            $match: {
              jobApplication: { $in: jobApplicationIdsWithOutDate },
              createdAt: { $gte: startDate, $lte: endDate },
            },
          },
          { $sort: { createdAt: -1 } },
          {
            $group: {
              _id: '$jobApplication',
              latestOffer: { $first: '$$ROOT' },
            },
          },
        ]);
        console.log(latestOffers);

        applicationsWithOffers = latestOffers.length;
        onboardedApplications = latestOffers.filter(
          (offer) => offer.latestOffer.isOnBoarded,
        ).length;
        acceptedOffers = latestOffers.filter(
          (offer) => offer.latestOffer.isAccepted,
        ).length;
      }

      const turnArountTime = await this.jobAllocationModel.aggregate([
        {
          $match: {
            assignee: userId,
            kind: 'JobAllocationToAssignees',
            createdAt: { $gte: startDate, $lte: endDate },
          },
        },
        {
          $group: {
            _id: '$job',
            assignedTime: { $first: '$createdAt' }, // Get the assigned time for the job
          },
        },
        {
          $lookup: {
            from: 'jobapplications', // Your actual Mongo collection name
            let: { jobId: '$_id' },
            pipeline: [
              {
                $match: {
                  $expr: {
                    $and: [
                      { $eq: ['$jobId', '$$jobId'] },
                      { $eq: ['$createdBy', userId] }, // 🔥 match creator with assignee
                    ],
                  },
                },
              },
              { $sort: { createdAt: 1 } },
              { $limit: 1 },
            ],
            as: 'firstApplication',
          },
        },
        {
          $unwind: {
            path: '$firstApplication',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $addFields: {
            timeDifferenceInMs: {
              $cond: {
                if: { $ifNull: ['$firstApplication', false] },
                then: {
                  $subtract: ['$firstApplication.createdAt', '$assignedTime'],
                },
                else: 0, // Treat jobs with no application as 0 ms
              },
            },
          },
        },
        {
          $match: {
            firstApplication: { $ne: null },
          },
        },
        {
          $group: {
            _id: null,
            averageTimeInMs: { $avg: '$timeDifferenceInMs' },
            totalJobs: { $sum: 1 },
          },
        },
        // {
        //   $project: {
        //     _id: 0,
        //     averageTimeInMs: 1,
        //     averageTimeInHours: {
        //       $round: [{ $divide: ["$averageTimeInMs", 1000 * 60 * 60] }, 2]
        //     },
        //     totalJobs: 1
        //   }
        // }
        {
          $project: {
            _id: 0,
            averageTimeInMs: 1,
            totalJobs: 1,
            days: {
              $floor: { $divide: ['$averageTimeInMs', 1000 * 60 * 60 * 24] },
            },
            hours: {
              $mod: [
                { $floor: { $divide: ['$averageTimeInMs', 1000 * 60 * 60] } },
                24,
              ],
            },
            minutes: {
              $mod: [
                { $floor: { $divide: ['$averageTimeInMs', 1000 * 60] } },
                60,
              ],
            },
          },
        },
      ]);

      const tat = turnArountTime[0] ?? {};
      const days = tat.days ?? 0;
      const hours = tat.hours ?? 0;
      const minutes = tat.minutes ?? 0;

      const formattedTAT =
        [
          days > 0 ? `${days}d` : null,
          hours > 0 ? `${hours}hrs` : null,
          minutes > 0 ? `${minutes}mins` : null,
        ]
          .filter(Boolean)
          .join(' ') || '0m';

      // const formattedTAT = `${days}d ${hours} hrs ${minutes} mins`;

      console.log('turnArountTime', turnArountTime);
      const ratioAnalysis = {
        totalApplications: totalApplications ?? 0,
        screenSelect: screeningCount ?? 0,
        InterviewShowUp: totalAttendedInterviews ?? 0,
        offerAcceptance: acceptedOffers ?? 0,
        onboarding: onboardedApplications ?? 0,
        // turnArountTime: turnArountTime[0]?.averageTimeInHours ?? 0,
        turnArountTime: formattedTAT ?? 0,
      };

      // Convert to moment for easy month manipulation
      const startMonth = moment(startDate).startOf('month');
      const endMonth = moment(endDate).startOf('month');

      // Generate list of months in "YYYY-MM" format between start and end
      const monthList: string[] = [];
      const current = startMonth.clone();
      while (current.isSameOrBefore(endMonth)) {
        monthList.push(current.format('YYYY-MM'));
        current.add(1, 'month');
      }

      // Query the recruiterTargets collection
      const targets = await this.recruiterTargetModel.find({
        recruiterId: userId,
        targetMonth: { $in: monthList },
      });

      // Sum up the targetNumber
      const totalTarget = targets.reduce(
        (sum, t) => sum + (t.targetNumber || 0),
        0,
      );

      const summary = {
        name: recruiterDetails?.firstName + ' ' + recruiterDetails?.lastName,
        doj: recruiterDetails?.createdAt,
        designation: recruiterDetails?.roles ?? [],
        cumulativeTarget: totalTarget ?? 0,
        cumulativeAchieved: onboardedApplications ?? 0,
        cumulativeBacklog: totalTarget - (onboardedApplications ?? 0),
      };

      const latestOffers = await this.offerModel.aggregate([
        {
          $match: {
            jobApplication: { $in: jobApplicationIdsWithOutDate },
            createdAt: { $gte: startDate, $lte: endDate },
            isOnBoarded: true, // ✅ Only onboarded offers
          },
        },
        { $sort: { createdAt: -1 } },
        {
          $group: {
            _id: '$jobApplication',
            latestOffer: { $first: '$$ROOT' },
          },
        },
        { $replaceRoot: { newRoot: '$latestOffer' } }, // Flatten result
        { $addFields: { jobApplication: { $toObjectId: '$jobApplication' } } },
        {
          $lookup: {
            from: 'jobapplications', // 👈 Ensure this matches the actual collection name
            localField: 'jobApplication',
            foreignField: '_id',
            as: 'jobApplicationDetails',
          },
        },
        { $unwind: '$jobApplicationDetails' },
        {
          $addFields: {
            jobId: { $toObjectId: '$jobApplicationDetails.jobId' },
          },
        },
        {
          $lookup: {
            from: 'jobs', // 👈 Ensure this matches your job collection name
            localField: 'jobId',
            foreignField: '_id',
            as: 'jobDetails',
          },
        },
        { $unwind: '$jobDetails' }, // Optional: only if you want flat job data
        {
          $addFields: {
            endClientOrg: { $toObjectId: '$jobDetails.endClientOrg' },
          },
        },
        {
          $lookup: {
            from: 'orgs', // 👈 Replace with your actual collection name for end clients
            localField: 'endClientOrg',
            foreignField: '_id',
            as: 'jobDetails.endclientOrgDetails',
          },
        },
        {
          $unwind: {
            path: '$jobDetails.endclientOrgDetails',
            preserveNullAndEmptyArrays: true, // Optional: in case endclientOrg is null/missing
          },
        },
      ]);

      console.log('latestOffersUnwind', latestOffers);

      return {
        ratioAnalysis,
        summary,
        latestOffers,
      };
    } catch (error) {
      this.logger.error(
        `Error retrieving recruiter performance report: ${error.message}`,
        error.stack,
      );
      throw new InternalServerErrorException(
        `Error retrieving performance report. ${error.message}`,
      );
    }
  }

  async getMonthlyStats(userId: string, orgId: string) {
    try {
      const currentDate = moment(); // Current date
      const monthsToFetch: string[] = [];

      // ✅ Calculate the current and past 3 months (handles year transition)
      for (let i = 0; i < 4; i++) {
        monthsToFetch.push(
          currentDate.clone().subtract(i, 'months').format('YYYY-MM'),
        );
      }

      // Fetch applications for the past 4 months for which only offers released by Offers collection
      const applications = await this.jobApplicationModel.aggregate([
        {
          $match: {
            createdBy: userId,
            // createdAt: {
            //   $gte: moment(monthsToFetch[3]).startOf('month').toDate(),// Oldest month start
            //   $lte: moment(monthsToFetch[0]).endOf('month').toDate(),// Latest month end
            // },
            isRejected: false,
          },
        },
        { $addFields: { _id: { $toString: '$_id' } } },
        {
          $lookup: {
            from: 'offers', // Collection name of the stage model
            localField: '_id',
            foreignField: 'jobApplication',
            as: 'offerDetails',
          },
        },
        {
          $addFields: {
            offerDetails: {
              $filter: {
                input: '$offerDetails',
                as: 'offer',
                cond: {
                  $and: [
                    { $eq: ['$$offer.isOnBoarded', true] },
                    {
                      $and: [
                        {
                          $gte: [
                            '$$offer.createdAt',
                            moment(monthsToFetch[3]).startOf('month').toDate(),
                          ],
                        },
                        {
                          $lte: [
                            '$$offer.createdAt',
                            moment(monthsToFetch[0]).endOf('month').toDate(),
                          ],
                        },
                      ],
                    },
                  ],
                },
              },
            },
          },
        },
        {
          $match: {
            'offerDetails.0': { $exists: true }, // Ensures `offerDetails` is not an empty array
          },
        },
        {
          $group: {
            _id: '$offerDetails.jobApplication',
            latestOffer: { $first: '$$ROOT' },
          },
        },
        { $replaceRoot: { newRoot: '$latestOffer' } },
      ]);

      console.log(applications);

      // Fetch recruiter targets for the past 4 months
      const recruiterTargets = await this.recruiterTargetModel
        .find({
          org: orgId,
          recruiterId: userId,
          targetMonth: { $in: monthsToFetch }, // Fetch only relevant months
        })
        .sort({ updatedAt: -1 });

      // ✅ Group applications by month
      const applicationCountByMonth: Record<string, number> = {};
      applications.forEach((app) => {
        const month = moment((app as any).createdAt).format('YYYY-MM');
        applicationCountByMonth[month] =
          (applicationCountByMonth[month] || 0) + 1;
      });

      // ✅ Keep only the most recent target record for each month
      const latestTargets: Record<string, number> = {};
      recruiterTargets.forEach((target) => {
        if (!latestTargets[target.targetMonth]) {
          latestTargets[target.targetMonth] = target.targetNumber;
        }
      });

      // ✅ Convert result into an array of objects (only for the past 4 months)
      const result = monthsToFetch.map((monthKey) => {
        const dateObj = moment(monthKey, 'YYYY-MM');
        return {
          year: dateObj.year(), // Extract year
          month: dateObj.format('MMM').toUpperCase(), // Convert month name (e.g., "FEB")
          profilesSubmitted: applicationCountByMonth[monthKey] || 0,
          target: latestTargets[monthKey] || 0,
        };
      });

      return result; // ✅ Returns an array of objects for past 4 months
    } catch (error) {
      throw new InternalServerErrorException(
        `Error retrieving monthly stats: ${error.message}`,
      );
    }
  }

  async softDeleteMany(jobApplicationIds: string[]) {
    // this.logger.log(JSON.stringify(jobApplicationIds))
    try {
      const result = await this.jobApplicationModel.updateMany(
        { _id: { $in: jobApplicationIds } },
        { $set: { isDeleted: true } },
      );

      if (result.modifiedCount === 0) {
        throw new NotFoundException(
          'No matching job applications found or already deleted.',
        );
      }

      return {
        message: 'Job applications marked deleted successfully.',
        modifiedCount: result.modifiedCount,
      };
    } catch (error) {
      this.logger.error(
        `Error in bulk soft delete: ${error.message}`,
        error.stack,
      );
      throw new InternalServerErrorException(
        `Failed to soft delete job applications. ${error.message}`,
      );
    }
  }

  groupByDate(applications: any[]) {
    return applications.reduce((acc: Record<string, any[]>, app: any) => {
      const date = new Date(app.createdAt).toISOString().split('T')[0];
      if (!acc[date]) {
        acc[date] = [];
      }
      acc[date].push(app);
      return acc;
    }, {});
  }

  groupBySource(applications: any[]) {
    return applications.reduce((acc: Record<string, any[]>, app: any) => {
      const roles = app.createdBy?.roles || [];

      let source = 'team'; // Default to "team" if no specific role is matched

      if (roles.includes(Role.Freelancer)) {
        source = 'freelancer';
      } else if (roles.includes(Role.Vendor)) {
        source = 'vendor';
      }

      if (!acc[source]) {
        acc[source] = [];
      }
      acc[source].push(app);
      return acc;
    }, {});
  }

  async getJobApplicationsByOrg(
    orgId: string,
    filters: CustomFilterDto,
    groupBy?: string,
  ) {
    validateObjectId(orgId);

    const {
      sortBySourceType,
      fromDate,
      toDate,
      minCurrentCTC,
      maxCurrentCTC,
      minExpectedCTC,
      maxExpectedCTC,
      gender,
      noticePeriodDays,
      ctcPercentage,
      country,
      state,
      city,
      isRejected,
      searchTerm,
    } = filters;

    const page = filters.page ?? 1;
    const limit = filters.limit ?? 10;

    const jobs = await this.jobModel.find({ postingOrg: orgId }).select('_id');
    const jobIds = jobs.map((job) => job._id.toString());

    // ✅ Base Query (Pre-filtered)
    const query: any = { jobId: { $in: jobIds } };

    if (sortBySourceType) {
      const userRoleFilter =
        sortBySourceType === Source.VENDOR ? Role.Vendor : Role.Freelancer;
      query['createdByDetails.roles'] = { $in: [userRoleFilter] };
    }

    if (fromDate || toDate) {
      query.createdAt = {};
      if (fromDate) query.createdAt.$gte = new Date(fromDate);
      if (toDate) query.createdAt.$lte = new Date(toDate);
    }

    if (minCurrentCTC !== undefined) {
      query.currentCTC = { $gte: minCurrentCTC };
    }

    if (maxCurrentCTC !== undefined) {
      query.currentCTC = { $lte: maxCurrentCTC };
    }

    if (minExpectedCTC !== undefined) {
      query.expectedCTC = { $gte: minExpectedCTC };
    }

    if (maxExpectedCTC !== undefined) {
      query.expectedCTC = { $gte: maxExpectedCTC };
    }

    if (ctcPercentage !== undefined) {
      query.ctcPercentage = { $gte: ctcPercentage };
    }

    if (gender) {
      query.gender = gender;
    }

    if (noticePeriodDays !== undefined) {
      query.noticePeriodDays = { $lte: noticePeriodDays };
    }

    if (country) query['countryDetails._id'] = new Types.ObjectId(country);
    if (state) query['stateDetails._id'] = new Types.ObjectId(state);
    if (city) query['cityDetails._id'] = new Types.ObjectId(city);

    if (isRejected !== undefined) {
      query.isRejected = isRejected;
    }

    if (filters.stageType) {
      query['stageDetails.type'] = filters.stageType;
    }

    const searchRegex = searchTerm ? new RegExp(searchTerm, 'i') : null;
    const searchPattern = searchTerm || '';
    const regexOptions = 'i';

    // this.logger.log(JSON.stringify(query))
    let jobApplications = await this.jobApplicationModel.aggregate([
      {
        $lookup: {
          from: 'basicusers', // Collection name in MongoDB
          let: { userId: { $toObjectId: '$createdBy' } }, // Convert string to ObjectId
          pipeline: [
            { $match: { $expr: { $eq: ['$_id', '$$userId'] } } },
            { $project: { _id: 1, firstName: 1, roles: 1 } }, // Select only necessary fields
          ],
          as: 'createdByDetails',
        },
      },
      {
        $unwind: {
          path: '$createdByDetails',
          preserveNullAndEmptyArrays: true,
        },
      },

      {
        $lookup: {
          from: 'stages',
          let: { stageId: { $toObjectId: '$stage' } },
          pipeline: [
            { $match: { $expr: { $eq: ['$_id', '$$stageId'] } } },
            { $project: { _id: 1, name: 1, type: 1, jobApplicationsCount: 1 } },
          ],
          as: 'stageDetails',
        },
      },
      { $unwind: { path: '$stageDetails', preserveNullAndEmptyArrays: true } },
      {
        $lookup: {
          from: 'jobs',
          let: { jobId: { $toObjectId: '$jobId' } },
          pipeline: [
            { $match: { $expr: { $eq: ['$_id', '$$jobId'] } } },
            {
              $lookup: {
                from: 'orgs',
                let: { postingOrgId: { $toObjectId: '$postingOrg' } },
                pipeline: [
                  { $match: { $expr: { $eq: ['$_id', '$$postingOrgId'] } } },
                  { $project: { _id: 1, title: 1, orgType: 1 } },
                ],
                as: 'postingOrgDetails',
              },
            },
            {
              $unwind: {
                path: '$postingOrgDetails',
                preserveNullAndEmptyArrays: true,
              },
            },
            {
              $lookup: {
                from: 'orgs',
                let: { endClientOrgId: { $toObjectId: '$endClientOrg' } },
                pipeline: [
                  { $match: { $expr: { $eq: ['$_id', '$$endClientOrgId'] } } },
                  { $project: { _id: 1, title: 1, orgType: 1 } },
                ],
                as: 'endClientOrgDetails',
              },
            },
            {
              $unwind: {
                path: '$endClientOrgDetails',
                preserveNullAndEmptyArrays: true,
              },
            },
            {
              $project: {
                _id: 1,
                title: 1,
                postingOrg: {
                  _id: '$postingOrgDetails._id',
                  title: '$postingOrgDetails.title',
                  orgType: '$postingOrgDetails.orgType',
                },
                endClientOrg: {
                  _id: '$endClientOrgDetails._id',
                  title: '$endClientOrgDetails.title',
                  orgType: '$endClientOrgDetails.orgType',
                },
              },
            },
          ],
          as: 'jobDetails',
        },
      },
      { $unwind: { path: '$jobDetails', preserveNullAndEmptyArrays: true } },

      {
        $lookup: {
          from: 'filemetadatas',
          let: { resumeMetadata: { $toObjectId: '$resumeMetadata' } }, // Convert to ObjectId
          pipeline: [
            { $match: { $expr: { $eq: ['$_id', '$$resumeMetadata'] } } },
            {
              $project: {
                _id: 1,
                originalName: 1,
                fileSize: 1,
                fileType: 1,
                locationUrl: 1,
              },
            },
          ],
          as: 'resumeMetadataDetails',
        },
      },
      {
        $lookup: {
          from: 'evaluationforms',
          let: {
            evaluationFormIds: {
              $map: {
                input: '$evaluationForm',
                as: 'id',
                in: { $toObjectId: '$$id' },
              },
            },
          }, // Convert array of string IDs to ObjectId
          pipeline: [
            { $match: { $expr: { $in: ['$_id', '$$evaluationFormIds'] } } },
            {
              $project: {
                _id: 1,
                skill: 1,
                years: 1,
                months: 1,
                rating: 1,
                isPrimary: 1,
              },
            }, // Select necessary fields
          ],
          as: 'evaluationFormDetails',
        },
      },
      {
        $lookup: {
          from: 'countries',
          let: { countryId: { $toObjectId: '$country' } }, // Convert to ObjectId
          pipeline: [{ $match: { $expr: { $eq: ['$_id', '$$countryId'] } } }],
          as: 'countryDetails',
        },
      },
      {
        $unwind: { path: '$countryDetails', preserveNullAndEmptyArrays: true },
      },
      {
        $lookup: {
          from: 'states',
          let: { stateId: { $toObjectId: '$state' } }, // Convert to ObjectId
          pipeline: [{ $match: { $expr: { $eq: ['$_id', '$$stateId'] } } }],
          as: 'stateDetails',
        },
      },
      { $unwind: { path: '$stateDetails', preserveNullAndEmptyArrays: true } },
      {
        $lookup: {
          from: 'cities',
          let: { cityId: { $toObjectId: '$city' } }, // Convert to ObjectId
          pipeline: [{ $match: { $expr: { $eq: ['$_id', '$$cityId'] } } }],
          as: 'cityDetails',
        },
      },
      { $unwind: { path: '$cityDetails', preserveNullAndEmptyArrays: true } },
      { $match: query },
      ...(searchRegex
        ? [
            {
              $match: {
                $or: [
                  { 'jobDetails.title': searchRegex },
                  { firstName: searchRegex },
                  { lastName: searchRegex },
                  { 'contactDetails.contactEmail': searchRegex },
                  { 'contactDetails.contactNumber': searchRegex },
                  { 'contactAddress.street': searchRegex },
                  { 'contactAddress.postalCode': searchRegex },
                  { currentLocation: searchRegex },
                  { 'countryDetails.countryName': searchRegex },
                  { 'stateDetails.stateName': searchRegex },
                  { 'cityDetails.name': searchRegex },
                  { 'evaluationFormDetails.skill': searchRegex },
                  {
                    $expr: {
                      $regexMatch: {
                        input: { $concat: ['$firstName', ' ', '$lastName'] },
                        regex: searchPattern,
                        options: regexOptions,
                      },
                    },
                  },
                ],
              },
            },
          ]
        : []),
      { $sort: { createdAt: -1 } }, // 🔥 Sort latest first
      { $skip: (page - 1) * limit },
      { $limit: limit },

      {
        $project: {
          _id: 1,
          firstName: 1,
          lastName: 1,
          contactDetails: 1,
          contactAddress: 1,
          currentLocation: 1,
          currentCTC: 1,
          expectedCTC: 1,
          gender: 1,
          noticePeriodDays: 1,
          createdAt: 1,
          updatedAt: 1,
          ctcPercentage: 1,
          companyNorms: 1,
          evaluationForm: '$evaluationFormDetails',
          country: {
            _id: '$countryDetails._id',
            countryName: '$countryDetails.countryName',
            countryPhoneCode: '$countryDetails.countryPhoneCode',
          },
          state: {
            _id: '$stateDetails._id',
            stateName: '$stateDetails.stateName',
          },
          city: {
            _id: '$cityDetails._id',
            name: '$cityDetails.name',
          },
          createdBy: {
            _id: '$createdByDetails._id',
            firstName: '$createdByDetails.firstName',
            roles: '$createdByDetails.roles',
          },
          stage: {
            _id: '$stageDetails._id',
            name: '$stageDetails.name',
            type: '$stageDetails.type',
            jobApplicationsCount: '$stageDetails.jobApplicationsCount',
          },
          jobId: {
            _id: '$jobDetails._id',
            title: '$jobDetails.title',
            postingOrg: '$jobDetails.postingOrg',
            endClientOrg: '$jobDetails.endClientOrg',
          },
          isRejected: 1,
        },
      },
    ]);

    if (groupBy === GroupByType.DATE) {
      const groupedApplications = this.groupByDate(jobApplications);
      jobApplications = groupedApplications;
    }

    if (groupBy === GroupByType.SOURCE) {
      const groupedApplications = this.groupBySource(jobApplications);
      jobApplications = groupedApplications;
    }

    const jobApplicationsCount = await this.jobApplicationModel
      .aggregate([
        {
          $lookup: {
            from: 'jobs',
            let: { jobId: { $toObjectId: '$jobId' } },
            pipeline: [
              { $match: { $expr: { $eq: ['$_id', '$$jobId'] } } },
              {
                $project: { _id: 1, title: 1 },
              },
            ],
            as: 'jobDetails',
          },
        },
        { $unwind: { path: '$jobDetails', preserveNullAndEmptyArrays: true } },
        {
          $lookup: {
            from: 'basicusers', // Collection name in MongoDB
            let: { userId: { $toObjectId: '$createdBy' } }, // Convert string to ObjectId
            pipeline: [
              { $match: { $expr: { $eq: ['$_id', '$$userId'] } } },
              { $project: { _id: 1, firstName: 1, roles: 1 } }, // Select only necessary fields
            ],
            as: 'createdByDetails',
          },
        },
        {
          $unwind: {
            path: '$createdByDetails',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: 'stages',
            let: { stageId: { $toObjectId: '$stage' } },
            pipeline: [
              { $match: { $expr: { $eq: ['$_id', '$$stageId'] } } },
              { $project: { _id: 1, name: 1, type: 1 } },
            ],
            as: 'stageDetails',
          },
        },
        {
          $unwind: { path: '$stageDetails', preserveNullAndEmptyArrays: true },
        },
        {
          $lookup: {
            from: 'evaluationforms',
            let: {
              evaluationFormIds: {
                $map: {
                  input: '$evaluationForm',
                  as: 'id',
                  in: { $toObjectId: '$$id' },
                },
              },
            }, // Convert array of string IDs to ObjectId
            pipeline: [
              { $match: { $expr: { $in: ['$_id', '$$evaluationFormIds'] } } },
              {
                $project: {
                  _id: 1,
                  skill: 1,
                  years: 1,
                  months: 1,
                  rating: 1,
                  isPrimary: 1,
                },
              }, // Select necessary fields
            ],
            as: 'evaluationFormDetails',
          },
        },
        {
          $lookup: {
            from: 'countries',
            let: { countryId: { $toObjectId: '$country' } }, // Convert to ObjectId
            pipeline: [{ $match: { $expr: { $eq: ['$_id', '$$countryId'] } } }],
            as: 'countryDetails',
          },
        },
        {
          $unwind: {
            path: '$countryDetails',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: 'states',
            let: { stateId: { $toObjectId: '$state' } }, // Convert to ObjectId
            pipeline: [{ $match: { $expr: { $eq: ['$_id', '$$stateId'] } } }],
            as: 'stateDetails',
          },
        },
        {
          $unwind: { path: '$stateDetails', preserveNullAndEmptyArrays: true },
        },
        {
          $lookup: {
            from: 'cities',
            let: { cityId: { $toObjectId: '$city' } }, // Convert to ObjectId
            pipeline: [{ $match: { $expr: { $eq: ['$_id', '$$cityId'] } } }],
            as: 'cityDetails',
          },
        },
        { $unwind: { path: '$cityDetails', preserveNullAndEmptyArrays: true } },
        { $match: query },
        ...(searchRegex
          ? [
              {
                $match: {
                  $or: [
                    { 'jobDetails.title': searchRegex },
                    { firstName: searchRegex },
                    { lastName: searchRegex },
                    { 'contactDetails.contactEmail': searchRegex },
                    { 'contactDetails.contactNumber': searchRegex },
                    { 'contactAddress.street': searchRegex },
                    { 'contactAddress.postalCode': searchRegex },
                    { currentLocation: searchRegex },
                    { 'countryDetails.countryName': searchRegex },
                    { 'stateDetails.stateName': searchRegex },
                    { 'cityDetails.name': searchRegex },
                    { 'evaluationFormDetails.skill': searchRegex },
                  ],
                },
              },
            ]
          : []),

        { $count: 'totalCount' },
      ])
      .exec();

    // Extract the count from the result
    const totalCount =
      jobApplicationsCount.length > 0 ? jobApplicationsCount[0].totalCount : 0;

    return {
      total: totalCount,
      // dataLength: jobApplications.length,
      data: jobApplications,
    };
  }

  async groupJobApplications(
    orgId: string,
    groupBy: string,
    page: number,
    limit: number,
  ) {
    const skip = (page - 1) * limit;

    const jobs = await this.jobModel.find({ postingOrg: orgId }).select('_id');
    const jobIds = jobs.map((job) => job._id.toString()); // Extract job IDs

    let aggregationPipeline: any[] = [
      {
        $match: {
          jobId: { $in: jobIds },
        },
      },
      {
        $lookup: {
          from: 'basicusers', // Collection name in MongoDB
          let: { userId: { $toObjectId: '$createdBy' } }, // Convert string to ObjectId
          pipeline: [
            { $match: { $expr: { $eq: ['$_id', '$$userId'] } } },
            { $project: { _id: 1, firstName: 1, roles: 1 } }, // Select only necessary fields
          ],
          as: 'createdByDetails',
        },
      },
      {
        $unwind: {
          path: '$createdByDetails',
          preserveNullAndEmptyArrays: true,
        },
      },

      {
        $lookup: {
          from: 'stages',
          let: { stageId: { $toObjectId: '$stage' } },
          pipeline: [
            { $match: { $expr: { $eq: ['$_id', '$$stageId'] } } },
            { $project: { _id: 1, name: 1, type: 1, jobApplicationsCount: 1 } },
          ],
          as: 'stageDetails',
        },
      },
      { $unwind: { path: '$stageDetails', preserveNullAndEmptyArrays: true } },
      {
        $lookup: {
          from: 'jobs',
          let: { jobId: { $toObjectId: '$jobId' } },
          pipeline: [
            { $match: { $expr: { $eq: ['$_id', '$$jobId'] } } },
            {
              $lookup: {
                from: 'orgs',
                let: { postingOrgId: { $toObjectId: '$postingOrg' } },
                pipeline: [
                  { $match: { $expr: { $eq: ['$_id', '$$postingOrgId'] } } },
                  { $project: { _id: 1, title: 1, orgType: 1 } },
                ],
                as: 'postingOrgDetails',
              },
            },
            {
              $unwind: {
                path: '$postingOrgDetails',
                preserveNullAndEmptyArrays: true,
              },
            },
            {
              $lookup: {
                from: 'orgs',
                let: { endClientOrgId: { $toObjectId: '$endClientOrg' } },
                pipeline: [
                  { $match: { $expr: { $eq: ['$_id', '$$endClientOrgId'] } } },
                  { $project: { _id: 1, title: 1, orgType: 1 } },
                ],
                as: 'endClientOrgDetails',
              },
            },
            {
              $unwind: {
                path: '$endClientOrgDetails',
                preserveNullAndEmptyArrays: true,
              },
            },
            {
              $project: {
                _id: 1,
                title: 1,
                postingOrg: {
                  _id: '$postingOrgDetails._id',
                  title: '$postingOrgDetails.title',
                  orgType: '$postingOrgDetails.orgType',
                },
                endClientOrg: {
                  _id: '$endClientOrgDetails._id',
                  title: '$endClientOrgDetails.title',
                  orgType: '$endClientOrgDetails.orgType',
                },
              },
            },
          ],
          as: 'jobDetails',
        },
      },
      { $unwind: { path: '$jobDetails', preserveNullAndEmptyArrays: true } },

      {
        $lookup: {
          from: 'filemetadatas',
          let: { resumeMetadata: { $toObjectId: '$resumeMetadata' } }, // Convert to ObjectId
          pipeline: [
            { $match: { $expr: { $eq: ['$_id', '$$resumeMetadata'] } } },
            {
              $project: {
                _id: 1,
                originalName: 1,
                fileSize: 1,
                fileType: 1,
                locationUrl: 1,
              },
            },
          ],
          as: 'resumeMetadataDetails',
        },
      },
      {
        $lookup: {
          from: 'evaluationforms',
          let: {
            evaluationFormIds: {
              $map: {
                input: '$evaluationForm',
                as: 'id',
                in: { $toObjectId: '$$id' },
              },
            },
          }, // Convert array of string IDs to ObjectId
          pipeline: [
            { $match: { $expr: { $in: ['$_id', '$$evaluationFormIds'] } } },
            {
              $project: {
                _id: 1,
                skill: 1,
                years: 1,
                months: 1,
                rating: 1,
                isPrimary: 1,
              },
            }, // Select necessary fields
          ],
          as: 'evaluationFormDetails',
        },
      },
      {
        $lookup: {
          from: 'countries',
          let: { countryId: { $toObjectId: '$country' } }, // Convert to ObjectId
          pipeline: [{ $match: { $expr: { $eq: ['$_id', '$$countryId'] } } }],
          as: 'countryDetails',
        },
      },
      {
        $unwind: { path: '$countryDetails', preserveNullAndEmptyArrays: true },
      },
      {
        $lookup: {
          from: 'states',
          let: { stateId: { $toObjectId: '$state' } }, // Convert to ObjectId
          pipeline: [{ $match: { $expr: { $eq: ['$_id', '$$stateId'] } } }],
          as: 'stateDetails',
        },
      },
      { $unwind: { path: '$stateDetails', preserveNullAndEmptyArrays: true } },
      {
        $lookup: {
          from: 'cities',
          let: { cityId: { $toObjectId: '$city' } }, // Convert to ObjectId
          pipeline: [{ $match: { $expr: { $eq: ['$_id', '$$cityId'] } } }],
          as: 'cityDetails',
        },
      },
      { $unwind: { path: '$cityDetails', preserveNullAndEmptyArrays: true } },
      {
        $project: {
          _id: 1,
          firstName: 1,
          lastName: 1,
          contactDetails: 1,
          contactAddress: 1,
          currentLocation: 1,
          currentCTC: 1,
          expectedCTC: 1,
          gender: 1,
          noticePeriodDays: 1,
          createdAt: 1,
          updatedAt: 1,
          ctcPercentage: 1,
          evaluationForm: '$evaluationFormDetails',
          country: {
            _id: '$countryDetails._id',
            countryName: '$countryDetails.countryName',
            countryPhoneCode: '$countryDetails.countryPhoneCode',
          },
          state: {
            _id: '$stateDetails._id',
            stateName: '$stateDetails.stateName',
          },
          city: {
            _id: '$cityDetails._id',
            name: '$cityDetails.name',
          },
          createdBy: {
            _id: '$createdByDetails._id',
            firstName: '$createdByDetails.firstName',
            roles: '$createdByDetails.roles',
          },
          stage: {
            _id: '$stageDetails._id',
            name: '$stageDetails.name',
            type: '$stageDetails.type',
            jobApplicationsCount: '$stageDetails.jobApplicationsCount',
          },
          jobId: {
            _id: '$jobDetails._id',
            title: '$jobDetails.title',
            postingOrg: '$jobDetails.postingOrg',
            endClientOrg: '$jobDetails.endClientOrg',
          },
          isRejected: 1,
        },
      },
    ];

    // Grouping logic
    if (groupBy === 'date') {
      aggregationPipeline.push(
        {
          $addFields: {
            formattedDate: {
              $dateToString: { format: '%Y-%m-%d', date: '$createdAt' },
            },
          },
        },
        {
          $group: {
            _id: '$formattedDate',
            applications: { $push: '$$ROOT' },
          },
        },
        { $sort: { _id: -1 } },
        { $skip: skip },
        { $limit: limit },
      );
    } else if (groupBy === 'source') {
      aggregationPipeline.push(
        {
          $addFields: {
            sourceGroup: {
              $cond: [
                { $in: [Role.Vendor, '$createdBy.roles'] },
                'Vendor',
                {
                  $cond: [
                    { $in: [Role.Freelancer, '$createdBy.roles'] },
                    'Freelancer',
                    'Team',
                  ],
                },
              ],
            },
          },
        },
        {
          $group: {
            _id: '$sourceGroup',
            applications: { $push: '$$ROOT' },
          },
        },
        { $sort: { _id: -1 } },
        { $skip: skip },
        { $limit: limit },
      );
    } else {
      throw new BadRequestException(
        "Invalid 'groupBy' value. Use 'date' or 'source'.",
      );
    }

    const groupedData =
      await this.jobApplicationModel.aggregate(aggregationPipeline);
    return groupedData.reduce((acc, entry) => {
      acc[entry._id] = entry.applications;
      return acc;
    }, {});
  }
}
