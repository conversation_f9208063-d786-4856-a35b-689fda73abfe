import { ApiProperty } from "@nestjs/swagger";
import { Transform, TransformFnParams } from "class-transformer";
import { IsNotEmpty, IsNumber, IsString, Max, <PERSON> } from "class-validator";
import { sanitizeWithStyle } from "src/utils/sanitize-with-style";

export class CreateStateDto {

  @ApiProperty({
    type: Number,
    required: true,
    default: 5102,
    description: 'Id of the country',
  })
  @IsNotEmpty()
  @IsNumber()
  @Min(5102, { message: 'State ID must be at least 5102.' })
  @Max(10000, { message: 'State ID must not exceed 10000.' })
  stateId: number;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Name of the state',
  })
  @IsNotEmpty()
  @IsString()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  stateName: string;

  // @ApiProperty({
  //   type: String,
  //   required: true,
  //   description: 'Id of the country',
  // })
  // @IsNotEmpty()
  // @IsMongoId()
  // @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  // country: string;
}
