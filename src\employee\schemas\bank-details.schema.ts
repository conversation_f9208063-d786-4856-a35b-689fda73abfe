import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, HydratedDocument, Types } from 'mongoose';
import { BasicUser } from 'src/user/schemas/basic-user.schema';
import { AccountType } from 'src/shared/constants';
import { Employee } from './employee.schema';

export type BankDetailsDocument = HydratedDocument<BankDetails>;
@Schema({
  timestamps: true
})
export class BankDetails extends Document {
  @Prop({ type: String, required: true, trim: true })
  accountHolderName: string;

  @Prop({ type: String, required: true, trim: true })
  accountNumber: string;

  @Prop({ type: String, required: true, trim: true })
  ifscCode: string;

  @Prop({ type: String, required: true, trim: true })
  bankName: string;

  @Prop({ type: String, required: false, trim: true })
  branchName?: string;

  @Prop({
    type: String,
    required: false,
    trim: true,
    //default: EmploymentType.FullTime,
    enum: Object.values(AccountType),
  })
  accountType?: string;

  @Prop({ type: String, required: false, trim: true })
  upiId?: string;

  @Prop({ type: Types.ObjectId, ref: 'Employee', required: true })
  empId: Employee;

  @Prop({ type: Boolean, default: false })
  isDeleted?: boolean;

  @Prop({ type: Boolean, default: true })
  isActive?: boolean;

  @Prop({
    required: true,
    type: Types.ObjectId, ref: 'BasicUser'
  })
  createdBy: BasicUser;
}

export const BankDetailsSchema = SchemaFactory.createForClass(BankDetails);
