import { <PERSON>, Get, Res } from '@nestjs/common';
import { AppService } from './app.service';
import { ApiResponse, ApiTags } from '@nestjs/swagger';
import { Logger } from '@nestjs/common';
import * as countries from './public/data/countries/countries.json';

@Controller()
@ApiTags('Home')
export class AppController {
  private readonly logger = new Logger(AppController.name);

  constructor(private readonly appService: AppService) { }

  @Get()
  @ApiResponse({ status: 200, description: 'All is well.' })
  getHello(@Res() res: any): any {
    this.logger.log(`Hello world`);
    const htmlString = this.appService.getHello();
    return res.type('text/html').send(htmlString);
    // return this.appService.getHello();
  }

  // @Get('countries')
  // @ApiResponse({ status: 200, description: 'All is well.' })
  // getCountries(@Res() res: any): any {
  //   return countries;
  // }

}
