import { BadRequestException, HttpException, Inject, Injectable, InternalServerErrorException, Logger, NotFoundException, UnauthorizedException, forwardRef } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { UserService } from 'src/user/user.service';
import { JwtService } from '@nestjs/jwt';
import * as bcrypt from 'bcrypt';
import { CreateUserDto } from 'src/user/dto/create-user.dto';
import { ForgotPasswordDto } from 'src/user/dto/forgot-password.dto';
import { VerifyUuidDto } from 'src/user/dto/verify-uuid.dto';
import { ResetPasswordDto } from 'src/user/dto/reset-password.dto';
import { PasswordDto } from 'src/user/dto/password.dto';
import { Model, PopulateOptions, Types } from 'mongoose';
import { Role } from './enums/role.enum';
import { VerifyOtpDto } from 'src/user/dto/verify-otp.dto';
import { UserProfileDto } from './dto/user-profile.dto';
import axios from 'axios';
import { InjectModel } from '@nestjs/mongoose';
import { BasicUser } from 'src/user/schemas/basic-user.schema';
import * as jwt from 'jsonwebtoken';
import qs from 'qs';

@Injectable()
export class AuthService {

  private readonly logger = new Logger(AuthService.name);

  constructor(private configService: ConfigService, private jwtService: JwtService,
    @InjectModel(BasicUser.name) private basicUserModel: Model<BasicUser>,
    @Inject(forwardRef(() => UserService)) private userService: UserService) {

  }


  async findUserByEmail(email: string) {
    return await this.userService.findUserByEmail(email);
  }

  async verifyEmail(verifyUuidDto: VerifyUuidDto, userId?: Types.ObjectId) {
    return await this.userService.verifyEmail(verifyUuidDto, userId);
  }

  async verifyEmailByOtp(verifyOtpDto: VerifyOtpDto, userId?: Types.ObjectId) {
    return await this.userService.verifyEmailByOtp(verifyOtpDto, userId);
  }

  async createUser(createUserDto: CreateUserDto) {
    return this.userService.create(createUserDto);
  }

  async updateProfile(userId: Types.ObjectId, userProfileDto: UserProfileDto) {
    return this.userService.updateProfile(userId, userProfileDto);
  }

  async createAccessToken(payload: any) {
    try {
      const token = await this.jwtService.signAsync(payload, {
        secret: this.configService.get<string>('SECRET_KEY'),
        expiresIn: this.configService.get<string>('TOKEN_EXPIRY_PERIOD'),
      });
      return token;
    } catch (error) {
      throw new InternalServerErrorException(`Failed to create access token. ${error.message}`);
    }
  }



  async handleForgotPasswordRequest(forgotPasswordDto: ForgotPasswordDto) {
    return this.userService.handleForgotPasswordRequest(forgotPasswordDto);
  }

  async forgotPasswordVerify(verifyUuidDto: VerifyUuidDto) {
    return await this.userService.forgotPasswordVerify(verifyUuidDto);

  }

  async forgotPasswordVerifyByOtp(verifyOtpDto: VerifyOtpDto) {
    return await this.userService.forgotPasswordVerifyByOtp(verifyOtpDto);

  }

  async resetPassword(resetPasswordDto: ResetPasswordDto) {
    this.logger.log('authServce')
    return await this.userService.resetPassword(resetPasswordDto);

  }

  async updatePasswordAfterForget(passwordDto: PasswordDto) {
    if (!passwordDto.email) {
      throw new NotFoundException(`Email is required`);
    }
    const user: any = await this.findUserByEmail(passwordDto?.email);
    return this.userService.updatePassword(user._id, passwordDto);
  }

  async updatePassword(id: string, passwordDto: PasswordDto) {
    const userId = new Types.ObjectId(id);
    return this.userService.updatePassword(userId, passwordDto);

  }

  async signIn(
    email: string,
    pass: string,
  ): Promise<{ access_token: string, expiresIn: string | undefined, roles: Role[], id: string, orgAdmin?: string, companyId?: string | undefined, employeeId?: string | undefined }> {

    const user: any = await this.userService.findUserByEmail(email);

    if (!user) {
      throw new NotFoundException(`Invalid email or password.`);
    }

    if (!user.isVerified) {
      this.logger.debug(`User with email ${email} is not verified yet.`);
      throw new BadRequestException(`User email is not verified.`);
    }

    if (user.isSuspended) {
      this.logger.debug(`User with email ${email} is suspended`);
      throw new BadRequestException('User is suspended.');
    }

    if (user.isDeleted) {
      this.logger.debug(`User with email ${email} is deleted`);
      throw new BadRequestException('User is deleted.');
    }
    this.logger.log(pass);
    const isMatch = await bcrypt.compare(pass, user.password);
    // this.logger.log(isMatch);
    if (!isMatch) {
      throw new BadRequestException(`User must have entered invalid credentials.`);
    }

    // Find the admin for the user's organization
    let orgAdmin: any = null
    if (user.org) {
      orgAdmin = await this.userService.findAdminByOrgId(user.org._id);
      this.logger.log(orgAdmin)
    }

    const payload = {
      email: user.email,
      firstName: user.firstName,
      _id: user._id,
      roles: user.roles,
      org: user.org,
      businessUnit: user.businessUnit,
      userInboxConfig: user.userInboxConfig,
      logo: user.logo,
      companyId: user.companyId,
      orgAdmin: orgAdmin ? orgAdmin._id.toString() : undefined,
      adminUserInbox: orgAdmin ? orgAdmin?.userInboxConfig ?? undefined : undefined,
      isCustom: user.isCustom ? user.isCustom : false,
      employeeId: user.employeeId
    };

    const token = await this.jwtService.signAsync(payload, {
      // secret: 'SOME_SECRET_KEY',
      secret: this.configService.get<string>('SECRET_KEY'),
      expiresIn: this.configService.get<string>('TOKEN_EXPIRY_PERIOD')
    });

    this.logger.debug(`User with email ${email} signed in successfully`);

    return {
      //user role and country and user id 
      id: user.id,
      roles: user.roles,
      access_token: token,
      expiresIn: this.configService.get<string>('TOKEN_EXPIRY_PERIOD'),
      orgAdmin: orgAdmin ? orgAdmin._id.toString() : undefined
    };
  }

  async generateInterviewToken(
    email: string,
    expiresIn: string,
  ): Promise<{ access_token: string, expiresIn: string | undefined, roles: Role[], id: string, orgAdmin?: string, companyId?: string | undefined, employeeId?: string | undefined }> {

    const user: any = await this.userService.findUserByEmail(email);

    if (!user) {
      throw new NotFoundException(`Invalid email or password.`);
    }

    if (!user.isVerified) {
      this.logger.debug(`User with email ${email} is not verified yet.`);
      throw new BadRequestException(`User email is not verified.`);
    }

    if (user.isSuspended) {
      this.logger.debug(`User with email ${email} is suspended`);
      throw new BadRequestException('User is suspended.');
    }

    if (user.isDeleted) {
      this.logger.debug(`User with email ${email} is deleted`);
      throw new BadRequestException('User is deleted.');
    }

    // Find the admin for the user's organization
    let orgAdmin: any = null
    if (user.org) {
      orgAdmin = await this.userService.findAdminByOrgId(user.org._id);
      this.logger.log(orgAdmin)
    }

    const payload = {
      email: user.email,
      firstName: user.firstName,
      _id: user._id,
      roles: user.roles,
      org: user.org,
      businessUnit: user.businessUnit,
      userInboxConfig: user.userInboxConfig,
      logo: user.logo,
      companyId: user.companyId,
      orgAdmin: orgAdmin ? orgAdmin._id.toString() : undefined,
      adminUserInbox: orgAdmin ? orgAdmin?.userInboxConfig ?? undefined : undefined,
      isCustom: user.isCustom ? user.isCustom : false,
      employeeId: user.employeeId
    };

    const token = await this.jwtService.signAsync(payload, {
      // secret: 'SOME_SECRET_KEY',
      secret: this.configService.get<string>('SECRET_KEY'),
      expiresIn: expiresIn
    });

    this.logger.debug(`User with email ${email} signed in successfully`);

    return {
      //user role and country and user id 
      id: user.id,
      roles: user.roles,
      access_token: token,
      expiresIn: expiresIn,
      orgAdmin: orgAdmin ? orgAdmin._id.toString() : undefined
    };
  }

  async refreshToken(email: string): Promise<{ access_token: string, expiresIn: string | undefined, roles: Role[], id: string, orgAdmin?: string, companyId?: string | undefined }> {
    try {
      // Fetch the latest user information, including populated fields
      const user: any = await this.userService.findUserByEmail(email);

      if (!user) {
        throw new NotFoundException(`User not found.`);
      }

      // Fetch the admin for the user's organization (if applicable)
      let orgAdmin: any = null;
      if (user.org) {
        orgAdmin = await this.userService.findAdminByOrgId(user.org._id);
      }

      // Prepare the payload with the latest user information
      const payload = {
        email: user.email,
        firstName: user.firstName,
        _id: user._id,
        roles: user.roles,
        org: user.org,
        businessUnit: user.businessUnit,
        userInboxConfig: user.userInboxConfig, // Updated userInboxConfig
        logo: user.logo,
        companyId: user.companyId,
        employeeId: user.employeeId,
        orgAdmin: orgAdmin ? orgAdmin._id.toString() : undefined,
        adminUserInbox: orgAdmin ? orgAdmin?.userInboxConfig ?? undefined : undefined,
      };

      // Generate a new token with the updated payload
      const token = await this.jwtService.signAsync(payload, {
        secret: this.configService.get<string>('SECRET_KEY'),
        expiresIn: this.configService.get<string>('TOKEN_EXPIRY_PERIOD'),
      });

      this.logger.debug(`Token refreshed successfully for user ${user.email}`);

      // const expiresIn = this.configService.get<string>('TOKEN_EXPIRY_PERIOD');

      // if (!expiresIn) {
      //   throw new Error('TOKEN_EXPIRY_PERIOD is not defined in the configuration.');
      // }

      return {
        id: user.id,
        roles: user.roles,
        access_token: token,
        expiresIn: this.configService.get<string>('TOKEN_EXPIRY_PERIOD'), // Now guaranteed to be a string
        orgAdmin: orgAdmin ? orgAdmin._id.toString() : undefined
      };

      // return {
      //   access_token: token,
      //   expiresIn: this.configService.get<string>('TOKEN_EXPIRY_PERIOD'),
      // };
    } catch (error) {
      this.logger.error(`Failed to refresh token: ${error.message}`);
      throw new InternalServerErrorException(`Failed to refresh token. ${error.message}`);
    }
  }

  //   verifyJwt(payload: any): { email: string, _id:string } {
  //     const jwt = this.jwtService.verify(payload);
  //     return {
  //         email: jwt.email,
  //         _id: jwt._id
  //     };
  //   }

  async resendVerificationTokenEmail(forgotPasswordDto: ForgotPasswordDto) {
    return await this.userService.resendVerificationTokenEmail(forgotPasswordDto.email);
  }


  async resendForgotPasswordEmail(forgotPasswordDto: ForgotPasswordDto) {
    return this.userService.resendForgotPasswordEmail(forgotPasswordDto.email);
  }

  async getGoogleTokens(code: string, redirectUri: string) {
    const { data } = await axios.post(
      'https://oauth2.googleapis.com/token',
      {
        code,
        client_id: process.env.GOOGLE_OAUTH_CLIENT_ID,
        client_secret: process.env.GOOGLE_OAUTH_CLIENT_SECRET,
        redirect_uri: redirectUri,
        grant_type: 'authorization_code',
      },
      {
        headers: {
          'Content-Type': 'application/json',
        },
      },
    );

    return data; // contains access_token, id_token, refresh_token etc.
  }

  async getGoogleUserInfo(idToken: string, accessToken: string) {
    const { data } = await axios.get(
      `https://www.googleapis.com/oauth2/v1/userinfo?alt=json&access_token=${accessToken}`,
      {
        headers: {
          Authorization: `Bearer ${idToken}`,
        },
      },
    );

    return data;
  }

  getPopulateOptions(): PopulateOptions[] {
    const populateOptions = [
      { path: 'customStatus', select: '_id name', model: 'Status' },
      { path: 'logo', select: '_id locationUrl originalName fileSize fileType', model: 'FileMetadata' },
      {
        path: 'org',
        select: '_id title orgType description customers vendors isAdminOrg logo isOnboarded companyId',
        model: 'Org',
        populate: [
          {
            path: 'logo',
            select: '_id originalName uniqueName fileSize fileType locationUrl etag status',
            model: 'FileMetadata',
          },
          {
            path: 'contactAddress',
            select: 'apartment street city postalCode',
            model: 'AddressInformation',
          },
        ]
      },
      {
        path: 'companyId', select: '_id title orgType createdBy', model: 'Org',
      },
      // {
      //   path: 'employeeId',
      //   model: 'Employee',
      //   select: '_id firstName lastName jobTitle jobApplication email contactNumber employmentType payRollOrg endClientOrg',
      //   populate: [
      //     {
      //       path: 'payRollOrg',
      //       model: 'Org',
      //       select: '_id title'
      //     },
      //     {
      //       path: 'endClientOrg',
      //       model: 'Org',
      //       select: '_id title'
      //     },
      //     {
      //       path: 'job',
      //       model: 'Job',
      //       select: '_id title employmentType'
      //     },
      //     {
      //       path: 'jobApplication',
      //       model: 'JobApplication',
      //       select: '_id firstName lastName'
      //     }
      //   ]
      // }

    ];

    // { path: 'contactDetails', select: '_id contactEmail contactNumber', model: 'ContactInformation' },
    // { path: 'contactAddress', select: '_id street city', model: 'AddressInformation' },



    if (this.basicUserModel.schema.paths['org']) {
      populateOptions.push({
        path: 'org',
        select: '_id title orgType description customers vendors isAdminOrg logo isOnboarded isApproved isUpdated',
        model: 'Org',
        populate: [
          {
            path: 'logo',
            select: '_id originalName uniqueName fileSize fileType locationUrl etag status',
            model: 'FileMetadata',
          },
          {
            path: 'contactAddress',
            select: 'apartment street city postalCode',
            model: 'AddressInformation',
          },
          {
            path: 'country',
            select: '_id countryName',
            model: 'Country',
          },
          {
            path: 'state',
            select: '_id stateName',
            model: 'State',
          },
          {
            path: 'city',
            select: '_id name',
            model: 'City',
          },
        ]
      });
    }


    if (this.basicUserModel.schema.paths['businessUnit']) {
      populateOptions.push({ path: 'businessUnit', select: '_id label parentBusinessUnit type org breadcrumb', model: 'BusinessUnit' });
    }

    if (this.basicUserModel.schema.paths['userInboxConfig']) {
      populateOptions.push({ path: 'userInboxConfig', select: '_id userName password  imapHost imapPort  smtpHost smtpPort fromEmail fromName', model: 'UserInboxConfig' });
    }

    // ✅ Conditionally push employeeId population if it exists in schema
    if (this.basicUserModel.schema.paths['employee']) {
      populateOptions.push({
        path: 'employeeId',
        model: 'Employee',
        select: '_id firstName lastName jobTitle jobApplication email contactNumber employmentType payRollOrg endClientOrg',
        populate: [
          {
            path: 'payRollOrg',
            model: 'Org',
            select: '_id title'
          },
          {
            path: 'endClientOrg',
            model: 'Org',
            select: '_id title'
          },
          {
            path: 'job',
            model: 'Job',
            select: '_id title employmentType'
          },
          {
            path: 'jobApplication',
            model: 'JobApplication',
            select: '_id firstName lastName'
          }
        ]
      });
    }



    return populateOptions;
  }
  async generateJwtForGoogleUser(google_user: any, role?: any) {
    // return this.jwtService.sign({ sub: user.id, email: user.email });
    // const user: any = await this.userService.findUserByEmail(google_user.email);
    const populateOptions = this.getPopulateOptions();
    const user = await this.basicUserModel.findOne({ email: google_user.email })
      .populate({ path: 'logo', select: '_id locationUrl originalName fileSize fileType', model: 'FileMetadata' })
      .populate({ path: 'country', select: '_id countryName' })
      .populate({ path: 'state', select: '_id stateName' })
      .populate({ path: 'city', select: '_id name' })
      
      .populate(populateOptions)
      .exec();
    // console.log("user", user)
    // console.log("role", role)
    const generateStrongPassword = () => {
      const uppercase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
      const lowercase = "abcdefghijklmnopqrstuvwxyz";
      const numbers = "0123456789";
      const specialChars = "!@#$%^&*()_-+=<>?";
      const allChars = uppercase + lowercase + numbers + specialChars;

      let password = '';
      password += uppercase[Math.floor(Math.random() * uppercase.length)];
      password += numbers[Math.floor(Math.random() * numbers.length)];
      password += specialChars[Math.floor(Math.random() * specialChars.length)];

      for (let i = 3; i < 8; i++) {
        password += allChars[Math.floor(Math.random() * allChars.length)];
      }

      return password.split('').sort(() => 0.5 - Math.random()).join(''); // Shuffle the password
    };
    // Generate and hash the password
    const password = generateStrongPassword();
    if (!user) {
      const createUserDto: CreateUserDto = {
        email: google_user.email,
        firstName: google_user.name || 'First',  // fallback if not available
        lastName: google_user.name || 'Last',   // fallback if not available
        password: password, // You might want to generate a secure random one instead
        // checked: true,
        isVerified: true,
        sendPassword: true,
        source: 'landingPage',
        // roles: [Role.JobSeeker],
        roles: [role]

        // throw new NotFoundException(`Invalid email or password.`);
      }
      let newUser = await this.userService.create(createUserDto);

      this.logger.log(newUser)
      let orgAdmin: any = null;

      const payload = {
        email: newUser.email,
        firstName: newUser.firstName,
        _id: newUser._id,
        roles: newUser.roles,
        org: newUser.org,
        businessUnit: newUser.businessUnit,
        userInboxConfig: newUser.userInboxConfig,
        logo: newUser.logo,
        companyId: newUser.companyId,
        employeeId: newUser.employeeId,
        orgAdmin: orgAdmin ? orgAdmin._id.toString() : undefined,
        adminUserInbox: orgAdmin ? orgAdmin?.userInboxConfig ?? undefined : undefined,
        isCustom: newUser.isCustom ? newUser.isCustom : false,
      };

      const token = await this.jwtService.signAsync(payload, {
        // secret: 'SOME_SECRET_KEY',
        secret: this.configService.get<string>('SECRET_KEY'),
        expiresIn: this.configService.get<string>('TOKEN_EXPIRY_PERIOD')
      });
      this.logger.debug(`NewUser with email ${google_user.email} signed in successfully`);
      return {
        id: newUser._id,
        roles: newUser.roles,
        access_token: token,
        expiresIn: this.configService.get<string>('TOKEN_EXPIRY_PERIOD'), // Now guaranteed to be a string
        orgAdmin: orgAdmin ? orgAdmin._id.toString() : undefined
      }

    }

    // this.logger.log(pass);
    // const isMatch = await bcrypt.compare(pass, user.password);
    // // this.logger.log(isMatch);
    // if (!isMatch) {
    //   throw new BadRequestException(`User must have entered invalid credentials.`);
    // }

    // Find the admin for the user's organization
    if (user) {
      this.logger.log(user)
      let orgAdmin: any = null
      if (user.org) {
        // orgAdmin = await this.userService.findAdminByOrgId(user.org._id);
        orgAdmin = user.org && typeof user.org === 'object' && '_id' in user.org
          ? await this.userService.findAdminByOrgId(user.org._id as string)
          : null;
        this.logger.log(orgAdmin)
      }

      const payload = {
        email: user.email,
        firstName: user.firstName,
        _id: user._id,
        roles: user.roles,
        org: user.org,
        businessUnit: user.businessUnit,
        userInboxConfig: user.userInboxConfig,
        logo: user.logo,
        companyId: user.companyId,
        employeeId: user.employeeId,
        orgAdmin: orgAdmin ? orgAdmin._id.toString() : undefined,
        adminUserInbox: orgAdmin ? orgAdmin?.userInboxConfig ?? undefined : undefined,
        isCustom: user.isCustom ? user.isCustom : false,
      };
      console.log("payload", payload)
      const token = await this.jwtService.signAsync(payload, {
        // secret: 'SOME_SECRET_KEY',
        secret: this.configService.get<string>('SECRET_KEY'),
        expiresIn: this.configService.get<string>('TOKEN_EXPIRY_PERIOD')
      });
      this.logger.debug(`User with email ${google_user.email} signed in successfully`);
      return {
        id: user.id,
        roles: user.roles,
        access_token: token,
        expiresIn: this.configService.get<string>('TOKEN_EXPIRY_PERIOD'), // Now guaranteed to be a string
        orgAdmin: orgAdmin ? orgAdmin._id.toString() : undefined
      }
    }



  }

  async exchangeLinkedInCodeForTokens(code: string, redirectUri: string) {
    console.log("code", code)
    console.log("redirectUri", redirectUri)
    // const response = await axios.post('https://www.linkedin.com/oauth/v2/token', null, {
    //   params: {
    //     grant_type: 'authorization_code',
    //     code,
    //     redirect_uri: redirectUri,
    //     client_id: process.env.LINKEDIN_OAUTH_CLIENT_ID,
    //     client_secret: process.env.LINKEDIN_OAUTH_CLIENT_SECRET,
    //   },
    //   headers: {
    //     'Content-Type': 'application/x-www-form-urlencoded',
    //   },
    // });  
    const payload = qs.stringify({
      grant_type: 'authorization_code',
      code,
      redirect_uri: redirectUri,
      client_id: process.env.LINKEDIN_OAUTH_CLIENT_ID || '86a4d0btrpqvar',
      client_secret: process.env.LINKEDIN_OAUTH_CLIENT_SECRET || 'WPL_AP1.XFaV4HIUAljXyyMD.w7cSTA==',
    });

    try {
      const response = await axios.post('https://www.linkedin.com/oauth/v2/accessToken', payload, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      });
      // console.log("response", response)
      return response.data; // contains access_token, id_token, etc.
    } catch (error) {
      console.error('LinkedIn Token Error:', error.response?.data || error.message);
      throw error;
    }

    // const response = await axios.post('https://www.linkedin.com/oauth/v2/token', payload, {
    //   headers: {
    //     'Content-Type': 'application/x-www-form-urlencoded',
    //   },
    // });
    // console.log("response", response)

    // return response.data; // contains access_token, id_token, etc.
  }

  async getLinkedInUserInfo(accessToken: string) {
    const response = await axios.get('https://api.linkedin.com/v2/userinfo', {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
    });

    const data = response.data;

    return {
      email: data.email,
      fullName: data.name,
      firstName: data.given_name,
      lastName: data.family_name,
      photo: data.picture,
    };
  }

  async generateJwtForLinkedInUser(linkedin_user: any, role?: any) {
    // return this.jwtService.sign({ sub: user.id, email: user.email });
    // const user: any = await this.userService.findUserByEmail(google_user.email);
    const populateOptions = this.getPopulateOptions();
    const user = await this.basicUserModel.findOne({ email: linkedin_user.email })
      .populate({ path: 'logo', select: '_id locationUrl originalName fileSize fileType', model: 'FileMetadata' })
      .populate({ path: 'country', select: '_id countryName' })
      .populate({ path: 'state', select: '_id stateName' })
      .populate({ path: 'city', select: '_id name' })
      .populate(populateOptions)
      .exec();
    // console.log("user", user)
    // console.log("role", role)
    const generateStrongPassword = () => {
      const uppercase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
      const lowercase = "abcdefghijklmnopqrstuvwxyz";
      const numbers = "0123456789";
      const specialChars = "!@#$%^&*()_-+=<>?";
      const allChars = uppercase + lowercase + numbers + specialChars;

      let password = '';
      password += uppercase[Math.floor(Math.random() * uppercase.length)];
      password += numbers[Math.floor(Math.random() * numbers.length)];
      password += specialChars[Math.floor(Math.random() * specialChars.length)];

      for (let i = 3; i < 8; i++) {
        password += allChars[Math.floor(Math.random() * allChars.length)];
      }

      return password.split('').sort(() => 0.5 - Math.random()).join(''); // Shuffle the password
    };
    // Generate and hash the password
    const password = generateStrongPassword();
    if (!user) {
      const createUserDto: CreateUserDto = {
        email: linkedin_user.email,
        firstName: linkedin_user.firstName || 'First',  // fallback if not available
        lastName: linkedin_user.lastName || 'Last',   // fallback if not available
        password: password, // You might want to generate a secure random one instead
        // checked: true,
        isVerified: true,
        sendPassword: true,
        source: 'landingPage',
        // roles: [Role.JobSeeker],
        roles: [role]

        // throw new NotFoundException(`Invalid email or password.`);
      }
      let newUser = await this.userService.create(createUserDto);

      this.logger.log(newUser)
      let orgAdmin: any = null;

      const payload = {
        email: newUser.email,
        firstName: newUser.firstName,
        _id: newUser._id,
        roles: newUser.roles,
        org: newUser.org,
        businessUnit: newUser.businessUnit,
        userInboxConfig: newUser.userInboxConfig,
        logo: newUser.logo,
        companyId: newUser.companyId,
        employeeId: newUser.employeeId,
        orgAdmin: orgAdmin ? orgAdmin._id.toString() : undefined,
        adminUserInbox: orgAdmin ? orgAdmin?.userInboxConfig ?? undefined : undefined,
        isCustom: newUser.isCustom ? newUser.isCustom : false,
      };

      const token = await this.jwtService.signAsync(payload, {
        // secret: 'SOME_SECRET_KEY',
        secret: this.configService.get<string>('SECRET_KEY'),
        expiresIn: this.configService.get<string>('TOKEN_EXPIRY_PERIOD')
      });
      this.logger.debug(`NewUser with email ${linkedin_user.email} signed in successfully`);
      return {
        id: newUser._id,
        roles: newUser.roles,
        access_token: token,
        expiresIn: this.configService.get<string>('TOKEN_EXPIRY_PERIOD'), // Now guaranteed to be a string
        orgAdmin: orgAdmin ? orgAdmin._id.toString() : undefined
      }

    }

    // this.logger.log(pass);
    // const isMatch = await bcrypt.compare(pass, user.password);
    // // this.logger.log(isMatch);
    // if (!isMatch) {
    //   throw new BadRequestException(`User must have entered invalid credentials.`);
    // }

    // Find the admin for the user's organization
    if (user) {
      this.logger.log(user)
      let orgAdmin: any = null
      if (user.org) {
        // orgAdmin = await this.userService.findAdminByOrgId(user.org._id);
        orgAdmin = user.org && typeof user.org === 'object' && '_id' in user.org
          ? await this.userService.findAdminByOrgId(user.org._id as string)
          : null;
        this.logger.log(orgAdmin)
      }

      const payload = {
        email: user.email,
        firstName: user.firstName,
        _id: user._id,
        roles: user.roles,
        org: user.org,
        businessUnit: user.businessUnit,
        userInboxConfig: user.userInboxConfig,
        logo: user.logo,
        companyId: user.companyId,
        employeeId: user.employeeId,
        orgAdmin: orgAdmin ? orgAdmin._id.toString() : undefined,
        adminUserInbox: orgAdmin ? orgAdmin?.userInboxConfig ?? undefined : undefined,
        isCustom: user.isCustom ? user.isCustom : false,
      };
      console.log("payload", payload)
      const token = await this.jwtService.signAsync(payload, {
        // secret: 'SOME_SECRET_KEY',
        secret: this.configService.get<string>('SECRET_KEY'),
        expiresIn: this.configService.get<string>('TOKEN_EXPIRY_PERIOD')
      });
      this.logger.debug(`User with email ${linkedin_user.email} signed in successfully`);
      return {
        id: user.id,
        roles: user.roles,
        access_token: token,
        expiresIn: this.configService.get<string>('TOKEN_EXPIRY_PERIOD'), // Now guaranteed to be a string
        orgAdmin: orgAdmin ? orgAdmin._id.toString() : undefined
      }
    }



  }

  // async getLinkedInTokens(code: string, redirectUri: string) {
  //   const tokenUrl = 'https://www.linkedin.com/oauth/v2/accessToken';

  //   const params = new URLSearchParams({
  //     grant_type: 'authorization_code',
  //     code,
  //     redirect_uri: redirectUri,
  //     client_id: process.env.LINKEDIN_OAUTH_CLIENT_ID || '86a4d0btrpqvar',
  //     client_secret: process.env.LINKEDIN_OAUTH_CLIENT_SECRET || 'WPL_AP1.XFaV4HIUAljXyyMD.w7cSTA==',
  //   });

  //   const response = await axios.post(tokenUrl, params.toString(), {
  //     headers: {
  //       'Content-Type': 'application/x-www-form-urlencoded',
  //     },
  //   });

  //   return response.data; // { access_token, expires_in }
  // }

  // async getLinkedInEmail(accessToken: string): Promise<string> {
  //   const emailUrl = 'https://api.linkedin.com/v2/emailAddress?q=members&projection=(elements*(handle~))';

  //   const response = await axios.get(emailUrl, {
  //     headers: {
  //       Authorization: `Bearer ${accessToken}`,
  //     },
  //   });

  //   return response.data.elements[0]['handle~'].emailAddress;
  // }

  // async getLinkedInProfile(accessToken: string) {
  //   const profileUrl =
  //     'https://api.linkedin.com/v2/me?projection=(id,localizedFirstName,localizedLastName,profilePicture(displayImage~:playableStreams))';

  //   const response = await axios.get(profileUrl, {
  //     headers: {
  //       Authorization: `Bearer ${accessToken}`,
  //     },
  //   });

  //   const profile = response.data;

  //   const avatar =
  //     profile.profilePicture?.['displayImage~']?.elements?.[0]?.identifiers?.[0]?.identifier || null;

  //   return {
  //     id: profile.id,
  //     name: `${profile.localizedFirstName} ${profile.localizedLastName}`,
  //     avatar,
  //   };
  // }

  // async getLinkedInUserInfo(code: string, redirectUri: string) {
  //   const { access_token } = await this.getLinkedInTokens(code, redirectUri);
  //   const email = await this.getLinkedInEmail(access_token);
  //   const profile = await this.getLinkedInProfile(access_token);

  //   return {
  //     email,
  //     name: profile.name,
  //     avatar: profile.avatar,
  //     accessToken: access_token,
  //   };
  // }

  // async handleGoogleLogin(googleUser: {
  //   email: string;
  //   name: string;
  //   picture: string;
  // }): Promise<any> {
  //   let user = await this.userService.findUserByEmail(googleUser.email);
  //   // let user = await this.userModel.findOne({ email: googleUser.email });
  //   let isNewUser = false;

  //   if (!user) {
  //     const createUserDto: CreateUserDto = {
  //       email: googleUser.email,
  //       firstName: googleUser.name || 'First',  // fallback if not available
  //       lastName: googleUser.name || 'Last',   // fallback if not available
  //       password: 'string123@', // You might want to generate a secure random one instead
  //       // checked: true,
  //       isVerified: true,
  //       source: 'landingPage',
  //       roles: [Role.JobSeeker],
  //     };

  //     let newUser = await this.userService.create(createUserDto);
  //     // user = await this.userModel.create({
  //     //   email: googleUser.email,
  //     //   name: googleUser.name,
  //     //   avatar: googleUser.picture,
  //     // });
  //     isNewUser = true;
  //   }

  //   const loggedInUser = await this.signIn(googleUser.email, 'string123@'); // Use a secure random password instead of 'string123@'
  //   // const payload = { sub: user._id, email: user.email, name: user.name };
  //   // const token = this.jwtService.sign(payload);

  //   return { loggedInUser, isNewUser };
  // }

}
