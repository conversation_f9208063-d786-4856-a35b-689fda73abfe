import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";
import { HydratedDocument, Types } from "mongoose";
import { Salutaion } from "src/shared/constants"; 

export type RecruitementTeamDocument = HydratedDocument<RecruitementTeam>;

@Schema({
    timestamps: true
})
export class RecruitementTeam {

    @Prop({
        required: true,
        default: Salutaion.MR,
        enum: Object.values(Salutaion),
   })
   salutation: string; 

   @Prop({
        type: String,
        required: true,
        trim: true,
    })
    firstName: string;

    @Prop({
        type: String,
        required: true,
        trim: true,
    })
    lastName: string;

   @Prop({
        type: String,
        required: false,
        trim: true,
    })
    middleName: string;

    @Prop({
        required: false,
        trim: true,
    })
    reference: string;

    @Prop({ type: String, required: true, trim: true, lowercase: true  })
    email: string;

    @Prop({ type: String, required: true, trim: true })
    phoneNumber: string;
}

export const RecruitementTeamSchema = SchemaFactory.createForClass(RecruitementTeam);