import { ApiProperty } from "@nestjs/swagger";
import { Transform, TransformFnParams } from "class-transformer";
import { IsMongoId, IsNotEmpty, IsNumber, IsOptional, IsString } from "class-validator";
import { sanitizeWithStyle } from "src/utils/sanitize-with-style";
import { StageType } from "src/shared/constants";

export class  CreateStageDto {

  @ApiProperty({
    type: String,
    required: true,
    description: 'Name of stage'
  })
  @IsString()
  @IsNotEmpty()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  name: string;
 
  @ApiProperty({
    type: String,
    required: true,
    description: 'Stage type',
    default: StageType.NONE,
    enum: Object.values(StageType),
  })
  @IsString()
  @IsNotEmpty()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  type: string;
  
  @ApiProperty({
    type: Number,
    required: false,
    description: 'Sequence number of stage used for reorder arrangement'
  })
  // @IsNumber()
  // @IsNotEmpty()
  @IsOptional()
  sequenceNumber?: number;
  
  @ApiProperty({
    type: Number,
    required: false,
    default: 0
  })
  jobApplicationsCount?: number;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Url of the the icon'
  })
  @IsString()
  @IsOptional()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  iconUrl?: string; 

  // @ApiProperty({
  //   type: String,
  //   required: true,
  //   description: ''
  // })
  // @IsMongoId()
  // org: string;

}

