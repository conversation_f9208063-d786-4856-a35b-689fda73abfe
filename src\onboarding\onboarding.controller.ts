import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Logger, BadRequestException, NotFoundException, UsePipes, ValidationPipe, Req } from '@nestjs/common';
import { OnboardingService } from './onboarding.service';
import { CreateOnboardingDto } from './dto/create-onboarding.dto';
import { UpdateOnboardingDto } from './dto/update-onboarding.dto';
import { ApiBearerAuth, ApiResponse, ApiOperation, ApiParam, ApiTags } from '@nestjs/swagger';
import { Roles } from 'src/auth/decorators/roles.decorator';
import { Role } from 'src/auth/enums/role.enum';
import { AuthJwtGuard } from 'src/auth/guards/auth-jwt/auth-jwt.guard';
import { RolesGuard } from 'src/auth/guards/roles/roles.guard';
import { validateObjectId } from 'src/utils/validation.utils';

@Controller('')
@ApiTags('Onboardings')
export class OnboardingController {

  private readonly logger = new Logger(OnboardingController.name);

  constructor(private readonly onboardingService: OnboardingService) { }

  @Post()
  // @ApiBearerAuth()
  // @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.SuperAdmin,)
  @ApiResponse({ status: 201, description: 'Onboarding is saved.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Data. ' })
  @ApiResponse({ status: 401, description: 'Unauthorized access. Authentication is required. ' })
  // @ApiResponse({ status: 403, description: `This endpoint allows you to create a new identifier. Accessible only to users with role "${Role.SuperAdmin}".` })
  @ApiOperation({ summary: 'Create a Onboarding', description: `This endpoint is used for creating a onboarding. This is only accessible for "admin".` })
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  async create(@Body() createOnboardingDto: CreateOnboardingDto, @Req() req: any) {
    try {
      // const { org, entityType, identifiers } = createOnboardingDto
      this.logger.log(`Received DTO: ${JSON.stringify(createOnboardingDto)}`);
      if (req.user) {
        createOnboardingDto.createdBy = req.user._id;
      }
      const result = await this.onboardingService.create(createOnboardingDto);
      return result;
    } catch (error) {
      this.logger.error('Error creating onboarding', error.stack);
      throw new BadRequestException('An error occurred while creating onboarding.');
    }
  }

  @Get('all')
  @ApiOperation({
    summary: 'Retrieve all onboardings',
    description: `This endpoint returns a list of all onboardings. Accessible only to users with role "${Role.Admin}"`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin)
  @Roles()
  @ApiResponse({ status: 200, description: 'All onboardings are retrieved.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Data.' })
  @ApiResponse({ status: 401, description: 'Unauthorized access. Authentication is required.' })
  @ApiResponse({ status: 403, description: `Forbidden. Only users with role "${Role.Admin}" are permitted to use this endpoint.` })
  findAll() {
    return this.onboardingService.findAll();
  }

  @Get(':orgId/find-all-by-org')
  @ApiOperation({
    summary: 'Retrieve all onboardings of an org',
    description: `This endpoint returns a list of all onboardings by an org Id. Accessible only to users with role "${Role.Admin}"`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.Vendor)
  @Roles()
  @ApiResponse({ status: 200, description: 'All onboardings are retrieved.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Data.' })
  @ApiResponse({ status: 401, description: 'Unauthorized access. Authentication is required.' })
  @ApiResponse({ status: 403, description: `Forbidden. Only users with role "${Role.Admin}" and "${Role.Vendor}" are permitted to use this endpoint.` })
  @ApiParam({ name: 'orgId', description: 'ID of the org' })
  async findAllByOrg(@Param('orgId') orgId: string) {
    try {
      this.logger.log(`Received orgId: ${orgId}`);
      const orgObjId = validateObjectId(orgId); // This line will throw BadRequestException if validation fails
      const onboardings = await this.onboardingService.findAllByOrg(orgId);
      return onboardings;
    } catch (error) {
      if (error instanceof BadRequestException) {
        // Handle BadRequestException from validateObjectId
        this.logger.error(`Invalid orgId: ${error.message}`);
        throw new BadRequestException(`Invalid orgId: ${error.message}`);
      } else {
        // Handle other errors
        this.logger.error(`Error finding onboardings: ${error.message}`);
        throw error; // Let NestJS handle the error response
      }
    }
  }

  @Get(':userId/find-all-by-user')
  @ApiOperation({
    summary: 'Retrieve all onboardings of a user',
    description: `This endpoint returns a list of all onboardings by a user Id. Accessible only to users with role "${Role.Admin}"`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin)
  @Roles()
  @ApiResponse({ status: 200, description: 'All onboardings are retrieved.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Data.' })
  @ApiResponse({ status: 401, description: 'Unauthorized access. Authentication is required.' })
  @ApiResponse({ status: 403, description: `Forbidden. Only users with role "${Role.Admin}" are permitted to use this endpoint.` })
  @ApiParam({ name: 'userId', description: 'ID of the user' })
  async findAllByUser(@Param('userId') userId: string) {
    try {
      this.logger.log(`Received userId: ${userId}`);
      const userObjId = validateObjectId(userId); // This line will throw BadRequestException if validation fails
      const onboardings = await this.onboardingService.findAllByUser(userId);
      return onboardings;
    } catch (error) {
      if (error instanceof BadRequestException) {
        // Handle BadRequestException from validateObjectId
        this.logger.error(`Invalid userId: ${error.message}`);
        throw new BadRequestException(`Invalid userId: ${error.message}`);
      } else {
        // Handle other errors
        this.logger.error(`Error finding onboardings by user: ${error.message}`);
        throw error; // Let NestJS handle the error response
      }
    }
  }

  @Get(':onboardingId')
  @ApiOperation({
    summary: 'Retrieve an onboarding by Id',
    description: `This endpoint retrieves an onboarding by its Id. Accessible only to users with role "${Role.Admin}"`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin)
  @Roles()
  @ApiResponse({ status: 200, description: 'Onboarding is retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized access. Authentication is required.' })
  @ApiResponse({ status: 403, description: `Forbidden. Only users with role "${Role.Admin}" are permitted to use this endpoint.` })
  @ApiResponse({ status: 404, description: 'Onboarding not found.' })
  @ApiParam({ name: 'onboardingId', description: 'ID of the onboarding' })
  findOne(@Param('onboardingId') onboardingId: string) {
    const onboardingObjId = validateObjectId(onboardingId);
    return this.onboardingService.findOne(onboardingObjId);
  }

  @Patch(':onboardingId')
  @ApiOperation({
    summary: 'Update an onboarding by Id',
    description: `This endpoint updates an onboarding by Id. Accessible only to users with roles "${Role.Admin}".`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.Vendor)
  @Roles()
  @ApiResponse({ status: 200, description: 'Onboarding is updated.' })
  @ApiResponse({ status: 401, description: 'Unauthorized access. Authentication is required.' })
  @ApiResponse({ status: 403, description: `Forbidden. Only users with role "${Role.Admin}" are permitted to use this endpoint.` })
  @ApiResponse({ status: 404, description: 'Onboarding not found.' })
  @ApiParam({ name: 'onboardingId', description: 'ID of the onboarding' })
  update(@Param('onboardingId') onboardingId: string, @Body() updateOnboardingDto: UpdateOnboardingDto, @Req() req: any) {
    const onboardingObjId = validateObjectId(onboardingId)
    return this.onboardingService.update(onboardingObjId, updateOnboardingDto, req.user);
  }

  @Delete(':onboardingId')
  @ApiOperation({
    summary: 'Delete an onboarding by Id',
    description: `This endpoint deletes an onboarding by Id. Accessible only to users with roles "${Role.Admin}".`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin)
  @Roles()
  @ApiResponse({ status: 200, description: 'Onboarding is deleted' })
  @ApiResponse({ status: 401, description: 'Unauthorized access. Authentication is required.' })
  @ApiResponse({ status: 403, description: `Forbidden. Only users with role "${Role.Admin}" are permitted to use this endpoint.` })
  @ApiResponse({ status: 404, description: 'Onboarding not found.' })
  @ApiParam({ name: 'onboardingId', description: 'ID of the onboarding' })
  remove(@Param('onboardingId') onboardingId: string) {
    const onboardingObjId = validateObjectId(onboardingId)
    return this.onboardingService.remove(onboardingObjId);
  }

  @Get(':orgId/find-all-by-customer')
  @ApiOperation({
    summary: 'Retrieve all onboardings of an org',
    description: `This endpoint returns a list of all onboardings by an org Id."`
  })
  @ApiResponse({ status: 200, description: 'All onboardings are retrieved.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Data.' })
  @ApiResponse({ status: 401, description: 'Unauthorized access. Authentication is required.' })
  @ApiParam({ name: 'orgId', description: 'ID of the org' })
  async findAllByCustomer(@Param('orgId') orgId: string) {
    try {
      this.logger.log(`Received orgId: ${orgId}`);
      const orgObjId = validateObjectId(orgId); // This line will throw BadRequestException if validation fails
      const onboardings = await this.onboardingService.findAllByOrg(orgId);
      return onboardings;
    } catch (error) {
      if (error instanceof BadRequestException) {
        // Handle BadRequestException from validateObjectId
        this.logger.error(`Invalid orgId: ${error.message}`);
        throw new BadRequestException(`Invalid orgId: ${error.message}`);
      } else {
        // Handle other errors
        this.logger.error(`Error finding onboardings: ${error.message}`);
        throw error; // Let NestJS handle the error response
      }
    }
  }

  @Patch(':onboardingId/customer')
  @ApiOperation({
    summary: 'Update an onboarding by Id',
    description: `This endpoint updates an onboarding by Id".`
  })
  @ApiResponse({ status: 200, description: 'Onboarding is updated.' })
  @ApiResponse({ status: 401, description: 'Unauthorized access. Authentication is required.' })
  @ApiResponse({ status: 404, description: 'Onboarding not found.' })
  @ApiParam({ name: 'onboardingId', description: 'ID of the onboarding' })
  updateCustomerOnboarding(@Param('onboardingId') onboardingId: string, @Body() updateOnboardingDto: UpdateOnboardingDto, @Req() req: any) {
    const onboardingObjId = validateObjectId(onboardingId)
    return this.onboardingService.updateCustomerOnboarding(onboardingObjId, updateOnboardingDto);
  }

  @Get(':userId/find-vendor-onBoardings')
  @ApiOperation({
    summary: 'Retrieve all onboardings of a user',
    description: `This endpoint returns a list of all onboardings by a user Id. Accessible only to users with role "${Role.Admin}"`
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  // @Roles(Role.Admin)
  @Roles()
  @ApiResponse({ status: 200, description: 'All onboardings are retrieved.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Data.' })
  @ApiResponse({ status: 401, description: 'Unauthorized access. Authentication is required.' })
  @ApiResponse({ status: 403, description: `Forbidden. Only users with role "${Role.Admin}" are permitted to use this endpoint.` })
  // @ApiParam({ name: 'userId', description: 'ID of the user' })
  async findAllByVendor(@Req() req: any) {
    try {
      const onboardings = await this.onboardingService.findAllByVendor(req.user);
      return onboardings;
    } catch (error) {
      if (error instanceof BadRequestException) {
        // Handle BadRequestException from validateObjectId
        this.logger.error(`Invalid user: ${error.message}`);
        throw new BadRequestException(`Invalid user: ${error.message}`);
      } else {
        // Handle other errors
        this.logger.error(`Error finding onboardings by user: ${error.message}`);
        throw error; // Let NestJS handle the error response
      }
    }
  }

}
