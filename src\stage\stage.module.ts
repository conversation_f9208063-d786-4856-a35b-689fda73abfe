import { Module, forwardRef } from '@nestjs/common';
import { StageService } from './stage.service';
import { StageController } from './stage.controller';
import { JwtModule } from '@nestjs/jwt';
import { ConfigModule } from '@nestjs/config';
import { MongooseModule } from '@nestjs/mongoose';
import { UserModule } from 'src/user/user.module';
import { Stage, StageSchema } from './schemas/stage.schema';
import { AuthModule } from 'src/auth/auth.module';
import { EndpointsRolesModule } from 'src/endpoints-roles/endpoints-roles.module';

@Module({
  imports: [
    ConfigModule,
    JwtModule, EndpointsRolesModule,
    MongooseModule.forFeature([{ name:Stage.name, schema: StageSchema}])
  ],
  controllers: [StageController],
  providers: [StageService],
  exports: [MongooseModule, StageService]
})
export class StageModule {}
