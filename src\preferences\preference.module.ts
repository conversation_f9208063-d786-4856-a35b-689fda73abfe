import { Module, forwardRef } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { PreferenceController } from './preference.controller';
import { PreferenceService } from './preference.service';
import { Preference, PreferenceSchema } from './schemas/preference.schema';
import { ConfigModule } from '@nestjs/config';
import { UserModule } from 'src/user/user.module';
import { JwtModule } from '@nestjs/jwt'; // ✅ Import JwtModule
import { EndpointsRolesModule } from 'src/endpoints-roles/endpoints-roles.module';
import { OrgModule } from 'src/org/org.module';

@Module({
  controllers: [PreferenceController],
  imports: [
    ConfigModule,
    EndpointsRolesModule,
    forwardRef(() => UserModule),
    forwardRef(() => OrgModule),
    JwtModule.register({               // ✅ Register JwtModule
      secret: process.env.JWT_SECRET || 'defaultSecret', 
      signOptions: { expiresIn: '1d' },
    }),
    MongooseModule.forFeatureAsync([
      {
        name: Preference.name,
        imports: [ConfigModule],
        useFactory: () => {
          const schema = PreferenceSchema;
          schema.pre('save', function (next) {
            const preference = this as any;
            if (!preference.createdAt) {
              preference.createdAt = new Date();
            }
            next();
          });
          return schema;
        },
        inject: [],
      },
    ]),
  ],
  providers: [PreferenceService],
  exports: [PreferenceService, MongooseModule],
})
export class PreferenceModule {}
