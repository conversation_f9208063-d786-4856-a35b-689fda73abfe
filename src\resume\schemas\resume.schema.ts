import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose, { Document, Types } from 'mongoose';
import { EvaluationForm } from 'src/evaluation-form/schemas/evaluation-form.schema';
import { BasicUser } from 'src/user/schemas/basic-user.schema';

export type ResumeDocument = Resumes & Document;

@Schema()
export class ContactDetails {

    @Prop({ type: mongoose.Schema.Types.ObjectId, auto: true })
    _id: mongoose.Types.ObjectId;
    
    @Prop()
    contactEmail?: string;

    @Prop()
    contactNumber?: string;
}

@Schema()
export class ContactAddress {

    @Prop({ type: mongoose.Schema.Types.ObjectId, auto: true })
    _id: mongoose.Types.ObjectId;

    @Prop()
    street?: string;

    @Prop()
    postalCode?: string;
}

@Schema()
export class WorkExperiences {

    @Prop({ type: mongoose.Schema.Types.ObjectId, auto: true })
    _id: mongoose.Types.ObjectId;

    @Prop({ required: true })
    jobTitle: string;

    @Prop({ required: true })
    companyName: string;

    @Prop()
    jobStartDate?: Date;

    @Prop()
    jobEndDate?: Date;

    @Prop()
    currentlyWorking?: Boolean;
}

@Schema()
export class EducationalQualification {

    @Prop({ type: mongoose.Schema.Types.ObjectId, auto: true })
    _id: mongoose.Types.ObjectId;

    @Prop({ required: true })
    courseName: string;

    @Prop({ required: true })
    university: string;

    @Prop()
    startDate?: Date;

    @Prop()
    endDate?: Date;
}

@Schema()
export class EvaluationForms {

    @Prop({ type: mongoose.Schema.Types.ObjectId, auto: true })
    _id: mongoose.Types.ObjectId;

    @Prop({ required: true })
    skill: string;

    @Prop({ required: true })
    years: number;
    
    @Prop()
    months?: number;

    @Prop({ required: false })
    rating: number;

    @Prop({ required: true })
    isPrimary: number;

}

@Schema({ timestamps: true })
export class Resumes {

    @Prop({ required: true })
    firstName: string;

    @Prop()
    lastName?: string;

    @Prop()
    panNumber?: string;

    @Prop()
    resumeMetadata?: string;

    @Prop()
    coverLetterMetadata?: string;

    @Prop({ type: ContactDetails })
    contactDetails?: ContactDetails;

    @Prop({ type: ContactAddress })
    contactAddress?: ContactAddress;

    @Prop()
    state?: string;

    @Prop()
    city?: string;

    @Prop()
    country?: string;

    @Prop()
    dob?: Date;

    @Prop()
    gender?: string;

    @Prop()
    disability?: boolean;

    @Prop()
    nationality?: string;

    @Prop()
    linkedInUrl?: string;

    @Prop()
    websiteOrBlogUrl?: string;

    @Prop()
    isExperienced?: boolean;

    @Prop()
    yearsOfExperience?: number;

    @Prop({ type: [EvaluationForms] })
    evaluationForm?: EvaluationForms[];

    @Prop()
    noticePeriodDays?: number;

    @Prop()
    servingNoticePeriod?: boolean;

    @Prop()
    lastWorkingDate?: Date;

    @Prop()
    currentLocation?: string;

    @Prop()
    willingToRelocate?: boolean;

    @Prop({ type: [String] })
    reLocation?: string[];

    @Prop()
    preferredLocation?: string;

    @Prop()
    currentCTC?: number;

    @Prop()
    expectedCTC?: number;

    @Prop()
    ctcPercentage?: number;

    @Prop()
    currency?: string;

    @Prop()
    companyNorms?: boolean;

    @Prop()
    org?: string;

    @Prop()
    isDraft?: boolean;

    @Prop({ type: Object })
    dynamicFields?: object;

    @Prop()
    resumeName?: string;

    @Prop({ type: [WorkExperiences] })
    workExperience?: WorkExperiences[];

    @Prop({ type: [EducationalQualification] })
    educationQualification?: EducationalQualification[];

    @Prop({
        required: true,
        type: Types.ObjectId, ref: 'BasicUser'
    })
    createdBy: BasicUser;

}


export const ResumeSchema = SchemaFactory.createForClass(Resumes);





