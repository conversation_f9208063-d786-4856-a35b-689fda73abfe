import { Controller, Get, Post, Body, Patch, Param, Delete, Req, UseGuards, Logger, Query, BadRequestException } from '@nestjs/common';
import { TaskService } from './task.service';
import { CreateTaskDto } from './dto/create-task.dto';
import { UpdateTaskDto } from './dto/update-task.dto';
import { ApiBearerAuth, ApiOperation, ApiParam, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { AuthJwtGuard } from 'src/auth/guards/auth-jwt/auth-jwt.guard';
import { RolesGuard } from 'src/auth/guards/roles/roles.guard';
import { Roles } from 'src/auth/decorators/roles.decorator';
import { Role } from 'src/auth/enums/role.enum';
import { validateObjectId } from 'src/utils/validation.utils';
import { Types } from 'mongoose';
import { CommentDto } from 'src/common/dto/comment.dto';
import { MoveToDto } from 'src/common/dto/move-to.dto';
import { QueryDTO } from 'src/note/dto/query-note.dto';
import { query } from 'express';
import { ChangeStatusTaskDto } from './dto/change-status-task.dto';
import { MemberJobAssignDto } from 'src/task/dto/assign-job-to-member';
import { VendorJobAssignDto } from 'src/task/dto/assign-job-to-vendor.dto';

@Controller('')
@ApiTags('Tasks')
export class TaskController {
  constructor(private readonly taskService: TaskService) { }

  private readonly logger = new Logger(TaskController.name);


  @Post()
  @ApiResponse({ status: 201, description: `Task is saved.` })
  @ApiResponse({ status: 401, description: `Unauthorized.` })
  @ApiResponse({ status: 400, description: `Bad Request / Data.` })
  @ApiResponse({ status: 403, description: `Forbidden, User with role "BUHead" , "ResourceManager", "DeliveryManager","Recruiter", "TeamLead", "TeamMember" and "AccountManager" can only use this end point.` })
  @ApiOperation({ summary: `Create a new Task.`, description: `This endpoint allows you to create a new task. This is accessible for "BUHead" , "ResourceManager", "DeliveryManager","Recruiter", "TeamLead", "TeamMember" and "AccountManager".` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager, Role.TeamMember)
  @Roles()
  create(@Req() req: any, @Body() createTaskDto: CreateTaskDto) {
    createTaskDto.createdBy = req.user._id;
    return this.taskService.create(createTaskDto);
  }

  @Post(':parentTaskId')
  @ApiResponse({ status: 201, description: `Sub-task is saved.` })
  @ApiResponse({ status: 401, description: `Unauthorized.` })
  @ApiResponse({ status: 400, description: `Bad Request / Data.` })
  @ApiResponse({ status: 403, description: `Forbidden, User with role "BUHead" , "ResourceManager", "DeliveryManager","Recruiter", "TeamLead", "TeamMember" and "AccountManager" can only use this end point.` })
  @ApiOperation({ summary: `Create a new Sub-task.`, description: `This endpoint allows you to create a new sub-task. This is accessible for "BUHead" , "ResourceManager", "DeliveryManager","Recruiter", "TeamLead", "TeamMember" and "AccountManager".` })
  @ApiParam({ name: 'parentTaskId', description: `Id of parent-task.` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.TeamMember, Role.Recruiter, Role.AccountManager)
  @Roles()
  createSubTask(@Req() req: any, @Param('parentTaskId') parentTaskId: string, @Body() createTaskDto: CreateTaskDto) {
    const objId = validateObjectId(parentTaskId);
    createTaskDto.createdBy = req.user._id;
    return this.taskService.createSubTask(createTaskDto, objId);
  }

  @Get('all')
  @ApiResponse({ status: 200, description: `Tasks retrieved.` })
  @ApiResponse({ status: 401, description: `Unauthorized.` })
  // @ApiResponse({ status: 403, description: `Forbidden, user with role "super-admin", "admin" and "sales-rep" can only use this end point.` })
  @ApiOperation({ summary: `Retrieve all tasks`, description: `This endpoint returns a list of all tasks. This is accessible for everyone.` })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number', example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of items per page', example: 10 })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager)
  @Roles()
  findAll(@Query('page') page: number = 1, @Query('limit') limit: number = 10) {
    return this.taskService.findAll(page, limit);
  }

  @Get('created-tasks')
  @ApiResponse({ status: 200, description: `Task is retrieved.` })
  @ApiResponse({ status: 404, description: `Task is not found.` })
  @ApiResponse({ status: 401, description: `Unauthorized. ` })
  // @ApiResponse({ status: 403, description: `Forbidden user with role "super-admin", "admin" and "sales-rep" can only use this end point.` })
  @ApiOperation({ summary: `Retrieve all tasks created by the logged-in user`, description: `This endpoint returns a list of tasks created by the logged-in user. This is accessible for everyone.` })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number', example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of items per page', example: 10 })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager)
  @Roles()
  findCreatedTasks(@Req() req: any, @Query('page') page: number = 1, @Query('limit') limit: number = 10) {
    return this.taskService.findCreatedTasks(req.user._id, page, limit);
  }

  @Get('assigned-tasks')
  @ApiResponse({ status: 200, description: `Tasks retrieved successfully.` })
  @ApiResponse({ status: 404, description: `No tasks found.` })
  @ApiResponse({ status: 401, description: `Unauthorized.` })
  @ApiOperation({
    summary: `Retrieve all tasks assigned to the logged-in user`,
    description: `This endpoint returns a list of tasks categorized as today's tasks, upcoming tasks, and past tasks. `
  })
  @ApiQuery({ name: 'search', required: false, description: 'Filter tasks by title or description' })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager, Role.TeamMember)
  @Roles()
  async findUserTasks(@Req() req: any, @Query('search') search?: string) {
    return this.taskService.getUserTasksByDate(req.user, search);
  }


  @Get('find-by-org-and-contact')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager, Role.TeamMember)
  @Roles()
  @ApiOperation({
    summary: 'Search tasks by contact and/or org ID',
    description: `This endpoint returns a list of tasks filtered by contact and/or org ID.This is accessible for everyone`,
  })
  @ApiResponse({ status: 200, description: 'tasks retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  // @ApiResponse({ status: 403, description: 'Forbidden, user with role admin, superAdmin, and sales rep can only use this endpoint.' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number', example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of items per page', example: 10 })
  @ApiQuery({ name: 'filter', description: 'Filter for tasks (today, upcoming, specificDate)', required: true })
  @ApiQuery({ name: 'specificDate', description: 'Filter for tasks based on specific date', required: false })
  @ApiQuery({ name: 'vendorId', description: 'Optional vendor ID to filter tasks', required: false })
  async findByContactAndOrg(@Query('filter') filter: string, @Query('specificDate') specificDate: string, @Query() query: QueryDTO, @Query('page') page: number = 1, @Query('limit') limit: number = 10, @Query('vendorId') vendorId?: string,) {
    // Search tasks by both contact and org ID
    return this.taskService.findByContactAndOrg(page, limit, query.orgId, vendorId, query.contactId, filter, specificDate);
  }

  @Get(':parentTaskId/sub-tasks')
  @ApiResponse({ status: 200, description: `Task is retrieved.` })
  @ApiResponse({ status: 404, description: `Task is not found.` })
  @ApiResponse({ status: 401, description: `Unauthorized. ` })
  // @ApiResponse({ status: 403, description: `Forbidden user with role "super-admin", "admin" and "sales-rep" can only use this end point.` })
  @ApiOperation({ summary: `Retrieve all child tasks assigned to the parentTask`, description: `This endpoint returns a list of child tasks. This is accessible for everyone.` })
  @ApiParam({ name: 'parentTaskId', description: `Id of the parentTask.` })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number', example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of items per page', example: 10 })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager, Role.TeamMember)
  @Roles()
  findChildTasks(@Param('parentTaskId') parentTaskId: string, @Query('page') page: number = 1, @Query('limit') limit: number = 10) {
    const objId = validateObjectId(parentTaskId);
    return this.taskService.findChildTasks(objId, page, limit);
  }

  @Get(':taskId')
  @ApiResponse({ status: 200, description: `Task is retrieved.` })
  @ApiResponse({ status: 404, description: `Task is not found.` })
  @ApiResponse({ status: 401, description: `Unauthorized. ` })
  // @ApiResponse({ status: 403, description: `Forbidden user with role "super-admin", "admin" and "sales-rep" can only use this end point.` })
  @ApiOperation({ summary: `Retrieve an task by Id`, description: `This endpoint returns an task by its Id. This is accessible for everyone.` })
  @ApiParam({ name: 'taskId', description: `Id of the task.` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager, Role.TeamMember)
  @Roles()
  findOne(@Param('taskId') taskId: string) {
    const objId = validateObjectId(taskId);
    return this.taskService.findOne(objId);
  }


  @Patch(':taskId')
  @ApiParam({ name: 'taskId', description: `Id of the Task.` })
  @ApiResponse({ status: 200, description: `Task updated.` })
  @ApiResponse({ status: 401, description: `Unauthorized.` })
  @ApiResponse({ status: 403, description: `Forbidden, user with role "BUHead" , "ResourceManager", "DeliveryManager","Recruiter", "TeamLead", "TeamMember" and "AccountManager" use this end point.` })
  @ApiOperation({ summary: `Update an task by id`, description: `This endpoint updates an task by Id. This is accessible for "BUHead" , "ResourceManager", "DeliveryManager","Recruiter", "TeamLead", "TeamMember" and "AccountManager".` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.TeamMember, Role.AccountManager)
  @Roles()
  update(@Req() req: any, @Param('taskId') taskId: string, @Body() updateTaskDto: UpdateTaskDto) {
    const objId = validateObjectId(taskId);
    return this.taskService.update(objId, updateTaskDto, req.user);
  }


  @Patch(':taskId/update-assignee')
  @ApiParam({ name: 'taskId', description: `Id of the Task.` })
  @ApiResponse({ status: 200, description: `Task updated.` })
  @ApiResponse({ status: 401, description: `Unauthorized.` })
  @ApiResponse({ status: 403, description: `Forbidden, user with role "BUHead" , "ResourceManager","TeamMember", "DeliveryManager","Recruiter", "TeamLead" and "AccountManager" use this end point.` })
  @ApiOperation({ summary: `Update assignee of the task`, description: `This endpoint update assignee of the task. This is accessible for "BUHead" , "ResourceManager", "TeamMember", "DeliveryManager","Recruiter", "TeamLead" and "AccountManager".` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.TeamMember, Role.Recruiter, Role.AccountManager)
  @Roles()
  assignTo(@Req() req: any, @Param('taskId') taskId: string, @Body() moveToDto: MoveToDto) {
    const taskObjId = validateObjectId(taskId);
    const { assignTo, comment } = moveToDto;
    // validateObjectId(moveTo);
    this.logger.log(req)
    return this.taskService.assignTo(taskObjId, assignTo ?? [], comment, req.user);
  }

  @Patch(':taskId/update-status')
  @ApiParam({ name: 'taskId', description: `Id of the Task.` })
  @ApiResponse({ status: 200, description: `Task updated.` })
  @ApiResponse({ status: 401, description: `Unauthorized.` })
  @ApiResponse({ status: 403, description: `Forbidden, user with role "BUHead" , "ResourceManager", "DeliveryManager","Recruiter", "TeamLead", "TeamMember" and "AccountManager" use this end point.` })
  @ApiOperation({ summary: `Update the status of the task`, description: `This endpoint update the status of the task. This is accessible for "BUHead" , "ResourceManager", "DeliveryManager","Recruiter", "TeamLead", "TeamMember" and "AccountManager".` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.TeamMember, Role.Recruiter, Role.AccountManager)
  @Roles()
  changeStatus(@Req() req: any, @Param('taskId') taskId: string, @Body() changeStatusTaskDto: ChangeStatusTaskDto) {
    const objId = validateObjectId(taskId);
    return this.taskService.changeStatus(objId, changeStatusTaskDto, req.user);
  }

  @Delete(':taskId')
  @ApiResponse({ status: 200, description: `Task is deleted.` })
  @ApiResponse({ status: 401, description: `Unauthorized.` })
  @ApiResponse({ status: 403, description: `Forbidden, user with role "BUHead" , "ResourceManager", "DeliveryManager","Recruiter", "TeamMember", "TeamLead" and "AccountManager"can only use this end point.` })
  @ApiParam({ name: 'taskId', description: `Id of the task.` })
  @ApiOperation({ summary: `Delete an task by Id`, description: `This endpoint deletes an task by Id. This is accessible for "BUHead" , "ResourceManager", "DeliveryManager","Recruiter", "TeamMember", "TeamLead" and "AccountManager".` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.TeamMember, Role.Recruiter, Role.AccountManager)
  @Roles()
  remove(@Req() req: any, @Param('taskId') taskId: string) {
    const objId = validateObjectId(taskId);
    return this.taskService.remove(objId, req.user);
  }

  @Get('recurring-tasks')
  @ApiResponse({ status: 200, description: `Recurring tasks retrieved.` })
  @ApiResponse({ status: 401, description: `Unauthorized.` })
  // @ApiResponse({ status: 403, description: `Forbidden, user with role "super-admin", "admin" and "sales-rep" can only use this end point.` })
  @ApiOperation({ summary: `Retrieve all recurring tasks`, description: `This endpoint returns a list of all recurring tasks. This is accessible for everyone.` })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number', example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of items per page', example: 10 })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager, Role.TeamMember)
  @Roles()
  recurringTasks(@Query('page') page: number = 1, @Query('limit') limit: number = 10) {
    return this.taskService.recurringTasks(page, limit);
  }

  @Get('date-filter')
  @ApiResponse({ status: 200, description: `Recurring tasks retrieved.` })
  @ApiResponse({ status: 401, description: `Unauthorized.` })
  // @ApiResponse({ status: 403, description: `Forbidden, user with role "super-admin", "admin" and "sales-rep" can only use this end point.` })
  @ApiOperation({ summary: `Filtering tasks`, description: `This endpoint filter today's, upcoming and particular date tasks . This is accessible for everyone.` })
  @ApiQuery({ name: 'filter', description: 'Filter for tasks (today, upcoming, specificDate)', required: true })
  @ApiQuery({ name: 'specificDate', description: 'Filter for tasks based on specific date', required: false })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number', example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of items per page', example: 10 })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager, Role.TeamMember)
  @Roles()
  dateFilter(@Req() req: any, @Query('filter') filter: string, @Query('specificDate') specificDate: string, @Query('page') page: number = 1, @Query('limit') limit: number = 10,) {
    return this.taskService.dateFilter(req.user, filter, specificDate, page, limit);
  }

  @Get('count-by-status')
  @ApiResponse({ status: 200, description: `Task's count is retrieved.` })
  @ApiResponse({ status: 404, description: `Unable to find task's count.` })
  @ApiResponse({ status: 401, description: `Unauthorized. ` })
  // @ApiResponse({ status: 403, description: `Forbidden user with role "super-admin", "admin" and "sales-rep" can only use this end point.` })
  @ApiOperation({ summary: `Retrieve task's count based on status`, description: `This endpoint returns task's count based on status.This is accessible for everyone.` })
  @ApiQuery({ name: 'status', description: 'Status of the task (TO-DO, IN-PROGRESS, COMPLETED)', required: true })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager, Role.TeamMember)
  @Roles()
  getTasksCountByStatus(@Req() req: any, @Query('status') status: string) {
    return this.taskService.getTasksCountByStatus(status, req.user._id);
  }

  @Post(':taskId/comments')
  @ApiResponse({ status: 201, description: 'Comment added to the task.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Data.' })
  @ApiResponse({ status: 403, description: 'Forbidden, User with role "BUHead" , "ResourceManager", "DeliveryManager","Recruiter", "TeamLead", "TeamMember" and "AccountManager" can only use this end point.' })
  @ApiOperation({ summary: 'Comment on the task.', description: 'This endpoint allows you to add comments on the task. This is accessible for "BUHead" , "ResourceManager", "DeliveryManager","Recruiter", "TeamLead", "TeamMember" and "AccountManager".' })
  @ApiParam({ name: 'taskId', description: 'Id of the task.' })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager, Role.TeamMember)
  @Roles()
  addComment(@Req() req: any, @Param('taskId') taskId: string, @Body() commentDto: CommentDto) {
    const objId = validateObjectId(taskId);
    commentDto.user = req.user._id;
    return this.taskService.addComment(objId, commentDto);
  }

  @Get(':taskId/comments')
  @ApiResponse({ status: 200, description: 'Comments retrieved successfully.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  // @ApiResponse({ status: 403, description: 'Forbidden, User with role "super-admin", "admin" and "sales-rep" can only use this end point.' })
  @ApiOperation({ summary: 'Get comments of the task.', description: 'This endpoint allows you to retrieve comments on the task. This is accessible for everyone.' })
  @ApiParam({ name: 'taskId', description: 'Id of the task.' })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager, Role.TeamMember)
  @Roles()
  getComments(@Param('taskId') taskId: string) {
    const objId = validateObjectId(taskId);
    return this.taskService.getComments(objId);
  }

  @Post('search')
  @ApiOperation({ summary: 'Search for task', description: `This endpoint allows you to search for task. This is accessible for "BUHead" , "ResourceManager", "DeliveryManager","Recruiter","TeamMember", "TeamLead" and "AccountManager"` })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager, Role.TeamMember, Role.Admin)
  @Roles()
  @ApiResponse({ status: 201, description: 'Task found.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Data ' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role "BUHead" , "ResourceManager", "DeliveryManager","Recruiter", "TeamLead", "TeamMember" and "AccountManager" can only use this end point.' })
  @ApiQuery({ name: 'name', required: true, type: String, description: 'Name of task', example: "John doe" })
  search(@Query('name') name: string) {
    return this.taskService.searchTasks(name);
  }

  @Post('members/:memberId/assign')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.DeliveryManager)
  @Roles()
  @ApiOperation({ summary: 'Assign a job to a member', description: 'This endpoint assigns a job to a specified member. This is accessible only for "DeliveryManager"' })
  @ApiResponse({ status: 201, description: 'Job assigned successfully.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role "DeliveryManager" can only use this endpoint.' })
  assignJobToMember(@Req() req: any, @Param('memberId') memberId: string, @Body() memberJobAssignDto: MemberJobAssignDto) {
    const member = validateObjectId(memberId);
    return this.taskService.assignJobToMember(req.user, member, memberJobAssignDto);
  }


  @Get('members/:memberId/all-assigned')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.AccountManager)
  @Roles()
  @ApiOperation({ summary: 'Retrieve all assigned jobs for a member', description: 'This endpoint returns a list of all jobs assigned to a specified member. This is accessible only for "Admin", "BUHead", "ResourceManager", "DeliveryManager" and "AccountManager"' })
  @ApiResponse({ status: 200, description: 'Assigned jobs retrieved successfully.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role "Admin", "BUHead", "ResourceManager", "DeliveryManager" and "AccountManager" can only use this endpoint.' })
  async getAllMemberAssignedJobs(@Param('memberId') memberId: string) {
    const member = validateObjectId(memberId);
    return this.taskService.getAllMemberAssignedJobs(member);
  }


  @Get('members/:memberId/assigned')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.AccountManager)
  @Roles()
  @ApiOperation({ summary: 'Retrieve assigned job for a member', description: 'This endpoint returns a job assigned to a specified member. This is accessible only for "Admin", "BUHead", "ResourceManager", "DeliveryManager" and "AccountManager"' })
  @ApiResponse({ status: 200, description: 'Assigned jobs retrieved successfully.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role "Admin", "BUHead", "ResourceManager", "DeliveryManager" and "AccountManager" can only use this endpoint.' })
  @ApiQuery({ name: 'jobId', required: true, description: 'Filter by job ID' })
  async getAssignedJobs(@Param('memberId') memberId: string, @Query('jobId') jobId: string) {
    const member = validateObjectId(memberId);
    const job = validateObjectId(jobId);
    return this.taskService.getAssignedJobs(member, job);
  }

  @Delete('members/:memberId/unassign')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.DeliveryManager, Role.Admin,)
  @Roles()
  @ApiOperation({ summary: 'Unassign a job from a member', description: 'This endpoint unassigns a job from a specified member. This is accessible only for "DeliveryManager"' })
  @ApiResponse({ status: 200, description: 'Job unassigned successfully.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role "DeliveryManager" can only use this endpoint.' })
  @ApiQuery({ name: 'jobId', required: true, description: 'Filter by job ID' })
  async unassignJob(@Param('memberId') memberId: string, @Query('jobId') jobId: string) {
    const member = validateObjectId(memberId);
    const job = validateObjectId(jobId);
    return this.taskService.unassignJob(member, job);
  }


  @Post('vendors/:vendorId/assign')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.DeliveryManager)
  @Roles()
  @ApiOperation({ summary: 'Assign a job to a vendor', description: ' This is accessible only for "DeliveryManager"' })
  @ApiResponse({ status: 201, description: 'Job assigned successfully.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role "DeliveryManager" can only use this endpoint.' })
  async assignJobToVendor(@Req() req: any, @Param('vendorId') vendorId: string, @Body() vendorJobAssignDto: VendorJobAssignDto) {
    const vendor = validateObjectId(vendorId);
    return this.taskService.assignJobToVendor(req.user, vendor, vendorJobAssignDto);
  }

  @Get('vendors/:vendorId/all-assigned')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.AccountManager)
  @Roles()
  @ApiOperation({ summary: 'Retrieve all assigned jobs for a member', description: 'This endpoint returns a list of all jobs assigned to a specified member. This is accessible only for "Admin", "BUHead", "ResourceManager", "DeliveryManager" and "AccountManager"' })
  @ApiResponse({ status: 200, description: 'Assigned jobs retrieved successfully.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden,  user with role "Admin", "BUHead", "ResourceManager", "DeliveryManager" and "AccountManager" can only use this endpoint.' })
  async getAllVendorAssignedJobs(@Param('vendorId') vendorId: string) {
    const vendor = validateObjectId(vendorId);
    return this.taskService.getAllVendorAssignedJobs(vendor);
  }

  @Get('vendors/:vendorId/assigned')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.AccountManager)
  @Roles()
  @ApiOperation({ summary: 'Retrieve all assigned jobs for a vendor', description: 'This is accessible only for "Admin", "BUHead", "ResourceManager", "DeliveryManager" and "AccountManager"' })
  @ApiResponse({ status: 200, description: 'Assigned jobs retrieved successfully.' })
  @ApiResponse({ status: 403, description: 'Forbidden,  user with role "Admin", "BUHead", "ResourceManager", "DeliveryManager" and "AccountManager" can only use this endpoint.' })
  @ApiQuery({ name: 'jobId', required: true, description: 'Filter by job ID' })
  async getVendorAssignedJobs(@Param('vendorId') vendorId: string, @Query('jobId') jobId: string) {
    const vendor = validateObjectId(vendorId);
    const job = validateObjectId(jobId);
    return this.taskService.getVendorAssignedJobs(vendor, job);
  }

  @Delete('vendors/:vendorId/unassign')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.DeliveryManager)
  @Roles()
  @ApiOperation({ summary: 'Unassign a job from a vendor', description: 'This is accessible only for "DeliveryManager"' })
  @ApiResponse({ status: 200, description: 'Job unassigned successfully.' })
  @ApiResponse({ status: 403, description: 'Forbidden, user with role "DeliveryManager" can only use this endpoint.' })
  @ApiQuery({ name: 'jobId', required: true, description: 'Job ID to unassign' })
  async unassignVendorJob(@Param('vendorId') vendorId: string, @Query('jobId') jobId: string) {
    const vendor = validateObjectId(vendorId);
    const job = validateObjectId(jobId);
    return this.taskService.unassignVendorJob(vendor, job);
  }

  @Get('analytics')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.Recruiter, Role.AccountManager)
  @Roles()
  @ApiOperation({ summary: 'Retrieve all assigned jobs for a vendor', description: 'This is accessible for everyone' })
  @ApiResponse({ status: 200, description: 'Assigned jobs retrieved successfully.' })
  @ApiQuery({ name: 'jobId', required: true, description: 'Filter by job ID' })
  async getAnalytics(@Query('jobId') jobId: string) {
    if (!jobId || !Types.ObjectId.isValid(jobId)) {
      throw new BadRequestException('Not a valid MongoDB Object ID.');
    }

    // Convert jobId to Types.ObjectId
    const validJobId = new Types.ObjectId(jobId);
    return this.taskService.getAnalytics(validJobId);
  }


  @Get('recruiter-tasks')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Recruiter)
  @Roles()
  @ApiOperation({ summary: "Retrieve today's and upcoming task count for recruiter" })
  @ApiResponse({ status: 200, description: "Task counts retrieved successfully." })
  @ApiResponse({ status: 401, description: "Unauthorized." })
  async getRecruiterTaskCounts(@Req() req: any) {
    return this.taskService.getRecruiterTaskCounts(req.user);
  }
}
