import { Module } from '@nestjs/common';
import { OfferService } from './offer.service';
import { OfferController } from './offer.controller';
import { JwtModule } from '@nestjs/jwt';
import { MongooseModule } from '@nestjs/mongoose';
import { Offer, OfferSchema } from './schemas/offer.schema';
import { EmailTemplate, EmailTemplateSchema } from 'src/email-template-builder/schemas/email-template-builder.schema';
import { Org, OrgSchema, Placeholder, PlaceholderSchema } from 'src/org/schemas/org.schema';
import { JobApplication, JobApplicationSchema } from 'src/job-application-form/schemas/job-application.schema';
import { EndpointsRolesModule } from 'src/endpoints-roles/endpoints-roles.module';
import { Job, JobSchema } from 'src/job/schemas/job.schema';
import { JobAllocationBase, JobAllocationBaseSchema } from 'src/job-allocation/schemas/job-allocation-base.schema';
import { Bgv, BgvSchema } from './schemas/bgv.schema';
import { Employee, EmployeeSchema } from 'src/employee/schemas/employee.schema';
import { BusinessUnit, BusinessUnitSchema } from 'src/business-unit/schemas/business-unit.schema';
import { BgvHandler, BgvHandlerSchema } from 'src/bgv-handler/schemas/bgv-handler.schema';
import { Invoice, InvoiceSchema } from 'src/invoices/schemas/invoice.schema';
import { FileMetadata, FileMetadataSchema } from 'src/file-upload/schemas/file-metadata.schema';
import { HikeApproval, HikeApprovalSchema } from './schemas/hike-approval.schema';
@Module({
  controllers: [OfferController],
  providers: [OfferService],
  imports: [
    JwtModule, EndpointsRolesModule,
    MongooseModule.forFeature([
      { name: Offer.name, schema: OfferSchema },
      { name: EmailTemplate.name, schema: EmailTemplateSchema },
      { name: Placeholder.name, schema: PlaceholderSchema },
      { name: JobApplication.name, schema: JobApplicationSchema },
      { name: Job.name, schema: JobSchema },
      { name: Org.name, schema: OrgSchema },
      { name: JobAllocationBase.name, schema: JobAllocationBaseSchema },
      { name: Bgv.name, schema: BgvSchema },
      { name: Employee.name, schema: EmployeeSchema },
      { name: BusinessUnit.name, schema: BusinessUnitSchema },
      { name: BgvHandler.name, schema: BgvHandlerSchema },
      { name: Invoice.name, schema: InvoiceSchema },
      {name: FileMetadata.name, schema:FileMetadataSchema},
      {name: HikeApproval.name, schema:HikeApprovalSchema}
      

    ])
  ],
  exports: [MongooseModule]
})
export class OfferModule { }
