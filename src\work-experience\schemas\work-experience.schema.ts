import { Prop, Schema, SchemaFactory, raw } from "@nestjs/mongoose";
import { HydratedDocument, Types } from "mongoose";
import { Job } from "src/job/schemas/job.schema";
import { BasicUser } from "src/user/schemas/basic-user.schema";

export type WorkExperienceDocument = HydratedDocument<WorkExperience>;
@Schema({
    timestamps: true
})
export class WorkExperience {

    @Prop({
        type: Types.ObjectId,
        required: true,
        ref: 'Job'
    })
    jobId: Job;

    @Prop({
        type: String,
        required: true,
        trim: true,
    })
    jobTitle: string;

    @Prop({
        type: String,
        required: true,
        trim: true,
    })
    companyName: string;

    @Prop({
        type: Date,
        required: false,
    })
    jobStartDate?: Date;

    @Prop({
        type: Date,
        required: false,
    })
    jobEndDate?: Date;

    @Prop({
        type: Boolean,
        required: false,
    })
    currentlyWorking?: boolean;

    @Prop({
        required: true,
        type: Types.ObjectId, ref: 'BasicUser'
      })
      createdBy: BasicUser;

}



export const WorkExperienceSchema = SchemaFactory.createForClass(WorkExperience);