import { IsOptional } from 'class-validator';
import { Transform, TransformFnParams } from 'class-transformer';
import { sanitizeWithStyle } from "src/utils/sanitize-with-style";
export class QueryDTO {

    @IsOptional()
    @Transform(({ value }) => {
        return value === undefined || value === '0' ? '' : value;
    })
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    industryId?: string;

    @IsOptional()
    @Transform(({ value }) => {
        return value === undefined || value === '0' ? '' : value;
    })
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    accountTypeId?: string;

    @IsOptional()
    @Transform(({ value }) => {
        return value > 0 ? value : 1;
    })
    page: number = 1;

    @IsOptional()
    @Transform(({ value }) => {
        return value > 0 ? value : 10;
    })
    limit: number = 10;
}
