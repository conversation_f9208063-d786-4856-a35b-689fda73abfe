import { ApiProperty } from "@nestjs/swagger";
import { IsString, IsNotEmpty, IsArray } from "class-validator";
import { CommentDto } from "./comment.dto";
import { Transform, TransformFnParams } from 'class-transformer';
import sanitizeWithStyle from 'sanitize-html'

export class MoveToDto {

  @ApiProperty({
    required: true,
    type: String,
    description: 'Reference to the Owner account, contact, client and task',
  })
  // @IsNotEmpty()
  @IsString()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  moveTo: string;

  @ApiProperty({
    type: [String],
    required: true,
    description: 'Array of user IDs to assign',
  })
  @IsArray()
  @IsNotEmpty()
  @Transform(({ value }) => {
    if (Array.isArray(value)) {
      return value.map((v) => sanitizeWithStyle(v));
    }
    return [];
  })
  assignTo?: string[];

  @ApiProperty({
    type: CommentDto
  })
  comment: CommentDto;

}