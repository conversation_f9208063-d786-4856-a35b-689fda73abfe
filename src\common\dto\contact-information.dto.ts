import { ApiProperty } from "@nestjs/swagger";
import { IsBoolean, IsEmail, IsNotEmpty, IsOptional, IsPhoneNumber, IsString, ValidateNested } from "class-validator";
import { Transform, TransformFnParams, Type } from 'class-transformer';
import sanitizeWithStyle from 'sanitize-html'
import { AddressInformationDto } from "./address-information.dto";

export class ContactInformationDto {
  @ApiProperty({
    type: String,
    required: false,
    description: 'Contact e-mail'
  })
  @IsOptional()
  @IsString()
  @IsEmail()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value).toLowerCase())
  contactEmail?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Contact number'
  })
  @IsString()
  @IsOptional()
  // @IsPhoneNumber()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  contactNumber?: string;

  @ApiProperty({
    type: Boolean,
    required: false,
    description: 'Primary contact'
  })
  @IsOptional()
  @IsBoolean()
  isPrimary?: boolean;
}
