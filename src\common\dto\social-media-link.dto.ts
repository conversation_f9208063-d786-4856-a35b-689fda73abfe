import { ApiProperty } from "@nestjs/swagger";
import { IsString, IsNotEmpty } from "class-validator";
import { Transform, TransformFnParams } from 'class-transformer';
import sanitizeWithStyle from 'sanitize-html'

export class SocialMediaLinkDto {

    @ApiProperty({
        required: true,
        type: String,
        description: 'Social media platform name.',
    })
    @IsNotEmpty()
    @IsString()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    platform: string;


    @ApiProperty({
        required: true,
        type: String,
        description: 'URL of the social media platform.',
    })
    @IsNotEmpty()
    @IsString()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    url: string;

}