

import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
// import { AuthService } from '../../auth.service';
// import { Reflector } from '@nestjs/core';
// import { jwtConstants } from '../constants';
import { WsException } from '@nestjs/websockets';
import { Logger } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { omit } from 'lodash';

// import Cryptr from 'cryptr';
// const Cryptr = require('cryptr');

@Injectable()
export class WebSocketAuthGuard implements CanActivate {

  cryptr: any;
  private readonly logger = new Logger(WebSocketAuthGuard.name);
  client:any;

  constructor(  private readonly jwtService: JwtService, private configService: ConfigService) {
    // super();
    // this.cryptr = new Cryptr(process.env.ENCRYPT_JWT_SECRET);
    // this.cryptr = new Cryptr(this.configService.get<string>('SECRET_KEY'));

  }

    async canActivate(context: ExecutionContext) {
      try {
        const client = context.switchToWs().getClient();
        this.client = client;
        const data = context.switchToWs().getData();

        const authToken = data?.headers?.token || data?.auth?.token;

        if (!authToken) {
          context.switchToWs().getData().user = null;
          this.logger.debug(`Unauthorized. No token.`);
          client.send(JSON.stringify({
            event: 'auth.error',
            message: 'Unauthorized. No token.'
          }));
          throw new WsException('Unauthorized. No token.');

        }
        // const decryptedString = this.cryptr.decrypt(authToken);

        try {
          const jwtPayload = <any>this.jwtService.verify(authToken);
          // const user: any = await this.authService.validateUser(jwtPayload);

          const payload = await this.jwtService.verifyAsync(
            authToken,
            {
              secret: this.configService.get<string>('SECRET_KEY')
            }
          );
          // 💡 We're assigning the payload to the request object here
          // so that we can access it in our route handlers
          const safeUserData = omit(payload, ['password']);
          this.logger.debug(`User on request is set to \n${JSON.stringify(safeUserData)}`);
          // Bonus if you need to access your user after the guard
          context.switchToWs().getData().user = safeUserData;
          this.logger.debug(`User authenticated.`);
          // client.emit('auth.verified', JSON.stringify({
          //   event: 'auth.verified',
          //   message: 'Token verified.'
          // }));
          return Boolean(safeUserData);
        } catch (error) {
          this.logger.error(`Error in validating auth token. ${error}`)
            context.switchToWs().getData().user = null;
            // client.send('auth.error');
            // client.emit('auth.error', JSON.stringify({
            //   event: 'auth.error',
            //   message: 'Unauthorized. Invalid token.'
            // }));
            // return false;
            throw new WsException('Unauthorized. Invalid token.');

        }
        // this.logger.log(`payload ${JSON.stringify(jwtPayload)}`);


      } catch (err) {
        this.logger.error(`Error in WebSocket Auth - ${err}`);
        // this.client?.emit('auth.error', JSON.stringify({
        //   event: 'auth.error',
        //   message: 'Unauthorized. Invalid token.'
        // }));
        throw new WsException(`Unknown error in WebSocket Auth. ${err}`);
      }
    }
}
