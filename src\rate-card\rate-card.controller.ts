import {
  Controller,
  Get,
  Post,
  Patch,
  Delete,
  Param,
  Body,
  Query,
  Req,
  UseGuards,
  BadRequestException,
  Logger,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { Roles } from 'src/auth/decorators/roles.decorator';
import { RateCardService } from './rate-card.service';
import { CreateRateCardDto } from './dto/create-rate-card.dto';
import { UpdateRateCardDto } from './dto/update-rate-card.dto';
import { AuthJwtGuard } from 'src/auth/guards/auth-jwt/auth-jwt.guard';
import { validateObjectId } from 'src/utils/validation.utils';
import { FilterRateCardDto } from './dto/filter-rate-card.dto';


@ApiTags('rate-cards')
@Controller('')
export class RateCardController {
  private readonly logger = new Logger(RateCardController.name);

  constructor(private readonly rateCardService: RateCardService) { }

  @Post()
  @ApiOperation({ summary: 'Create a new rate card' })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  @Roles()
  @ApiResponse({ status: 201, description: 'Rate card created successfully.' })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  create(@Req() req: any, @Body() createRateCardDto: CreateRateCardDto) {
    createRateCardDto.createdBy = req.user._id;
    if (req.user.org?._id) {
      createRateCardDto.org = req.user.org._id;
    } else {
      throw new BadRequestException('User does not belong to an organization');
    }
    return this.rateCardService.create(createRateCardDto, req.user);
  }

  @Get()
  @ApiOperation({ summary: 'Get all rate cards' })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  @Roles()
  @ApiResponse({ status: 200, description: 'Rate cards retrieved successfully.' })
  findAll(@Req() req: any, @Query() query: FilterRateCardDto) {
    if (!query.org && req.user.org) {
      query.org = req.user.org._id;
    }
    return this.rateCardService.findAll(query);
  }

  @Get('count')
  @ApiOperation({ summary: 'Get count of rate cards' })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  @Roles()
  @ApiResponse({ status: 200, description: 'Count retrieved successfully.' })
  getCount(@Req() req: any, @Query() query: FilterRateCardDto) {
    if (!query.org && req.user.org) {
      query.org = req.user.org._id;
    }
    return this.rateCardService.countAll(query);
  }

  @Get(':rateCardId')
  @ApiOperation({ summary: 'Get rate card by ID' })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  @Roles()
  @ApiResponse({ status: 200, description: 'Rate card retrieved successfully.' })
  @ApiResponse({ status: 404, description: 'Rate card not found.' })
  @ApiParam({ name: 'rateCardId', description: 'ID of the rate card' })
  findOne(@Param('rateCardId') rateCardId: string) {
    return this.rateCardService.findOne(validateObjectId(rateCardId));
  }

  @Patch(':rateCardId')
  @ApiOperation({ summary: 'Update rate card by ID' })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  @Roles()
  @ApiResponse({ status: 200, description: 'Rate card updated successfully.' })
  @ApiResponse({ status: 404, description: 'Rate card not found.' })
  @ApiParam({ name: 'rateCardId', description: 'ID of the rate card' })
  update(
    @Req() req: any,
    @Param('rateCardId') rateCardId: string,
    @Body() dto: UpdateRateCardDto,
  ) {
    return this.rateCardService.update(validateObjectId(rateCardId), dto, req.user);
  }

  @Delete(':rateCardId/hard-delete')
  @ApiOperation({ summary: 'Hard delete rate card by ID' })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  @Roles()
  @ApiResponse({ status: 200, description: 'Rate card hard deleted successfully.' })
  @ApiResponse({ status: 404, description: 'Rate card not found.' })
  @ApiParam({ name: 'rateCardId', description: 'ID of the rate card' })
  hardDelete(@Param('rateCardId') rateCardId: string, @Req() req: any) {
    return this.rateCardService.hardDelete(validateObjectId(rateCardId));
  }

  @Delete(':rateCardId/soft-delete')
  @ApiOperation({ summary: 'Soft delete rate card by ID' })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  @Roles()
  @ApiResponse({ status: 200, description: 'Rate card soft deleted successfully.' })
  @ApiResponse({ status: 404, description: 'Rate card not found.' })
  @ApiParam({ name: 'rateCardId', description: 'ID of the rate card' })
  softDelete(@Param('rateCardId') rateCardId: string, @Req() req: any) {
    return this.rateCardService.softDelete(validateObjectId(rateCardId), req.user);
  }
}
