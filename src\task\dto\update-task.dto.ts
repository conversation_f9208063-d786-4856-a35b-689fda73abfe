import { ApiHideProperty, ApiProperty } from "@nestjs/swagger";
import { Transform, TransformFnParams } from "class-transformer";
import { IsEnum, IsISO8601, IsNotEmpty, IsOptional, IsString, Length,  } from "class-validator";
import { sanitizeWithStyle } from "src/utils/sanitize-with-style";
import { Priority, RecurrenceInterval, Status } from "src/shared/constants";

export class UpdateTaskDto {

    @ApiProperty({
        type: String,
        required: false,
        description: 'Task title',
    })
    @IsString()
    @IsOptional()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    title?: string;

    @ApiProperty({
        type: String,
        required: false,
        description: 'Task summary',
    })
    @IsString()
    @IsOptional()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    summary?: string;

    @ApiProperty({
        type: Date,
        required: false,
        description: 'Task due date',
        default: new Date(Date.UTC(new Date().getUTCFullYear(), new Date().getUTCMonth(), new Date().getUTCDate(), new Date().getUTCHours(), new Date().getUTCMinutes())),
    })
    // YYYY-MM-DDThh:mm:ssZ - example : 2024-05-27T00:00:00.000Z
    @IsISO8601({strict: true})
    @IsOptional()
    @Length(10,24)
    dueDate?: Date;

    @ApiProperty({
        type: String,
        required: false,
        description: 'Task location',
    })
    @IsString()
    @IsOptional()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    location?: string;

    @ApiProperty({
        type: String,
        required: false,
        description: 'Origanization associated with the task',
    })
    @IsString()
    @IsOptional()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    org?: string

    @ApiProperty({
        type: String,
        required: false,
        description: 'Single point of contact for task',
    })
    @IsString()
    @IsOptional()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    spoc?: string;

   
    @ApiProperty({
        type: String,
        required: false,
        default: Priority.LOW,
        enum: Priority,
        description: 'Task priority',
    })
    @IsEnum(Priority)
    @IsString()
    @IsOptional()
    priority?: Priority;

}
