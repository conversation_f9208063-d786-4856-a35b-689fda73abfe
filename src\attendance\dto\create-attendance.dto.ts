import { ApiHideProperty } from '@nestjs/swagger';
import { Transform, TransformFnParams } from 'class-transformer';
import { IsDateString, IsEnum, IsOptional } from 'class-validator';
import { sanitizeWithStyle } from "src/utils/sanitize-with-style";


export class CreateAttendanceDto {

    @ApiHideProperty()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    userId?: string;



    @IsDateString()
    date: string;

    @IsEnum(['present', 'absent', 'leave'])
    status: 'present' | 'absent' | 'leave';

    @IsEnum(['office', 'remote'])
    workLocation: 'office' | 'remote';

    @IsOptional()
    totalWorkHours?: number;

    @IsOptional()
    totalBreakHours?: number;

    @IsOptional()
    effectiveWorkHours?: number;
}

