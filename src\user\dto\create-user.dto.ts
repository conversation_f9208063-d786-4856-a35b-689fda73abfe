import { ApiProperty } from "@nestjs/swagger";
import { <PERSON><PERSON>rray, IsBoolean, IsEmail, IsEnum, IsMongoId, IsNotEmpty, IsOptional, IsString, MaxLength, MinLength } from 'class-validator';
import { Transform, TransformFnParams } from 'class-transformer';
import { sanitizeWithStyle } from 'src/utils/sanitize-with-style';
import { Role } from "src/auth/enums/role.enum";
import { SourceType } from "src/shared/constants";

export class CreateUserDto {
  @ApiProperty({
    type: String,
    required: true,
    description: 'First name of the user',
  })
  @IsString()
  @IsNotEmpty()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  firstName: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Last name of the user',
  })
  @IsString()
  @IsNotEmpty()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  lastName: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Email address of the user',
    format: 'email',
    default: '<EMAIL>',
  })
  @IsNotEmpty()
  @IsEmail()
  @IsString()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value).toLowerCase())
  email: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Password for the user',
  })
  @MinLength(8)
  @MaxLength(128)
  @IsString()
  @IsNotEmpty()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  password: string;

  @ApiProperty({
    type: String,
    required: false,
    default: SourceType.ADMIN_PORTAL,
    enum: SourceType,
    description: 'Source type of the user',
  })
  @IsOptional()
  @IsString()
  @IsEnum(SourceType)
  source?: string;

  @ApiProperty({
    type: [Role],
    required: false,
    description: 'Roles assigned to the user',
    enum: Role,
    default: [Role.User],
    isArray: true,
  })
  @IsArray()
  @IsOptional()
  @IsEnum(Role, { each: true })
  roles?: Role[];

  @ApiProperty({
    type: String,
    required: false,
    description: 'MongoDB ID of the country of the user',
  })
  @IsOptional()
  @IsMongoId({ message: 'Country must be a valid MongoDB ObjectId.' })
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  country?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'MongoDB ID of the state of the user',
  })
  @IsOptional()
  @IsMongoId({ message: 'State must be a valid MongoDB ObjectId.' })
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  state?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'MongoDB ID of the city of the user',
  })
  @IsOptional()
  @IsMongoId({ message: 'City must be a valid MongoDB ObjectId.' })
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  city?: string;


  @ApiProperty({
    type: String,
    required: false,
    description: 'The logo of your org',
  })
  @IsMongoId({ message: 'Logo must be a valid MongoDB ObjectId.' })
  @IsOptional()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  logo?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'The  org',
  })
  @IsMongoId({ message: 'org must be a valid MongoDB ObjectId.' })
  @IsOptional()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  org?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'The  org',
  })
  @IsMongoId({ message: 'org must be a valid MongoDB ObjectId.' })
  @IsOptional()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  companyId?: string;


  @ApiProperty({
    type: Boolean,
    required: false,
    description: 'To send password to the user in email',
  })
  @IsBoolean()
  @IsOptional()
  sendPassword?: boolean;

  @ApiProperty({
    type: [String],
    required: false,
    description: 'RoleIds assigned to the user',
    isArray: true,
  })
  @IsArray()
  @IsOptional()
  roleIds?: String[];

  @ApiProperty({
    type: Boolean,
    required: false,
    description: 'To check user is verified or not',
  })
  @IsBoolean()
  @IsOptional()
  isVerified?: boolean;


}
