import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { Employee } from 'src/employee/schemas/employee.schema';
import { FileMetadata } from 'src/file-upload/schemas/file-metadata.schema';
import { Invoice } from 'src/invoices/schemas/invoice.schema';
import { Org } from 'src/org/schemas/org.schema';
import { Project } from 'src/projects/schemas/project.schema';
import { Task } from 'src/task/schemas/task.schema';
import { BasicUser } from 'src/user/schemas/basic-user.schema';


@Schema({ _id: false })
class DailyEntry {
    @Prop({ type: Date, required: false })
    date?: Date;

    @Prop({ type: Number, default: 0 }) // Direct hours input for weekly/monthly
    hoursWorked: number;

    @Prop({ type: Boolean, default: false })
    isWeekOff: boolean;


    @Prop({ type: Boolean, default: false })
    isInCycle: boolean;

    @Prop({ type: String, required: false }) // Optional remarks for the day
    remarks?: string;
}


@Schema({ _id: false })
class WeeklyEntry {
    @Prop({ required: true }) // e.g. "2025-W27"
    weekLabel: string;

    @Prop({ type: [DailyEntry], default: [] })
    days: DailyEntry[];

    @Prop({ default: 0 }) // Optional
    totalWeekHours: number;
}

@Schema({ timestamps: true })
export class TimeSheet extends Document {

    @Prop({
        required: true,
        type: Types.ObjectId, ref: 'BasicUser'
    })
    employee: BasicUser;


    @Prop({
        required: false,
        type: Types.ObjectId, ref: 'Employee'
    })
    employeeId?: Employee;


    @Prop({ type: [Types.ObjectId], required: false, ref: 'Project' })
    projectId?: Project[]; // or just ObjectId[] if you don't need full population

    @Prop({ type: [Types.ObjectId], required: false, ref: 'Task' })
    taskId?: Task[]; // or just ObjectId[] if you don't need full population

    @Prop({
        required: false,
        trim: true,
    })
    description?: string;



    @Prop({ type: Types.ObjectId, required: false, ref: 'Org' })
    orgId?: Org;

    @Prop({ type: String, enum: ['Daily', 'Weekly', 'Monthly'], required: true })
    timesheetType: 'Daily' | 'Weekly' | 'Monthly';

    //Daily//

    @Prop({ type: Date, required: false })  // ISO 8601 format
    date?: Date;

    @Prop({ type: String, required: false }) // Format: "09:00" or "09:00:00"
    startTime?: string;

    @Prop({ type: String, required: false }) // Format: "17:30" or "17:30:00"
    endTime?: string;


    @Prop({ type: Number, default: 0 }) // Only for daily timesheets
    hoursWorked?: number;



    @Prop() overtimeAmount?: number;


    @Prop({ type: Boolean, default: false }) // Only for daily timesheets
    isWeekOff?: boolean;

    //Weekly//

    @Prop() // For Weekly
    week?: string;

    // New: Weekly view support
    @Prop({ type: [WeeklyEntry], default: [] })
    weeklyEntries?: WeeklyEntry[];

    //monthly

    // Monthly detailed entries (each day of the cycle)
    @Prop({ type: [DailyEntry], default: [] })
    monthlyEntries?: DailyEntry[];

    @Prop() // For Monthly
    month?: string;

    @Prop({ type: Number, default: 0 }) // Number of working days considered for billing
    billableDays?: number;

    @Prop({ type: Number, default: 0 }) // Number of leaves in the month
    leaveDays?: number;

    @Prop({ type: Number, default: 0 }) // billableDays - leaveDays
    totalBillableDays?: number;

    @Prop({ type: Number, default: 0 }) // totalBillableDays * hourlyRate
    totalBillableAmount?: number;

    @Prop({ type: Number, default: 0 }) // totalBillableDays * hourlyRate
    totalBillableAmountHours?: number;



    @Prop({ required: false }) // Set by HR
    hourlyRate?: number;

    @Prop({ type: Number, default: 0 })
    overtimeHours?: number;


    @Prop({ default: 0 }) // Set by HR
    overtimeHourlyRate: number;


    @Prop({ type: [Types.ObjectId], default: [], required: false })
    attachments?: FileMetadata[];

    @Prop({
        type: String,
        enum: ['Draft', 'Submitted', 'Approved', 'Rejected', 'Pending'],
        default: 'Pending',
        index: true
    })
    status: string;

    // Submission related fields
    @Prop({ type: Date })
    submittedAt?: Date;

    @Prop({ type: String, maxlength: 500 })
    submissionComments?: string;

    // Approval related fields
    @Prop({ type: Types.ObjectId, ref: 'BasicUser' })
    approvedBy?: Types.ObjectId;

    @Prop({ type: Date })
    approvedAt?: Date;

    @Prop({ type: String, maxlength: 500 })
    approvalComments?: string;

    @Prop({ type: Number })
    approvedHours?: number; // If approved hours differ from submitted hours

    @Prop({ type: Number })
    originalHours?: number; // Store original hours when approved hours differ

    // Rejection related fields
    @Prop({ type: Types.ObjectId, ref: 'BasicUser' })
    rejectedBy?: Types.ObjectId;

    @Prop({ type: Date })
    rejectedAt?: Date;

    @Prop({ type: String, maxlength: 500 })
    rejectionReason?: string;

    @Prop({ type: String, maxlength: 500 })
    rejectionComments?: string;

    // Recall related fields
    @Prop({ type: Date })
    recalledAt?: Date;

    @Prop({ type: String, maxlength: 500 })
    recallReason?: string;

    // Workflow tracking
    @Prop({
        type: [{
            action: { type: String, enum: ['submitted', 'approved', 'rejected', 'recalled'] },
            performedBy: { type: Types.ObjectId, ref: 'BasicUser' },
            performedAt: { type: Date, default: Date.now },
            comments: { type: String, maxlength: 500 }
        }]
    })
    workflowHistory?: Array<{
        action: string;
        performedBy: Types.ObjectId;
        performedAt: Date;
        comments?: string;
    }>;


    @Prop()
    remarks?: string;

    @Prop({ type: Types.ObjectId, ref: 'BasicUser' }) // Optional HR reviewer
    reviewedBy?: BasicUser;


    @Prop({ type: Date, required: false })  // ISO 8601 format
    reviewedAt?: Date;

    @Prop({
        required: false,
        type: Types.ObjectId, ref: 'BasicUser'
    })
    createdBy: BasicUser;

    @Prop({
        required: false,
        type: Boolean,
        default: false
    })
    isInvoiced: boolean;

    @Prop({
        required: false,
        type: Types.ObjectId,
        ref: 'Invoice',
    })
    invoiceId: Invoice

}

export const TimeSheetSchema = SchemaFactory.createForClass(TimeSheet);

@Schema({ timestamps: true })
export class TimeSheetSettings extends Document {
    @Prop({ required: true }) orgId: string; // or mongoose ObjectId with ref
    @Prop({ type: [Types.ObjectId], ref: 'Project' })
    projectId?: Project[];

    @Prop({ required: true }) weekStartDay: string;
    @Prop({ type: [String], default: [] }) weekends: string[];
    @Prop({ type: Number, required: true })
    monthlyCycleStartDay: number;

    @Prop({ type: Number, required: true })
    monthlyCycleEndDay: number;

    @Prop({
        type: {
            dailyThreshold: { type: Number, default: 8 },      // e.g., overtime after 8 hours/day
            weeklyThreshold: { type: Number, default: 40 },    // e.g., overtime after 40 hours/week
            monthlyThreshold: { type: Number, default: 160 },  // e.g., overtime after 160 hours/month
            allowDailyOvertime: { type: Boolean, default: true },
            allowWeeklyOvertime: { type: Boolean, default: true },
            allowMonthlyOvertime: { type: Boolean, default: true },
        },
        default: {}
    })
    overtimeSettings?: {
        dailyThreshold?: number;
        weeklyThreshold?: number;
        monthlyThreshold?: number;
        allowDailyOvertime?: boolean;
        allowWeeklyOvertime?: boolean;
        allowMonthlyOvertime?: boolean;
    };


}

export const TimeSheetSettingsSchema = SchemaFactory.createForClass(TimeSheetSettings);



// @Prop({ required: true })
// billableHours: number;


// @Prop({ required: true })
// leaveDays: number;

// @Prop({ required: true })
// totalBillable: number;

// @Prop({ default: 0 })
// overtimeDays: number;
