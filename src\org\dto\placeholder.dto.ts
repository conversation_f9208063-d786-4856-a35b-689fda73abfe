import { ApiHideProperty, ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsOptional, IsMongoId, IsEnum } from 'class-validator';
import { EmailTemplateEvent } from 'src/shared/constants';

export class PlaceholderDto {
    @ApiProperty({ description: 'Name of the placeholder' })
    @IsString()
    @IsNotEmpty()
    name: string;

    @ApiProperty({ description: 'Description of the placeholder', required: false })
    @IsString()
    @IsOptional()
    description?: string;

    @ApiProperty({ description: 'Default value of the placeholder', default: 'none', required: false })
    @IsString()
    @IsOptional()
    defaultValue?: 'none';

    @ApiProperty({ description: 'JSON path of the placeholder', required: true })
    @IsString()
    @IsNotEmpty()
    jsonPath: string;

    @ApiProperty({ description: 'Collection name associated with the placeholder', required: false })
    @IsString()
    @IsOptional()
    collectionName?: string;

    @ApiProperty({ description: 'Field name in the collection', required: false })
    @IsString()
    @IsOptional()
    fieldName?: string;

    //Organization ID associated with the placeholder
    @ApiHideProperty()
    @IsMongoId()
    @IsOptional()
    orgId?: string;

    @ApiProperty({ type: Boolean, required: false, default: false, description: 'Default email template' })
    @IsOptional()
    isDefault?: boolean;

    // @ApiProperty({
    //     type: String,
    //     required: false,
    //     enum: EmailTemplateEvent,
    //     description: 'Identifying the email template to which this placeholder belongs'
    // })
    // @IsEnum(EmailTemplateEvent)
    // @IsString()
    // @IsOptional()
    // eventName?: EmailTemplateEvent;

    @IsMongoId()
    @IsOptional()
    @ApiProperty({
        description: 'The ID of the email template.',
        type: String,
        required: false
    })
    emailTemplate?: string;

}
