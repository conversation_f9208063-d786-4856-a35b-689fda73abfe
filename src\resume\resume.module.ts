import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { MongooseModule } from '@nestjs/mongoose';
import { Resumes, ResumeSchema } from './schemas/resume.schema';
import { ResumeController } from './resume.controller';
import { ResumeService } from './resume.service';
import { EndpointsRolesModule } from 'src/endpoints-roles/endpoints-roles.module';

@Module({
  imports: [
    ConfigModule,
    JwtModule, EndpointsRolesModule,
    MongooseModule.forFeature([{ name: Resumes.name, schema: ResumeSchema}])
  ],
  controllers: [ResumeController],
  providers: [ResumeService],
  exports: [ResumeService, MongooseModule],
})
export class ResumeModule {}
