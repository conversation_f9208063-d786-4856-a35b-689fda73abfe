


import { IsMongoId, <PERSON>NotEmpty, IsString, <PERSON><PERSON><PERSON>, Is<PERSON><PERSON>al, IsArray } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Role } from 'src/auth/enums/role.enum';

export class CreateDeleteUserDto {
  @ApiProperty({ description: 'ID of the user being deleted' })
  @IsMongoId()
  @IsNotEmpty()
  userId: string;

  @ApiProperty({ description: 'ID of the org' })
//   @IsMongoId()
  org?: string;

    @ApiProperty({
      type: [Role],
      required: false,
      description: 'Roles assigned to the user',
      enum: Role,
      default: [Role.User],
      isArray: true,
    })
    @IsArray()
    @IsOptional()
    @IsEnum(Role, { each: true })
    roles?: Role[];

  @ApiProperty({ description: 'Reason for deleting the user' })
  @IsString()
  @IsNotEmpty()
  reason: string;

  @ApiProperty({ description: 'Status of the delete request', enum: ['APPROVED', 'REJECTED', 'PENDING'] })
  @IsEnum(['APPROVED', 'REJECTED', 'PENDING'])
  @IsNotEmpty()
  status: string;
}
