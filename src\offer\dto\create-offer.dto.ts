import { ApiHideProperty, ApiProperty } from "@nestjs/swagger";
import { Transform, TransformFnParams } from "class-transformer";
import { IsArray, IsBoolean, IsISO8601, IsMongoId, IsNotEmpty, IsOptional, IsString, Length } from "class-validator";
import { sanitizeWithStyle } from "src/utils/sanitize-with-style";

export class CreateOfferDto {


  @ApiProperty({
    type: String,
    required: true,
    description: ''
  })
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  @IsString()
  jobApplication: string;

  @ApiProperty({
    type: Date,
    required: true,
    description: 'Date of joining',
    default: new Date(Date.UTC(new Date().getUTCFullYear(), new Date().getUTCMonth(), new Date().getUTCDate(), new Date().getUTCHours(), new Date().getUTCMinutes())),
  })
  // YYYY-MM-DDThh:mm:ssZ - example : 2024-05-27T00:00:00.000Z
  @IsISO8601({ strict: true })
  dateOfJoining: Date;

  @ApiProperty({
    type: Number,
    required: true,
    description: ''
  })
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  @IsNotEmpty()
  salaryPerAnnum: number;

  @ApiProperty({
    required: false,
    default: false
  })
  @IsBoolean()
  @IsOptional()
  isAccepted?: boolean;

  @ApiProperty({
    required: false,
    default: false
  })
  @IsBoolean()
  @IsOptional()
  isOnBoarded?: boolean;

  @ApiHideProperty()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  createdBy?: string;

}

