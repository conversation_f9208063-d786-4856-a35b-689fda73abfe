// import { WebSocketGateway, SubscribeMessage, MessageBody, ConnectedSocket } from '@nestjs/websockets';
// import { Socket } from 'socket.io';
// import { MediasoupService } from './mediasoup.service';
// import { MediaKind } from 'mediasoup/node/lib/types';

// @WebSocketGateway({
//     cors: {
//         origin: '*',
//     },
// })
// export class MediasoupGateway {
//     constructor(private readonly mediasoupService: MediasoupService) { }

//     @SubscribeMessage('createRoom')
//     async handleCreateRoom(
//         @ConnectedSocket() client: Socket,
//         @MessageBody() roomId: string
//     ) {
//         try {
//             const room = await this.mediasoupService.createRoom(roomId);
//             return { roomId: room.id };
//         } catch (error) {
//             return { error: error.message };
//         }
//     }

//     @SubscribeMessage('joinRoom')
//     async handleJoinRoom(
//         @ConnectedSocket() client: Socket,
//         @MessageBody() data: { roomId: string; peerId: string }
//     ) {
//         try {
//             const room = await this.mediasoupService.createRoom(data.roomId);
//             const transport = await this.mediasoupService.createWebRtcTransport(
//                 data.roomId,
//                 data.peerId
//             );

//             return {
//                 routerRtpCapabilities: room.router.rtpCapabilities,
//                 transport,
//             };
//         } catch (error) {
//             return { error: error.message };
//         }
//     }

//     @SubscribeMessage('connectTransport')
//     async handleConnectTransport(
//         @ConnectedSocket() client: Socket,
//         @MessageBody() data: {
//             roomId: string;
//             transportId: string;
//             dtlsParameters: any;
//         }
//     ) {
//         try {
//             await this.mediasoupService.connectTransport(
//                 data.roomId,
//                 data.transportId,
//                 data.dtlsParameters
//             );
//             return { success: true };
//         } catch (error) {
//             return { error: error.message };
//         }
//     }

//     @SubscribeMessage('produce')
//     async handleProduce(
//         @ConnectedSocket() client: Socket,
//         @MessageBody() data: {
//             roomId: string;
//             transportId: string;
//             kind: string;
//             rtpParameters: any;
//         }
//     ) {
//         try {
//             const producer = await this.mediasoupService.produce(
//                 data.roomId,
//                 data.transportId,
//                 {
//                     kind: data.kind as MediaKind,
//                     rtpParameters: data.rtpParameters,
//                 }
//             );

//             // Notify other peers in the room about new producer
//             client.to(data.roomId).emit('newProducer', {
//                 producerId: producer.id,
//                 kind: producer.kind,
//             });

//             return { id: producer.id };
//         } catch (error) {
//             return { error: error.message };
//         }
//     }

//     @SubscribeMessage('consume')
//     async handleConsume(
//         @ConnectedSocket() client: Socket,
//         @MessageBody() data: {
//             roomId: string;
//             transportId: string;
//             producerId: string;
//             rtpCapabilities: any;
//         }
//     ) {
//         try {
//             const consumer = await this.mediasoupService.consume(
//                 data.roomId,
//                 data.transportId,
//                 data.producerId,
//                 data.rtpCapabilities
//             );
//             return consumer;
//         } catch (error) {
//             return { error: error.message };
//         }
//     }
// }


// // Creates WebRTC Transports for media transmission.
// // Handles Transport connection using DTLS.
// // Manages Producers (senders) and Consumers (receivers).
