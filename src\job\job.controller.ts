import { FileInterceptor } from '@nest-lab/fastify-multer';
import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  FileTypeValidator,
  Get,
  HttpStatus,
  Logger,
  MaxFileSizeValidator,
  Param,
  ParseFilePipe,
  Patch,
  Post,
  Query,
  Req,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiConsumes,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import axios from 'axios';
import * as express from 'express';
import FormData from 'form-data';
import { Roles } from 'src/auth/decorators/roles.decorator';
import { AuthJwtGuard } from 'src/auth/guards/auth-jwt/auth-jwt.guard';
import { RolesGuard } from 'src/auth/guards/roles/roles.guard';
import { JobAllocationService } from 'src/job-allocation/job-allocation.service';
import { validateObjectId } from 'src/utils/validation.utils';
import { CreateJobDto } from './dto/create-job.dto';
import { JobsQueryDTO } from './dto/query-jobs.dto';
import { ChangeStatusJobDto } from './dto/update-job-status.dto';
import { UpdateJobDto } from './dto/update-job.dto';
import { JobService } from './job.service';

@Controller('')
@ApiTags('Jobs')
export class JobController {
  private readonly logger = new Logger(JobController.name);

  constructor(
    private readonly jobService: JobService,
    private jobAllocationService: JobAllocationService,
  ) {}

  @Post()
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.TeamLead, Role.TeamMember, Role.Vendor, Role.JobSeeker, Role.DeliveryManager)
  @Roles() // TODO: Customer org member can also post jobs. Add role here.
  @ApiResponse({ status: 201, description: 'Job is saved.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Data. ' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  @ApiResponse({
    status: 403,
    description:
      'Forbidden, user with role "BUHead" and "ResourceManager" can use this end point.',
  })
  @ApiOperation({
    summary: 'Create a Job',
    description: `This endpoint for creating a job.This is only accessible for "BUHead" and "ResourceManager".`,
  })
  async create(@Req() req: any, @Body() createJobDto: CreateJobDto) {
    createJobDto.createdBy = req.user._id;
    createJobDto.postingOrg = req.user.org._id;
    createJobDto.isDraft = false;
    return await this.jobService.create(createJobDto, req.user);
  }

  @Post('draft')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.TeamLead, Role.TeamMember, Role.Vendor, Role.JobSeeker)
  @Roles() // TODO: Customer org member can also post jobs. Add role here.
  @ApiResponse({ status: 201, description: 'Job is drafted.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Data. ' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  @ApiResponse({
    status: 403,
    description:
      'Forbidden, user with role "BUHead" and "ResourceManager" can use this end point.',
  })
  @ApiOperation({
    summary: 'Draft a Job',
    description: `This endpoint for drafting a job.This is only accessible for "BUHead" and "ResourceManager".`,
  })
  async createDraft(@Req() req: any, @Body() createJobDto: CreateJobDto) {
    createJobDto.createdBy = req.user._id;
    createJobDto.postingOrg = req.user.org._id;
    createJobDto.isDraft = true;
    createJobDto.isOpen = false;
    return await this.jobService.create(createJobDto, req.user);
  }

  @Post('parse-jd')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  @Roles()
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @ApiResponse({ status: 201, description: 'Job Description is parsed.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Data. ' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  @ApiResponse({
    status: 403,
    description:
      'Forbidden, user with role "BUHead" and "ResourceManager" can use this end point.',
  })
  @ApiOperation({
    summary: 'Parse Job Description',
    description: `This endpoint for creating parsing a job description.This is only accessible for "BUHead" and "ResourceManager".`,
  })
  async createWithExternalProcessing(
    @Req() req: any,
    @UploadedFile(
      new ParseFilePipe({
        validators: [
          new MaxFileSizeValidator({ maxSize: 10000000 }), // 10MB limit
          new FileTypeValidator({
            fileType:
              'application/pdf|application/msword|application/vnd.openxmlformats-officedocument.wordprocessingml.document|text/plain',
          }),
        ],
        errorHttpStatusCode: HttpStatus.UNPROCESSABLE_ENTITY,
        fileIsRequired: true,
      }),
    )
    file: Express.Multer.File,
  ) {
    if (!file) {
      throw new BadRequestException('File is required');
    }

    try {
      // Create a readable stream from buffer
      const { Readable } = require('stream');
      const fileStream = Readable.from(file.buffer);
      // Send file to ai-service API
      const formData = new FormData();
      formData.append('file', fileStream, {
        filename: file.originalname,
        contentType: file.mimetype,
        knownLength: file.size,
      });
      const aiServiceUrl =
        process.env.AI_SERVICE_URL || 'http://localhost:6000';
      const url = `${aiServiceUrl}/api/v1/parser/jd`;
      const response = await axios.post(url, formData, {
        headers: formData.getHeaders(),
      });

      if (response.status === 200 || response.status === 201) {
        this.logger.log('JD parsed successfully.');
        return response.data;
      }
      throw new Error('Failed to parse jd.');
    } catch (error) {
      if (error.response) {
        throw new BadRequestException(
          `External service error: ${error.response.data.message || error.message}`,
        );
      }
      throw new BadRequestException(`Failed to process file: ${error.message}`);
    }
  }

  @Get('interview-jd')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  @Roles()
  @ApiResponse({ status: 201, description: 'Job Interview created.' })
  @ApiResponse({ status: 400, description: 'Bad Request / Data. ' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  @ApiResponse({
    status: 403,
    description:
      'Forbidden, user with role "BUHead" and "ResourceManager" can use this end point.',
  })
  @ApiOperation({
    summary: 'Create Interview for Job Description',
    description: `This endpoint for creating parsing a job description.This is only accessible for "BUHead" and "ResourceManager".`,
  })
  async createJDInterview(@Req() req: any) {
    try {
      const aiServiceUrl =
        process.env.AI_SERVICE_URL || 'http://localhost:6000';
      const url = `${aiServiceUrl}/api/v1/interview/create-jd`;
      const payload = {
        user_id: req.user.email.replace('@', '_').replace('.', '_'),
        skills: [],
        exp_min: 30,
        details: {},
        interview_id: 'JD_INTERVIEW',
      };
      const response = await axios.post(url, payload, {
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.status === 200 || response.status === 201) {
        this.logger.log('JD parsed successfully.');
        const data = response.data;
        const interviewBaseUrl =
          process.env.INTERVIEW_WEB_URL || 'https://interview.talsy.ai';
        const interviewUrl = `${interviewBaseUrl}/jd/${data.session_id}`;
        return {
          ...data,
          interviewUrl: interviewUrl,
        };
      }
      throw new Error('Failed to create interview for jd.');
    } catch (error) {
      if (error.response) {
        throw new BadRequestException(
          `External service error: ${error.response.data.message || error.message}`,
        );
      }
      throw new BadRequestException(
        `Failed to create interview: ${error.message}`,
      );
    }
  }

  @Get('all')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.TeamMember, Role.Recruiter, Role.AccountManager, Role.Vendor, Role.JobSeeker, Role.Freelancer)
  @Roles()
  @ApiOperation({
    summary: 'Retrieve all jobs with filters',
    description: `This endpoint returns a list of all jobs with filters.This is accessible for everyone.`,
  })
  @ApiResponse({ status: 200, description: 'Job retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  // @ApiResponse({ status: 403, description: 'Forbidden, user with role admin and sales rep  can only use this end point.' })
  findAll(@Req() req: any, @Query() query: JobsQueryDTO) {
    return this.jobService.findAll(req.user, query);
  }

  /**
   * The `UseGuards` and `Roles` decorators are commented out because we received
   * a requirement for a public page for an organization that displays job listings.
   * This endpoint needs to be accessible without authentication or role restrictions.
   */

  @Get('all-count')
  @ApiBearerAuth()
  // @UseGuards(AuthJwtGuard, RolesGuard)
  @UseGuards(AuthJwtGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.TeamMember,  Role.Recruiter, Role.AccountManager)
  @ApiOperation({
    summary: 'Retrieve all jobs with filters',
    description: `This endpoint returns a list of all jobs with filters.This is accessible for everyone.`,
  })
  @ApiResponse({ status: 200, description: 'Job retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  // @ApiResponse({ status: 403, description: 'Forbidden, user with role admin and sales rep  can only use this end point.' })
  findAllCount(@Req() req: express.Request, @Query() query: JobsQueryDTO) {
    const userId = (req as any).user._id;
    return this.jobService.findAllCount(userId, query);
  }

  @Get(':jobId')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.AccountManager, Role.TeamLead, Role.TeamMember, Role.Recruiter, Role.Vendor, Role.JobSeeker, Role.Freelancer)
  @Roles()
  @ApiOperation({
    summary: 'Retrieve a job by ID',
    description:
      'This endpoint returns a job by its Id.This is accessible for everyone.',
  })
  @ApiResponse({ status: 200, description: 'Job is retrieved.' })
  @ApiResponse({ status: 404, description: 'Job not found.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  // @ApiResponse({ status: 403, description: 'Forbidden, user with role admin and salesrep can only use this end point.' })
  @ApiParam({ name: 'jobId', description: 'id of the job.' })
  findOne(@Param('jobId') jobId: string) {
    const objId = validateObjectId(jobId);
    return this.jobService.findById(objId);
  }

  @Patch(':jobId')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.TeamLead, Role.TeamMember, Role.Vendor, Role.JobSeeker, Role.DeliveryManager)
  @Roles()
  @ApiOperation({
    summary: 'Update a job by ID',
    description: `This endpoint updates a job by Id.This is only accessible for "BUHead" and "ResourceManager".`,
  })
  @ApiResponse({ status: 200, description: 'job is saved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  @ApiResponse({
    status: 403,
    description:
      'Forbidden, user with role "BUHead" and "ResourceManager" can use this end point.',
  })
  @ApiParam({ name: 'jobId', description: 'id of the job.' })
  update(
    @Req() req: any,
    @Param('jobId') jobId: string,
    @Body() updatejobDto: UpdateJobDto,
  ) {
    const objId = validateObjectId(jobId);
    updatejobDto.postingOrg = req.user.org._id;
    return this.jobService.update(objId, updatejobDto, req.user);
  }

  @Patch(':jobId/share-with-freelancers')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.TeamLead, Role.TeamMember, Role.Vendor, Role.JobSeeker, Role.DeliveryManager)
  @Roles()
  @ApiOperation({
    summary: 'Toggle shareWithFreelancers',
    description: `This endpoint toggles the shareWithFreelancers field for a job.`,
  })
  @ApiResponse({ status: 200, description: 'Job field updated successfully.' })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized. Authentication is required.',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden. Only authorized roles can update this field.',
  })
  @ApiResponse({ status: 404, description: 'Job not found.' })
  @ApiResponse({ status: 500, description: 'Internal server error.' })
  @ApiParam({ name: 'jobId', description: 'ID of the job.' })
  async toggleShareWithFreelancers(@Param('jobId') jobId: string) {
    const validatedJobId = validateObjectId(jobId);
    return this.jobService.updateShareWithFreelancers(validatedJobId);
  }

  @Patch(':jobId/share-with-vendors/:vendorId')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.TeamLead, Role.TeamMember, Role.Vendor, Role.JobSeeker, Role.DeliveryManager)
  @Roles()
  @ApiOperation({
    summary: 'Toggle shareWithFreelancers',
    description: `This endpoint toggles the shareWithFreelancers field for a job.`,
  })
  @ApiResponse({ status: 200, description: 'Job field updated successfully.' })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized. Authentication is required.',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden. Only authorized roles can update this field.',
  })
  @ApiResponse({ status: 404, description: 'Job not found.' })
  @ApiResponse({ status: 500, description: 'Internal server error.' })
  @ApiParam({ name: 'jobId', description: 'ID of the job.' })
  @ApiParam({ name: 'vendorId', description: 'ID of the vendor.' })
  async toggleShareWithVendors(
    @Param('jobId') jobId: string,
    @Param('vendorId') vendorId: string,
  ) {
    const validatedJobId = validateObjectId(jobId);
    const validatedVendorId = validateObjectId(vendorId);
    return this.jobService.updateShareWithVendors(
      validatedJobId,
      validatedVendorId,
    );
  }

  @Delete(':jobId/hard-delete')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.TeamLead, Role.TeamMember, Role.Vendor, Role.JobSeeker)
  @Roles()
  @ApiOperation({
    summary: 'Delete a job by ID',
    description:
      'This endpoint deletes a job by Id. This is only accessible for "BUHead" and "ResourceManager".',
  })
  @ApiResponse({ status: 200, description: 'Job is hard deleted.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({
    status: 403,
    description:
      'Forbidden, user with role "BUHead" and "ResourceManager" can use this end point.',
  })
  @ApiParam({ name: 'jobId', description: 'ID of the job type' })
  hardDelete(@Param('jobId') jobId: string) {
    const objId = validateObjectId(jobId);
    return this.jobService.remove(objId);
  }

  @Delete(':jobId/soft-delete')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.TeamLead, Role.TeamMember, Role.Vendor, Role.JobSeeker)
  @Roles()
  @ApiOperation({
    summary: 'Delete a job by ID',
    description:
      'This endpoint soft deletes a job by Id. This is only accessible for "BUHead" and "ResourceManager".',
  })
  @ApiResponse({ status: 200, description: 'Job is hard deleted.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({
    status: 403,
    description:
      'Forbidden, user with role "BUHead" and "ResourceManager" can use this end point.',
  })
  @ApiParam({ name: 'jobId', description: 'ID of the job type' })
  softDelete(@Param('jobId') jobId: string) {
    const objId = validateObjectId(jobId);
    return this.jobService.delete(objId);
  }

  @Get('jobs-count')
  @ApiResponse({ status: 200, description: `Job's count is retrieved.` })
  @ApiResponse({ status: 404, description: `Unable to find job's count.` })
  @ApiResponse({ status: 401, description: `Unauthorized. ` })
  // @ApiResponse({ status: 403, description: `Forbidden user with role "super-admin", "admin" and "sales-rep" can only use this end point.` })
  @ApiOperation({
    summary: `Retrieve job's count.`,
    description: `This endpoint returns job's count. This is accessible for everyone.`,
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.TeamLead, Role.TeamMember, Role.DeliveryManager, Role.TeamLead, Role.TeamMember, Role.Recruiter, Role.AccountManager, Role.Vendor, Role.JobSeeker, Role.Freelancer)
  @Roles()
  getJobsCount(@Req() req: any, @Query() query: JobsQueryDTO) {
    return this.jobService.getJobsCount(req.user, query);
  }

  @Get('jobs-count-by-org/:postingOrg')
  @ApiResponse({
    status: 200,
    description: `Job counts for the organization retrieved successfully.`,
  })
  @ApiResponse({
    status: 404,
    description: `Unable to find job counts for the organization.`,
  })
  @ApiResponse({ status: 401, description: `Unauthorized.` })
  @ApiParam({
    name: 'postingOrg',
    description: 'ID of the posting organization',
  })
  @ApiOperation({
    summary: `Retrieve job counts for a specific organization`,
    description: `This endpoint returns total and open job counts for a specific posting organization.`,
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.TeamLead, Role.TeamMember, Role.DeliveryManager, Role.TeamLead, Role.TeamMember, Role.Recruiter, Role.AccountManager, Role.Vendor, Role.JobSeeker)
  @Roles()
  async getJobsCountByOrg(@Param('postingOrg') postingOrg: string) {
    return this.jobService.getJobsCountByPostingOrg(postingOrg);
  }

  @Get('jobs-internal-count')
  @ApiResponse({
    status: 200,
    description: `Internal job's count is retrieved.`,
  })
  @ApiResponse({ status: 404, description: `Unable to find job's count.` })
  @ApiResponse({ status: 401, description: `Unauthorized. ` })
  // @ApiResponse({ status: 403, description: `Forbidden user with role "super-admin", "admin" and "sales-rep" can only use this end point.` })
  @ApiOperation({
    summary: `Retrieve internal job's count.`,
    description: `This endpoint returns internal job's count. This is accessible for everyone.`,
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.TeamLead, Role.TeamMember, Role.DeliveryManager, Role.TeamLead, Role.TeamMember, Role.Recruiter, Role.AccountManager, Role.Vendor, Role.JobSeeker)
  @Roles()
  getActiveInternalJobsCount() {
    return this.jobService.getActiveInternalJobsCount();
  }

  @Patch(':jobId/update-status')
  @ApiParam({ name: 'jobId', description: `Id of the job.` })
  @ApiResponse({ status: 200, description: `Job updated.` })
  @ApiResponse({ status: 401, description: `Unauthorized.` })
  @ApiResponse({
    status: 403,
    description: `Forbidden, user with role "BUHead" and "ResourceManager" can use this end point.`,
  })
  @ApiOperation({
    summary: `Update the status of the job`,
    description: `This endpoint update the status of the job. This is accessible only for "BUHead" and "ResourceManager".`,
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.TeamLead, Role.TeamMember, Role.Vendor, Role.JobSeeker, Role.DeliveryManager)
  @Roles()
  changeStatus(
    @Req() req: any,
    @Param('jobId') jobId: string,
    @Body() changeStatusJobDto: ChangeStatusJobDto,
  ) {
    const objId = validateObjectId(jobId);
    return this.jobService.changeStatus(objId, changeStatusJobDto, req.user);
  }

  @Delete('bulk-delete')
  @ApiOperation({
    summary: 'Soft delete multiple jobs',
    description: `This endpoint returns job count. It is accessible only for "BUHead" and "ResourceManager".`,
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.TeamLead, Role.TeamMember, Role.Vendor, Role.JobSeeker)
  @Roles()
  @ApiResponse({ status: 200, description: 'Jobs have been soft deleted.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({
    status: 403,
    description: `Forbidden, only users with "BUHead" and "ResourceManager" roles can use this endpoint.`,
  })
  @ApiResponse({ status: 404, description: 'No jobs found for the given IDs.' })
  @ApiResponse({
    status: 400,
    description: 'Bad request. Job IDs are required.',
  })
  async bulkSoftDelete(@Body() jobIds: string[]): Promise<{ message: string }> {
    if (jobIds && jobIds.length > 0) {
      const validJobIds = jobIds.map((jobId: string) =>
        validateObjectId(jobId),
      );

      const result = await this.jobService.bulkDelete(validJobIds);

      if (result.modifiedCount > 0) {
        return {
          message: `Successfully soft deleted ${result.modifiedCount} jobs.`,
        };
      } else {
        return { message: 'No jobs were soft deleted.' };
      }
    } else {
      throw new BadRequestException(
        'Job IDs are required for bulk soft delete.',
      );
    }
  }

  @Patch('bulk-status-update')
  @ApiOperation({
    summary: 'Bulk update status of multiple jobs by their IDs',
    description: `This endpoint updates the status of multiple jobs by their IDs. Accessible only to users with roles "BUHead" and "ResourceManager""`,
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.TeamLead, Role.TeamMember, Role.Vendor, Role.JobSeeker, Role.DeliveryManager)
  @Roles()
  @ApiResponse({ status: 200, description: 'Jobs status updated.' })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized. Authentication is required.',
  })
  @ApiResponse({
    status: 403,
    description: `Forbidden. Only users with roles "BUHead" and "ResourceManager" can use this endpoint.`,
  })
  @ApiResponse({ status: 404, description: 'Jobs not found.' })
  async bulkChangeStatus(
    @Req() req: any,
    @Body() changeStatusJobDto: ChangeStatusJobDto,
  ) {
    const { jobIds } = changeStatusJobDto;

    if (jobIds && jobIds.length > 0) {
      const validJobIds = jobIds.map((jobId: string) =>
        validateObjectId(jobId),
      );
      return this.jobService.bulkChangeStatus(changeStatusJobDto, req.user);
    } else {
      throw new BadRequestException(
        'JobIds are required for bulk status update.',
      );
    }
  }

  @Get('job-count-by-department')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  //@Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.TeamMember,  Role.Recruiter, Role.AccountManager)
  @ApiOperation({
    summary: 'get job count by department wise',
    description: `This endpoint returns the count of jobs by department wise and fitler jobs by (today, yesterday, thisWeek, thisMonth).  This is accessible for everyone.`,
  })
  @ApiResponse({ status: 200, description: 'Jobs retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiQuery({
    name: 'filter',
    description: 'Filter for jobs by (today, yesterday, thisWeek, thisMonth)',
    required: true,
  })
  async findByContactAndOrg(@Req() req: any, @Query('filter') filter: string) {
    const userId = req.user._id;
    if (!req.user.org._id) {
      throw new BadRequestException('User does not have org id');
    }
    return this.jobService.findJobCountByDept(filter, userId, req.user);
  }

  @Get('internal-job-count-by-department')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  //@Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.DeliveryManager, Role.TeamLead, Role.TeamMember,  Role.Recruiter, Role.AccountManager)
  @ApiOperation({
    summary: 'get internal job count by department wise',
    description: `This endpoint returns the count of internal jobs by department wise and fitler jobs by (today, yesterday, thisWeek, thisMonth).  This is accessible for everyone.`,
  })
  @ApiResponse({ status: 200, description: 'Internal jobs retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiQuery({
    name: 'filter',
    description:
      'Filter for internal jobs by (today, yesterday, thisWeek, thisMonth)',
    required: true,
  })
  async findInternalJobCountByDept(@Query('filter') filter: string) {
    return this.jobService.findInternalJobCountByDept(filter);
  }

  @Get('consolidated-job-count')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  @ApiOperation({
    summary: 'get total jobs and hired count for last six months',
    description: `This endpoint returns the count of jobs and hired  for last six months.  This is accessible for everyone.`,
  })
  @ApiResponse({
    status: 200,
    description: 'Consolidated Jobs count retrieved.',
  })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  async findJobsAndHiredCountByMonth() {
    return this.jobService.findJobsAndHiredCountByMonth();
  }

  @Get('job-recruitement-summary')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  @ApiOperation({
    summary:
      'Get count of job summary, i.e count of total jobs,interviews and shortlisted and hired.',
    description: `This endpoint returns the count of total jobs,interviews and shortlisted and hired.  This is accessible for everyone.`,
  })
  @ApiResponse({
    status: 200,
    description: 'Consolidated recruitement count summary retrieved.',
  })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  async findJobRecruitementSummary() {
    return this.jobService.findJobRecruitementSummary();
  }

  @Get('dashboard/job-seeker')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.JobSeeker)
  @Roles()
  @ApiOperation({ summary: 'Retrieve job-seeker dashboard metrics' })
  @ApiResponse({
    status: 200,
    description: 'Dashboard data retrieved successfully.',
  })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  async getJobSeekerDashboard(@Req() req: any) {
    return this.jobService.getJobSeekerDashboard(req.user);
  }

  @Get('count-by-location')
  @ApiOperation({
    summary: 'Get count of jobs grouped by location',
    description: 'Returns the count of jobs for each location.',
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.JobSeeker)
  @Roles()
  @ApiResponse({
    status: 200,
    description: 'Job count per location retrieved successfully.',
  })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  async getJobCountByLocation() {
    return this.jobService.getJobCountByLocation();
  }

  @Get('dashboard/recruiter')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Recruiter)
  @Roles()
  @ApiOperation({ summary: 'Retrieve recruiter dashboard metrics' })
  @ApiResponse({
    status: 200,
    description: 'Dashboard data retrieved successfully.',
  })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  async getRecruiterDashboard(@Req() req: any) {
    return this.jobService.getRecruiterDashboard(req.user);
  }

  @Get('dashboard/vendor')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.Vendor)
  @Roles()
  @ApiOperation({ summary: 'Retrieve job counts for recruiter' })
  @ApiResponse({
    status: 200,
    description: 'Job counts retrieved successfully.',
  })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiQuery({
    name: 'fromDate',
    required: false,
    type: String,
    description: 'Start date in YYYY-MM-DD format',
  })
  @ApiQuery({
    name: 'toDate',
    required: false,
    type: String,
    description: 'End date in YYYY-MM-DD format',
  })
  async getRecruiterJobCounts(
    @Req() req: any,
    @Query('fromDate') fromDate?: string,
    @Query('toDate') toDate?: string,
  ) {
    return this.jobService.getRecruiterJobCounts(req.user, fromDate, toDate);
  }

  @Get('clientwise-jobs-count')
  @ApiResponse({
    status: 200,
    description: `Clientwise Job counts for the  posting organization retrieved successfully.`,
  })
  @ApiResponse({
    status: 404,
    description: `Unable to find Clientwise Job counts.`,
  })
  @ApiResponse({ status: 401, description: `Unauthorized.` })
  // @ApiParam({ name: 'postingOrg', description: 'ID of the posting organization' })
  @ApiQuery({
    name: 'page',
    required: true,
    type: Number,
    description: 'Page number for pagination',
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: true,
    type: Number,
    description: 'Number of records per page',
    example: 5,
  })
  @ApiOperation({
    summary: `Retrieve clientwise job counts for a specific organization `,
    description: `This endpoint returns open job counts clientwise for a specific posting organization.`,
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.BUHead, Role.DeliveryManager, Role.AccountManager, Role.ResourceManager, Role.TeamLead, Role.TeamMember, Role.Recruiter)
  @Roles()
  async getClientWiseJobsCountByPostingOrg(
    @Req() req: any,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 5,
  ) {
    if (!req.user.org._id) {
      throw new BadRequestException('User does not have org id');
    }
    return this.jobService.getClientWiseJobsCountByPostingOrg(
      req.user,
      page,
      limit,
    );
  }
  @Get('sourcewise-jobs-count')
  @ApiResponse({
    status: 200,
    description: `sourcewise Job counts for the  posting organization retrieved successfully.`,
  })
  @ApiResponse({
    status: 404,
    description: `Unable to find sourcewise Job counts.`,
  })
  @ApiResponse({ status: 401, description: `Unauthorized.` })
  // @ApiParam({ name: 'postingOrg', description: 'ID of the posting organization' })
  @ApiOperation({
    summary: `Retrieve sourcewise job counts for a specific organization `,
    description: `This endpoint returns open job counts sourcewise for a specific posting organization.`,
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.BUHead, Role.DeliveryManager, Role.AccountManager, Role.ResourceManager, Role.TeamLead, Role.TeamMember, Role.Recruiter)
  @Roles()
  async getSourceWiseJobsCountByPostingOrg(@Req() req: any) {
    if (!req.user.org._id) {
      throw new BadRequestException('User does not have org id');
    }
    return this.jobService.getSourceWiseJobsCountByPostingOrg(req.user);
  }

  @Get('active-candidates-count')
  @ApiResponse({
    status: 200,
    description: `StageWise Job Application Counts for the  posting organization retrieved successfully.`,
  })
  @ApiResponse({
    status: 404,
    description: `Unable to find StageWise Job counts.`,
  })
  @ApiResponse({ status: 401, description: `Unauthorized.` })
  // @ApiParam({ name: 'postingOrg', description: 'ID of the posting organization' })
  @ApiOperation({
    summary: `Retrieve StageWise Job Application Counts for a specific organization `,
    description: `This endpoint returns open job Application counts Stagewise for a specific posting organization.`,
  })
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  // @Roles(Role.BUHead, Role.DeliveryManager, Role.AccountManager, Role.ResourceManager, Role.TeamLead, Role.TeamMember, Role.Recruiter)
  @Roles()
  async getActiveJobsCountByPostingOrg(@Req() req: any) {
    if (!req.user.org._id) {
      throw new BadRequestException('User does not have org id');
    }
    return this.jobService.getActiveJobsCountByPostingOrg(req.user);
  }

  @Get('all-count-postingOrg')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard, RolesGuard)
  @UseGuards(AuthJwtGuard)
  // @Roles(Role.BUHead, Role.DeliveryManager, Role.AccountManager, Role.ResourceManager, Role.TeamLead, Role.TeamMember, Role.Recruiter)
  @Roles()
  @ApiOperation({
    summary: 'Retrieve all jobs counts',
    description: `This endpoint returns a count of all jobs, active,draft,closed josbs.`,
  })
  @ApiResponse({ status: 200, description: 'Jobs Count retrieved.' })
  @ApiResponse({ status: 404, description: `Unable to find Job counts.` })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  // @ApiResponse({ status: 403, description: 'Forbidden, user with role admin and sales rep  can only use this end point.' })
  findAllpostingOrgJobCount(@Req() req: any) {
    if (!req.user.org._id) {
      throw new BadRequestException('User does not have org id');
    }
    return this.jobService.findAllpostingOrgJobCount(req.user);
  }

  @Get('analytics/:jobId')
  @ApiBearerAuth()
  @ApiResponse({ status: 200, description: 'Successfully retrieved Analytics' })
  @ApiResponse({
    status: 403,
    description:
      'Forbidden, user with role "Admin", "BUHead", "ResourceManager", "AccountManager" and "DeliveryManager" can use this end point.',
  })
  @ApiResponse({ status: 404, description: 'No analytics found' })
  @UseGuards(AuthJwtGuard, RolesGuard)
  @UseGuards(AuthJwtGuard)
  @ApiOperation({
    summary: 'Get all Analytics for a job',
    description: `Job Analytics not found, This is only accessible for "DeliveryManager", "Admin", "BUHead", "ResourceManager" and "AccountManager".`,
  })
  // @Roles(Role.Admin, Role.BUHead, Role.ResourceManager, Role.TeamLead, Role.TeamMember,  Role.AccountManager, Role.DeliveryManager,Role.Vendor)
  @Roles()
  async getAnalytics(@Param('jobId') jobId: string) {
    return this.jobAllocationService.getAnalytics(jobId);
  }

  @Get('public')
  @ApiOperation({
    summary: 'Retrieve all jobs with filters',
    description:
      'This endpoint returns a list of all jobs with optional filters (pagination and search). This is accessible for everyone.',
  })
  @ApiResponse({ status: 200, description: 'Jobs retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number for pagination (default is 1)',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of jobs per page (default is 10)',
  })
  @ApiQuery({
    name: 'search',
    required: false,
    type: String,
    description:
      'Search term to filter jobs by title, skills, employment type, etc.',
  })
  publicJobs(
    @Query('page') page = 1,
    @Query('limit') limit = 10,
    @Query('search') search: string,
    @Req() req: any,
  ) {
    return this.jobService.publicJobs(page, limit, search);
  }

  @Patch(':jobId/add-vendor-rate-card/:vendorId/:rateCardId')
  @ApiBearerAuth()
  @UseGuards(AuthJwtGuard)
  @ApiOperation({ summary: 'Add or update vendor rate card for a job' })
  @ApiResponse({
    status: 200,
    description: 'Vendor rate card added or updated successfully.',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request. Duplicate or invalid data.',
  })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  @ApiResponse({ status: 404, description: 'Job not found.' })
  @ApiResponse({ status: 500, description: 'Internal server error.' })
  @ApiParam({ name: 'jobId', description: 'ID of the job.' })
  @ApiParam({ name: 'vendorId', description: 'ID of the vendor.' })
  @ApiParam({ name: 'rateCardId', description: 'ID of the rate card.' })
  async addOrUpdateVendorRateCard(
    @Param('jobId') jobId: string,
    @Param('vendorId') vendorId: string,
    @Param('rateCardId') rateCardId: string,
  ) {
    const validatedJobId = validateObjectId(jobId);
    const validatedVendorId = validateObjectId(vendorId);
    const validatedRateCardId = validateObjectId(rateCardId);
    return this.jobService.addOrUpdateVendorRateCard(
      validatedJobId,
      validatedVendorId,
      validatedRateCardId,
    );
  }

  @Get('jobs/application/tree')
  @ApiOperation({
    summary: 'Retrieve all jobs with filters',
    description: `This endpoint returns a list of all jobs with filters.This is accessible for everyone.`,
  })
  @ApiResponse({ status: 200, description: 'Job retrieved.' })
  @ApiResponse({ status: 401, description: 'Unauthorized. ' })
  // @ApiResponse({ status: 403, description: 'Forbidden, user with role admin and sales rep  can only use this end point.' })
  findAllJobsAndApplications() {
    return this.jobService.findAllJobsAndApplications();
  }
}
