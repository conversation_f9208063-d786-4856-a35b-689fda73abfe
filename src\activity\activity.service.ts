import { Injectable, InternalServerErrorException, Logger } from '@nestjs/common';
import { CreateActivityDto } from './dto/create-activity.dto';
import { ConfigService } from '@nestjs/config';
import { InjectModel } from '@nestjs/mongoose';
import { Activity, ActivityDocument } from './schemas/activity.schema';
import { Model, Types } from 'mongoose';
import moment from 'moment';
import { OnEvent } from '@nestjs/event-emitter';
import { validateObjectId } from 'src/utils/validation.utils';
import { EmailAlertService } from 'src/alert/email-alert/email-alert.service';
import { CommentDocument, Comment } from 'src/common/schemas/comment.schema';

@Injectable()
export class ActivityService {
  private readonly logger = new Logger(ActivityService.name);

  constructor(private configService: ConfigService,
    @InjectModel(Activity.name) private activityModel: Model<Activity>,
    @InjectModel(Comment.name) private commentModel: Model<Comment>,
    private emailAlertService: EmailAlertService
  ) { }
  async getActivities({ contactId, orgId, taskId,
    regionId, businessUnitId, userId,
    dateFilter, customDate, page, limit, actor, rateCardId }: {
      contactId?: string;
      orgId?: string;
      taskId?: string;
      regionId?: string;
      businessUnitId?: string;
      userId?: string;
      dateFilter?: string;
      customDate?: Date;
      // startDate?: Date;
      // endDate?: Date;
      page: number;
      limit: number;
      actor: string;
      rateCardId?: string;
    }): Promise<Activity[]> {
    try {

      let filterConditions: any = { actor };
      const andConditions: any[] = [];

      const buildOrCondition = (field: string, value: string) => {
        const conditions: any[] = [{ [field]: value }];
        try {
          conditions.push({ [field]: validateObjectId(value) });
        } catch (error) {
          // If the value is not a valid ObjectId, just ignore the conversion
          this.logger.error('Not valid ignore conversion');
        }
        return { $or: conditions };
      };

      // Add orgId conditions
      if (orgId) {
        andConditions.push(buildOrCondition('org', orgId));
      }

      // Add contactId conditions
      if (contactId) {
        andConditions.push(buildOrCondition('contact', contactId));
      }

      if (taskId) {
        andConditions.push(buildOrCondition('task', taskId));
      }

      if (regionId) {
        andConditions.push(buildOrCondition('region', regionId));
      }

      if (businessUnitId) {
        andConditions.push(buildOrCondition('businessUnit', businessUnitId));
      }

      if (userId) {
        andConditions.push(buildOrCondition('user', userId));
      }
      if (rateCardId) {
        andConditions.push(buildOrCondition('rateCard', rateCardId));
      }

      // Combine all conditions with $and
      if (andConditions.length > 0) {
        filterConditions.$and = andConditions;
      }

      // Apply date filters
      if (dateFilter) {
        const dateConditions = this.getFilterConditions(dateFilter, customDate);
        if (Object.keys(dateConditions).length > 0) {
          if (!filterConditions.$and) {
            filterConditions.$and = [];
          }
          filterConditions.$and.push(dateConditions);
        }
      }

      this.logger.debug(`Final filter conditions: ${JSON.stringify(filterConditions)}`);

      const activities = await this.activityModel.find(filterConditions)
        .skip((page - 1) * limit)
        .limit(limit)
        .populate({ path: 'actor', select: '_id roles firstName', model: 'BasicUser' })
        .populate({ path: 'note', select: '_id title', model: 'Note' })
        .populate({ path: 'task', select: '_id title', model: 'Task' })
        .populate({ path: 'contact', select: '_id firstName lastName', model: 'Contact' })
        .populate({ path: 'region', select: '_id country', model: 'Region' })
        .populate({ path: 'businessUnit', select: '_id label key org type', model: 'BusinessUnit' })
        .populate({
          path: 'comment', // Populating the 'comment' field
          select: '_id contents attachments', // Selecting specific fields from 'Comment'
          model: 'Comment', // Specifying the model name to populate
          populate: {
            path: 'attachments', // Populating the 'attachments' field inside 'comment'
            select: '_id originalName fileSize fileType locationUrl', // Selecting specific fields from 'FileMetadata'
            model: 'FileMetadata' // Specifying the model name for 'attachments'
          }
        })
        .sort({ updatedAt: -1 })
        .exec();
      return activities;


    } catch (error) {
      this.logger.error('Error fetching activities', error);
      throw new Error('Error fetching activities');
    }
  }

  async getActivitiesOfContact({ contactId, dateFilter, customDate, page, limit, actor }: {
    contactId: string;
    dateFilter?: string;
    customDate?: Date;
    page: number;
    limit: number;
    actor: string;
  }): Promise<Activity[]> {
    return this.getActivities({ contactId, dateFilter, customDate, page, limit, actor });
  }

  async getActivitiesOfOrg({ orgId, dateFilter, customDate, page, limit, actor }: {
    orgId: string;
    dateFilter?: string;
    customDate?: Date;
    page: number;
    limit: number;
    actor: string;
  }): Promise<Activity[]> {
    return this.getActivities({ orgId, dateFilter, customDate, page, limit, actor });
  }

  async getActivitiesOfTask({ taskId, dateFilter, customDate, page, limit, actor }: {
    taskId: string;
    dateFilter?: string;
    customDate?: Date;
    page: number;
    limit: number;
    actor: string;
  }): Promise<Activity[]> {
    return this.getActivities({ taskId, dateFilter, customDate, page, limit, actor });
  }


  private getFilterConditions(dateFilter: string, customDate?: Date, startDate?: Date, endDate?: Date) {
    const conditions: any = {};
    switch (dateFilter) {
      case 'today':
        const todayConditions = this.getTodayConditions();
        conditions.$or = [
          { dueDate: { $exists: true, ...todayConditions } },
          { dueDate: null, createdAt: todayConditions },
          { updatedAt: todayConditions },
        ];
        this.logger.debug(`Today's filter conditions: ${JSON.stringify(conditions)}`);
        break;
      case 'upcoming':
        conditions.dueDate = { $gt: moment().utc().endOf('day').toDate() };
        break;
      case 'custom':
        if (customDate) {
          const customDateConditions = this.getCustomDateConditions(customDate);
          conditions.$or = [
            { dueDate: { $exists: true, ...customDateConditions } },
            { dueDate: null, createdAt: customDateConditions },
            { updatedAt: customDateConditions },
          ];
        } else {
          throw new Error('Custom date is required for custom filter');
        }
        this.logger.debug(`Custom date filter conditions: ${JSON.stringify(conditions)}`);
        break;
      case 'lastWeek':
        const lastWeekConditions = this.getLastWeekConditions();
        conditions.$or = [
          { dueDate: { $exists: true, ...lastWeekConditions } },
          { dueDate: null, createdAt: lastWeekConditions },
          { updatedAt: lastWeekConditions },
        ];
        this.logger.debug(`Last week's filter conditions: ${JSON.stringify(conditions)}`);
        break;
      case 'twoWeeksAgo':
        const lastTwoWeeksAgoConditions = this.getWeeksAgoConditions(2);
        conditions.$or = [
          { dueDate: { $exists: true, ...lastTwoWeeksAgoConditions } },
          { dueDate: null, createdAt: lastTwoWeeksAgoConditions },
          { updatedAt: lastTwoWeeksAgoConditions },
        ];
        this.logger.debug(`Last two weeks' filter conditions: ${JSON.stringify(conditions)}`);
        break;
      case 'threeWeeksAgo':
        const lastThreeWeeksAgoConditions = this.getWeeksAgoConditions(3);
        conditions.$or = [
          { dueDate: { $exists: true, ...lastThreeWeeksAgoConditions } },
          { dueDate: null, createdAt: lastThreeWeeksAgoConditions },
          { updatedAt: lastThreeWeeksAgoConditions },
        ];
        this.logger.debug(`Last three weeks' filter conditions: ${JSON.stringify(conditions)}`);
        break;
      case 'lastMonth':
        const lastMonthConditions = this.getLastMonthConditions();
        conditions.$or = [
          { dueDate: { $exists: true, ...lastMonthConditions } },
          { dueDate: null, createdAt: lastMonthConditions },
          { updatedAt: lastMonthConditions },
        ];
        this.logger.debug(`Last month's filter conditions: ${JSON.stringify(conditions)}`);
        break;


      default:
        throw new Error('Invalid filter specified');
    }
    return conditions;
  }


  private getTodayConditions(): { $gte: Date, $lt: Date } {
    const startOfDay = moment().startOf('day').toDate();
    const endOfDay = moment().endOf('day').toDate();
    return { $gte: startOfDay, $lt: endOfDay };
  }

  private getCustomDateConditions(date: Date): { $gte: Date, $lt: Date } {
    const startOfDay = moment(date).startOf('day').toDate();
    const endOfDay = moment(date).endOf('day').toDate();
    return { $gte: startOfDay, $lt: endOfDay };
  }

  private getLastWeekConditions(): { $gte: Date, $lt: Date } {
    const startOfLastWeek = moment().subtract(1, 'weeks').startOf('isoWeek').toDate();
    const endOfLastWeek = moment().subtract(1, 'weeks').endOf('isoWeek').toDate();
    return { $gte: startOfLastWeek, $lt: endOfLastWeek };
  }

  private getWeeksAgoConditions(weeks: number): { $gte: Date, $lt: Date } {
    const startOfAgoWeeks = moment().subtract(weeks, 'weeks').startOf('isoWeek').toDate();
    const endOfAgoWeeks = moment().subtract(weeks - 1, 'weeks').startOf('isoWeek').toDate();
    return { $gte: startOfAgoWeeks, $lt: endOfAgoWeeks };
  }

  private getLastMonthConditions(): { $gte: Date, $lt: Date } {
    const startOfLastMonth = moment().subtract(1, 'months').startOf('month').toDate();
    const endOfLastMonth = moment().subtract(1, 'months').endOf('month').toDate();
    return { $gte: startOfLastMonth, $lt: endOfLastMonth };
  }

  async createActivity(newActivity: CreateActivityDto, parentActivityId?: Types.ObjectId): Promise<ActivityDocument> {
    try {
      const createdActivity = new this.activityModel(newActivity);
      if (parentActivityId) {
        createdActivity.parentActivity = parentActivityId;
      }
      return await createdActivity.save();
    } catch (error) {
      this.logger.error('Failed to create activity', error);
      throw new InternalServerErrorException('Unknown error when creating activity.');
    }
  }

  async createSubActivity(newActivity: CreateActivityDto, parentActivityId: Types.ObjectId): Promise<ActivityDocument> {
    try {

      const createdActivity = new this.activityModel(newActivity);
      createdActivity.parentActivity = parentActivityId;
      return await createdActivity.save();
    }
    catch (error) {
      this.logger.error(`Failed to create activity. ${error}`);
      throw new InternalServerErrorException(`Error while creating a sub-activity. ${error?.message}`);
    }
  }


  async createComment(comment: object): Promise<CommentDocument> {
    try {
      const createdComment = new this.commentModel(comment);
      return await createdComment.save();
    }
    catch (error) {
      this.logger.error(`Failed to create comment. ${error}`);
      throw new InternalServerErrorException(`Error while creating a comment. ${error?.message}`);
    }
  }



  @OnEvent('note.created', { async: true })
  async onNoteCreatedEvent(payload: any) {
    let newNote = payload;
    let newActivity: CreateActivityDto = {
      title: `${newNote.createdBy.firstName} created a new note "${newNote.title}".`,
      titleWithPlaceholders: `{{newNote.createdBy.firstName}} created a new note "{{newNote.title}}".`,
      org: newNote.org,
      contact: newNote.contact,
      note: newNote._id,
      actor: newNote.createdBy,
      dueDate: newNote.dueDate,
    }
    await this.createActivity(newActivity);
  }

  @OnEvent('note.title.updated', { async: true })
  async onNoteTitleUpdatedEvent(payload: any) {
    let { updatedNote, user } = payload;
    let parentActivityId = updatedNote._id;
    let newActivity: CreateActivityDto = {
      title: `${user.firstName} updated the note title to "${updatedNote.title}".`,
      titleWithPlaceholders: `{{user.firstName}} updated the note title to "{{updatedNote.title}}".`,
      org: updatedNote.org,
      contact: updatedNote.contact,
      note: updatedNote._id,
      actor: user._id,
      dueDate: updatedNote.dueDate,
      parentActivity: parentActivityId,
    }
    await this.createActivity(newActivity);
  }

  @OnEvent('note.org.updated', { async: true })
  async onNoteOrgUpdatedEvent(payload: any) {
    let { updatedNote, user } = payload;
    let parentActivityId = updatedNote._id;
    let newActivity: CreateActivityDto = {
      title: `${user.firstName} updated the org of the note to "${updatedNote.org.name}".`,
      titleWithPlaceholders: `{{user.firstName}} updated the org of the note to "{{updatedNote.org}}".`,
      org: updatedNote.org,
      contact: updatedNote.contact,
      note: updatedNote._id,
      actor: user._id,
      dueDate: updatedNote.dueDate,
      parentActivity: parentActivityId,
    }
    await this.createActivity(newActivity);
  }

  @OnEvent('note.contact.updated', { async: true })
  async onNoteContactUpdatedEvent(payload: any) {
    let { updatedNote, user } = payload;
    let parentActivityId = updatedNote._id;
    let newActivity: CreateActivityDto = {
      title: `${user.firstName} updated the contact of the note to "${updatedNote.contact.firstName} ${updatedNote.contact.lastName}".`,
      titleWithPlaceholders: `{{user.firstName}} updated the contact of the note to "{{updatedNote.contact}}".`,
      org: updatedNote.org,
      contact: updatedNote.contact,
      note: updatedNote._id,
      actor: user._id,
      dueDate: updatedNote.dueDate,
      parentActivity: parentActivityId,
    }
    await this.createActivity(newActivity);
  }

  @OnEvent('note.remove', { async: true })
  async onNoteDeleteEvent(payload: any) {
    let { note, user } = payload
    let parentActivityId = note._id;
    let newActivity: CreateActivityDto = {
      title: `${user.firstName} deleted the note "${note.title}".`,
      titleWithPlaceholders: `{{user.firstName}} deleted the note "{{note.title}}".`,
      note: note._id,
      actor: user._id,
      parentActivity: parentActivityId,
    }
    console.log(newActivity)
    await this.createActivity(newActivity);
    console.log("deleted")
  }


  //TODO: html template or placeholders  or any better way


  @OnEvent('task.created', { async: true })
  async onTaskCreatedEvent(payload: any) {
    let newTask = payload
    // this.logger.log(`event emiiter task created, ${newTask}`)
    let newActivity: CreateActivityDto = {
      title: `${newTask.createdBy.firstName} created a new task "${newTask.title}".`,
      titleWithPlaceholders: `{{newTask.createdBy.firstName}} created a new task "{{newTask.title}}".`,
      org: newTask.org,
      task: newTask._id,
      actor: newTask.createdBy,
      dueDate: newTask.dueDate,
      contact: newTask.spoc
    }
    await this.createActivity(newActivity);
    if (newTask.vendor) {
      await this.createActivity({ ...newActivity, org: newTask.vendor });
    }
  }

  @OnEvent('task.assignee.changed', { async: true })
  async onTaskUpdateAssigneeEvent(payload: any) {
    let { updatedTask, commentObj, user } = payload;
    // this.logger.log(user)
    const createdComment = await this.createComment(commentObj);
    let newActivity: CreateActivityDto = {
      title: `${user.firstName} assigned the "${updatedTask.title}" task to ${updatedTask.assignee.firstName}.`,
      titleWithPlaceholders: `{{user.firstName}} assigned the "{{updatedTask.title}}" task to {{updatedTask.assignee}}.`,
      org: updatedTask.org,
      task: updatedTask._id,
      actor: user._id,
      dueDate: updatedTask.dueDate,
      contact: updatedTask.spoc,
      comment: createdComment._id
    }
    await this.createActivity(newActivity);
    if (updatedTask.vendor) {
      await this.createActivity({ ...newActivity, org: updatedTask.vendor });
    }
  }

  @OnEvent('task.status.changed', { async: true })
  async onTaskStatusUpdateEvent(payload: any) {
    let { updatedTask, comment, user } = payload;
    const createComment = await this.createComment(comment);
    this.logger.debug(createComment);
    // this.logger.debug(updatedTask)
    let newActivity: CreateActivityDto = {
      title: `${user.firstName} updated the "${updatedTask.title}" task status to ${updatedTask.status}.`,
      titleWithPlaceholders: `{{user.firstName}} updated the "{{updatedTask.title}}" task status to {{updatedTask.status}}.`,
      org: updatedTask.org,
      task: updatedTask._id,
      actor: user._id,
      dueDate: updatedTask.dueDate,
      contact: updatedTask.spoc,
      comment: createComment._id
    }
    await this.createActivity(newActivity);
    if (updatedTask.vendor) {
      await this.createActivity({ ...newActivity, org: updatedTask.vendor });
    }
  }

  @OnEvent('task.title.updated', { async: true })
  async onTaskTitleUpdateEvent(payload: any) {
    let { updatedTask, user } = payload;
    this.logger.debug(updatedTask)
    let newActivity: CreateActivityDto = {
      title: `${user.firstName} updated the task title to "${updatedTask.title}".`,
      titleWithPlaceholders: `{{user.firstName}} updated the task title to "{{updatedTask.title}}".`,
      org: updatedTask.org,
      task: updatedTask._id,
      actor: user._id,
      dueDate: updatedTask.dueDate,
      contact: updatedTask.spoc,
    }
    await this.createActivity(newActivity);
    if (updatedTask.vendor) {
      await this.createActivity({ ...newActivity, org: updatedTask.vendor });
    }
  }

  @OnEvent('task.duedate.updated', { async: true })
  async onTaskDueDateUpdateEvent(payload: any) {
    let { updatedTask, user } = payload;
    // this.logger.debug(updatedTask)
    let newActivity: CreateActivityDto = {
      title: `${user.firstName} updated the task due date to "${updatedTask.dueDate}".`,
      titleWithPlaceholders: `{{user.firstName}} updated the task due date to "{{updatedTask.dueDate}}".`,
      org: updatedTask.org,
      task: updatedTask._id,
      actor: user._id,
      dueDate: updatedTask.dueDate,
      contact: updatedTask.spoc,
    }
    await this.createActivity(newActivity);
    if (updatedTask.vendor) {
      await this.createActivity({ ...newActivity, org: updatedTask.vendor });
    }
  }

  @OnEvent('task.priority.updated', { async: true })
  async onTaskPriorityUpdateEvent(payload: any) {
    let { updatedTask, user } = payload;
    // this.logger.debug(updatedTask)
    let newActivity: CreateActivityDto = {
      title: `${user.firstName} updated the task priority to "${updatedTask.priority}".`,
      titleWithPlaceholders: `{{user.firstName}} updated the task priority to "{{updatedTask.priority}}".`,
      org: updatedTask.org,
      task: updatedTask._id,
      actor: user._id,
      dueDate: updatedTask.dueDate,
      contact: updatedTask.spoc,
    }
    await this.createActivity(newActivity);
    if (updatedTask.vendor) {
      await this.createActivity({ ...newActivity, org: updatedTask.vendor });
    }
  }


  @OnEvent('task.spoc.updated', { async: true })
  async onTaskSpocUpdateEvent(payload: any) {
    let { updatedTask, user } = payload;
    // this.logger.debug(updatedTask)
    let newActivity: CreateActivityDto = {
      title: `${user.firstName} updated the point of contact of the task to "${updatedTask.spoc.firstName} ${updatedTask.spoc.lastName}".`,
      titleWithPlaceholders: `{{user.firstName}} updated the point of contact of the task to "{{updatedTask.spoc}}".`,
      org: updatedTask.org,
      task: updatedTask._id,
      actor: user._id,
      dueDate: updatedTask.dueDate,
      contact: updatedTask.spoc,
    }
    await this.createActivity(newActivity);
    if (updatedTask.vendor) {
      await this.createActivity({ ...newActivity, org: updatedTask.vendor });
    }
  }

  @OnEvent('task.org.updated', { async: true })
  async onTaskAccountUpdateEvent(payload: any) {
    let { updatedTask, user } = payload;
    // this.logger.debug(updatedTask)
    let newActivity: CreateActivityDto = {
      title: `${user.firstName} updated the organization of the task to "${updatedTask.org.title}".`,
      titleWithPlaceholders: `{{user.firstName}} updated the account of the task to "{{updatedTask.account}}".`,
      org: updatedTask.org,
      task: updatedTask._id,
      actor: user._id,
      dueDate: updatedTask.dueDate,
      contact: updatedTask.spoc,
    }
    await this.createActivity(newActivity);
  }

  @OnEvent('task.removed', { async: true })
  async onTaskDeletedEvent(payload: any) {
    let { task, user } = payload;
    // this.logger.log(task)
    let newActivity: CreateActivityDto = {
      title: `${user.firstName} deleted the task "${task.title}".`,
      titleWithPlaceholders: `{{user.firstName}} deleted the task "{{newTask.title}}".`,
      org: task.org,
      task: task._id,
      actor: user._id,
      dueDate: task.dueDate,
      contact: task.spoc
    }
    await this.createActivity(newActivity);
    if (task.vendor) {
      await this.createActivity({ ...newActivity, org: task.vendor });
    }
  }

  @OnEvent('contact.created', { async: true })
  async onContactCreatedEvent(payload: any) {
    let { createdContact, user } = payload
    let newActivity: CreateActivityDto = {
      title: `${user.firstName} created a new contact "${createdContact.firstName + " " + createdContact.lastName}".`,
      titleWithPlaceholders: `{{user.firstName}} added a new contact "{{createdContact.name}}".`,
      contact: createdContact._id,
      org: createdContact.accountOrg,
      actor: user._id,
    }
    await this.createActivity(newActivity);
    if (createdContact.vendor) {
      await this.createActivity({ ...newActivity, org: createdContact.vendor });
    }
  }

  @OnEvent('contact.account_org.updated', { async: true })
  async onContactAccountInfoUpdateEvent(payload: any) {
    let { updatedContact, user } = payload;
    let newActivity: CreateActivityDto = {
      title: `${user.firstName} updated the contact's account org to "${updatedContact.accountOrg.title}".`,
      titleWithPlaceholders: `{{user.firstName}} the contact's account org to "{{updatedContact.account.title}}".`,
      contact: updatedContact._id,
      org: updatedContact.accountOrg,
      actor: user._id,
    }
    await this.createActivity(newActivity);
  }

  @OnEvent('contact.reporting_to.updated', { async: true })
  async onContactReportingToUpdateEvent(payload: any) {
    let { updatedContact, user } = payload;
    let newActivity: CreateActivityDto = {
      title: `${user.firstName} updated the contact's reporting  to "${updatedContact.reportingTo.firstName + " " + updatedContact.reportingTo.lastName}".`,
      titleWithPlaceholders: `{{user.firstName}} updated the contact's reporting  to "{{updatedContact.reportingTo.firstName}}".`,
      contact: updatedContact._id,
      org: updatedContact.accountOrg,
      actor: user._id,
    }
    await this.createActivity(newActivity);
    if (updatedContact.vendor) {
      await this.createActivity({ ...newActivity, org: updatedContact.vendor });
    }
  }

  @OnEvent('contact.industry.updated', { async: true })
  async onContactIndustryUpdateEvent(payload: any) {
    let { updatedContact, user } = payload;
    let newActivity: CreateActivityDto = {
      title: `${user.firstName} updated the contact's industry "${updatedContact.industry.name}".`,
      titleWithPlaceholders: `{{user.firstName}} updated the contact's industry  to "{{updatedContact.industry.name}}".`,
      contact: updatedContact._id,
      org: updatedContact.accountOrg,
      actor: user._id,
    }
    await this.createActivity(newActivity);
    if (updatedContact.vendor) {
      await this.createActivity({ ...newActivity, org: updatedContact.vendor });
    }
  }

  @OnEvent('contact.status.changed', { async: true })
  async onContactStatusChangedEvent(payload: any) {
    let { updatedContact, commentObj, user } = payload;
    const createComment = await this.createComment(commentObj);
    // this.logger.log(createComment);
    // this.logger.log(payload);
    let newActivity: CreateActivityDto = {
      title: `${user.firstName} updated the "${updatedContact.firstName + updatedContact.lastName}" contact status to "${updatedContact.status}".`,
      titleWithPlaceholders: `{{user.firstName}} updated the "{{updatedContact.firstName + updatedContact.lastName}}" contact status to "{{updatedContact.status}}".`,
      contact: updatedContact._id,
      org: updatedContact.accountOrg,
      actor: user._id,
      comment: createComment._id
    }
    await this.createActivity(newActivity);
    if (updatedContact.vendor) {
      await this.createActivity({ ...newActivity, org: updatedContact.vendor });
    }
  }

  @OnEvent('contact.assign_to.changed', { async: true })
  async onContactAssignToUpdateEvent(payload: any) {
    let { updatedContact, commentDto, user } = payload;
    const createComment = await this.createComment(commentDto);
    // this.logger.log(createComment);
    // this.logger.log(payload);
    let newActivity: CreateActivityDto = {
      title: `${user.firstName} assigned the "${updatedContact.firstName + " " + updatedContact.lastName}" contact to ${updatedContact.assignTo.firstName}.`,
      titleWithPlaceholders: `{{user.firstName}} assigned the "{{updatedContact.firstName + updatedContact.lastName}}" contact to {{updatedContact.assignee.firstName}}.`,
      contact: updatedContact._id,
      org: updatedContact.accountOrg,
      actor: user._id,
      comment: createComment._id
    }
    const activity = await this.createActivity(newActivity);
    // this.logger.log(activity);
    if (updatedContact.vendor) {
      await this.createActivity({ ...newActivity, org: updatedContact.vendor });
    }
    // this.emailAlertService.moveToEmail(`${updatedContact?.assignee.firstName}`, `${updatedContact?.assignee.email}`, `Contact is assigned to you`, `${updatedContact?.firstName + updatedContact.lastName} contact is assigned to ${updatedContact?.assignee.firstName}`, 'contacts', `${updatedContact._id}`)
  }

  // @OnEvent('org.created', { async: true })
  // async onOrgCreatedEvent(payload: any) {
  //   let { createdOrg, user } = payload
  //   let newActivity: CreateActivityDto = {
  //     title: `${user.firstName} created a new org "${createdOrg.title}".`,
  //     titleWithPlaceholders: `{{user.firstName}} created a new org "{{createdOrg.name}}".`,
  //     org: createdOrg._id,
  //     actor: user._id,
  //   }
  //   await this.createActivity(newActivity);
  // }

  @OnEvent('org.account_type.updated', { async: true })
  async onOrgAccountTypeUpdateEvent(payload: any) {
    let { existingOrg, updatedOrg, user } = payload;
    let newActivity: CreateActivityDto = {
      title: `${user.firstName} updated the org account type from ${existingOrg.accountType.name} to ${updatedOrg.accountType.name}.`,
      titleWithPlaceholders: `{{user.firstName}} updated the org account type to "{{updatedOrg.accountType.name}}".`,
      org: updatedOrg._id,
      actor: user._id,
    }
    await this.createActivity(newActivity);
  }

  @OnEvent('org.industry.updated', { async: true })
  async onOrgIndustryUpdateEvent(payload: any) {
    let { existingOrg, updatedOrg, user } = payload;
    let newActivity: CreateActivityDto = {
      title: `${user.firstName} updated the industry from ${existingOrg.industryOrDomain.name} to ${updatedOrg.industryOrDomain.name}.`,
      titleWithPlaceholders: `{{user.firstName}} updated the industry to "{{updatedOrg.industryOrDomain.name}}".`,
      org: updatedOrg._id,
      actor: user._id,
    }
    await this.createActivity(newActivity);
  }

  @OnEvent('org.status.changed', { async: true })
  async onOrgStatusChangedEvent(payload: any) {
    let { updatedOrg, comment, user } = payload;
    const createComment = await this.createComment(comment);
    console.log(createComment)
    let newActivity: CreateActivityDto = {
      title: `${user?.firstName} updated the "${updatedOrg?.title}" org status to "${updatedOrg?.status}".`,
      titleWithPlaceholders: `{{createdBy.firstName}} updated the "{{updatedOrg.title}}" org status to "{{updatedOrg.status}}".`,
      org: updatedOrg._id,
      actor: user._id,
      comment: createComment?._id || undefined
    }
    await this.createActivity(newActivity);
  }

  @OnEvent('org.custom.status.changed', { async: true })
  async onOrgCustomStatusChangedEvent(payload: any) {
    const { existingOrg, updatedOrg, comment, user } = payload;

    // 1. Create and populate the comment
    const createdComment = await this.createComment({
      ...comment,
      org: updatedOrg._id,
      user: user._id,
    });

    const populatedComment = await this.commentModel.findById(createdComment._id)
      .populate({
        path: 'attachments',
        select: '_id originalName locationUrl',
        model: 'FileMetadata',
      })
      .exec();

    // 2. Activity for status change
    await this.createActivity({
      title: `${user.firstName} changed the status from "${existingOrg.customStatus.name}" to "${updatedOrg.customStatus.name}".`,
      titleWithPlaceholders: `{{user.firstName}} changed the status to "{{updatedOrg.customStatus.name}}".`,
      org: updatedOrg._id,
      actor: user._id,
      comment: createdComment._id,
    });

    // 3. Activity for comment text (if present)
    if (populatedComment?.contents?.trim()) {
      await this.createActivity({
        title: `${user.firstName} commented: ${populatedComment.contents}`,
        titleWithPlaceholders: `{{user.firstName}} commented: {{comment.contents}}`,
        org: updatedOrg._id,
        actor: user._id,
        comment: createdComment._id,
      });
    }

    // 4. Activity for each attached file
    for (const file of populatedComment?.attachments ?? []) {
      await this.createActivity({
        title: `Attached file: <a href="${file.locationUrl}" target="_blank" rel="noopener noreferrer">${file.originalName}</a>`,
        titleWithPlaceholders: `Attached file: [${file.originalName}](${file.locationUrl})`,
        org: updatedOrg._id,
        actor: user._id,
        comment: createdComment._id,
      });
    }
  }

  @OnEvent('org.title.updated', { async: true })
  async onOrgTitleUpdateEvent(payload: any) {
    let { existingOrg, updatedOrg, user } = payload;
    let newActivity: CreateActivityDto = {
      title: `${user.firstName} updated the company name from ${existingOrg.title} to ${updatedOrg.title}.`,
      titleWithPlaceholders: `{{user.firstName}} updated the company name to "{{updatedOrg.title}}".`,
      org: updatedOrg._id,
      actor: user._id,
    }
    await this.createActivity(newActivity);
  }

  @OnEvent('org.legalName.updated', { async: true })
  async onOrgLegalNameUpdateEvent(payload: any) {
    let { existingOrg, updatedOrg, user } = payload;
    let newActivity: CreateActivityDto = {
      title: `${user.firstName} updated the company legal name from ${existingOrg.legalName} to ${updatedOrg.legalName}.`,
      titleWithPlaceholders: `{{user.firstName}} updated the company legal name to "{{updatedOrg.legalName}}".`,
      org: updatedOrg._id,
      actor: user._id,
    }
    await this.createActivity(newActivity);
  }

  @OnEvent('org.country.updated', { async: true })
  async onOrgCountryUpdateEvent(payload: any) {
    let { existingOrg, updatedOrg, user } = payload;
    let newActivity: CreateActivityDto = {
      title: `${user.firstName} updated the country from ${existingOrg.country.countryName} to ${updatedOrg.country.countryName}.`,
      titleWithPlaceholders: `{{user.firstName}} updated the country to "{{updatedOrg.country.countryName}}".`,
      org: updatedOrg._id,
      actor: user._id,
    }
    await this.createActivity(newActivity);
  }

  @OnEvent('org.state.updated', { async: true })
  async onOrgStateUpdateEvent(payload: any) {
    let { existingOrg, updatedOrg, user } = payload;
    console.log(payload)
    let newActivity: CreateActivityDto = {
      title: `${user.firstName} updated the state from ${existingOrg.state.stateName} to ${updatedOrg.state.stateName}.`,
      titleWithPlaceholders: `{{user.firstName}} updated the state to "{{updatedOrg.state.stateName}}".`,
      org: updatedOrg._id,
      actor: user._id,
    }
    await this.createActivity(newActivity);
  }

  @OnEvent('org.contactEmail.updated', { async: true })
  async onOrgContactEmailEvent(payload: any) {
    let { existingOrg, updatedOrg, user } = payload;
    let newActivity: CreateActivityDto = {
      title: `${user.firstName} updated the contact email from ${existingOrg.contactDetails[0].contactEmail} to ${updatedOrg.contactDetails[0].contactEmail}.`,
      titleWithPlaceholders: `{{user.firstName}} updated the contact email to "{{updatedOrg.contactDetails[0].contactEmail}}".`,
      org: updatedOrg._id,
      actor: user._id,
    }
    await this.createActivity(newActivity);
  }

  @OnEvent('org.contactNumber.updated', { async: true })
  async onOrgContactNumberEvent(payload: any) {
    let { existingOrg, updatedOrg, user } = payload;
    let newActivity: CreateActivityDto = {
      title: `${user.firstName} updated the contact number from ${existingOrg.contactDetails[0].contactNumber} to ${updatedOrg.contactDetails[0].contactNumber}.`,
      titleWithPlaceholders: `{{user.firstName}} updated the contact number to "{{updatedOrg.contactDetails[0].contactNumber}}".`,
      org: updatedOrg._id,
      actor: user._id,
    }
    await this.createActivity(newActivity);
  }

  @OnEvent('org.contactAddress.updated', { async: true })
  async onOrgContactAddressEvent(payload: any) {
    let { existingOrg, updatedOrg, user } = payload;
    let newActivity: CreateActivityDto = {
      title: `${user.firstName} updated the contact address from ${existingOrg.contactAddress[0].apartment} to ${updatedOrg.contactAddress[0].apartment}.`,
      titleWithPlaceholders: `{{user.firstName}} updated the contact address to "{{updatedOrg.contactAddress[0].apartment}}".`,
      org: updatedOrg._id,
      actor: user._id,
    }
    await this.createActivity(newActivity);
  }

  @OnEvent('org.status.bulkChanged', { async: true })
  async onOrgStatusBulkChangedEvent(payload: any) {
    let { updatedOrgs, comment, user } = payload;
    const createComment = await this.createComment(comment);
    for (const updatedOrg of updatedOrgs) {
      try {
        // Create the activity data
        const newActivity: CreateActivityDto = {
          title: `${user?.firstName} updated the "${updatedOrg?.title}" org status to "${updatedOrg?.status}".`,
          titleWithPlaceholders: `{{createdBy.firstName}} updated the "{{updatedOrg.title}}" org status to "{{updatedOrg.status}}".`,
          org: updatedOrg._id,
          actor: user._id,
          comment: createComment?._id || undefined
        };
        // Create the activity
        await this.createActivity(newActivity);

      } catch (error) {
        this.logger.error(`Failed to create activity for org: ${updatedOrg.title}`, error.stack);
      }
    }
  }


  @OnEvent('org.assign_to.changed', { async: true })
  async onOrgAssigneeUpdateEvent(payload: any) {
    let { updatedOrg, commentDto, user } = payload;
    let createComment = null;
    if (updatedOrg?.title?.includes(' assigned the') || updatedOrg?.status?.includes(' assigned the')) {
      createComment = await this.createComment(commentDto);
    }
    let newActivity: CreateActivityDto = {
      title: `${user?.firstName} assigned the "${updatedOrg?.title}" org to ${updatedOrg?.assignTo?.firstName}.`,
      titleWithPlaceholders: `{{user.firstName}} assigned the "{{updatedOrg.title}}" org to {{updatedOrg.assignTo.firstName}}.`,
      org: updatedOrg._id,
      actor: user._id,
      comment: createComment?._id || undefined
    }
    const activity = await this.createActivity(newActivity);
    this.logger.log(activity);
    // this.emailAlertService.moveToEmail(`${updatedContact?.assignee.firstName}`, `${updatedContact?.assignee.email}`, `Contact is assigned to you`, `${updatedContact?.firstName + updatedContact.lastName} contact is assigned to ${updatedContact?.assignee.firstName}`, 'contacts', `${updatedContact._id}`)
  }

  @OnEvent('region.created', { async: true })
  async onRegionCreatedEvent(payload: any) {
    let { populatedRegion, user } = payload;
    let newActivity: CreateActivityDto = {
      title: `${user.firstName} created a new region "${populatedRegion.country.countryName} - ${populatedRegion.state.stateName}".`,
      titleWithPlaceholders: `{{createdBy.firstName}} created a new region "{{newRegion.country.countryName} - {newRegion.state.stateName}}".`,
      region: populatedRegion._id,
      actor: user._id
    }
    await this.createActivity(newActivity);
  }

  @OnEvent('region.country.updated', { async: true })
  async onRegionCountryUpdatedEvent(payload: any) {
    const { existingRegion, updatedRegion, user } = payload;
    console.log(updatedRegion);

    const newActivity: CreateActivityDto = {
      title: `${user.firstName} updated the region from "${existingRegion.country.countryName}" - "${existingRegion.state.stateName}" to "${updatedRegion.country.countryName}" - "${updatedRegion.state.stateName}" .`,
      titleWithPlaceholders: `{{updatedBy.firstName}} updated the country to "{{updatedRegion.country.countryName}}" in region "{{updatedRegion.name}}".`,
      region: updatedRegion._id,
      actor: user._id,
    };

    await this.createActivity(newActivity);
  }

  @OnEvent('region.state.updated', { async: true })
  async onRegionStateUpdatedEvent(payload: any) {
    const { existingRegion, updatedRegion, user } = payload;
    console.log(updatedRegion);

    const newActivity: CreateActivityDto = {
      title: `${user.firstName} updated the state of region from "${existingRegion.state.stateName}"to "${updatedRegion.state.stateName}".`,
      titleWithPlaceholders: `{{updatedBy.firstName}} updated the state to "{{updatedRegion.state.stateName}}" in region "{{updatedRegion.name}}".`,
      region: updatedRegion._id,
      actor: user._id,
    };

    await this.createActivity(newActivity);
  }

  @OnEvent('region.status.changed', { async: true })
  async onRegionStatusChangedEvent(payload: any) {
    const { existingRegion, updatedRegion, comment, user } = payload;
    const createdComment = await this.createComment(comment);
    const newActivity: CreateActivityDto = {
      title: `${user.firstName} changed the status of region "${updatedRegion.country.countryName}" - "${updatedRegion.state.stateName}" from "${existingRegion.status.name}" to "${updatedRegion.status.name}".`,
      titleWithPlaceholders: `{{user.firstName}} changed the status of region "{{updatedRegion.country.countryName}}" - "{{updatedRegion.state.stateName}}" to "{{updatedRegion.status.name}}".`,
      region: updatedRegion._id,
      actor: user._id,
      comment: createdComment._id,
    };

    await this.createActivity(newActivity);
  }

  @OnEvent('user.custom.status.changed', { async: true })
  async onUserCustomStatusChangedEvent(payload: any) {
    const { existingUser, updatedUser, comment, user } = payload;
    const createdComment = await this.createComment(comment);
    const newActivity: CreateActivityDto = {
      title: `${user.firstName} changed the status from "${existingUser.customStatus.name}" to "${updatedUser.customStatus.name}".`,
      titleWithPlaceholders: `{{user.firstName}} changed the status from {{existingUser.customStatus.name}} to  "{{updatedOrg.customStatus.name}}".`,
      user: updatedUser._id,
      actor: user._id,
      comment: createdComment._id,
    };

    await this.createActivity(newActivity);
  }

  @OnEvent('user.firstName.updated', { async: true })
  async onUserfirstNameUpdateEvent(payload: any) {
    let { existingUser, updatedUser, user } = payload;
    let newActivity: CreateActivityDto = {
      title: `${user.firstName} updated the user full name from ${existingUser.firstName} to ${updatedUser.firstName}.`,
      titleWithPlaceholders: `{{user.firstName}} updated the user full name to "{{updatedUser.firstName}}".`,
      user: updatedUser._id,
      actor: user._id,
    }
    await this.createActivity(newActivity);
  }

  @OnEvent('user.country.updated', { async: true })
  async onUserCountryUpdateEvent(payload: any) {
    let { existingUser, updatedUser, user } = payload;
    let newActivity: CreateActivityDto = {
      title: `${user.firstName} updated the country from ${existingUser.country} to ${updatedUser.country}.`,
      titleWithPlaceholders: `{{user.firstName}} updated the country to "{{updatedUser.country}}".`,
      user: updatedUser._id,
      actor: user._id,
    }
    await this.createActivity(newActivity);
  }

  @OnEvent('user.state.updated', { async: true })
  async onUserStateUpdateEvent(payload: any) {
    let { existingUser, updatedUser, user } = payload;
    let newActivity: CreateActivityDto = {
      title: `${user.firstName} updated the state from ${existingUser.state.stateName} to ${updatedUser.state.stateName}.`,
      titleWithPlaceholders: `{{user.firstName}} updated the state to "{{updatedUser.state.stateName}}".`,
      user: updatedUser._id,
      actor: user._id,
    }
    await this.createActivity(newActivity);
  }

  @OnEvent('user.contactEmail.updated', { async: true })
  async onUserContactEmailEvent(payload: any) {
    let { existingUser, updatedUser, user } = payload;
    let newActivity: CreateActivityDto = {
      title: `${user.firstName} updated the contact email from ${existingUser.contactDetails[0].contactEmail} to ${updatedUser.contactDetails[0].contactEmail}.`,
      titleWithPlaceholders: `{{user.firstName}} updated the contact email to "{{updatedUser.contactDetails[0].contactEmail}}".`,
      user: updatedUser._id,
      actor: user._id,
    }
    await this.createActivity(newActivity);
  }

  @OnEvent('onboarding.identifier.value.updated', { async: true })
  async onIdentifierValueUpdateEvent(payload: any) {
    const { existingIdentifier, updatedOnboarding, user, updatedIdentifier } = payload;
    console.log(payload)

    const entity = updatedOnboarding.user || updatedOnboarding.org;
    const entityType = updatedOnboarding.user ? 'User' : 'Org';

    let newActivity: CreateActivityDto;
    if (entityType == 'User') {
      newActivity = {
        title: `${user.firstName} updated the identifier value for ${updatedIdentifier.identifier.name} from  ${existingIdentifier.value} to ${updatedIdentifier.value} `,
        titleWithPlaceholders: `{{user.firstName}} updated the identifier value for "{{updatedIdentifier.identifier.name}}" to {{updatedIdentifier.value}}`,
        user: entity._id,
        actor: user._id,
      };
      console.log(newActivity);
      await this.createActivity(newActivity);
    }
    else {
      newActivity = {
        title: `${user.firstName} updated the identifier value for ${updatedIdentifier.identifier.name} from  ${existingIdentifier.value} to ${updatedIdentifier.value}`,
        titleWithPlaceholders: `{{user.firstName}} updated the identifier value for "{{updatedIdentifier.identifier.name}}" to {{updatedIdentifier.value}}`,
        org: entity._id,
        actor: user._id,
      };
      console.log(newActivity);
      await this.createActivity(newActivity);
    }

  }

  @OnEvent('onboarding.identifier.attachment.updated', { async: true })
  async onIdentifierAttachmentUpdateEvent(payload: any) {
    const { existingIdentifier, updatedOnboarding, user, updatedIdentifier } = payload;
    console.log(payload)

    const entity = updatedOnboarding.user || updatedOnboarding.org;
    const entityType = updatedOnboarding.user ? 'User' : 'Org';

    let newActivity: CreateActivityDto;

    if (entityType === 'User') {
      newActivity = {
        title: `${user.firstName} updated the attachments for ${updatedIdentifier.identifier.name} from ${existingIdentifier.attachmentUrls[0].originalName} to ${updatedIdentifier.attachmentUrls[0].originalName}`,
        titleWithPlaceholders: `{{user.firstName}} updated the attachments for "{{updatedIdentifier.identifier.name}}"`,
        user: entity._id,
        actor: user._id,
      };
    } else {
      newActivity = {
        title: `${user.firstName} updated the attachments for ${updatedIdentifier.identifier.name} from ${existingIdentifier.attachmentUrls[0].originalName} to ${updatedIdentifier.attachmentUrls[0].originalName}`,
        titleWithPlaceholders: `{{user.firstName}} updated the attachments for "{{updatedIdentifier.identifier.name}}"`,
        org: entity._id,
        actor: user._id,
      };
    }

    console.log(newActivity);
    await this.createActivity(newActivity);
  }

  @OnEvent('onboarding.file.status.updated', { async: true })
  async handleFileStatusUpdateEvent(payload: any) {
    const { updatedFile, status, user, org, createdByUser } = payload;
    console.log(payload)
    const entityId = user || org;
    const entityType = user ? 'User' : 'Org';

    let newActivity: CreateActivityDto;

    if (entityType === 'User') {
      newActivity = {
        title: `${createdByUser.firstName} updated the status of the file "${updatedFile.originalName}" to "${status}".`,
        titleWithPlaceholders: `{{createdByuser.firstName}} updated the status of the file "{{updatedFile.originalName}}" to "{{status}}"`,
        user: entityId,
        actor: createdByUser._id,
      };
      await this.createActivity(newActivity);
    } else if (entityType === 'Org') {
      newActivity = {
        title: `${createdByUser.firstName} updated the status of the file "${updatedFile.originalName}" to "${status}".`,
        titleWithPlaceholders: `{{createdByuser.firstName}} updated the status of the file "{{updatedFile.originalName}}" to "{{status}}"`,
        org: entityId,
        actor: createdByUser._id,
      };
      await this.createActivity(newActivity);
    }

  }

  @OnEvent('identifier.created', { async: true })
  async onIdentifierCreateEvent(payload: any) {
    const { existingIdentifier, user, entityType } = payload
    let newActivity: CreateActivityDto = {
      title: `${user.firstName} created the optional ${entityType} identifier ${existingIdentifier.name}.`,
      titleWithPlaceholders: `{{user.firstName}} created the identifier "{{identifier.name}}".`,
      region: existingIdentifier.region._id,
      actor: user._id,
    }
    await this.createActivity(newActivity);
  }

  @OnEvent('identifier.updated', { async: true })
  async onIdentifierUpdateEvent(payload: any) {
    const { region, title, user } = payload
    let newActivity: CreateActivityDto = {
      title: title,
      titleWithPlaceholders: `Identifier has been updated.`,
      region: region._id,
      actor: user._id,
    }
    await this.createActivity(newActivity);
  }

  @OnEvent('identifier.deleted', { async: true })
  async onIdentifierDeleteEvent(payload: any) {
    const { identifier, user } = payload
    let newActivity: CreateActivityDto = {
      title: `${user.firstName} deleted the identifier ${identifier.name}.`,
      titleWithPlaceholders: `{{user.firstName}} deleted the identifier "{{identifier.name}}".`,
      region: identifier.region._id,
      actor: user._id,
    }
    await this.createActivity(newActivity);
  }

  @OnEvent('business-unit.created', { async: true })
  async onBusinessUnitCreatedEvent(payload: any) {
    const { createdBusinessUnit, user } = payload
    let newActivity: CreateActivityDto = {
      title: `${user.firstName} created the business unit ${createdBusinessUnit.label}.`,
      titleWithPlaceholders: `{{user.firstName}} created the business unit "{{createdBusinessUnit.label}}".`,
      businessUnit: createdBusinessUnit._id,
      actor: user._id,
    }
    await this.createActivity(newActivity);
  }

  @OnEvent('business-unit.moved.root', { async: true })
  async onBusinessUnitMovedToRootEvent(payload: any) {
    const { movingBusinessUnit, user } = payload
    let newActivity: CreateActivityDto = {
      title: `${user.firstName} moved the business unit ${movingBusinessUnit.label} to the root level.`,
      titleWithPlaceholders: `{{user.firstName}} moved the business unit "{{movingBusinessUnit.label}} to the root level".`,
      businessUnit: movingBusinessUnit._id,
      actor: user._id,
    }
    await this.createActivity(newActivity);
  }

  @OnEvent('business-unit.moved.destination', { async: true })
  async onBusinessUnitMovedToDestinationEvent(payload: any) {
    const { movingBusinessUnit, destinationBusinessUnit, user } = payload
    let newActivity: CreateActivityDto = {
      title: `${user.firstName} moved the business unit ${movingBusinessUnit.label} to ${destinationBusinessUnit.label}.`,
      titleWithPlaceholders: `{{user.firstName}} moved the business unit "{{movingBusinessUnit.label}} to {{destinationBusinessUnit.label}}".`,
      businessUnit: movingBusinessUnit._id,
      actor: user._id,
    }
    await this.createActivity(newActivity);
  }

  @OnEvent('business-unit.merged.destination', { async: true })
  async onBusinessUnitMergedToDestinationEvent(payload: any) {
    const { movingBusinessUnit, destinationBusinessUnit, user } = payload
    let newActivity: CreateActivityDto = {
      title: `${user.firstName} merged the business unit ${movingBusinessUnit.label} to ${destinationBusinessUnit.label}.`,
      titleWithPlaceholders: `{{user.firstName}} merged the business unit "{{movingBusinessUnit.label}} to {{destinationBusinessUnit.label}}".`,
      businessUnit: movingBusinessUnit._id,
      actor: user._id,
    }
    await this.createActivity(newActivity);
  }

  @OnEvent('business-unit.updated', { async: true })
  async onBusinessUnitUpdatedEvent(payload: any) {
    const { updatedBusinessUnit, user } = payload
    let newActivity: CreateActivityDto = {
      title: `${user.firstName} updated the business unit ${updatedBusinessUnit.label}.`,
      titleWithPlaceholders: `{{user.firstName}} updated the business unit "{{updatedBusinessUnit.label}}".`,
      businessUnit: updatedBusinessUnit._id,
      actor: user._id,
    }
    await this.createActivity(newActivity);
  }

  @OnEvent('business-unit.hard.delete', { async: true })
  async onBusinessUnitHardDeletedEvent(payload: any) {
    const { businessUnit, user } = payload
    let newActivity: CreateActivityDto = {
      title: `${user.firstName} hard-deleted the business unit ${businessUnit.label}.`,
      titleWithPlaceholders: `{{user.firstName}} hard-deleted the business unit "{{businessUnit.label}}".`,
      businessUnit: businessUnit._id,
      actor: user._id,
    }
    await this.createActivity(newActivity);
  }

  @OnEvent('business-unit.soft.delete', { async: true })
  async onBusinessUnitSoftDeletedEvent(payload: any) {
    const { businessUnit, user } = payload
    let newActivity: CreateActivityDto = {
      title: `${user.firstName} soft-deleted the business unit ${businessUnit.label}.`,
      titleWithPlaceholders: `{{user.firstName}} soft-deleted the business unit "{{businessUnit.label}}".`,
      businessUnit: businessUnit._id,
      actor: user._id,
    }
    await this.createActivity(newActivity);
  }

  @OnEvent('business-unit.restore', { async: true })
  async onBusinessUnitRestoredEvent(payload: any) {
    const { businessUnit, user } = payload
    let newActivity: CreateActivityDto = {
      title: `${user.firstName} restored the business unit ${businessUnit.label}.`,
      titleWithPlaceholders: `{{user.firstName}} restored the business unit "{{businessUnit.label}}".`,
      businessUnit: businessUnit._id,
      actor: user._id,
    }
    await this.createActivity(newActivity);
  }


  @OnEvent('rate-card.created', { async: true })
  async onRateCardCreated(payload: any) {
    let { rateCard, user, org } = payload;
    let newActivity: CreateActivityDto = {
      title: `${user.firstName} created a new rate card "${rateCard.name}".`,
      titleWithPlaceholders: `{{createdBy.firstName}} created a new region "{{rateCard.name}}".`,
      rateCard: rateCard._id,
      ...(org ? { org } : {}),
      actor: user._id
    }
    this.logger.log(JSON.stringify(newActivity))
    await this.createActivity(newActivity);
  }

  @OnEvent('rate-card.updated', { async: true })
  async onRateCardUpdated(payload: any) {
    const { updated, previous, user } = payload;

    const changes: string[] = [];

    if (previous.name !== updated.name) {
      changes.push(`Name changed from "${previous.name}" to "${updated.name}"`);
    }

    if (previous.fixedRate !== updated.fixedRate) {
      changes.push(`Fixed Rate changed from "${previous.fixedRate}" to "${updated.fixedRate}"`);
    }

    if (previous.percentageOfCTC !== updated.percentageOfCTC) {
      changes.push(`Percentage of CTC changed from "${previous.percentageOfCTC}" to "${updated.percentageOfCTC}"`);
    }

    if (previous.description !== updated.description) {
      changes.push(`Description updated`);
    }

    if (previous.isDeleted !== updated.isDeleted) {
      const deletedStatus = updated.isDeleted ? 'deleted' : 'restored';
      changes.push(`Rate card was ${deletedStatus}`);
    }

    // You can add more fields similarly if needed

    const changesSummary = changes.length > 0 ? changes.join('; ') : ' ';

    const newActivity: CreateActivityDto = {
      title: `${user.firstName} updated the rate card "${updated.name}". Changes: ${changesSummary}`,
      titleWithPlaceholders: `{{updatedBy.firstName}} updated the rate card "{{rateCard.name}}". Changes: ${changesSummary}`,
      rateCard: updated._id,
      actor: user._id,
      ...(updated.category?.client ? { org: updated.category.client } : {}),
    };

    await this.createActivity(newActivity);
  }

}
