import {
  BadRequestException,
  ConflictException,
  ForbiddenException,
  HttpException,
  HttpStatus,
  Inject,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
  UnauthorizedException,
  forwardRef,
} from '@nestjs/common';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { InjectModel } from '@nestjs/mongoose';
import { BasicUser, BasicUserDocument } from './schemas/basic-user.schema';
import { Model, PopulateOptions, Types } from 'mongoose';
import { Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { RolesDto } from './dto/roles.dto';
import { ForgotPasswordDto } from './dto/forgot-password.dto';
import { PasswordDto } from './dto/password.dto';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';
import { v4 } from 'uuid';
import {
  ForgotPassword,
  ForgotPasswordDocument,
} from './schemas/forgot-password.schema';
import {
  addHours,
  addMinutes,
  differenceInHours,
  differenceInMinutes,
  isAfter,
} from 'date-fns';
import { VerifyUuidDto } from './dto/verify-uuid.dto';
import { ResetPasswordDto } from './dto/reset-password.dto';
import { AuthService } from 'src/auth/auth.service';
import { omit } from 'lodash';
import { Org, OrgDocument } from 'src/org/schemas/org.schema';
import { Role } from 'src/auth/enums/role.enum';
import {
  NotificationType,
  OrgType,
  SourceType,
  StatusConfigType,
} from 'src/shared/constants';
import { CommentDto } from 'src/common/dto/comment.dto';
import { Comment } from 'src/common/schemas/comment.schema';
import { VerifyOtpDto } from './dto/verify-otp.dto';
import { CreateFreelancerDto } from './dto/create-freelancer-dto';
import { StatusService } from 'src/status/status.service';
import { ChangeCustomStatusDto } from './dto/change-status.dto';
import { StatusDocument } from 'src/status/schemas/status.schema';
import { StateDocument } from 'src/state/schemas/state.schema';
import { CountryDocument } from 'src/country/schemas/country.schema';
import { FilterUsersDto } from './dto/filter-user-by-org-and-bu.dto';
import { CreateMemberDto } from 'src/org/dto/create-member.dto';
import { UpdateMemberDto } from 'src/org/dto/update-member.dto';
import { BusinessUnitService } from '../business-unit/business-unit.service';
import { UserProfileDto } from 'src/auth/dto/user-profile.dto';
import * as bcrypt from 'bcrypt';
import { UserTemp } from './schemas/user-temp.schema';
import { VendorInviteService } from 'src/vendor-invite/vendor-invite.service';
import { DeleteUser } from 'src/delete-user/schemas/delete-user.schema';
import { CreateDeleteUserDto } from 'src/delete-user/dto/create-delete-user.dto';
import { EndpointPermission } from 'src/endpoint-permissions/schemas/endpointpermissions.schema';
import { EndpointsRoles } from 'src/endpoints-roles/schemas/endpoints-roles.schema';
import { BusinessUnit } from 'src/business-unit/schemas/business-unit.schema';
import { Roles } from 'src/roles/schemas/roles.schema';
import { NotificationsService } from 'src/notification/notifications.service';

export interface UserNode {
  _id: string;
  firstName: string;
  reportingTo: string[];
  roles: Role[];
  roleAliases: string[];
  children: UserNode[];
}

@Injectable()
export class UserService {
  // HOURS_TO_VERIFY = 24;
  //HOURS_TO_VERIFY = this.configService.get<number>('HOURS_TO_VERIFY');
  // HOURS_TO_BLOCK = 8;
  //HOURS_TO_BLOCK = this.configService.get<number>('HOURS_TO_BLOCK');
  // LOGIN_ATTEMPTS_TO_BLOCK = 5;
  //LOGIN_ATTEMPTS_TO_BLOCK = this.configService.get<number>(
  //'LOGIN_ATTEMPTS_TO_BLOCK',
  //);

  private readonly logger = new Logger(UserService.name);

  constructor(
    private configService: ConfigService,
    @Inject(forwardRef(() => AuthService))
    private readonly authService: AuthService,
    @InjectModel(BasicUser.name) private basicUserModel: Model<BasicUser>,
    @InjectModel(DeleteUser.name) private deleteUserModel: Model<DeleteUser>,
    @InjectModel(ForgotPassword.name)
    private forgotPasswordModel: Model<ForgotPassword>,
    @InjectModel(Org.name) private orgModel: Model<OrgDocument>,
    @InjectModel(Comment.name) private commentModel: Model<Comment>,
    @InjectModel(UserTemp.name) private userTempModel: Model<UserTemp>,
    @InjectModel(EndpointPermission.name)
    private endpointPermissionModel: Model<EndpointPermission>,
    @InjectModel(EndpointsRoles.name)
    private endpointRolesModel: Model<EndpointsRoles>,
    @InjectModel(Roles.name) private rolesModel: Model<Roles>,
    @InjectModel(BusinessUnit.name)
    private businessUnitModel: Model<BusinessUnit>,
    @Inject(forwardRef(() => VendorInviteService))
    private vendorInviteService: VendorInviteService,
    private eventEmitter: EventEmitter2,
    private statusService: StatusService,
    private businessUnitService: BusinessUnitService,
    private readonly notificationsService: NotificationsService,
  ) {}

  // Add getter methods to access these values
  private get HOURS_TO_VERIFY(): number {
    return this.configService.get<number>('HOURS_TO_VERIFY') || 24;
  }

  private get HOURS_TO_BLOCK(): number {
    return this.configService.get<number>('HOURS_TO_BLOCK') || 8;
  }

  private get LOGIN_ATTEMPTS_TO_BLOCK(): number {
    return this.configService.get<number>('LOGIN_ATTEMPTS_TO_BLOCK') || 5;
  }

  // Getter for userTempModel
  getUserTempModel(): Model<UserTemp> {
    return this.userTempModel;
  }

  // Getter for basicUserModel
  getBasicUserModel(): Model<BasicUser> {
    return this.basicUserModel;
  }

  generateOTP(length: number): string {
    const digits = '0123456789';
    let OTP = '';
    for (let i = 0; i < length; i++) {
      OTP += digits[Math.floor(Math.random() * 10)];
    }
    return OTP;
  }

  async createVendor(createUserDto: CreateUserDto, isTemp: boolean = false) {
    const userModel = isTemp ? this.userTempModel : this.basicUserModel;

    // Check for existing user in both temporary and main collections
    const isThere = await Promise.all([
      this.basicUserModel
        .findOne({ email: createUserDto.email, isDeleted: false })
        .exec(),
      this.userTempModel
        .findOne({ email: createUserDto.email, isDeleted: false })
        .exec(),
    ]);

    if (isThere[0] || isThere[1]) {
      throw new BadRequestException(
        `The user with email "${createUserDto.email}" already exists.`,
      );
    }

    // const otpCode = this.generateOTP(6);

    const defaultStatus: any = await this.getDefaultStatus(
      StatusConfigType.USER,
    );

    //Determine verification state based on temporary status
    const verificationState = isTemp
      ? {
          isVerified: false,
          verified: null,
          verificationExpires: addMinutes(new Date(), 10),
        }
      : { isVerified: true, verified: new Date(), verificationExpires: null };

    const hashedPassword = await bcrypt.hash(createUserDto.password, 10);

    // Create the user instance with proper data
    const createdUser = new userModel({
      ...createUserDto, // password will be hashed automatically if 'password' field is modified
      password: hashedPassword,
      customStatus: defaultStatus,
      ...verificationState,
    });

    try {
      const savedUser = await createdUser.save();

      await this.vendorInviteService.removeInviteIfUserRegistered(
        savedUser.email,
        userModel === this.userTempModel,
        savedUser?.companyId?.toString(),
      );

      // if (savedUser.isVerified) {
      //   this.emitEvent('user.verify.email.confirm', savedUser);
      // } else {
      //   this.emitEvent('user.verify.email', savedUser);
      // }

      // Sanitize the user object to exclude sensitive fields
      const sanitizedUser = omit(savedUser.toObject(), [
        'password',
        'verification',
        'verificationExpires',
        '__v',
        'otpCode',
      ]);

      return sanitizedUser;
    } catch (error) {
      this.logger.error(`Error creating vendor user: ${error.message}`);
      throw new InternalServerErrorException(
        `Failed to create vendor user: ${error.message}`,
      );
    }
  }

  async updateUserOrg(
    orgId: Types.ObjectId,
    userId: Types.ObjectId,
  ): Promise<BasicUserDocument> {
    try {
      // Determine which model to use
      const isTemp = await this.userTempModel.findById(userId).exec();

      // Use appropriate model and type casting
      const updatedUser = isTemp
        ? ((await this.userTempModel
            .findByIdAndUpdate(userId, { org: orgId }, { new: true })
            .exec()) as BasicUserDocument)
        : await this.basicUserModel
            .findByIdAndUpdate(userId, { org: orgId }, { new: true })
            .populate({
              path: 'customStatus',
              select: '_id name',
              model: 'Status',
            })
            .populate({
              path: 'org',
              select: '_id title orgType description',
              model: 'Org',
            })
            .populate({ path: 'country', select: '_id countryName' })
            .populate({ path: 'state', select: '_id stateName' })
            .populate({ path: 'city', select: '_id name' })
            .populate({
              path: 'contactDetails',
              match: { _id: { $ne: null } },
              select: '_id contactEmail contactNumber',
            })
            .populate({
              path: 'userInboxConfig',
              select:
                '_id userName password imapHost imapPort smtpHost smtpPort fromEmail fromName',
              model: 'UserInboxConfig',
            })
            .select('-password -verification -verificationExpires')
            .exec();
      if (!updatedUser || updatedUser.isDeleted) {
        throw new NotFoundException(`User not found with ID ${userId}`);
      }

      return updatedUser;
    } catch (error) {
      this.logger.error(`Error updating user org: ${error.message}`);
      throw new InternalServerErrorException(
        `Failed to update user org: ${error.message}`,
      );
    }
  }

  // async createVendor(createUserDto: CreateUserDto, isTemp: boolean = false) {
  //   const userModel = isTemp ? this.userTempModel : this.basicUserModel;

  //   // Check for existing user in both temporary and main collections
  //   const isThere = await Promise.all([
  //     this.basicUserModel.findOne({ email: createUserDto.email, isDeleted: false }).exec(),
  //     this.userTempModel.findOne({ email: createUserDto.email, isDeleted: false }).exec(),
  //   ]);

  //   if (isThere[0] || isThere[1]) {
  //     throw new BadRequestException(
  //       `The user with email "${createUserDto.email}" already exists.`
  //     );
  //   }

  //   const otpCode = this.generateOTP(6); // Generate OTP

  //   const defaultStatus: any = await this.getDefaultStatus(StatusConfigType.USER);

  //   // Create the user instance without the verificationState, but manually add otpCode
  //   const createdUser = new userModel({
  //     ...createUserDto, // password will be hashed automatically if 'password' field is modified
  //     customStatus: defaultStatus,
  //     otpCode, // Add OTP code here
  //     verificationExpires: addMinutes(new Date(), 10), // Set token expiration
  //     isVerified: false, // Make sure isVerified is false for temporary users
  //   });

  //   try {
  //     const savedUser = await createdUser.save();
  //     console.log("Saved user:", savedUser);

  //       if (savedUser.isVerified) {
  //         this.emitEvent('user.verify.email.confirm', savedUser);
  //         console.log('Emitted event: user.verify.email.confirm');
  //       } else {
  //         this.emitEvent('user.verify.email', savedUser); // This should trigger email with OTP
  //         console.log('Emitted event: user.verify.email');
  //       }

  //     // Sanitize the user object to exclude sensitive fields
  //     const sanitizedUser = omit(savedUser.toObject(), [
  //       'password',
  //       'verification',
  //       'verificationExpires',
  //       '__v',
  //       'otpCode',
  //     ]);

  //     console.log("Sanitized user:", sanitizedUser);

  //     return sanitizedUser;
  //   } catch (error) {
  //     this.logger.error(`Error creating vendor user: ${error.message}`);
  //     throw new InternalServerErrorException(
  //       `Failed to create vendor user: ${error.message}`
  //     );
  //   }
  // }

  async moveUserToMainCollection(userId: Types.ObjectId): Promise<BasicUser> {
    try {
      const tempUser = await this.userTempModel.findById(userId).exec();
      if (!tempUser) {
        throw new BadRequestException(
          `User with ID ${userId} not found in temporary users.`,
        );
      }

      // Fetch vendorInvite if companyId is missing in tempUser
      let { companyId } = tempUser;

      const {
        firstName,
        lastName,
        email,
        password,
        roles,
        customStatus,
        otpCode,
        org,
      } = tempUser;

      const mainUser = new this.basicUserModel({
        firstName,
        lastName,
        email,
        password, // Keep the hashed password
        roles,
        customStatus,
        otpCode,
        org,
        companyId, // Ensure companyId is moved
        isVerified: true,
        verified: true,
        verificationExpires: null,
      });

      const savedUser = await mainUser.save();

      await this.userTempModel.findByIdAndDelete(userId).exec();

      // this.emitEvent('user.verify.email.confirm', savedUser);

      // Sanitize only for API responses, but do not omit the password here
      const sanitizedUser = omit(savedUser.toObject(), [
        'verification',
        'verificationExpires',
        '__v',
        'otpCode',
      ]);

      return sanitizedUser as BasicUser;
    } catch (error) {
      this.logger.error(
        `Error moving user to main collection: ${error.message}`,
      );
      throw new InternalServerErrorException(
        `Failed to move user to main collection: ${error.message}`,
      );
    }
  }

  async verifyOTP(email: string, otpCode: string): Promise<boolean> {
    try {
      // First check temp users
      let user = await this.userTempModel
        .findOne({
          email,
          otpCode,
          isDeleted: false,
          verificationExpires: { $gt: new Date() },
        })
        .exec();

      if (!user) {
        // Then check main users
        user = await this.basicUserModel
          .findOne({
            email,
            otpCode,
            isDeleted: false,
            verificationExpires: { $gt: new Date() },
          })
          .exec();
      }

      if (!user) {
        throw new BadRequestException('Invalid or expired OTP code');
      }

      // Update user verification status
      user.isVerified = true;
      user.verified = true;
      await user.save();

      return true;
    } catch (error) {
      this.logger.error(`Error verifying OTP: ${error.message}`);
      throw new BadRequestException(`Failed to verify OTP: ${error.message}`);
    }
  }

  async resendOTP(email: string): Promise<void> {
    try {
      // First, check in the temporary users collection
      let user = await this.userTempModel
        .findOne({ email, isDeleted: false })
        .exec();

      if (!user) {
        // If not found in temporary users, check in the main users collection
        user = await this.basicUserModel
          .findOne({ email, isDeleted: false })
          .exec();
      }

      // If the user is not found in either collection
      if (!user) {
        throw new NotFoundException('User not found or has been deleted.');
      }
      // Generate a new OTP
      const newOtpCode = Math.floor(100000 + Math.random() * 900000).toString(); // 6-digit random OTP
      const verificationExpires = new Date();
      verificationExpires.setMinutes(verificationExpires.getMinutes() + 10); // OTP valid for 10 minutes

      // Update user with new OTP and expiration time
      user.otpCode = newOtpCode;
      user.verificationExpires = verificationExpires;
      await user.save();

      // Emit event after OTP has been resent
      this.emitEvent('user.resend.otp', user); // Emitting the event here

      // Log the OTP generation (you might send it via email in production)
      this.logger.debug(`Resent OTP to ${email}: ${newOtpCode}`);

      // Simulate sending OTP for testing purposes
      console.log(
        `Your OTP code is ${newOtpCode}. It is valid for 10 minutes.`,
      );
    } catch (error) {
      this.logger.error(`Error resending OTP: ${error.message}`);
      throw new BadRequestException(`Failed to resend OTP: ${error.message}`);
    }
  }

  // Add this method to track if a user is verified
  async isUserVerified(email: string): Promise<boolean> {
    try {
      // Check in temp collection
      let user = await this.userTempModel
        .findOne({ email, isDeleted: false })
        .exec();

      if (!user) {
        // Check in main collection
        user = await this.basicUserModel
          .findOne({ email, isDeleted: false })
          .exec();
      }

      return user?.isVerified || false;
    } catch (error) {
      this.logger.error(
        `Error checking user verification status: ${error.message}`,
      );
      return false;
    }
  }

  async create(createUserDto: CreateUserDto) {
    const isThere = await this.findUserByEmailExists(createUserDto.email);
    if (isThere) {
      throw new BadRequestException(
        `The user with email "${createUserDto.email}" already exists.`,
      );
    }

    const otpCode = this.generateOTP(6);

    //TODO: need to be updated with role based access

    const defaultStatus: any = await this.getDefaultStatus(
      StatusConfigType.USER,
    );

    const hashedPassword = await bcrypt.hash(createUserDto.password, 10);

    const userData = omit(createUserDto, ['sendPassword']);

    const createdBasicUser = new this.basicUserModel({
      ...userData,
      password: hashedPassword,
      // verification: v4(),    // Generate a unique verification token
      otpCode,
      verificationExpires: addMinutes(new Date(), 10), // Set token expiration
      customStatus: defaultStatus,
    });

    const createdUser = await createdBasicUser.save();
    // this.emitEvent('user.verify.email', createdUser);

    if (createUserDto.sendPassword && createdUser.isVerified) {
      this.emitEvent('user.verify.email.confirm.password', createUserDto);
    } else if (createdUser.isVerified) {
      //if already verified by admin(e.g creted org and approved)
      this.emitEvent('user.verify.email.confirm', createdUser);
    } else {
      this.emitEvent('user.verify.email', createdUser);
    }

    const sanitizedUser = omit(createdUser.toObject(), [
      'password',
      'verification',
      'verificationExpires',
      '__v',
      'otpCode',
    ]);
    return sanitizedUser;
  }

  async createFreelancer(createUserDto: CreateFreelancerDto) {
    const isThere = await this.findUserByEmailExists(createUserDto.email);
    if (isThere) {
      throw new BadRequestException(
        `The user with email "${createUserDto.email}" already exists.`,
      );
    }

    const defaultStatus: any = await this.getDefaultStatus(
      StatusConfigType.USER,
    );

    const hashedPassword = await bcrypt.hash(createUserDto.password, 10);

    const createdBasicUser = new this.basicUserModel({
      ...createUserDto,
      password: hashedPassword,
      customStatus: defaultStatus,
      // verification: v4(),    // Generate a unique verification token
      // otpCode,
      // verificationExpires: addMinutes(new Date(), 10) // Set token expiration
    });

    const createdUser = await createdBasicUser.save();
    this.emitEvent('user.verify.email', createdUser);
    const sanitizedUser = omit(createdUser.toObject(), [
      'password',
      'verification',
      'verificationExpires',
      '__v',
      'otpCode',
    ]);
    return sanitizedUser;
  }

  async updateProfile(userId: Types.ObjectId, userProfileDto: UserProfileDto) {
    try {
      const user = await this.findById(userId);
      if (!user) {
        throw new NotFoundException(`User with ID ${userId} not found`);
      }

      const updatedUser = await this.basicUserModel
        .findByIdAndUpdate(userId, userProfileDto, { new: true })
        .populate({ path: 'country', select: '_id countryName' })
        .populate({ path: 'state', select: '_id stateName' })
        .populate({ path: 'city', select: '_id name' })
        .select('-password -verification -verificationExpires')
        .exec();

      return updatedUser;
    } catch (error) {
      throw new InternalServerErrorException(
        `Error updating user profile: ${error.message}`,
      );
    }
  }

  async findAll(
    page: number,
    limit: number,
    role?: Role,
    name?: string,
    orgType?: string,
  ) {
    try {
      const query: any = { isDeleted: false };
      if (role) {
        query.roles = role;
      }

      if (name) {
        const regex = new RegExp(name, 'i'); // 'i' for case-insensitive
        query.firstName = { $regex: regex };
      }
      if (orgType) {
        const orgs = await this.orgModel.find({ orgType }).select('_id').exec();
        const orgIds = orgs.map((org) => org._id.toString());
        query.org = { $in: orgIds };
      }
      return this.basicUserModel
        .find(query)
        .populate({
          path: 'org',
          select: '_id title orgType description',
          model: 'Org',
        })
        .populate({ path: 'customStatus', select: '_id name', model: 'Status' })
        .populate({
          path: 'logo',
          select: '_id locationUrl originalName fileSize fileType',
          model: 'FileMetadata',
        })
        .skip((page - 1) * limit)
        .limit(limit)
        .sort({ updatedAt: -1 })
        .select('-password -verification -verificationExpires')
        .exec();
    } catch (error) {
      throw new InternalServerErrorException(
        `Error while fetching all users: ${error?.message}`,
      );
    }
  }

  async findAllOrgUser(
    orgId: string,
    page: number,
    limit: number,
    role?: Role,
    name?: string,
    orgType?: string,
  ) {
    try {
      const query: any = { isDeleted: false, org: orgId };
      if (role) {
        query.roles = role;
      }

      if (name) {
        const regex = new RegExp(name, 'i'); // 'i' for case-insensitive
        query.firstName = { $regex: regex };
      }
      if (orgType) {
        const orgs = await this.orgModel.find({ orgType }).select('_id').exec();
        const orgIds = orgs.map((org) => org._id.toString());
        query.org = { $in: orgIds };
      }
      return this.basicUserModel
        .find(query)
        .populate({
          path: 'org',
          select: '_id title orgType description',
          model: 'Org',
        })
        .populate({ path: 'customStatus', select: '_id name', model: 'Status' })
        .populate({
          path: 'logo',
          select: '_id locationUrl originalName fileSize fileType',
          model: 'FileMetadata',
        })
        .skip((page - 1) * limit)
        .limit(limit)
        .sort({ updatedAt: -1 })
        .select('-password -verification -verificationExpires')
        .exec();
    } catch (error) {
      throw new InternalServerErrorException(
        `Error while fetching all users: ${error?.message}`,
      );
    }
  }

  findAllActiveUsers(page: number, limit: number, role?: string) {
    try {
      const query: any = {
        isDeleted: false,
        isSuspended: false,
        isVerified: true, // this is for email verification
      };

      if (role) {
        query['roles'] = role;
      }

      return this.basicUserModel
        .find(query)
        .skip((page - 1) * limit)
        .limit(limit)
        .select('-password -verification -verificationExpires')
        .exec();
    } catch (error) {
      throw new InternalServerErrorException(
        `Error while fetching all active users: ${error?.message}`,
      );
    }
  }

  async findAllOrg(
    page: number,
    limit: number,
    user: any,
    role?: Role,
    name?: string,
    orgType?: string,
  ) {
    try {
      const query: any = { isDeleted: false };

      if (user?.org?._id) {
        query.org = user.org._id; // Filter by logged-in user's organization
      }

      if (role) {
        query.roles = role;
      }

      if (name) {
        const regex = new RegExp(name, 'i'); // Case-insensitive search
        query.firstName = { $regex: regex };
      }

      if (orgType) {
        const orgs = await this.orgModel.find({ orgType }).select('_id').exec();
        const orgIds = orgs.map((org) => org._id.toString());
        query.org = { $in: orgIds };
      }

      return this.basicUserModel
        .find(query)
        .populate({
          path: 'org',
          select: '_id title orgType description',
          model: 'Org',
        })
        .populate({ path: 'customStatus', select: '_id name', model: 'Status' })
        .populate({
          path: 'logo',
          select: '_id locationUrl originalName fileSize fileType',
          model: 'FileMetadata',
        })
        .skip((page - 1) * limit)
        .limit(limit)
        .sort({ updatedAt: -1 })
        .select('-password -verification -verificationExpires')
        .exec();
    } catch (error) {
      throw new InternalServerErrorException(
        `Error while fetching all users: ${error?.message}`,
      );
    }
  }

  findAllSuspendedUsers(page: number, limit: number) {
    try {
      return this.basicUserModel
        .find({
          // isDeleted:false,
          // isBlocked:false,
          isSuspended: true,
        })
        .skip((page - 1) * limit)
        .limit(limit)
        .select('-password -verification -verificationExpires')
        .exec();
    } catch (error) {
      throw new InternalServerErrorException(
        `Error while fetching all suspended users: ${error?.message}`,
      );
    }
  }

  // Note - lets  not use isBlocked. use isSuspended
  // findAllBlockedUsers() {
  //   return this.basicUserModel.find({
  //     // isDeleted:false,
  //     // isBlocked: true, lets  not use isBlocked. use isSuspended
  //     isSuspended:true
  //   });
  // }

  async findUserByEmail(email: string) {
    const populateOptions = this.getPopulateOptions();
    try {
      const user = await this.basicUserModel
        .findOne({ email })
        .populate({
          path: 'logo',
          select: '_id locationUrl originalName fileSize fileType',
          model: 'FileMetadata',
        })
        .populate({ path: 'country', select: '_id countryName' })
        .populate({ path: 'state', select: '_id stateName' })
        .populate({ path: 'city', select: '_id name' })
        .populate(populateOptions)
        .exec();

      if (!user) {
        this.logger.debug(`No user found with email ${email}`);
        throw new NotFoundException(
          `User not found with the given email: ${email}`,
        );
      }
      // this.logger.debug(`User found with email ${email}`);
      // const sanitizedUser = omit(user, ['password', 'verification', 'verificationExpires', '__v']);
      return user; // Not sanitized because sign in endpoint requires password
    } catch (error) {
      this.logger.error(
        `Error occurred while searching for user: ${error.message}`,
      );
      throw error;
    }
  }

  async findAdminByOrgId(orgId: string) {
    const populateOptions = this.getPopulateOptions();
    try {
      const adminUser = await this.basicUserModel
        .findOne({ org: orgId, roles: { $in: [Role.Admin] }, isDeleted: false })
        .populate(populateOptions)
        .exec();

      if (!adminUser) {
        this.logger.log(`No admin found for org ID ${orgId}`);
        return null;
      }

      return adminUser;
    } catch (error) {
      this.logger.error(
        `Error occurred while searching for admin: ${error.message}`,
      );
      throw error;
    }
  }
  async findById(userId: Types.ObjectId): Promise<BasicUserDocument> {
    try {
      const user = await this.basicUserModel
        .findById(userId)
        .populate({ path: 'customStatus', select: '_id name', model: 'Status' })
        .populate({
          path: 'org',
          select: '_id title orgType description',
          model: 'Org',
        })
        .populate({ path: 'country', select: '_id countryName' })
        .populate({ path: 'state', select: '_id stateName' })
        .populate({ path: 'city', select: '_id name' })
        .populate({
          path: 'contactDetails',
          match: { _id: { $ne: null } }, // Skip null or invalid references
          select: '_id contactEmail contactNumber',
        })
        .populate({
          path: 'userInboxConfig',
          select:
            '_id userName password  imapHost imapPort  smtpHost smtpPort fromEmail fromName',
          model: 'UserInboxConfig',
        })
        .select('-password -verification -verificationExpires')
        .exec();
      // this.logger.debug(user);
      if (!user || user.isDeleted) {
        throw new NotFoundException(`User not found with ID ${userId}`);
      }
      return user;
    } catch (error) {
      // this.logger.error(error);
      // this.logger.error(`An error occurred while fetching a user by ID ${userId}. ${error?.message}`);
      // throw new UnprocessableEntityException(`May be invalid id format ${id}`);
      throw new NotFoundException(
        `An error occurred while fetching a user by ID ${userId}. ${error?.message}`,
      );
    }
  }

  // New findByIdAndUpdate method
  async findByIdAndUpdate(
    userId: Types.ObjectId,
    updateData: Partial<BasicUserDocument>,
    session?: any,
  ): Promise<BasicUserDocument | null> {
    try {
      const user = await this.basicUserModel
        .findByIdAndUpdate(userId, updateData, { new: true, session })
        .exec();

      if (!user) {
        throw new NotFoundException(`User not found with ID ${userId}`);
      }
      return user;
    } catch (error) {
      throw new NotFoundException(
        `Error updating user by ID ${userId}: ${error.message}`,
      );
    }
  }

  async getUsersByCustomStatus(
    status: Types.ObjectId,
    page: number,
    limit: number,
    role: string,
  ) {
    try {
      const users = await this.basicUserModel
        .find({ customStatus: status, roles: role })
        .populate({ path: 'customStatus', select: '_id name' })
        .skip((page - 1) * limit)
        .limit(limit)
        .exec();
      return users;
    } catch (error) {
      throw new InternalServerErrorException(
        `Error while fetching users  by status ID: ${error.message}`,
      );
    }
  }

  async getFreelancersCountByCustomStatus(): Promise<
    Array<{ id: string; name: string; count: number }>
  > {
    try {
      // Fetch all statuses for 'Freelancer' type
      const allStatuses =
        await this.statusService.findAllStatusByType('Freelancer');

      const statusMap: Record<
        string,
        { id: string; name: string; count: number }
      > = {};

      const result = await this.basicUserModel.aggregate([
        {
          $match: { roles: 'freelancer' }, // Match freelancers specifically
        },
        {
          $lookup: {
            from: 'status', // Join with Status collection
            localField: 'customStatus',
            foreignField: '_id',
            as: 'statusDetails',
          },
        },
        {
          $unwind: '$statusDetails', // Unwind the joined status details
        },
        {
          $group: {
            _id: {
              statusId: '$statusDetails._id', // Group by status id
            },
            statusName: { $first: '$statusDetails.name' },
            count: { $sum: 1 }, // Count the number of users for each status
          },
        },
        {
          $project: {
            _id: 0, // Exclude the _id field
            id: '$_id.statusId', // Include the status ID
            name: '$statusName', // Rename _id field to statusName
            count: 1, // Include the count field
          },
        },
      ]);

      result.forEach((entry) => {
        const key = `${entry.id}`;
        statusMap[key] = {
          id: entry.id,
          name: entry.name,
          count: (statusMap[key]?.count || 0) + entry.count,
        };
      });

      // Include all statuses that have no associated freelancers (count as 0)
      allStatuses.forEach((status) => {
        const key = status._id.toString();
        if (!statusMap[key]) {
          statusMap[key] = {
            id: status._id.toString(),
            name: status.name,
            count: 0, // Set count to 0 if no users exist for this status
          };
        }
      });

      // Convert the result to an array of objects
      const statusArray: Array<{ id: string; name: string; count: number }> =
        Object.values(statusMap);

      return statusArray;
    } catch (error) {
      throw new InternalServerErrorException(
        `Error while fetching freelancer counts by custom status: ${error.message}`,
      );
    }
  }

  async changeUserCustomStatus(
    userId: Types.ObjectId,
    changeStatusDto: ChangeCustomStatusDto,
    user: Object,
  ) {
    try {
      // 1. Find the existing freelancer by its ID
      const existingUser = await this.basicUserModel
        .findById(userId)
        .populate<{
          status: StatusDocument;
        }>({ path: 'customStatus', select: '_id name' });

      if (!existingUser) {
        throw new NotFoundException(`Freelancer with ID "${userId}" not found`);
      }

      const { customStatus, ...comment } = changeStatusDto;
      const statusObjId = new Types.ObjectId(customStatus); // Ensure status is an ObjectId

      // 2. Find the new status by its ID
      const newStatus = await this.statusService.findById(statusObjId);
      if (!newStatus) {
        throw new BadRequestException(
          `Status with ID "${newStatus}" not found`,
        );
      }

      const updatedUser = await this.basicUserModel
        .findOneAndUpdate(
          { _id: userId },
          { customStatus: statusObjId },
          { new: true },
        )
        .populate<{
          status: StatusDocument;
        }>({ path: 'customStatus', select: '_id name' });

      this.emitEvent('user.custom.status.changed', {
        existingUser,
        updatedUser,
        comment,
        user,
      });

      return updatedUser;
    } catch (error) {
      this.logger.error(
        `An error occurred while changing the status of Freelancer by ID ${userId}. ${error?.message}`,
      );
      // Re-throw specific exceptions
      if (
        error instanceof BadRequestException ||
        error instanceof NotFoundException ||
        error instanceof ConflictException
      ) {
        throw error;
      }
      // General error handling
      throw new InternalServerErrorException();
    }
  }

  async searchUsers(name: string, page: number, limit: number) {
    try {
      if (!name) {
        throw new HttpException(
          'Name parameter is required',
          HttpStatus.BAD_REQUEST,
        );
      }
      const regex = new RegExp(name, 'i'); // 'i' for case-insensitive
      return await this.basicUserModel
        .find({ firstName: { $regex: regex }, isDeleted: false })
        .skip((page - 1) * limit)
        .limit(limit)
        .exec();
    } catch (error) {
      // this.logger.error(`Error while searching for users: ${error.message}`, error.stack);
      throw new InternalServerErrorException(
        `Error while searching for users: ${error?.message}`,
      );
    }
  }

  async findAllUsersByOrgType(
    orgType: string,
    page: number,
    limit: number,
  ): Promise<BasicUserDocument[]> {
    try {
      const orgs = await this.orgModel.find({ orgType }).select('_id').exec();
      const orgIds = orgs.map((org) => org._id.toString());
      const users = await this.basicUserModel
        .find({
          org: { $in: orgIds },
          isDeleted: false,
          isSuspended: false,
          isVerified: true, // this is for email verification
        })
        .skip((page - 1) * limit)
        .limit(limit)
        .select('-password -verification -verificationExpires')
        .exec();
      return users;
    } catch (error) {
      throw new InternalServerErrorException(
        `An error occurred while fetching users: ${error?.message}.`,
      );
    }
  }

  async findUserByOrgAndBusinessUnit(queryDto: FilterUsersDto) {
    const { org, businessUnit, page, limit } = queryDto;
    // const populateOptions = this.getPopulateOptions();
    try {
      const query: any = {};

      if (org) {
        query.org = org;
      }

      if (businessUnit) {
        query.businessUnit = businessUnit;
      }

      const users = await this.basicUserModel
        .find(query)
        .populate({ path: 'org', select: '_id title description' })
        .populate({
          path: 'reportingTo',
          select: '_id firstName firstName lastName roles',
        })
        .populate({ path: 'businessUnit', select: '_id label org level type' })
        .skip((page - 1) * limit)
        .limit(limit)
        .exec();

      return users;
    } catch (error) {
      throw new InternalServerErrorException(
        `An error occurred while fetching users: ${error?.message}`,
      );
    }
  }

  async update(
    userId: Types.ObjectId,
    updateUserDto: UpdateUserDto,
    user: BasicUser,
  ) {
    try {
      const existingUser = await this.findById(userId);
      const sanitizedUpdateUserDto = omit(updateUserDto, ['password']);
      // this.logger.debug(sanitizedUpdateUserDto);
      const updatedUser = await this.basicUserModel
        .findByIdAndUpdate(existingUser._id, sanitizedUpdateUserDto, {
          new: true,
        })
        .select('-password -verification -verificationExpires')
        .populate({ path: 'country', select: '_id countryName' })
        .populate({ path: 'state', select: '_id stateName' })
        .populate({ path: 'city', select: '_id name' })
        .populate({
          path: 'logo',
          select: '_id locationUrl originalName fileSize fileType',
          model: 'FileMetadata',
        })
        .exec();

      if (!updatedUser) {
        this.logger.error(`Failed to update user with ID ${userId}`);
        throw new InternalServerErrorException(
          `Failed to update user with ID ${userId}`,
        );
      }

      if (existingUser.firstName !== updatedUser.firstName) {
        this.emitEvent('user.firstName.updated', {
          existingUser,
          updatedUser,
          user,
        });
      }

      if (existingUser.country !== updatedUser.country) {
        this.emitEvent('user.country.updated', {
          existingUser,
          updatedUser,
          user,
        });
      }

      if (existingUser.state !== updatedUser.state) {
        this.emitEvent('user.state.updated', {
          existingUser,
          updatedUser,
          user,
        });
      }

      if (existingUser.email !== updatedUser.email) {
        this.emitEvent('user.email.updated', {
          existingUser,
          updatedUser,
          user,
        });
      }

      const contactDetails = existingUser?.contactDetails ?? [];
      const updatedContactDetails = updatedUser?.contactDetails ?? [];

      if (contactDetails.length > 0) {
        if (
          contactDetails[0]?.contactNumber !==
          updatedContactDetails[0]?.contactNumber
        )
          this.emitEvent('org.contactNumber.updated', {
            existingUser,
            updatedUser,
            user,
          });
      }

      return updatedUser;
    } catch (error) {
      // this.logger.error(error);
      // this.logger.error(`An error occurred while updating user by ID ${userId}. ${error?.message}`);
      throw new BadRequestException(
        `An error occurred while updating a user by ID ${userId}. ${error?.message}`,
      );
    }
  }

  async updateRoles(userId: Types.ObjectId, rolesDto: RolesDto) {
    try {
      const user = await this.findById(userId);
      const updatedUser = await this.basicUserModel
        .findByIdAndUpdate(user._id, { roles: rolesDto.roles })
        .select('-password -verification -verificationExpires')
        .exec();
      return updatedUser;
    } catch (error) {
      // this.logger.error(`An error occurred while updating roles of user by ID ${userId}. ${error?.message}`);
      throw new InternalServerErrorException(
        `An error occurred while updating roles of user by ID ${userId}. ${error?.message}`,
      );
    }
  }

  async verifyEmail(verifyUuidDto: VerifyUuidDto, userId?: any) {
    // TODO:: use try / catch
    try {
      this.logger.debug('Verifying details:', verifyUuidDto, userId);

      const user = await this.findUserByVerificationCode(
        verifyUuidDto.verification,
      );
      this.logger.debug(
        `Found user by verification code: ${user.firstName}, ${user.email}`,
      ); // TODO:: DONT LOG  ENTIRE USER OBJECT
      if (!user) {
        // TODO:: SEND ANOTHER EMAIL VERIFICATION EMAIL BY EVENTS???
        // DOUBT:: To send verification email by event we need user data. how do we get user data ???
        return {
          message: 'Invalid or expired verification token.',
          done: false,
        };
      }

      if (user.isVerified) {
        return {
          message: 'Email verification is already completed.',
          done: true,
          firstName: user.firstName,
        };
      } else {
        await this.setUserAsVerified(user);
        // this.emailerService.sendEmailVerificationSuccess(user);
        this.emitEvent('user.verify.email.confirm', user);
        return {
          done: true,
          message: 'Email verification is completed.',
          firstName:
            `${user?.firstName}` || `${user?.firstName} ${user?.lastName}`,
          email: user.email,
          access_token: await this.authService.createAccessToken({
            userId: userId,
            email: user.email,
            org: user.org,
          }),
          // refreshToken: await this.authService.createRefreshToken(user._id),
        };
      }
      // const user = await this.findByVerification(verifyUuidDto.verification);
    } catch (error) {
      throw new HttpException(
        `Failed to find user by verification code. ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async verifyEmailByOtp(verifyOtpDto: VerifyOtpDto, userId?: any) {
    // TODO:: use try / catch
    try {
      this.logger.log('Verifying details:', verifyOtpDto, userId);

      const user = await this.findUserByOtpVerificationCode(
        verifyOtpDto.verification,
      );
      this.logger.log(
        `Found user by verification code: ${user.firstName}, ${user.email}`,
      ); // TODO:: DONT LOG  ENTIRE USER OBJECT
      if (!user) {
        // TODO:: SEND ANOTHER EMAIL VERIFICATION EMAIL BY EVENTS???
        // DOUBT:: To send verification email by event we need user data. how do we get user data ???
        // return {
        //   message: 'Invalid or expired otp code.',
        //   done: false
        // };
        throw new BadRequestException('Invalid or expired OTP code.');
      }

      if (user.isVerified) {
        return {
          message: 'Email verification is already completed.',
          done: true,
          firstName: user.firstName,
        };
      } else {
        await this.setUserAsVerified(user);
        // this.emailerService.sendEmailVerificationSuccess(user);
        this.emitEvent('user.verify.email.confirm', user);
        return {
          done: true,
          message: 'Email verification is completed.',
          firstName:
            `${user?.firstName}` || `${user?.firstName} ${user?.lastName}`,
          _id: user._id,
          email: user.email,
          access_token: await this.authService.createAccessToken({
            _id: user._id,
            email: user.email,
            org: user.org,
            roles: user.roles,
          }),
          // refreshToken: await this.authService.createRefreshToken(user._id),
        };
      }
      // const user = await this.findByVerification(verifyUuidDto.verification);
    } catch (error) {
      // throw new HttpException(
      //   `Failed to find user by verification code. ${error.message}`,
      //   HttpStatus.INTERNAL_SERVER_ERROR
      // );
      if (!(error instanceof BadRequestException)) {
        throw new HttpException(
          `Failed to verify email. ${error.message}`,
          HttpStatus.INTERNAL_SERVER_ERROR,
        );
      }
      throw error;
    }
  }

  private async setUserAsVerified(user: any) {
    // USE TRY / CATCH
    try {
      user.verified = true;
      user.isVerified = true;
      await user.save();
    } catch (error) {
      throw new HttpException(
        `Failed to set user as verified. ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  private async findUserByVerificationCode(
    verification: string,
  ): Promise<BasicUserDocument> {
    const user = await this.basicUserModel
      .findOne({
        verification,
        // verified: false,
        verificationExpires: { $gt: new Date() },
      })
      .select('-password -verification -verificationExpires')
      .exec();
    this.logger.debug(
      `Found user by verification code: ${user?.firstName}, ${user?.email}`,
    ); // TODO:: DONT LOG  ENTIRE USER OBJECT
    if (!user) {
      throw new BadRequestException('Invalid Token.');
    }
    return user;
  }

  private async findUserByOtpVerificationCode(
    verification: string,
  ): Promise<BasicUserDocument> {
    const user = await this.basicUserModel
      .findOne({
        otpCode: verification,
        // verified: false,
        verificationExpires: { $gt: new Date() },
      })
      .select('-password -verification -verificationExpires')
      .exec();
    this.logger.debug(
      `Found user by otp verification code: ${user?.firstName}, ${user?.email}`,
    ); // TODO:: DONT LOG  ENTIRE USER OBJECT
    if (!user) {
      throw new BadRequestException('Invalid Otp.');
    }
    return user;
  }

  async updatePassword(id: Types.ObjectId, passwordDto: PasswordDto) {
    try {
      const user = await this.findById(id);
      if (!user || user.isDeleted) {
        throw new NotFoundException(`User not found with ID ${id}`);
      }
      user.password = await bcrypt.hash(passwordDto.password, 10);
      await user?.save();
      this.emitEvent('user.password.change', user);
      return 'Password is updated successfully';
    } catch (error) {
      throw new HttpException(
        `Error while updating password ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async suspendUser(userId: Types.ObjectId, commentDto: CommentDto) {
    try {
      const user = await this.findById(userId);
      user.isSuspended = true;
      await user.save();

      const createdComment = new this.commentModel(commentDto);
      createdComment.user = userId;
      await createdComment.save();

      // this.logger.debug(user)
      return user;
    } catch (error) {
      throw new HttpException(
        `Error while suspending user by ID ${userId}. ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async rejectUser(userId: Types.ObjectId) {
    try {
      const user = await this.findById(userId);
      user.isRejected = true;
      await user.save();
      return user;
    } catch (error) {
      throw new HttpException(
        `Error while rejecting user by ID ${userId}. ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async approveUser(userId: Types.ObjectId) {
    try {
      const user = await this.findById(userId);
      user.isApproved = true;
      await user.save();
      return user;
    } catch (error) {
      // this.logger.error(error);
      // this.logger.error(`An error occurred while approving user by ID ${userId}. ${error?.message}`);
      throw new HttpException(
        `Error while approving user by ID ${userId}. ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async handleForgotPasswordRequest(forgotPasswordDto: ForgotPasswordDto) {
    //TODO: Will be implemented after the emailer service is implemented - DONE
    // throw new Error('Method not implemented.');
    try {
      const user: any = await this.findUserByEmail(forgotPasswordDto.email);
      console.log('user', user);
      this.logger.log('user found');
      if (!user?.isVerified) {
        // TODO:: SEND ANOTHER EMAIL VERIFICATION EMAIL. USE EVENTS.
        this.logger.debug(
          `User with email ${user?.email} is not verified yet.`,
        );
        this.resendVerificationTokenEmail(forgotPasswordDto.email);
        throw new UnauthorizedException(`User email is not verified.`);
      }
      this.logger.log('user verified');
      this.logger.log(user);
      const forgotPasswordRecord = await this.saveForgotPassword(
        forgotPasswordDto,
        user?.firstName,
      );
      // await this.emailerService.sendEmailForForgotPassword(forgotPasswordRecord);
      this.logger.log('user verified twice');
      return {
        email: forgotPasswordDto.email,
        message: 'Reset password email sent.',
      };
    } catch (error) {
      this.logger.debug(error?.message);
      throw new InternalServerErrorException(
        `Error handling forgot password request: ${error.message}`,
      );
    }
  }

  private async saveForgotPassword(
    createForgotPasswordDto: ForgotPasswordDto,
    userName?: string,
  ) {
    try {
      let forgotPassword = await this.forgotPasswordModel.findOne({
        email: createForgotPasswordDto.email,
      });

      const otpCode = this.generateOTP(6);

      if (forgotPassword) {
        // Update existing record
        // forgotPassword.verification = v4();
        forgotPassword.otpCode = otpCode;
        forgotPassword.expires = addHours(
          new Date(),
          Number(this.HOURS_TO_VERIFY),
        );
        forgotPassword.firstUsed = false;
        forgotPassword.finalUsed = false;
      } else {
        // Create new record
        forgotPassword = new this.forgotPasswordModel({
          email: createForgotPasswordDto.email,
          firstName: userName,
          // verification: v4(),
          otpCode,
          expires: addHours(new Date(), Number(this.HOURS_TO_VERIFY)),
          // ip: this.authService.getIp(req),
          // browser: this.authService.getBrowserInfo(req),
          // country: this.authService.getCountry(req),
        });
      }

      this.logger.log('new record created');

      const forgotPasswordRecord = await forgotPassword.save();
      this.emitEvent('user.forgot.password', forgotPasswordRecord);
      return forgotPasswordRecord;
    } catch (error) {
      this.logger.error(error);
      this.logger.error(
        `Failed to save forgot password record for email ${createForgotPasswordDto.email}`,
        error,
      );
      throw new InternalServerErrorException(
        `Unable to save forgot password record: ${error.message}`,
      );
    }
  }

  async forgotPasswordVerify(verifyUuidDto: VerifyUuidDto) {
    // console.log(verifyUuidDto);
    const forgotPassword = await this.findForgotPasswordByUuid(verifyUuidDto);

    await this.setForgotPasswordFirstUsed(forgotPassword);
    // this.emitEvent('forgot.password.done', forgotPassword);
    this.emitEvent('user.forgot.password.done', forgotPassword);
    return { email: forgotPassword.email, message: 'Now reset your password.' };
  }

  async forgotPasswordVerifyByOtp(verifyOtpDto: VerifyOtpDto) {
    // console.log(verifyUuidDto);
    const forgotPassword = await this.findForgotPasswordByOtp(verifyOtpDto);

    await this.setForgotPasswordFirstUsed(forgotPassword);
    // this.emitEvent('forgot.password.done', forgotPassword);
    this.emitEvent('user.forgot.password.done', forgotPassword);
    return { email: forgotPassword.email, message: 'Now reset your password.' };
  }

  private async setForgotPasswordFirstUsed(
    forgotPassword: ForgotPasswordDocument,
  ) {
    forgotPassword.firstUsed = true;
    // forgotPassword.ipChanged = this.authService.getIp(req);
    // forgotPassword.browserChanged = this.authService.getBrowserInfo(req);
    // forgotPassword.countryChanged = this.authService.getCountry(req);
    await forgotPassword.save();
  }

  async resetPassword(resetPasswordDto: ResetPasswordDto) {
    this.logger.log('userService');
    const forgotPassword =
      await this.findForgotPasswordByEmail(resetPasswordDto);
    this.logger.log('forgotPassword found', forgotPassword);
    await this.setForgotPasswordFinalUsed(forgotPassword);
    const savedUser = await this.resetUserPassword(resetPasswordDto);
    this.logger.log('savedUser', savedUser);
    // this.emailerService.sendEmailForPasswordUpdated(savedUser);
    this.emitEvent('user.password.reset', savedUser);
    return {
      email: resetPasswordDto.email,
      message: 'Password successfully changed.',
    };
  }

  private async resetUserPassword(resetPasswordDto: ResetPasswordDto) {
    const user = await this.basicUserModel
      .findOne({ email: resetPasswordDto.email, verified: true })
      .select('-password -verification -verificationExpires')
      .exec();
    if (!user) {
      throw new NotFoundException(
        `User not found with email ${resetPasswordDto.email}`,
      );
    }
    const hashedPassword = await bcrypt.hash(resetPasswordDto.password, 10);
    user.password = hashedPassword;
    user.markModified('password');
    await user.save({ validateModifiedOnly: true });
    return user;
  }

  private async findForgotPasswordByEmail(
    resetPasswordDto: ResetPasswordDto,
  ): Promise<ForgotPasswordDocument> {
    const forgotPassword = await this.forgotPasswordModel.findOne({
      email: resetPasswordDto.email,
      firstUsed: true,
      finalUsed: false,
      expires: { $gt: new Date() },
    });
    if (!forgotPassword) {
      throw new BadRequestException('Bad request.');
    }
    return forgotPassword;
  }

  private async setForgotPasswordFinalUsed(
    forgotPassword: ForgotPasswordDocument,
  ) {
    forgotPassword.finalUsed = true;
    await forgotPassword.save();
  }

  private async findForgotPasswordByUuid(
    verifyUuidDto: VerifyUuidDto,
  ): Promise<ForgotPasswordDocument> {
    // console.log(verifyUuidDto);

    const forgotPassword = await this.forgotPasswordModel.findOne({
      verification: verifyUuidDto.verification,
      firstUsed: false,
      finalUsed: false,
      expires: { $gt: new Date() },
    });
    if (!forgotPassword) {
      //TODO:: GENEREATE ANOTHER TOKEN??? USE EVENTS
      throw new BadRequestException('Invalid token.');
    }
    return forgotPassword;
  }

  private async findForgotPasswordByOtp(
    verifyOtpDto: VerifyOtpDto,
  ): Promise<ForgotPasswordDocument> {
    // console.log(verifyOtpDto);

    const forgotPassword = await this.forgotPasswordModel.findOne({
      // verification: verifyUuidDto.verification,
      email: verifyOtpDto.email,
      otpCode: verifyOtpDto.verification,
      firstUsed: false,
      finalUsed: false,
      expires: { $gt: new Date() },
    });
    if (!forgotPassword) {
      //TODO:: GENEREATE ANOTHER TOKEN??? USE EVENTS
      throw new BadRequestException('Invalid token.');
    }
    return forgotPassword;
  }

  async restoreSoftDeletedUser(userId: Types.ObjectId) {
    try {
      const user = await this.basicUserModel.findById(userId);
      if (!user) {
        throw new NotFoundException(
          `The user with ID: "${userId}" doesn't exist.`,
        );
      }
      if (!user.isDeleted) {
        throw new BadRequestException(
          `The user with ID: "${userId}" is not soft deleted.`,
        );
      }
      user.isDeleted = false;
      await user.save();
      return user;
    } catch (error) {
      // this.logger.error(`An error occurred while restoring User by ID ${userId}. ${error?.message}`);
      throw new InternalServerErrorException(
        `An error occurred in restoring user by Id ${userId}: ${error?.message}`,
      );
    }
  }

  async hardDelete(userId: Types.ObjectId) {
    try {
      await this.findById(userId);
      return await this.basicUserModel.findByIdAndDelete(userId);
    } catch (error) {
      // this.logger.error(error);
      this.logger.error(
        `An error occurred while hard deleting user by ID ${userId}. ${error?.message}`,
      );
      throw new InternalServerErrorException(
        `An error occurred while hard deleting user by ID ${userId}. ${error?.message}`,
      );
    }
  }

  async softDelete(userId: Types.ObjectId, commentDto: CommentDto) {
    try {
      const user = await this.findById(userId);
      user.isDeleted = true;
      await user.save();

      const createdComment = new this.commentModel(commentDto);
      createdComment.user = userId;
      await createdComment.save();

      return user;
    } catch (error) {
      this.logger.error(error);
      this.logger.error(
        `An error occurred while soft deleting user by ID ${userId}. ${error?.message}`,
      );
      throw new InternalServerErrorException(
        `An error occurred while soft deleting user by ID ${userId}. ${error?.message}`,
      );
    }
  }

  async softDeleteRecruiter(
    userId: Types.ObjectId,
    createDeleteUserDto: CreateDeleteUserDto,
  ) {
    try {
      const existingRequest = await this.deleteUserModel.findOne({ userId });
      if (existingRequest) {
        throw new BadRequestException(
          'A delete request for this user already exists.',
        );
      }

      // console.log("existingRequest", existingRequest)

      const user = await this.findById(userId);
      user.isRequested = true;
      user.isPending = true;
      await user.save();

      const rolesArray =
        Array.isArray(createDeleteUserDto.roles) &&
        createDeleteUserDto.roles.length > 0
          ? createDeleteUserDto.roles
          : [Role.User]; // Fallback to default role
      // console.log("rolesArray", rolesArray)
      // Create entry in delete-user collection
      const deleteRequest = new this.deleteUserModel({
        userId: createDeleteUserDto.userId || user._id, // ✅ Ensure userId is included
        reason: createDeleteUserDto.reason || 'No reason provided', // ✅ Ensure reason is included
        roles: rolesArray || 'user', // ✅ Ensure userId is included
        org:
          user.org instanceof Types.ObjectId
            ? user.org
            : (user.org as any)?._id || null,
        status: createDeleteUserDto.status || 'PENDING', // ✅ Use DTO to validate status
      });
      await deleteRequest.save();

      return user;
    } catch (error) {
      this.logger.error(error);
      this.logger.error(
        `An error occurred while requesting to  deleting user by ID ${userId}. ${error?.message}`,
      );
      throw new BadRequestException(
        `An error occurred while requesting  deleting user by ID ${userId}. ${error?.message}`,
      );
    }
  }

  async removeAll() {
    try {
      const usersToDelete = await this.basicUserModel.find();
      if (!usersToDelete.length) {
        this.logger.log('No users found to delete.');
        throw new NotFoundException('No users found to delete.');
      }
      return await this.basicUserModel.deleteMany();
    } catch (error) {
      this.logger.error(`Error while deleting all users. ${error?.message}`);
      throw new InternalServerErrorException(
        'Error while hard deleting all users',
      );
    }
  }

  async findUserByEmailExists(email: string): Promise<BasicUser | null> {
    try {
      // Check in both temporary and main user collections
      const existingMainUser = await this.basicUserModel
        .findOne({ email, isDeleted: false })
        .exec();

      const existingTempUser = await this.userTempModel
        .findOne({ email, isDeleted: false })
        .exec();

      // Return null if no existing user is found in either collection
      return existingMainUser || existingTempUser || null;
    } catch (error) {
      this.logger.error(`Error checking user existence: ${error.message}`);
      throw new InternalServerErrorException(
        `Error checking user existence: ${error.message}`,
      );
    }
  }

  async getRecruitersByOrg(
    orgId: string,
    page: number,
    limit: number,
    name?: string,
  ) {
    const query: any = {
      org: orgId,
      roles: { $in: [Role.Recruiter, Role.TeamLead] },
      isDeleted: false,
    };

    if (name) {
      query.$or = [
        { firstName: { $regex: name, $options: 'i' } },
        { lastName: { $regex: name, $options: 'i' } },
      ];
    }

    return this.basicUserModel
      .find(query)
      .populate({ path: 'org', select: '_id title description' })
      .populate({
        path: 'reportingTo',
        select: '_id firstName firstName lastName roles',
      })
      .populate({ path: 'businessUnit', select: '_id label org level type' })
      .populate({
        path: 'logo',
        select: '_id locationUrl originalName fileSize fileType',
        model: 'FileMetadata',
      })
      .skip((page - 1) * limit)
      .limit(limit)
      .select(
        '_id email firstName lastName roles org businessUnit source reportingTo contactDetails logo',
      )
      .exec();
  }

  async countRecruiters(orgId?: string): Promise<Object> {
    if (!orgId) {
      throw new BadRequestException('Organization ID is required');
    }

    const query: any = {
      org: orgId,
      roles: { $in: [Role.Recruiter] },
      isDeleted: false,
    };

    const recruitersCount = await this.basicUserModel
      .countDocuments(query)
      .exec();

    return { count: recruitersCount };
  }

  // async assignOrg(orgId: Types.ObjectId, userId: Types.ObjectId, orgType: string): Promise<any> {
  //   try {
  //     // Find the user
  //     const user = await this.findById(userId);

  //     const organization: any = await this.orgModel.findById(orgId);

  //     if (!organization) {
  //       throw new NotFoundException(`Organization with ID ${orgId} not found.`);
  //     }

  //     user.org = organization;

  //     // Handle roles based on org type
  //     switch (orgType) {
  //       case OrgType.ADMIN_ORG:
  //         if (!user.roles?.includes(Role.RootOrgAdmin)) {
  //           user.roles?.push(Role.RootOrgAdmin);
  //         }
  //         break;
  //       case OrgType.AGENCY_ORG:
  //         if (!user.roles?.includes(Role.AgencyOrgAdmin)) {
  //           user.roles?.push(Role.AgencyOrgAdmin);
  //         }
  //         break;
  //       case OrgType.CUSTOMER_ORG:
  //         if (!user.roles?.includes(Role.CustomerOrgAdmin)) {
  //           user.roles?.push(Role.CustomerOrgAdmin);
  //         }
  //         break;
  //       // case OrgType.ACCOUNT_ORG:
  //       //   if (!user.roles?.includes(Role.AccountAdmin)) {
  //       //     user.roles?.push(Role.AccountAdmin);
  //       //   }
  //       //   break;
  //       default:
  //         throw new BadRequestException(`Invalid organization type: ${orgType}`);
  //     }

  //     await user.save();

  //     return user.populate({ path: 'org', select: '_id title orgType description', model: 'Org' });

  //   } catch (error) {
  //     this.logger.error(`Error in assigning org to  the user. ${error?.message}`);
  //     throw new BadRequestException(`Error in assigning org to the user ${userId}. ${error?.message}`);
  //   }
  // }

  async getTeamMembersOfLead(
    orgId: Types.ObjectId,
    businessUnitId: string,
    leadId: string,
  ) {
    try {
      const orgIdStr = orgId.toString();
      const businessUnitIdStr = businessUnitId.toString();

      const query: any = {
        org: orgIdStr,
        businessUnit: businessUnitIdStr,
        isDeleted: false,
        reportingTo: leadId,
      };

      console.log(query);
      const teamMembers = await this.basicUserModel
        .find(query)
        .populate({
          path: 'reportingTo',
          select: '_id firstName lastName firstName',
          model: 'BasicUser',
        })
        .populate({
          path: 'businessUnit',
          select: '_id key label type breadcrumb',
          model: 'BusinessUnit',
        })
        .exec();
      console.log(teamMembers);
      return teamMembers;
    } catch (error) {
      throw new InternalServerErrorException(
        `An error occurred while retrieving members from org with ID ${orgId} and business unit ID ${businessUnitId}: ${error?.message}`,
      );
    }
  }

  async getAllMembers(
    orgId: Types.ObjectId,
    businessUnitId: string,
    searchTerm?: string,
    roles?: string[],
    userId?: string,
  ) {
    try {
      const orgIdStr = orgId.toString();
      const businessUnitIdStr = businessUnitId.toString();
      const searchRegex = searchTerm ? new RegExp(searchTerm, 'i') : null;

      const query: any = {
        org: orgIdStr,
        businessUnit: businessUnitIdStr,
        isDeleted: false,
      };

      if (searchRegex) {
        query.$or = [
          { firstName: { $regex: searchRegex } },
          { lastName: { $regex: searchRegex } },
          { email: { $regex: searchRegex } },
        ];
      }

      if (roles && roles.length > 0) {
        query.role = { $in: roles };
      }

      let members = await this.basicUserModel
        .find(query)
        .sort({ createdAt: -1 })
        .populate({
          path: 'reportingTo',
          select: '_id firstName lastName firstName',
          model: 'BasicUser',
        })
        .populate({
          path: 'businessUnit',
          select: '_id key label type breadcrumb',
          model: 'BusinessUnit',
        })
        .exec();

      const userIdStr = userId?.toString();

      if (userIdStr) {
        // Exclude the user from main members list
        members = members.filter((m) => m._id.toString() !== userIdStr);

        // Fetch that user (to get their reportingTo IDs)
        const currentUser = await this.basicUserModel
          .findById(userIdStr)
          .lean();

        if (!currentUser) {
          throw new NotFoundException(`User with ID ${userIdStr} not found`);
        }

        const reportingToIds = (currentUser.reportingTo || []).map((id: any) =>
          id.toString(),
        );

        if (reportingToIds.length > 0) {
          // Fetch those reportingTo users (full populated like members)
          const managerDocs = await this.basicUserModel
            .find({ _id: { $in: reportingToIds }, isDeleted: false })
            .populate({
              path: 'reportingTo',
              select: '_id firstName lastName',
              model: 'BasicUser',
            })
            .populate({
              path: 'businessUnit',
              select: '_id key label type breadcrumb',
              model: 'BusinessUnit',
            })
            .exec();

          // Avoid duplicates using Set of string IDs
          const existingIds = new Set(members.map((m) => m._id.toString()));

          for (const manager of managerDocs) {
            const managerId = manager._id.toString();
            if (!existingIds.has(managerId)) {
              members.push(manager);
              existingIds.add(managerId);
            }
          }
        }
      }

      const department = await this.businessUnitModel
        .findById(businessUnitIdStr)
        .exec();
      const departmentHead = department?.departmentHead?.toString();

      const updatedMembers = members.map((member) => {
        const isDepartmentHead = member._id.toString() === departmentHead;
        return { ...member.toObject(), isDepartmentHead };
      });

      // Move department head to the top
      const departmentHeadArr = updatedMembers.find((m) => m.isDepartmentHead);
      const others = updatedMembers.filter((m) => !m.isDepartmentHead);

      // const finalMembers = departmentHead ? [departmentHeadArr, ...others] : others;
      const finalMembers = departmentHeadArr
        ? [departmentHeadArr, ...others]
        : others;

      return finalMembers;
    } catch (error) {
      throw new InternalServerErrorException(
        `An error occurred while retrieving members from org with ID ${orgId} and business unit ID ${businessUnitId}: ${error?.message}`,
      );
    }
  }

  async getRolesForJobApplicationPost(orgId: string): Promise<string[]> {
    // Step 1: Find endpoint IDs for Job-Application-Forms POST
    const matchingEndpoints = await this.endpointPermissionModel.find(
      { controller: 'Job-Application-Forms', method: 'POST' },
      { _id: 1 },
    );

    const endpointIds = matchingEndpoints.map((e) => e._id.toString());

    if (endpointIds.length === 0) return [];

    // Step 2: Find roles from endpointRoles
    const endpointRoles = await this.endpointRolesModel.find({
      org: orgId,
      endPoint: { $in: endpointIds },
    });

    // Step 3: Aggregate roles uniquely
    const rolesSet = new Set<string>();
    for (const entry of endpointRoles) {
      for (const role of entry?.roles || []) {
        rolesSet.add(role);
      }
    }

    return Array.from(rolesSet); // Return as array
  }

  // async getAllDepartmentMembers(orgId: Types.ObjectId, businessUnitId: string,user: string ) {
  //   try {
  //     const orgIdStr = orgId.toString();
  //     const businessUnitIdStr = businessUnitId.toString();
  //     const userId = user.toString();

  //     // Step 1: Find endpoint IDs for Job-Application-Forms POST
  //     const matchingEndpoints = await this.endpointPermissionModel.find({
  //       controller: 'Job-Application-Forms',
  //       method: 'POST',
  //     }, { _id: 1 });

  //     const endpointIds = matchingEndpoints.map(e => e._id.toString());
  //     console.log("endpointIds", endpointIds);

  //     if (endpointIds.length === 0) return [];

  //     // Step 2: Find roles from endpointRoles
  //     const endpointRoles = await this.endpointRolesModel.find({
  //       org: orgId.toString(),
  //       endPoint: { $in: endpointIds },
  //     });
  //     console.log("endpointRoles", endpointRoles);

  //     // Step 3: Aggregate roles uniquely
  //     const rolesSet = new Set<string>();
  //     for (const entry of endpointRoles) {
  //       for (const role of entry?.roles || []) {
  //         rolesSet.add(role);
  //       }
  //     }

  //     const allowedRoles = Array.from(rolesSet);
  //     console.log("allowedRoles", allowedRoles);
  //     if (allowedRoles.length === 0) {
  //       return { roots: [], byManager: {} };
  //     }

  //     const currentUser = await this.basicUserModel.findById(userId).exec();
  //     if (!currentUser) {
  //       throw new Error("User not found.");
  //     }
  //     const managerRoles = [Role.AccountManager, Role.ResourceManager, Role.DeliveryManager];
  //     const hasManagerRole = currentUser.roles?.some(role => managerRoles.includes(role));
  //     const allRoles = await this.rolesModel.find({orgId: orgId.toString()}).lean().exec();
  //     const roleAliasMap = allRoles.reduce((acc, role) => {
  //       acc[role.role] = role.roleAlias;
  //       return acc;
  //     }, {} as Record<string, string>);

  //     console.log(roleAliasMap);
  //     if (hasManagerRole) {
  //       // 👉 Logic A (for accountManager, deliveryManager, resourceManager)
  //       console.log("User is Manager type - applying Manager Logic");

  //       const query: any = {
  //         org: orgIdStr,
  //         businessUnit: businessUnitIdStr,
  //         isDeleted: false,
  //         roles: { $in: allowedRoles },
  //       };

  //       const members = await this.basicUserModel.find(query);
  //       const userMap: Record<string, UserNode> = {};
  //       const byManager: Record<string, UserNode[]> = {};
  //       console.log("members", members);

  //       // Initialize user map and map user names to their ID
  //       const idToNameMap: Record<string, string> = {};

  //       // Step 4: Create user nodes
  //       // Step 4: Create user nodes
  //       for (const user of members) {
  //         const id = user._id.toString();
  //         const name = user.firstName + ' ' + user.lastName;
  //         const roles = user.roles || [];
  //         // Map roles to their alias names
  //         const roleAliases = roles.map(role => roleAliasMap[role] || role);
  //         // Filter reportingTo to only include managers inside same department
  //         const filteredReportingTo = (user.reportingTo || [])
  //           .map((r: any) => r.toString())
  //           .filter((managerIdStr) => members.some(m => m._id.toString() === managerIdStr));

  //         userMap[id] = {
  //           _id: id,
  //           firstName: name,
  //           roles: roles,
  //           roleAliases: roleAliases,
  //           reportingTo: filteredReportingTo,
  //           children: []
  //         };

  //         idToNameMap[id] = name;
  //       }

  //       console.log("userMap", userMap);

  //       // Step 5: Find the department head (e.g., user with "bu_head" role)
  //       const department = await this.businessUnitModel.findById(businessUnitIdStr).exec();
  //       const departmentHead = department?.departmentHead;

  //       if (!departmentHead) {
  //         throw new Error("No department head found.");
  //       }

  //       const departmentHeadId = departmentHead.toString();

  //       // Step 5.1: Ensure department head is present
  //       if (!userMap[departmentHeadId]) {
  //         const headUser = await this.basicUserModel.findById(departmentHeadId);
  //         if (!headUser) {
  //           throw new Error("Department head user not found.");
  //         }
  //         const name = `${headUser.firstName} ${headUser.lastName}`;
  //         userMap[departmentHeadId] = {
  //           _id: departmentHeadId,
  //           firstName: name,
  //           roles: headUser.roles || [],
  //           roleAliases: headUser.roles?.map(role => roleAliasMap[role] || role) || [],
  //           reportingTo: (headUser.reportingTo || []).map((r: any) => r.toString()),
  //           children: []
  //         };
  //         idToNameMap[departmentHeadId] = name;
  //       }

  //       // Step 6: Create the hierarchy by setting children based on reportingTo
  //       const roots: UserNode[] = [];
  //       const visitedUsers = new Set<string>();  // Set to track visited users and prevent circular references

  //       // Step 7: Process members and set their children
  //       for (const user of members) {
  //         const userId = user._id.toString();
  //         const userNode = userMap[userId];

  //         if (userId === departmentHeadId) {
  //           continue; // skip department head
  //         }

  //         let managerIdsToAssign: string[] = [];

  //         if (user.reportingTo && user.reportingTo.length > 0) {
  //           managerIdsToAssign = (user.reportingTo || [])
  //             .map((managerId: any) => managerId.toString())
  //             .filter(managerIdStr => userMap[managerIdStr]);
  //         }

  //         // If no valid manager found, assign to department head
  //         if (managerIdsToAssign.length === 0) {
  //           managerIdsToAssign = [departmentHeadId];

  //           userNode.reportingTo = [departmentHeadId];

  //           console.warn(`User ${userId} had no valid manager, assigned to Department Head.`);
  //         }

  //         for (const managerId of managerIdsToAssign) {
  //           const managerNode = userMap[managerId];
  //           const managerName = idToNameMap[managerId];

  //           if (managerNode) {
  //             // Create a new cloned node with only that managerId in reportingTo
  //             const clonedUserNode = {
  //               ...userNode,
  //               reportingTo: [managerId],
  //             };

  //             managerNode.children.push(clonedUserNode);

  //             if (!byManager[managerName]) {
  //               byManager[managerName] = [];
  //             }
  //             byManager[managerName].push(clonedUserNode);
  //           }
  //         }

  //       }

  //       // Step 8: Set the department head as the root (if it's not already a root)
  //       const departmentHeadNode = userMap[departmentHeadId];
  //       if (departmentHeadNode && !visitedUsers.has(departmentHeadId)) {
  //         roots.push(departmentHeadNode);  // Add department head to roots
  //       }

  //       // return { roots, byManager };
  //       return { roots };

  //     }
  //     else {
  //       // 👉 Logic B: Non-manager user hierarchy starting from the logged-in user
  //       console.log("User is Normal type - applying Normal Logic");

  //       const members = await this.basicUserModel.find({
  //         org: orgIdStr,
  //         businessUnit: businessUnitIdStr,
  //         isDeleted: false,
  //         roles: { $in: allowedRoles },
  //       });

  //       const userMap: Record<string, UserNode> = {};
  //       const byManager: Record<string, UserNode[]> = {};
  //       const idToNameMap: Record<string, string> = {};

  //       // Step 1: Build user map with cleaned reportingTo
  //       for (const user of members) {
  //         const id = user._id.toString();
  //         const name = `${user.firstName} ${user.lastName}`;
  //         const roles = user.roles || [];

  //         // Map roles to their alias names
  //         const roleAliases = roles.map(role => roleAliasMap[role] || role);
  //         const filteredReportingTo = (user.reportingTo || [])
  //           .map((r: any) => r.toString())
  //           .filter(managerIdStr => members.some(m => m._id.toString() === managerIdStr));

  //         userMap[id] = {
  //           _id: id,
  //           firstName: name,
  //           roles: roles,
  //           roleAliases: roleAliases,
  //           reportingTo: filteredReportingTo,
  //           children: [],
  //         };

  //         idToNameMap[id] = name;
  //       }

  //       // Step 2: Build reportingTo-based child links
  //       for (const user of members) {
  //         const userId = user._id.toString();
  //         const reportingToIds = (user.reportingTo || []).map((id: any) => id.toString());

  //         for (const managerId of reportingToIds) {
  //           if (userMap[managerId]) {
  //             const clonedNode = {
  //               ...userMap[userId],
  //               reportingTo: [managerId],
  //             };

  //             userMap[managerId].children.push(clonedNode);

  //             const managerName = idToNameMap[managerId];
  //             if (!byManager[managerName]) {
  //               byManager[managerName] = [];
  //             }
  //             byManager[managerName].push(clonedNode);
  //           }
  //         }
  //       }

  //       const loggedInUserNode = userMap[userId];
  //       if (!loggedInUserNode) {
  //         throw new Error("Logged-in user not found in member list.");
  //       }

  //       // Step 3: Recursively build only the tree below the logged-in user
  //       function buildSubtree(node: UserNode): UserNode {
  //         const resultNode: UserNode = { ...node, children: [] };
  //         for (const child of node.children) {
  //           resultNode.children.push(buildSubtree(child));
  //         }
  //         return resultNode;
  //       }

  //       const subtreeRoot = buildSubtree(loggedInUserNode);

  //       return { roots: [subtreeRoot] };
  //     }

  //     // return { roots, byManager };
  //     return { };
  //   } catch (error) {
  //     throw new InternalServerErrorException(
  //       `An error occurred while retrieving members from org with ID ${orgId} and business unit ID ${businessUnitId}: ${error?.message}`
  //     );
  //   }
  // }

  async findValidManagers(
    userId: string,
    membersMap: Record<string, any>,
    visited: Set<string> = new Set(),
  ): Promise<string[]> {
    if (visited.has(userId)) return [];
    visited.add(userId);

    const user = await this.basicUserModel
      .findById(userId)
      .select('reportingTo')
      .lean();
    if (
      !user ||
      !Array.isArray(user.reportingTo) ||
      user.reportingTo.length === 0
    )
      return [];

    let validManagers: string[] = [];

    for (const managerId of user.reportingTo) {
      if (membersMap[managerId]) {
        validManagers.push(managerId);
      } else {
        // const higherManagers = await this.findValidManagers.call(this, managerId, membersMap, visited);
        // validManagers.push(...higherManagers);
      }
    }

    return [...new Set(validManagers)];
  }

  async getAllDepartmentMembers(
    orgId: Types.ObjectId,
    businessUnitId: string,
    user: string,
  ) {
    try {
      const orgIdStr = orgId.toString();
      const businessUnitIdStr = businessUnitId.toString();
      const userId = user.toString();

      // Step 1: Find endpoint IDs for Job-Application-Forms POST
      const matchingEndpoints = await this.endpointPermissionModel.find(
        { controller: 'Job-Application-Forms', method: 'POST' },
        { _id: 1 },
      );

      const endpointIds = matchingEndpoints.map((e) => e._id.toString());
      console.log('endpointIds', endpointIds);

      if (endpointIds.length === 0) return [];

      // Step 2: Find roles from endpointRoles
      const endpointRoles = await this.endpointRolesModel.find({
        org: orgId.toString(),
        endPoint: { $in: endpointIds },
      });
      console.log('endpointRoles', endpointRoles);

      // Step 3: Aggregate roles uniquely
      const rolesSet = new Set<string>();
      for (const entry of endpointRoles) {
        for (const role of entry?.roles || []) {
          rolesSet.add(role);
        }
      }

      const allowedRoles = Array.from(rolesSet);
      console.log('allowedRoles', allowedRoles);
      if (allowedRoles.length === 0) {
        return { roots: [], byManager: {} };
      }

      const currentUser = await this.basicUserModel.findById(userId).exec();
      if (!currentUser) {
        throw new Error('User not found.');
      }
      const managerRoles = [
        Role.AccountManager,
        Role.ResourceManager,
        Role.DeliveryManager,
      ];
      const hasManagerRole = currentUser.roles?.some((role) =>
        managerRoles.includes(role),
      );
      const allRoles = await this.rolesModel
        .find({ orgId: orgId.toString() })
        .lean()
        .exec();
      const roleAliasMap = allRoles.reduce(
        (acc, role) => {
          acc[role.role] = role.roleAlias;
          return acc;
        },
        {} as Record<string, string>,
      );

      console.log(roleAliasMap);
      if (hasManagerRole) {
        // 👉 Logic A (for accountManager, deliveryManager, resourceManager)
        console.log('User is Manager type - applying Manager Logic');

        const query: any = {
          org: orgIdStr,
          businessUnit: businessUnitIdStr,
          isDeleted: false,
          roles: { $in: allowedRoles },
        };

        const members = await this.basicUserModel.find(query);
        const userMap: Record<string, UserNode> = {};
        const byManager: Record<string, UserNode[]> = {};
        console.log('members', members);

        // Initialize user map and map user names to their ID
        const idToNameMap: Record<string, string> = {};

        // Step 4: Create user nodes
        // Step 4: Create user nodes
        for (const user of members) {
          const id = user._id.toString();
          const name = user.firstName + ' ' + user.lastName;
          const roles = user.roles || [];
          // Map roles to their alias names
          const roleAliases = roles.map((role) => roleAliasMap[role] || role);
          // Filter reportingTo to only include managers inside same department
          const filteredReportingTo = (user.reportingTo || [])
            .map((r: any) => r.toString())
            .filter((managerIdStr) =>
              members.some((m) => m._id.toString() === managerIdStr),
            );

          userMap[id] = {
            _id: id,
            firstName: name,
            roles: roles,
            roleAliases: roleAliases,
            reportingTo: filteredReportingTo,
            children: [],
          };

          idToNameMap[id] = name;
        }

        console.log('userMap', userMap);

        // Step 5: Find the department head (e.g., user with "bu_head" role)
        const department = await this.businessUnitModel
          .findById(businessUnitIdStr)
          .exec();
        const departmentHead = department?.departmentHead;

        if (!departmentHead) {
          throw new Error('No department head found.');
        }

        const departmentHeadId = departmentHead.toString();

        // Step 5.1: Ensure department head is present
        if (!userMap[departmentHeadId]) {
          const headUser = await this.basicUserModel.findById(departmentHeadId);
          if (!headUser) {
            throw new Error('Department head user not found.');
          }
          const name = `${headUser.firstName} ${headUser.lastName}`;
          userMap[departmentHeadId] = {
            _id: departmentHeadId,
            firstName: name,
            roles: headUser.roles || [],
            roleAliases:
              headUser.roles?.map((role) => roleAliasMap[role] || role) || [],
            reportingTo: (headUser.reportingTo || []).map((r: any) =>
              r.toString(),
            ),
            children: [],
          };
          idToNameMap[departmentHeadId] = name;
        }

        // Step 6: Create the hierarchy by setting children based on reportingTo
        const roots: UserNode[] = [];
        const visitedUsers = new Set<string>(); // Set to track visited users and prevent circular references

        // Step 7: Process members and set their children
        for (const user of members) {
          const userId = user._id.toString();
          const userNode = userMap[userId];

          if (userId === departmentHeadId) {
            continue; // skip department head
          }

          let managerIdsToAssign: string[] = [];

          if (user.reportingTo && user.reportingTo.length > 0) {
            managerIdsToAssign = (user.reportingTo || [])
              .map((managerId: any) => managerId.toString())
              .filter((managerIdStr) => userMap[managerIdStr]);
          }

          // If no valid manager found, assign to department head
          if (managerIdsToAssign.length === 0) {
            let fallbackManagers: string[] = [];

            if (
              Array.isArray(user.reportingTo) &&
              user.reportingTo.length > 0
            ) {
              for (const managerId of user.reportingTo) {
                const resolved = await this.findValidManagers.call(
                  this,
                  managerId,
                  userMap,
                );
                fallbackManagers.push(...resolved);
              }
            }

            fallbackManagers = [...new Set(fallbackManagers)];

            if (fallbackManagers.length > 0) {
              managerIdsToAssign = fallbackManagers;
              userNode.reportingTo = fallbackManagers;
              console.warn(
                `User ${userId} assigned to fallback managers: ${fallbackManagers.join(', ')}`,
              );
            } else {
              managerIdsToAssign = [departmentHeadId];
              userNode.reportingTo = [departmentHeadId];
              console.warn(
                `User ${userId} had no valid manager in department, assigned to Department Head.`,
              );
            }
          }

          for (const managerId of managerIdsToAssign) {
            const managerNode = userMap[managerId];
            const managerName = idToNameMap[managerId];

            if (managerNode) {
              // Create a new cloned node with only that managerId in reportingTo
              const clonedUserNode = { ...userNode, reportingTo: [managerId] };

              managerNode.children.push(clonedUserNode);

              if (!byManager[managerName]) {
                byManager[managerName] = [];
              }
              byManager[managerName].push(clonedUserNode);
            }
          }
        }

        // Step 8: Set the department head as the root (if it's not already a root)
        const departmentHeadNode = userMap[departmentHeadId];
        if (departmentHeadNode && !visitedUsers.has(departmentHeadId)) {
          roots.push(departmentHeadNode); // Add department head to roots
        }

        // return { roots, byManager };
        return { roots };
      } else {
        // 👉 Logic B: Non-manager user hierarchy starting from the logged-in user
        console.log('User is Normal type - applying Normal Logic');

        // 👉 Logic B: Non-manager user hierarchy starting from the logged-in user
        console.log('User is Normal type - applying Normal Logic');

        const members = await this.basicUserModel.find({
          org: orgIdStr,
          businessUnit: businessUnitIdStr,
          isDeleted: false,
          roles: { $in: allowedRoles },
        });

        const userMap: Record<string, UserNode> = {};
        const idToNameMap: Record<string, string> = {};

        // Step 1: Build user map
        for (const user of members) {
          const id = user._id.toString();
          const name = `${user.firstName} ${user.lastName}`;
          const roles = user.roles || [];

          const roleAliases = roles.map((role) => roleAliasMap[role] || role);

          userMap[id] = {
            _id: id,
            firstName: name,
            roles: roles,
            roleAliases: roleAliases,
            reportingTo: [], // To be filled with validated ones
            children: [],
          };

          idToNameMap[id] = name;
        }

        // Step 2: Helper to recursively find valid internal managers
        async function findValidReportingTo(
          user: any,
          membersMap: Record<string, UserNode>,
          visited = new Set<string>(),
        ): Promise<string[]> {
          if (!user?.reportingTo || visited.has(user._id.toString())) return [];

          visited.add(user._id.toString());

          const validManagers: string[] = [];

          for (const managerId of user.reportingTo) {
            const managerIdStr = managerId.toString();

            if (membersMap[managerIdStr]) {
              validManagers.push(managerIdStr);
            } else {
              const manager = await this.basicUserModel
                .findById(managerIdStr)
                .select('reportingTo')
                .lean();
              if (manager) {
                const fallbackManagers = await findValidReportingTo.call(
                  this,
                  manager,
                  membersMap,
                  visited,
                );
                validManagers.push(...fallbackManagers);
              }
            }
          }

          return [...new Set(validManagers)];
        }

        // Step 3: Clean reportingTo and rebuild children map
        for (const user of members) {
          const userId = user._id.toString();
          const validManagers = await findValidReportingTo.call(
            this,
            user,
            userMap,
          );

          userMap[userId].reportingTo = validManagers;

          for (const managerId of validManagers) {
            if (userMap[managerId]) {
              userMap[managerId].children.push({
                ...userMap[userId],
                reportingTo: [managerId],
              });
            }
          }
        }

        // Step 4: Build subtree starting from the logged-in user
        const loggedInUserNode = userMap[userId];
        if (!loggedInUserNode) {
          throw new Error('Logged-in user not found in member list.');
        }

        function buildSubtree(node: UserNode): UserNode {
          const resultNode: UserNode = { ...node, children: [] };
          for (const child of node.children) {
            resultNode.children.push(buildSubtree(child));
          }
          return resultNode;
        }

        const subtreeRoot = buildSubtree(loggedInUserNode);

        return { roots: [subtreeRoot] };
      }

      // return { roots, byManager };
      return {};
    } catch (error) {
      throw new InternalServerErrorException(
        `An error occurred while retrieving members from org with ID ${orgId} and business unit ID ${businessUnitId}: ${error?.message}`,
      );
    }
  }

  async getAllMembersOrg(
    orgId: Types.ObjectId,
    businessUnitIds: string[],
    searchTerm?: string,
    roles?: string[],
    userId?: string,
  ) {
    try {
      const orgIdStr = orgId.toString();
      // const businessUnitIdStr = businessUnitId.toString();
      const businessUnitIdStrs = businessUnitIds.map((id) => id.toString()); // Convert to strings
      const searchRegex = searchTerm ? new RegExp(searchTerm, 'i') : null;

      const query: any = {
        org: orgIdStr,
        // businessUnit: businessUnitIdStr,
        businessUnit: { $in: businessUnitIdStrs },
        isDeleted: false,
      };

      if (searchRegex) {
        query.$or = [
          { firstName: { $regex: searchRegex } },
          { lastName: { $regex: searchRegex } },
          { email: { $regex: searchRegex } },
        ];
      }

      if (roles && roles.length > 0) {
        query.role = { $in: roles };
      }

      const members = await this.basicUserModel
        .find(query)
        .populate({
          path: 'reportingTo',
          select: '_id firstName lastName firstName',
          model: 'BasicUser',
        })
        .populate({
          path: 'businessUnit',
          select: '_id key label type breadcrumb',
          model: 'BusinessUnit',
        })
        .exec();

      // 🔥 Exclude user if userId is provided
      const filteredMembers = userId
        ? members.filter((m) => m._id.toString() !== userId.toString())
        : members;

      return filteredMembers;
    } catch (error) {
      throw new InternalServerErrorException(
        `An error occurred while retrieving members from org with ID ${orgId} and business unit IDs ${businessUnitIds.join(
          ', ',
        )}: ${error?.message}`,
      );
    }
  }

  async getAllMembersByOrg(
    orgId: Types.ObjectId | string,
    page: number,
    limit: number,
    searchTerm?: string,
    departmentId?: Types.ObjectId,
  ) {
    try {
      const orgIdStr =
        orgId instanceof Types.ObjectId ? orgId : new Types.ObjectId(orgId);
      const searchRegex = searchTerm ? new RegExp(searchTerm, 'i') : null;
      const regexSource = searchRegex?.source; // Extract source (pattern only)

      // Construct the query object
      const query: any = { isDeleted: false };
      query.$or = [
        { org: orgIdStr }, // For ObjectId type
        { org: orgId.toString() }, // For string type
      ];

      if (searchRegex) {
        query.$or = [
          { firstName: { $regex: searchRegex } },
          { lastName: { $regex: searchRegex } },
          { email: { $regex: searchRegex } },
          {
            $expr: {
              $regexMatch: {
                input: { $concat: ['$firstName', ' ', '$lastName'] },
                regex: regexSource,
                options: 'i',
              },
            },
          },
        ];
      }

      if (departmentId) {
        // Get all department IDs starting from the given department ID
        const departmentIds =
          await this.businessUnitService.getAllDepartmentIds(departmentId);
        // Add the departmentIds to the query to filter members
        query.businessUnit = { $in: departmentIds };
      }

      // Use Promise.all to fetch members and total count concurrently
      const [members, totalRecords] = await Promise.all([
        this.basicUserModel
          .find(query)
          .populate({
            path: 'reportingTo',
            select: '_id firstName lastName firstName',
            model: 'BasicUser',
          })
          .populate({
            path: 'businessUnit',
            select: '_id label key breadcrumb',
            model: 'BusinessUnit',
          })
          .populate({
            path: 'logo',
            select: '_id locationUrl originalName fileSize fileType',
            model: 'FileMetadata',
          })
          .sort({ createdAt: -1 }) // 👈 Both sorting
          .skip((page - 1) * limit)
          .limit(limit)
          .exec(),
        this.basicUserModel.countDocuments(query).exec(),
      ]);

      // Return members and total record count
      return { members, totalRecords };
    } catch (error) {
      throw new InternalServerErrorException(
        `An error occurred while retrieving members from org with ID ${orgId}: ${error?.message}`,
      );
    }
  }

  async getAllMembersByOrgAndDepartment(
    orgId: Types.ObjectId,
    page: number,
    limit: number,
    searchTerm?: string,
    departmentId?: Types.ObjectId,
  ) {
    try {
      const orgIdStr = orgId.toString();
      const searchRegex = searchTerm ? new RegExp(searchTerm, 'i') : null;

      // Construct the query object
      const query: any = { org: orgIdStr, isDeleted: false };

      if (searchRegex) {
        query.$or = [
          { firstName: { $regex: searchRegex } },
          { lastName: { $regex: searchRegex } },
          { email: { $regex: searchRegex } },
        ];
      }

      if (departmentId) {
        // Get all department IDs starting from the given department ID
        const departmentIds =
          await this.businessUnitService.getAllDepartmentIds(departmentId);
        query.businessUnit = { $in: departmentIds };
      }

      // Fetch members and total count
      const [members, totalRecords] = await Promise.all([
        this.basicUserModel
          .find(query)
          .populate({
            path: 'reportingTo',
            select: '_id firstName lastName firstName',
            model: 'BasicUser',
          })
          .populate({
            path: 'businessUnit',
            select: '_id label key breadcrumb',
            model: 'BusinessUnit',
          })
          .skip((page - 1) * limit)
          .limit(limit)
          .exec(),
        this.basicUserModel.countDocuments(query).exec(),
      ]);

      // Group members by department (business unit)
      const groupedData = members.reduce((result: any, member: any) => {
        const department = member.businessUnit?.label;

        if (!result[department]) {
          result[department] = [];
        }

        result[department].push(member);

        return result;
      }, {});

      // Return the structured response
      return { groupedData, totalRecords };
    } catch (error) {
      throw new InternalServerErrorException(
        `An error occurred while retrieving members from org with ID ${orgId}: ${error?.message}`,
      );
    }
  }

  async getMember(orgId: Types.ObjectId, memberId: Types.ObjectId) {
    try {
      const orgIdStr = orgId.toString();

      const member = await this.basicUserModel
        .findOne({ org: orgIdStr, _id: memberId })
        .populate({
          path: 'businessUnit',
          model: 'BusinessUnit', // Explicitly specifying model
          select: '_id key label type breadcrumb',
        })
        .populate({
          path: 'reportingTo',
          model: 'BasicUser', // Explicitly specifying model
          select: '_id firstName lastName',
        })
        // .lean() // Convert Mongoose document to plain JavaScript object
        .exec();

      return member;
    } catch (error) {
      throw new InternalServerErrorException(
        `An error occurred while retrieving members from org with ID ${orgId} and business unit ID ${memberId}: ${error?.message}`,
      );
    }
  }

  async addMemberToOrg(orgId: string, createMemberDto: CreateMemberDto) {
    const isThere = await this.findUserByEmailExists(createMemberDto.email);
    if (isThere) {
      throw new BadRequestException(
        `The user with email "${createMemberDto.email}" already exists.`,
      );
    }

    // Generate a strong password
    const generateStrongPassword = () => {
      const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
      const lowercase = 'abcdefghijklmnopqrstuvwxyz';
      const numbers = '0123456789';
      const specialChars = '!@#$%^&*()_-+=<>?';
      const allChars = uppercase + lowercase + numbers + specialChars;

      let password = '';
      password += uppercase[Math.floor(Math.random() * uppercase.length)];
      password += numbers[Math.floor(Math.random() * numbers.length)];
      password += specialChars[Math.floor(Math.random() * specialChars.length)];

      for (let i = 3; i < 8; i++) {
        password += allChars[Math.floor(Math.random() * allChars.length)];
      }

      return password
        .split('')
        .sort(() => 0.5 - Math.random())
        .join(''); // Shuffle the password
    };
    // Generate and hash the password
    const password = generateStrongPassword();
    const hashedPassword = await bcrypt.hash(password, 10);

    const selectedUnits = Object.entries(createMemberDto.businessUnit)
      .filter(([_, value]) => value === true)
      .map(([key]) => key);

    console.log(selectedUnits); // ['0-0', '1-2']
    const departmentIds =
      await this.businessUnitService.getDepartmentIdsByKeyValues(
        orgId,
        selectedUnits,
      );

    // Determine reportingTo
    let reportingTo = createMemberDto.reportingTo;

    // If reportingTo is empty or undefined, use department heads
    if (!reportingTo || reportingTo.length === 0) {
      const departments =
        await this.businessUnitService.getDepartmentsByIds(departmentIds);
      console.log('departments', departments);

      reportingTo = departments
        .map((dept) => dept.departmentHead)
        .filter(Boolean) // remove null or undefined
        .map((id) => id.toString()); // ensure they're all strings

      console.log('reportingTo', reportingTo);
    }

    const createMember = new this.basicUserModel({
      ...createMemberDto,
      businessUnit: departmentIds,
      org: orgId,
      password: hashedPassword,
      isVerified: true,
      reportingTo: reportingTo,
    });

    const createdMember = await createMember.save();

    this.emitEvent('user.verify.email.confirm.password', {
      ...createMemberDto,
      password,
    });
    const sanitizedUser = omit(createdMember.toObject(), ['password', '__v']);
    return sanitizedUser;
  }

  async updateMemberOfOrg(
    orgId: Types.ObjectId,
    memberId: Types.ObjectId,
    updateMemberDto: UpdateMemberDto,
    userId: string,
  ) {
    try {
      const orgIdStr = orgId.toString();
      // if ('reportingTo' in updateMemberDto && updateMemberDto.reportingTo === '') {
      //   updateMemberDto.reportingTo = null;
      // }

      if (!updateMemberDto.businessUnit) {
        throw new BadRequestException('businessUnit is required');
      }

      const selectedUnits = Object.entries(updateMemberDto.businessUnit)
        .filter(([_, value]) => value === true)
        .map(([key]) => key);

      console.log(selectedUnits); // ['0-0', '1-2']
      const departmentIds =
        await this.businessUnitService.getDepartmentIdsByKeyValues(
          orgIdStr,
          selectedUnits,
        );

      if ('reportingTo' in updateMemberDto) {
        updateMemberDto.reportingTo =
          Array.isArray(updateMemberDto.reportingTo) &&
          updateMemberDto.reportingTo.length === 0
            ? undefined // Use undefined instead of null
            : updateMemberDto.reportingTo;
      }
      const existingMember = await this.basicUserModel
        .findById(memberId)
        .exec();
      if (!existingMember) throw new NotFoundException('User not found');
      const updatedMember = await this.basicUserModel
        .findOneAndUpdate(
          { _id: memberId, org: orgIdStr },
          { $set: updateMemberDto, businessUnit: departmentIds },
          { new: true },
        )
        .exec();

      if (!updatedMember) {
        throw new InternalServerErrorException(
          `Failed to update member with ID ${memberId}`,
        );
      }

      console.log('existingMember', existingMember);
      console.log('updatedMember', updatedMember);
      const notifications = [];

      const allRoles = await this.rolesModel
        .find({ orgId: orgId.toString() })
        .lean()
        .exec();
      const roleAliasMap = allRoles.reduce(
        (acc, role) => {
          acc[role.role] = role.roleAlias;
          return acc;
        },
        {} as Record<string, string>,
      );
      const oldroles = existingMember.roles || [];
      const newroles = updatedMember.roles || [];

      const oldSet = new Set(oldroles);
      const newSet = new Set(newroles);

      const addedRoles = [...newSet].filter((role) => !oldSet.has(role));
      const removedRoles = [...oldSet].filter((role) => !newSet.has(role));

      // Only trigger notification if there's a real change
      if (addedRoles.length > 0 || removedRoles.length > 0) {
        const addedAliases = addedRoles.map(
          (role) => roleAliasMap[role] || role,
        );
        const removedAliases = removedRoles.map(
          (role) => roleAliasMap[role] || role,
        );

        let message = 'Your roles have been updated.';
        if (addedAliases.length > 0) {
          message += ` Added: [${addedAliases.join(', ')}].`;
        }
        if (removedAliases.length > 0) {
          message += ` Removed: [${removedAliases.join(', ')}].`;
        }

        // await this.notificationsService.createAndNotify(
        //   memberId.toString(),
        //   message,
        //   userId,
        //   {},
        //   'role-update',
        // );

        notifications.push({ message, type: NotificationType.PROFILE_UPDATE });
      }

      // Notify on reportingTo update
      if ('reportingTo' in updateMemberDto) {
        const oldReportingToIds = (existingMember.reportingTo || []).map((id) =>
          id.toString(),
        );
        const newReportingToIds = (updateMemberDto.reportingTo || []).map(
          (id) => id.toString(),
        );

        const addedReportingToIds = newReportingToIds.filter(
          (id) => !oldReportingToIds.includes(id),
        );
        const removedReportingToIds = oldReportingToIds.filter(
          (id) => !newReportingToIds.includes(id),
        );

        if (
          addedReportingToIds.length > 0 ||
          removedReportingToIds.length > 0
        ) {
          const reportingToUsers = await this.basicUserModel
            .find({
              _id: { $in: [...addedReportingToIds, ...removedReportingToIds] },
            })
            .select('firstName lastName')
            .lean();

          const getName = (id: string) => {
            const user = reportingToUsers.find((u) => u._id.toString() === id);
            return user ? `${user.firstName} ${user.lastName}` : id;
          };
          let message = 'Your reporting structure has been updated.';
          if (addedReportingToIds.length > 0) {
            message += ` Added: [${addedReportingToIds.map(getName).join(', ')}].`;
          }
          if (removedReportingToIds.length > 0) {
            message += ` Removed: [${removedReportingToIds.map(getName).join(', ')}].`;
          }
          notifications.push({
            message,
            type: NotificationType.PROFILE_UPDATE,
          });
        }
      }

      // Notify on businessUnit update
      if (selectedUnits.length > 0) {
        const oldBUIds = (existingMember.businessUnit || []).map((id) =>
          id.toString(),
        );
        const newBUIds = (departmentIds || []).map((id) => id.toString());

        const addedBUIds = newBUIds.filter((id) => !oldBUIds.includes(id));
        const removedBUIds = oldBUIds.filter((id) => !newBUIds.includes(id));

        if (addedBUIds.length > 0 || removedBUIds.length > 0) {
          const allUnits = await this.businessUnitModel
            .find({ _id: { $in: [...addedBUIds, ...removedBUIds] } })
            .select('label')
            .lean();

          const getBUName = (id: string) => {
            const unit = allUnits.find((u) => u._id.toString() === id);
            return unit ? unit.label : id;
          };

          let message = 'Your business unit assignments have been updated.';
          if (addedBUIds.length > 0) {
            message += ` Added: [${addedBUIds.map(getBUName).join(', ')}].`;
          }
          if (removedBUIds.length > 0) {
            message += ` Removed: [${removedBUIds.map(getBUName).join(', ')}].`;
          }

          notifications.push({
            message,
            type: NotificationType.PROFILE_UPDATE,
          });
        }
      }

      // Send notifications if any changes were detected
      for (const notification of notifications) {
        await this.notificationsService.createAndNotify(
          memberId.toString(),
          notification.message,
          userId,
          {},
          notification.type,
        );
      }

      return updatedMember;
    } catch (error) {
      this.logger.error(
        `An error occurred while updating member with ID ${memberId}:`,
        error.stack,
      );
      throw new InternalServerErrorException(
        `An error occurred while updating member with ID ${memberId}: ${error?.message}`,
      );
    }
  }

  async removeMemberFromOrg(orgId: Types.ObjectId, memberId: Types.ObjectId) {
    try {
      const orgIdStr = orgId.toString();

      const user = await this.basicUserModel.findOne({
        _id: memberId,
        org: orgIdStr,
      });

      if (!user) {
        throw new NotFoundException(
          `Member with ID ${memberId} not found in organization with ID ${orgId}`,
        );
      }

      user.isDeleted = true;
      await user.save();

      return user;
    } catch (error) {
      throw new InternalServerErrorException(
        `An error occurred while soft deleting the member from org with ID ${orgId}: ${error?.message}`,
      );
    }
  }

  async bulkRemoveMembers(orgId: Types.ObjectId, memberIds: Types.ObjectId[]) {
    try {
      const orgIdStr = orgId.toString();

      // const result = await this.basicUserModel.updateMany(
      //   { _id: { $in: memberIds }, orgIdStr },
      //   { $set: { isDeleted: true } }
      // ).exec();

      // if (result.modifiedCount > 0) {
      //   this.logger.log(`Successfully soft-deleted ${result.modifiedCount} member(s) from org ${orgId}.`);
      //   return { message: `Successfully soft-deleted ${result.modifiedCount} member(s).` };
      // } else {
      //   this.logger.error('No members were deleted.');
      //   return { message: 'No members were deleted.' };
      // }

      const promises = memberIds.map(async (memberId) => {
        const user = await this.basicUserModel.findOne({
          _id: memberId,
          org: orgIdStr,
        });

        if (!user) {
          throw new NotFoundException(
            `Member with ID ${memberId} not found in organization with ID ${orgId}`,
          );
        }

        user.isDeleted = true;
        await user.save(); // Save after updating the `isDeleted` field
      });

      // Wait for all operations to complete
      await Promise.all(promises);

      return {
        message: `Successfully soft-deleted ${memberIds.length} member(s).`,
      };
    } catch (error) {
      this.logger.error(
        `Error while bulk deleting members: ${error.message}`,
        error.stack,
      );
      throw new InternalServerErrorException(
        'Error while bulk deleting members.',
      );
    }
  }

  async hardRemoveMemberFromOrg(
    orgId: Types.ObjectId,
    memberId: Types.ObjectId,
  ) {
    try {
      const orgIdStr = orgId.toString();

      const deletedUser = await this.basicUserModel.findOneAndDelete({
        _id: memberId,
        org: orgIdStr,
      });

      if (!deletedUser) {
        throw new NotFoundException(
          `Member with ID ${memberId} not found in organization with ID ${orgId}`,
        );
      }

      return {
        message: `Member with ID ${memberId} has been successfully deleted.`,
      };
    } catch (error) {
      throw new InternalServerErrorException(
        `An error occurred while hard deleting the member from org with ID ${orgId}: ${error?.message}`,
      );
    }
  }

  async addUserToOrg(
    orgId: Types.ObjectId,
    userId: Types.ObjectId,
    role?: string,
  ): Promise<any> {
    try {
      const user: any = await this.findById(userId);

      const organization: any = await this.orgModel.findById(orgId);

      if (!organization) {
        throw new NotFoundException(`Organization with ID ${orgId} not found.`);
      }

      user.org = organization;

      if (role && Object.values(Role).includes(role as Role)) {
        const newRole = role as Role;
        if (!user.roles?.includes(newRole)) {
          user.roles?.push(newRole);
        }
      }

      await user.save();
      const populatedUser = await user.populate({
        path: 'org',
        select: '_id title orgType description',
        model: 'Org',
      });
      const sanitizedUser = omit(populatedUser.toObject(), [
        'password',
        'verification',
        'verificationExpires',
        '__v',
      ]);

      return sanitizedUser;
      // TODO:: the above line will return entire user object along with passwords. Dont do this. Return safe user object. omit sensitive information
    } catch (error) {
      this.logger.error(`Error in adding user to the org. ${error?.message}`);
      throw new BadRequestException(
        `Error in adding user to the org. ${error?.message}`,
      );
    }
  }

  // async removeUserFromOrg(userId: Types.ObjectId): Promise<any> {
  //   try {
  //     const user = await this.findById(userId);

  //     user.org = undefined;

  //     const rolesToKeep: Role[] = [];

  //     if (user.roles) {
  //       user.roles.forEach((role) => {
  //         if (![Role.RootOrgAdmin, Role.AgencyOrgAdmin, Role.CustomerOrgAdmin].includes(role)) {
  //           rolesToKeep.push(role);
  //         }
  //       });
  //     }

  //     user.roles = rolesToKeep.length > 0 ? rolesToKeep : [Role.User];

  //     await user.save();
  //     const populatedUser = await user.populate({ path: 'org', select: '_id title orgType description', model: 'Org' });
  //     const sanitizedUser = omit(populatedUser.toObject(), ['password', 'verification', 'verificationExpires', '__v']);

  //     return sanitizedUser;
  //     // TODO:: the above line will return entire user object along with passwords. Dont do this. Return safe user object. omit sensitive information
  //   } catch (error) {
  //     this.logger.error(`Error in removing user from the org. ${error?.message}`);
  //     throw new BadRequestException(`Error in removing user from the org. ${error?.message}`);
  //   }
  // }

  async resendVerificationTokenEmail(email: string) {
    try {
      const user: any = await this.findUserByEmail(email);

      if (!user.isVerified) {
        if (
          user.verificationExpires &&
          isAfter(new Date(), new Date(user.verificationExpires))
        ) {
          const otpCode = this.generateOTP(6);

          const updatedUser = await this.basicUserModel.findByIdAndUpdate(
            user._id,
            {
              // verification: v4(),
              otpCode,
              verificationExpires: addMinutes(new Date(), 10), // Set token expiration
            },
            { new: true },
          );
          this.emitEvent('user.verify.email', updatedUser);
          return {
            statusCode: HttpStatus.OK,
            message: 'Verification OTP resent successfully.',
          };
        } else {
          // const timeToWait = differenceInHours(new Date(user.verificationExpires), new Date());
          const timeToWait = differenceInMinutes(
            new Date(user.verificationExpires),
            new Date(),
          );
          throw new HttpException(
            `Verification OTP is still valid. Please wait ${timeToWait} minutes before requesting a new one.`,
            HttpStatus.BAD_REQUEST,
          );
        }
      } else {
        throw new HttpException(
          'User is already verified.',
          HttpStatus.BAD_REQUEST,
        );
      }
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        `Failed to update verification code of user. ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async resendForgotPasswordEmail(email: string): Promise<any> {
    try {
      const user = await this.findUserByEmail(email);

      // if (user?.status?.toLowerCase() === 'blocked') {
      //   throw new ForbiddenException(`User's access is blocked. Contact user support.`);
      // }

      if (!user?.isVerified) {
        try {
          await this.resendVerificationTokenEmail(email);
          return {
            statusCode: HttpStatus.OK,
            message: 'Verification token resent successfully.',
          };
        } catch (error) {
          this.logger.error(
            `Failed to resend verification email: ${error.message}`,
          );
          throw new UnauthorizedException(
            `User email is not verified and failed to resend verification email.`,
          );
        }
      }

      let forgotPassword = await this.forgotPasswordModel.findOne({ email });

      if (forgotPassword) {
        if (isAfter(new Date(), forgotPassword.expires)) {
          // Token expired, update token and expiration
          forgotPassword.verification = v4();
          forgotPassword.expires = addMinutes(new Date(), 10);
          forgotPassword.firstUsed = false;
          forgotPassword.finalUsed = false;
        } else {
          // const timeToWait = differenceInHours(new Date(forgotPassword.expires), new Date());
          const timeToWait = differenceInMinutes(
            new Date(forgotPassword.expires),
            new Date(),
          );
          throw new HttpException(
            `Forgot password email is still valid. Please wait ${timeToWait} minutes before requesting a new one.`,
            HttpStatus.BAD_REQUEST,
          );
        }
      } else {
        forgotPassword = new this.forgotPasswordModel({
          email,
          verification: v4(),
          expires: addMinutes(new Date(), 1),

          // expires: addHours(new Date(), Number(this.HOURS_TO_VERIFY)),
        });
      }

      const forgotPasswordRecord = await forgotPassword.save();
      this.emitEvent('user.forgot.password', forgotPasswordRecord);

      return {
        statusCode: HttpStatus.OK,
        message: 'Forgot password email resent',
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new InternalServerErrorException(
        `Error occurred while sending forgot password email: ${error.message}`,
      );
    }
  }

  async getAllInternalContactsByOrg(
    orgId: Types.ObjectId,
    page: number,
    limit: number,
    searchTerm?: string,
    departmentId?: Types.ObjectId,
  ) {
    try {
      const orgIdStr = orgId.toString();
      const searchRegex = searchTerm ? new RegExp(searchTerm, 'i') : null;

      // Construct the query object
      // const query: any = { org: orgIdStr, isDeleted: false };
      const query: any = {
        org: orgIdStr,
        isDeleted: false,
        roles: {
          $in: [Role.BUHead, Role.ResourceManager, Role.AccountManager],
        }, // Filter only BUHead & ResourceManager && Account Manager
      };

      if (searchRegex) {
        query.$or = [
          { firstName: { $regex: searchRegex } },
          { lastName: { $regex: searchRegex } },
          { email: { $regex: searchRegex } },
        ];
      }

      if (departmentId) {
        // Get all department IDs starting from the given department ID
        const departmentIds =
          await this.businessUnitService.getAllDepartmentIds(departmentId);
        // Add the departmentIds to the query to filter members
        query.businessUnit = { $in: departmentIds };
      }
      // Use Promise.all to fetch members and total count concurrently
      const [members, totalRecords] = await Promise.all([
        this.basicUserModel
          .find(query)
          .populate({
            path: 'reportingTo',
            select: '_id firstName lastName firstName',
            model: 'BasicUser',
          })
          .populate({
            path: 'businessUnit',
            select: '_id label key breadcrumb',
            model: 'BusinessUnit',
          })
          .skip((page - 1) * limit)
          .limit(limit)
          .exec(),
        this.basicUserModel.countDocuments(query).exec(),
      ]);
      // Return members and total record count
      return { members, totalRecords };
    } catch (error) {
      throw new InternalServerErrorException(
        `An error occurred while retrieving members from org with ID ${orgId}: ${error?.message}`,
      );
    }
  }
  getPopulateOptions(): PopulateOptions[] {
    const populateOptions = [
      { path: 'customStatus', select: '_id name', model: 'Status' },
      {
        path: 'logo',
        select: '_id locationUrl originalName fileSize fileType',
        model: 'FileMetadata',
      },
      {
        path: 'org',
        select:
          '_id title orgType description customers vendors isAdminOrg logo isOnboarded companyId',
        model: 'Org',
        populate: [
          {
            path: 'logo',
            select:
              '_id originalName uniqueName fileSize fileType locationUrl etag status',
            model: 'FileMetadata',
          },
          {
            path: 'contactAddress',
            select: 'apartment street city postalCode',
            model: 'AddressInformation',
          },
        ],
      },
      {
        path: 'companyId',
        select: '_id title orgType createdBy',
        model: 'Org',
      },
    ];

    // { path: 'contactDetails', select: '_id contactEmail contactNumber', model: 'ContactInformation' },
    // { path: 'contactAddress', select: '_id street city', model: 'AddressInformation' },

    if (this.basicUserModel.schema.paths['org']) {
      populateOptions.push({
        path: 'org',
        select:
          '_id title orgType description customers vendors isAdminOrg logo isOnboarded isApproved isUpdated',
        model: 'Org',
        populate: [
          {
            path: 'logo',
            select:
              '_id originalName uniqueName fileSize fileType locationUrl etag status',
            model: 'FileMetadata',
          },
          {
            path: 'contactAddress',
            select: 'apartment street city postalCode',
            model: 'AddressInformation',
          },
          { path: 'country', select: '_id countryName', model: 'Country' },
          { path: 'state', select: '_id stateName', model: 'State' },
          { path: 'city', select: '_id name', model: 'City' },
        ],
      });
    }

    if (this.basicUserModel.schema.paths['businessUnit']) {
      populateOptions.push({
        path: 'businessUnit',
        select: '_id label parentBusinessUnit type org breadcrumb',
        model: 'BusinessUnit',
      });
    }

    if (this.basicUserModel.schema.paths['userInboxConfig']) {
      populateOptions.push({
        path: 'userInboxConfig',
        select:
          '_id userName password  imapHost imapPort  smtpHost smtpPort fromEmail fromName',
        model: 'UserInboxConfig',
      });
    }

    if (this.basicUserModel.schema.paths['employee']) {
      populateOptions.push({
        path: 'employeeId',
        model: 'Employee',
        select:
          '_id firstName lastName jobTitle jobApplication email contactNumber employmentType payRollOrg endClientOrg',
        populate: [
          { path: 'payRollOrg', model: 'Org', select: '_id title' },
          { path: 'endClientOrg', model: 'Org', select: '_id title' },
          { path: 'job', model: 'Job', select: '_id title employmentType' },
          {
            path: 'jobApplication',
            model: 'JobApplication',
            select: '_id firstName lastName',
          },
        ],
      });
    }

    return populateOptions;
  }

  async getDefaultStatus(userType: string): Promise<Types.ObjectId> {
    const statuses = await this.statusService.findAllStatusByType(userType);
    if (!statuses || statuses.length === 0) {
      throw new BadRequestException('No status available to set as default');
    }
    return statuses[0]._id;
  }

  @OnEvent('org.createAdminUser', { async: true })
  async handleOrgAdminCreation(payload: any) {
    const { createdOrg, createOrgDto } = payload;
    try {
      const adminData = {
        email: createOrgDto.createUserDto.email,
        firstName: createOrgDto.createUserDto.firstName,
        lastName: createOrgDto.createUserDto.lastName,
        password: createOrgDto.createUserDto.password,
        roles: createOrgDto.createUserDto.roles,
        org: createdOrg._id,
        source: SourceType.LANDING_PAGE,
      };
      const newAdminUser = await this.create(adminData);
    } catch (error) {
      console.error(`Failed to create admin user: ${error.message}`);
    }
  }

  @OnEvent('org.createOrgAdmin', { async: true })
  async handleOrgAdmin(payload: any) {
    try {
      const adminData = {
        email: payload.email,
        firstName: payload.firstName,
        lastName: payload.lastName,
        password: payload.password,
        roles: payload.roles,
        org: payload.org,
        source: SourceType.ADMIN_PORTAL,
        isVerified: true,
        isApproved: true,
        sendPassword: true,
      };
      const isThere = await this.findUserByEmailExists(adminData.email);
      if (isThere) {
        this.emitEvent('customer.reApproved', isThere);
      } else {
        const newAdminUser = await this.create(adminData);
        this.logger.log(newAdminUser);
      }
    } catch (error) {
      console.error(`Failed to create admin user: ${error.message}`);
    }
  }

  @OnEvent('region.blocked', { async: true })
  async handleRegionBlockedForUser(payload: any) {
    const { region } = payload;

    try {
      if (region.state.stateName === 'ALL') {
        await this.basicUserModel.updateMany(
          { country: region.country._id },
          { isOperational: false },
        );
      } else {
        await this.basicUserModel.updateMany(
          { state: region.state._id },
          { isOperational: false },
        );
      }

      console.log(
        `Organizations for region ${region._id} are now non-operational.`,
      );
    } catch (error) {
      console.error(
        `Error while updating organizations for region ${region._id}:`,
        error,
      );
    }
  }

  @OnEvent('region.activated', { async: true })
  async handleRegionActivatedForUser(payload: any) {
    const { region } = payload;

    try {
      if (region.state.stateName === 'ALL') {
        await this.basicUserModel.updateMany(
          { country: region.country._id },
          { isOperational: true },
        );
      } else {
        await this.basicUserModel.updateMany(
          { state: region.state._id },
          { isOperational: true },
        );
      }

      this.logger.log(`Users for region ${region._id} are now operational.`);
    } catch (error) {
      this.logger.error(
        `Error while updating users for region ${region._id}:`,
        error,
      );
    }
  }

  // async assignJobToMember(memberId: string, memberJobAssignDto: MemberJobAssignDto) {
  //   try {
  //    const  memberObjId = validateObjectId(memberId);
  //     const member = await this.basicUserModel.findById(memberObjId);

  //     if(!member) {
  //       throw new NotFoundException(`Member with ID ${memberId} not found.`);
  //     }

  //     return this.taskService.assignJobToMember(memberId, memberJobAssignDto);
  //   } catch (error) {
  //     throw new InternalServerErrorException(`An error occurred while fetching member by Id ${memberId}. ${error?.message}.`);
  //   }
  // }

  // async getAssignedJobs(memberId: string, jobId: string) {
  //   try {
  //       const memberObjId = validateObjectId(memberId);
  //       const member = await this.basicUserModel.findById(memberObjId);

  //       if (!member) {
  //           throw new NotFoundException(`Member with ID ${memberId} not found.`);
  //       }

  //       return this.taskService.getAssignedJobs(memberObjId, jobId);
  //   } catch (error) {
  //       throw new InternalServerErrorException(`An error occurred while fetching assigned jobs for member ID ${memberId}. ${error?.message}.`);
  //   }
  // }

  // async unassignJob(memberId: string, jobId: string) {
  //   try {
  //       const memberObjId = validateObjectId(memberId);
  //       const member = await this.basicUserModel.findById(memberObjId);

  //       if (!member) {
  //           throw new NotFoundException(`Member with ID ${memberId} not found.`);
  //       }

  //       return this.taskService.unassignJob(memberObjId, jobId);
  //   } catch (error) {
  //       throw new InternalServerErrorException(`An error occurred while unassigning job ID ${jobId} from member ID ${memberId}. ${error?.message}.`);
  //   }
  // }

  @OnEvent('user-inbox-config.created', { async: true })
  async onUserInboxConfigCreated(payload: any) {
    try {
      const { createdUserInboxConfig, user } = payload;
      const foundUser = await this.findById(user?._id);
      foundUser.userInboxConfig = createdUserInboxConfig?._id;
      const dummy = await foundUser.save();
    } catch (error) {
      this.logger.error('Failed to set user config details in user', error);
    }
  }

  emitEvent(eventName: string, payload: any) {
    this.eventEmitter.emit(eventName, payload);
  }
}
