import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Types } from 'mongoose';
import { EndpointPermission } from 'src/endpoint-permissions/schemas/endpointpermissions.schema';
import { Org } from 'src/org/schemas/org.schema';
import { BasicUser } from 'src/user/schemas/basic-user.schema';


export type EndpointsRolesDocument = HydratedDocument<EndpointsRoles>;
@Schema({ timestamps: true })
export class EndpointsRoles {
    @Prop({ type: Types.ObjectId, ref: 'EndpointPermission', required: true })
    endPoint: EndpointPermission;

    @Prop({ type: [String],lowercase: true, required: false })
    roles?: string[];

    @Prop({
        type: Types.ObjectId,
        required: true,
        ref: 'Org'
    })
    org: Org;
}

export const EndpointsRolesSchema = SchemaFactory.createForClass(EndpointsRoles);
EndpointsRolesSchema.index({ endPoint: 1, org: 1 }, { unique: true });