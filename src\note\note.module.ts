import { Module, forwardRef } from '@nestjs/common';
import { NoteService } from './note.service';
import { NoteController } from './note.controller';
import { ConfigModule } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { UserModule } from 'src/user/user.module';
import { MongooseModule } from '@nestjs/mongoose';
import { Note, NoteSchema } from './schemas/note.schema';
import { AuthModule } from 'src/auth/auth.module';
import { OrgModule } from 'src/org/org.module';
import { EndpointsRolesModule } from 'src/endpoints-roles/endpoints-roles.module';
@Module({
  imports: [
    ConfigModule,
    JwtModule, EndpointsRolesModule,
    UserModule,
    AuthModule,
    OrgModule,
    MongooseModule.forFeature([{ name:Note.name, schema: NoteSchema }])
  ],
  controllers: [NoteController],
  providers: [NoteService],
})
export class NoteModule {}
