import { Injectable, InternalServerErrorException, Logger, NotFoundException } from "@nestjs/common";
import ical from "ical-generator";
import { Model, Types } from "mongoose";
import { ConfigService } from "@nestjs/config";
import { InjectModel } from "@nestjs/mongoose";
import { Blog } from "./schemas/blog.schema";
import { CreateBlogDto } from "./dto/create-blog.dto";
import { UpdateBlogDto } from "./dto/update-blog.dto";

@Injectable()
export class BlogService {

  private readonly logger = new Logger(BlogService.name);

  constructor(private configService: ConfigService, @InjectModel(Blog.name) private blogModel: Model<Blog>) { }

  async create(createBlogDto: CreateBlogDto) {
    try {
      const { title, type, description, status } = createBlogDto;
      const createdBlog = new this.blogModel(createBlogDto);

      const blog = await createdBlog.save();
      return blog.toString();
    }
    catch (error) {
      this.logger.error(`Failed to create an blog. ${error}`);
      throw new InternalServerErrorException(`Error when creating blog. ${error?.message}`);
    }
  }

  async update(blogId: Types.ObjectId, updateBlogDto: UpdateBlogDto) {
    try {
      const blog = await this.blogModel.findById(blogId)
      if (!blog) {
        throw new NotFoundException(`The blog details with id: "${blogId}" doesn't exist.`);
      }
      return await this.blogModel.findByIdAndUpdate(blogId, updateBlogDto, { new: true });
    }
    catch (error) {
      this.logger.error(`An error occurred while updating blog details by ID ${blogId}. ${error?.message}`);
      throw error;
    }
  }

  async findAll() {
    return await this.blogModel.find().sort({ createdAt: -1 }).exec();
  }


  async findOne(blogId: Types.ObjectId) {
    try {
      const blog = await this.blogModel.findById(blogId)
        .exec();

      if (!blog) {
        throw new NotFoundException(`Blog not found with ID ${blogId}`);
      }

      return blog;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      } else {
        throw new InternalServerErrorException(`Error while fetching blog with ID ${blogId}: ${error.message}`);
      }
    }
  }

}