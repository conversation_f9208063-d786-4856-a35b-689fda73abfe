import {
  HttpException,
  HttpStatus,
  Injectable,
  InternalServerErrorException,
} from '@nestjs/common';
import {
  NotFoundException,
  ForbiddenException,
  BadRequestException,
  UnprocessableEntityException,
  UnauthorizedException,
} from '@nestjs/common';
import { CreateJobDto } from './dto/create-job.dto';
import { UpdateJobDto } from './dto/update-job.dto';
import { ConfigService } from '@nestjs/config';
import { InjectModel } from '@nestjs/mongoose';
import { Job } from './schemas/job.schema';
import { Model, ObjectId, Types } from 'mongoose';
import { Logger } from '@nestjs/common';
import { JobsQueryDTO } from './dto/query-jobs.dto';
import { Stage } from 'src/stage/schemas/stage.schema';
import {
  Workflow,
  WorkflowDocument,
} from 'src/workflow/schemas/workflow.schema';
import * as fs from 'fs/promises';
import * as path from 'path';
import { ChangeStatusJobDto } from './dto/update-job-status.dto';
import { WorkflowService } from 'src/workflow/workflow.service';
import { BasicUser } from 'src/user/schemas/basic-user.schema';
import { MemberJobAssignDto } from '../task/dto/assign-job-to-member';
import { validateObjectId } from 'src/utils/validation.utils';
import moment from 'moment';
import { Interview } from 'src/interview/schemas/interview.schema';
import { Preference } from 'src/preferences/schemas/preference.schema';
import { JobApplication } from 'src/job-application-form/schemas/job-application.schema';
import { Role } from 'src/auth/enums/role.enum';
import {
  JobType,
  NotificationType,
  OrgType,
  StatusConfigType,
} from 'src/shared/constants';
import { Resumes } from 'src/resume/schemas/resume.schema';
import { Offer } from 'src/offer/schemas/offer.schema';
import { Org } from 'src/org/schemas/org.schema';
import { request } from 'http';
import { application } from 'express';
import { OrgModule } from 'src/org/org.module';
import { JobAllocationBase } from 'src/job-allocation/schemas/job-allocation-base.schema';
import { NotificationsService } from 'src/notification/notifications.service';

@Injectable()
export class JobService {
  //   select: '_id title legalName'
  // }

  private readonly logger = new Logger(JobService.name);

  constructor(
    private configService: ConfigService,
    @InjectModel(Job.name) private jobModel: Model<Job>,
    @InjectModel(Stage.name) private stageModel: Model<Stage>,
    @InjectModel(Workflow.name) private workflowModel: Model<Workflow>,
    private readonly workflowService: WorkflowService,
    @InjectModel(Interview.name) private interviewModel: Model<Interview>,
    @InjectModel('Preference')
    private readonly preferenceModel: Model<Preference>,
    @InjectModel(JobApplication.name)
    private jobApplicationModel: Model<JobApplication>,
    @InjectModel('Resumes') private readonly resumeModel: Model<Resumes>,
    @InjectModel(Offer.name) private offerModel: Model<Offer>,
    @InjectModel(Org.name) private orgsModel: Model<Org>,
    @InjectModel(JobAllocationBase.name)
    private jobAllocationBasesModel: Model<JobAllocationBase>,
    @InjectModel(BasicUser.name) private basicUserModel: Model<BasicUser>,
    private readonly notificationsService: NotificationsService,
  ) {}

  private readonly populateOptions = [
    {
      path: 'jobLocation',
      select: '_id city state county postalCode',
      model: 'JobLocation',
    },
    { path: 'industryOrDomain', select: '_id name code' },
    { path: 'createdBy', select: '_id roles firstName' },
    { path: 'postingOrg', select: '_id orgType title legalName' },
    { path: 'endClientOrg', select: '_id orgType title legalName' },
    { path: 'hiringOrg', select: '_id orgType title legalName' },
    { path: 'spoc', select: '_id firstName lastName' },
    {
      path: 'workflow',
      populate: [
        {
          path: 'stages',
          select: '_id name type sequenceNumber jobApplicationsCount',
        },
        // {
        //   path: 'org',
        //   select: '_id title legalName'
        // }
      ],
      select:
        '_id name department isDefault isActive stages totalJobApplicationsCount',
    },
    {
      path: 'instructionFile',
      select: '_id locationUrl originalName fileSize fileType',
      model: 'FileMetadata',
    },
    {
      path: 'rateCard',
      select: '_id name description fixedRate percentageOfCTC',
      model: 'RateCard',
    },
    {
      path: 'vendorRateCards.vendor',
      select: '_id title legalName orgType',
      model: 'Org',
    },
    {
      path: 'vendorRateCards.rateCard',
      select: '_id name description fixedRate percentageOfCTC',
      model: 'RateCard',
    },
  ];

  async create(createJobDto: CreateJobDto, user: any): Promise<Job> {
    let createdJob;

    try {
      this.logger.log(createJobDto.endClientOrg);

      // Clone the default workflow
      const clonedWorkflow = await this.workflowService.cloneDefaultWorkflow(
        createJobDto,
        user,
      );

      // Generate job code
      const jobCode = await this.generateJobCode();

      // Create job instance
      createdJob = new this.jobModel({
        ...createJobDto,
        workflow: clonedWorkflow._id,
        jobCode, // Assign generated job code
      });
      await createdJob.save();

      const job = await this.jobModel
        .findById(createdJob._id)
        .populate('endClientOrg')
        .populate('createdBy')
        .exec();
      const jobTitle = job?.title || 'a job';
      const createdName =
        (job?.createdBy?.firstName ?? '') +
          ' ' +
          (job?.createdBy?.lastName ?? '') || 'the admin';
      const message_1 = `A new job titled '${jobTitle}' has been created by ${createdName}.`;

      // notify the user who is removed with job
      await this.notificationsService.createAndNotify(
        createdJob.createdBy.toString(), // the person who created the job
        message_1,
        createdJob.createdBy.toString(), // the person who removed (could be admin/manager)
        {},
        NotificationType.JOB_CREATED,
        createdJob._id.toString(),
      );

      const updatedWorkFlow = await this.workflowService.updateJobIdInWorkFlow(
        clonedWorkflow._id,
        createdJob._id.toString(),
      );
    } catch (error) {
      this.logger.error('Failed to create job', error?.message);
      throw new InternalServerErrorException(
        `Unknown error when creating job. ${error.message}`,
      );
    }

    return createdJob;
  }

  private async generateJobCode(): Promise<string> {
    const date = new Date().toISOString().slice(0, 10).replace(/-/g, '');

    try {
      // Find the latest job with a jobCode starting with today's date
      const lastJob = await this.jobModel
        .findOne({ jobCode: new RegExp(`^TJ-${date}`) })
        .sort({ jobCode: -1 });

      const lastSequence = lastJob?.jobCode?.split('-')[2] ?? '000';

      const nextSequenceNumber = parseInt(lastSequence, 10) + 1;

      return `TJ-${date}-${nextSequenceNumber.toString().padStart(3, '0')}`;
    } catch (error) {
      this.logger.error('Error generating jobCode', error);
      throw new InternalServerErrorException('Error generating job code');
    }
  }

  // async findAll(user: any, query: JobsQueryDTO): Promise<Job[]> {
  //   try {
  //     const { isJobAllocation, postingOrg, endClientOrg, hiringOrg, locationIds, isOpen, isDraft, shareWithFreelancers, department, name, page = 1, limit = 10, jobType, workMode, employmentType, fromDate, toDate, matchedJobs, spoc, vendorId } = query;

  //     let conditions: any = { isDeleted: false }; // Default condition to exclude deleted jobs

  //     // Apply filters only if they are provided in the query
  //     if (postingOrg) {
  //       conditions.postingOrg = postingOrg;
  //     }
  //     if (hiringOrg) {
  //       conditions.hiringOrg = hiringOrg;
  //     }
  //     if (endClientOrg) {
  //       conditions.endClientOrg = endClientOrg;
  //     }
  //     if (locationIds && locationIds.length > 0) {
  //       conditions.jobLocation = { $in: locationIds };
  //     }
  //     if (department) {
  //       conditions.department = department;
  //     }
  //     if (isOpen !== undefined) {
  //       conditions.isOpen = isOpen;
  //     }

  //     if (isDraft !== undefined) {
  //       conditions.isDraft = isDraft;
  //     }

  //     if (shareWithFreelancers !== undefined) {
  //       conditions.shareWithFreelancers = shareWithFreelancers;
  //     }

  //     // if (shareWithVendors !== undefined) {
  //     //   conditions.shareWithVendors = shareWithVendors;
  //     // }

  //     if (jobType) {
  //       conditions.jobType = jobType
  //     }
  //     if (workMode) {
  //       conditions.workMode = workMode
  //     }
  //     if (employmentType) {
  //       conditions.employmentType = employmentType
  //     }

  //     if (fromDate) {
  //       conditions.createdAt = { ...conditions.createdAt, $gte: moment(fromDate).startOf('day').toDate() };
  //     }
  //     if (toDate) {
  //       conditions.createdAt = {
  //         ...conditions.createdAt,
  //         $lte: moment(toDate).endOf('day').toDate(),
  //       };
  //     }

  //     // Search by job title (case-insensitive)
  //     if (name) {
  //       const regex = new RegExp(name, 'i'); // 'i' for case-insensitive search
  //       conditions.title = regex;
  //     }

  //     if (spoc) {
  //       conditions.spoc = spoc;
  //     }

  //     if (vendorId) {
  //       conditions.vendors = { $in: new Types.ObjectId(vendorId) };
  //     }

  //     const isFreelancer = user.roles?.includes(Role.Freelancer);
  //     if (isFreelancer) {
  //       conditions.shareWithFreelancers = true;
  //       delete conditions.jobType;
  //     }

  //     let jobs = await this.jobModel.find(conditions)
  //       .populate(this.populateOptions)
  //       .sort({ createdAt: -1 })
  //       .skip((page - 1) * limit)
  //       .limit(limit)
  //       .exec();

  //     const isJobSeeker = user.roles?.includes(Role.JobSeeker);
  //     const managerRoles = [Role.AccountManager, Role.DeliveryManager, Role.ResourceManager];
  //     const isManager = user.roles?.some((role: Role) => managerRoles.includes(role));

  //     if (!isManager && isJobAllocation) {
  //       const queryConditions: any = {
  //         assignee: user._id.toString(),
  //         kind: 'JobAllocationToAssignees',
  //         isDeleted: false
  //       };
  //       const jobAllocations = await this.jobAllocationBasesModel.find(queryConditions).exec();
  //       const jobIds = jobAllocations.map(allocation => new Types.ObjectId(allocation.job.toString()));
  //       jobs = await this.jobModel.find({ _id: { $in: jobIds } })
  //         .populate(this.populateOptions)
  //         .sort({ createdAt: -1 })
  //         .skip((page - 1) * limit)
  //         .limit(limit)
  //         .exec();
  //       return jobs;
  //     }

  //     if (matchedJobs && isJobSeeker) {
  //       let externalJobs = await this.jobModel.find({ isDeleted: false, jobType: JobType.External, ...conditions })
  //         .populate({ path: 'jobLocation', select: '_id city state country postalCode', model: 'JobLocation' })
  //         .exec();

  //       // 2️⃣ Get user preferences (skills & locations & clients)
  //       const userPreferences = await this.preferenceModel.findOne({ createdBy: user._id }).exec();
  //       const userSkills = userPreferences?.skills?.map(skill => skill.skill.toLowerCase()) || [];
  //       const userLocations = userPreferences?.locations?.map(location => location.location.toLowerCase()) || [];
  //       const userClients = userPreferences?.clients?.map(client => client.clientId.toString()) || []; // Convert ObjectIds to strings

  //       // 3️⃣ Filter external jobs: Match by Skills, Locations, AND End Client Org
  //       let matchedJobs = externalJobs.filter(job => {
  //         const jobSkills = [...(job?.primarySkills ?? []), ...(job?.secondarySkills ?? [])].map(skill => skill.toLowerCase());
  //         const jobLocations = (job?.jobLocation ?? []).map(location => location.city.toLowerCase());
  //         const jobEndClientOrg = job?.endClientOrg ? job.endClientOrg.toString() : "";

  //         // Match jobs where either Skills AND Locations AND End Client Org match user preferences
  //         return (
  //           (jobSkills.some(skill => userSkills.includes(skill)) &&
  //             jobLocations.some(city => userLocations.includes(city)) &&
  //             userClients.includes(jobEndClientOrg)) // Check if job's endClientOrg is in userClients
  //         );
  //       });

  //       // 4️⃣ Populate `endClientOrg` after filtering
  //       matchedJobs = await this.jobModel.populate(matchedJobs, this.populateOptions);

  //       // this.logger.log(JSON.stringify(matchedJobs));
  //       // this.logger.log(`Matched ${matchedJobs.length} jobs for user ${user._id}`);
  //       jobs = matchedJobs;
  //     }

  //     const isRecruiter = user.roles?.includes(Role.Recruiter);

  //     const isVendor = user.roles?.includes(Role.Vendor);

  //     if (isRecruiter || isFreelancer) {
  //       const userApplications = await this.jobApplicationModel.find({ createdBy: user._id, isRejected: false }).exec();

  //       // 🔹 Create a map of job applications count per stage per job
  //       const applicationCountMap = new Map<string, Record<string, number>>();

  //       userApplications.forEach(application => {
  //         const jobId = application.jobId?.toString();
  //         const stageId = application.stage?.toString();

  //         if (!jobId || !stageId) {
  //           this.logger.log(`Skipping due to missing jobId or stageId: ${JSON.stringify(application)}`);
  //           return;
  //         }

  //         if (!applicationCountMap.has(jobId)) {
  //           applicationCountMap.set(jobId, {});
  //         }

  //         if (!applicationCountMap.get(jobId)![stageId]) {
  //           applicationCountMap.get(jobId)![stageId] = 0;
  //         }

  //         applicationCountMap.get(jobId)![stageId]++;

  //         // Log after updating
  //         // this.logger.log(`Updated applicationCountMap: ${JSON.stringify(Object.fromEntries(applicationCountMap))}`);
  //       });

  //       // 🔹 Update workflow stages count based on the applications
  //       const jobsWithUpdatedStages = jobs.map(job => {
  //         const jobId = job._id.toString();
  //         const stageCounts = applicationCountMap.get(jobId) || {};

  //         const updatedStages = job.workflow.stages.map(stage => ({
  //           ...(stage as any).toObject(),
  //           jobApplicationsCount: stageCounts[stage._id.toString()] || 0
  //         }));

  //         const totalJobApplicationsCount = job.workflow.stages.reduce(
  //           (sum, stage) => sum + ((stage as any).jobApplicationsCount || 0),
  //           0
  //         );

  //         return {
  //           ...job.toObject(),
  //           workflow: {
  //             ...(job.workflow as any).toObject(),
  //             stages: updatedStages
  //           },
  //           totalJobApplicationsCount
  //         };
  //       });

  //       return jobsWithUpdatedStages;
  //     }

  //     // After fetching jobs, calculate totalJobApplicationsCount for each job
  //     const jobsWithTotalApplications = jobs.map((job) => {
  //       const totalJobApplicationsCount = job.workflow.stages.reduce(
  //         (sum: number, stage: any) => sum + (stage.jobApplicationsCount || 0),
  //         0
  //       );

  //       return {
  //         ...job.toObject(),
  //         totalJobApplicationsCount, // Add the computed field
  //       };
  //     });

  //     // const isVendor = user.roles?.includes(Role.Vendor);
  //     if (isJobSeeker) {
  //       const jobIds = jobs.map(job => job._id.toString());

  //       const userApplications = await this.jobApplicationModel.find({
  //         jobId: { $in: jobIds },
  //         createdBy: user._id,
  //         isRejected: false
  //       }).exec();

  //       const appliedJobIds = new Set(userApplications.map(app => app.jobId.toString()));

  //       // Add 'hasApplied' to each job
  //       return jobsWithTotalApplications.map(job => ({
  //         ...job,
  //         hasApplied: appliedJobIds.has(job._id.toString())
  //       }));
  //     }

  //     if (isVendor) {
  //       //for Freelancing in Vendors
  //       if (shareWithFreelancers && postingOrg == undefined) {
  //         delete conditions.jobType;
  //         const jobs = await this.jobModel.find(conditions)
  //           .populate(this.populateOptions)
  //           .sort({ createdAt: -1 })
  //           .skip((page - 1) * limit)
  //           .limit(limit)
  //           .exec();
  //         const userApplications = await this.jobApplicationModel.find({ createdBy: user._id, isRejected: false }).exec();

  //         // 🔹 Create a map of job applications count per stage per job
  //         const applicationCountMap = new Map<string, Record<string, number>>();

  //         userApplications.forEach(application => {
  //           const jobId = application.jobId?.toString();
  //           const stageId = application.stage?.toString();

  //           if (!jobId || !stageId) {
  //             this.logger.log(`Skipping due to missing jobId or stageId: ${JSON.stringify(application)}`);
  //             return;
  //           }

  //           if (!applicationCountMap.has(jobId)) {
  //             applicationCountMap.set(jobId, {});
  //           }

  //           if (!applicationCountMap.get(jobId)![stageId]) {
  //             applicationCountMap.get(jobId)![stageId] = 0;
  //           }

  //           applicationCountMap.get(jobId)![stageId]++;

  //           // Log after updating
  //           // this.logger.log(`Updated applicationCountMap: ${JSON.stringify(Object.fromEntries(applicationCountMap))}`);
  //         });

  //         // 🔹 Update workflow stages count based on the applications
  //         const jobsWithUpdatedStages = jobs.map(job => {
  //           const jobId = job._id.toString();
  //           const stageCounts = applicationCountMap.get(jobId) || {};

  //           const updatedStages = job.workflow.stages.map(stage => ({
  //             ...(stage as any).toObject(),
  //             jobApplicationsCount: stageCounts[stage._id.toString()] || 0
  //           }));

  //           const totalJobApplicationsCount = job.workflow.stages.reduce(
  //             (sum, stage) => sum + ((stage as any).jobApplicationsCount || 0),
  //             0
  //           );

  //           return {
  //             ...job.toObject(),
  //             workflow: {
  //               ...(job.workflow as any).toObject(),
  //               stages: updatedStages
  //             },
  //             totalJobApplicationsCount
  //           };
  //         });
  //         return jobsWithUpdatedStages;
  //       }
  //       //for marketPlace in Vendors
  //       if (shareWithFreelancers == undefined && postingOrg == undefined) {
  //         conditions.shareWithFreelancers = true;
  //         delete conditions.jobType;
  //         const jobs = await this.jobModel.find(conditions)
  //           .populate(this.populateOptions)
  //           .sort({ createdAt: -1 })
  //           .skip((page - 1) * limit)
  //           .limit(limit)
  //           .exec();
  //         const jobIds = jobs.map(job => job._id.toString());
  //         console.log("jobIds", jobIds);

  //         const userApplications = await this.jobApplicationModel.find({
  //           jobId: { $in: jobIds },
  //           createdBy: user._id,
  //           isRejected: false
  //         }).exec();

  //         const applicationCountMap = new Map<string, Record<string, number>>();

  //         userApplications.forEach(application => {
  //           const jobId = application.jobId?.toString();
  //           const stageId = application.stage?.toString();

  //           if (!jobId || !stageId) {
  //             this.logger.log(`Skipping due to missing jobId or stageId: ${JSON.stringify(application)}`);
  //             return;
  //           }

  //           if (!applicationCountMap.has(jobId)) {
  //             applicationCountMap.set(jobId, {});
  //           }

  //           if (!applicationCountMap.get(jobId)![stageId]) {
  //             applicationCountMap.get(jobId)![stageId] = 0;
  //           }

  //           applicationCountMap.get(jobId)![stageId]++;

  //           // Log after updating
  //           // this.logger.log(`Updated applicationCountMap: ${JSON.stringify(Object.fromEntries(applicationCountMap))}`);
  //         });

  //         // 🔹 Update workflow stages count based on the applications
  //         const jobsWithUpdatedStages = jobs.map(job => {
  //           const jobId = job._id.toString();
  //           const stageCounts = applicationCountMap.get(jobId) || {};

  //           const updatedStages = job.workflow.stages.map(stage => ({
  //             ...(stage as any).toObject(),
  //             jobApplicationsCount: stageCounts[stage._id.toString()] || 0
  //           }));

  //           const totalJobApplicationsCount = job.workflow.stages.reduce(
  //             (sum, stage) => sum + ((stage as any).jobApplicationsCount || 0),
  //             0
  //           );

  //           return {
  //             ...job.toObject(),
  //             workflow: {
  //               ...(job.workflow as any).toObject(),
  //               stages: updatedStages
  //             },
  //             totalJobApplicationsCount
  //           };
  //         });

  //         const appliedJobIds = new Set(userApplications.map(app => app.jobId.toString()));
  //         // Add 'hasApplied' to each job
  //         return jobsWithUpdatedStages.map(job => ({
  //           ...job,
  //           hasApplied: appliedJobIds.has(job._id.toString())
  //         }));
  //       }
  //       //for Lists in Vendors
  //       if (shareWithFreelancers == undefined && postingOrg !== undefined) {
  //         console.log(user.org._id)
  //         const externalJobs = await this.jobModel.find(conditions)
  //           .populate(this.populateOptions)
  //           .sort({ createdAt: -1 })
  //           .skip((page - 1) * limit)
  //           .limit(limit)
  //           .exec();
  //         console.log("externalJobs", externalJobs)
  //         delete conditions.jobType;
  //         conditions.vendors = { $in: new Types.ObjectId(user.org._id) };
  //         // conditions.$or = [{ vendors: { $in: [new Types.ObjectId(vendorId)] } }];
  //         let vendorJobs = await this.jobModel.find(conditions)
  //           .populate(this.populateOptions)
  //           .sort({ createdAt: -1 })
  //           .skip((page - 1) * limit)
  //           .limit(limit)
  //           .exec();
  //         console.log("vendorJobs", vendorJobs)

  //         const allJobs = [...externalJobs, ...vendorJobs];

  //         // Create a Map to store unique jobs by _id
  //         const uniqueJobsMap = new Map<string, any>();

  //         allJobs.forEach((job) => {
  //           uniqueJobsMap.set(job._id.toString(), job);
  //         });

  //         // Convert back to an array
  //         const jobs = Array.from(uniqueJobsMap.values());

  //         console.log("uniqueJobs", jobs);
  //         const jobIds = jobs.map(job => job._id.toString());
  //         console.log("jobIds", jobIds);

  //         const userApplications = await this.jobApplicationModel.find({
  //           jobId: { $in: jobIds },
  //           createdBy: user._id,
  //           isRejected: false
  //         }).exec();

  //         const applicationCountMap = new Map<string, Record<string, number>>();

  //         userApplications.forEach(application => {
  //           const jobId = application.jobId?.toString();
  //           const stageId = application.stage?.toString();

  //           if (!jobId || !stageId) {
  //             this.logger.log(`Skipping due to missing jobId or stageId: ${JSON.stringify(application)}`);
  //             return;
  //           }

  //           if (!applicationCountMap.has(jobId)) {
  //             applicationCountMap.set(jobId, {});
  //           }

  //           if (!applicationCountMap.get(jobId)![stageId]) {
  //             applicationCountMap.get(jobId)![stageId] = 0;
  //           }

  //           applicationCountMap.get(jobId)![stageId]++;

  //           // Log after updating
  //           // this.logger.log(`Updated applicationCountMap: ${JSON.stringify(Object.fromEntries(applicationCountMap))}`);
  //         });

  //         // 🔹 Update workflow stages count based on the applications
  //         const jobsWithUpdatedStages = jobs.map(job => {
  //           const jobId = job._id.toString();
  //           const stageCounts = applicationCountMap.get(jobId) || {};

  //           const updatedStages = job.workflow.stages.map((stage: { _id: { toString: () => string | number; }; }) => ({
  //             ...(stage as any).toObject(),
  //             jobApplicationsCount: stageCounts[stage._id.toString()] || 0
  //           }));

  //           const totalJobApplicationsCount = job.workflow.stages.reduce(
  //             (sum: any, stage: any) => sum + ((stage as any).jobApplicationsCount || 0),
  //             0
  //           );

  //           return {
  //             ...job.toObject(),
  //             workflow: {
  //               ...(job.workflow as any).toObject(),
  //               stages: updatedStages
  //             },
  //             totalJobApplicationsCount
  //           };
  //         });

  //         // const appliedJobIds = new Set(userApplications.map(app => app.jobId.toString()));
  //         // // Add 'hasApplied' to each job
  //         // return jobsWithUpdatedStages.map(job => ({
  //         //   ...job,
  //         //   hasApplied: appliedJobIds.has(job._id.toString())
  //         // }));
  //         return jobsWithUpdatedStages
  //       }

  //     }

  //     return jobsWithTotalApplications;
  //   } catch (error) {
  //     this.logger.error(error);
  //     throw new InternalServerErrorException(`An error occurred while retrieving jobs. ${error?.message}`);
  //   }
  // }

  async getValidSubordinatesRecursively(
    managerId: string,
    jobId: string,
    allUsersMap: Map<string, any>,
    allocationMap: Map<string, any[]>,
  ): Promise<Set<string>> {
    const validSubordinateIds = new Set<string>();

    for (const [userId, user] of allUsersMap) {
      if (!user.reportingTo?.includes(managerId)) continue;

      const userAllocations = allocationMap.get(userId) || [];
      const isValid = userAllocations.some(
        (a) =>
          a.job?.toString() === jobId && a.createdBy?.toString() === managerId,
      );

      if (!isValid) continue;

      validSubordinateIds.add(userId);

      const childSubs = await this.getValidSubordinatesRecursively(
        userId,
        jobId,
        allUsersMap,
        allocationMap,
      );
      childSubs.forEach((id) => validSubordinateIds.add(id));
    }

    return validSubordinateIds;
  }

  async findAll(user: any, query: JobsQueryDTO): Promise<Job[]> {
    try {
      const {
        isJobAllocation,
        postingOrg,
        endClientOrg,
        hiringOrg,
        locationIds,
        isOpen,
        isDraft,
        shareWithFreelancers,
        department,
        name,
        page = 1,
        limit = 10,
        jobType,
        workMode,
        employmentType,
        fromDate,
        toDate,
        matchedJobs,
        spoc,
        vendorId,
      } = query;

      let conditions: any = { isDeleted: false }; // Default condition to exclude deleted jobs

      // Apply filters only if they are provided in the query
      if (postingOrg) {
        conditions.postingOrg = postingOrg;
      }
      if (hiringOrg) {
        // conditions.hiringOrg = hiringOrg;
        conditions.endClientOrg = hiringOrg;
      }
      if (endClientOrg) {
        conditions.endClientOrg = endClientOrg;
      }
      if (locationIds && locationIds.length > 0) {
        conditions.jobLocation = { $in: locationIds };
      }
      if (department) {
        conditions.department = department;
      }
      if (isOpen !== undefined) {
        conditions.isOpen = isOpen;
      }

      if (isDraft !== undefined) {
        conditions.isDraft = isDraft;
      }

      if (shareWithFreelancers !== undefined) {
        conditions.shareWithFreelancers = shareWithFreelancers;
      }

      // if (shareWithVendors !== undefined) {
      //   conditions.shareWithVendors = shareWithVendors;
      // }
      console.log('jobType', jobType);
      if (jobType) {
        conditions.jobType = jobType;
      }
      if (workMode) {
        conditions.workMode = workMode;
      }
      if (employmentType) {
        conditions.employmentType = employmentType;
      }

      if (fromDate) {
        conditions.createdAt = {
          ...conditions.createdAt,
          $gte: moment(fromDate).startOf('day').toDate(),
        };
      }
      if (toDate) {
        conditions.createdAt = {
          ...conditions.createdAt,
          $lte: moment(toDate).endOf('day').toDate(),
        };
      }

      // Search by job title (case-insensitive)
      if (name) {
        const regex = new RegExp(name, 'i'); // 'i' for case-insensitive search
        conditions.title = regex;
      }

      if (spoc) {
        conditions.spoc = spoc;
      }

      if (vendorId) {
        conditions.vendors = { $in: new Types.ObjectId(vendorId) };
      }

      const isFreelancer = user.roles?.includes(Role.Freelancer);
      if (isFreelancer) {
        conditions.shareWithFreelancers = true;
        delete conditions.jobType;
      }

      let jobOrConditions: any[] = [];

      console.log('conditions', conditions);
      let jobs = await this.jobModel
        .find(conditions)
        .populate(this.populateOptions)
        .sort({ createdAt: -1 })
        .skip((page - 1) * limit)
        .limit(limit)
        .exec();

      // Handle Admin & DeliveryManager
      if (
        user.roles?.includes(Role.Admin) ||
        user.roles?.includes(Role.DeliveryManager)
      ) {
        conditions.postingOrg = user.org._id.toString();
        let vendorJobsList: Job[] = [];
        delete conditions.jobType;
        if (user.roles?.includes(Role.Vendor)) {
          //for Lists in Vendors
          if (shareWithFreelancers == undefined && postingOrg !== undefined) {
            // console.log(user.org._id)

            // Step 1: Get all matching vendor orgs
            const existingOrgs = await this.orgsModel
              .find({
                'contactDetails.contactEmail': user.email,
                isDeleted: false,
                orgType: OrgType.VENDOR_ORG,
              })
              .exec();

            // Step 2: Extract org _ids
            const vendorOrgIds = existingOrgs.map((org) => org._id.toString());
            // console.log("vendorOrgIds", vendorOrgIds);
            // Step 3: Prepare allocation query
            const queryConditions: any = {
              kind: 'JobAllocationToVendors',
              isDeleted: false,
              vendor: { $in: vendorOrgIds }, // Match any of the vendor orgs
            };
            // Step 4: Query allocations
            const jobAllocations = await this.jobAllocationBasesModel
              .find(queryConditions)
              .exec();
            const allocatedjobIds = jobAllocations.map(
              (allocation) => new Types.ObjectId(allocation.job.toString()),
            );
            let vendorJobs = await this.jobModel
              .find({ _id: { $in: allocatedjobIds } })
              .populate(this.populateOptions)
              .sort({ createdAt: -1 })
              .skip((page - 1) * limit)
              .limit(limit)
              .exec();

            const allJobs = [...vendorJobs];

            // Create a Map to store unique jobs by _id
            const uniqueJobsMap = new Map<string, any>();

            allJobs.forEach((job) => {
              uniqueJobsMap.set((job as any)._id.toString(), job);
            });

            // Convert back to an array
            const jobs = Array.from(uniqueJobsMap.values());

            // console.log("uniqueJobs", jobs);
            const jobIds = jobs.map((job) => job._id.toString());
            // console.log("jobIds", jobIds);

            const userApplications = await this.jobApplicationModel
              .find({
                jobId: { $in: jobIds },
                createdBy: user._id,
                isRejected: false,
              })
              .exec();

            const applicationCountMap = new Map<
              string,
              Record<string, number>
            >();

            userApplications.forEach((application) => {
              const jobId = application.jobId?.toString();
              const stageId = application.stage?.toString();

              if (!jobId || !stageId) {
                this.logger.log(
                  `Skipping due to missing jobId or stageId: ${JSON.stringify(application)}`,
                );
                return;
              }

              if (!applicationCountMap.has(jobId)) {
                applicationCountMap.set(jobId, {});
              }

              if (!applicationCountMap.get(jobId)![stageId]) {
                applicationCountMap.get(jobId)![stageId] = 0;
              }

              applicationCountMap.get(jobId)![stageId]++;

              // Log after updating
              // this.logger.log(`Updated applicationCountMap: ${JSON.stringify(Object.fromEntries(applicationCountMap))}`);
            });

            // 🔹 Update workflow stages count based on the applications
            const jobsWithUpdatedStages = jobs.map((job) => {
              const jobId = job._id.toString();
              const stageCounts = applicationCountMap.get(jobId) || {};

              const updatedStages = job.workflow.stages.map(
                (stage: { _id: { toString: () => string | number } }) => ({
                  ...(stage as any).toObject(),
                  jobApplicationsCount: stageCounts[stage._id.toString()] || 0,
                }),
              );

              const totalJobApplicationsCount = job.workflow.stages.reduce(
                (sum: any, stage: any) =>
                  sum + ((stage as any).jobApplicationsCount || 0),
                0,
              );

              return {
                ...job.toObject(),
                workflow: {
                  ...(job.workflow as any).toObject(),
                  stages: updatedStages,
                },
                totalJobApplicationsCount,
              };
            });

            vendorJobsList = jobsWithUpdatedStages;
            // return jobsWithUpdatedStages
          }
        }
        // console.log("conditions", conditions);
        console.log('console conditions', conditions);
        jobs = await this.jobModel
          .find(conditions)
          .populate(this.populateOptions)
          .sort({ createdAt: -1 })
          .skip((page - 1) * limit)
          .limit(limit)
          .exec();
        // After fetching jobs, calculate totalJobApplicationsCount for each job
        const jobsWithTotalApplications = jobs.map((job) => {
          const totalJobApplicationsCount = job.workflow.stages.reduce(
            (sum: number, stage: any) =>
              sum + (stage.jobApplicationsCount || 0),
            0,
          );

          return {
            ...job.toObject(),
            totalJobApplicationsCount, // Add the computed field
          };
        });

        // console.log("jobsWithTotalApplications", jobsWithTotalApplications);
        // Combine vendorJobs and jobs
        const allJobs = [...vendorJobsList, ...jobsWithTotalApplications];

        // Create a Map to ensure uniqueness by _id
        const uniqueJobsMap = new Map<string, any>();
        allJobs.forEach((job) => {
          uniqueJobsMap.set((job as any)._id.toString(), job);
        });

        // Convert the map values back to array
        const mergedUniqueJobs = Array.from(uniqueJobsMap.values());

        return mergedUniqueJobs;
        // return jobsWithTotalApplications;
      }
      // Handle AccountManager: jobs created by self OR assigned orgs
      let accountManagerJobs: Job[] = [];
      if (user.roles?.includes(Role.AccountManager)) {
        // Step 2: Get orgs assigned to this user
        const assignedOrgs = await this.orgsModel
          .find({
            assignTo: user?._id?.toString(),
            isDeleted: false,
            orgType: { $in: [OrgType.ACCOUNT_ORG, OrgType.CUSTOMER_ORG] },
            createdByOrg: user.org?._id?.toString(),
          })
          .select('_id')
          .lean();
        // console.log('Assigned Orgs:', assignedOrgs);

        // Step 2: Get orgs created by the current user
        const createdOrgs = await this.orgsModel
          .find({
            createdBy: user._id.toString(),
            isDeleted: false,
            orgType: { $in: [OrgType.ACCOUNT_ORG, OrgType.CUSTOMER_ORG] },
            createdByOrg: user.org?._id?.toString(),
          })
          .select('_id')
          .lean();

        // console.log('Created Orgs:', createdOrgs);

        // const assignedOrgIds = assignedOrgs.map(org => org._id.toString());

        // Step 3: Merge and deduplicate
        const assignedOrgIdsSet = new Set<string>();

        assignedOrgs.forEach((org) =>
          assignedOrgIdsSet.add(org._id.toString()),
        );
        createdOrgs.forEach((org) => assignedOrgIdsSet.add(org._id.toString()));

        const assignedOrgIds = Array.from(assignedOrgIdsSet);
        // console.log('Assigned Org IDs:', assignedOrgIds);

        // Step 4: Add OR condition to fetch jobs createdBy the user or from assigned orgs
        // conditions.$or = [
        //   { createdBy: user._id.toString() }, // createdBy refers to job.createdBy
        //   { endClientOrg: { $in: assignedOrgIds } } // org refers to job.endClientOrg
        // ];
        jobOrConditions.push(
          { createdBy: user._id.toString() },
          { endClientOrg: { $in: assignedOrgIds } },
        );
        // console.log('Final Conditions for AccountManager:', conditions);
        // Final condition with OR merged
        const finalConditions = {
          ...conditions,
          $or: jobOrConditions,
        };

        // console.log('Final Conditions:', finalConditions);

        jobs = await this.jobModel
          .find(finalConditions)
          .populate(this.populateOptions)
          .sort({ createdAt: -1 })
          .skip((page - 1) * limit)
          .limit(limit)
          .exec();

        // After fetching jobs, calculate totalJobApplicationsCount for each job
        const jobsWithTotalApplications = jobs.map((job) => {
          const totalJobApplicationsCount = job.workflow.stages.reduce(
            (sum: number, stage: any) =>
              sum + (stage.jobApplicationsCount || 0),
            0,
          );

          return {
            ...job.toObject(),
            totalJobApplicationsCount, // Add the computed field
          };
        });

        accountManagerJobs = jobsWithTotalApplications;
      }

      const userRoles = user.roles || [];
      const hasNonAccountRoles = userRoles.some(
        (role: Role) => role !== Role.AccountManager,
      );
      let nonAccountManagerJobs: Job[] = [];
      if (
        hasNonAccountRoles &&
        !user.roles?.includes(Role.Freelancer) &&
        !user.roles?.includes(Role.JobSeeker) &&
        !user.roles?.includes(Role.Vendor)
      ) {
        // Jobs created by this user
        jobOrConditions.push({ createdBy: user._id.toString() });

        // Jobs assigned to this user via job allocations
        const jobAllocations = await this.jobAllocationBasesModel
          .find({
            assignee: user._id.toString(),
            kind: 'JobAllocationToAssignees',
            isDeleted: false,
          })
          .exec();

        const allocatedJobIds = jobAllocations.map(
          (allocation) => new Types.ObjectId(allocation.job.toString()),
        );
        if (allocatedJobIds.length) {
          jobOrConditions.push({ _id: { $in: allocatedJobIds } });
        }

        // Final condition with OR merged
        const finalConditions = {
          ...conditions,
          $or: jobOrConditions,
        };

        // console.log('Final Conditions:', finalConditions);

        jobs = await this.jobModel
          .find(finalConditions)
          .populate(this.populateOptions)
          .sort({ createdAt: -1 })
          .skip((page - 1) * limit)
          .limit(limit)
          .exec();

        // async buildJobsWithApplicationCounts(user: any, jobs: any[]) {
        const jobIds = jobs.map((job) => job._id.toString());

        // Fetch all users once
        const allUsers = await this.basicUserModel
          .find({ isDeleted: false, org: user.org._id.toString() })
          .lean();
        const allUsersMap = new Map(allUsers.map((u) => [u._id.toString(), u]));

        // Fetch allocations once
        const allocations = (await this.jobAllocationBasesModel
          .find({
            job: { $in: jobIds },
            kind: 'JobAllocationToAssignees',
            isDeleted: false,
          })
          .lean()) as any[];
        const allocationMap = new Map<string, any[]>();
        for (const allocation of allocations) {
          const assigneeId = allocation.assignee.toString();
          if (!allocationMap.has(assigneeId)) allocationMap.set(assigneeId, []);
          allocationMap.get(assigneeId)!.push(allocation);
        }

        const jobsWithCounts = [];

        for (const job of jobs) {
          const jobId = job._id.toString();
          const isOwner = job.createdBy?.toString() === user._id?.toString();

          if (isOwner) {
            // Use existing counts
            const totalJobApplicationsCount = job.workflow.stages.reduce(
              (sum: any, stage: any) =>
                sum + ((stage as any).jobApplicationsCount || 0),
              0,
            );
            jobsWithCounts.push({
              ...job.toObject(),
              totalJobApplicationsCount,
            });
            continue;
          }

          // Get valid subordinates
          const validSubordinates = await this.getValidSubordinatesRecursively(
            user._id.toString(),
            jobId,
            allUsersMap,
            allocationMap,
          );

          const creatorIds = [
            user._id?.toString(),
            ...Array.from(validSubordinates),
          ];
          // console.log('Creator IDs:', creatorIds);
          const relatedApplications = await this.jobApplicationModel
            .find({
              createdBy: { $in: creatorIds },
              jobId: jobId,
              isRejected: false,
              isDeleted: false,
            })
            .lean();

          const applicationCountMap = new Map<string, number>();
          for (const app of relatedApplications) {
            const stageId = app.stage?.toString();
            if (!stageId) continue;
            applicationCountMap.set(
              stageId,
              (applicationCountMap.get(stageId) || 0) + 1,
            );
          }

          const updatedStages = job.workflow.stages.map((stage) => {
            const stageObj = (stage as any).toObject();
            return {
              ...stageObj,
              jobApplicationsCount:
                applicationCountMap.get(stageObj._id.toString()) || 0,
            };
          });

          const totalJobApplicationsCount = updatedStages.reduce(
            (sum: any, stage: { jobApplicationsCount: any }) =>
              sum + (stage.jobApplicationsCount || 0),
            0,
          );

          jobsWithCounts.push({
            ...job.toObject(),
            workflow: {
              ...(job.workflow as any).toObject(),
              stages: updatedStages,
            },
            totalJobApplicationsCount,
          });
        }

        nonAccountManagerJobs = jobsWithCounts;
      }

      const combinedJobsMap = new Map<string, any>();

      // Add accountManagerJobs first (they take priority)
      for (const job of accountManagerJobs) {
        combinedJobsMap.set((job as any)._id.toString(), job);
      }

      // Add nonAccountManagerJobs only if not already present
      for (const job of nonAccountManagerJobs) {
        const jobId = (job as any)._id.toString();
        if (!combinedJobsMap.has(jobId)) {
          combinedJobsMap.set(jobId, job);
        }
      }
      // Convert map back to array
      const finalJobs = Array.from(combinedJobsMap.values());

      const isJobSeeker = user.roles?.includes(Role.JobSeeker);
      const managerRoles = [
        Role.AccountManager,
        Role.DeliveryManager,
        Role.ResourceManager,
      ];
      const isManager = user.roles?.some((role: Role) =>
        managerRoles.includes(role),
      );

      if (!isManager && isJobAllocation) {
        const queryConditions: any = {
          assignee: user._id.toString(),
          kind: 'JobAllocationToAssignees',
          isDeleted: false,
        };
        const jobAllocations = await this.jobAllocationBasesModel
          .find(queryConditions)
          .exec();
        const jobIds = jobAllocations.map(
          (allocation) => new Types.ObjectId(allocation.job.toString()),
        );
        jobs = await this.jobModel
          .find({ _id: { $in: jobIds } })
          .populate(this.populateOptions)
          .sort({ createdAt: -1 })
          .skip((page - 1) * limit)
          .limit(limit)
          .exec();
        return jobs;
      }

      if (matchedJobs && isJobSeeker) {
        let externalJobs = await this.jobModel
          .find({ isDeleted: false, jobType: JobType.External, ...conditions })
          .populate({
            path: 'jobLocation',
            select: '_id city state country postalCode',
            model: 'JobLocation',
          })
          .exec();

        // 2️⃣ Get user preferences (skills & locations & clients)
        const userPreferences = await this.preferenceModel
          .findOne({ createdBy: user._id })
          .exec();
        const userSkills =
          userPreferences?.skills?.map((skill) => skill.skill.toLowerCase()) ||
          [];
        const userLocations =
          userPreferences?.locations?.map((location) =>
            location.location.toLowerCase(),
          ) || [];
        const userClients =
          userPreferences?.clients?.map((client) =>
            client.clientId.toString(),
          ) || []; // Convert ObjectIds to strings

        // 3️⃣ Filter external jobs: Match by Skills, Locations, AND End Client Org
        let matchedJobs = externalJobs.filter((job) => {
          const jobSkills = [
            ...(job?.primarySkills ?? []),
            ...(job?.secondarySkills ?? []),
          ].map((skill) => skill.toLowerCase());
          const jobLocations = (job?.jobLocation ?? []).map((location) =>
            location.city.toLowerCase(),
          );
          const jobEndClientOrg = job?.endClientOrg
            ? job.endClientOrg.toString()
            : '';

          // Match jobs where either Skills AND Locations AND End Client Org match user preferences
          return (
            jobSkills.some((skill) => userSkills.includes(skill)) &&
            jobLocations.some((city) => userLocations.includes(city)) &&
            userClients.includes(jobEndClientOrg) // Check if job's endClientOrg is in userClients
          );
        });

        // 4️⃣ Populate `endClientOrg` after filtering
        matchedJobs = await this.jobModel.populate(
          matchedJobs,
          this.populateOptions,
        );

        // this.logger.log(JSON.stringify(matchedJobs));
        // this.logger.log(`Matched ${matchedJobs.length} jobs for user ${user._id}`);
        jobs = matchedJobs;
      }

      const isRecruiter = user.roles?.includes(Role.Recruiter);

      const isVendor = user.roles?.includes(Role.Vendor);

      if (isFreelancer) {
        const userApplications = await this.jobApplicationModel
          .find({ createdBy: user._id, isRejected: false })
          .exec();

        // 🔹 Create a map of job applications count per stage per job
        const applicationCountMap = new Map<string, Record<string, number>>();

        userApplications.forEach((application) => {
          const jobId = application.jobId?.toString();
          const stageId = application.stage?.toString();

          if (!jobId || !stageId) {
            this.logger.log(
              `Skipping due to missing jobId or stageId: ${JSON.stringify(application)}`,
            );
            return;
          }

          if (!applicationCountMap.has(jobId)) {
            applicationCountMap.set(jobId, {});
          }

          if (!applicationCountMap.get(jobId)![stageId]) {
            applicationCountMap.get(jobId)![stageId] = 0;
          }

          applicationCountMap.get(jobId)![stageId]++;

          // Log after updating
          // this.logger.log(`Updated applicationCountMap: ${JSON.stringify(Object.fromEntries(applicationCountMap))}`);
        });

        // 🔹 Update workflow stages count based on the applications
        const jobsWithUpdatedStages = jobs.map((job) => {
          const jobId = job._id.toString();
          const stageCounts = applicationCountMap.get(jobId) || {};

          const updatedStages = job.workflow.stages.map((stage) => ({
            ...(stage as any).toObject(),
            jobApplicationsCount: stageCounts[stage._id.toString()] || 0,
          }));

          const totalJobApplicationsCount = job.workflow.stages.reduce(
            (sum, stage) => sum + ((stage as any).jobApplicationsCount || 0),
            0,
          );

          return {
            ...job.toObject(),
            workflow: {
              ...(job.workflow as any).toObject(),
              stages: updatedStages,
            },
            totalJobApplicationsCount,
          };
        });

        return jobsWithUpdatedStages;
      }

      // After fetching jobs, calculate totalJobApplicationsCount for each job
      const jobsWithTotalApplications = jobs.map((job) => {
        const totalJobApplicationsCount = job.workflow.stages.reduce(
          (sum: number, stage: any) => sum + (stage.jobApplicationsCount || 0),
          0,
        );

        return {
          ...job.toObject(),
          totalJobApplicationsCount, // Add the computed field
        };
      });

      // const isVendor = user.roles?.includes(Role.Vendor);
      if (isJobSeeker) {
        const jobIds = jobs.map((job) => job._id.toString());

        const userApplications = await this.jobApplicationModel
          .find({
            jobId: { $in: jobIds },
            createdBy: user._id,
            isRejected: false,
          })
          .exec();

        const appliedJobIds = new Set(
          userApplications.map((app) => app.jobId.toString()),
        );

        // Add 'hasApplied' to each job
        return jobsWithTotalApplications.map((job) => ({
          ...job,
          hasApplied: appliedJobIds.has(job._id.toString()),
        }));
      }

      if (isVendor) {
        //for Freelancing in Vendors
        if (shareWithFreelancers && postingOrg == undefined) {
          delete conditions.jobType;
          // Step 1: Get all matching vendor orgs
          const existingOrgs = await this.orgsModel
            .find({
              'contactDetails.contactEmail': user.email,
              isDeleted: false,
              orgType: OrgType.VENDOR_ORG,
            })
            .exec();

          // Step 2: Extract org _ids
          const vendorCompanyIds = existingOrgs.map(
            (org) => org.companyId?.toString() ?? '',
          );
          console.log('vendorCompanyIds', vendorCompanyIds);
          conditions.postingOrg = { $in: vendorCompanyIds };

          const jobs = await this.jobModel
            .find(conditions)
            .populate(this.populateOptions)
            .sort({ createdAt: -1 })
            .skip((page - 1) * limit)
            .limit(limit)
            .exec();
          const userApplications = await this.jobApplicationModel
            .find({ createdBy: user._id, isRejected: false })
            .exec();

          // 🔹 Create a map of job applications count per stage per job
          const applicationCountMap = new Map<string, Record<string, number>>();

          userApplications.forEach((application) => {
            const jobId = application.jobId?.toString();
            const stageId = application.stage?.toString();

            if (!jobId || !stageId) {
              this.logger.log(
                `Skipping due to missing jobId or stageId: ${JSON.stringify(application)}`,
              );
              return;
            }

            if (!applicationCountMap.has(jobId)) {
              applicationCountMap.set(jobId, {});
            }

            if (!applicationCountMap.get(jobId)![stageId]) {
              applicationCountMap.get(jobId)![stageId] = 0;
            }

            applicationCountMap.get(jobId)![stageId]++;

            // Log after updating
            // this.logger.log(`Updated applicationCountMap: ${JSON.stringify(Object.fromEntries(applicationCountMap))}`);
          });

          // 🔹 Update workflow stages count based on the applications
          const jobsWithUpdatedStages = jobs.map((job) => {
            const jobId = job._id.toString();
            const stageCounts = applicationCountMap.get(jobId) || {};

            const updatedStages = job.workflow.stages.map((stage) => ({
              ...(stage as any).toObject(),
              jobApplicationsCount: stageCounts[stage._id.toString()] || 0,
            }));

            const totalJobApplicationsCount = job.workflow.stages.reduce(
              (sum, stage) => sum + ((stage as any).jobApplicationsCount || 0),
              0,
            );

            return {
              ...job.toObject(),
              workflow: {
                ...(job.workflow as any).toObject(),
                stages: updatedStages,
              },
              totalJobApplicationsCount,
            };
          });
          return jobsWithUpdatedStages;
        }
        //for marketPlace in Vendors
        if (shareWithFreelancers == undefined && postingOrg == undefined) {
          conditions.shareWithFreelancers = true;
          delete conditions.jobType;

          // Step 1: Get all matching vendor orgs
          const existingOrgs = await this.orgsModel
            .find({
              'contactDetails.contactEmail': user.email,
              isDeleted: false,
              orgType: OrgType.VENDOR_ORG,
            })
            .exec();

          // Step 2: Extract org _ids
          const vendorOrgIds = existingOrgs.map((org) => org._id.toString());
          console.log('vendorOrgIds', vendorOrgIds);
          // Step 3: Prepare allocation query
          const queryConditions: any = {
            kind: 'JobAllocationToVendors',
            isDeleted: false,
            vendor: { $in: vendorOrgIds }, // Match any of the vendor orgs
          };
          // Step 4: Query allocations
          const jobAllocations = await this.jobAllocationBasesModel
            .find(queryConditions)
            .exec();
          const allocatedjobIds = jobAllocations.map(
            (allocation) => new Types.ObjectId(allocation.job.toString()),
          );

          conditions.$or = conditions.$or || [];
          conditions.$or.push({ _id: { $in: allocatedjobIds } });

          const jobs = await this.jobModel
            .find(conditions)
            .populate(this.populateOptions)
            .sort({ createdAt: -1 })
            .skip((page - 1) * limit)
            .limit(limit)
            .exec();
          const jobIds = jobs.map((job) => job._id.toString());
          console.log('jobIds', jobIds);

          const userApplications = await this.jobApplicationModel
            .find({
              jobId: { $in: jobIds },
              createdBy: user._id,
              isRejected: false,
            })
            .exec();

          const applicationCountMap = new Map<string, Record<string, number>>();

          userApplications.forEach((application) => {
            const jobId = application.jobId?.toString();
            const stageId = application.stage?.toString();

            if (!jobId || !stageId) {
              this.logger.log(
                `Skipping due to missing jobId or stageId: ${JSON.stringify(application)}`,
              );
              return;
            }

            if (!applicationCountMap.has(jobId)) {
              applicationCountMap.set(jobId, {});
            }

            if (!applicationCountMap.get(jobId)![stageId]) {
              applicationCountMap.get(jobId)![stageId] = 0;
            }

            applicationCountMap.get(jobId)![stageId]++;

            // Log after updating
            // this.logger.log(`Updated applicationCountMap: ${JSON.stringify(Object.fromEntries(applicationCountMap))}`);
          });

          // 🔹 Update workflow stages count based on the applications
          const jobsWithUpdatedStages = jobs.map((job) => {
            const jobId = job._id.toString();
            const stageCounts = applicationCountMap.get(jobId) || {};

            const updatedStages = job.workflow.stages.map((stage) => ({
              ...(stage as any).toObject(),
              jobApplicationsCount: stageCounts[stage._id.toString()] || 0,
            }));

            const totalJobApplicationsCount = job.workflow.stages.reduce(
              (sum, stage) => sum + ((stage as any).jobApplicationsCount || 0),
              0,
            );

            return {
              ...job.toObject(),
              workflow: {
                ...(job.workflow as any).toObject(),
                stages: updatedStages,
              },
              totalJobApplicationsCount,
            };
          });

          const appliedJobIds = new Set(
            userApplications.map((app) => app.jobId.toString()),
          );
          // Add 'hasApplied' to each job
          return jobsWithUpdatedStages.map((job) => ({
            ...job,
            hasApplied: appliedJobIds.has(job._id.toString()),
          }));
        }
        //for Lists in Vendors
        if (shareWithFreelancers == undefined && postingOrg !== undefined) {
          console.log('vendor wit freelancer');
          // console.log(user.org._id)

          // Step 1: Get all matching vendor orgs
          const existingOrgs = await this.orgsModel
            .find({
              'contactDetails.contactEmail': user.email,
              isDeleted: false,
              orgType: OrgType.VENDOR_ORG,
            })
            .exec();

          // Step 2: Extract org _ids
          const vendorOrgIds = existingOrgs.map((org) => org._id.toString());
          // console.log("vendorOrgIds", vendorOrgIds);
          // Step 3: Prepare allocation query
          const queryConditions: any = {
            kind: 'JobAllocationToVendors',
            isDeleted: false,
            vendor: { $in: vendorOrgIds }, // Match any of the vendor orgs
          };

          console.log('qury conditions', queryConditions);
          // Step 4: Query allocations
          const jobAllocations = await this.jobAllocationBasesModel
            .find(queryConditions)
            .exec();
          const allocatedjobIds = jobAllocations.map(
            (allocation) => new Types.ObjectId(allocation.job.toString()),
          );
          let vendorJobs = await this.jobModel
            .find({ ...conditions, _id: { $in: allocatedjobIds } })
            .populate(this.populateOptions)
            .sort({ createdAt: -1 })
            .skip((page - 1) * limit)
            .limit(limit)
            .exec();

          const allJobs = [...vendorJobs];

          // Create a Map to store unique jobs by _id
          const uniqueJobsMap = new Map<string, any>();

          allJobs.forEach((job) => {
            uniqueJobsMap.set(job._id.toString(), job);
          });

          // Convert back to an array
          const jobs = Array.from(uniqueJobsMap.values());

          // console.log("uniqueJobs", jobs);
          const jobIds = jobs.map((job) => job._id.toString());
          // console.log("jobIds", jobIds);

          const userApplications = await this.jobApplicationModel
            .find({
              jobId: { $in: jobIds },
              createdBy: user._id,
              isRejected: false,
            })
            .exec();

          const applicationCountMap = new Map<string, Record<string, number>>();

          userApplications.forEach((application) => {
            const jobId = application.jobId?.toString();
            const stageId = application.stage?.toString();

            if (!jobId || !stageId) {
              this.logger.log(
                `Skipping due to missing jobId or stageId: ${JSON.stringify(application)}`,
              );
              return;
            }

            if (!applicationCountMap.has(jobId)) {
              applicationCountMap.set(jobId, {});
            }

            if (!applicationCountMap.get(jobId)![stageId]) {
              applicationCountMap.get(jobId)![stageId] = 0;
            }

            applicationCountMap.get(jobId)![stageId]++;

            // Log after updating
            // this.logger.log(`Updated applicationCountMap: ${JSON.stringify(Object.fromEntries(applicationCountMap))}`);
          });

          // 🔹 Update workflow stages count based on the applications
          const jobsWithUpdatedStages = jobs.map((job) => {
            const jobId = job._id.toString();
            const stageCounts = applicationCountMap.get(jobId) || {};

            const updatedStages = job.workflow.stages.map(
              (stage: { _id: { toString: () => string | number } }) => ({
                ...(stage as any).toObject(),
                jobApplicationsCount: stageCounts[stage._id.toString()] || 0,
              }),
            );

            const totalJobApplicationsCount = job.workflow.stages.reduce(
              (sum: any, stage: any) =>
                sum + ((stage as any).jobApplicationsCount || 0),
              0,
            );

            return {
              ...job.toObject(),
              workflow: {
                ...(job.workflow as any).toObject(),
                stages: updatedStages,
              },
              totalJobApplicationsCount,
            };
          });

          // const appliedJobIds = new Set(userApplications.map(app => app.jobId.toString()));
          // // Add 'hasApplied' to each job
          // return jobsWithUpdatedStages.map(job => ({
          //   ...job,
          //   hasApplied: appliedJobIds.has(job._id.toString())
          // }));
          return jobsWithUpdatedStages;
        }
      }

      return finalJobs;
    } catch (error) {
      this.logger.error(error);
      throw new InternalServerErrorException(
        `An error occurred while retrieving jobs. ${error?.message}`,
      );
    }
  }

  async findAllCount(
    userId: string,
    query: JobsQueryDTO,
  ): Promise<{ jobs: Job[]; totalCount: number; matchedJobsCount: number }> {
    try {
      const {
        postingOrg,
        endClientOrg,
        hiringOrg,
        locationIds,
        isOpen,
        isDraft,
        department,
        name,
        page = 1,
        limit = 10,
        jobType,
        workMode,
        employmentType,
        fromDate,
        toDate,
        matchedJobs,
        shareWithFreelancers,
        vendorId,
      } = query;

      let conditions: any = { isDeleted: false }; // Default condition to exclude deleted jobs

      // Apply filters only if they are provided in the query
      if (postingOrg) {
        conditions.postingOrg = postingOrg;
      }
      if (hiringOrg) {
        conditions.hiringOrg = hiringOrg;
      }
      if (endClientOrg) {
        conditions.endClientOrg = endClientOrg;
      }
      if (locationIds) {
        conditions.jobLocation = { $in: [locationIds] };
      }
      if (department) {
        conditions.department = department;
      }
      if (isOpen !== undefined) {
        conditions.isOpen = isOpen;
      }
      if (isDraft !== undefined) {
        conditions.isDraft = isDraft;
      }
      if (shareWithFreelancers !== undefined) {
        conditions.shareWithFreelancers = shareWithFreelancers;
      }
      if (jobType) {
        conditions.jobType = jobType;
      }
      if (workMode) {
        conditions.workMode = workMode;
      }
      if (employmentType) {
        conditions.employmentType = employmentType;
      }
      if (fromDate) {
        conditions.createdAt = {
          ...conditions.createdAt,
          $gte: moment(fromDate).startOf('day').toDate(),
        };
      }
      if (toDate) {
        conditions.createdAt = {
          ...conditions.createdAt,
          $lte: moment(toDate).endOf('day').toDate(),
        };
      }
      // Search by job title (case-insensitive)
      if (name) {
        const regex = new RegExp(name, 'i'); // 'i' for case-insensitive search
        conditions.title = regex;
      }

      if (vendorId) {
        conditions.vendors = { $in: new Types.ObjectId(vendorId) };
      }

      let userSkills: string[] = [];
      if (matchedJobs) {
        const preference = await this.preferenceModel
          .findOne({ createdBy: userId })
          .exec();
        if (preference) {
          userSkills = preference.skills.map((skill) => skill.skill);
        }
      }

      let jobs = await this.jobModel
        .find(conditions)
        .populate(this.populateOptions)
        .sort({ createdAt: -1 })
        .skip((page - 1) * limit)
        .limit(limit)
        .exec();

      let totalCount: number;
      let matchedJobsCount: number = 0;

      if (matchedJobs && userSkills.length > 0) {
        const allJobs = await this.jobModel.find(conditions).exec();
        const matchedJobsList = allJobs.filter((job) => {
          const jobSkills = [
            ...(job.primarySkills ?? []),
            ...(job.secondarySkills ?? []),
          ];
          return jobSkills.some((skill) => userSkills.includes(skill));
        });
        matchedJobsCount = matchedJobsList.length;
        jobs = jobs.filter((job) => {
          const jobSkills = [
            ...(job.primarySkills ?? []),
            ...(job.secondarySkills ?? []),
          ];
          return jobSkills.some((skill) => userSkills.includes(skill));
        });
        totalCount = jobs.length;
      } else {
        totalCount = await this.jobModel.countDocuments(conditions).exec();
      }

      // Add total applications count for each job
      const jobsWithTotalApplications = jobs.map((job) => {
        const totalJobApplicationsCount =
          job.workflow?.stages?.reduce(
            (sum: number, stage: any) =>
              sum + (stage.jobApplicationsCount || 0),
            0,
          ) || 0; // Use optional chaining and default to 0 if workflow or stages are undefined
        return { ...job.toObject(), totalJobApplicationsCount };
      });

      return { jobs: jobsWithTotalApplications, totalCount, matchedJobsCount };
    } catch (error) {
      this.logger.error(`Error in findAllCount: ${error.message}`, error.stack);
      throw new InternalServerErrorException(
        `An error occurred while retrieving jobs. ${error.message}`,
      );
    }
  }

  async findOne(jobId: Types.ObjectId): Promise<Job> {
    try {
      const job = await this.jobModel
        .findById(jobId)
        .populate(this.populateOptions)
        .exec();

      if (!job) {
        throw new NotFoundException(`Job not found with ID ${jobId}`);
      }
      return job;
    } catch (error) {
      this.logger.error(error);
      this.logger.error(
        `An error occurred in fetching job by Id ${jobId}. ${error?.message}`,
      );
      throw error;
    }
  }

  async findById(jobId: Types.ObjectId) {
    try {
      const job = await this.jobModel
        .findById(jobId)
        .populate(this.populateOptions)
        .exec();

      if (!job) {
        throw new NotFoundException(`Job not found with ID ${jobId}`);
      }
      return job;
    } catch (error) {
      this.logger.error(error);
      this.logger.error(
        `An error occurred in fetching job by Id ${jobId}. ${error?.message}`,
      );
      throw error;
    }
  }

  async update(
    jobId: Types.ObjectId,
    updateJobDto: UpdateJobDto,
    user: Object,
  ) {
    // this.logger.log(jobId)
    try {
      const job = await this.findById(jobId);
      if (!job) {
        throw new NotFoundException(`Job not found with ID ${jobId}`);
      }

      const unsetData: any = {};

      // List of fields to check (all with { required: false, type: Types.ObjectId })
      const fieldsToCheck: (keyof UpdateJobDto)[] = [
        'department',
        'jdUrl',
        'industryOrDomain',
        'jobLocation',
        'socialMediaLinks',
        'postingOrg',
        'hiringOrg',
        'spoc',
        'endClientOrg',
        'employmentType',
        'workMode',
        'hiringMode',
        'instructionFile',
        'rateCard',
      ];

      // Iterate through each field, check for empty string, remove it from updateJobDto, and mark for unset
      fieldsToCheck.forEach((field) => {
        if (updateJobDto[field] === '') {
          unsetData[field] = ''; // Mark for removal
          delete updateJobDto[field]; // Remove from updateJobDto
        }
      });

      // Perform the update operation
      const updatedJob = await this.jobModel
        .findByIdAndUpdate(
          jobId,
          {
            $set: updateJobDto, // Apply the updated fields
            $unset: unsetData, // Remove the fields marked for deletion
          },
          { new: true }, // Return the updated document
        )
        .populate(this.populateOptions)
        .exec();
      return updatedJob;
    } catch (error) {
      this.logger.error(error);
      this.logger.error(
        `An error occurred while updating Job by ID ${jobId}. ${error?.message}`,
      );
      throw error;
    }
  }

  async updateShareWithFreelancers(
    jobId: Types.ObjectId,
  ): Promise<Job | { message: string }> {
    try {
      const job = await this.jobModel.findById(jobId).exec();
      if (!job) {
        throw new BadRequestException(`Job with ID ${jobId} not found.`);
      }

      // If already true, return a message instead of updating
      if (job.shareWithFreelancers) {
        return { message: 'Job is already available for freelancers.' };
      }

      const updatedJob = await this.jobModel
        .findByIdAndUpdate(
          jobId,
          { shareWithFreelancers: true }, // Set to true
          { new: true },
        )
        .select('_id title shareWithFreelancers')
        .exec();

      // console.log("updatedJob", updatedJob);

      if (!updatedJob) {
        throw new NotFoundException(`Job not found with ID ${jobId}`);
      }

      return updatedJob;
    } catch (error) {
      this.logger.error(
        `Error updating shareWithFreelancers for Job ID ${jobId}: ${error?.message}`,
      );
      throw new InternalServerErrorException(
        `Failed to update shareWithFreelancers. ${error.message}`,
      );
    }
  }

  async updateShareWithVendors(
    jobId: Types.ObjectId,
    validatedVendorId: Types.ObjectId,
  ): Promise<Job | { message: string }> {
    try {
      const job = await this.jobModel.findById(jobId).exec();
      if (!job) {
        throw new BadRequestException(`Job with ID ${jobId} not found.`);
      }

      // If already true, return a message instead of updating
      // if (job.shareWithVendors) {
      //   return { message: 'Job is already available for Vendors.' };
      // }

      // Ensure job.vendors is an array and contains validatedVendorId
      if (job.vendors?.includes(validatedVendorId)) {
        throw new BadRequestException(`Job is already assigned to Vendor ID`);
      }

      job.vendors.push(validatedVendorId);
      job.shareWithVendors = true;

      // Save the updated job
      await job.save();

      const updatedJob = await this.jobModel
        .findById(jobId)
        .select('_id title shareWithVendors')
        .exec();

      console.log('updatedJob', updatedJob);

      if (!updatedJob) {
        throw new NotFoundException(`Job not found with ID ${jobId}`);
      }

      return updatedJob;
    } catch (error) {
      this.logger.error(
        `Error updating shareWithVendors for Job ID ${jobId}: ${error?.message}`,
      );
      throw new InternalServerErrorException(
        `Failed to update shareWithVendors. ${error.message}`,
      );
    }
  }

  async remove(jobId: Types.ObjectId) {
    try {
      const job = await this.findById(jobId);
      if (!job) {
        throw new NotFoundException(`Job not found with ID ${jobId}`);
      }
      await this.jobModel.deleteOne({ _id: jobId });
      return { message: 'Job deleted' };
    } catch (error) {
      this.logger.error(
        `An error occurred while deleting job by ID ${jobId}. ${error?.message}`,
      );
      throw new InternalServerErrorException(
        'An error occurred while hard deleting the Job',
      );
    }
  }

  async delete(jobId: Types.ObjectId) {
    try {
      const job = await this.findById(jobId);
      if (!job) {
        throw new NotFoundException(`Job not found with ID ${jobId}`);
      }
      await this.jobModel.findByIdAndUpdate(jobId, { isDeleted: true });
      return { message: 'Job soft deleted' };
    } catch (error) {
      this.logger.error(
        `An error occurred while soft deleting job by ID ${jobId}. ${error?.message}`,
      );
      throw new InternalServerErrorException(
        'An error occurred while soft deleting the Job',
      );
    }
  }

  // async getJobsCount(user: any, query: JobsQueryDTO): Promise<{ jobsCount: number; jobsOpen: number; }> {
  //   try {

  //     const { postingOrg, shareWithFreelancers, endClientOrg, hiringOrg, locationIds, isOpen, isDraft, department, name, page = 1, limit = 10, jobType, workMode, employmentType, fromDate, toDate, matchedJobs, vendorId } = query;

  //     let conditions: any = { isDeleted: false }; // Default condition to exclude deleted jobs

  //     // Apply filters only if they are provided in the query
  //     if (postingOrg) {
  //       conditions.postingOrg = postingOrg;
  //     }
  //     if (shareWithFreelancers !== undefined) {
  //       conditions.shareWithFreelancers = shareWithFreelancers;
  //     }
  //     if (hiringOrg) {
  //       conditions.hiringOrg = hiringOrg;
  //     }
  //     if (endClientOrg) {
  //       conditions.endClientOrg = endClientOrg;
  //     }
  //     if (locationIds) {
  //       conditions.jobLocation = { $in: [locationIds] };
  //     }
  //     if (department) {
  //       conditions.department = department;
  //     }
  //     if (isOpen !== undefined) {
  //       conditions.isOpen = isOpen;
  //     }
  //     if (isDraft !== undefined) {
  //       conditions.isDraft = isDraft;
  //     }
  //     if (shareWithFreelancers !== undefined) {
  //       conditions.shareWithFreelancers = shareWithFreelancers;
  //     }
  //     if (jobType) {
  //       conditions.jobType = jobType;
  //     }
  //     if (workMode) {
  //       conditions.workMode = workMode;
  //     }
  //     if (employmentType) {
  //       conditions.employmentType = employmentType;
  //     }
  //     if (fromDate) {
  //       conditions.createdAt = { ...conditions.createdAt, $gte: moment(fromDate).startOf('day').toDate() };
  //     }
  //     if (toDate) {
  //       conditions.createdAt = {
  //         ...conditions.createdAt,
  //         $lte: moment(toDate).endOf('day').toDate(),
  //       };
  //     }

  //     if (vendorId) {
  //       conditions.vendors = { $in: new Types.ObjectId(vendorId) };
  //     }

  //     const isJobSeeker = user.roles?.includes(Role.JobSeeker);
  //     const isFreeLancer = user.roles?.includes(Role.Freelancer);
  //     if (isFreeLancer) {
  //       conditions.shareWithFreelancers = true;
  //       delete conditions.jobType;
  //     }

  //     if (matchedJobs && isJobSeeker) {
  //       const jobs = await this.findAll(user, {
  //         jobType: JobType.External,
  //         matchedJobs: true,
  //         page: 0,
  //         limit: 0
  //       });
  //       const openJobs = jobs.filter(job => job.isOpen);
  //       return {
  //         jobsCount: jobs.length,
  //         jobsOpen: openJobs.length
  //       };
  //     }

  //     const jobsCount = await this.jobModel.countDocuments(conditions);
  //     const openQuery = { ...conditions, isOpen: true };
  //     const jobsOpen = await this.jobModel.countDocuments(openQuery);

  //     return {
  //       jobsCount,
  //       jobsOpen
  //     };
  //   } catch (error) {
  //     throw new Error(`Error fetching job's count. ${error.message}`);
  //   }
  // }

  async getJobsCount(
    user: any,
    query: JobsQueryDTO,
  ): Promise<{ jobsCount: number; jobsOpen: number }> {
    try {
      const {
        postingOrg,
        shareWithFreelancers,
        endClientOrg,
        hiringOrg,
        locationIds,
        isOpen,
        isDraft,
        department,
        name,
        page = 1,
        limit = 10,
        jobType,
        workMode,
        employmentType,
        fromDate,
        toDate,
        matchedJobs,
        vendorId,
      } = query;

      const isAdmin = user.roles?.includes(Role.Admin);
      const isDeliveryManager = user.roles?.includes(Role.DeliveryManager);
      const isAccountManager = user.roles?.includes(Role.AccountManager);
      const isJobSeeker = user.roles?.includes(Role.JobSeeker);
      const isFreelancer = user.roles?.includes(Role.Freelancer);
      const isVendor = user.roles?.includes(Role.Vendor);

      // 1️⃣ Matched jobs for job seeker
      if (matchedJobs && isJobSeeker) {
        const jobs = await this.findAll(user, {
          jobType: JobType.External,
          matchedJobs: true,
          page: 0,
          limit: 0,
        });

        const openJobs = jobs.filter((job) => job.isOpen);

        return {
          jobsCount: jobs.length,
          jobsOpen: openJobs.length,
        };
      }

      // 2️⃣ Default filter setup
      let conditions: any = { isDeleted: false };

      if (postingOrg) conditions.postingOrg = postingOrg;
      if (hiringOrg) conditions.hiringOrg = hiringOrg;
      if (endClientOrg) conditions.endClientOrg = endClientOrg;
      if (department) conditions.department = department;
      if (isOpen !== undefined) conditions.isOpen = isOpen;
      if (isDraft !== undefined) conditions.isDraft = isDraft;
      if (jobType) conditions.jobType = jobType;
      if (workMode) conditions.workMode = workMode;
      if (employmentType) conditions.employmentType = employmentType;
      if (shareWithFreelancers !== undefined)
        conditions.shareWithFreelancers = shareWithFreelancers;
      // Search by job title (case-insensitive)
      if (name) {
        const regex = new RegExp(name, 'i'); // 'i' for case-insensitive search
        conditions.title = regex;
      }

      if (locationIds) {
        conditions.jobLocation = { $in: [locationIds] };
      }

      if (fromDate) {
        conditions.createdAt = {
          ...conditions.createdAt,
          $gte: moment(fromDate).startOf('day').toDate(),
        };
      }

      if (toDate) {
        conditions.createdAt = {
          ...conditions.createdAt,
          $lte: moment(toDate).endOf('day').toDate(),
        };
      }

      if (vendorId) {
        conditions.vendors = { $in: [new Types.ObjectId(vendorId)] };
      }

      // 3️⃣ Role-based access
      if (isAdmin || isDeliveryManager) {
        if (isVendor) {
          const allocatedJobIds = await this.jobAllocationBasesModel.distinct(
            'jobId',
            {
              $or: [{ vendors: user.orgId }, { assignees: user._id }],
              isDeleted: false,
            },
          );

          conditions.$or = [
            { postingOrg: postingOrg ?? user.orgId },
            { _id: { $in: allocatedJobIds } },
          ];
        } else {
          if (postingOrg) {
            conditions.postingOrg = postingOrg;
          }
        }
      } else if (isAccountManager) {
        const assignedOrgs = await this.orgsModel
          .find({
            $or: [{ accountManager: user._id }, { createdBy: user._id }],
            isDeleted: false,
          })
          .select('_id')
          .lean();

        const assignedOrgIds = assignedOrgs.map((org) => org._id);
        conditions.postingOrg = { $in: assignedOrgIds };
      } else if (isFreelancer) {
        delete conditions.jobType;
        conditions.shareWithFreelancers = true;
      } else if (isVendor) {
        if (shareWithFreelancers && postingOrg === undefined) {
          // Vendor: Freelancing tab (without postingOrg)
          delete conditions.jobType;
          const vendorOrgs = await this.orgsModel.find({
            'contactDetails.contactEmail': user.email,
            isDeleted: false,
            orgType: OrgType.VENDOR_ORG,
          });

          const vendorOrgIds = vendorOrgs.map((org) => org._id);
          conditions.postingOrg = { $in: vendorOrgIds };
          conditions.shareWithFreelancers = true;
        } else if (
          shareWithFreelancers === undefined &&
          postingOrg !== undefined
        ) {
          // ✅ Vendor: Applied filter with postingOrg but not freelancing
          console.log(
            'Vendor: Applied filter with postingOrg but not freelancing',
          );
          const existingOrgs = await this.orgsModel.find({
            'contactDetails.contactEmail': user.email,
            isDeleted: false,
            orgType: OrgType.VENDOR_ORG,
          });

          const vendorOrgIds = existingOrgs.map((org) => org._id.toString());

          const jobAllocations = await this.jobAllocationBasesModel.find({
            kind: 'JobAllocationToVendors',
            isDeleted: false,
            vendor: { $in: vendorOrgIds },
          });

          const allocatedJobIds = jobAllocations.map((a) => a.job.toString());
          console.log('allocated jobs', allocatedJobIds);
          conditions._id = { $in: allocatedJobIds };
        } else {
          // Vendor: Assigned jobs (default)
          const allocatedJobIds = await this.jobAllocationBasesModel.distinct(
            'jobId',
            {
              $or: [{ vendors: user.orgId }, { assignees: user._id }],
              isDeleted: false,
            },
          );
          conditions._id = { $in: allocatedJobIds };
        }
      } else {
        // Recruiters and other roles
        const createdByOrAssignedJobIds =
          await this.jobAllocationBasesModel.distinct('jobId', {
            $or: [{ assignees: user._id }, { createdBy: user._id }],
            isDeleted: false,
          });
        conditions._id = { $in: createdByOrAssignedJobIds };
      }
      console.log('conditions', conditions);
      // 4️⃣ Final counts
      const jobsCount = await this.jobModel.countDocuments(conditions);
      const openCount = await this.jobModel.countDocuments({
        ...conditions,
        isOpen: true,
      });

      return {
        jobsCount,
        jobsOpen: openCount,
      };
    } catch (error) {
      throw new Error(`Error fetching job count: ${error.message}`);
    }
  }

  async getJobsCountByPostingOrg(postingOrg: string) {
    try {
      // Base condition to exclude deleted jobs
      const baseConditions = {
        isDeleted: false,
        postingOrg: postingOrg,
      };

      // Get total jobs count for the posting org
      const jobsCount = await this.jobModel.countDocuments(baseConditions);

      // Get open jobs count for the posting org
      const jobsOpen = await this.jobModel.countDocuments({
        ...baseConditions,
        isOpen: true,
      });

      return {
        jobsCount,
        jobsOpen,
      };
    } catch (error) {
      this.logger.error(error);
      throw new Error(
        `Error fetching job counts for posting organization. ${error.message}`,
      );
    }
  }

  async getActiveInternalJobsCount() {
    try {
      const activeInternalJobsCount = await this.jobModel.countDocuments({
        jobType: 'Internal', // Filter for internal jobs
        isOpen: true, // Active jobs
        isDeleted: false, // Exclude deleted jobs
      });

      return {
        activeInternalJobsCount, // Count of active internal jobs
      };
    } catch (error) {
      throw new Error(
        `Error fetching active internal jobs count. ${error.message}`,
      );
    }
  }

  async changeStatus(
    jobId: Types.ObjectId,
    changeStatusJobDto: ChangeStatusJobDto,
    user: object,
  ) {
    try {
      const job = await this.jobModel.findById(jobId);
      // this.logger.debug(job);
      if (!job) {
        throw new NotFoundException(
          `The job with id: "${jobId}" doesn't exist.`,
        );
      }

      const { isOpen, isDraft } = changeStatusJobDto;

      const updatedJob = await this.jobModel
        .findByIdAndUpdate(
          jobId,
          { isOpen: isOpen, isDraft: isDraft },
          { new: true },
        )
        .populate(this.populateOptions)
        .exec();

      return updatedJob;
    } catch (error) {
      this.logger.error(
        `An error occurred while updating the job status. ${error?.message}`,
      );
      throw error;
    }
  }

  async bulkDelete(jobIds: Types.ObjectId[]) {
    try {
      const result = await this.jobModel
        .updateMany({ _id: { $in: jobIds } }, { $set: { isDeleted: true } })
        .exec();

      if (result.modifiedCount > 0) {
        this.logger.log(
          `Successfully soft-deleted ${result.modifiedCount} jobs.`,
        );
      } else {
        this.logger.error('No jobs were deleted.');
      }

      return result;
    } catch (error) {
      this.logger.error(
        `Error while bulk deleting jobs: ${error.message}`,
        error.stack,
      );
      throw new InternalServerErrorException('Error while bulk deleting jobs.');
    }
  }

  async bulkChangeStatus(
    changeStatusJobDto: ChangeStatusJobDto,
    user: BasicUser,
  ) {
    try {
      const { isOpen, isDraft, jobIds } = changeStatusJobDto;
      const result = await this.jobModel
        .updateMany(
          { _id: { $in: jobIds } },
          { $set: { isOpen } },
          { $set: { isDraft } },
        )
        .exec();

      return result;
    } catch (error) {
      this.logger.error(
        `Error while bulk updating jobs status: ${error.message}`,
        error.stack,
      );
      throw new InternalServerErrorException(
        'Error while bulk updating jobs status.',
      );
    }
  }

  async findJobCountByDept(filter: string, userId: string, user: any) {
    try {
      const currentDate = new Date();
      let startDate: Date;
      let endDate: Date;
      const postingOrg = user.org._id;

      switch (filter) {
        case 'yesterday':
          startDate = new Date(currentDate);
          startDate.setDate(currentDate.getDate() - 1); // Set to previous day
          startDate.setHours(0, 0, 0, 0); // Start of the previous day
          endDate = new Date(startDate); // Start with startDate for endDate
          endDate.setHours(23, 59, 59, 999); // End of the previous day
          break;
        case 'thisWeek':
          startDate = new Date(currentDate);
          const dayOfWeek = startDate.getDay();
          const diffToMonday = dayOfWeek === 0 ? -6 : 1 - dayOfWeek;
          startDate.setDate(currentDate.getDate() + diffToMonday);
          startDate.setHours(0, 0, 0, 0);
          endDate = new Date(currentDate);
          endDate.setHours(23, 59, 59, 999); // End of today
          break;
        case 'thisMonth':
          startDate = new Date(
            currentDate.getFullYear(),
            currentDate.getMonth(),
            1,
          );
          endDate = new Date(currentDate);
          endDate.setHours(23, 59, 59, 999); // End of today
          break;
        default: // today
          startDate = new Date(currentDate);
          startDate.setHours(0, 0, 0, 0);
          endDate = new Date(currentDate);
          endDate.setHours(23, 59, 59, 999); // End of today
          break;
      }
      const jobCount = await this.jobModel.aggregate([
        {
          $match: {
            $or: [
              { createdAt: { $gte: startDate, $lte: endDate } },
              { updatedAt: { $gte: startDate, $lte: endDate } },
            ],
            isDeleted: false,
            isOpen: true,
            postingOrg: postingOrg,
          },
        },
        { $addFields: { departmentId: { $toObjectId: '$department' } } },
        {
          $lookup: {
            from: 'businessunits',
            localField: 'departmentId',
            foreignField: '_id',
            as: 'deptDetails',
          },
        },
        {
          $unwind: {
            path: '$deptDetails',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $match: {
            'deptDetails.label': { $ne: null }, // Exclude null department names
          },
        },
        {
          $group: {
            _id: {
              // deptId: '$deptDetails._id', // Group by dept id
              // parentId:'$deptDetails.parentBusinessUnit',
              deptName: '$deptDetails.label', // Also group by dept name
            },
            totalCount: { $sum: 1 }, // Count the number of jobs for each dept
          },
        },

        {
          $project: {
            deptName: '$_id.deptName', // Use dept name as field
            count: '$totalCount', // Total count for that stage
            _id: 0, // Exclude _id from the result
          },
        },
      ]);

      return jobCount;
    } catch (error) {
      this.logger.error(
        `Error fetching job counts by department for given date: ${error.message}`,
      );
      throw new InternalServerErrorException(
        `Error fetching job counts by department for given date:: ${error.message}`,
      );
    }
  }

  async findInternalJobCountByDept(filter: string) {
    try {
      const currentDate = new Date();
      let startDate: Date;
      let endDate: Date;

      // Validate the filter value
      const validFilters = ['today', 'yesterday', 'thisWeek', 'thisMonth'];
      if (!validFilters.includes(filter)) {
        filter = 'today';
      }

      switch (filter) {
        case 'yesterday':
          startDate = new Date(currentDate);
          startDate.setDate(currentDate.getDate() - 1); // Set to yesterday
          startDate.setHours(0, 0, 0, 0); // Start of yesterday

          endDate = new Date(currentDate); // Today's end
          endDate.setHours(23, 59, 59, 999); // End of today
          break;

        case 'thisWeek':
          startDate = new Date(currentDate);
          startDate.setDate(currentDate.getDate() - 7); // Move to the same day last week
          startDate.setHours(0, 0, 0, 0); // Start of last week's same day

          // End date is today
          endDate = new Date(currentDate);
          endDate.setHours(23, 59, 59, 999); // End of today
          break;

        case 'thisMonth':
          startDate = new Date(
            currentDate.getFullYear(),
            currentDate.getMonth(),
            1,
          );
          endDate = new Date(currentDate);
          endDate.setHours(23, 59, 59, 999); // End of today
          break;

        default: // today
          startDate = new Date(currentDate);
          startDate.setHours(0, 0, 0, 0);
          endDate = new Date(currentDate);
          endDate.setHours(23, 59, 59, 999); // End of today
          break;
      }

      //       // Log the filter and date range
      // console.log('Filter:', filter);
      // console.log('Start Date (UTC):', startDate.toISOString());
      // console.log('End Date (UTC):', endDate.toISOString());
      // console.log('Start Date (Local):', startDate.toLocaleString());
      // console.log('End Date (Local):', endDate.toLocaleString());

      // Aggregation pipeline
      const jobCount = await this.jobModel.aggregate([
        {
          $match: {
            $or: [
              { createdAt: { $gte: startDate, $lte: endDate } },
              { updatedAt: { $gte: startDate, $lte: endDate } },
            ],
            isDeleted: false,
            jobType: 'Internal', // Filter for internal jobs only
          },
        },
        { $addFields: { userId: { $toObjectId: '$department' } } },
        {
          $lookup: {
            from: 'businessunits',
            localField: 'userId',
            foreignField: '_id',
            as: 'deptDetails',
          },
        },
        {
          $unwind: {
            path: '$deptDetails',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $group: {
            _id: {
              deptId: '$deptDetails._id', // Group by department ID
              deptName: '$deptDetails.label', // Group by department name
            },
            totalCount: { $sum: 1 }, // Count the number of jobs
          },
        },
        {
          $project: {
            deptName: '$_id.deptName', // Department name
            count: '$totalCount', // Total count
            _id: 0, // Exclude _id from the result
          },
        },
      ]);

      return jobCount;
    } catch (error) {
      this.logger.error(
        `Error fetching job counts by department for given date: ${error.message}`,
      );
      throw new InternalServerErrorException(
        `Error fetching job counts by department for given date:: ${error.message}`,
      );
    }
  }

  async findJobsAndHiredCountByMonth() {
    try {
      const currentDate = new Date();
      let startDate: Date;
      let endDate: Date;

      // Count jobs and hired
      const counts: { Total: number; Hired: number; Month: string }[] = [];

      const monthName = [
        'January',
        'February',
        'March',
        'April',
        'May',
        'June',
        'July',
        'August',
        'September',
        'October',
        'November',
        'December',
      ];

      for (let i = 0; i < 6; i++) {
        startDate = new Date(
          currentDate.getFullYear(),
          currentDate.getMonth() - i,
          1,
        );
        startDate.setHours(0, 0, 0, 0); // Start of day

        endDate = new Date(
          currentDate.getFullYear(),
          currentDate.getMonth() - i,
          31,
        );
        endDate.setHours(23, 59, 59, 999); // End of day

        const jobsCount = await this.jobModel.countDocuments({
          isDeleted: false,
          $or: [
            { createdAt: { $gte: startDate, $lte: endDate } },
            { updatedAt: { $gte: startDate, $lte: endDate } },
          ],
        });
        const hired = await this.stageModel.countDocuments({
          type: { $in: ['workflow.offer'] },
          $or: [
            { createdAt: { $gte: startDate, $lte: endDate } },
            { updatedAt: { $gte: startDate, $lte: endDate } },
          ],
        });
        const month = monthName[currentDate.getMonth() - i];
        counts.push({
          Total: jobsCount,
          Hired: hired,
          Month: month,
        });
      }
      return counts;
    } catch (error) {
      throw new InternalServerErrorException(
        'Error fetching job and hired counts for last six months:: ${error.message}',
      );
    }
  }

  async findJobRecruitementSummary() {
    try {
      // Count jobs,interviews,shortlisted and hired
      const counts: {
        TotalJobs: number;
        Interviews: number;
        Shortlisted: number;
        hired: number;
      }[] = [];

      const jobsCount = await this.jobModel.countDocuments({
        isDeleted: false,
      });
      const hired = await this.stageModel.countDocuments({
        type: { $in: ['workflow.offer'] },
      });
      const shortlisted = await this.stageModel.countDocuments({
        type: { $in: ['workflow.screening'] },
      });
      const interviews = await this.interviewModel.countDocuments({
        isCandidateAttended: true,
      });

      counts.push({
        TotalJobs: jobsCount,
        Interviews: interviews,
        Shortlisted: shortlisted,
        hired: hired,
      });
      return counts;
    } catch (error) {
      throw new InternalServerErrorException(
        'Error fetching job recruitement summary count:: ${error.message}',
      );
    }
  }

  async getJobSeekerDashboard(user: any) {
    try {
      const userId = user._id;

      // 1️⃣ Get external jobs count (with populated locations)
      const externalJobs = await this.jobModel
        .find({
          isDeleted: false,
          isOpen: true,
          jobType: JobType.External,
        })
        .populate({
          path: 'jobLocation',
          select: '_id city state country postalCode',
          model: 'JobLocation',
        })
        .exec();

      const externalJobsCount = externalJobs.length;

      // 2️⃣ Get user preferences (skills & locations & clients)
      const userPreferences = await this.preferenceModel
        .findOne({ createdBy: userId })
        .exec();
      const userSkills =
        userPreferences?.skills?.map((skill) => skill.skill.toLowerCase()) ||
        [];
      const userLocations =
        userPreferences?.locations?.map((location) =>
          location.location.toLowerCase(),
        ) || [];
      const userClients =
        userPreferences?.clients?.map((client) => client.clientId.toString()) ||
        []; // Convert ObjectIds to strings

      // 3️⃣ Filter external jobs: Match by Skills, Locations, AND End Client Org
      const matchedJobs = externalJobs.filter((job) => {
        const jobSkills = [
          ...(job?.primarySkills ?? []),
          ...(job?.secondarySkills ?? []),
        ].map((skill) => skill.toLowerCase());
        const jobLocations = (job?.jobLocation ?? []).map((location) =>
          location.city.toLowerCase(),
        );
        const jobEndClientOrg = job?.endClientOrg
          ? job.endClientOrg.toString()
          : '';

        // Match jobs where either Skills AND Locations AND End Client Org match user preferences
        return (
          jobSkills.some((skill) => userSkills.includes(skill)) &&
          jobLocations.some((city) => userLocations.includes(city)) &&
          userClients.includes(jobEndClientOrg) // Check if job's endClientOrg is in userClients
        );
      });

      // 4️⃣ Get user preferences count
      const preferencesCount =
        userSkills.length + userLocations.length + userClients.length;

      // 5️⃣ Get resumes count uploaded by the job-seeker
      const resumesCount = await this.resumeModel.countDocuments({
        createdBy: userId,
      });

      return {
        externalJobsCount,
        matchedJobsCount: matchedJobs.length,
        matchedJobs,
        preferencesCount,
        resumesCount,
      };
    } catch (error) {
      this.logger.error(
        `Error retrieving dashboard data: ${error.message}`,
        error.stack,
      );
      throw new InternalServerErrorException(
        `Error retrieving dashboard data. ${error.message}`,
      );
    }
  }

  async getJobCountByLocation(): Promise<any> {
    try {
      const result = await this.jobModel.aggregate([
        { $match: { jobType: JobType.External } },
        { $unwind: '$jobLocation' }, // Expand jobLocation array
        {
          $group: {
            _id: { $toObjectId: '$jobLocation' }, // Convert jobLocation to ObjectId
            value: { $sum: 1 }, // Rename jobCount to 'value'
          },
        },
        {
          $lookup: {
            from: 'joblocations', // Collection name in MongoDB (lowercase!)
            localField: '_id',
            foreignField: '_id',
            as: 'locationDetails',
          },
        },
        {
          $unwind: {
            path: '$locationDetails',
            preserveNullAndEmptyArrays: true,
          }, // Prevent breaking if empty
        },
        {
          $project: {
            _id: 0,
            name: '$locationDetails.city', // Use city name as 'name'
            value: 1, // Keep job count as 'value'
            x: '$locationDetails.latitude', // Latitude
            y: '$locationDetails.longitude', // Longitude
          },
        },
        {
          $match: { name: { $ne: null } }, // Ensure valid city names only
        },
      ]);

      return result;
    } catch (error) {
      throw new InternalServerErrorException(
        `Error fetching job count by location: ${error.message}`,
      );
    }
  }

  async getRecruiterDashboard(user: any) {
    try {
      const userId = user._id;

      // 1️⃣ Get all active external jobs created by the recruiter
      const activeJobs = await this.jobModel
        .find({
          postingOrg: user?.org?._id,
          isDeleted: false,
          isOpen: true,
        })
        .exec();

      const activeJobsCount = activeJobs.length;
      const activeJobIds = activeJobs.map((job) => job._id.toString());

      // 2️⃣ Get distinct job IDs where the recruiter has applied (to avoid multiple applications per job)
      const appliedJobIds = await this.jobApplicationModel.distinct('jobId', {
        createdBy: userId,
        jobId: { $in: activeJobIds },
        isRejected: false,
      });

      const appliedJobsCount = appliedJobIds.length;

      return {
        activeJobsCount,
        appliedJobsCount,
      };
    } catch (error) {
      this.logger.error(
        `Error retrieving recruiter dashboard data: ${error.message}`,
        error.stack,
      );
      throw new InternalServerErrorException(
        `Error retrieving recruiter dashboard data. ${error.message}`,
      );
    }
  }

  async getRecruiterJobCounts(user: any, fromDate?: string, toDate?: string) {
    try {
      const orgId = user.companyId?._id;
      const userId = user._id;

      // Convert string dates to actual Date objects if provided
      const dateFilter: any = {};
      if (fromDate) dateFilter.$gte = new Date(fromDate);
      if (toDate) {
        const toDateObj = new Date(toDate);
        toDateObj.setDate(toDateObj.getDate() + 1); // Move to the next day
        dateFilter.$lt = toDateObj;
      }

      // Apply date filter only if at least one date is provided
      const dateCondition = Object.keys(dateFilter).length
        ? { createdAt: dateFilter }
        : {};

      // Step 1: Get all matching vendor orgs
      const existingOrgs = await this.orgsModel
        .find({
          'contactDetails.contactEmail': user.email,
          isDeleted: false,
          orgType: OrgType.VENDOR_ORG,
        })
        .exec();

      // Step 2: Extract org _ids
      const vendorOrgIds = existingOrgs.map((org) => org._id.toString());
      console.log('vendorOrgIds', vendorOrgIds);
      // Step 3: Prepare allocation query
      const queryConditions: any = {
        kind: 'JobAllocationToVendors',
        isDeleted: false,
        vendor: { $in: vendorOrgIds }, // Match any of the vendor orgs
      };
      // Step 4: Query allocations
      const jobAllocations = await this.jobAllocationBasesModel
        .find(queryConditions)
        .exec();
      const allocatedjobIds = jobAllocations.map(
        (allocation) => new Types.ObjectId(allocation.job.toString()),
      );
      let vendorJobs = await this.jobModel
        .find({ _id: { $in: allocatedjobIds } })
        .exec();

      const allJobs = [...vendorJobs];

      // Create a Map to store unique jobs by _id
      const uniqueJobsMap = new Map<string, any>();

      allJobs.forEach((job) => {
        uniqueJobsMap.set(job._id.toString(), job);
      });
      // Step 1: Create a Set of job IDs from allJobs
      const allJobIdsSet = new Set(allJobs.map((job) => job._id.toString()));

      // Convert back to an array
      const openJobsCount = Array.from(uniqueJobsMap.values());

      // Step 2: Extract org _ids
      const vendorCompanyIds = existingOrgs.map(
        (org) => org.companyId?.toString() ?? '',
      );
      console.log('vendorCompanyIds', vendorCompanyIds);

      const totalFreelancingJobsCount = await this.jobModel.countDocuments({
        postingOrg: { $in: vendorCompanyIds },
        shareWithFreelancers: true,
        isOpen: true,
        isDeleted: false,
        // jobType: JobType.External,
        ...dateCondition,
      });

      // 2️⃣ Get job IDs where the user has applied at least once
      const rawAppliedJobIds = await this.jobApplicationModel.distinct(
        'jobId',
        {
          createdBy: userId,
          isRejected: false,
          ...dateCondition,
        },
      );
      // Step 3: Filter applied job IDs to only include those present in allJobs
      const appliedJobIds = rawAppliedJobIds
        .map((id) => id.toString())
        .filter((id) => allJobIdsSet.has(id));

      // 3️⃣ Get count of jobs that are external and belong to the recruiter's organization where user has applied
      // const appliedExternalJobsCount = await this.jobModel.countDocuments({
      //   // jobType: JobType.External,
      //   postingOrg: orgId,
      //   _id: { $in: appliedJobIds },
      //   isDeleted: false,
      //   isOpen: true
      // });

      // const appliedFreelancingJobsCount = await this.jobModel.countDocuments({
      //   // jobType: JobType.External,
      //   shareWithFreelancers: true,
      //   _id: { $in: appliedJobIds }
      // });

      const activeJobsCount = await this.jobModel
        .distinct('_id', {
          // jobType: JobType.External,
          _id: { $in: appliedJobIds },
          ...dateCondition,
          // isDeleted: false,
          // isOpen: true,
          // $or: [
          //   { shareWithFreelancers: true } // Freelancing jobs
          // ]
        })
        .then((ids) => ids.length);

      // Get distinct job application IDs
      let jobApplications = await this.jobApplicationModel
        .find({
          createdBy: userId,
          isRejected: false,
          ...dateCondition,
        })
        .exec();

      // Step 3: Filter applications to only include those in allJobs
      jobApplications = jobApplications.filter((app) =>
        allJobIdsSet.has(app.jobId.toString()),
      );

      let jobApplicationIds = jobApplications.map((app) => app._id.toString());

      // Get latest offers for submitted job applications
      const latestOffers = await this.offerModel.aggregate([
        { $match: { jobApplication: { $in: jobApplicationIds } } },
        { $match: { isOnBoarded: true, ...dateCondition } },
        { $sort: { createdAt: -1 } }, // Sort offers by creation date (latest first)
        {
          $group: {
            _id: '$jobApplication', // Group by job application
            latestOffer: { $first: '$$ROOT' }, // Select the most recent offer per application
          },
        },
      ]);

      const totalOffersGenerated = latestOffers.length; // Number of job applications with at least one offer

      return {
        open: openJobsCount.length,
        active: activeJobsCount,
        freelancing: totalFreelancingJobsCount,
        request: jobApplications.length,
        responded: totalOffersGenerated,
      };
    } catch (error) {
      this.logger.error(
        `Error retrieving recruiter job counts: ${error.message}`,
        error.stack,
      );
      throw new InternalServerErrorException(
        `Error retrieving recruiter job counts. ${error.message}`,
      );
    }
  }

  async getClientWiseJobsCountByPostingOrg(
    user: any,
    page: number,
    limit: number,
  ) {
    try {
      const postingOrg = user.org._id;

      // Fetch total count of clients (for pagination)
      const totalClients = await this.orgsModel.countDocuments({
        // orgType: OrgType.CUSTOMER_ORG,
        orgType: { $in: [OrgType.CUSTOMER_ORG, OrgType.ACCOUNT_ORG] },
        createdByOrg: user.org._id,
        // createdBy: user.orgAdmin
      });

      // Fetch paginated Client Organizations
      const clients = await this.orgsModel
        .find(
          {
            // orgType: OrgType.CUSTOMER_ORG,
            orgType: { $in: [OrgType.CUSTOMER_ORG, OrgType.ACCOUNT_ORG] },
            createdByOrg: user.org._id,
            // createdBy: user.orgAdmin
          },
          { _id: 1, title: 1 }, // Select only necessary fields
        )
        .skip((page - 1) * limit)
        .limit(limit)
        .lean();

      if (!clients.length) return { clientWiseJobCount: [], totalClients };

      const clientIdSet = new Set(
        clients.map((client) => client._id.toString()),
      );

      // Fetch Jobs grouped by End Client
      const groupByEndClient = await this.jobModel.aggregate([
        {
          $match: {
            postingOrg: postingOrg,
            endClientOrg: { $in: Array.from(clientIdSet) },
            isDeleted: false,
            isOpen: true,
          },
        },
        {
          $group: {
            _id: '$endClientOrg',
            totalCount: { $sum: 1 },
          },
        },
      ]);

      // Convert clients list into a Map for quick lookup
      // const clientMap = new Map(clients.map(client => [client._id.toString(), client.title]));

      // Convert job counts into a Map
      const jobCountMap = new Map(
        groupByEndClient.map((client) => [
          client._id.toString(),
          client.totalCount,
        ]),
      );

      // Fetch Jobs (only the necessary fields)
      const jobs = await this.jobModel
        .find(
          {
            postingOrg: postingOrg,
            endClientOrg: { $in: Array.from(clientIdSet) },
            isDeleted: false,
            isOpen: true,
          },
          { _id: 1, endClientOrg: 1 },
        )
        .lean();

      const jobIdSet = new Set(jobs.map((job) => job._id.toString()));

      // Fetch Job Applications
      const jobApplications = await this.jobApplicationModel
        .find(
          { jobId: { $in: Array.from(jobIdSet) }, isRejected: false },
          { _id: 1, jobId: 1 },
        )
        .lean();

      const jobApplicationMap = new Map(
        jobApplications.map((app) => [
          app._id.toString(),
          app.jobId.toString(),
        ]),
      );

      // Fetch Latest Offers per Job Application
      const offers = await this.offerModel.aggregate([
        {
          $match: {
            jobApplication: { $in: Array.from(jobApplicationMap.keys()) },
          },
        },
        { $sort: { createdAt: -1 } }, // Sort offers by latest createdAt
        {
          $group: {
            _id: '$jobApplication',
            latestOffer: { $first: '$$ROOT' },
          },
        },
        { $replaceRoot: { newRoot: '$latestOffer' } },
      ]);

      // Count Offers by End Client
      const offerCountByClient: Record<string, number> = {};

      for (const offer of offers) {
        const jobAppId = offer.jobApplication.toString();
        const jobId = jobApplicationMap.get(jobAppId);

        if (jobId) {
          const job = jobs.find((j) => j._id.toString() === jobId);
          if (job?.endClientOrg) {
            const endClientOrgId = job.endClientOrg.toString();
            offerCountByClient[endClientOrgId] =
              (offerCountByClient[endClientOrgId] || 0) + 1;
          }
        }
      }

      // Ensure all clients are included, even those with zero jobs
      const clientWiseJobCount = clients
        .map((client) => ({
          endClientName: client.title,
          jobCount: jobCountMap.get(client._id.toString()) || 0, // Default to 0 if no jobs
          offerCount: offerCountByClient[client._id.toString()] || 0, // Default to 0 if no offers
        }))
        .sort((a, b) => b.offerCount - a.offerCount); // Sort by offerCount in descending order;

      return { clientWiseJobCount, totalClients };
    } catch (error) {
      this.logger.error(error);
      throw new Error(
        `Error fetching job counts for posting organization. ${error.message}`,
      );
    }
  }

  async getSourceWiseJobsCountByPostingOrg(user: any) {
    try {
      const postingOrg = user.org._id;

      // Fetch Internal Jobs and Count
      const internalJobs = await this.jobModel.aggregate([
        {
          $match: {
            postingOrg: postingOrg,
            jobType: JobType.Internal,
            isDeleted: false,
            isOpen: true,
          },
        },
        { $project: { _id: 1 } }, // Select only _id
      ]);

      const internalJobIds = internalJobs.map((job) => job._id.toString());

      // Fetch Internal Job Applications (IDs only)
      const internalJobApplications = await this.jobApplicationModel
        .find(
          { jobId: { $in: internalJobIds }, isRejected: false },
          { _id: 1, jobId: 1 },
        )
        .lean();

      const internalJobApplicationIds = internalJobApplications.map((app) =>
        app._id.toString(),
      );

      // Fetch Internal Offers and Count
      // const internalJobOfferCount = await this.offerModel.countDocuments({
      //     jobApplication: { $in: internalJobApplicationIds }
      // });

      // Fetch Latest Offers per Job Application
      const internalJobOffers = await this.offerModel.aggregate([
        { $match: { jobApplication: { $in: internalJobApplicationIds } } },
        { $sort: { createdAt: -1 } }, // Sort offers by latest createdAt
        {
          $group: {
            _id: '$jobApplication',
            latestOffer: { $first: '$$ROOT' },
          },
        },
        { $replaceRoot: { newRoot: '$latestOffer' } },
      ]);

      // Fetch Vendor Organizations
      // const vendorOrgs = await this.orgsModel.find(
      //     { companyId: postingOrg, orgType: StatusConfigType.VENDOR_ORG },
      //     { _id: 1, companyId: 1 } // Select only necessary fields
      // ).lean();

      // const vendorCompanyIds = vendorOrgs.map(org => org.companyId?.toString());

      // Fetch Vendor Jobs and Count
      const vendorJobs = await this.jobModel.aggregate([
        {
          $match: {
            postingOrg: postingOrg,
            // jobType: JobType.External, //??can it be internal because BuHead can also assign internal jobs to vendors
            shareWithVendors: true,
            isDeleted: false,
            isOpen: true,
          },
        },
        { $project: { _id: 1 } }, // Select only _id
      ]);

      const vendorJobIds = vendorJobs.map((job) => job._id.toString());

      // Fetch Vendor Job Applications (IDs only)
      const vendorJobApplications = await this.jobApplicationModel
        .find(
          { jobId: { $in: vendorJobIds }, isRejected: false },
          { _id: 1, jobId: 1 },
        )
        .lean();

      const vendorJobApplicationIds = vendorJobApplications.map((app) =>
        app._id.toString(),
      );

      // // Fetch Vendor Offers and Count
      // const vendorJobOfferCount = await this.offerModel.countDocuments({
      //     jobApplication: { $in: vendorJobApplicationIds }
      // });

      // Fetch Latest Offers per Job Application
      const vendorJobOffers = await this.offerModel.aggregate([
        { $match: { jobApplication: { $in: vendorJobApplicationIds } } },
        { $sort: { createdAt: -1 } }, // Sort offers by latest createdAt
        {
          $group: {
            _id: '$jobApplication',
            latestOffer: { $first: '$$ROOT' },
          },
        },
        { $replaceRoot: { newRoot: '$latestOffer' } },
      ]);

      // Final Result
      const result = {
        internal: {
          internalJobsCount: internalJobs?.length,
          internalOfferCount: internalJobOffers.length,
        },
        vendor: {
          vendorJobCount: vendorJobs.length,
          vendorOfferCount: vendorJobOffers.length,
        },
      };

      return { result };
    } catch (error) {
      this.logger.error(error);
      throw new Error(
        `Error fetching job counts for posting organization. ${error.message}`,
      );
    }
  }

  async getActiveJobsCountByPostingOrg(user: any) {
    try {
      const postingOrg = user.org._id;

      const stageWiseJobCount = await this.jobApplicationModel.aggregate([
        // Lookup to fetch job details
        { $addFields: { jobId: { $toObjectId: '$jobId' } } },
        {
          $lookup: {
            from: 'jobs', // Jobs collection
            localField: 'jobId',
            foreignField: '_id',
            as: 'jobDetails',
          },
        },
        { $unwind: { path: '$jobDetails', preserveNullAndEmptyArrays: true } }, // Flatten the job details array

        // Match active jobs and non-deleted job applications
        {
          $match: {
            'jobDetails.postingOrg': postingOrg, //from jobsModel
            'jobDetails.isOpen': true, //from jobsModel
            'jobDetails.isDeleted': false, //from jobsModel
            isDeleted: false, //from jobApplicationModel
            isRejected: false, //from jobApplicationModel
          },
        },

        // Lookup to fetch stage details
        {
          $lookup: {
            from: 'stages', // Stages collection
            localField: 'stage', // stageId from jobApplicationModel
            foreignField: '_id', // _id from stageModel
            as: 'stageDetails',
          },
        },

        // Flatten stageDetails array (if stage exists)
        {
          $unwind: { path: '$stageDetails', preserveNullAndEmptyArrays: true },
        },

        {
          $group: {
            _id: '$stageDetails.name', // Group by stage name
            stageName: { $first: '$stageDetails.name' }, // Rename _id to stageName
            count: { $sum: 1 }, // Count number of job applications per stage
          },
        },
        {
          $project: {
            _id: 0, // Remove MongoDB's default _id
            stageName: 1,
            count: 1,
          },
        },
        { $sort: { count: -1 } },
      ]);

      // console.log(stageWiseJobCount);
      return stageWiseJobCount;
    } catch (error) {
      this.logger.error(error);
      throw new Error(
        `Error fetching job counts for posting organization. ${error.message}`,
      );
    }
  }

  async findAllpostingOrgJobCount(user: any) {
    try {
      const postingOrg = user.org._id;

      // Jobs assigned to this user via job allocations
      const jobAllocations = await this.jobAllocationBasesModel
        .find({
          assignee: user._id.toString(),
          kind: 'JobAllocationToAssignees',
          isDeleted: false,
        })
        .exec();

      const jobIds = jobAllocations.map(
        (allocation) => new Types.ObjectId(allocation.job.toString()),
      );

      if (user.roles.includes(Role.DeliveryManager)) {
        // Fetch Jobs grouped by End Client
        const jobscount = await this.jobModel.aggregate([
          {
            $match: {
              postingOrg: postingOrg,
            },
          },
          {
            $group: {
              _id: null,
              totalJobs: { $sum: 1 },
              activeJobs: {
                $sum: { $cond: [{ $eq: ['$isOpen', true] }, 1, 0] },
              },
              draftJobs: {
                $sum: {
                  $cond: [
                    {
                      $and: [
                        { $eq: ['$isOpen', false] },
                        { $eq: ['$isDraft', true] },
                      ],
                    },
                    1,
                    0,
                  ],
                },
              },
              closedJobs: {
                $sum: {
                  $cond: [
                    {
                      $and: [
                        { $eq: ['$isOpen', false] },
                        { $eq: ['$isDraft', false] },
                      ],
                    },
                    1,
                    0,
                  ],
                },
              },
            },
          },
          {
            $project: {
              _id: 0, // Removes the _id field from the final output
              totalJobs: 1,
              activeJobs: 1,
              draftJobs: 1,
              closedJobs: 1,
            },
          },
        ]);
        return jobscount[0] || {};
      }
      let accountManagerJobIds: string[] = [];
      if (user.roles.includes(Role.AccountManager)) {
        const assignedOrgs = await this.orgsModel
          .find({
            assignTo: user?._id?.toString(),
            isDeleted: false,
            orgType: { $in: [OrgType.ACCOUNT_ORG, OrgType.CUSTOMER_ORG] },
            createdByOrg: user.org?._id?.toString(),
          })
          .select('_id')
          .lean();
        console.log('Assigned Orgs:', assignedOrgs);

        // Step 2: Get orgs created by the current user
        const createdOrgs = await this.orgsModel
          .find({
            createdBy: user._id.toString(),
            isDeleted: false,
            orgType: { $in: [OrgType.ACCOUNT_ORG, OrgType.CUSTOMER_ORG] },
            createdByOrg: user.org?._id?.toString(),
          })
          .select('_id')
          .lean();

        console.log('Created Orgs:', createdOrgs);

        // const assignedOrgIds = assignedOrgs.map(org => org._id.toString());

        // Step 3: Merge and deduplicate
        const assignedOrgIdsSet = new Set<string>();

        assignedOrgs.forEach((org) =>
          assignedOrgIdsSet.add(org._id.toString()),
        );
        createdOrgs.forEach((org) => assignedOrgIdsSet.add(org._id.toString()));

        const assignedOrgIds = Array.from(assignedOrgIdsSet);
        console.log('Assigned Org IDs:', assignedOrgIds);

        const accountManagerJobs = await this.jobModel
          .find({
            postingOrg: postingOrg,
            $or: [
              { createdBy: user._id.toString() },
              { endClientOrg: { $in: assignedOrgIds } },
            ],
          })
          .select('_id isOpen isDraft')
          .lean();
        // Fetch Jobs grouped by End Client
        // const jobscount = await this.jobModel.aggregate([
        //   {
        //     $match: {
        //       postingOrg: postingOrg,
        //       $or: [
        //         { createdBy: user._id.toString() }, // Jobs created by the user
        //         { endClientOrg: { $in: assignedOrgIds } }
        //       ]
        //     }
        //   },
        //   {
        //     $group: {
        //       _id: null,
        //       totalJobs: { $sum: 1 },
        //       activeJobs: { $sum: { $cond: [{ $eq: ["$isOpen", true] }, 1, 0] } },
        //       draftJobs: {
        //         $sum: {
        //           $cond: [
        //             { $and: [{ $eq: ["$isOpen", false] }, { $eq: ["$isDraft", true] }] },
        //             1,
        //             0
        //           ]
        //         }
        //       },
        //       closedJobs: {
        //         $sum: {
        //           $cond: [
        //             { $and: [{ $eq: ["$isOpen", false] }, { $eq: ["$isDraft", false] }] },
        //             1, 0]
        //         }
        //       },
        //     }
        //   },
        //   {
        //     $project: {
        //       _id: 0,  // Removes the _id field from the final output
        //       totalJobs: 1,
        //       activeJobs: 1,
        //       draftJobs: 1,
        //       closedJobs: 1
        //     }
        //   }
        // ]);
        // return jobscount[0] || {};;
        accountManagerJobIds = accountManagerJobs.map((job) =>
          job._id.toString(),
        );
      }

      // Step 2: Fetch job IDs for other roles (e.g., recruiter)
      let otherRoleJobIds: string[] = [];
      const otherRoleJobs = await this.jobModel
        .find({
          postingOrg: postingOrg,
          $or: [{ createdBy: user._id.toString() }, { _id: { $in: jobIds } }],
        })
        .select('_id isOpen isDraft')
        .lean();
      otherRoleJobIds = otherRoleJobs.map((job) => job._id.toString());

      // Step 3: Merge and deduplicate job IDs
      const uniqueJobIdSet = new Set<string>([
        ...accountManagerJobIds,
        ...otherRoleJobIds,
      ]);
      const uniqueJobIds = Array.from(uniqueJobIdSet);
      // Fetch Jobs grouped by End Client
      const jobscount = await this.jobModel.aggregate([
        {
          $match: {
            _id: { $in: uniqueJobIds.map((id) => new Types.ObjectId(id)) },
          },
        },
        {
          $group: {
            _id: null,
            totalJobs: { $sum: 1 },
            activeJobs: { $sum: { $cond: [{ $eq: ['$isOpen', true] }, 1, 0] } },
            draftJobs: {
              $sum: {
                $cond: [
                  {
                    $and: [
                      { $eq: ['$isOpen', false] },
                      { $eq: ['$isDraft', true] },
                    ],
                  },
                  1,
                  0,
                ],
              },
            },
            closedJobs: {
              $sum: {
                $cond: [
                  {
                    $and: [
                      { $eq: ['$isOpen', false] },
                      { $eq: ['$isDraft', false] },
                    ],
                  },
                  1,
                  0,
                ],
              },
            },
          },
        },
        {
          $project: {
            _id: 0, // Removes the _id field from the final output
            totalJobs: 1,
            activeJobs: 1,
            draftJobs: 1,
            closedJobs: 1,
          },
        },
      ]);

      return jobscount[0] || {};
    } catch (error) {
      this.logger.error(error);
      throw new Error(
        `Error fetching job counts for posting organization. ${error.message}`,
      );
    }
  }

  async publicJobs(page = 1, limit = 10, search?: string): Promise<Job[]> {
    try {
      let conditions: any = {
        isDeleted: false,
        jobType: JobType.External,
        isOpen: true,
      };

      if (search) {
        const regex = new RegExp(search, 'i'); // Case-insensitive
        conditions.$or = [
          { title: regex },
          { primarySkills: { $elemMatch: { $regex: regex } } },
          { secondarySkills: { $elemMatch: { $regex: regex } } },
          { employmentType: regex },
          { workMode: regex },
          { hiringMode: regex },
        ];
      }

      const jobs = await this.jobModel
        .find(conditions)
        .populate(this.populateOptions)
        .sort({ createdAt: -1 })
        .skip((page - 1) * limit)
        .limit(limit)
        .exec();

      return jobs;
    } catch (error) {
      this.logger.error(error);
      throw new InternalServerErrorException(
        `An error occurred while retrieving jobs. ${error?.message}`,
      );
    }
  }

  async addOrUpdateVendorRateCard(
    jobId: Types.ObjectId,
    vendorId: Types.ObjectId,
    rateCardId: Types.ObjectId,
  ) {
    try {
      const job = await this.jobModel.findById(jobId).exec();

      if (!job) {
        throw new NotFoundException(`Job with ID ${jobId} not found.`);
      }

      if (!Array.isArray(job.vendorRateCards)) {
        job.vendorRateCards = [];
      }

      const existingIndex = job.vendorRateCards.findIndex(
        (item) => item.vendor.toString() === vendorId.toString(),
      );

      if (existingIndex >= 0) {
        // Update rate card if vendor already exists
        job.vendorRateCards[existingIndex].rateCard = rateCardId;
      } else {
        // Add new vendor-rateCard pair
        job.vendorRateCards.push({ vendor: vendorId, rateCard: rateCardId });
      }

      await job.save();

      const updatedJob = await this.jobModel.findById(jobId).exec();

      return updatedJob;
    } catch (error) {
      this.logger.error(
        `Error in addOrUpdateVendorRateCard for Job ID ${jobId}: ${error?.message}`,
      );
      throw new InternalServerErrorException(
        `Failed to update vendor rate card. ${error.message}`,
      );
    }
  }

  async findAllJobsAndApplications() {
    try {
      // let jobs = await this.jobModel.find(conditions)
      //   .populate(this.populateOptions)
      //   .sort({ createdAt: -1 })
      //   .exec();

      const jobs = await this.jobModel
        .find() // Add any filters like orgId or createdBy here if needed
        .select('title primarySkills secondarySkills endClientName jdUrl') // Select required fields
        .populate({
          path: 'jdUrl',
          model: 'FileMetadata',
          select: 'originalName locationUrl',
        })
        .sort({ createdAt: -1 }) // Latest first
        .exec(); // Optional: If you want plain JS objects

      const jobIds = jobs.map((job) => job._id.toString());

      const jobApplications = await this.jobApplicationModel
        .find({
          jobId: { $in: jobIds },
        })
        .select('jobId evaluationForm coverLetterMetadata resumeMetadata') // Only required fields
        .populate({
          path: 'evaluationForm',
          model: 'EvaluationForm', // replace with actual model name if different
          select: 'skill years months rating',
        })
        .populate({
          path: 'coverLetterMetadata',
          model: 'FileMetadata',
          select: 'originalName locationUrl',
        })
        .populate({
          path: 'resumeMetadata',
          model: 'FileMetadata',
          select: 'originalName locationUrl',
        })
        .exec();

      const jobAppMap = new Map<string, any[]>();

      for (const app of jobApplications) {
        const jobId = app.jobId.toString();
        if (!jobAppMap.has(jobId)) jobAppMap.set(jobId, []);
        jobAppMap.get(jobId)!.push(app);
      }

      // Combine jobs with their applications
      const jobWithApplications = jobs.map((job) => ({
        ...job.toObject(),
        jobApplications: jobAppMap.get(job._id.toString()) || [],
      }));

      return jobWithApplications;
    } catch (error) {
      this.logger.error(error);
      throw new InternalServerErrorException(
        `An error occurred while retrieving jobs. ${error?.message}`,
      );
    }
  }
}
