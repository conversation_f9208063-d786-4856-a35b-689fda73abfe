import { Module } from '@nestjs/common';
import { AttendanceService } from './attendance.service';
import { AttendanceController } from './attendance.controller';
import { ConfigModule } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { EndpointsRolesModule } from 'src/endpoints-roles/endpoints-roles.module';
import { UserModule } from 'src/user/user.module';
import { CommonModule } from 'src/common/common.module';
import { MongooseModule } from '@nestjs/mongoose';
import { BasicUser, BasicUserSchema } from 'src/user/schemas/basic-user.schema';
import { AttendanceSchema } from './schemas/attendance.schema';
import { AttendanceSessionSchema } from './schemas/attendance-session.schema';
import { Project, ProjectSchema } from 'src/projects/schemas/project.schema';

@Module({
  controllers: [AttendanceController],
  imports: [
    ConfigModule,
    JwtModule, EndpointsRolesModule,
    UserModule,
    CommonModule,
    MongooseModule.forFeature([
      { name: 'Attendance', schema: AttendanceSchema },
      { name: 'AttendanceSession', schema: AttendanceSessionSchema },
      { name: BasicUser.name, schema: BasicUserSchema },
      { name: Project.name, schema: ProjectSchema }
    ]),
  ],
  providers: [AttendanceService],
})
export class AttendanceModule { }
