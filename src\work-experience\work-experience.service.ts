import { Injectable, InternalServerErrorException, Logger, NotFoundException } from '@nestjs/common';
import { CreateWorkExperienceDto } from './dto/create-work-experience.dto';
import { UpdateWorkExperienceDto } from './dto/update-work-experience.dto';
import { ConfigService } from '@nestjs/config';
import { WorkExperience, WorkExperienceDocument } from './schemas/work-experience.schema';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';

@Injectable()
export class WorkExperienceService {
  private readonly logger = new Logger(WorkExperienceService.name);

  constructor(private configService: ConfigService, @InjectModel(WorkExperience.name) private workExperienceModel: Model<WorkExperience>) {

  }

  async create(createWorkExperienceDto: CreateWorkExperienceDto) {
    try {
      const createdWorkExperienceDto = new this.workExperienceModel(createWorkExperienceDto);
      const reponse =  await createdWorkExperienceDto.save();
      return reponse
    } catch (error) {
      this.logger.debug(`Failed to create work Experience. ${error}`);
      throw new InternalServerErrorException(`Unknown error when creating the work Experience. ${error.message}`);
    }
  }

  async findAll(): Promise<WorkExperienceDocument[]> {
    return this.workExperienceModel.find()
      .populate({ path: 'createdBy', select: '_id roles firstName' })
      .exec()
  }

  async findOne(workExperienceId: Types.ObjectId): Promise<WorkExperienceDocument> {
    try {
      const workExperience = await this.workExperienceModel.findById(workExperienceId)
        .populate({ path: 'createdBy', select: '_id roles firstName' })
        .exec()
      if (!workExperience) {
        throw new NotFoundException(`work Experience not found with ID ${workExperienceId}`);
      }
      return workExperience;
    } catch (error) {
      this.logger.error(error);
      this.logger.error(`An error occurred in fetching work Experience by Id ${workExperienceId}. ${error?.message}`);
      throw error;
    }
  }

  async findById(workExperienceId: Types.ObjectId): Promise<WorkExperienceDocument> {
    try {
      const workExperience = await this.workExperienceModel.findById(workExperienceId)
      if (!workExperience) {
        throw new NotFoundException(`Work experience not found with ID ${workExperienceId}`);
      }
      return workExperience;
    } catch (error) {
      this.logger.error(error);
      this.logger.error(`An error occurred in feching work experience by Id ${workExperienceId}. ${error?.message}`);
      throw error;

    }
  }

  async update(workExperienceId: Types.ObjectId, updateWorkExperienceDto: UpdateWorkExperienceDto) {
    try {
      const workExperience = await this.findById(workExperienceId);
      if (!workExperience) {
        throw new NotFoundException(`Work Experience not found with ID ${workExperienceId}`);
      }
      return await this.workExperienceModel.findByIdAndUpdate(workExperienceId, updateWorkExperienceDto, { new: true })
        .populate({ path: 'createdBy', select: '_id roles firstName' })
        .exec();

    } catch (error) {
      this.logger.error(error);
      this.logger.error(`An error occurred while updating work Experience by ID ${workExperienceId}. ${error?.message}`);
      throw error;
    }
  }

  async remove(workExperienceId: Types.ObjectId) {
    try {
      const workExperience = await this.findById(workExperienceId);
      if (!workExperience) {
        throw new NotFoundException(`work Experience not found with ID ${workExperience}`);
      }
      await this.workExperienceModel.deleteOne({ _id: workExperienceId });
      return { message: 'work Experience deleted' };
    } catch (error) {
      this.logger.error(`An error occurred while hard deleting by ID ${workExperienceId}. ${error?.message}`);
      throw new InternalServerErrorException('An error occurred while hard deleting the work Experience');
    }
  }
}
