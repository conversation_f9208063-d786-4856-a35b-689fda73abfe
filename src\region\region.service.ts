import { BadRequestException, ConflictException, HttpException, HttpStatus, Injectable, InternalServerErrorException, Logger, NotFoundException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Region, RegionDocument } from './schemas/region.schema';
import { Comment } from 'src/common/schemas/comment.schema';
import { StatusDocument } from 'src/status/schemas/status.schema'
import { ChangeStatusDto } from './dto/change-status.dto';
import { StatusService } from 'src/status/status.service';
import { State, StateDocument } from 'src/state/schemas/state.schema';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { CountryDocument } from 'src/country/schemas/country.schema';
import { StatusConfigType } from 'src/shared/constants';


@Injectable()
export class RegionService {


  private readonly logger = new Logger(RegionService.name);

  constructor(private configService: ConfigService,
    @InjectModel(Region.name) private regionModel: Model<Region>,
    @InjectModel(Comment.name) private commentModel: Model<Comment>,
    @InjectModel(State.name) private stateModel: Model<State>,
    private statusService: StatusService,
    private eventEmitter: EventEmitter2
  ) { }

  async create(createRegionDto: { country: Types.ObjectId; state: Types.ObjectId }, user: any): Promise<Region> {

    try {
      const defaultStatus = await this.getDefaultStatus();

      const regionData = {
        ...createRegionDto,
        status: defaultStatus,
        createdBy: user._id
      }

      const newRegion = await new this.regionModel(regionData).save();

      const populatedRegion = await this.findById(newRegion._id)

      // console.log(populatedRegion)

      this.emitEvent('region.created', { populatedRegion, user });

      return populatedRegion;
    } catch (error) {
      if (error.code === 11000) {
        throw new HttpException('Region with provided details already exists', HttpStatus.CONFLICT);
      }
      throw new InternalServerErrorException(`Error while creating Region: ${error?.message}`);
    }
  }

  async findRegionByCountryAndState(countryId: Types.ObjectId, stateId: Types.ObjectId) {
    try {
      // First, try to find a region with the specific countryId and stateId

      let region = await this.regionModel.findOne({
        country: countryId,
        state: stateId,
      })
        .populate({
          path: 'status',
          select: '_id name'
        })
        .populate({ path: 'country', select: '_id countryName countryPhoneCode currencyCode' })
        .populate({ path: 'state', select: '_id stateName' })
        .exec();

      console.log(region, 'specific region result')

      let allState;

      // If no region is found, try to find the 'ALL' stateId specific to the countryId
      if (!region) {
        allState = await this.stateModel.findOne({
          stateName: 'ALL',
          country: countryId, // Ensure the 'ALL' state is related to the specific countryId
        }).exec();

        console.log(allState, 'overall country result')

        // If 'ALL' state is found, use its _id to find the region
        if (allState) {
          console.log(allState._id)
          const newStateId = allState._id
          region = await this.regionModel.findOne({
            country: countryId,
            state: newStateId,
          })
            .populate({
              path: 'status',
              select: '_id name'
            })
            .populate({ path: 'country', select: '_id countryName countryPhoneCode currencyCode' })
            .populate({ path: 'state', select: '_id stateName' })
            .exec();
          console.log(`region result`, region)
        }

      }

      // If still no region is found, throw a NotFoundException
      if (!region) {
        throw new NotFoundException(`Region not found for countryId: ${countryId}, stateId: ${stateId}`);
      }

      const status = await this.statusService.findById(region.status);

      if (!status || status.functionality !== 'ACTIVATE') {
        throw new NotFoundException(`Region not found or inactive for countryId: ${countryId}, stateId: ${stateId}`);
      }

      // Return the found region
      return region;
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      this.logger.error(`Error while finding region: ${error.message}`, error.stack);
      throw new InternalServerErrorException('Error while finding region');
    }

  }

  async getRegionsByStatus(status: Types.ObjectId) {
    try {
      const regions = await this.regionModel
        .find({ status })
        .populate({
          path: 'status',
          select: '_id name'
        })
        .populate({ path: 'country', select: '_id countryName countryPhoneCode currencyCode' })
        .populate({ path: 'state', select: '_id stateName' })
        .sort({ updatedAt: -1 })
        .exec();
      console.log('Query:', { status });
      console.log('Regions:', regions);
      return regions;
    } catch (error) {
      throw new InternalServerErrorException(`Error while fetching countries by status ID: ${error.message}`);
    }
  }

  async getRegionsCountByStatus(): Promise<Array<{ id: string; name: string; count: number }>> {
    try {
      // Aggregate with $lookup to join Status collection and get status names

      const allStatuses = await this.statusService.findAllStatusByType('Region');

      const statusMap: Record<string, { id: string; name: string; count: number }> = {};


      const result = await this.regionModel.aggregate([
        {
          $lookup: {
            from: 'status', // The name of the Status collection
            localField: 'status',
            foreignField: '_id',
            as: 'statusDetails',
          }
        },
        {
          $unwind: '$statusDetails' // Unwind the joined status details
        },
        {
          $group: {
            _id: '$statusDetails._id', // Group by status name
            statusName: { $first: '$statusDetails.name' },
            count: { $sum: 1 } // Count the number of regions for each status name
          }
        },
        {
          $project: {
            _id: 0, // Exclude the _id field
            id: '$_id', // Include the status ID
            name: '$statusName', // Rename _id field to statusName. 
            count: 1 // Include the count field
          }
        }
      ]);

      result.forEach(entry => {
        statusMap[entry.id] = {
          id: entry.id,
          name: entry.name,
          count: (statusMap[entry.id]?.count || 0) + entry.count
        };
      });

      // Ensure all statuses are included with a count of 0 if they have no regions
      allStatuses.forEach(status => {
        if (!statusMap[status._id.toString()]) {
          statusMap[status._id.toString()] = {
            id: status._id.toString(),
            name: status.name,
            count: 0
          };
        }
      });

      // Convert the result to an array of objects
      const statusArray: Array<{ id: string; name: string; count: number }> = Object.values(statusMap);;

      return statusArray;
    } catch (error) {
      throw new InternalServerErrorException(`Error while fetching regions count by status: ${error.message}`);
    }
  }

  async getAllRegions(): Promise<RegionDocument[]> {
    const result = this.regionModel
      .find()
      .populate({
        path: 'status',
        select: '_id name'
      })
      .populate({ path: 'country', select: '_id countryName countryPhoneCode currencyCode' })
      .populate({ path: 'state', select: '_id stateName' })
      .sort({ updatedAt: -1 })
      .exec();
    return result;
  }

  async findById(regionId: Types.ObjectId) {
    try {
      const Region = await this.regionModel.findById(regionId)
        .populate({
          path: 'status',
          select: '_id name functionality'
        })
        .populate({ path: 'country', select: '_id countryName countryPhoneCode currencyCode' })
        .populate({ path: 'state', select: '_id stateName' })
        .populate({ path: 'createdBy', select: '_id roles firstName email', model: 'BasicUser' })
        .exec();

      if (!Region) {
        throw new NotFoundException(`Region not found with ID ${regionId}`);
      }
      return Region;
    } catch (error) {
      // this.logger.error(error);
      this.logger.error(`An error occurred while fetching a Region by ID ${regionId}. ${error?.message}`);
      // throw new UnprocessableEntityException(`May be invalid id format ${id}`);
      throw error;
    }
  }

  async searchByName(name: string): Promise<RegionDocument[]> {
    if (!name) {
      throw new BadRequestException('Name parameter is required');
    }

    const regex = new RegExp(name, 'i');

    try {

      const results = await this.regionModel.aggregate([
        {
          $lookup: {
            from: 'countries',
            localField: 'country',
            foreignField: '_id',
            as: 'country'
          }
        },
        { $unwind: "$country" },
        {
          $lookup: {
            from: 'states',
            localField: 'state',
            foreignField: '_id',
            as: 'state'
          }
        },
        { $unwind: "$state" },
        {
          $lookup: {
            from: 'status', // Assuming your collection is named 'statuses'
            localField: 'status',
            foreignField: '_id',
            as: 'status'
          }
        },
        { $unwind: "$status" },
        {
          $match: {
            $or: [
              { 'country.countryName': { $regex: regex } },
              { 'state.stateName': { $regex: regex } }
            ]
          }
        },
        {
          $project: {
            _id: 1,
            country: { _id: 1, countryName: 1, countryPhoneCode: 1, currencyCode: 1 },
            state: { _id: 1, stateName: 1 },
            status: { _id: 1, name: 1 }
          }
        }
      ])
        .exec();

      return results;
    } catch (error) {
      this.logger.error(`Error while searching for regions: ${error.message}`, error.stack);
      throw new InternalServerErrorException('Error while searching for regions');
    }
  }

  async getDefaultStatus(): Promise<Types.ObjectId> {
    const statuses = await this.statusService.findAllStatusByType(StatusConfigType.REGION); // Assuming you have an order field to sort
    if (!statuses || statuses.length === 0) {
      throw new BadRequestException('No status available to set as default');
    }
    return statuses[0]._id; // Returning the ObjectId of the first status
  }


  async update(regionId: Types.ObjectId, updateRegionDto: { country: Types.ObjectId; state: Types.ObjectId }, user: Object) {
    try {
      const existingRegion = await this.findById(regionId);
      // console.log(existingRegion)
      if (!existingRegion) {
        throw new NotFoundException(`Region with ID "${regionId}" not found`);
      }

      const { country, state } = updateRegionDto;
      const updates: { country?: Types.ObjectId; state?: Types.ObjectId } = {};

      if (country && (existingRegion.country as CountryDocument)?._id?.toString() !== country.toString()) {
        updates.country = country;
      }

      if (state && (existingRegion.state as StateDocument)?._id?.toString() !== state.toString()) {
        updates.state = state;
      }

      if (Object.keys(updates).length === 0) {
        return existingRegion;
      }

      const updatedRegion = await this.regionModel
        .findByIdAndUpdate(regionId, updateRegionDto, { new: true, runValidators: true })
        .populate({ path: 'country', select: '_id countryName countryPhoneCode currencyCode' })
        .populate({ path: 'state', select: '_id stateName' })
        .exec();

      console.log(updates)

      if (updates.country) {
        this.emitEvent('region.country.updated', { existingRegion, updatedRegion, user });
      } else if (updates.state) {
        this.emitEvent('region.state.updated', { existingRegion, updatedRegion, user });
      }



      return updatedRegion;
    } catch (error) {
      if (error.code === 11000) {
        throw new HttpException('Region with provided details already exists', HttpStatus.CONFLICT);
      }
      this.logger.error(`An error occurred while updating Region by ID ${regionId}. ${error?.message}`);
      throw error;
    }
  }

  async changeStatus(regionId: Types.ObjectId, changeStatusDto: ChangeStatusDto, user: Object) {
    try {
      const existingRegion = await this.findById(regionId)

      if (!existingRegion) {
        throw new NotFoundException(`Region with ID "${regionId}" not found`)
      }

      const existingStatus = await this.statusService.findById(existingRegion.status)

      const { status, ...comment } = changeStatusDto;
      const statusObjId = new Types.ObjectId(status);

      const newStatus = await this.statusService.findById(statusObjId);
      if (!newStatus) {
        throw new BadRequestException(`Status with ID "${status}" not found`);
      }

      //Todo: SCD
      // if (existingRegion.status) {
      //   newStatus.parentStatus = existingRegion.status._id;
      //   await newStatus.save();
      // }


      const updatedRegion = await this.regionModel.findOneAndUpdate(
        { _id: regionId },
        { status: statusObjId },
        { new: true }
      )
        .populate({ path: 'country', select: '_id countryName countryPhoneCode currencyCode' })
        .populate({ path: 'state', select: '_id stateName' })
        .populate<{ status: StatusDocument }>({
          path: 'status',
          select: '_id name'
        });

      const blockedFunctionalities = ['REJECT', 'SUSPEND', 'ONHOLD'];

      if (blockedFunctionalities.includes(newStatus.functionality)) {
        this.emitEvent('region.blocked', { region: updatedRegion });
      }

      if (blockedFunctionalities.includes(existingStatus.functionality) && !blockedFunctionalities.includes(newStatus.functionality)) {
        this.emitEvent('region.activated', { region: updatedRegion });
      }

      this.emitEvent('region.status.changed', { existingRegion, updatedRegion, comment, user });

      return updatedRegion;

    } catch (error) {
      this.logger.error(`An error occurred while changing the status of Region by ID ${regionId}. ${error?.message}`)
      if (error instanceof BadRequestException || error instanceof NotFoundException || error instanceof ConflictException) {
        throw error;
      }
      throw new InternalServerErrorException;
    }
  }

  async remove(regionId: Types.ObjectId): Promise<void> {
    const region = await this.regionModel.findByIdAndDelete(regionId).exec();
    if (!region) {
      throw new NotFoundException(`Region with ID "${regionId}" not found`);
    }
  }

  async getComments(regionId: Types.ObjectId): Promise<Comment[]> {
    return this.commentModel.find({ region: regionId })
      // .populate({ path: 'user', select: '_id roles firstName' })
      .populate({ path: 'attachments', select: '_id originalName fileSize fileType locationUrl', model: 'FileMetadata' }).exec();
  }


  emitEvent(eventName: string, payload: any) {
    this.eventEmitter.emit(eventName, payload);
  }

}
