import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { UserInboxConfigService } from './user-inbox-config.service';
import { UserInboxConfigController } from './user-inbox-config.controller';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { UserModule } from 'src/user/user.module';
import { MongooseModule } from '@nestjs/mongoose';
import { AuthModule } from 'src/auth/auth.module';
import { OrgModule } from 'src/org/org.module';
import * as bcrypt from 'bcrypt';
import { UserInboxConfig, UserInboxConfigSchema } from './schemas/user-inbox-config.schema';
import { FileMetadata, FileMetadataSchema } from 'src/file-upload/schemas/file-metadata.schema';
import { EndpointsRolesModule } from 'src/endpoints-roles/endpoints-roles.module';
@Module({
  imports: [
    ConfigModule,
    JwtModule, EndpointsRolesModule,
    UserModule,
    AuthModule,
    OrgModule,
    // MongooseModule.forFeatureAsync([
    //   { 
    //     name:UserInboxConfig.name,
    //     imports: [ConfigModule],
    //     useFactory: (configService: ConfigService) => {
    //       const logger = new Logger('UserInboxConfigSchemaPreHook')
    //     const  schema = UserInboxConfigSchema;
    //     schema.pre('save', async function (next: any) {
    //       logger.debug('pre hook on user schema');

    //       try {
    //         if (!this.isModified('password')) {
    //           return next();
    //         }
    //         logger.debug('noticed a password change.')
    //         // tslint:disable-next-line:no-string-literal
    //         const hashed = await bcrypt.hash(this['password'], 10);
    //         // tslint:disable-next-line:no-string-literal
    //         this['password'] = hashed;
    //         return next();
    //       } catch (err) {
    //         return next(err);
    //       }
    //     });
    //     return schema;
    //   },
    //   inject: [ConfigService],
    //   }])
    MongooseModule.forFeature([
      { name:UserInboxConfig.name, schema:UserInboxConfigSchema },
      {name: FileMetadata.name, schema:FileMetadataSchema}
    ])
  ],
  controllers: [UserInboxConfigController],
  providers: [UserInboxConfigService],
  exports: [MongooseModule],
})
export class UserInboxConfigModule {}
