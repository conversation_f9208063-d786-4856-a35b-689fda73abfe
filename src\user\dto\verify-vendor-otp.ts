import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsString, Length } from 'class-validator';

export class VerifyVendorOtpDto {
  @ApiProperty({
    example: '<EMAIL>',
    description: 'The email address of the user',
  })
  @IsEmail()
  email: string;

  @ApiProperty({
    example: '123456',
    description: 'The 6-digit OTP code sent to the user',
  })
  @IsString()
  @Length(6, 6)
  otpCode: string;
}