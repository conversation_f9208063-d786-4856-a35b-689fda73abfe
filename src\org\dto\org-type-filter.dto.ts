
import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsNotEmpty, IsEnum, IsString, IsOptional, IsMongoId } from 'class-validator';
import { OrgType } from 'src/shared/constants';


export class OrgTypeFilterDto {

  @ApiProperty({
    type: String,
    required: true,
    default: OrgType.NONE,
    enum: OrgType,
    description: "Org type",
  })
  @IsNotEmpty()
  @IsEnum(OrgType)
  orgType: string;

  @IsOptional()
  @Transform(({ value }) => {
    return value === undefined || value === '0' ? '' : value;
  })
  isDeleted?: boolean;

  // @IsOptional()
  // @IsMongoId()
  // @Transform(({ value }) => {
  //   return value === undefined || value === '0' ? '' : value;
  // })
  // orgAdmin?: string;

  @IsOptional()
  @IsString()
  @Transform(({ value }) => (value === undefined || value === '0' ? '' : value))
  contactId?: string;

}
