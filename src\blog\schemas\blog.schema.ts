import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";
import { HydratedDocument, Types } from "mongoose";
import { BlogStatus, BlogType } from "src/shared/constants";
import { BasicUser } from "src/user/schemas/basic-user.schema";

export type BlogDocument = HydratedDocument<Blog>;

@Schema({
    timestamps: true
})
export class Blog {

    @Prop({
        required: true,
        trim: true,
    })
    title: string; 

    @Prop({
        required: true,
        default: BlogType.COMPANIES,
        enum: Object.values(BlogType),
    })
    type: string;

    @Prop({
        required: false,
        trim: true,
    })
    description?: string;

    @Prop({
        required: true,
        default: BlogStatus.ACTIVE,
        enum: Object.values(BlogStatus),
    })
    status : string;

    @Prop({
    required: true,
    type: Types.ObjectId, ref: 'BasicUser'
    })
    createdBy: BasicUser;
}

export const BlogSchema = SchemaFactory.createForClass(Blog);