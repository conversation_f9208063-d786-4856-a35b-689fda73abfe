import { ApiHideProperty, ApiProperty } from "@nestjs/swagger";
import { IsArray, IsEnum, IsMongoId, IsNotEmpty, IsOptional, IsString, ValidateNested } from "class-validator";
import { Transform, TransformFnParams, Type } from 'class-transformer';
import sanitizeWithStyle from 'sanitize-html'
import { IdentifierType } from "src/shared/constants";

export class IdentifierDataRowDto {

    @ApiProperty({ type: String, description: 'Identifier ObjectId' })
    @IsMongoId()
    @IsNotEmpty()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    identifier: string;

    @ApiProperty({ type: String, description: 'Identifier Consumption Value' })
    @IsOptional()
    @IsString()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    value?: string;

    @ApiProperty({ type: String, description: 'FileMetadata ObjectId' })
    @IsOptional()
    // @IsArray()
        // @IsMongoId({ each: true })
    // @IsMongoId()
    // @Transform((params: TransformFnParams) => params.value.map((value: string) => sanitizeWithStyle(value)))
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    attachmentUrls?: string;


}


export class CreateOnboardingDto {

    @ApiProperty({ type: [IdentifierDataRowDto], description: 'List of Identifiers and their data', required: true })
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => IdentifierDataRowDto)
    @IsNotEmpty()
    identifiers: IdentifierDataRowDto[];

    @ApiProperty({
        type: String,
        required: false,
    })
    @IsOptional()
    @IsMongoId()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    org?: string;

    @ApiProperty({
        type: String,
        required: false,
    })
    @IsOptional()
    @IsMongoId()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    user?: string;

    @ApiProperty({ enum: IdentifierType, description: 'Entity type, can be either "company" or "individual"', required: true })
    @IsEnum(IdentifierType)
    @IsNotEmpty()
    entityType: IdentifierType;

    @ApiHideProperty()
    @IsOptional()
    @IsString()
    createdBy?: string;

}
