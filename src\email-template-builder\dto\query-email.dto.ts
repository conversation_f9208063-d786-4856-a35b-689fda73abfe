import { ApiProperty } from "@nestjs/swagger";
import { IsOptional, IsString, IsBoolean, IsMongoId, IsEnum } from "class-validator";
import { Transform, TransformFnParams } from "class-transformer";
import { sanitizeWithStyle } from "src/utils/sanitize-with-style";
import { EmailTemplateEvent } from "src/shared/constants";

export class QueryEmailTemplateDto {
    @ApiProperty({
        type: String,
        required: false,
        description: 'The name of the email template'
    })
    @IsOptional()
    @IsString()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    templateName?: string;

    @ApiProperty({
        type: String,
        required: false,
        enum: EmailTemplateEvent,
        description: 'Event name triggering the email',
    })
    @IsEnum(EmailTemplateEvent)
    @IsOptional()
    @IsString()
    eventName?: EmailTemplateEvent;

    @ApiProperty({
        type: String,
        required: false,
        description: 'Reference to the organization associated with the email template'
    })
    @IsOptional()
    @IsMongoId()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    org?: string;

    @Transform(({ value }) => {
        return value > 0 ? value : 1;
    })
    page: number = 1;

    @Transform(({ value }) => {
        return value > 0 ? value : 10;
    })
    limit: number = 10;

}
