import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsEnum, IsNumber, IsDateString, IsString, IsMongoId, IsBoolean } from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { Gender, Source, StageType } from 'src/shared/constants';

export class CustomFilterDto {
    @ApiProperty({ type: Number, required: false, example: 1, description: 'Page number for pagination' })
    @IsOptional()
    @Type(() => Number)
    @IsNumber()
    page?: number = 1;

    @ApiProperty({ type: Number, required: false, example: 10, description: 'Number of items per page' })
    @IsOptional()
    @Type(() => Number)
    @IsNumber()
    limit?: number = 10;

    @ApiProperty({ required: false, enum: Source, description: 'Sort applications by source type (Vendor or Freelancer)' })
    @IsOptional()
    @IsEnum(Source)
    sortBySourceType?: Source;

    @ApiProperty({ required: false, example: '2025-02-01', description: 'Filter job applications from this date (YYYY-MM-DD)' })
    @IsOptional()
    @IsDateString()
    fromDate?: string;

    @ApiProperty({ required: false, example: '2025-03-31', description: 'Filter job applications until this date (YYYY-MM-DD)' })
    @IsOptional()
    @IsDateString()
    toDate?: string;

    @ApiProperty({ required: false, description: 'Filter by gender (Male, Female, Other)', enum: Gender })
    @IsOptional()
    @IsEnum(Gender)
    gender?: string;

    @ApiProperty({ type: Number, required: false, description: 'Filter by notice period in days' })
    @IsOptional()
    @Type(() => Number)
    @IsNumber()
    noticePeriodDays?: number;

    @ApiProperty({ type: Number, required: false, description: 'Minimum Current CTC (in currency unit)' })
    @IsOptional()
    @Type(() => Number)
    @IsNumber()
    minCurrentCTC?: number;

    @ApiProperty({ type: Number, required: false, description: 'Max Current CTC (in currency unit)' })
    @IsOptional()
    @Type(() => Number)
    @IsNumber()
    maxCurrentCTC?: number;

    @ApiProperty({ type: Number, required: false, description: 'Minimum Expected CTC (in currency unit)' })
    @IsOptional()
    @Type(() => Number)
    @IsNumber()
    minExpectedCTC?: number;

    @ApiProperty({ type: Number, required: false, description: 'Maximum Expected CTC (in currency unit)' })
    @IsOptional()
    @Type(() => Number)
    @IsNumber()
    maxExpectedCTC?: number;

    @ApiProperty({ type: Number, required: false, description: 'CTC Percentage Hike' })
    @IsOptional()
    @Type(() => Number)
    @IsNumber()
    ctcPercentage?: number;

    @ApiProperty({ type: String, required: false, description: 'Filter by country' })
    @IsOptional()
    @IsMongoId()
    country?: string;

    @ApiProperty({ type: String, required: false, description: 'Filter by state' })
    @IsOptional()
    @IsMongoId()
    state?: string;

    @ApiProperty({ type: String, required: false, description: 'Filter by city' })
    @IsOptional()
    @IsMongoId()
    city?: string;

    @ApiProperty({ required: false, description: 'Filter by rejected status' })
    @IsOptional()
    @IsBoolean()
    @Transform(({ value }) => value === 'true' || value === true) // Ensures it's a boolean
    isRejected?: boolean;

    @ApiProperty({
        type: String,
        required: false,
    })
    @IsOptional()
    @IsString()
    searchTerm?: string;

    @ApiProperty({ required: false, description: 'Stage type of job application', enum: StageType })
    @IsOptional()
    @IsEnum(StageType)
    stageType?: string;
}
