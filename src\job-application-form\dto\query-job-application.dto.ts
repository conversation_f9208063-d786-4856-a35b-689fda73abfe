import { ApiHideProperty, ApiProperty } from "@nestjs/swagger";
import { IsArray, IsBoolean, IsDate, IsEnum, IsISO8601, IsMongoId, IsNotEmpty, IsNumber, IsOptional, IsString, IsUrl, Length, Max, Min, Validate, ValidateIf, ValidateNested } from "class-validator";
import { Transform, TransformFnParams, Type } from 'class-transformer';
import { sanitizeWithStyle } from "src/utils/sanitize-with-style";
import { Prop } from "@nestjs/mongoose";
import moment from "moment";
import { EmploymentType, StageType } from "src/shared/constants";

export class CustomDateFormatValidator {
    validate(date: string, args: any): boolean {
        const [format] = args.constraints;
        return moment(date, format, true).isValid();
    }

    defaultMessage(): string {
        return 'Invalid date format';
    }
}
export class QueryJobApplicationDto {

    @ApiProperty({
        type: String,
        required: false,
    })
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    @IsString()
    @IsOptional()
    firstName?: string;

    @ApiProperty({
        type: String,
        required: false,
    })
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    @IsString()
    @IsOptional()
    lastName?: string;

    @ApiProperty({
        type: String,
        required: false,
    })
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    @IsOptional()
    @IsMongoId()
    currentLocation?: string;

    @ApiProperty({
        type: [String],
        required: false,
    })
    @IsArray()
    @IsOptional()
    @Transform((params: TransformFnParams) => params.value.map((value: string) => sanitizeWithStyle(value)))
    keywords?: string[];

    @ApiProperty({
        type: [String],
        required: false,
    })
    @IsArray()
    @IsOptional()
    @Transform((params: TransformFnParams) => params.value.map((value: string) => sanitizeWithStyle(value)))
    excludeKeywords?: string[];

    @ApiProperty({
        type: Number,
        required: false,
    })
    @IsOptional()
    minYearsOfExperience?: number;

    @ApiProperty({
        type: Number,
        required: false,
    })
    @IsOptional()
    maxYearsOfExperience?: number;

    @ApiProperty({
        type: Number,
        required: false,
    })
    @IsOptional()
    minCurrentCTC?: number;

    @ApiProperty({
        type: Number,
        required: false,
    })
    @IsOptional()
    maxCurrentCTC?: number;

    @ApiProperty({
        type: Number,
        required: false,
    })
    @IsOptional()
    minExpectedCTC?: number;

    @ApiProperty({
        type: Number,
        required: false,
    })
    @IsOptional()
    maxExpectedCTC?: number;

    @ApiProperty({
        type: String,
        required: false,
    })
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    @IsOptional()
    @IsMongoId()
    industry?: string;

    //company
    @ApiProperty({
        type: String,
        required: false,
    })
    @IsMongoId()
    @IsOptional()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    org?: string;

    @ApiProperty({
        type: String,
        required: false,
    })
    @IsMongoId()
    @IsOptional()
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    excludedOrg?: string;

    @ApiProperty({
        type: String,
        required: false,
    })
    @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
    @IsOptional()
    designation?: string;

    @ApiProperty({
        type: Number,
        required: false,
    })
    @IsOptional()
    // @IsNumber()
    noticePeriodDays?: number;

    @ApiProperty({
        type: Date,
        required: false,
        description: 'Date of joining',
        default: new Date(Date.UTC(new Date().getUTCFullYear(), new Date().getUTCMonth(), new Date().getUTCDate(), new Date().getUTCHours(), new Date().getUTCMinutes())),
    })
    // YYYY-MM-DDThh:mm:ssZ - example : 2024-05-27T00:00:00.000Z
    @IsISO8601({ strict: true })
    // @Length(10,24)
    @IsOptional()
    dateOfJoining?: Date;

    @IsOptional()
    @Validate(CustomDateFormatValidator, ['YYYY-MM-DD'], {
        message: 'fromDate must be in the format YYYY-MM-DD',
    })
    @ApiProperty({ description: 'Filter jobs created after this date (YYYY-MM-DD)', required: false })
    fromDate?: string;

    @IsOptional()
    @Validate(CustomDateFormatValidator, ['YYYY-MM-DD'], {
        message: 'toDate must be in the format YYYY-MM-DD',
    })
    @ApiProperty({ description: 'Filter jobs created before this date (YYYY-MM-DD)', required: false })
    toDate?: string;

    // @IsOptional()
    // @IsEnum(EmploymentType, { message: 'Invalid Employment type specified' })
    // @Transform(({ value }) => {
    //     return value === undefined || value.trim() === '' ? null : value;
    // })
    // employmentType?: string;

    // @IsOptional()
    // @IsEnum(StageType, { message: 'Invalid status specified' })
    // @Transform(({ value }) => {
    //     return value === undefined || value.trim() === '' ? null : value;
    // })
    // stageType?: string;

    @Transform(({ value }) => {
        return value > 0 ? value : 1;
    })
    page: number = 1;

    @Transform(({ value }) => {
        return value > 0 ? value : 10;
    })
    limit: number = 10;
    @IsOptional()
    @IsString()
    @Transform(({ value }) => (value === undefined || value === '0' ? '' : value))
    name?: string;
}