import { IsNotEmpty, IsUUID } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Transform, TransformFnParams } from 'class-transformer';
import sanitizeWithStyle from 'sanitize-html'

export class VerifyUuidDto {
  @ApiProperty({
    description: 'UUID to verify user',
    format: 'uuid',
    uniqueItems: true,
  })
  @IsNotEmpty()
  @IsUUID()
  @Transform((params: TransformFnParams) => sanitizeWithStyle(params.value))
  readonly verification: string;
}
